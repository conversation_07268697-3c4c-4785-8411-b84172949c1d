#!/usr/bin/env node

/**
 * Verify Profile Dropdown Fix
 * 
 * This script verifies that the profile dropdown authentication display issue
 * has been fixed by checking the TypeScript interfaces and testing the API.
 */

const fs = require('fs');
const path = require('path');

async function verifyProfileDropdownFix() {
  console.log('🔍 Verifying Profile Dropdown Fix...\n');

  let allChecksPass = true;

  // 1. Check TypeScript interfaces
  console.log('1️⃣ Checking TypeScript User interfaces...');
  
  const filesToCheck = [
    'src/components/dashboard/DashboardLayout.tsx',
    'src/app/dashboard/page.tsx',
    'src/app/admin/page.tsx',
    'src/app/notifications/preferences/page.tsx',
    'src/app/admin/meetings/import/page.tsx',
    'src/app/admin/meeting-assignments/page.tsx'
  ];

  for (const filePath of filesToCheck) {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Check if the file has a User interface
      const hasUserInterface = content.includes('interface User {');
      
      if (hasUserInterface) {
        // Check if it includes hasCongregationPinAccess
        const hasFlag = content.includes('hasCongregationPinAccess?: boolean;');
        
        if (hasFlag) {
          console.log(`   ✅ ${filePath} - User interface includes hasCongregationPinAccess`);
        } else {
          console.log(`   ❌ ${filePath} - User interface MISSING hasCongregationPinAccess`);
          allChecksPass = false;
        }
      } else {
        console.log(`   ℹ️  ${filePath} - No User interface found (OK)`);
      }
    } else {
      console.log(`   ⚠️  ${filePath} - File not found`);
    }
  }

  // 2. Check DashboardLayout component logic
  console.log('\n2️⃣ Checking DashboardLayout profile dropdown logic...');
  
  const dashboardLayoutPath = path.join(__dirname, '..', 'src/components/dashboard/DashboardLayout.tsx');
  if (fs.existsSync(dashboardLayoutPath)) {
    const content = fs.readFileSync(dashboardLayoutPath, 'utf8');
    
    // Check for the conditional logic
    const hasConditionalLogic = content.includes('user.hasCongregationPinAccess ?');
    const showsCongregationName = content.includes('{congregation.name}');
    const showsPinAccessText = content.includes('Acceso con PIN de Congregación');
    const showsMemberName = content.includes('{user.name}');
    const showsRoleDisplay = content.includes('getRoleDisplayName(user.role)');
    
    if (hasConditionalLogic) {
      console.log('   ✅ Conditional logic for hasCongregationPinAccess found');
    } else {
      console.log('   ❌ Conditional logic for hasCongregationPinAccess NOT found');
      allChecksPass = false;
    }
    
    if (showsCongregationName && showsPinAccessText) {
      console.log('   ✅ Congregation PIN access display logic found');
    } else {
      console.log('   ❌ Congregation PIN access display logic NOT found');
      allChecksPass = false;
    }
    
    if (showsMemberName && showsRoleDisplay) {
      console.log('   ✅ Member-specific display logic found');
    } else {
      console.log('   ❌ Member-specific display logic NOT found');
      allChecksPass = false;
    }
  } else {
    console.log('   ❌ DashboardLayout.tsx not found');
    allChecksPass = false;
  }

  // 3. Test API authentication
  console.log('\n3️⃣ Testing API authentication...');
  
  try {
    const loginResponse = await fetch('http://localhost:3001/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1930',
        rememberMe: false,
      }),
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      
      if (loginData.user.hasCongregationPinAccess === true) {
        console.log('   ✅ API returns hasCongregationPinAccess: true');
      } else {
        console.log('   ❌ API does NOT return hasCongregationPinAccess: true');
        allChecksPass = false;
      }
      
      // Test dashboard API
      const dashboardResponse = await fetch('http://localhost:3001/api/dashboard', {
        headers: {
          'Authorization': `Bearer ${loginData.token}`,
        },
      });

      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json();
        
        if (dashboardData.user.hasCongregationPinAccess === true) {
          console.log('   ✅ Dashboard API preserves hasCongregationPinAccess: true');
        } else {
          console.log('   ❌ Dashboard API does NOT preserve hasCongregationPinAccess: true');
          allChecksPass = false;
        }
      } else {
        console.log('   ❌ Dashboard API call failed');
        allChecksPass = false;
      }
    } else {
      console.log('   ❌ Login API call failed');
      allChecksPass = false;
    }
  } catch (error) {
    console.log(`   ❌ API test failed: ${error.message}`);
    allChecksPass = false;
  }

  // 4. Summary
  console.log('\n📋 Summary:');
  if (allChecksPass) {
    console.log('✅ ALL CHECKS PASSED! The profile dropdown fix is working correctly.');
    console.log('\n🎯 Expected Behavior:');
    console.log('   When logged in with congregation PIN (1441 / 1930):');
    console.log('   - Profile dropdown should show: "Coral Oeste"');
    console.log('   - Profile dropdown should show: "Acceso con PIN de Congregación"');
    console.log('   - Profile dropdown should show: "Cerrar Sesión"');
    console.log('   - Profile dropdown should NOT show: "Carlos Coordinador"');
    console.log('   - Profile dropdown should NOT show: "Coordinador"');
  } else {
    console.log('❌ SOME CHECKS FAILED! The profile dropdown fix needs attention.');
  }

  return allChecksPass;
}

// Run the verification
verifyProfileDropdownFix()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Verification failed with error:', error);
    process.exit(1);
  });
