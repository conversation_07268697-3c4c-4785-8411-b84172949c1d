/**
 * Service Groups Management API Endpoint
 *
 * Handles CRUD operations for service groups including
 * group creation, member assignments, and overseer management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

// Validation schemas
const CreateServiceGroupSchema = z.object({
  name: z.string().min(1, 'Group name is required'),
  groupNumber: z.number().min(1, 'Group number must be positive'),
  overseerId: z.string().optional(),
  assistantId: z.string().nullable().optional(),
  address: z.string().optional(),
});

const UpdateServiceGroupSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Group name is required').optional(),
  groupNumber: z.number().min(1, 'Group number must be positive').optional(),
  overseerId: z.string().optional(),
  assistantId: z.string().nullable().optional(),
  address: z.string().optional(),
  isActive: z.boolean().optional(),
});

/**
 * GET /api/admin/service-groups
 * Retrieve all service groups for the congregation
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    const member = authResult.user;

    // Check if user has permission to view service groups
    if (!['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view service groups' },
        { status: 403 }
      );
    }

    // Get service groups with related data
    const serviceGroups = await prisma.serviceGroup.findMany({
      where: {
        congregationId: member.congregationId,
        isActive: true,
      },
      include: {
        overseer: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        assistant: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
      },
      orderBy: {
        groupNumber: 'asc',
      },
    });

    return NextResponse.json({
      success: true,
      groups: serviceGroups,
    });

  } catch (error) {
    console.error('Service groups GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve service groups',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/service-groups
 * Create a new service group
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    const member = authResult.user;

    // Check if user has permission to manage service groups
    if (!['elder', 'coordinator', 'overseer_coordinator', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to manage service groups' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const validationResult = CreateServiceGroupSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid service group data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { name, groupNumber, overseerId, assistantId, address } = validationResult.data;

    // Check if group number already exists
    const existingGroup = await prisma.serviceGroup.findFirst({
      where: {
        congregationId: member.congregationId,
        groupNumber,
        isActive: true,
      },
    });

    if (existingGroup) {
      return NextResponse.json(
        { error: 'Group number already exists' },
        { status: 400 }
      );
    }

    // Verify overseer and assistant exist and are valid
    if (overseerId) {
      const overseer = await prisma.member.findFirst({
        where: {
          id: overseerId,
          congregationId: member.congregationId,
          isActive: true,
        },
      });

      if (!overseer) {
        return NextResponse.json(
          { error: 'Invalid overseer selected' },
          { status: 400 }
        );
      }
    }

    if (assistantId) {
      const assistant = await prisma.member.findFirst({
        where: {
          id: assistantId,
          congregationId: member.congregationId,
          isActive: true,
        },
      });

      if (!assistant) {
        return NextResponse.json(
          { error: 'Invalid assistant selected' },
          { status: 400 }
        );
      }
    }

    // Create the service group
    const serviceGroup = await prisma.serviceGroup.create({
      data: {
        congregationId: member.congregationId,
        name,
        groupNumber,
        overseerId: overseerId || null,
        assistantId: assistantId || null,
        address: address || null,
      },
      include: {
        overseer: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        assistant: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      group: serviceGroup,
    });

  } catch (error) {
    console.error('Service group creation error:', error);

    return NextResponse.json(
      {
        error: 'Failed to create service group',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/service-groups
 * Update an existing service group
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    const member = authResult.user;

    // Check if user has permission to manage service groups
    if (!['elder', 'coordinator', 'overseer_coordinator', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to manage service groups' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const validationResult = UpdateServiceGroupSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid service group data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { id, ...updateData } = validationResult.data;

    // Verify the service group exists and belongs to the congregation
    const existingGroup = await prisma.serviceGroup.findFirst({
      where: {
        id,
        congregationId: member.congregationId,
      },
    });

    if (!existingGroup) {
      return NextResponse.json(
        { error: 'Service group not found' },
        { status: 404 }
      );
    }

    // Update the service group
    const updatedGroup = await prisma.serviceGroup.update({
      where: { id },
      data: {
        ...updateData,
        address: updateData.address || existingGroup.address,
      },
      include: {
        overseer: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        assistant: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      group: updatedGroup,
    });

  } catch (error) {
    console.error('Service group update error:', error);

    return NextResponse.json(
      {
        error: 'Failed to update service group',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/service-groups
 * Delete (deactivate) a service group
 */
export async function DELETE(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    const member = authResult.user;

    // Check if user has permission to manage service groups
    if (!['elder', 'coordinator', 'overseer_coordinator', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to manage service groups' },
        { status: 403 }
      );
    }

    // Get group ID from query parameters
    const { searchParams } = new URL(request.url);
    const groupId = searchParams.get('id');

    if (!groupId) {
      return NextResponse.json(
        { error: 'Group ID is required' },
        { status: 400 }
      );
    }

    // Verify the service group exists and belongs to the congregation
    const existingGroup = await prisma.serviceGroup.findFirst({
      where: {
        id: groupId,
        congregationId: member.congregationId,
      },
    });

    if (!existingGroup) {
      return NextResponse.json(
        { error: 'Service group not found' },
        { status: 404 }
      );
    }

    // Soft delete by setting isActive to false
    await prisma.serviceGroup.update({
      where: { id: groupId },
      data: { isActive: false },
    });

    return NextResponse.json({
      success: true,
      message: 'Service group deleted successfully',
    });

  } catch (error) {
    console.error('Service group deletion error:', error);

    return NextResponse.json(
      {
        error: 'Failed to delete service group',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
