#!/usr/bin/env node

/**
 * Find PostgreSQL Installation
 * 
 * This script finds PostgreSQL installations on the system.
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

async function findPostgreSQL() {
    console.log('🔍 SEARCHING FOR POSTGRESQL INSTALLATIONS...');
    console.log('');

    // Common PostgreSQL installation paths
    const commonPaths = [
        'C:\\Program Files\\PostgreSQL',
        'C:\\Program Files (x86)\\PostgreSQL',
        'C:\\laragon\\bin\\postgresql',
        'C:\\xampp\\postgresql',
        'C:\\wamp\\bin\\postgresql',
        'C:\\PostgreSQL'
    ];

    console.log('📂 Checking common installation paths...');
    for (const basePath of commonPaths) {
        if (fs.existsSync(basePath)) {
            console.log(`   ✅ Found: ${basePath}`);
            
            // Look for version directories
            try {
                const versions = fs.readdirSync(basePath);
                for (const version of versions) {
                    const versionPath = path.join(basePath, version);
                    const binPath = path.join(versionPath, 'bin');
                    const psqlPath = path.join(binPath, 'psql.exe');
                    
                    if (fs.existsSync(psqlPath)) {
                        console.log(`   🎯 PSQL found: ${psqlPath}`);
                        
                        // Test the psql command
                        try {
                            await testPsql(psqlPath);
                        } catch (error) {
                            console.log(`   ⚠️  Test failed: ${error.message}`);
                        }
                    }
                }
            } catch (error) {
                console.log(`   ⚠️  Cannot read directory: ${error.message}`);
            }
        } else {
            console.log(`   ❌ Not found: ${basePath}`);
        }
    }

    // Try to find psql in PATH
    console.log('🔍 Checking if psql is in PATH...');
    try {
        await new Promise((resolve, reject) => {
            exec('where psql', (error, stdout, stderr) => {
                if (error) {
                    console.log('   ❌ psql not found in PATH');
                    reject(error);
                } else {
                    console.log(`   ✅ psql found in PATH: ${stdout.trim()}`);
                    resolve(stdout);
                }
            });
        });
    } catch (error) {
        // psql not in PATH
    }

    console.log('');
    console.log('💡 MANUAL RESTORATION COMMANDS:');
    console.log('If you found a working psql.exe path above, use these commands:');
    console.log('');
    console.log('1. Drop and recreate database:');
    console.log('   "C:\\Path\\To\\psql.exe" -h localhost -p 5432 -U mywebsites -d postgres -c "DROP DATABASE IF EXISTS hermanos;"');
    console.log('   "C:\\Path\\To\\psql.exe" -h localhost -p 5432 -U mywebsites -d postgres -c "CREATE DATABASE hermanos;"');
    console.log('');
    console.log('2. Restore from backup:');
    console.log('   "C:\\Path\\To\\psql.exe" -h localhost -p 5432 -U mywebsites -d hermanos -f "hermanos-07-25-25E.sql"');
    console.log('');
    console.log('Environment variable to set:');
    console.log('   set PGPASSWORD=password');
}

function testPsql(psqlPath) {
    return new Promise((resolve, reject) => {
        exec(`"${psqlPath}" --version`, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                console.log(`   ✅ Version: ${stdout.trim()}`);
                resolve(stdout);
            }
        });
    });
}

// Run the search
findPostgreSQL();
