/**
 * Create sample PDF files for existing letters in database
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const prisma = new PrismaClient();

async function createSamplePDFs() {
  try {
    console.log('📄 Creating sample PDF files...');
    
    // Get all letters from database
    const letters = await prisma.letter.findMany({
      where: { isActive: true },
      select: { filename: true, filePath: true, title: true }
    });
    
    console.log(`Found ${letters.length} letters in database`);
    
    // Create uploads directory
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'letters');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('📁 Created uploads directory');
    }
    
    // Sample PDF content template
    const pdfTemplate = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length CONTENT_LENGTH
>>
stream
BT
/F1 12 Tf
72 720 Td
(TITLE_PLACEHOLDER) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000369 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
466
%%EOF`;
    
    // Create PDF files for each letter
    let created = 0;
    let existing = 0;
    
    for (const letter of letters) {
      const filePath = path.join(uploadsDir, letter.filename);
      
      if (!fs.existsSync(filePath)) {
        const title = letter.title.slice(0, 30); // Limit title length
        const content = pdfTemplate
          .replace('TITLE_PLACEHOLDER', title)
          .replace('CONTENT_LENGTH', title.length.toString());
        
        fs.writeFileSync(filePath, content);
        console.log(`✅ Created: ${letter.filename}`);
        created++;
      } else {
        console.log(`⏭️  Exists: ${letter.filename}`);
        existing++;
      }
    }
    
    console.log(`\n🎉 Summary:`);
    console.log(`   📄 Created: ${created} files`);
    console.log(`   ⏭️  Existing: ${existing} files`);
    console.log(`   📁 Total: ${letters.length} files`);
    
    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    await prisma.$disconnect();
    process.exit(1);
  }
}

createSamplePDFs();
