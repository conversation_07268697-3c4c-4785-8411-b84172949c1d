/**
 * Diagnose Territories 4, 5, 7, 8, 9, 10 Excel Structure
 * 
 * Examines the Excel files to understand structure including buildings vs houses
 */

const XLSX = require('xlsx');
const path = require('path');

async function diagnoseTerritory(territoryNumber) {
  try {
    console.log(`\n🔍 Diagnosing Territory ${territoryNumber}...`);
    
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${territoryNumber.padStart(3, '0')}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    console.log(`📊 Available sheets: ${workbook.SheetNames.join(', ')}`);
    
    // Try to find the correct sheet
    const possibleSheetNames = [
      `Terr ${territoryNumber}`,
      `Terr. ${territoryNumber}`,
      `Terr  ${territoryNumber}`,
      `Territory ${territoryNumber}`,
      `T${territoryNumber}`,
      workbook.SheetNames[0]
    ];
    
    let worksheet = null;
    let sheetName = '';
    
    for (const name of possibleSheetNames) {
      if (workbook.Sheets[name]) {
        worksheet = workbook.Sheets[name];
        sheetName = name;
        break;
      }
    }
    
    console.log(`📋 Using sheet: ${sheetName}`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Total rows: ${excelData.length}`);
    
    // Show first 25 rows to understand structure
    console.log(`\n📋 First 25 rows of Territory ${territoryNumber}:`);
    for (let i = 0; i < Math.min(25, excelData.length); i++) {
      const row = excelData[i];
      if (row && row.length > 0) {
        console.log(`Row ${i + 1}:`, row.slice(0, 8)); // Show first 8 columns
      }
    }
    
    // Look for building indicators
    console.log(`\n🏢 Looking for building indicators in Territory ${territoryNumber}:`);
    let buildingCount = 0;
    let houseCount = 0;
    
    for (let i = 8; i < Math.min(50, excelData.length); i++) {
      const row = excelData[i];
      if (row && row[1]) {
        const cellValue = row[1].toString().toLowerCase();
        if (cellValue.includes('building') || cellValue.includes('edificio') || 
            cellValue.includes('apt') || cellValue.includes('condo') ||
            cellValue.includes('tower') || cellValue.includes('plaza')) {
          buildingCount++;
          console.log(`   🏢 Building found: ${row[1]}`);
        } else if (/^\d/.test(cellValue)) {
          houseCount++;
        }
      }
    }
    
    console.log(`📊 Analysis: ${buildingCount} buildings, ${houseCount} houses found in sample`);
    
  } catch (error) {
    console.error(`❌ Error diagnosing Territory ${territoryNumber}:`, error.message);
  }
}

async function diagnoseMultipleTerritories() {
  console.log('🔍 Starting diagnosis of Territories 4, 5, 7, 8, 9, 10...');
  
  const territories = ['004', '005', '007', '008', '009', '010'];
  
  for (const territory of territories) {
    await diagnoseTerritory(territory);
  }
  
  console.log('\n✅ Diagnosis completed!');
}

diagnoseMultipleTerritories();
