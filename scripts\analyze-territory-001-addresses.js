#!/usr/bin/env node

/**
 * Analyze Territory 001 Address Distribution
 * 
 * This script analyzes the actual geographic distribution of all 74 addresses
 * in Territory 001 to determine accurate boundary coordinates based on real
 * address locations rather than theoretical street grid assumptions.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Analyze address patterns to determine geographic extent
 */
function analyzeAddressDistribution(addresses) {
  console.log('📍 Analyzing Address Distribution');
  console.log('=================================\n');

  // Parse addresses and extract street information
  const addressData = addresses.map(addr => {
    const match = addr.match(/^(\d+[a-z]?)\s+(.+?),\s*Miami,?\s*FL/i);
    if (match) {
      const number = match[1];
      const street = match[2].trim();
      
      // Extract numeric part for sorting
      const numericPart = parseInt(number.replace(/[a-z]/i, ''));
      
      return {
        original: addr,
        number: number,
        numericNumber: numericPart,
        street: street,
        isValid: true
      };
    }
    return {
      original: addr,
      isValid: false
    };
  }).filter(addr => addr.isValid);

  console.log(`✅ Parsed ${addressData.length} valid addresses\n`);

  // Group by street
  const streetGroups = {};
  addressData.forEach(addr => {
    if (!streetGroups[addr.street]) {
      streetGroups[addr.street] = [];
    }
    streetGroups[addr.street].push(addr);
  });

  // Analyze each street
  console.log('📊 Address Distribution by Street:');
  console.log('==================================');
  
  Object.keys(streetGroups).sort().forEach(street => {
    const addresses = streetGroups[street];
    const numbers = addresses.map(a => a.numericNumber).sort((a, b) => a - b);
    
    console.log(`\n${street}:`);
    console.log(`  Count: ${addresses.length} addresses`);
    console.log(`  Range: ${numbers[0]} - ${numbers[numbers.length - 1]}`);
    console.log(`  Numbers: ${numbers.join(', ')}`);
  });

  // Determine geographic boundaries based on address analysis
  console.log('\n🗺️  Geographic Boundary Analysis:');
  console.log('==================================');

  // Find extreme addresses for each direction
  const streets = Object.keys(streetGroups);
  
  // East-West analysis (Avenues)
  const avenues = streets.filter(s => s.includes('AVE')).sort();
  console.log(`\nAvenues (East-West): ${avenues.join(', ')}`);
  
  if (avenues.length > 0) {
    const easternmost = avenues[0]; // Lowest avenue number = easternmost
    const westernmost = avenues[avenues.length - 1]; // Highest avenue number = westernmost
    console.log(`  Easternmost: ${easternmost}`);
    console.log(`  Westernmost: ${westernmost}`);
  }

  // North-South analysis
  const streets_ns = streets.filter(s => !s.includes('AVE'));
  console.log(`\nNorth-South Streets: ${streets_ns.join(', ')}`);

  // Find address number ranges for boundary estimation
  let minNumber = Infinity;
  let maxNumber = -Infinity;
  
  addressData.forEach(addr => {
    if (addr.numericNumber < minNumber) minNumber = addr.numericNumber;
    if (addr.numericNumber > maxNumber) maxNumber = addr.numericNumber;
  });

  console.log(`\nAddress Number Range: ${minNumber} - ${maxNumber}`);

  return {
    streetGroups,
    avenues,
    streets_ns,
    minNumber,
    maxNumber,
    totalAddresses: addressData.length
  };
}

/**
 * Create boundary recommendations based on address analysis
 */
function createBoundaryRecommendations(analysis) {
  console.log('\n💡 Boundary Recommendations:');
  console.log('=============================\n');

  // Miami street grid reference (approximate)
  const MIAMI_COORDS = {
    // Avenues (East-West, longitude)
    'NW 65 AVE': -80.2725,
    'NW 66 AVE': -80.2750, 
    'NW 67 AVE': -80.2775,
    
    // Streets (North-South, latitude)
    'TAMIAMI CANAL RD': 25.7630,
    'NW 2 ST': 25.7620,
    
    // Address number to latitude mapping (approximate)
    getLatitudeFromNumber: (number) => {
      // Lower numbers = further south
      // Higher numbers = further north
      if (number < 100) return 25.7580;
      if (number < 200) return 25.7590;
      if (number < 300) return 25.7600;
      return 25.7610;
    }
  };

  // Determine boundary coordinates
  const eastBoundary = MIAMI_COORDS['NW 65 AVE']; // Eastern edge
  const westBoundary = MIAMI_COORDS['NW 67 AVE']; // Western edge
  
  // Determine north/south boundaries based on actual address ranges
  const southBoundary = MIAMI_COORDS.getLatitudeFromNumber(analysis.minNumber);
  const northBoundary = Math.max(
    MIAMI_COORDS.getLatitudeFromNumber(analysis.maxNumber),
    MIAMI_COORDS['TAMIAMI CANAL RD'] // Include Tamiami Canal Rd addresses
  );

  const recommendedBoundary = {
    type: 'Polygon',
    coordinates: [[
      [westBoundary, northBoundary],   // Northwest
      [eastBoundary, northBoundary],   // Northeast  
      [eastBoundary, southBoundary],   // Southeast
      [westBoundary, southBoundary],   // Southwest
      [westBoundary, northBoundary]    // Close polygon
    ]]
  };

  console.log('Recommended Boundary Coordinates:');
  console.log(`  Northwest: [${westBoundary}, ${northBoundary}]`);
  console.log(`  Northeast: [${eastBoundary}, ${northBoundary}]`);
  console.log(`  Southeast: [${eastBoundary}, ${southBoundary}]`);
  console.log(`  Southwest: [${westBoundary}, ${southBoundary}]`);

  // Calculate dimensions
  const latSpan = northBoundary - southBoundary;
  const lngSpan = eastBoundary - westBoundary; // Note: eastBoundary > westBoundary in longitude
  
  console.log(`\nBoundary Dimensions:`);
  console.log(`  Latitude span: ${latSpan.toFixed(6)} degrees (~${(latSpan * 69).toFixed(2)} miles)`);
  console.log(`  Longitude span: ${Math.abs(lngSpan).toFixed(6)} degrees (~${(Math.abs(lngSpan) * 54.6).toFixed(2)} miles)`);

  return recommendedBoundary;
}

/**
 * Compare current vs recommended boundaries
 */
async function compareBoundaries(recommended) {
  console.log('\n🔍 Boundary Comparison:');
  console.log('=======================\n');

  try {
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        boundaries: true
      }
    });

    if (territory && territory.boundaries) {
      const current = territory.boundaries;
      
      console.log('Current Boundary:');
      current.coordinates[0].forEach((coord, index) => {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
        console.log(`  ${labels[index]}: [${coord[0]}, ${coord[1]}]`);
      });

      console.log('\nRecommended Boundary:');
      recommended.coordinates[0].forEach((coord, index) => {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
        console.log(`  ${labels[index]}: [${coord[0]}, ${coord[1]}]`);
      });

      // Calculate differences
      console.log('\nDifferences:');
      for (let i = 0; i < 4; i++) { // Skip the closing point
        const currentCoord = current.coordinates[0][i];
        const recommendedCoord = recommended.coordinates[0][i];
        const lngDiff = recommendedCoord[0] - currentCoord[0];
        const latDiff = recommendedCoord[1] - currentCoord[1];
        
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest'];
        console.log(`  ${labels[i]}: Lng ${lngDiff > 0 ? '+' : ''}${lngDiff.toFixed(6)}, Lat ${latDiff > 0 ? '+' : ''}${latDiff.toFixed(6)}`);
      }

    } else {
      console.log('No current boundary found in database');
    }
  } catch (error) {
    console.error('Error comparing boundaries:', error);
  }
}

/**
 * Main analysis function
 */
async function main() {
  try {
    console.log('🗺️  Territory 001 Address-Based Boundary Analysis');
    console.log('=================================================\n');

    // Get Territory 001 addresses
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        address: true
      }
    });

    if (!territory) {
      console.log('❌ Territory 001 not found');
      return;
    }

    const addresses = territory.address.split('\n').filter(addr => addr.trim());
    console.log(`📋 Found ${addresses.length} addresses in Territory 001\n`);

    // Analyze address distribution
    const analysis = analyzeAddressDistribution(addresses);

    // Create boundary recommendations
    const recommendedBoundary = createBoundaryRecommendations(analysis);

    // Compare with current boundary
    await compareBoundaries(recommendedBoundary);

    console.log('\n🎯 Next Steps:');
    console.log('==============');
    console.log('1. Review the address distribution analysis');
    console.log('2. Compare current vs recommended boundaries');
    console.log('3. If significant differences exist, update the boundary');
    console.log('4. Test the updated boundary on the map');
    console.log('5. Verify all addresses fall within the boundary area');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the analysis
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  analyzeAddressDistribution,
  createBoundaryRecommendations,
  compareBoundaries
};
