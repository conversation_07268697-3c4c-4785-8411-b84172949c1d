// Territory Management Types
// Based on Prisma schema and architecture requirements

export type TerritoryStatus = 'available' | 'assigned' | 'completed' | 'out_of_service';
export type AssignmentStatus = 'active' | 'completed' | 'overdue' | 'cancelled';

export interface Territory {
  id: string;
  congregationId: string;
  territoryNumber: string;
  address: string;
  status: TerritoryStatus;
  boundaries?: GeoJSON.Polygon;
  notes?: string;
  displayOrder?: number;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  congregation?: Congregation;
  assignments?: TerritoryAssignment[];
  currentAssignment?: TerritoryAssignment;
}

export interface TerritoryAssignment {
  id: string;
  territoryId: string;
  memberId: string;
  assignedBy: string;
  assignedAt: Date;
  completedAt?: Date;
  dueDate?: Date;
  status: AssignmentStatus;
  notes?: string;
  congregationId: string;
  createdAt: Date;
  updatedAt: Date;

  // Relations
  territory?: Territory;
  member?: Member;
  assignedByMember?: Member;
  congregation?: Congregation;
}

// Request/Response Types for API
export interface CreateTerritoryRequest {
  territoryNumber: string;
  address: string;
  notes?: string;
  congregationId: string;
}

export interface UpdateTerritoryRequest {
  territoryNumber?: string;
  address?: string;
  status?: TerritoryStatus;
  notes?: string;
}

export interface CreateAssignmentRequest {
  territoryId: string;
  memberId: string;
  assignedBy: string;
  dueDate?: Date;
  notes?: string;
  congregationId: string;
}

export interface TerritoryFilters {
  status?: TerritoryStatus;
  search?: string;
  assignedMemberId?: string;
}

export interface AssignmentFilters {
  status?: AssignmentStatus;
  territoryId?: string;
  memberId?: string;
  assignedBy?: string;
}

// Database validation schemas (for use with Zod)
export const TerritoryStatusValues = ['available', 'assigned', 'completed', 'out_of_service'] as const;
export const AssignmentStatusValues = ['active', 'completed', 'overdue', 'cancelled'] as const;

// Utility types for database operations
export interface TerritoryWithAssignments extends Territory {
  assignments: TerritoryAssignment[];
  currentAssignment?: TerritoryAssignment;
}

export interface AssignmentWithDetails extends TerritoryAssignment {
  territory: Territory;
  member: Member;
  assignedByMember: Member;
}

// Basic types for related models (to avoid circular dependencies)
interface Congregation {
  id: string;
  name: string;
}

interface Member {
  id: string;
  name: string;
  email?: string;
  role: string;
}
