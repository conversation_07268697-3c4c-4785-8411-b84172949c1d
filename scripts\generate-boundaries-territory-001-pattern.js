#!/usr/bin/env node

/**
 * Generate Territory Boundaries Using Territory 001 Pattern
 * 
 * This script uses the EXACT same methodology that was used for Territory 001
 * to generate consistent, accurate boundaries for all territories.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Miami Street Grid Coordinate System - EXACT same as Territory 001
 */
const MIAMI_GRID = {
  // Longitude coordinates for avenues (East-West)
  NW_65_AVE: -80.2725,
  NW_66_AVE: -80.2750,
  NW_67_AVE: -80.2775,
  NW_68_AVE: -80.2800,
  NW_69_AVE: -80.2825,
  NW_70_AVE: -80.2850,
  
  // Latitude coordinates for streets (North-South)
  TAMIAMI_CANAL_RD: 25.7630,
  NW_2_ST: 25.7620,
  LOW_ADDRESSES: 25.7580,    // 20-100 address range
  MID_ADDRESSES: 25.7590,    // 100-200 address range
  HIGH_ADDRESSES: 25.7600,   // 200-300+ address range
  
  // Get avenue longitude by number
  getAvenueLongitude: (aveNumber) => {
    const baseAve = 65;
    const baseLng = -80.2725;
    const increment = -0.0025; // Each avenue west adds 0.0025
    return baseLng + ((aveNumber - baseAve) * increment);
  },
  
  // Get latitude from address number range
  getLatitudeFromAddressRange: (minNumber, maxNumber) => {
    if (maxNumber <= 100) return 25.7580;
    if (maxNumber <= 200) return 25.7590;
    if (maxNumber <= 300) return 25.7600;
    return 25.7610;
  }
};

/**
 * Analyze territory addresses using Territory 001 pattern
 */
function analyzeTerritoryAddresses(territoryNumber, addresses) {
  console.log(`📍 Analyzing Territory ${territoryNumber} (Territory 001 pattern)`);
  
  const addressList = addresses.split('\n').filter(addr => addr.trim());
  
  const analysis = {
    avenues: new Set(),
    streets: new Set(),
    minNumber: Infinity,
    maxNumber: -Infinity,
    hasTagiamiCanal: false,
    hasNW2St: false
  };
  
  addressList.forEach(addr => {
    // Extract avenue numbers (NW XX AVE)
    const aveMatch = addr.match(/NW\s+(\d+)\s+AVE/i);
    if (aveMatch) {
      analysis.avenues.add(parseInt(aveMatch[1]));
    }
    
    // Check for special streets
    if (addr.includes('TAMIAMI CANAL RD')) {
      analysis.hasTagiamiCanal = true;
    }
    if (addr.includes('NW 2 ST')) {
      analysis.hasNW2St = true;
    }
    
    // Extract address numbers
    const numberMatch = addr.match(/^(\d+)/);
    if (numberMatch) {
      const number = parseInt(numberMatch[1]);
      analysis.minNumber = Math.min(analysis.minNumber, number);
      analysis.maxNumber = Math.max(analysis.maxNumber, number);
    }
  });
  
  console.log(`   Avenues: ${Array.from(analysis.avenues).sort().join(', ')}`);
  console.log(`   Address range: ${analysis.minNumber} - ${analysis.maxNumber}`);
  console.log(`   Special streets: ${analysis.hasTagiamiCanal ? 'Tamiami Canal Rd' : ''} ${analysis.hasNW2St ? 'NW 2 ST' : ''}`);
  
  return analysis;
}

/**
 * Create boundary using EXACT Territory 001 pattern
 */
function createBoundaryTerritory001Pattern(territoryNumber, analysis) {
  console.log(`   Creating boundary for Territory ${territoryNumber} (Territory 001 pattern)`);
  
  // Determine avenue boundaries (East-West)
  const avenues = Array.from(analysis.avenues).sort((a, b) => a - b);
  let eastBoundary, westBoundary;
  
  if (avenues.length > 0) {
    const minAve = Math.min(...avenues);
    const maxAve = Math.max(...avenues);
    
    // Use exact avenue coordinates
    eastBoundary = MIAMI_GRID.getAvenueLongitude(minAve);
    westBoundary = MIAMI_GRID.getAvenueLongitude(maxAve);
  } else {
    // Default if no avenues found
    eastBoundary = MIAMI_GRID.NW_65_AVE;
    westBoundary = MIAMI_GRID.NW_67_AVE;
  }
  
  // Determine latitude boundaries (North-South) - EXACT Territory 001 logic
  let northBoundary = MIAMI_GRID.getLatitudeFromAddressRange(analysis.minNumber, analysis.maxNumber);
  let southBoundary = MIAMI_GRID.LOW_ADDRESSES;
  
  // Special street handling - EXACT Territory 001 logic
  if (analysis.hasTagiamiCanal) {
    northBoundary = MIAMI_GRID.TAMIAMI_CANAL_RD;
  }
  if (analysis.hasNW2St) {
    northBoundary = Math.max(northBoundary, MIAMI_GRID.NW_2_ST);
  }
  
  // Ensure minimum boundary size (same as Territory 001)
  const minLatSpan = 0.005;  // Same as Territory 001
  const minLngSpan = 0.005;  // Same as Territory 001
  
  if (northBoundary - southBoundary < minLatSpan) {
    const center = (northBoundary + southBoundary) / 2;
    southBoundary = center - minLatSpan / 2;
    northBoundary = center + minLatSpan / 2;
  }
  
  if (eastBoundary - westBoundary < minLngSpan) {
    const center = (eastBoundary + westBoundary) / 2;
    westBoundary = center - minLngSpan / 2;
    eastBoundary = center + minLngSpan / 2;
  }
  
  // Create boundary - EXACT Territory 001 pattern
  const boundary = {
    type: 'Polygon',
    coordinates: [[
      [westBoundary, northBoundary],   // Northwest
      [eastBoundary, northBoundary],   // Northeast
      [eastBoundary, southBoundary],   // Southeast
      [westBoundary, southBoundary],   // Southwest
      [westBoundary, northBoundary]    // Close polygon
    ]]
  };
  
  console.log(`   Boundary: NW[${westBoundary.toFixed(4)}, ${northBoundary.toFixed(4)}] to SE[${eastBoundary.toFixed(4)}, ${southBoundary.toFixed(4)}]`);
  
  return boundary;
}

/**
 * Generate boundaries using Territory 001 pattern
 */
async function generateBoundariesTerritory001Pattern() {
  try {
    console.log('🗺️  Generating Boundaries Using Territory 001 Pattern');
    console.log('====================================================\n');
    
    // Get all territories
    const territories = await prisma.territory.findMany({
      where: {
        congregationId: '1441'
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        boundaries: true
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });
    
    console.log(`📊 Found ${territories.length} territories to process\n`);
    
    let processedCount = 0;
    let updatedCount = 0;
    
    for (const territory of territories) {
      processedCount++;
      
      console.log(`\n🔄 Processing Territory ${territory.territoryNumber} (${processedCount}/${territories.length})`);
      
      // Analyze addresses using Territory 001 pattern
      const analysis = analyzeTerritoryAddresses(territory.territoryNumber, territory.address);
      
      // Create boundary using Territory 001 pattern
      const boundary = createBoundaryTerritory001Pattern(territory.territoryNumber, analysis);
      
      // Update database
      await prisma.territory.update({
        where: { id: territory.id },
        data: { boundaries: boundary }
      });
      
      console.log(`✅ Territory ${territory.territoryNumber}: Boundary updated with Territory 001 pattern`);
      updatedCount++;
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    console.log('\n📊 Generation Summary (Territory 001 Pattern):');
    console.log('===============================================');
    console.log(`Total territories: ${territories.length}`);
    console.log(`Updated: ${updatedCount}`);
    console.log(`Pattern: Territory 001 methodology`);
    
    console.log('\n✅ All territories now use Territory 001 boundary pattern!');
    
  } catch (error) {
    console.error('❌ Error generating boundaries:', error);
  }
}

/**
 * Test Territory 002 with new pattern
 */
async function testTerritory002NewPattern() {
  try {
    console.log('\n🧪 Testing Territory 002 with Territory 001 Pattern');
    console.log('===================================================\n');
    
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '002'
      },
      select: {
        territoryNumber: true,
        boundaries: true
      }
    });
    
    if (territory && territory.boundaries) {
      console.log('📍 Territory 002 Boundary (Territory 001 Pattern):');
      territory.boundaries.coordinates[0].forEach((coord, index) => {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
        console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}]`);
      });
    } else {
      console.log('❌ Territory 002 not found or no boundaries');
    }
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

/**
 * Main function
 */
async function main() {
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'generate':
        await generateBoundariesTerritory001Pattern();
        await testTerritory002NewPattern();
        break;
      case 'test':
        await testTerritory002NewPattern();
        break;
      default:
        console.log('🗺️  Territory Boundary Generator (Territory 001 Pattern)');
        console.log('========================================================\n');
        console.log('Usage: node scripts/generate-boundaries-territory-001-pattern.js <command>\n');
        console.log('Commands:');
        console.log('  generate  - Generate boundaries using Territory 001 pattern');
        console.log('  test      - Test Territory 002 with new pattern');
        console.log('\n💡 This uses the EXACT same methodology as Territory 001');
    }
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  analyzeTerritoryAddresses,
  createBoundaryTerritory001Pattern,
  generateBoundariesTerritory001Pattern,
  MIAMI_GRID
};
