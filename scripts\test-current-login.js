#!/usr/bin/env node

const fetch = require('node-fetch');

async function testCurrentLogin() {
  try {
    console.log('🔍 Testing current login flow...');
    
    // Test login with congregation 1441 and PIN 1930
    const loginResponse = await fetch('http://localhost:3000/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1930'
      })
    });
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginResponse.status);
      const errorText = await loginResponse.text();
      console.log('Error:', errorText);
      return;
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ Login successful!');
    console.log(`👤 Logged in as: ${loginData.user.name}`);
    console.log(`🎭 Role: ${loginData.user.role}`);
    console.log(`🏢 Congregation: ${loginData.user.congregationId}`);
    
    // Check admin permissions
    const adminRoles = ['elder', 'overseer_coordinator', 'developer', 'ministerial_servant'];
    const hasAdminAccess = adminRoles.includes(loginData.user.role);
    console.log(`🔑 Has admin access: ${hasAdminAccess ? '✅ YES' : '❌ NO'}`);
    
    if (hasAdminAccess) {
      console.log('🎉 Admin button should be visible!');
    } else {
      console.log('⚠️  Admin button will NOT be visible');
    }
    
    // Test dashboard API with the token
    console.log('\n📊 Testing dashboard API...');
    const dashboardResponse = await fetch('http://localhost:3000/api/dashboard', {
      headers: {
        'Authorization': `Bearer ${loginData.token}`
      }
    });
    
    if (dashboardResponse.ok) {
      const dashboardData = await dashboardResponse.json();
      console.log('✅ Dashboard API successful');
      console.log(`🔑 Permissions - canAccessAdmin: ${dashboardData.permissions?.canAccessAdmin ? '✅ YES' : '❌ NO'}`);
      
      if (dashboardData.permissions?.canAccessAdmin) {
        console.log('🎉 Admin button should definitely be visible!');
      }
    } else {
      console.log('❌ Dashboard API failed:', dashboardResponse.status);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testCurrentLogin();
