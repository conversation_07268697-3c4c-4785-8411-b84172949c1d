/**
 * Import Real Territories Script
 * 
 * Imports territory data from the actual Excel files in the Territorios directory.
 * Uses the existing ExcelImportService to process the files.
 */

const fs = require('fs');
const path = require('path');
const XLSX = require('xlsx');

async function importRealTerritories() {
  try {
    console.log('📂 Importing real territories from Excel files...');

    const territoriosDir = path.join(__dirname, '..', 'Territorios');
    
    if (!fs.existsSync(territoriosDir)) {
      console.error('❌ Territorios directory not found');
      process.exit(1);
    }

    // Get all Excel files
    const files = fs.readdirSync(territoriosDir)
      .filter(file => file.endsWith('.xlsx'))
      .sort();

    console.log(`📊 Found ${files.length} Excel files`);

    // Process first few files to understand structure
    const sampleFiles = files.slice(0, 3);
    
    for (const filename of sampleFiles) {
      console.log(`\n🔍 Examining: ${filename}`);
      
      const filePath = path.join(territoriosDir, filename);
      const workbook = XLSX.readFile(filePath);
      const sheetNames = workbook.SheetNames;
      
      console.log(`   Sheets: ${sheetNames.join(', ')}`);
      
      // Read first sheet
      const worksheet = workbook.Sheets[sheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      console.log(`   Rows: ${jsonData.length}`);
      
      if (jsonData.length > 0) {
        console.log(`   First row: ${JSON.stringify(jsonData[0])}`);
        if (jsonData.length > 1) {
          console.log(`   Second row: ${JSON.stringify(jsonData[1])}`);
        }
        if (jsonData.length > 2) {
          console.log(`   Third row: ${JSON.stringify(jsonData[2])}`);
        }
      }
    }

    console.log('\n✅ Territory file structure analysis complete!');
    console.log('\n📝 Next steps:');
    console.log('1. Review the structure above');
    console.log('2. Update the import service column mappings if needed');
    console.log('3. Use the admin interface to import territories');

  } catch (error) {
    console.error('❌ Error importing territories:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  importRealTerritories();
}

module.exports = { importRealTerritories };
