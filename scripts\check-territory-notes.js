/**
 * Check Territory Notes
 * 
 * Examines all territory notes to see what's actually stored
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Configuration
const CONGREGATION_ID = '1441'; // Coral Oeste

async function checkTerritoryNotes() {
  try {
    console.log('🔍 Checking all territory notes...');
    
    // Get all territories for the congregation
    const territories = await prisma.territory.findMany({
      where: {
        congregationId: CONGREGATION_ID
      },
      orderBy: {
        displayOrder: 'asc'
      }
    });
    
    console.log(`📊 Found ${territories.length} territories\n`);
    
    for (const territory of territories) {
      console.log(`📋 Territory ${territory.territoryNumber}:`);
      
      // Check addresses for notes
      if (territory.address) {
        const addressLines = territory.address.split('\n');
        const addressesWithNotes = addressLines.filter(line => 
          line.includes('(') || 
          line.toLowerCase().includes('candado') ||
          line.toLowerCase().includes('rejas') ||
          line.toLowerCase().includes('perro')
        );
        
        if (addressesWithNotes.length > 0) {
          console.log('   🏠 Addresses with notes:');
          addressesWithNotes.forEach(addr => {
            console.log(`      ${addr}`);
          });
        }
      }
      
      // Check notes field
      if (territory.notes) {
        console.log('   📝 Notes field:');
        const noteLines = territory.notes.split('\n');
        noteLines.forEach(note => {
          if (note.trim()) {
            console.log(`      ${note}`);
          }
        });
      }
      
      if (!territory.address?.includes('(') && !territory.notes) {
        console.log('   ✅ No notes found');
      }
      
      console.log(''); // Empty line for readability
    }
    
  } catch (error) {
    console.error('❌ Error checking territory notes:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  checkTerritoryNotes();
}

module.exports = { checkTerritoryNotes };
