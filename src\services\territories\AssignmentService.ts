// Territory Assignment Service
// Service for managing territory assignments to congregation members

import { prisma } from '@/lib/prisma';
import {
  AssignmentRequest,
  AssignmentResponse,
  AssignmentValidation,
  MemberWithAssignments,
  TerritoryWithAssignment,
  ASSIGNMENT_VALIDATION_RULES
} from '@/types/territories/assignment';
import { TerritoryAssignment } from '@/types/territories/territory';

export class AssignmentService {
  /**
   * Assign a territory to a member
   */
  static async assignTerritory(
    request: AssignmentRequest,
    assignedBy: string,
    congregationId: string
  ): Promise<AssignmentResponse> {
    try {
      // Validate the assignment request
      const validation = await this.validateAssignment(request, congregationId);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        };
      }

      // Start database transaction
      const result = await prisma.$transaction(async (tx) => {
        // Check if territory is still available
        const territory = await tx.territory.findUnique({
          where: { id: request.territoryId },
          include: {
            currentAssignment: true
          }
        });

        if (!territory) {
          throw new Error('Territory not found');
        }

        if (territory.status !== 'available') {
          throw new Error('Territory is not available for assignment');
        }

        if (territory.currentAssignment) {
          throw new Error('Territory is already assigned');
        }

        // Create the assignment
        const assignment = await tx.territoryAssignment.create({
          data: {
            territoryId: request.territoryId,
            memberId: request.memberId,
            assignedBy,
            assignedAt: new Date(),
            status: 'active',
            notes: request.notes,
            congregationId
          },
          include: {
            territory: {
              select: {
                id: true,
                territoryNumber: true,
                address: true,
                status: true
              }
            },
            member: {
              select: {
                id: true,
                name: true,
                role: true,
                email: true
              }
            },
            assignedByMember: {
              select: {
                id: true,
                name: true,
                role: true
              }
            }
          }
        });

        // Update territory status
        await tx.territory.update({
          where: { id: request.territoryId },
          data: { status: 'assigned' }
        });

        return assignment;
      });

      console.log(`Territory ${request.territoryId} assigned to member ${request.memberId}`);

      return {
        success: true,
        assignment: result as TerritoryAssignment,
        message: 'Territory assigned successfully'
      };

    } catch (error) {
      console.error('Error assigning territory:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Validate an assignment request
   */
  static async validateAssignment(
    request: AssignmentRequest,
    congregationId: string
  ): Promise<AssignmentValidation> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if territory exists and is available
      const territory = await prisma.territory.findUnique({
        where: { id: request.territoryId },
        include: {
          currentAssignment: true
        }
      });

      const territoryAvailable = territory && territory.status === 'available' && !territory.currentAssignment;
      if (!territory) {
        errors.push('Territory not found');
      } else if (territory.congregationId !== congregationId) {
        errors.push('Territory belongs to different congregation');
      } else if (territory.status !== 'available') {
        errors.push(`Territory is ${territory.status} and cannot be assigned`);
      } else if (territory.currentAssignment) {
        errors.push('Territory is already assigned to another member');
      }

      // Check if member exists and is eligible
      const member = await prisma.member.findUnique({
        where: { id: request.memberId },
        include: {
          territoryAssignments: {
            where: { status: 'active' }
          }
        }
      });

      const memberEligible = member && ASSIGNMENT_VALIDATION_RULES.ELIGIBLE_ROLES.includes(member.role);
      if (!member) {
        errors.push('Member not found');
      } else if (member.congregationId !== congregationId) {
        errors.push('Member belongs to different congregation');
      } else if (ASSIGNMENT_VALIDATION_RULES.RESTRICTED_ROLES.includes(member.role)) {
        errors.push(`Members with role '${member.role}' cannot be assigned territories`);
      } else if (!ASSIGNMENT_VALIDATION_RULES.ELIGIBLE_ROLES.includes(member.role)) {
        warnings.push(`Member role '${member.role}' is not typically assigned territories`);
      }

      // Check member workload
      const activeAssignments = member?.territoryAssignments?.length || 0;
      let memberWorkload: 'light' | 'normal' | 'heavy' = 'light';
      
      if (activeAssignments >= ASSIGNMENT_VALIDATION_RULES.MAX_ASSIGNMENTS_PER_MEMBER) {
        errors.push(`Member already has maximum number of assignments (${ASSIGNMENT_VALIDATION_RULES.MAX_ASSIGNMENTS_PER_MEMBER})`);
        memberWorkload = 'heavy';
      } else if (activeAssignments >= 2) {
        warnings.push('Member already has multiple active assignments');
        memberWorkload = 'normal';
      } else if (activeAssignments === 1) {
        memberWorkload = 'normal';
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        territoryAvailable: territoryAvailable || false,
        memberEligible: memberEligible || false,
        memberWorkload,
        suggestedAction: errors.length === 0 ? 'proceed' : 'resolve_errors'
      };

    } catch (error) {
      console.error('Error validating assignment:', error);
      return {
        isValid: false,
        errors: ['Validation failed due to system error'],
        warnings: [],
        territoryAvailable: false,
        memberEligible: false,
        memberWorkload: 'normal'
      };
    }
  }

  /**
   * Get members with their assignment information
   */
  static async getMembersWithAssignments(congregationId: string): Promise<MemberWithAssignments[]> {
    try {
      const members = await prisma.member.findMany({
        where: { congregationId },
        include: {
          territoryAssignments: {
            where: { status: 'active' },
            include: {
              territory: {
                select: {
                  id: true,
                  territoryNumber: true,
                  address: true,
                  status: true
                }
              }
            }
          },
          _count: {
            select: {
              territoryAssignments: true
            }
          }
        }
      });

      return members.map(member => {
        const activeAssignments = member.territoryAssignments.length;
        const totalAssignments = member._count.territoryAssignments;
        
        // Determine workload status
        let workloadStatus: 'light' | 'normal' | 'heavy' = 'light';
        if (activeAssignments >= ASSIGNMENT_VALIDATION_RULES.MAX_ASSIGNMENTS_PER_MEMBER) {
          workloadStatus = 'heavy';
        } else if (activeAssignments >= 2) {
          workloadStatus = 'normal';
        }

        // Determine availability
        const isAvailable = ASSIGNMENT_VALIDATION_RULES.ELIGIBLE_ROLES.includes(member.role) &&
                           !ASSIGNMENT_VALIDATION_RULES.RESTRICTED_ROLES.includes(member.role) &&
                           activeAssignments < ASSIGNMENT_VALIDATION_RULES.MAX_ASSIGNMENTS_PER_MEMBER;

        // Get last assignment date
        const lastAssignmentDate = member.territoryAssignments.length > 0
          ? new Date(Math.max(...member.territoryAssignments.map(a => a.assignedAt.getTime())))
          : undefined;

        return {
          id: member.id,
          name: member.name,
          role: member.role,
          email: member.email,
          phone: member.phone,
          activeAssignments,
          totalAssignments,
          lastAssignmentDate,
          isAvailable,
          workloadStatus,
          assignments: member.territoryAssignments as TerritoryAssignment[]
        };
      });

    } catch (error) {
      console.error('Error getting members with assignments:', error);
      return [];
    }
  }

  /**
   * Get territories with their assignment information
   */
  static async getTerritoriesWithAssignments(congregationId: string): Promise<TerritoryWithAssignment[]> {
    try {
      const territories = await prisma.territory.findMany({
        where: { congregationId },
        include: {
          currentAssignment: {
            include: {
              member: {
                select: {
                  id: true,
                  name: true,
                  role: true,
                  email: true
                }
              },
              assignedByMember: {
                select: {
                  id: true,
                  name: true,
                  role: true
                }
              }
            }
          },
          territoryAssignments: {
            orderBy: { assignedAt: 'desc' },
            take: 5, // Last 5 assignments for history
            include: {
              member: {
                select: {
                  id: true,
                  name: true,
                  role: true
                }
              }
            }
          },
          _count: {
            select: {
              territoryAssignments: true
            }
          }
        }
      });

      return territories.map(territory => {
        const totalAssignments = territory._count.territoryAssignments;
        
        // Calculate average assignment duration
        const completedAssignments = territory.territoryAssignments.filter(a => a.status === 'completed');
        const averageAssignmentDuration = completedAssignments.length > 0
          ? completedAssignments.reduce((sum, assignment) => {
              if (assignment.completedAt) {
                const duration = Math.floor((assignment.completedAt.getTime() - assignment.assignedAt.getTime()) / (1000 * 60 * 60 * 24));
                return sum + duration;
              }
              return sum;
            }, 0) / completedAssignments.length
          : undefined;

        // Get last assignment and return dates
        const lastAssignedDate = territory.territoryAssignments.length > 0
          ? territory.territoryAssignments[0].assignedAt
          : undefined;

        const lastReturnedDate = completedAssignments.length > 0
          ? completedAssignments[0].completedAt
          : undefined;

        return {
          id: territory.id,
          territoryNumber: territory.territoryNumber,
          address: territory.address,
          status: territory.status as 'available' | 'assigned' | 'completed' | 'out_of_service',
          notes: territory.notes,
          congregationId: territory.congregationId,
          createdAt: territory.createdAt,
          updatedAt: territory.updatedAt,
          currentAssignment: territory.currentAssignment as TerritoryAssignment | undefined,
          assignmentHistory: territory.territoryAssignments as TerritoryAssignment[],
          totalAssignments,
          averageAssignmentDuration,
          lastAssignedDate,
          lastReturnedDate
        };
      });

    } catch (error) {
      console.error('Error getting territories with assignments:', error);
      return [];
    }
  }

  /**
   * Return a territory assignment
   */
  static async returnTerritory(
    assignmentId: string,
    returnedBy: string,
    congregationId: string,
    notes?: string
  ): Promise<AssignmentResponse> {
    try {
      const result = await prisma.$transaction(async (tx) => {
        // Get the assignment
        const assignment = await tx.territoryAssignment.findUnique({
          where: { id: assignmentId },
          include: {
            territory: true,
            member: true
          }
        });

        if (!assignment) {
          throw new Error('Assignment not found');
        }

        if (assignment.congregationId !== congregationId) {
          throw new Error('Assignment belongs to different congregation');
        }

        if (assignment.status !== 'active') {
          throw new Error('Assignment is not active');
        }

        // Update assignment status
        const updatedAssignment = await tx.territoryAssignment.update({
          where: { id: assignmentId },
          data: {
            status: 'completed',
            completedAt: new Date(),
            notes: notes ? `${assignment.notes || ''}\n\nReturned: ${notes}`.trim() : assignment.notes
          },
          include: {
            territory: {
              select: {
                id: true,
                territoryNumber: true,
                address: true,
                status: true
              }
            },
            member: {
              select: {
                id: true,
                name: true,
                role: true,
                email: true
              }
            }
          }
        });

        // Update territory status back to available
        await tx.territory.update({
          where: { id: assignment.territoryId },
          data: { status: 'available' }
        });

        return updatedAssignment;
      });

      console.log(`Territory assignment ${assignmentId} returned by ${returnedBy}`);

      return {
        success: true,
        assignment: result as TerritoryAssignment,
        message: 'Territory returned successfully'
      };

    } catch (error) {
      console.error('Error returning territory:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}
