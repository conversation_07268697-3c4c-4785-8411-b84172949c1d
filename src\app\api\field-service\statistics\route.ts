/**
 * Field Service Statistics API Endpoint
 *
 * Provides congregation-wide service statistics and reporting
 * for service coordinators and elders.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { FieldServiceManagementService } from '@/lib/services/fieldServiceManagementService';

// Validation schema for statistics requests
const StatisticsRequestSchema = z.object({
  serviceMonth: z.string().regex(/^\d{4}-\d{2}$/, 'Service month must be in YYYY-MM format').optional(),
  type: z.enum(['summary', 'pending', 'statistics']).default('summary'),
});

/**
 * GET /api/field-service/statistics
 * Retrieve congregation service statistics and reports
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - coordinators, elders and ministerial servants can view statistics
    if (!['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view congregation statistics' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    const validationResult = StatisticsRequestSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { serviceMonth, type } = validationResult.data;
    const targetMonth = serviceMonth || FieldServiceManagementService.getCurrentServiceMonth();

    switch (type) {
      case 'summary':
        // Get complete monthly summary with all member reports
        const summary = await FieldServiceManagementService.getMonthlyServiceSummary(
          member.congregationId,
          targetMonth
        );

        return NextResponse.json({
          success: true,
          summary,
          serviceMonth: targetMonth,
          formattedMonth: FieldServiceManagementService.formatServiceMonth(targetMonth),
        });

      case 'pending':
        // Get members who haven't submitted reports
        const pendingReports = await FieldServiceManagementService.getPendingReports(
          member.congregationId,
          targetMonth
        );

        return NextResponse.json({
          success: true,
          pendingReports,
          count: pendingReports.length,
          serviceMonth: targetMonth,
          formattedMonth: FieldServiceManagementService.formatServiceMonth(targetMonth),
        });

      case 'statistics':
        // Get just the statistics without member details
        const statistics = await FieldServiceManagementService.calculateMonthlyStatistics(
          member.congregationId,
          targetMonth
        );

        return NextResponse.json({
          success: true,
          statistics,
          serviceMonth: targetMonth,
          formattedMonth: FieldServiceManagementService.formatServiceMonth(targetMonth),
        });

      default:
        return NextResponse.json(
          { error: 'Invalid statistics type' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Field service statistics error:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve service statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/field-service/statistics
 * Generate service reports for multiple months or export data
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - coordinators and elders can generate reports
    if (!['elder', 'coordinator', 'overseer_coordinator', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to generate service reports' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { startMonth, endMonth, reportType } = body;

    // Validate date range
    if (!startMonth || !endMonth ||
        !/^\d{4}-\d{2}$/.test(startMonth) ||
        !/^\d{4}-\d{2}$/.test(endMonth)) {
      return NextResponse.json(
        { error: 'Valid start and end months (YYYY-MM) are required' },
        { status: 400 }
      );
    }

    // Generate date range
    const months: string[] = [];
    const start = new Date(startMonth + '-01');
    const end = new Date(endMonth + '-01');

    if (start > end) {
      return NextResponse.json(
        { error: 'Start month must be before or equal to end month' },
        { status: 400 }
      );
    }

    // Limit to 12 months maximum
    const monthsDiff = (end.getFullYear() - start.getFullYear()) * 12 +
                      (end.getMonth() - start.getMonth());

    if (monthsDiff > 11) {
      return NextResponse.json(
        { error: 'Date range cannot exceed 12 months' },
        { status: 400 }
      );
    }

    // Generate month list
    const current = new Date(start);
    while (current <= end) {
      const year = current.getFullYear();
      const month = String(current.getMonth() + 1).padStart(2, '0');
      months.push(`${year}-${month}`);
      current.setMonth(current.getMonth() + 1);
    }

    // Generate reports for each month
    const reports = [];
    for (const month of months) {
      try {
        const summary = await FieldServiceManagementService.getMonthlyServiceSummary(
          member.congregationId,
          month
        );
        reports.push(summary);
      } catch (error) {
        console.error(`Error generating report for ${month}:`, error);
        // Continue with other months
      }
    }

    // Calculate totals across all months
    const totals = reports.reduce((acc, report) => {
      return {
        totalHours: acc.totalHours + report.statistics.totalHours,
        totalPlacements: acc.totalPlacements + report.statistics.totalPlacements,
        totalVideoShowings: acc.totalVideoShowings + report.statistics.totalVideoShowings,
        totalReturnVisits: acc.totalReturnVisits + report.statistics.totalReturnVisits,
        totalBibleStudies: acc.totalBibleStudies + report.statistics.totalBibleStudies,
        averageSubmissionRate: acc.averageSubmissionRate + report.submissionRate,
      };
    }, {
      totalHours: 0,
      totalPlacements: 0,
      totalVideoShowings: 0,
      totalReturnVisits: 0,
      totalBibleStudies: 0,
      averageSubmissionRate: 0,
    });

    // Calculate averages
    if (reports.length > 0) {
      totals.averageSubmissionRate = Math.round(totals.averageSubmissionRate / reports.length);
    }

    return NextResponse.json({
      success: true,
      reports,
      totals,
      dateRange: {
        startMonth,
        endMonth,
        monthsIncluded: months.length,
      },
      reportType: reportType || 'summary',
      generatedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Field service report generation error:', error);

    return NextResponse.json(
      {
        error: 'Failed to generate service report',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
