/**
 * Individual Song API Endpoint
 *
 * Handles operations for specific songs by song number:
 * - Getting individual song details
 * - Updating specific song information
 * - Deleting songs (admin only)
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

// Validation schemas
const updateSongSchema = z.object({
  titleEs: z.string().min(1).max(255).optional(),
  titleEn: z.string().min(1).max(255).optional(),
  category: z.string().max(100).optional(),
  isActive: z.boolean().optional(),
});

interface RouteParams {
  params: {
    songNumber: string;
  };
}

/**
 * GET /api/songs/[songNumber]
 * Get specific song by number
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Verify authentication
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse song number
    const songNumber = parseInt(params.songNumber);
    if (isNaN(songNumber) || songNumber <= 0) {
      return NextResponse.json(
        { error: 'Invalid song number' },
        { status: 400 }
      );
    }

    // Get song
    const song = await prisma.song.findUnique({
      where: { songNumber },
      select: {
        id: true,
        songNumber: true,
        titleEs: true,
        titleEn: true,
        category: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!song) {
      return NextResponse.json(
        { error: 'Song not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ song });

  } catch (error) {
    console.error('Error fetching song:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/songs/[songNumber]
 * Update specific song
 */
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Verify authentication and admin permissions
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!['overseer', 'elder', 'ministerial_servant'].includes(authResult.user.role)) {
      return NextResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    // Parse song number
    const songNumber = parseInt(params.songNumber);
    if (isNaN(songNumber) || songNumber <= 0) {
      return NextResponse.json(
        { error: 'Invalid song number' },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const validationResult = updateSongSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { titleEs, titleEn, category, isActive } = validationResult.data;

    // Check if song exists
    const existingSong = await prisma.song.findUnique({
      where: { songNumber },
    });

    if (!existingSong) {
      return NextResponse.json(
        { error: 'Song not found' },
        { status: 404 }
      );
    }

    // Update song
    const updatedSong = await prisma.song.update({
      where: { songNumber },
      data: {
        ...(titleEs !== undefined && { titleEs }),
        ...(titleEn !== undefined && { titleEn }),
        ...(category !== undefined && { category }),
        ...(isActive !== undefined && { isActive }),
      },
      select: {
        id: true,
        songNumber: true,
        titleEs: true,
        titleEn: true,
        category: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({
      song: updatedSong,
      message: 'Song updated successfully',
    });

  } catch (error) {
    console.error('Error updating song:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/songs/[songNumber]
 * Delete specific song (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Verify authentication and admin permissions
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has elder permissions
    if (!['overseer', 'elder'].includes(authResult.user.role)) {
      return NextResponse.json(
        { error: 'Elder permissions required for song deletion' },
        { status: 403 }
      );
    }

    // Parse song number
    const songNumber = parseInt(params.songNumber);
    if (isNaN(songNumber) || songNumber <= 0) {
      return NextResponse.json(
        { error: 'Invalid song number' },
        { status: 400 }
      );
    }

    // Check if song exists
    const existingSong = await prisma.song.findUnique({
      where: { songNumber },
    });

    if (!existingSong) {
      return NextResponse.json(
        { error: 'Song not found' },
        { status: 404 }
      );
    }

    // Delete song
    await prisma.song.delete({
      where: { songNumber },
    });

    return NextResponse.json({
      message: 'Song deleted successfully',
      songNumber,
    });

  } catch (error) {
    console.error('Error deleting song:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
