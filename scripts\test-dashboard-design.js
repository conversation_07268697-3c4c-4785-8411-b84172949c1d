#!/usr/bin/env node

/**
 * Dashboard Design Test Script for Hermanos App
 *
 * Tests the dashboard implementation to ensure it matches the exact design
 * from the screenshot and includes all required functionality.
 */

const { PrismaClient } = require('@prisma/client');

class DashboardDesignTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = {
      authenticationTested: false,
      dashboardLoaded: false,
      designVerified: false,
      errors: [],
    };
  }

  async initialize() {
    try {
      await this.prisma.$connect();
      console.log('✅ Database connected');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async testAuthentication() {
    console.log('\n🔐 Testing authentication...');

    try {
      // Test login with existing congregation
      const loginData = {
        region: 'North America',
        congregationId: '1441',
        pin: '1930',
        rememberMe: false,
      };

      const response = await fetch('http://localhost:3000/api/auth/congregation-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('✅ Authentication successful');
        console.log(`   User: ${data.user?.name} (${data.user?.role})`);
        console.log(`   Congregation: ${data.congregation?.name}`);
        console.log(`   Admin Access: ${data.permissions?.canAccessAdmin ? 'Yes' : 'No'}`);

        this.testResults.authenticationTested = true;
        return data.token;
      } else {
        console.error('❌ Authentication failed:', data.error || 'Unknown error');
        this.testResults.errors.push(`Authentication: ${data.error || 'Unknown error'}`);
        return null;
      }

    } catch (error) {
      console.error('❌ Error testing authentication:', error.message);
      this.testResults.errors.push(`Authentication: ${error.message}`);
      return null;
    }
  }

  async testDashboardAPI(token) {
    console.log('\n📊 Testing dashboard API...');

    try {
      const response = await fetch('http://localhost:3000/api/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('✅ Dashboard API working');
        console.log(`   Current Month Hours: ${data.data?.currentMonthHours || 0}`);
        console.log(`   Upcoming Meetings: ${data.data?.upcomingMeetings || 0}`);
        console.log(`   Pending Tasks: ${data.data?.pendingTasks || 0}`);

        this.testResults.dashboardLoaded = true;
        return true;
      } else {
        console.error('❌ Dashboard API failed:', data.error || 'Unknown error');
        this.testResults.errors.push(`Dashboard API: ${data.error || 'Unknown error'}`);
        return false;
      }

    } catch (error) {
      console.error('❌ Error testing dashboard API:', error.message);
      this.testResults.errors.push(`Dashboard API: ${error.message}`);
      return false;
    }
  }

  async testDashboardPage() {
    console.log('\n🎨 Testing dashboard page...');

    try {
      const response = await fetch('http://localhost:3000/dashboard');

      if (response.ok) {
        const html = await response.text();

        // Check for key design elements
        const designChecks = [
          { name: 'Blue header', check: html.includes('bg-blue-600') },
          { name: 'Congregation name display', check: html.includes('Salón Del Reino') },
          { name: 'Ocean background', check: html.includes('bg-gradient-to-r from-blue-400 to-blue-600') },
          { name: 'Bottom navigation', check: html.includes('fixed bottom-0') },
          { name: 'Administración section', check: html.includes('Administración') },
          { name: 'Servicio al campo', check: html.includes('Servicio al campo') },
          { name: 'Programas section', check: html.includes('Programas') },
          { name: 'Reuniones section', check: html.includes('Reuniones') },
          { name: 'Entre semana', check: html.includes('Entre semana') },
          { name: 'Fin de semana', check: html.includes('Fin de semana') },
          { name: 'Asignaciones', check: html.includes('Asignaciones') },
          { name: 'Tareas', check: html.includes('Tareas') },
          { name: 'Comunicación section', check: html.includes('Comunicación') },
          { name: 'Cartas', check: html.includes('Cartas') },
          { name: 'Eventos', check: html.includes('Eventos') },
        ];

        let passedChecks = 0;
        console.log('\n   Design Element Checks:');

        designChecks.forEach(check => {
          if (check.check) {
            console.log(`   ✅ ${check.name}`);
            passedChecks++;
          } else {
            console.log(`   ❌ ${check.name}`);
            this.testResults.errors.push(`Design: Missing ${check.name}`);
          }
        });

        const designScore = (passedChecks / designChecks.length) * 100;
        console.log(`\n   Design Score: ${designScore.toFixed(1)}% (${passedChecks}/${designChecks.length})`);

        if (designScore >= 90) {
          console.log('✅ Dashboard design verification passed');
          this.testResults.designVerified = true;
        } else {
          console.log('❌ Dashboard design verification failed');
          this.testResults.errors.push(`Design: Only ${designScore.toFixed(1)}% of elements found`);
        }

        return designScore >= 90;

      } else {
        console.error('❌ Dashboard page failed to load:', response.status);
        this.testResults.errors.push(`Dashboard page: HTTP ${response.status}`);
        return false;
      }

    } catch (error) {
      console.error('❌ Error testing dashboard page:', error.message);
      this.testResults.errors.push(`Dashboard page: ${error.message}`);
      return false;
    }
  }

  async generateTestReport() {
    console.log('\n📋 Generating test report...');

    const totalTests = 3;
    const passedTests = [
      this.testResults.authenticationTested,
      this.testResults.dashboardLoaded,
      this.testResults.designVerified,
    ].filter(Boolean).length;

    console.log('\n' + '='.repeat(60));
    console.log('📊 DASHBOARD DESIGN TEST REPORT');
    console.log('='.repeat(60));
    console.log(`✅ Tests passed: ${passedTests}/${totalTests}`);
    console.log(`❌ Errors: ${this.testResults.errors.length}`);

    if (this.testResults.errors.length > 0) {
      console.log('\n🚨 ERRORS:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    const allTestsPassed = this.testResults.errors.length === 0 && passedTests === totalTests;
    console.log('\n' + (allTestsPassed ? '✅ All tests PASSED!' : '❌ Some tests FAILED!'));
    console.log('='.repeat(60));

    return allTestsPassed;
  }

  async runFullTest() {
    console.log('🧪 Starting dashboard design test...\n');

    try {
      await this.initialize();

      const token = await this.testAuthentication();
      if (token) {
        await this.testDashboardAPI(token);
      }
      await this.testDashboardPage();

      const allTestsPassed = await this.generateTestReport();

      if (!allTestsPassed) {
        process.exit(1);
      }

      console.log('\n✅ Dashboard design test completed successfully!');

    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Main execution
async function main() {
  const tester = new DashboardDesignTester();
  await tester.runFullTest();
}

// Run test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = DashboardDesignTester;
