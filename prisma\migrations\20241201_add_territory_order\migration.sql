-- Add order field to territories table to preserve Excel sheet order
ALTER TABLE "territories" ADD COLUMN "display_order" INTEGER;

-- Create index for efficient ordering
CREATE INDEX "territories_display_order_idx" ON "territories"("display_order");

-- Update existing territories with default order based on territory number
UPDATE "territories" SET "display_order" = CAST("territory_number" AS INTEGER) WHERE "territory_number" ~ '^[0-9]+$';
