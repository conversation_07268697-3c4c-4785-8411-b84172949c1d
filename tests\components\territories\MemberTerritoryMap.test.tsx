import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import MemberTerritoryMap from '@/components/territories/member/MemberTerritoryMap';
import type { Territory } from '@/types/territories/map';

// Mock MapLibre GL JS
jest.mock('maplibre-gl', () => ({
  Map: jest.fn(() => ({
    on: jest.fn(),
    off: jest.fn(),
    remove: jest.fn(),
    addSource: jest.fn(),
    addLayer: jest.fn(),
    removeLayer: jest.fn(),
    removeSource: jest.fn(),
    fitBounds: jest.fn(),
    setCenter: jest.fn(),
    setZoom: jest.fn(),
    getCanvas: jest.fn(() => ({ style: {} })),
  })),
  Marker: jest.fn(() => ({
    setLngLat: jest.fn().mockReturnThis(),
    setPopup: jest.fn().mockReturnThis(),
    addTo: jest.fn().mockReturnThis(),
    remove: jest.fn(),
    getElement: jest.fn(() => ({ style: {} })),
  })),
  Popup: jest.fn(() => ({
    setHTML: jest.fn().mockReturnThis(),
    setLngLat: jest.fn().mockReturnThis(),
    addTo: jest.fn().mockReturnThis(),
  })),
  NavigationControl: jest.fn(),
  GeolocateControl: jest.fn(),
}));

// Mock TerritoryMap component
jest.mock('@/components/territories/shared/TerritoryMap', () => {
  return function MockTerritoryMap({ territories, onTerritorySelect, height, width, className }: any) {
    return (
      <div
        data-testid="territory-map"
        className={className}
        style={{ height, width }}
      >
        <div>Mock Territory Map</div>
        <div>Territories: {territories?.length || 0}</div>
        {territories?.map((territory: Territory) => (
          <button
            key={territory.id}
            onClick={() => onTerritorySelect?.(territory)}
            data-testid={`territory-${territory.id}`}
          >
            Territory {territory.territoryNumber}
          </button>
        ))}
      </div>
    );
  };
});

// Mock fetch for API calls
global.fetch = jest.fn();

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock territories for testing
const mockMemberTerritories: Territory[] = [
  {
    id: '1',
    territoryNumber: '007',
    address: '123 Main St, Miami, FL 33144',
    coordinates: { latitude: 25.7617, longitude: -80.2711 },
    status: 'assigned'
  },
  {
    id: '2',
    territoryNumber: '014',
    address: '456 Oak Ave, Miami, FL 33144',
    coordinates: { latitude: 25.7620, longitude: -80.2715 },
    status: 'assigned'
  },
  {
    id: '3',
    territoryNumber: '025',
    address: '789 Pine St, Miami, FL 33144',
    coordinates: { latitude: 25.7615, longitude: -80.2708 },
    status: 'assigned'
  }
];

// Add assignment info to territories
mockMemberTerritories.forEach(territory => {
  (territory as any).currentAssignment = {
    id: `assignment-${territory.id}`,
    memberId: 'member-1',
    memberName: 'Juan Pérez',
    assignedAt: '2024-01-15T10:00:00Z'
  };
});

describe('MemberTerritoryMap Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('mock-token');
  });

  it('renders loading state initially', () => {
    (fetch as jest.Mock).mockImplementation(() => new Promise(() => {})); // Never resolves

    render(<MemberTerritoryMap />);

    expect(screen.getByText('Cargando mis territorios...')).toBeInTheDocument();
    // Check for loading spinner by class instead of role
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('fetches member territories on mount', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ territories: mockMemberTerritories })
    });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        '/api/territories/map-data?memberView=true&includeAssignments=true',
        {
          headers: {
            'Authorization': 'Bearer mock-token'
          }
        }
      );
    });
  });

  it('displays territory count correctly', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ territories: mockMemberTerritories })
    });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('Mis Territorios: 3')).toBeInTheDocument();
      expect(screen.getByText('3 asignados')).toBeInTheDocument();
    });
  });

  it('shows route optimization button when multiple territories', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ territories: mockMemberTerritories })
    });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('Optimizar Ruta')).toBeInTheDocument();
    });
  });

  it('toggles route optimization', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ territories: mockMemberTerritories })
    });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      const optimizeButton = screen.getByText('Optimizar Ruta');
      fireEvent.click(optimizeButton);
      expect(screen.getByText('Ocultar Ruta')).toBeInTheDocument();
    });
  });

  it('handles error state correctly', async () => {
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('Error al cargar territorios')).toBeInTheDocument();
      expect(screen.getByText('Network error')).toBeInTheDocument();
      expect(screen.getByText('Reintentar')).toBeInTheDocument();
    });
  });

  it('shows no territories message when empty', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ territories: [] })
    });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('No tienes territorios asignados')).toBeInTheDocument();
      expect(screen.getByText('Contacta a un anciano para que te asigne territorios.')).toBeInTheDocument();
    });
  });

  it('handles retry functionality', async () => {
    (fetch as jest.Mock)
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ territories: mockMemberTerritories })
      });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('Error al cargar territorios')).toBeInTheDocument();
    });

    const retryButton = screen.getByText('Reintentar');
    fireEvent.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText('Mis Territorios: 3')).toBeInTheDocument();
    });

    expect(fetch).toHaveBeenCalledTimes(2);
  });

  it('handles authentication error', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      statusText: 'Unauthorized'
    });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('Error al cargar territorios')).toBeInTheDocument();
      expect(screen.getByText('Failed to fetch territories: Unauthorized')).toBeInTheDocument();
    });
  });

  it('calculates distance between territories correctly', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ territories: mockMemberTerritories })
    });

    const { container } = render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('Optimizar Ruta')).toBeInTheDocument();
    });

    // Test distance calculation by triggering route optimization
    const optimizeButton = screen.getByText('Optimizar Ruta');
    fireEvent.click(optimizeButton);

    // The component should calculate distances and optimize the route
    // This is tested indirectly through the route optimization functionality
    expect(screen.getByText('Ocultar Ruta')).toBeInTheDocument();
  });

  it('handles territory completion', async () => {
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ territories: mockMemberTerritories })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ territories: mockMemberTerritories.slice(1) }) // One less territory
      });

    // Mock window.alert
    window.alert = jest.fn();

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('Mis Territorios: 3')).toBeInTheDocument();
    });

    // Simulate territory selection and completion
    // Note: This would require more complex mocking of the TerritoryMap component
    // For now, we test the API call structure
    expect(fetch).toHaveBeenCalledWith(
      '/api/territories/map-data?memberView=true&includeAssignments=true',
      expect.objectContaining({
        headers: {
          'Authorization': 'Bearer mock-token'
        }
      })
    );
  });

  it('handles missing authentication token', async () => {
    mockLocalStorage.getItem.mockReturnValue(null);

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('No tienes territorios asignados')).toBeInTheDocument();
    });
  });

  it('renders with custom props', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ territories: mockMemberTerritories })
    });

    render(
      <MemberTerritoryMap
        height="400px"
        width="100%"
        className="custom-class"
        showControls={false}
      />
    );

    await waitFor(() => {
      expect(screen.getByText(/Territories:/)).toBeInTheDocument();
    });

    const container = screen.getByTestId('territory-map');
    expect(container).toHaveStyle('height: 400px');
  });

  // Note: Route optimization logic is tested through the component behavior
  // The button appears when territories.length > 1, which is covered by other tests
});

describe('Route Optimization Algorithm', () => {
  it('calculates distances correctly', () => {
    // Test the distance calculation formula
    const territory1 = mockMemberTerritories[0];
    const territory2 = mockMemberTerritories[1];

    // Calculate expected distance using Haversine formula
    const lat1 = territory1.coordinates!.latitude;
    const lon1 = territory1.coordinates!.longitude;
    const lat2 = territory2.coordinates!.latitude;
    const lon2 = territory2.coordinates!.longitude;

    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const expectedDistance = R * c;

    // The distance should be very small since the coordinates are close
    expect(expectedDistance).toBeLessThan(1); // Less than 1 km
    expect(expectedDistance).toBeGreaterThan(0);
  });

  it('handles territories without coordinates', () => {
    const territoryWithoutCoords = {
      ...mockMemberTerritories[0],
      coordinates: undefined
    };

    // Distance calculation should return Infinity for territories without coordinates
    // This is tested indirectly through the component behavior
    expect(territoryWithoutCoords.coordinates).toBeUndefined();
  });
});

describe('API Integration', () => {
  it('sends correct API request for member territories', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ territories: mockMemberTerritories })
    });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        '/api/territories/map-data?memberView=true&includeAssignments=true',
        {
          headers: {
            'Authorization': 'Bearer mock-token'
          }
        }
      );
    });
  });

  it('handles API response correctly', async () => {
    const apiResponse = {
      territories: mockMemberTerritories,
      total: mockMemberTerritories.length,
      memberView: true
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => apiResponse
    });

    render(<MemberTerritoryMap />);

    await waitFor(() => {
      expect(screen.getByText('Mis Territorios: 3')).toBeInTheDocument();
    });
  });
});
