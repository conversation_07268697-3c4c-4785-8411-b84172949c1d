# Database Schema

Based on the data models defined earlier and the local PostgreSQL database configuration, here's the concrete database schema using Prisma ORM.

```sql
-- <PERSON>os Multi-Congregation Database Schema
-- PostgreSQL with Prisma ORM
-- Database: hermanos (localhost:5432)

-- Enable UUID extension for primary keys
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Congregations table - Central tenant entity
CREATE TABLE congregations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    region VARCHAR(100) NOT NULL,
    pin VARCHAR(255) NOT NULL, -- bcrypt hashed
    language VARCHAR(10) NOT NULL DEFAULT 'es',
    timezone VARCHAR(50) NOT NULL DEFAULT 'America/Mexico_City',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance
    CONSTRAINT congregations_name_region_unique UNIQUE (name, region)
);

-- Members table - User accounts with role-based access
CREATE TABLE members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    pin VARCHAR(255) NOT NULL, -- bcrypt hashed
    role VARCHAR(50) NOT NULL CHECK (role IN ('publisher', 'ministerial_servant', 'elder', 'overseer_coordinator', 'developer')),
    service_group VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance and tenant isolation
    INDEX idx_members_congregation_id (congregation_id),
    INDEX idx_members_role (role),
    INDEX idx_members_active (is_active),
    CONSTRAINT members_email_congregation_unique UNIQUE (email, congregation_id)
);

-- Meetings table - Base entity for midweek and weekend meetings
CREATE TABLE meetings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('midweek', 'weekend')),
    date DATE NOT NULL,
    chairman_id UUID REFERENCES members(id) ON DELETE SET NULL,
    location VARCHAR(20) NOT NULL DEFAULT 'kingdom_hall' CHECK (location IN ('kingdom_hall', 'zoom')),
    zoom_details JSONB,
    status VARCHAR(20) NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance and tenant isolation
    INDEX idx_meetings_congregation_id (congregation_id),
    INDEX idx_meetings_date (date),
    INDEX idx_meetings_type (type),
    CONSTRAINT meetings_congregation_date_type_unique UNIQUE (congregation_id, date, type)
);

-- Meeting parts table - Individual meeting segments with assignments
CREATE TABLE meeting_parts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    meeting_id UUID NOT NULL REFERENCES meetings(id) ON DELETE CASCADE,
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    part_type VARCHAR(50) NOT NULL CHECK (part_type IN ('treasures', 'digging', 'living', 'public_talk', 'watchtower')),
    title VARCHAR(500) NOT NULL,
    duration INTEGER NOT NULL DEFAULT 0, -- minutes
    assigned_member_id UUID REFERENCES members(id) ON DELETE SET NULL,
    assistant_id UUID REFERENCES members(id) ON DELETE SET NULL,
    song_number INTEGER,
    notes TEXT,
    display_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance and tenant isolation
    INDEX idx_meeting_parts_meeting_id (meeting_id),
    INDEX idx_meeting_parts_congregation_id (congregation_id),
    INDEX idx_meeting_parts_assigned_member (assigned_member_id),
    INDEX idx_meeting_parts_order (display_order)
);

-- Field service records table - Service time tracking
CREATE TABLE field_service_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    hours INTEGER NOT NULL DEFAULT 0,
    minutes INTEGER NOT NULL DEFAULT 0 CHECK (minutes >= 0 AND minutes < 60),
    activity_type VARCHAR(30) NOT NULL CHECK (activity_type IN ('field_service', 'return_visit', 'bible_study', 'public_witnessing')),
    notes TEXT,
    service_year INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance and tenant isolation
    INDEX idx_field_service_member_id (member_id),
    INDEX idx_field_service_congregation_id (congregation_id),
    INDEX idx_field_service_date (date),
    INDEX idx_field_service_year (service_year),
    CONSTRAINT field_service_member_date_unique UNIQUE (member_id, date)
);

-- Letters table - Document management
CREATE TABLE letters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('announcement', 'instruction', 'form', 'schedule', 'other')),
    visibility VARCHAR(20) NOT NULL CHECK (visibility IN ('public', 'elders_only', 'ms_and_elders')),
    uploaded_by UUID NOT NULL REFERENCES members(id) ON DELETE RESTRICT,
    upload_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    file_size INTEGER,
    mime_type VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance and tenant isolation
    INDEX idx_letters_congregation_id (congregation_id),
    INDEX idx_letters_category (category),
    INDEX idx_letters_visibility (visibility),
    INDEX idx_letters_upload_date (upload_date)
);

-- Row Level Security (RLS) policies for multi-tenancy
ALTER TABLE members ENABLE ROW LEVEL SECURITY;
ALTER TABLE meetings ENABLE ROW LEVEL SECURITY;
ALTER TABLE meeting_parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE field_service_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE letters ENABLE ROW LEVEL SECURITY;

-- Example RLS policy for members table
CREATE POLICY members_congregation_isolation ON members
    FOR ALL
    USING (congregation_id = current_setting('app.current_congregation_id')::UUID);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_congregations_updated_at BEFORE UPDATE ON congregations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_members_updated_at BEFORE UPDATE ON members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meetings_updated_at BEFORE UPDATE ON meetings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meeting_parts_updated_at BEFORE UPDATE ON meeting_parts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_field_service_records_updated_at BEFORE UPDATE ON field_service_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_letters_updated_at BEFORE UPDATE ON letters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```
