// Territory Validation Types
// Types for territory data validation, cleaning, and conflict resolution

export interface ValidationRule {
  id: string;
  name: string;
  type: 'format' | 'uniqueness' | 'duplicate' | 'address' | 'custom';
  enabled: boolean;
  severity: 'warning' | 'error' | 'critical';
  description: string;
  configuration: Record<string, any>;
  congregationId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TerritoryNumberValidationConfig {
  pattern: RegExp;
  minLength: number;
  maxLength: number;
  allowedCharacters: string;
  caseSensitive: boolean;
  requirePrefix: boolean;
  prefixPattern?: string;
}

export interface AddressValidationConfig {
  standardizeCapitalization: boolean;
  removeExtraSpaces: boolean;
  standardizePunctuation: boolean;
  validateComponents: boolean;
  requiredComponents: string[];
  abbreviations: Record<string, string>;
  forbiddenCharacters: string[];
}

export interface DuplicateDetectionConfig {
  enableFuzzyMatching: boolean;
  similarityThreshold: number; // 0-1
  compareFields: ('territoryNumber' | 'address' | 'notes')[];
  ignoreCase: boolean;
  ignoreSpacing: boolean;
  ignorePunctuation: boolean;
}

export interface ValidationError {
  id: string;
  territoryId?: string;
  territoryNumber: string;
  address: string;
  field: string;
  errorType: 'format' | 'uniqueness' | 'duplicate' | 'address' | 'custom';
  severity: 'warning' | 'error' | 'critical';
  message: string;
  details: string;
  suggestedFix?: string;
  ruleId: string;
  rowNumber?: number; // For import validation
  canOverride: boolean;
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
  resolutionNotes?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  cleaned: {
    territoryNumber: string;
    address: string;
    notes?: string;
  };
  duplicates: DuplicateMatch[];
  summary: {
    totalChecked: number;
    errorsFound: number;
    warningsFound: number;
    duplicatesFound: number;
    cleanedFields: number;
  };
}

export interface DuplicateMatch {
  id: string;
  territory1: {
    id?: string;
    territoryNumber: string;
    address: string;
  };
  territory2: {
    id?: string;
    territoryNumber: string;
    address: string;
  };
  similarityScore: number; // 0-1
  matchedFields: string[];
  confidence: 'low' | 'medium' | 'high';
  suggestedAction: 'merge' | 'keep_both' | 'manual_review';
  isResolved: boolean;
  resolution?: 'merged' | 'kept_both' | 'deleted_duplicate';
  resolvedBy?: string;
  resolvedAt?: Date;
}

export interface ValidationConflict {
  id: string;
  type: 'duplicate' | 'format' | 'uniqueness';
  status: 'pending' | 'resolved' | 'overridden';
  territories: {
    id?: string;
    territoryNumber: string;
    address: string;
    notes?: string;
  }[];
  errors: ValidationError[];
  suggestedResolution: string;
  adminNotes?: string;
  resolvedBy?: string;
  resolvedAt?: Date;
  resolutionAction?: string;
}

export interface ValidationReport {
  id: string;
  congregationId: string;
  type: 'import' | 'cleanup' | 'audit';
  status: 'generating' | 'completed' | 'failed';
  generatedBy: string;
  generatedAt: Date;
  parameters: {
    dateRange?: { start: Date; end: Date };
    includeResolved?: boolean;
    severityFilter?: ('warning' | 'error' | 'critical')[];
    errorTypes?: string[];
  };
  summary: {
    totalTerritories: number;
    validTerritories: number;
    territoriesWithErrors: number;
    totalErrors: number;
    totalWarnings: number;
    duplicatesFound: number;
    conflictsResolved: number;
  };
  downloadUrl?: string;
  expiresAt: Date;
}

export interface ValidationRuleTemplate {
  id: string;
  name: string;
  description: string;
  category: 'basic' | 'advanced' | 'custom';
  rules: Omit<ValidationRule, 'id' | 'congregationId' | 'createdAt' | 'updatedAt'>[];
  isDefault: boolean;
}

export interface CleaningResult {
  original: string;
  cleaned: string;
  changes: {
    type: 'capitalization' | 'spacing' | 'punctuation' | 'abbreviation' | 'format';
    description: string;
    before: string;
    after: string;
  }[];
  confidence: number; // 0-1
}

export interface ValidationBatch {
  id: string;
  congregationId: string;
  type: 'import' | 'cleanup' | 'audit';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalTerritories: number;
  processedTerritories: number;
  errors: ValidationError[];
  conflicts: ValidationConflict[];
  startedAt: Date;
  completedAt?: Date;
  progress: number; // 0-100
}

// Default validation configurations
export const DEFAULT_TERRITORY_NUMBER_CONFIG: TerritoryNumberValidationConfig = {
  pattern: /^[A-Za-z0-9\-_]+$/,
  minLength: 1,
  maxLength: 20,
  allowedCharacters: 'A-Z, a-z, 0-9, -, _',
  caseSensitive: false,
  requirePrefix: false
};

export const DEFAULT_ADDRESS_CONFIG: AddressValidationConfig = {
  standardizeCapitalization: true,
  removeExtraSpaces: true,
  standardizePunctuation: true,
  validateComponents: false,
  requiredComponents: [],
  abbreviations: {
    'Street': 'St',
    'Avenue': 'Ave',
    'Boulevard': 'Blvd',
    'Road': 'Rd',
    'Drive': 'Dr',
    'Lane': 'Ln',
    'Court': 'Ct',
    'Place': 'Pl',
    'Apartment': 'Apt',
    'Suite': 'Ste'
  },
  forbiddenCharacters: ['<', '>', '{', '}', '[', ']']
};

export const DEFAULT_DUPLICATE_CONFIG: DuplicateDetectionConfig = {
  enableFuzzyMatching: true,
  similarityThreshold: 0.8,
  compareFields: ['territoryNumber', 'address'],
  ignoreCase: true,
  ignoreSpacing: true,
  ignorePunctuation: true
};

// Validation rule templates
export const VALIDATION_RULE_TEMPLATES: ValidationRuleTemplate[] = [
  {
    id: 'basic-congregation',
    name: 'Basic Congregation Rules',
    description: 'Standard validation rules for most congregations',
    category: 'basic',
    isDefault: true,
    rules: [
      {
        name: 'Territory Number Format',
        type: 'format',
        enabled: true,
        severity: 'error',
        description: 'Validates territory number format',
        configuration: DEFAULT_TERRITORY_NUMBER_CONFIG
      },
      {
        name: 'Territory Number Uniqueness',
        type: 'uniqueness',
        enabled: true,
        severity: 'error',
        description: 'Ensures territory numbers are unique within congregation',
        configuration: {}
      },
      {
        name: 'Address Standardization',
        type: 'address',
        enabled: true,
        severity: 'warning',
        description: 'Standardizes address formatting',
        configuration: DEFAULT_ADDRESS_CONFIG
      },
      {
        name: 'Duplicate Territory Detection',
        type: 'duplicate',
        enabled: true,
        severity: 'warning',
        description: 'Detects potential duplicate territories',
        configuration: DEFAULT_DUPLICATE_CONFIG
      }
    ]
  }
];

// Error message templates
export const VALIDATION_ERROR_MESSAGES = {
  TERRITORY_NUMBER_REQUIRED: 'Territory number is required',
  TERRITORY_NUMBER_INVALID_FORMAT: 'Territory number contains invalid characters',
  TERRITORY_NUMBER_TOO_SHORT: 'Territory number is too short (minimum {min} characters)',
  TERRITORY_NUMBER_TOO_LONG: 'Territory number is too long (maximum {max} characters)',
  TERRITORY_NUMBER_DUPLICATE: 'Territory number already exists in this congregation',
  ADDRESS_REQUIRED: 'Address is required',
  ADDRESS_INVALID_CHARACTERS: 'Address contains invalid characters',
  ADDRESS_TOO_LONG: 'Address exceeds maximum length',
  DUPLICATE_TERRITORY_FOUND: 'Potential duplicate territory found (similarity: {score}%)',
  CUSTOM_RULE_VIOLATION: 'Custom validation rule violation: {rule}'
};
