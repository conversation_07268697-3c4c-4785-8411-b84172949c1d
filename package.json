{"name": "hermanos-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "docs:migrate": "node scripts/migrate-enhanced-documents.js", "docs:test": "node scripts/test-enhanced-documents.js", "db:migrate-old": "node scripts/migrate-from-old-database.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^5.0.0", "@turf/turf": "^7.2.0", "@types/maplibre-gl": "^1.13.2", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "bcryptjs": "^2.4.3", "dotenv": "^17.2.0", "jsonwebtoken": "^9.0.0", "lucide-react": "^0.290.0", "maplibre-gl": "^5.6.1", "mysql2": "^3.14.2", "next": "^14.0.0", "node-fetch": "^3.3.2", "node-geocoder": "^4.4.1", "pg": "^8.16.3", "prisma": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.76", "zustand": "^4.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.4.0", "prettier": "^3.0.0", "puppeteer": "^24.15.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}}