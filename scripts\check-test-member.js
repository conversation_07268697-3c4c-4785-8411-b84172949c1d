/**
 * Check Test Member
 * 
 * Checks for test members and their credentials
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTestMember() {
  try {
    console.log('👤 Checking Test Members...\n');

    // Get members with admin roles
    const adminMembers = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        role: {
          in: ['developer', 'overseer', 'elder']
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        pin: true,
      },
    });

    if (adminMembers.length > 0) {
      console.log('✅ Admin members found:');
      adminMembers.forEach(member => {
        console.log(`   ID: ${member.id}`);
        console.log(`   Name: ${member.name}`);
        console.log(`   Email: ${member.email}`);
        console.log(`   Role: ${member.role}`);
        console.log(`   PIN: ${member.pin}`);
        console.log('   ---');
      });
    } else {
      console.log('❌ No admin members found');
    }

    // Check congregation settings for PIN
    const congregationSettings = await prisma.congregationSettings.findFirst({
      where: { congregationId: '1441' },
      select: {
        congregationPin: true,
      },
    });

    if (congregationSettings) {
      console.log('\n🔐 Congregation Settings:');
      console.log(`   Congregation PIN: ${congregationSettings.congregationPin}`);
    }

  } catch (error) {
    console.error('❌ Error checking test member:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkTestMember().catch(console.error);
