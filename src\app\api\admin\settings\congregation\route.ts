import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/admin/settings/congregation
 * Retrieve congregation settings
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to access congregation settings' },
        { status: 403 }
      );
    }

    // Get congregation settings from PostgreSQL
    const congregation = await prisma.congregation.findUnique({
      where: {
        id: user.congregationId,
      },
      include: {
        congregationSettings: true,
      },
    });

    if (!congregation) {
      return NextResponse.json(
        { error: 'Congregation not found' },
        { status: 404 }
      );
    }

    // Convert settings array to object for easier access
    const settingsMap = congregation.congregationSettings.reduce((acc, setting) => {
      acc[setting.settingKey] = setting.settingValue;
      return acc;
    }, {} as Record<string, string | null>);

    // Build response with congregation info and settings
    const response = {
      id: congregation.id,
      name: congregation.name,
      number: settingsMap.congregation_number || '',
      pin: settingsMap.congregation_pin || '',
      language: congregation.language || 'es', // Include language from congregation table
      circuitNumber: settingsMap.circuit_number || '',
      circuitOverseer: settingsMap.circuit_overseer || '',
      address: settingsMap.address || '',
      midweekDay: settingsMap.midweek_day || 'Thursday',
      midweekTime: settingsMap.midweek_time || '7:00 PM',
      weekendDay: settingsMap.weekend_day || 'Sunday',
      weekendTime: settingsMap.weekend_time || '10:00 AM',
      defaultCongregationId: settingsMap.default_congregation_id || '',
      defaultCongregationPin: settingsMap.default_congregation_pin || '',
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching congregation settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/settings/congregation
 * Update congregation settings
 */
export async function PUT(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update congregation settings' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { type, ...updateData } = body;

    switch (type) {
      case 'info':
        // Update congregation name and language
        await prisma.congregation.update({
          where: { id: user.congregationId },
          data: {
            name: updateData.name,
            language: updateData.language || 'es' // Update language field
          },
        });

        // Update settings in congregation_settings table
        const infoSettings = [
          { key: 'congregation_number', value: updateData.number },
          { key: 'circuit_number', value: updateData.circuitNumber },
          { key: 'circuit_overseer', value: updateData.circuitOverseer },
          { key: 'address', value: updateData.address },
          { key: 'congregation_pin', value: updateData.pin },
          { key: 'default_congregation_pin', value: updateData.pin }, // Also update default PIN
        ];

        for (const setting of infoSettings) {
          await prisma.congregationSetting.upsert({
            where: {
              congregationId_settingKey: {
                congregationId: user.congregationId,
                settingKey: setting.key,
              },
            },
            update: {
              settingValue: setting.value,
            },
            create: {
              congregationId: user.congregationId,
              settingKey: setting.key,
              settingValue: setting.value,
            },
          });
        }
        break;

      case 'schedule':
        const scheduleSettings = [
          { key: 'midweek_day', value: updateData.midweekDay },
          { key: 'midweek_time', value: updateData.midweekTime },
          { key: 'weekend_day', value: updateData.weekendDay },
          { key: 'weekend_time', value: updateData.weekendTime },
        ];

        for (const setting of scheduleSettings) {
          await prisma.congregationSetting.upsert({
            where: {
              congregationId_settingKey: {
                congregationId: user.congregationId,
                settingKey: setting.key,
              },
            },
            update: {
              settingValue: setting.value,
            },
            create: {
              congregationId: user.congregationId,
              settingKey: setting.key,
              settingValue: setting.value,
            },
          });
        }
        break;

      case 'auth':
        const authSettings = [
          { key: 'default_congregation_id', value: updateData.defaultCongregationId },
          { key: 'default_congregation_pin', value: updateData.defaultCongregationPin },
          { key: 'congregation_pin', value: updateData.defaultCongregationPin },
        ];

        for (const setting of authSettings) {
          await prisma.congregationSetting.upsert({
            where: {
              congregationId_settingKey: {
                congregationId: user.congregationId,
                settingKey: setting.key,
              },
            },
            update: {
              settingValue: setting.value,
            },
            create: {
              congregationId: user.congregationId,
              settingKey: setting.key,
              settingValue: setting.value,
            },
          });
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid update type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: 'Congregation settings updated successfully'
    });

  } catch (error) {
    console.error('Error updating congregation settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
