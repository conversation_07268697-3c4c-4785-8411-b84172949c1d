'use client';

import React, { useState, useEffect } from 'react';
import SimpleTerritoryMap from '@/components/territories/shared/SimpleTerritoryMap';
import type { Territory } from '@/types/territories/map';

export default function TestHeightIssuePage() {
  const [territory, setTerritory] = useState<Territory | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem('hermanos_token');
        if (!token) {
          throw new Error('No token found');
        }

        const response = await fetch('/api/territories/cmdjwgwzb0001jk0xm9rqeni1', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch: ${response.statusText}`);
        }

        const territoryData = await response.json();

        const mapTerritory: Territory = {
          id: territoryData.id,
          territoryNumber: territoryData.territoryNumber,
          address: territoryData.address,
          status: territoryData.status,
          coordinates: territoryData.coordinates,
          boundary: territoryData.boundary
        };

        setTerritory(mapTerritory);

      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <div className="text-sm text-gray-600">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-4 py-3">
          <h1 className="text-xl font-semibold text-gray-900">
            📏 Height Issue Test
          </h1>
          <p className="text-sm text-gray-500 mt-1">
            Testing different height configurations to isolate the rendering issue
          </p>
        </div>
      </div>

      {/* Test Grid */}
      <div className="p-4 space-y-6">
        
        {/* Test 1: height="100%" (Working in debug) */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200 bg-green-50">
            <h2 className="text-lg font-medium text-green-900">✅ Test 1: height="100%" (Debug Style)</h2>
            <p className="text-sm text-green-700">This works in the debug page</p>
          </div>
          <div className="h-80">
            {territory && (
              <SimpleTerritoryMap
                territories={[territory]}
                height="100%"
                className="w-full"
              />
            )}
          </div>
        </div>

        {/* Test 2: height="300px" (Not working in detail) */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200 bg-red-50">
            <h2 className="text-lg font-medium text-red-900">❌ Test 2: height="300px" (Detail Style)</h2>
            <p className="text-sm text-red-700">This doesn't work in territory detail</p>
          </div>
          <div className="bg-white border-b border-gray-200">
            {territory && (
              <SimpleTerritoryMap
                territories={[territory]}
                height="300px"
                className="w-full"
              />
            )}
          </div>
        </div>

        {/* Test 3: Exact TerritoryDetail structure */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200 bg-blue-50">
            <h2 className="text-lg font-medium text-blue-900">🔍 Test 3: Exact TerritoryDetail Structure</h2>
            <p className="text-sm text-blue-700">Replicating the exact structure from TerritoryDetail component</p>
          </div>
          {/* Exact structure from TerritoryDetail */}
          <div className="bg-white border-b border-gray-200">
            {territory && (
              <SimpleTerritoryMap
                territories={[territory]}
                height="300px"
                className="w-full"
              />
            )}
          </div>
        </div>

        {/* Test 4: Fixed height container */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200 bg-purple-50">
            <h2 className="text-lg font-medium text-purple-900">🧪 Test 4: Fixed Height Container</h2>
            <p className="text-sm text-purple-700">Using a fixed height container</p>
          </div>
          <div style={{ height: '300px' }}>
            {territory && (
              <SimpleTerritoryMap
                territories={[territory]}
                height="300px"
                className="w-full"
              />
            )}
          </div>
        </div>

      </div>

      {/* Debug Info */}
      <div className="p-4">
        <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
          <h3 className="font-medium text-gray-900 mb-2">🔧 Territory Data:</h3>
          <div className="text-sm text-gray-700">
            <div>Territory: {territory?.territoryNumber}</div>
            <div>Has boundary: {!!territory?.boundary ? 'Yes' : 'No'}</div>
            <div>Boundary type: {territory?.boundary?.type || 'N/A'}</div>
            <div>Coordinates: {territory?.coordinates ? `${territory.coordinates.latitude}, ${territory.coordinates.longitude}` : 'None'}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
