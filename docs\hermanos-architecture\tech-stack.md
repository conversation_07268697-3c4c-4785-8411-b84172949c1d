# Tech Stack

This is the **DEFINITIVE technology selection** for the entire Hermanos project. All development must use these exact versions and technologies.

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.3+ | Type-safe frontend development | Essential for large codebase maintainability and preventing runtime errors in multi-tenant environment |
| Frontend Framework | Next.js | 14.1+ | React framework with SSR/SSG | App Router provides excellent multi-tenant routing, built-in optimization, and seamless deployment |
| UI Component Library | Tailwind CSS + Headless UI | 3.4+ / 1.7+ | Utility-first styling + accessible components | Enables pixel-perfect UI preservation while maintaining accessibility and mobile responsiveness |
| State Management | Zustand + React Query | 4.4+ / 5.17+ | Client state + server state management | Lightweight state management with excellent server state caching for multi-congregation data |
| Backend Language | TypeScript | 5.3+ | Type-safe backend development | Shared types between frontend/backend, essential for API contract safety |
| Backend Framework | Next.js API Routes | 14.1+ | Serverless API endpoints | Integrated with frontend, excellent optimization, simplified deployment |
| API Style | REST + tRPC | tRPC 10.45+ | Type-safe API layer | End-to-end type safety while maintaining REST compatibility for external integrations |
| Database | PostgreSQL (Local) | 15+ | Multi-tenant relational database | Superior multi-tenancy support, JSON capabilities, excellent Prisma integration |
| Cache | Redis (Local) | 7.0+ | Session and data caching | Local Redis for JW.org data caching and session management |
| File Storage | Local File System | Latest | PDF and image storage | Local filesystem for complete control and existing infrastructure alignment |
| Authentication | Custom JWT | Latest | User authentication and authorization | Custom JWT implementation with role-based access control |
| Frontend Testing | Vitest + React Testing Library | 1.2+ / 14.0+ | Unit and component testing | Fast test runner with excellent React component testing capabilities |
| Backend Testing | Vitest + Supertest | 1.2+ / 6.3+ | API endpoint testing | Consistent testing framework across frontend/backend |
| E2E Testing | Playwright | 1.40+ | End-to-end testing | Excellent mobile testing capabilities, critical for responsive design validation |
| Build Tool | Next.js built-in | 14.1+ | Compilation and bundling | Optimized for Next.js applications with excellent performance |
| Bundler | Turbopack (Next.js) | Latest | Fast development builds | Next.js integrated bundler for optimal development experience |
| IaC Tool | Local Scripts | Latest | Infrastructure management | Local deployment scripts for self-hosted infrastructure |
| CI/CD | GitHub Actions | Latest | Automated testing and deployment | Automated testing with local deployment integration |
| Monitoring | Winston + Local Logs | Latest | Performance and error monitoring | Local logging with structured JSON format |
| Logging | Winston + Pino | Latest / 8.17+ | Application logging | Structured logging with local file storage |
| CSS Framework | Tailwind CSS | 3.4+ | Utility-first styling | Essential for pixel-perfect UI preservation and rapid development |

## Additional Key Dependencies

- **Prisma ORM**: 5.8+ - Type-safe database operations with excellent PostgreSQL support
- **React Hook Form**: 7.48+ - Performant form handling with Zod validation
- **Zod**: 3.22+ - Runtime type validation for API endpoints and forms
- **bcryptjs**: 2.4+ - Password hashing for PIN authentication
- **jose**: 5.2+ - JWT token handling for authentication
- **date-fns**: 3.2+ - Date manipulation for meeting and service management
