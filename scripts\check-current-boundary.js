const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkCurrentBoundary() {
  try {
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        boundaries: true
      }
    });

    if (territory && territory.boundaries) {
      console.log('Current boundary in database:');
      console.log(JSON.stringify(territory.boundaries, null, 2));
    } else {
      console.log('No boundary data found for Territory 001');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentBoundary();
