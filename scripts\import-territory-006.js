/**
 * Import Territory 006 Script
 * 
 * Based on the working import-territory-001.js script
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const TERRITORY_NUMBER = '006';
const CONGREGATION_ID = '1441'; // Coral Oeste
const ZIP_CODE = 'Miami, FL 33126';
const DISPLAY_ORDER = 6; // Territory 006 should be 6th in order

/**
 * Parse addresses from Excel data while preserving order
 */
function parseAddresses(excelData) {
  const addresses = [];
  let currentStreet = '';
  
  for (let i = 0; i < excelData.length; i++) {
    const row = excelData[i];
    
    if (!row || row.length === 0) continue;
    
    // Check if this row contains a street name (usually in column A, starts with text)
    const firstCell = row[0];
    if (firstCell && typeof firstCell === 'string' && firstCell.trim()) {
      const cellValue = firstCell.toString().trim();
      
      // If it doesn't start with a number, it's likely a street name
      if (!/^\d/.test(cellValue)) {
        currentStreet = cellValue;
        console.log(`📍 Found street: ${currentStreet}`);
        continue;
      }
    }
    
    // Process house numbers (usually in columns A, B, C, D, E, F)
    for (let colIndex = 0; colIndex < Math.min(row.length, 6); colIndex++) {
      const cellValue = row[colIndex];
      
      if (cellValue && /^\d/.test(cellValue.toString().trim())) {
        const houseNumber = cellValue.toString().trim();
        const fullAddress = `${houseNumber} ${currentStreet}, ${ZIP_CODE}`;
        
        // Get notes from column G (index 6) if available
        const notes = row[6] && typeof row[6] === 'string' ? row[6].trim() : null;
        
        addresses.push({
          address: fullAddress,
          notes: notes && notes !== 'null' ? notes : null,
          street: currentStreet,
          houseNumber: houseNumber
        });
        
        console.log(`🏠 Added address: ${fullAddress}${notes ? ` (${notes})` : ''}`);
      }
    }
  }
  
  return addresses;
}

async function importTerritory006() {
  try {
    console.log(`📂 Importing Territory ${TERRITORY_NUMBER}...`);

    // Read Excel file
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${TERRITORY_NUMBER}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    // Try different possible sheet names
    const possibleSheetNames = [
      `Terr ${TERRITORY_NUMBER}`,
      `Terr. ${TERRITORY_NUMBER}`,
      `Terr  ${TERRITORY_NUMBER}`,
      `Territory ${TERRITORY_NUMBER}`,
      `T${TERRITORY_NUMBER}`,
      workbook.SheetNames[0] // Fallback to first sheet
    ];
    
    let worksheet = null;
    let sheetName = '';
    
    for (const name of possibleSheetNames) {
      if (workbook.Sheets[name]) {
        worksheet = workbook.Sheets[name];
        sheetName = name;
        break;
      }
    }
    
    if (!worksheet) {
      console.error(`❌ No valid sheet found in Territory ${TERRITORY_NUMBER}`);
      console.log(`Available sheets: ${workbook.SheetNames.join(', ')}`);
      process.exit(1);
    }
    
    console.log(`📊 Using sheet: ${sheetName}`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse addresses
    const addresses = parseAddresses(excelData);
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    if (addresses.length === 0) {
      console.log(`⚠️  No addresses found to import for Territory ${TERRITORY_NUMBER}`);
      return;
    }
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: CONGREGATION_ID }
    });

    if (!congregation) {
      console.error(`❌ Congregation ${CONGREGATION_ID} (Coral Oeste) not found`);
      process.exit(1);
    }

    console.log(`✅ Found congregation: ${congregation.name}`);
    
    // Clear existing territory if it exists
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: congregation.id,
        territory: {
          territoryNumber: TERRITORY_NUMBER
        }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: congregation.id,
        territoryNumber: TERRITORY_NUMBER
      }
    });
    
    console.log(`🗑️  Cleared existing Territory ${TERRITORY_NUMBER}`);
    
    // Create the territory with all addresses combined
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: congregation.id,
        territoryNumber: TERRITORY_NUMBER,
        address: allAddresses,
        notes: allNotes || null,
        status: 'available',
        displayOrder: DISPLAY_ORDER
      }
    });
    
    console.log(`✅ Created Territory ${TERRITORY_NUMBER} with ${addresses.length} addresses`);
    console.log(`📍 Display Order: ${DISPLAY_ORDER}`);
    console.log(`🆔 Territory ID: ${territory.id}`);
    
    // Display summary
    console.log('\n📋 Import Summary:');
    console.log(`   Territory: ${TERRITORY_NUMBER}`);
    console.log(`   Addresses: ${addresses.length}`);
    console.log(`   Notes: ${addresses.filter(addr => addr.notes).length}`);
    console.log(`   Display Order: ${DISPLAY_ORDER}`);

  } catch (error) {
    console.error(`❌ Error importing Territory ${TERRITORY_NUMBER}:`, error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  importTerritory006();
}

module.exports = { importTerritory006 };
