import { NextRequest, NextResponse } from 'next/server';
import { EnhancedDocumentService } from '@/lib/services/enhancedDocumentService';
import { DocumentFilters } from '@/lib/types/document';
import { extractAndVerifyToken } from '@/lib/middleware/auth';

/**
 * GET /api/documents
 * Get documents with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    const { searchParams } = new URL(request.url);

    // Parse filters from query parameters
    const filters: DocumentFilters = {};

    if (searchParams.get('category')) {
      filters.category = searchParams.get('category')!;
    }

    if (searchParams.get('subcategory')) {
      filters.subcategory = searchParams.get('subcategory')!;
    }

    if (searchParams.get('visibility')) {
      filters.visibility = searchParams.get('visibility') as any;
    }

    if (searchParams.get('priority')) {
      filters.priority = searchParams.get('priority') as any;
    }

    if (searchParams.get('status')) {
      filters.status = searchParams.get('status') as any;
    }

    if (searchParams.get('folderId')) {
      filters.folderId = parseInt(searchParams.get('folderId')!);
    }

    if (searchParams.get('uploadedById')) {
      filters.uploadedById = parseInt(searchParams.get('uploadedById')!);
    }

    if (searchParams.get('startDate')) {
      filters.startDate = new Date(searchParams.get('startDate')!);
    }

    if (searchParams.get('endDate')) {
      filters.endDate = new Date(searchParams.get('endDate')!);
    }

    if (searchParams.get('isExpired')) {
      filters.isExpired = searchParams.get('isExpired') === 'true';
    }

    if (searchParams.get('search')) {
      filters.search = searchParams.get('search')!;
    }

    if (searchParams.get('tags')) {
      filters.tags = searchParams.get('tags')!.split(',');
    }

    // Parse pagination parameters
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get documents
    const documents = await EnhancedDocumentService.getDocuments(
      user.congregationId,
      filters,
      limit,
      offset,
      user.role
    );

    return NextResponse.json({ documents });
  } catch (error) {
    console.error('Error fetching documents:', error);
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/documents
 * Create a new document
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Check if user has permission to create documents
    if (!['elder', 'ministerial_servant'].includes(user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const documentData = await request.json();

    // Add congregation ID and uploader ID
    documentData.congregationId = user.congregationId;
    documentData.uploadedById = user.userId;

    // Create document
    const document = await EnhancedDocumentService.createDocument(
      user.congregationId,
      documentData,
      user.userId
    );

    return NextResponse.json({ document }, { status: 201 });
  } catch (error) {
    console.error('Error creating document:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create document' },
      { status: 500 }
    );
  }
}
