#!/usr/bin/env node

/**
 * Migrate Territory Status Schema
 * 
 * This script migrates the territory status from 'out_of_service' to 'unavailable'
 * and adds support for partial completion tracking with visit logging.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Migrate territory status values
 */
async function migrateTerritoryStatus() {
  try {
    console.log('🔄 Migrating Territory Status Values');
    console.log('====================================\n');

    // Update any territories with 'out_of_service' status to 'unavailable'
    const updateResult = await prisma.territory.updateMany({
      where: {
        status: 'out_of_service'
      },
      data: {
        status: 'unavailable'
      }
    });

    console.log(`✅ Updated ${updateResult.count} territories from 'out_of_service' to 'unavailable'`);

    // Check current status distribution
    const statusCounts = await prisma.territory.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    });

    console.log('\n📊 Current Territory Status Distribution:');
    statusCounts.forEach(status => {
      console.log(`   ${status.status}: ${status._count.id} territories`);
    });

    return updateResult.count;

  } catch (error) {
    console.error('❌ Error migrating territory status:', error);
    throw error;
  }
}

/**
 * Initialize partial completion fields for existing assignments
 */
async function initializePartialCompletionFields() {
  try {
    console.log('\n🔄 Initializing Partial Completion Fields');
    console.log('==========================================\n');

    // Update existing assignments to have default values for new fields
    const updateResult = await prisma.territoryAssignment.updateMany({
      where: {
        visitCount: {
          equals: null
        }
      },
      data: {
        visitCount: 0,
        isPartiallyCompleted: false
      }
    });

    console.log(`✅ Initialized partial completion fields for ${updateResult.count} assignments`);

    // Check assignment status distribution
    const assignmentStats = await prisma.territoryAssignment.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    });

    console.log('\n📊 Current Assignment Status Distribution:');
    assignmentStats.forEach(stat => {
      console.log(`   ${stat.status}: ${stat._count.id} assignments`);
    });

    return updateResult.count;

  } catch (error) {
    console.error('❌ Error initializing partial completion fields:', error);
    throw error;
  }
}

/**
 * Verify schema migration
 */
async function verifyMigration() {
  try {
    console.log('\n🔍 Verifying Schema Migration');
    console.log('==============================\n');

    // Test that we can create a territory with new status
    const testTerritory = await prisma.territory.create({
      data: {
        territoryNumber: 'TEST-001',
        address: 'Test Address for Migration',
        status: 'unavailable',
        congregationId: '1441'
      }
    });

    console.log(`✅ Successfully created test territory with 'unavailable' status`);

    // Test that we can create an assignment with new fields
    const testMember = await prisma.member.findFirst({
      where: { congregationId: '1441' }
    });

    if (testMember) {
      const testAssignment = await prisma.territoryAssignment.create({
        data: {
          territoryId: testTerritory.id,
          memberId: testMember.id,
          assignedBy: testMember.id,
          status: 'active',
          visitCount: 1,
          isPartiallyCompleted: true,
          partialCompletionNotes: 'Test partial completion',
          congregationId: '1441'
        }
      });

      console.log(`✅ Successfully created test assignment with partial completion fields`);

      // Test that we can create a visit
      const testVisit = await prisma.territoryVisit.create({
        data: {
          assignmentId: testAssignment.id,
          visitDate: new Date(),
          isCompleted: false,
          notes: 'Test visit for migration verification',
          addressesWorked: 'Test addresses 1-10',
          congregationId: '1441'
        }
      });

      console.log(`✅ Successfully created test visit record`);

      // Clean up test data
      await prisma.territoryVisit.delete({ where: { id: testVisit.id } });
      await prisma.territoryAssignment.delete({ where: { id: testAssignment.id } });
      await prisma.territory.delete({ where: { id: testTerritory.id } });

      console.log(`✅ Cleaned up test data`);
    }

    console.log('\n🎉 Schema migration verification completed successfully!');

  } catch (error) {
    console.error('❌ Error verifying migration:', error);
    throw error;
  }
}

/**
 * Main migration function
 */
async function main() {
  console.log('🗄️  Territory Status Schema Migration');
  console.log('=====================================\n');

  try {
    // Step 1: Migrate territory status values
    const statusUpdates = await migrateTerritoryStatus();

    // Step 2: Initialize partial completion fields
    const fieldUpdates = await initializePartialCompletionFields();

    // Step 3: Verify migration
    await verifyMigration();

    console.log('\n📊 Migration Summary:');
    console.log('=====================');
    console.log(`✅ Territory status updates: ${statusUpdates}`);
    console.log(`✅ Assignment field updates: ${fieldUpdates}`);
    console.log(`✅ Schema verification: Passed`);
    console.log('\n🎯 Migration completed successfully!');
    console.log('   - Territory status changed from "out_of_service" to "unavailable"');
    console.log('   - Added partial completion tracking fields');
    console.log('   - Added TerritoryVisit model for visit tracking');
    console.log('   - All existing data preserved and updated');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  migrateTerritoryStatus,
  initializePartialCompletionFields,
  verifyMigration
};
