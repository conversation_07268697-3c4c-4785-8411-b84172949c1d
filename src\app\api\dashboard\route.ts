/**
 * Dashboard API Endpoint
 *
 * Provides dashboard data for authenticated users including pending tasks,
 * recent service reports, and upcoming meetings with congregation isolation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user still exists and is active
    const member = await prisma.member.findUnique({
      where: {
        id: user.userId,
        isActive: true,
      },
      include: {
        congregation: {
          select: {
            id: true,
            name: true,
            language: true,
            timezone: true,
            isActive: true,
          },
        },
      },
    });

    if (!member || !member.congregation?.isActive) {
      return NextResponse.json(
        { error: 'User or congregation no longer active' },
        { status: 401 }
      );
    }

    // Get dashboard data with congregation isolation
    const [
      pendingTasks,
      recentServiceReport,
      upcomingMeetings,
      totalMembers,
      totalTasks,
    ] = await Promise.all([
      // Get pending tasks for the user
      prisma.taskAssignment.count({
        where: {
          congregationId: member.congregationId,
          assignedMemberId: member.id,
          status: 'pending',
        },
      }),

      // Get most recent service report
      prisma.fieldServiceRecord.findFirst({
        where: {
          congregationId: member.congregationId,
          memberId: member.id,
        },
        orderBy: {
          serviceMonth: 'desc',
        },
      }),

      // Get upcoming meetings (next 7 days)
      prisma.midweekMeeting.count({
        where: {
          congregationId: member.congregationId,
          meetingDate: {
            gte: new Date(),
            lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          },
          isActive: true,
        },
      }),

      // Get total active members (for admin users)
      member.role !== 'publisher' ? prisma.member.count({
        where: {
          congregationId: member.congregationId,
          isActive: true,
        },
      }) : 0,

      // Get total active tasks (for admin users)
      member.role !== 'publisher' ? prisma.task.count({
        where: {
          congregationId: member.congregationId,
          isActive: true,
        },
      }) : 0,
    ]);

    // Calculate service hours for current month
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const currentMonthService = await prisma.fieldServiceRecord.findFirst({
      where: {
        congregationId: member.congregationId,
        memberId: member.id,
        serviceMonth: {
          gte: currentMonth,
        },
      },
    });

    // Return dashboard data
    return NextResponse.json({
      success: true,
      user: {
        id: member.id,
        name: member.name,
        role: member.role,
        congregationId: member.congregationId,
        congregationName: member.congregation.name,
        hasCongregationPinAccess: user.hasCongregationPinAccess || false,
      },
      congregation: {
        id: member.congregation.id,
        name: member.congregation.name,
        language: member.congregation.language,
        timezone: member.congregation.timezone,
      },
      permissions: {
        canAccessAdmin: user.hasCongregationPinAccess || ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role),
        canManageSettings: user.hasCongregationPinAccess || ['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(member.role),
        hasCongregationPinAccess: user.hasCongregationPinAccess || false,
      },
      dashboardData: {
        pendingTasks,
        upcomingMeetings,
        currentMonthHours: currentMonthService?.hours || 0,
        lastServiceMonth: recentServiceReport?.serviceMonth || null,
        totalMembers: member.role !== 'publisher' ? totalMembers : null,
        totalTasks: member.role !== 'publisher' ? totalTasks : null,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Dashboard API error:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch dashboard data',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
