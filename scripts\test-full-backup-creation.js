/**
 * Test Full Backup Creation
 * 
 * Tests the full backup creation to ensure all records are included
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs').promises;
const path = require('path');

const prisma = new PrismaClient();

async function testFullBackupCreation() {
  try {
    console.log('🗄️ Testing Full Database Backup Creation...\n');

    // Test 1: Count all records in each table
    console.log('1. Counting records in each table...');
    const tables = [
      { name: 'congregations', model: 'congregation' },
      { name: 'members', model: 'member' },
      { name: 'roles', model: 'role' },
      { name: 'elder_permissions', model: 'elderPermission' },
      { name: 'songs', model: 'song' },
      { name: 'letters', model: 'letter' },
      { name: 'tasks', model: 'task' },
      { name: 'task_assignments', model: 'taskAssignment' },
      { name: 'field_service_records', model: 'fieldServiceRecord' },
      { name: 'congregation_settings', model: 'congregationSetting' }
    ];

    let expectedTotalRecords = 0;
    const tableCounts = {};

    for (const table of tables) {
      try {
        let count = 0;
        
        switch (table.model) {
          case 'congregation':
            count = await prisma.congregation.count();
            break;
          case 'member':
            count = await prisma.member.count();
            break;
          case 'role':
            count = await prisma.role.count();
            break;
          case 'elderPermission':
            count = await prisma.elderPermission.count();
            break;
          case 'song':
            count = await prisma.song.count();
            break;
          case 'letter':
            count = await prisma.letter.count();
            break;
          case 'task':
            count = await prisma.task.count();
            break;
          case 'taskAssignment':
            count = await prisma.taskAssignment.count();
            break;
          case 'fieldServiceRecord':
            count = await prisma.fieldServiceRecord.count();
            break;
          case 'congregationSetting':
            count = await prisma.congregationSetting.count();
            break;
        }

        tableCounts[table.name] = count;
        expectedTotalRecords += count;
        console.log(`  ${table.name}: ${count} records`);

      } catch (error) {
        console.log(`  ${table.name}: Error - ${error.message}`);
        tableCounts[table.name] = 0;
      }
    }

    console.log(`\n📊 Expected total records: ${expectedTotalRecords}`);

    // Test 2: Create full backup and count actual records
    console.log('\n2. Creating full backup...');
    
    const timestamp = new Date().toISOString();
    let sqlContent = `-- Database Backup Created: ${timestamp}\n`;
    sqlContent += `-- Coral Oeste Congregation Database\n\n`;

    let actualTotalRecords = 0;

    for (const table of tables) {
      try {
        console.log(`  Processing table: ${table.name}...`);
        
        let tableData = [];
        
        switch (table.model) {
          case 'congregation':
            tableData = await prisma.congregation.findMany();
            break;
          case 'member':
            tableData = await prisma.member.findMany();
            break;
          case 'role':
            tableData = await prisma.role.findMany();
            break;
          case 'elderPermission':
            tableData = await prisma.elderPermission.findMany();
            break;
          case 'song':
            tableData = await prisma.song.findMany();
            break;
          case 'letter':
            tableData = await prisma.letter.findMany();
            break;
          case 'task':
            tableData = await prisma.task.findMany();
            break;
          case 'taskAssignment':
            tableData = await prisma.taskAssignment.findMany();
            break;
          case 'fieldServiceRecord':
            tableData = await prisma.fieldServiceRecord.findMany();
            break;
          case 'congregationSetting':
            tableData = await prisma.congregationSetting.findMany();
            break;
        }

        if (Array.isArray(tableData) && tableData.length > 0) {
          const columns = Object.keys(tableData[0]);
          const columnList = columns.map(col => `"${col}"`).join(', ');
          
          sqlContent += `-- Table: ${table.name} (${tableData.length} records)\n`;
          sqlContent += `INSERT INTO "${table.name}" (${columnList}) VALUES\n`;
          
          const values = tableData.map(row => {
            const rowValues = columns.map(col => {
              const value = row[col];
              if (value === null || value === undefined) return 'NULL';
              if (typeof value === 'string') {
                const escaped = value.replace(/'/g, "''").replace(/\\/g, '\\\\');
                return `'${escaped}'`;
              }
              if (value instanceof Date) return `'${value.toISOString()}'`;
              if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
              if (typeof value === 'object') {
                return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
              }
              return String(value);
            });
            return `(${rowValues.join(', ')})`;
          });
          
          sqlContent += values.join(',\n') + ';\n\n';
          actualTotalRecords += tableData.length;
          console.log(`    ✅ Processed ${tableData.length} records`);
        } else {
          sqlContent += `-- No data in table ${table.name}\n\n`;
          console.log(`    ⚠️ No data found`);
        }

      } catch (error) {
        console.log(`    ❌ Error: ${error.message}`);
        sqlContent += `-- Error processing table ${table.name}: ${error.message}\n\n`;
      }
    }

    sqlContent += `-- Backup completed: ${new Date().toISOString()}\n`;
    sqlContent += `-- Total records processed: ${actualTotalRecords}\n`;

    // Test 3: Write backup file and check size
    console.log('\n3. Writing backup file...');
    const backupDir = path.join(process.cwd(), 'backups');
    await fs.mkdir(backupDir, { recursive: true });
    
    const testFilename = `full_backup_test_${Date.now()}.sql`;
    const testFilePath = path.join(backupDir, testFilename);
    
    await fs.writeFile(testFilePath, sqlContent, 'utf8');
    const stats = await fs.stat(testFilePath);
    
    console.log(`✅ Full backup file created: ${testFilename}`);
    console.log(`✅ File size: ${formatFileSize(stats.size)}`);
    console.log(`✅ Expected records: ${expectedTotalRecords}`);
    console.log(`✅ Actual records: ${actualTotalRecords}`);

    // Test 4: Analyze backup content
    console.log('\n4. Analyzing backup content...');
    const lines = sqlContent.split('\n');
    const insertLines = lines.filter(line => line.startsWith('INSERT INTO'));
    console.log(`✅ INSERT statements: ${insertLines.length}`);
    
    const recordLines = lines.filter(line => line.includes('records)')).length;
    console.log(`✅ Table headers: ${recordLines}`);

    // Show sample content
    console.log('\n5. Sample backup content:');
    console.log(sqlContent.split('\n').slice(0, 15).join('\n') + '...');

    // Clean up
    await fs.unlink(testFilePath);
    console.log('\n🧹 Test file cleaned up');

    // Summary
    console.log('\n📋 BACKUP ANALYSIS SUMMARY:');
    console.log(`Expected total records: ${expectedTotalRecords}`);
    console.log(`Actual records in backup: ${actualTotalRecords}`);
    console.log(`File size: ${formatFileSize(stats.size)}`);
    console.log(`Records match: ${expectedTotalRecords === actualTotalRecords ? '✅ YES' : '❌ NO'}`);
    
    if (stats.size < 100000) { // Less than 100KB
      console.log('⚠️ WARNING: Backup file seems small for a full database backup');
    } else {
      console.log('✅ Backup file size looks appropriate for full database');
    }

  } catch (error) {
    console.error('❌ Full backup test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Helper function to format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run the test
testFullBackupCreation().catch(console.error);
