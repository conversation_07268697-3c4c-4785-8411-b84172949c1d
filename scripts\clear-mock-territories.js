/**
 * Clear Mock Territories Script
 * 
 * Removes the mock/sample territories that were created for testing
 * so we can import the real territory data from Excel files.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearMockTerritories() {
  try {
    console.log('🧹 Clearing mock territories...');

    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (!congregation) {
      console.error('❌ Congregation 1441 (Coral Oeste) not found');
      process.exit(1);
    }

    console.log(`✅ Found congregation: ${congregation.name}`);

    // Get current territories count
    const currentCount = await prisma.territory.count({
      where: { congregationId: congregation.id }
    });

    console.log(`📊 Current territories count: ${currentCount}`);

    if (currentCount === 0) {
      console.log('✅ No territories to clear');
      return;
    }

    // Clear territory assignments first (due to foreign key constraints)
    const assignmentsDeleted = await prisma.territoryAssignment.deleteMany({
      where: { congregationId: congregation.id }
    });

    console.log(`🗑️  Deleted ${assignmentsDeleted.count} territory assignments`);

    // Clear territories
    const territoriesDeleted = await prisma.territory.deleteMany({
      where: { congregationId: congregation.id }
    });

    console.log(`🗑️  Deleted ${territoriesDeleted.count} territories`);

    console.log('✅ Mock territories cleared successfully!');

  } catch (error) {
    console.error('❌ Error clearing mock territories:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  clearMockTerritories();
}

module.exports = { clearMockTerritories };
