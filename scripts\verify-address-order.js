/**
 * Verify Address Order Script
 * 
 * Compares the order of addresses in the database vs Excel sheet
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

async function verifyAddressOrder(territoryNumber) {
  try {
    console.log(`\n🔍 Verifying address order for Territory ${territoryNumber}...`);
    
    // Get territory from database
    const territory = await prisma.territory.findFirst({
      where: {
        territoryNumber: territoryNumber,
        congregationId: '1441'
      }
    });
    
    if (!territory) {
      console.error(`❌ Territory ${territoryNumber} not found in database`);
      return;
    }
    
    // Get addresses from database (split by newline)
    const dbAddresses = territory.address.split('\n').filter(addr => addr.trim());
    console.log(`📊 Database has ${dbAddresses.length} addresses`);
    
    // Read Excel file and parse addresses in order
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${territoryNumber}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    let sheetName = '';
    if (territoryNumber === '002') sheetName = 'Terr  2';
    else if (territoryNumber === '003') sheetName = 'Terr 3';
    else sheetName = `Terr ${territoryNumber}`;
    
    const worksheet = workbook.Sheets[sheetName];
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    // Parse Excel addresses in order (using same logic as import script)
    const excelAddresses = [];
    let currentStreet = '';
    
    for (let i = 8; i < excelData.length; i++) { // Skip headers
      const row = excelData[i];
      if (!row || row.length === 0) continue;
      
      const cellB = row[1];
      if (!cellB) continue;
      
      const cellValue = cellB.toString().trim();
      if (!cellValue) continue;
      
      // Skip Excel date serials
      if (typeof cellB === 'number' && cellB > 40000) continue;
      
      // Check if street name
      const isStreetName = /^[A-Z]/.test(cellValue) && 
                          (/AVE|ST|RD|CT|WAY|BLVD|PL|LN|DR|CIR|CANAL/i.test(cellValue) ||
                           cellValue.includes('NW ') || cellValue.includes('SW '));
      
      if (isStreetName) {
        currentStreet = cellValue;
        continue;
      }
      
      // Check if house number
      if (/^\d/.test(cellValue) && currentStreet) {
        const houseNumber = cellValue;
        const numericValue = parseInt(houseNumber);
        if (numericValue > 10000 && !currentStreet.includes('CANAL')) continue;
        
        const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL 33126`;
        excelAddresses.push(fullAddress);
      }
    }
    
    console.log(`📊 Excel has ${excelAddresses.length} addresses`);
    
    // Compare orders
    console.log(`\n📋 Address Order Comparison (First 10):`);
    console.log(`${'#'.padStart(3)} | ${'Excel Order'.padEnd(40)} | ${'Database Order'.padEnd(40)} | Match`);
    console.log(`${'-'.repeat(3)} | ${'-'.repeat(40)} | ${'-'.repeat(40)} | -----`);
    
    let matches = 0;
    const maxCompare = Math.min(10, Math.max(excelAddresses.length, dbAddresses.length));
    
    for (let i = 0; i < maxCompare; i++) {
      const excelAddr = excelAddresses[i] || 'N/A';
      const dbAddr = dbAddresses[i] || 'N/A';
      const isMatch = excelAddr === dbAddr;
      if (isMatch) matches++;
      
      console.log(`${(i + 1).toString().padStart(3)} | ${excelAddr.substring(0, 40).padEnd(40)} | ${dbAddr.substring(0, 40).padEnd(40)} | ${isMatch ? '✅' : '❌'}`);
    }
    
    console.log(`\n📊 Summary:`);
    console.log(`   Excel addresses: ${excelAddresses.length}`);
    console.log(`   Database addresses: ${dbAddresses.length}`);
    console.log(`   First 10 matches: ${matches}/${maxCompare}`);
    console.log(`   Order preserved: ${matches === maxCompare ? '✅ YES' : '❌ NO'}`);
    
    if (matches !== maxCompare) {
      console.log(`\n🔍 Full Excel Order (First 20):`);
      excelAddresses.slice(0, 20).forEach((addr, i) => {
        console.log(`   ${(i + 1).toString().padStart(2)}. ${addr}`);
      });
      
      console.log(`\n🔍 Full Database Order (First 20):`);
      dbAddresses.slice(0, 20).forEach((addr, i) => {
        console.log(`   ${(i + 1).toString().padStart(2)}. ${addr}`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Error verifying Territory ${territoryNumber}:`, error.message);
  }
}

async function verifyMultipleTerritories() {
  console.log('🔍 Starting address order verification...');
  
  await verifyAddressOrder('002');
  await verifyAddressOrder('003');
  
  console.log('\n✅ Verification completed!');
  await prisma.$disconnect();
}

verifyMultipleTerritories();
