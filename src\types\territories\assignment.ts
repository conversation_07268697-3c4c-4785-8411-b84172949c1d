// Territory Assignment Types
// Specialized types for territory assignment operations

import { AssignmentStatus, TerritoryAssignment, Territory } from './territory';

export interface AssignmentHistory {
  id: string;
  territoryId: string;
  memberId: string;
  assignedBy: string;
  assignedAt: Date;
  completedAt?: Date;
  duration?: number; // in days
  status: AssignmentStatus;
  notes?: string;
}

export interface AssignmentSummary {
  totalAssignments: number;
  activeAssignments: number;
  completedAssignments: number;
  overdueAssignments: number;
  averageDuration: number;
}

export interface MemberAssignmentStats {
  memberId: string;
  memberName: string;
  totalAssignments: number;
  activeAssignments: number;
  completedAssignments: number;
  averageCompletionTime: number;
  currentTerritories: Territory[];
}

export interface TerritoryAssignmentStats {
  territoryId: string;
  territoryNumber: string;
  totalAssignments: number;
  averageCompletionTime: number;
  lastAssignedDate?: Date;
  lastCompletedDate?: Date;
  currentAssignment?: TerritoryAssignment;
}

export interface AssignmentReport {
  congregationId: string;
  reportDate: Date;
  summary: AssignmentSummary;
  memberStats: MemberAssignmentStats[];
  territoryStats: TerritoryAssignmentStats[];
  overdueAssignments: TerritoryAssignment[];
  availableTerritories: Territory[];
}

// Assignment workflow types
export interface AssignmentWorkflow {
  territoryId: string;
  fromMemberId?: string;
  toMemberId: string;
  assignedBy: string;
  reason?: string;
  notes?: string;
}

export interface BulkAssignmentRequest {
  assignments: AssignmentWorkflow[];
  congregationId: string;
  assignedBy: string;
}

export interface BulkAssignmentResult {
  successful: TerritoryAssignment[];
  failed: {
    assignment: AssignmentWorkflow;
    error: string;
  }[];
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// Assignment validation types
export interface AssignmentValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface AssignmentConflict {
  territoryId: string;
  conflictType: 'already_assigned' | 'member_overloaded' | 'territory_unavailable';
  details: string;
  currentAssignment?: TerritoryAssignment;
}

// Assignment notification types
export interface AssignmentNotification {
  type: 'assignment_created' | 'assignment_completed' | 'assignment_overdue' | 'assignment_reminder';
  assignmentId: string;
  territoryId: string;
  memberId: string;
  message: string;
  metadata?: Record<string, unknown>;
}

// Assignment interface specific types
export interface AssignmentRequest {
  territoryId: string;
  memberId: string;
  notes?: string;
}

export interface AssignmentResponse {
  success: boolean;
  assignment?: TerritoryAssignment;
  error?: string;
  message?: string;
}

export interface MemberWithAssignments {
  id: string;
  name: string;
  role: string;
  email?: string;
  phone?: string;
  activeAssignments: number;
  totalAssignments: number;
  lastAssignmentDate?: Date;
  isAvailable: boolean;
  workloadStatus: 'light' | 'normal' | 'heavy';
  assignments?: TerritoryAssignment[];
}

export interface TerritoryWithAssignment {
  id: string;
  territoryNumber: string;
  address: string;
  status: 'available' | 'assigned' | 'completed' | 'out_of_service';
  notes?: string;
  congregationId: string;
  createdAt: Date;
  updatedAt: Date;

  // Current assignment (if any)
  currentAssignment?: TerritoryAssignment;

  // Assignment history
  assignmentHistory?: TerritoryAssignment[];

  // Assignment statistics
  totalAssignments: number;
  averageAssignmentDuration?: number; // in days
  lastAssignedDate?: Date;
  lastReturnedDate?: Date;
}

// Assignment status helpers
export const ASSIGNMENT_STATUS_LABELS = {
  active: 'Activo',
  completed: 'Completado',
  overdue: 'Vencido'
} as const;

export const WORKLOAD_STATUS_LABELS = {
  light: 'Ligera',
  normal: 'Normal',
  heavy: 'Pesada'
} as const;

export const WORKLOAD_STATUS_COLORS = {
  light: 'text-green-600 bg-green-50',
  normal: 'text-blue-600 bg-blue-50',
  heavy: 'text-red-600 bg-red-50'
} as const;

// Assignment validation rules
export const ASSIGNMENT_VALIDATION_RULES = {
  MAX_ASSIGNMENTS_PER_MEMBER: 3,
  MIN_DAYS_BETWEEN_ASSIGNMENTS: 1,
  ELIGIBLE_ROLES: ['elder', 'ministerial_servant', 'publisher'],
  RESTRICTED_ROLES: ['inactive', 'disfellowshipped']
} as const;
