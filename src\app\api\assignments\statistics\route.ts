/**
 * Assignment Statistics API Endpoint
 * 
 * Provides congregation-wide assignment statistics, member summaries,
 * and coordination analytics for assignment management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { AssignmentCoordinationService } from '@/lib/services/assignmentCoordinationService';

// Validation schema for statistics requests
const StatisticsRequestSchema = z.object({
  type: z.enum(['overview', 'member', 'unassigned', 'conflicts']).default('overview'),
  memberId: z.string().optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
});

/**
 * GET /api/assignments/statistics
 * Retrieve assignment statistics and reports
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = StatisticsRequestSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { type, memberId, startDate, endDate } = validationResult.data;

    switch (type) {
      case 'overview':
        // Check permissions for congregation-wide statistics
        if (!['elder', 'ministerial_servant'].includes(member.role)) {
          return NextResponse.json(
            { error: 'Insufficient permissions to view congregation statistics' },
            { status: 403 }
          );
        }

        // Get complete assignment statistics
        const statistics = await AssignmentCoordinationService.getAssignmentStatistics(
          member.congregationId
        );

        return NextResponse.json({
          success: true,
          statistics,
          type: 'overview',
        });

      case 'member':
        // Get member assignment summary
        const targetMemberId = memberId || member.id;

        // If requesting another member's data, check permissions
        if (targetMemberId !== member.id && !['elder', 'ministerial_servant'].includes(member.role)) {
          return NextResponse.json(
            { error: 'Insufficient permissions to view other members\' assignment data' },
            { status: 403 }
          );
        }

        const memberSummary = await AssignmentCoordinationService.getMemberAssignmentSummary(
          member.congregationId,
          targetMemberId
        );

        return NextResponse.json({
          success: true,
          memberSummary,
          type: 'member',
        });

      case 'unassigned':
        // Check permissions for unassigned parts
        if (!['elder', 'ministerial_servant'].includes(member.role)) {
          return NextResponse.json(
            { error: 'Insufficient permissions to view unassigned parts' },
            { status: 403 }
          );
        }

        // Get unassigned meeting parts
        const unassignedParts = await AssignmentCoordinationService.getUnassignedParts(
          member.congregationId,
          {
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
          }
        );

        return NextResponse.json({
          success: true,
          unassignedParts,
          count: unassignedParts.length,
          type: 'unassigned',
        });

      case 'conflicts':
        // Check permissions for conflict detection
        if (!['elder', 'ministerial_servant'].includes(member.role)) {
          return NextResponse.json(
            { error: 'Insufficient permissions to view assignment conflicts' },
            { status: 403 }
          );
        }

        // Check for assignment conflicts
        const conflicts = await AssignmentCoordinationService.checkAssignmentConflicts(
          member.congregationId,
          startDate ? new Date(startDate) : undefined,
          endDate ? new Date(endDate) : undefined
        );

        return NextResponse.json({
          success: true,
          conflicts,
          count: conflicts.length,
          type: 'conflicts',
        });

      default:
        return NextResponse.json(
          { error: 'Invalid statistics type' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Assignment statistics error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve assignment statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/assignments/statistics
 * Generate assignment reports and export data
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders can generate comprehensive reports
    if (member.role !== 'elder') {
      return NextResponse.json(
        { error: 'Only elders can generate assignment reports' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { reportType, startDate, endDate, includeStatistics } = body;

    // Validate date range
    if (startDate && endDate) {
      if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        return NextResponse.json(
          { error: 'Dates must be in YYYY-MM-DD format' },
          { status: 400 }
        );
      }

      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start > end) {
        return NextResponse.json(
          { error: 'Start date must be before or equal to end date' },
          { status: 400 }
        );
      }

      // Limit to 1 year maximum
      const daysDiff = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        return NextResponse.json(
          { error: 'Date range cannot exceed 1 year' },
          { status: 400 }
        );
      }
    }

    // Generate comprehensive report
    const [
      assignments,
      statistics,
      unassignedParts,
      conflicts
    ] = await Promise.all([
      AssignmentCoordinationService.getUpcomingAssignments(member.congregationId, {
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        includeUnassigned: true,
        limit: 1000, // Large limit for reports
      }),
      includeStatistics ? AssignmentCoordinationService.getAssignmentStatistics(member.congregationId) : null,
      AssignmentCoordinationService.getUnassignedParts(member.congregationId, {
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
      }),
      AssignmentCoordinationService.checkAssignmentConflicts(
        member.congregationId,
        startDate ? new Date(startDate) : undefined,
        endDate ? new Date(endDate) : undefined
      ),
    ]);

    // Calculate additional metrics
    const assignedParts = assignments.filter(a => a.assignedMember);
    const assignmentRate = assignments.length > 0 
      ? Math.round((assignedParts.length / assignments.length) * 100)
      : 0;

    const report = {
      reportType: reportType || 'comprehensive',
      generatedAt: new Date().toISOString(),
      dateRange: {
        from: startDate || null,
        to: endDate || null,
      },
      summary: {
        totalParts: assignments.length,
        assignedParts: assignedParts.length,
        unassignedParts: unassignedParts.length,
        assignmentRate,
        conflictCount: conflicts.length,
      },
      assignments,
      unassignedParts,
      conflicts,
      ...(includeStatistics && { statistics }),
    };

    return NextResponse.json({
      success: true,
      report,
      message: 'Assignment report generated successfully',
    });

  } catch (error) {
    console.error('Assignment report generation error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to generate assignment report',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
