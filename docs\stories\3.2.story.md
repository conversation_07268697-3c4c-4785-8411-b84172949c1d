# Story 3.2: Weekend Meeting Management

**Epic:** Epic 3: Meeting Management & JW.org Integration  
**Story Points:** 8  
**Priority:** High  
**Status:** Draft  

## Story

As a meeting coordinator,
I want to manage weekend meetings with public talk scheduling and Watchtower study coordination,
so that I can ensure proper speaker arrangements and study conductor assignments.

## Acceptance Criteria

1. **Public talk scheduling with speaker coordination and topic management**
   - Public talk scheduling interface with speaker database and availability tracking
   - Topic management with outline library and speaker expertise matching
   - Speaker coordination with invitation workflow and confirmation tracking
   - Visiting speaker management with congregation coordination and travel arrangements

2. **Watchtower study assignment with conductor and reader coordination**
   - Watchtower study conductor assignment with rotation and availability management
   - Reader assignment with member coordination and preparation tracking
   - Study material preparation with JW.org integration and resource access
   - Assignment conflict detection with alternative suggestions and resolution

3. **Meeting location management with Kingdom Hall and Zoom coordination**
   - Location configuration with Kingdom Hall and virtual meeting support
   - Zoom meeting management with link generation and access control
   - Hybrid meeting coordination with technical requirements and setup
   - Location-specific logistics with capacity management and resource allocation

4. **Speaker database with contact information and talk inventory tracking**
   - Comprehensive speaker database with contact details and availability
   - Talk inventory management with outline tracking and expertise areas
   - Speaker performance tracking with feedback and development monitoring
   - Visiting speaker coordination with circuit and district integration

5. **Meeting attendance tracking with participation analytics and reporting**
   - Attendance tracking with digital check-in and participation monitoring
   - Analytics dashboard with attendance trends and participation patterns
   - Reporting capabilities with congregation statistics and improvement insights
   - Member engagement tracking with participation quality and development

6. **Meeting preparation coordination with study materials and resource sharing**
   - Study material coordination with JW.org integration and resource distribution
   - Preparation tracking with conductor and reader readiness monitoring
   - Resource sharing with document distribution and collaborative preparation
   - Preparation analytics with effectiveness monitoring and improvement recommendations

7. **Weekend meeting calendar with recurring schedule and exception management**
   - Calendar management with recurring weekend meeting patterns
   - Exception handling with holiday adjustments and special arrangements
   - Schedule coordination with circuit assembly and convention integration
   - Calendar sharing with congregation communication and notification systems

## Dev Notes

### Technical Architecture

**Meeting Coordination:**
- Public talk scheduling with speaker database and availability management
- Watchtower study coordination with conductor and reader assignment
- Meeting location management with Kingdom Hall and virtual meeting support
- Calendar management with recurring patterns and exception handling

**Speaker Management:**
- Speaker database with contact information and talk inventory
- Visiting speaker coordination with invitation and confirmation workflow
- Speaker performance tracking with feedback and development monitoring
- Circuit integration with speaker sharing and coordination

### API Endpoints (tRPC)

```typescript
// Weekend meeting management routes
weekendMeetings: router({
  schedulePublicTalk: adminProcedure
    .input(z.object({
      date: z.date(),
      speakerId: z.string(),
      talkOutline: z.string(),
      talkTitle: z.string(),
      isVisitingSpeaker: z.boolean(),
      speakerCongregation: z.string().optional(),
      confirmationRequired: z.boolean().default(true)
    }))
    .mutation(async ({ input, ctx }) => {
      return await weekendMeetingService.schedulePublicTalk(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    }),

  assignWatchtowerStudy: adminProcedure
    .input(z.object({
      date: z.date(),
      conductorId: z.string(),
      readerId: z.string(),
      studyArticle: z.string(),
      preparationDeadline: z.date(),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await weekendMeetingService.assignWatchtowerStudy(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    }),

  manageMeetingLocation: adminProcedure
    .input(z.object({
      date: z.date(),
      location: z.enum(['kingdom_hall', 'zoom', 'hybrid']),
      zoomDetails: z.object({
        meetingId: z.string(),
        password: z.string(),
        joinUrl: z.string()
      }).optional(),
      capacity: z.number().optional(),
      technicalRequirements: z.array(z.string()).optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await weekendMeetingService.manageMeetingLocation(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    }),

  getWeekendSchedule: protectedProcedure
    .input(z.object({
      startDate: z.date(),
      endDate: z.date(),
      includeVisitingSpeakers: z.boolean().default(true)
    }))
    .query(async ({ input, ctx }) => {
      return await weekendMeetingService.getWeekendSchedule(
        input.startDate,
        input.endDate,
        ctx.user.congregationId,
        input.includeVisitingSpeakers
      );
    })
})
```

### Data Models

```typescript
interface WeekendMeeting {
  id: string;
  congregationId: string;
  date: Date;
  publicTalk: {
    speakerId: string;
    talkOutline: string;
    talkTitle: string;
    isVisitingSpeaker: boolean;
    speakerCongregation?: string;
    confirmationStatus: 'pending' | 'confirmed' | 'declined';
  };
  watchtowerStudy: {
    conductorId: string;
    readerId: string;
    studyArticle: string;
    preparationDeadline: Date;
    preparationStatus: 'assigned' | 'prepared' | 'completed';
  };
  location: 'kingdom_hall' | 'zoom' | 'hybrid';
  zoomDetails?: {
    meetingId: string;
    password: string;
    joinUrl: string;
  };
  attendance: MeetingAttendance[];
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Speaker {
  id: string;
  congregationId: string;
  name: string;
  congregation: string;
  contactInfo: {
    phone?: string;
    email?: string;
  };
  talkOutlines: string[];
  availability: {
    preferredDates: Date[];
    blackoutDates: Date[];
    maxTravelDistance: number;
  };
  isActive: boolean;
  lastTalkDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Critical Implementation Requirements

1. **Multi-Tenant Data Isolation**: Every weekend meeting query must include congregation_id filtering
2. **Speaker Coordination**: Reliable speaker management with visiting speaker integration
3. **Type Safety Enforcement**: All API calls use tRPC procedures with Zod validation
4. **Database-First Testing**: Real database with comprehensive weekend meeting scenarios
5. **Local Infrastructure Only**: Local PostgreSQL and file storage for meeting data
6. **Calendar Integration**: Efficient calendar management with recurring patterns

### Testing Requirements

**Unit Tests:**
- Public talk scheduling with speaker coordination logic
- Watchtower study assignment with conflict detection
- Meeting location management with technical requirements
- Speaker database management with availability tracking

**Integration Tests:**
- Complete weekend meeting workflow from scheduling to completion
- Multi-congregation speaker coordination and visiting speaker management
- Meeting location coordination with Kingdom Hall and virtual meeting support
- Calendar management with recurring patterns and exception handling

**E2E Tests:**
- Full weekend meeting coordinator interface with speaker management
- Public talk scheduling and Watchtower study assignment workflow
- Meeting location management with Zoom integration
- Weekend meeting calendar with attendance tracking

## Testing

### Test Data Requirements

- Sample speaker database with various congregations and talk outlines
- Complex weekend meeting scenarios with visiting speakers and location changes
- Test calendar patterns with recurring meetings and exceptions
- Sample attendance data for analytics and reporting validation

### Validation Scenarios

- Test weekend meeting coordination with various speaker availability patterns
- Validate meeting location management with technical requirements
- Test calendar management with recurring patterns and exception handling
- Verify speaker coordination with visiting speaker arrangements

## Definition of Done

- [ ] Public talk scheduling with speaker coordination and topic management
- [ ] Watchtower study assignment with conductor and reader coordination
- [ ] Meeting location management with Kingdom Hall and Zoom coordination
- [ ] Speaker database with contact information and talk inventory tracking
- [ ] Meeting attendance tracking with participation analytics and reporting
- [ ] Meeting preparation coordination with study materials and resource sharing
- [ ] Weekend meeting calendar with recurring schedule and exception management
- [ ] All unit tests pass with real weekend meeting scenarios
- [ ] Integration tests validate complete weekend meeting workflow
- [ ] E2E tests confirm speaker coordination and meeting management interface
- [ ] Code review completed and approved
- [ ] Documentation updated with weekend meeting management features

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: BMad Master Task Executor
- Date: 2025-01-24

### Debug Log References
- None yet

### Completion Notes
- Story recreated with comprehensive weekend meeting management system
- Speaker coordination with visiting speaker management and talk inventory
- Meeting location management with Kingdom Hall and virtual meeting support
- Complete API specification with tRPC procedures for weekend meeting coordination
- Testing requirements defined with complex speaker and meeting scenarios

### File List
- docs/stories/3.2.story.md (recreated)

### Change Log
- 2025-01-24: Story recreated with comprehensive weekend meeting specification
