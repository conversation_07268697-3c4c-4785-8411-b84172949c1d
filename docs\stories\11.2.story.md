# Story 11.2: Territory Assignment History Tracking

**Epic:** Epic 11: Territory Assignment & Management  
**Story Points:** 5  
**Priority:** High  
**Status:** Draft

## Story

**As a** congregation administrator,  
**I want** to view territory assignment history,  
**so that** I can track who has worked territories and when they were completed.

## Acceptance Criteria

1. Territory detail view displays complete assignment history chronologically
2. History entries show assigned member, assignment date, completion date, and duration
3. Current assignment is clearly distinguished from historical assignments
4. Assignment history is preserved when territories are reassigned
5. History view includes search and filter capabilities by member or date range
6. Assignment statistics show average assignment duration and completion rates

## Tasks / Subtasks

- [ ] Create territory assignment history component (AC: 1, 3)
  - [ ] Build AssignmentHistory component for territory detail view
  - [ ] Display assignment history in chronological order (newest first)
  - [ ] Clearly distinguish current assignment from historical assignments
  - [ ] Add visual indicators for assignment status (active, completed, overdue)
  - [ ] Implement responsive design for mobile and desktop viewing
- [ ] Implement assignment history data display (AC: 2)
  - [ ] Show assigned member name and role for each assignment
  - [ ] Display assignment date with proper date formatting
  - [ ] Show completion date when assignment is completed
  - [ ] Calculate and display assignment duration in days
  - [ ] Add assignment notes and status information
- [ ] Create assignment history API endpoints (AC: 4)
  - [ ] Implement GET /api/territories/{id}/assignments endpoint
  - [ ] Add proper congregation isolation and security
  - [ ] Include member information in assignment history response
  - [ ] Ensure assignment history is preserved during reassignments
  - [ ] Add pagination for territories with extensive history
- [ ] Add search and filtering functionality (AC: 5)
  - [ ] Implement member name search within assignment history
  - [ ] Add date range filtering for assignment periods
  - [ ] Create status filtering (active, completed, overdue, cancelled)
  - [ ] Add assignment duration filtering (short, medium, long assignments)
  - [ ] Implement clear filters functionality
- [ ] Create assignment statistics dashboard (AC: 6)
  - [ ] Calculate average assignment duration for territory
  - [ ] Show completion rates and assignment frequency
  - [ ] Display member assignment statistics
  - [ ] Create visual charts for assignment trends
  - [ ] Add territory utilization metrics
- [ ] Integrate with territory detail view (Integration)
  - [ ] Add assignment history tab to territory detail page
  - [ ] Create navigation between territory info and assignment history
  - [ ] Update assignment history real-time when new assignments are made
  - [ ] Add quick actions for current assignment management
- [ ] Implement assignment history service (Backend)
  - [ ] Create AssignmentHistoryService with history retrieval methods
  - [ ] Add getAssignmentHistory method with filtering and pagination
  - [ ] Implement assignment statistics calculation methods
  - [ ] Add duration calculation utilities using date-fns
  - [ ] Create assignment analytics and reporting functions
- [ ] Write comprehensive tests (Testing Standards)
  - [ ] Unit tests for assignment history components and filtering
  - [ ] Integration tests for assignment history API endpoints
  - [ ] Test assignment statistics calculations and accuracy
  - [ ] Test search and filtering functionality
  - [ ] E2E tests for assignment history navigation and display

## Dev Notes

### Dependencies and Prerequisites
**DEPENDENCY**: This story depends on:
- Story 11.1 (Member Territory Assignment Interface) - Assignment creation must exist
- Story 10.1 (Territory Database Schema) - TerritoryAssignment model with history tracking

### Assignment History Architecture
[Source: docs/territories-architecture.md#territory-assignment-service]

**Key Interface:**
- `getAssignmentHistory(territoryId: string): Promise<TerritoryAssignment[]>`

**Technology Stack:** TypeScript service classes, Prisma for assignment tracking, date-fns for duration calculations

### Assignment History Types
[Source: src/types/territories/assignment.ts]

**AssignmentHistory Interface:**
```typescript
export interface AssignmentHistory {
  id: string;
  territoryId: string;
  memberId: string;
  assignedBy: string;
  assignedAt: Date;
  completedAt?: Date;
  duration?: number; // in days
  status: AssignmentStatus;
  notes?: string;
}
```

**AssignmentSummary Interface:**
```typescript
export interface AssignmentSummary {
  totalAssignments: number;
  activeAssignments: number;
  completedAssignments: number;
  overdueAssignments: number;
  averageDuration: number;
}
```

### Database Schema Integration
[Source: docs/territories-architecture.md#database-schema]

**TerritoryAssignment Model Fields:**
- assignedAt: Assignment timestamp
- completedAt: Completion timestamp (nullable)
- status: Assignment status tracking
- notes: Assignment-specific notes
- All assignments preserved for historical tracking

### API Specification
**Assignment History API Endpoints:**
- `GET /api/territories/{id}/assignments` - Get assignment history for territory
- `GET /api/territories/{id}/assignments/stats` - Get assignment statistics
- Query parameters: `member`, `dateFrom`, `dateTo`, `status`, `page`, `limit`
- Response: Array of TerritoryAssignment objects with member details

### Component Architecture
**Component Organization:**
- `src/components/territories/admin/AssignmentHistory.tsx` - Main history component
- `src/components/territories/admin/AssignmentHistoryItem.tsx` - Individual history entry
- `src/components/territories/admin/AssignmentStats.tsx` - Statistics dashboard
- `src/components/territories/shared/AssignmentFilters.tsx` - Search and filter controls

### Technology Stack
[Source: docs/territories-architecture.md#tech-stack]
- **Frontend**: Next.js 14+ with TypeScript, Tailwind CSS for styling
- **Data Visualization**: Chart.js or similar for assignment trend charts
- **Date Handling**: date-fns for duration calculations and date formatting
- **State Management**: React Query for assignment history caching
- **Backend**: Prisma ORM for assignment history queries

### File Structure and Locations
[Source: docs/territories-architecture.md#unified-project-structure]
- **History Component**: `src/components/territories/admin/AssignmentHistory.tsx`
- **API Route**: `src/app/api/territories/[id]/assignments/route.ts`
- **Service**: `src/services/territories/AssignmentHistoryService.ts`
- **Types**: `src/types/territories/assignment.ts` (already exists)

### Security and Performance
**Security Requirements:**
- Congregation isolation for assignment history queries
- Admin role verification for accessing assignment history
- Proper authentication for all history API endpoints

**Performance Considerations:**
- Pagination for territories with extensive assignment history
- Efficient database queries with proper indexing
- Caching of assignment statistics for frequently accessed territories

### Assignment Statistics Calculations
**Duration Calculations:**
- Assignment duration = completedAt - assignedAt (in days)
- Average duration across all completed assignments
- Completion rate = completed assignments / total assignments
- Territory utilization = total days assigned / total days since first assignment

### Testing Requirements
[Source: docs/territories-architecture.md#testing-strategy]
- **Component Tests**: React Testing Library for history display and filtering
- **API Tests**: Supertest for assignment history endpoints
- **Statistics Tests**: Verify accuracy of duration and completion rate calculations
- **Performance Tests**: Test pagination and query performance with large datasets
- **Security Tests**: Verify congregation isolation and role-based access

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-25 | 1.0 | Initial story creation for territory assignment history tracking | PO Agent |

## Dev Agent Record

### Agent Model Used
*To be populated by development agent*

### Debug Log References
*To be populated by development agent*

### Completion Notes List
*To be populated by development agent*

### File List
*To be populated by development agent*

## QA Results
*To be populated by QA agent*
