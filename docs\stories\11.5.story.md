# Story 11.5: Territory Assignment Notifications

**Epic:** Epic 11: Territory Assignment & Management  
**Story Points:** 5  
**Priority:** High  
**Status:** Draft

## Story

**As a** congregation member,  
**I want** to receive notifications about territory assignments,  
**so that** I'm informed when territories are assigned to me or need attention.

## Acceptance Criteria

1. Email notifications are sent when territories are assigned to members
2. Notification includes territory details and assignment information
3. Reminder notifications are sent for territories assigned beyond typical duration
4. Members can configure notification preferences (email, in-app, frequency)
5. Administrators receive notifications when territories are marked completed
6. Notification system integrates with existing Coral Oeste App notification patterns

## Tasks / Subtasks

- [ ] Integrate with existing notification system (AC: 6)
  - [ ] Extend existing CommunicationService for territory notifications
  - [ ] Add TERRITORIES category to existing NotificationCategory enum
  - [ ] Integrate with existing notification preferences system
  - [ ] Use existing notification delivery methods (email, in-app, SMS)
  - [ ] Follow existing notification patterns and templates
- [ ] Create territory assignment notifications (AC: 1, 2)
  - [ ] Send notification when territory is assigned to member
  - [ ] Include territory number, address, and assignment date in notification
  - [ ] Add assignment details and any special instructions
  - [ ] Include link to territory detail view in member interface
  - [ ] Use existing email templates with territory-specific content
- [ ] Implement territory completion notifications (AC: 5)
  - [ ] Send notification to administrators when territory is marked completed
  - [ ] Include completion details and member information
  - [ ] Add territory statistics and completion time
  - [ ] Notify service coordinators and overseers
  - [ ] Include link to territory assignment history
- [ ] Create territory reminder notifications (AC: 3)
  - [ ] Implement scheduled reminder system for overdue territories
  - [ ] Calculate typical assignment duration and identify overdue assignments
  - [ ] Send gentle reminder notifications to members with overdue territories
  - [ ] Include territory details and assignment duration
  - [ ] Add escalation notifications to administrators for long-overdue territories
- [ ] Extend notification preferences for territories (AC: 4)
  - [ ] Add territory notification preferences to existing preference system
  - [ ] Allow members to configure territory assignment notification frequency
  - [ ] Add territory reminder notification preferences
  - [ ] Integrate with existing quiet hours and delivery method preferences
  - [ ] Create territory-specific notification categories
- [ ] Create territory notification service (Backend Integration)
  - [ ] Implement TerritoryNotificationService extending CommunicationService
  - [ ] Add territory assignment notification methods
  - [ ] Create territory completion notification methods
  - [ ] Implement territory reminder notification scheduling
  - [ ] Add notification template management for territory notifications
- [ ] Integrate with territory workflow (Workflow Integration)
  - [ ] Trigger assignment notifications when territories are assigned
  - [ ] Send completion notifications when territories are marked completed
  - [ ] Schedule reminder notifications based on assignment duration
  - [ ] Update notification status when territory status changes
  - [ ] Handle notification cleanup when territories are reassigned
- [ ] Write comprehensive tests (Testing Standards)
  - [ ] Unit tests for territory notification service and methods
  - [ ] Integration tests for notification delivery and preferences
  - [ ] Test notification scheduling and reminder system
  - [ ] Test notification content and template rendering
  - [ ] E2E tests for complete notification workflow

## Dev Notes

### Dependencies and Prerequisites
**DEPENDENCY**: This story depends on:
- Story 11.1 (Member Territory Assignment Interface) - Assignment workflow integration
- Story 11.3 (Territory Status Management) - Status change notifications
- Existing CommunicationService and notification infrastructure

### Existing Notification System Integration
[Source: src/lib/services/communicationService.ts]

**Existing Notification Infrastructure:**
- **CommunicationService**: Comprehensive notification service with multi-channel delivery
- **NotificationCategory**: Existing categories including ASSIGNMENTS - extend for TERRITORIES
- **DeliveryMethod**: Email, SMS, in-app notifications already implemented
- **CommunicationPreferences**: User preference management already exists
- **Notification Templates**: Template system for consistent messaging

### Notification Categories Extension
[Source: src/lib/services/communicationService.ts - NotificationCategory enum]

**Add Territory Category:**
```typescript
export enum NotificationCategory {
  // ... existing categories
  TERRITORIES = 'TERRITORIES', // NEW: Territory-specific notifications
}
```

### Territory Notification Types
**Assignment Notifications:**
- Territory assigned to member
- Territory reassigned to different member
- Territory assignment updated or modified

**Completion Notifications:**
- Territory marked completed by member
- Territory completion confirmed by administrator
- Territory ready for reassignment

**Reminder Notifications:**
- Territory assignment reminder (weekly/monthly)
- Overdue territory notification
- Long-overdue territory escalation

### API Integration Points
[Source: src/app/api/notifications/route.ts]

**Existing API Endpoints:**
- `GET /api/notifications` - Retrieve notifications (extend for territory notifications)
- `POST /api/notifications` - Create notifications (use for territory notifications)
- `PUT /api/notifications` - Mark as read (existing functionality)

### Notification Service Extension
**TerritoryNotificationService Methods:**
```typescript
class TerritoryNotificationService extends CommunicationService {
  static async sendAssignmentNotification(territoryId: string, memberId: string, assignedBy: string): Promise<void>
  static async sendCompletionNotification(territoryId: string, memberId: string): Promise<void>
  static async sendReminderNotification(territoryId: string, memberId: string, daysSinceAssignment: number): Promise<void>
  static async scheduleReminderNotifications(territoryId: string, memberId: string): Promise<void>
}
```

### Notification Templates
**Territory Assignment Template:**
```
Subject: "Nuevo Territorio Asignado - {territoryNumber}"
Message: "Se te ha asignado el territorio {territoryNumber} en {address}. 
Fecha de asignación: {assignmentDate}. 
Ver detalles: {territoryDetailLink}"
```

**Territory Completion Template:**
```
Subject: "Territorio Completado - {territoryNumber}"
Message: "{memberName} ha completado el territorio {territoryNumber} en {address}.
Tiempo de asignación: {duration} días.
Ver historial: {historyLink}"
```

### File Structure and Locations
[Source: docs/territories-architecture.md#unified-project-structure]
- **Notification Service**: `src/services/territories/TerritoryNotificationService.ts`
- **Notification Integration**: Extend existing `src/lib/services/communicationService.ts`
- **API Integration**: Use existing `src/app/api/notifications/route.ts`
- **Preferences**: Extend existing `src/app/notifications/preferences/page.tsx`

### Technology Stack
[Source: Existing notification system]
- **Notification Service**: Existing CommunicationService with TypeScript
- **Email Delivery**: Existing email service integration
- **Database**: Prisma ORM with existing notification tables
- **Scheduling**: Node.js cron jobs or scheduled tasks for reminders
- **Templates**: Existing notification template system

### Notification Preferences Integration
[Source: src/app/notifications/preferences/page.tsx]

**Extend Existing Preferences:**
- Add territory notification preferences to existing preference interface
- Use existing categoryPreferences system for territory notifications
- Integrate with existing quiet hours and delivery method preferences
- Maintain existing preference management UI patterns

### Security and Authorization
**Notification Security:**
- Congregation isolation for all territory notifications
- Member-specific notifications (only notify assigned member)
- Administrator notifications for completion events
- Proper authentication for notification API endpoints

### Performance Considerations
**Notification Performance:**
- Batch notification processing for multiple territory assignments
- Efficient reminder notification scheduling
- Optimized database queries for overdue territory detection
- Rate limiting for notification delivery

### Testing Requirements
[Source: docs/territories-architecture.md#testing-strategy]
- **Notification Tests**: Test all territory notification types and delivery
- **Integration Tests**: Verify integration with existing notification system
- **Preference Tests**: Test territory notification preference management
- **Workflow Tests**: Test notification triggering from territory workflows
- **Performance Tests**: Test notification delivery performance and scheduling

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-25 | 1.0 | Initial story creation for territory assignment notifications | PO Agent |

## Dev Agent Record

### Agent Model Used
*To be populated by development agent*

### Debug Log References
*To be populated by development agent*

### Completion Notes List
*To be populated by development agent*

### File List
*To be populated by development agent*

## QA Results
*To be populated by QA agent*
