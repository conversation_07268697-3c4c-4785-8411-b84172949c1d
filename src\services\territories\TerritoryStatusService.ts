/**
 * Territory Status Service
 * 
 * Manages territory status changes, partial completion tracking, and visit logging.
 * Supports the real-world workflow where members complete territories multiple times
 * and may require multiple visits to complete a single assignment.
 */

import { prisma } from '@/lib/prisma';
import type { TerritoryStatus, AssignmentStatus } from '@/types/territories/territory';

export interface StatusChangeRequest {
  territoryId: string;
  newStatus: TerritoryStatus;
  reason?: string;
  notes?: string;
  changedBy: string;
  congregationId: string;
}

export interface VisitLogRequest {
  assignmentId: string;
  visitDate: Date;
  isCompleted: boolean;
  notes?: string;
  addressesWorked?: string;
  congregationId: string;
}

export interface PartialCompletionRequest {
  assignmentId: string;
  isPartiallyCompleted: boolean;
  partialCompletionNotes?: string;
  visitCount: number;
  congregationId: string;
}

export class TerritoryStatusService {
  /**
   * Update territory status with audit logging
   */
  static async updateTerritoryStatus(request: StatusChangeRequest) {
    const { territoryId, newStatus, reason, notes, changedBy, congregationId } = request;

    // Get current territory status
    const territory = await prisma.territory.findFirst({
      where: {
        id: territoryId,
        congregationId
      },
      select: {
        id: true,
        territoryNumber: true,
        status: true,
        currentAssignment: {
          select: {
            id: true,
            status: true,
            member: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!territory) {
      throw new Error('Territory not found');
    }

    const oldStatus = territory.status;

    // Validate status transition
    this.validateStatusTransition(oldStatus, newStatus, territory.currentAssignment);

    // Update territory status
    const updatedTerritory = await prisma.territory.update({
      where: { id: territoryId },
      data: {
        status: newStatus,
        updatedAt: new Date()
      },
      include: {
        currentAssignment: {
          include: {
            member: {
              select: {
                name: true,
                role: true
              }
            }
          }
        }
      }
    });

    // Log status change (we'll create this model later if needed)
    // For now, we'll use the notes field or create a simple log

    // Handle automatic status transitions
    if (newStatus === 'completed') {
      await this.handleTerritoryCompletion(territoryId, congregationId);
    }

    return updatedTerritory;
  }

  /**
   * Log a territory visit
   */
  static async logTerritoryVisit(request: VisitLogRequest) {
    const { assignmentId, visitDate, isCompleted, notes, addressesWorked, congregationId } = request;

    // Verify assignment exists and belongs to congregation
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        id: assignmentId,
        congregationId,
        status: 'active'
      },
      include: {
        territory: {
          select: {
            territoryNumber: true,
            status: true
          }
        }
      }
    });

    if (!assignment) {
      throw new Error('Active assignment not found');
    }

    // Create visit record
    const visit = await prisma.territoryVisit.create({
      data: {
        assignmentId,
        visitDate,
        isCompleted,
        notes,
        addressesWorked,
        congregationId
      }
    });

    // Update assignment visit count
    const updatedAssignment = await prisma.territoryAssignment.update({
      where: { id: assignmentId },
      data: {
        visitCount: {
          increment: 1
        },
        updatedAt: new Date()
      }
    });

    // If this visit completed the territory, handle completion
    if (isCompleted) {
      await this.completeAssignment(assignmentId, congregationId);
    }

    return { visit, assignment: updatedAssignment };
  }

  /**
   * Update partial completion status
   */
  static async updatePartialCompletion(request: PartialCompletionRequest) {
    const { assignmentId, isPartiallyCompleted, partialCompletionNotes, visitCount, congregationId } = request;

    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        id: assignmentId,
        congregationId
      }
    });

    if (!assignment) {
      throw new Error('Assignment not found');
    }

    return await prisma.territoryAssignment.update({
      where: { id: assignmentId },
      data: {
        isPartiallyCompleted,
        partialCompletionNotes,
        visitCount,
        updatedAt: new Date()
      },
      include: {
        territory: {
          select: {
            territoryNumber: true,
            address: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      }
    });
  }

  /**
   * Complete an assignment and make territory available for reassignment
   */
  static async completeAssignment(assignmentId: string, congregationId: string) {
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        id: assignmentId,
        congregationId
      },
      include: {
        territory: true
      }
    });

    if (!assignment) {
      throw new Error('Assignment not found');
    }

    // Update assignment status to completed
    await prisma.territoryAssignment.update({
      where: { id: assignmentId },
      data: {
        status: 'completed',
        completedAt: new Date(),
        updatedAt: new Date()
      }
    });

    // Make territory available for reassignment (territories are worked multiple times)
    await prisma.territory.update({
      where: { id: assignment.territoryId },
      data: {
        status: 'available',
        updatedAt: new Date()
      }
    });

    return assignment;
  }

  /**
   * Get visit history for an assignment
   */
  static async getVisitHistory(assignmentId: string, congregationId: string) {
    return await prisma.territoryVisit.findMany({
      where: {
        assignmentId,
        congregationId
      },
      orderBy: {
        visitDate: 'desc'
      }
    });
  }

  /**
   * Get assignment statistics including visit data
   */
  static async getAssignmentStatistics(assignmentId: string, congregationId: string) {
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        id: assignmentId,
        congregationId
      },
      include: {
        visits: {
          orderBy: {
            visitDate: 'asc'
          }
        },
        territory: {
          select: {
            territoryNumber: true,
            address: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      }
    });

    if (!assignment) {
      throw new Error('Assignment not found');
    }

    const totalVisits = assignment.visits.length;
    const completedVisits = assignment.visits.filter(v => v.isCompleted).length;
    const lastVisit = assignment.visits[assignment.visits.length - 1];
    
    // Calculate duration
    const assignedDate = new Date(assignment.assignedAt);
    const endDate = assignment.completedAt ? new Date(assignment.completedAt) : new Date();
    const durationDays = Math.ceil((endDate.getTime() - assignedDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      assignment,
      statistics: {
        totalVisits,
        completedVisits,
        durationDays,
        isPartiallyCompleted: assignment.isPartiallyCompleted,
        lastVisitDate: lastVisit?.visitDate,
        averageVisitsPerWeek: totalVisits > 0 && durationDays > 0 ? (totalVisits / (durationDays / 7)).toFixed(1) : 0
      }
    };
  }

  /**
   * Validate status transitions
   */
  private static validateStatusTransition(
    oldStatus: TerritoryStatus,
    newStatus: TerritoryStatus,
    currentAssignment?: any
  ) {
    // Business rules for status transitions
    const validTransitions: Record<TerritoryStatus, TerritoryStatus[]> = {
      available: ['assigned', 'unavailable'],
      assigned: ['completed', 'available', 'unavailable'],
      completed: ['available', 'unavailable'],
      unavailable: ['available']
    };

    if (!validTransitions[oldStatus]?.includes(newStatus)) {
      throw new Error(`Invalid status transition from ${oldStatus} to ${newStatus}`);
    }

    // Additional validation rules
    if (newStatus === 'assigned' && currentAssignment?.status === 'active') {
      throw new Error('Territory is already assigned to an active member');
    }

    if (newStatus === 'completed' && !currentAssignment) {
      throw new Error('Cannot mark territory as completed without an active assignment');
    }
  }

  /**
   * Handle territory completion workflow
   */
  private static async handleTerritoryCompletion(territoryId: string, congregationId: string) {
    // Get current assignment
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        territoryId,
        congregationId,
        status: 'active'
      }
    });

    if (assignment) {
      // Complete the assignment
      await this.completeAssignment(assignment.id, congregationId);
    }

    // Territory status is already updated to 'completed' by the caller
    // It will be made 'available' when the assignment is completed
  }

  /**
   * Get territories by status with filtering
   */
  static async getTerritoriesByStatus(
    congregationId: string,
    status?: TerritoryStatus,
    includeAssignments: boolean = false
  ) {
    const whereClause: any = { congregationId };
    
    if (status) {
      whereClause.status = status;
    }

    return await prisma.territory.findMany({
      where: whereClause,
      include: {
        currentAssignment: includeAssignments ? {
          include: {
            member: {
              select: {
                name: true,
                role: true
              }
            }
          }
        } : false
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });
  }
}
