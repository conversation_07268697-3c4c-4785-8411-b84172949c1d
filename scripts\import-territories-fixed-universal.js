/**
 * Universal Territory Import Script - Fixed Parsing
 * 
 * Handles both houses and buildings with proper Excel structure parsing
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const CONGREGATION_ID = '1441'; // Coral Oeste

// Territory configurations
const TERRITORY_CONFIGS = {
  '004': { displayOrder: 4, zipCode: 'Miami, FL 33126' },
  '005': { displayOrder: 5, zipCode: 'Miami, FL 33126' },
  '007': { displayOrder: 7, zipCode: 'Miami, FL 33144' },
  '008': { displayOrder: 8, zipCode: 'Miami, FL 33144' },
  '009': { displayOrder: 9, zipCode: 'Miami, FL 33144' },
  '010': { displayOrder: 10, zipCode: 'Miami, FL 33144' }
};

/**
 * Parse addresses from Excel data with building and house support
 */
function parseAddresses(excelData, territoryNumber, zipCode) {
  const addresses = [];
  let currentStreet = '';
  let currentBuilding = '';
  let isInBuilding = false;
  
  for (let i = 0; i < excelData.length; i++) {
    const row = excelData[i];
    
    if (!row || row.length === 0) continue;
    
    // Skip header rows and metadata
    if (i < 7) continue;
    
    // Column B (index 1) contains street names, building names, and house/apartment numbers
    const cellB = row[1];
    if (!cellB) continue;
    
    const cellValue = cellB.toString().trim();
    if (!cellValue) continue;
    
    // Skip Excel date serial numbers
    if (typeof cellB === 'number' && cellB > 40000) {
      console.log(`⏭️  Skipping Excel date serial: ${cellB}`);
      continue;
    }
    
    // Check for building indicators
    if (cellValue.includes('FLAGLER') || cellValue.includes('Edif') || cellValue.includes('Edificio')) {
      // Extract building address
      if (cellValue.includes('FLAGLER')) {
        currentBuilding = cellValue;
        isInBuilding = true;
        console.log(`🏢 Found building: ${currentBuilding}`);
        continue;
      } else if (cellValue.includes('Edif') || cellValue.includes('Edificio')) {
        // This might be a note about the building, look for the actual address in notes
        const notes = row[6] && typeof row[6] === 'string' ? row[6].toString().trim() : '';
        if (notes === 'Edificio') {
          currentBuilding = cellValue;
          isInBuilding = true;
          console.log(`🏢 Found building: ${currentBuilding}`);
          continue;
        }
      }
    }
    
    // Check if this is a street name (for regular houses)
    const isStreetName = /^[A-Z]/.test(cellValue) && 
                        (/AVE|ST|RD|CT|WAY|BLVD|PL|LN|DR|CIR|CANAL/i.test(cellValue) ||
                         cellValue.includes('NW ') || cellValue.includes('SW ') ||
                         cellValue.includes('NE ') || cellValue.includes('SE '));
    
    if (isStreetName && !isInBuilding) {
      currentStreet = cellValue;
      console.log(`📍 Found street: ${currentStreet}`);
      continue;
    }
    
    // Check if this is a house/apartment number
    if (/^\d/.test(cellValue)) {
      const number = cellValue;
      let fullAddress = '';
      
      if (isInBuilding && currentBuilding) {
        // Building apartment: "Apt 1, 6437 W FLAGLER ST, Miami, FL 33144"
        fullAddress = `Apt ${number}, ${currentBuilding}, ${zipCode}`;
      } else if (currentStreet) {
        // Regular house: "214 NW 64 CT, Miami, FL 33126"
        const numericValue = parseInt(number);
        if (numericValue > 10000 && !currentStreet.includes('CANAL')) {
          console.log(`⏭️  Skipping large house number: ${number}`);
          continue;
        }
        fullAddress = `${number} ${currentStreet}, ${zipCode}`;
      } else {
        console.log(`⚠️  Number ${number} found without street or building context`);
        continue;
      }
      
      // Get notes from column G (index 6) if available
      let notes = null;
      if (row[6] && typeof row[6] === 'string') {
        const noteText = row[6].toString().trim();
        if (noteText && noteText !== 'null' && noteText !== '' && noteText !== 'Edificio') {
          notes = noteText;
        }
      }
      
      addresses.push({
        address: fullAddress,
        notes: notes,
        street: isInBuilding ? currentBuilding : currentStreet,
        houseNumber: number,
        isBuilding: isInBuilding
      });
      
      console.log(`${isInBuilding ? '🏢' : '🏠'} Added address: ${fullAddress}${notes ? ` (${notes})` : ''}`);
    }
  }
  
  return addresses;
}

async function importTerritoryFixed(territoryNumber) {
  try {
    console.log(`\n📂 Importing Territory ${territoryNumber} (Fixed Parsing)...`);
    
    const config = TERRITORY_CONFIGS[territoryNumber];
    if (!config) {
      console.error(`❌ No configuration found for Territory ${territoryNumber}`);
      return false;
    }

    // Read Excel file
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${territoryNumber}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    // Determine sheet name based on territory
    let sheetName = '';
    if (territoryNumber === '004') sheetName = 'Terr 4';
    else if (territoryNumber === '005') sheetName = 'Terr 5';
    else if (territoryNumber === '007') sheetName = 'Terr 7';
    else if (territoryNumber === '008') sheetName = 'Terr 8';
    else if (territoryNumber === '009') sheetName = 'Terr 9';
    else if (territoryNumber === '010') sheetName = 'Terr 10';
    
    const worksheet = workbook.Sheets[sheetName];
    
    if (!worksheet) {
      console.error(`❌ Sheet '${sheetName}' not found`);
      console.log(`Available sheets: ${workbook.SheetNames.join(', ')}`);
      return false;
    }
    
    console.log(`📊 Using sheet: ${sheetName}`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse addresses with building/house logic
    const addresses = parseAddresses(excelData, territoryNumber, config.zipCode);
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    if (addresses.length === 0) {
      console.log(`⚠️  No addresses found to import for Territory ${territoryNumber}`);
      return false;
    }
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: CONGREGATION_ID }
    });

    if (!congregation) {
      console.error(`❌ Congregation ${CONGREGATION_ID} (Coral Oeste) not found`);
      return false;
    }

    console.log(`✅ Found congregation: ${congregation.name}`);
    
    // Clear existing territory
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: congregation.id,
        territory: {
          territoryNumber: territoryNumber
        }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: congregation.id,
        territoryNumber: territoryNumber
      }
    });
    
    console.log(`🗑️  Cleared existing Territory ${territoryNumber}`);
    
    // Create the territory with all addresses combined
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: congregation.id,
        territoryNumber: territoryNumber,
        address: allAddresses,
        notes: allNotes || null,
        status: 'available',
        displayOrder: config.displayOrder
      }
    });
    
    console.log(`✅ Created Territory ${territoryNumber} with ${addresses.length} addresses`);
    console.log(`📍 Display Order: ${config.displayOrder}`);
    
    // Display summary
    console.log('\n📋 Address Summary:');
    const buildingCount = addresses.filter(addr => addr.isBuilding).length;
    const houseCount = addresses.filter(addr => !addr.isBuilding).length;
    console.log(`   🏢 Buildings/Apartments: ${buildingCount}`);
    console.log(`   🏠 Houses: ${houseCount}`);
    console.log(`   📝 With Notes: ${addresses.filter(addr => addr.notes).length}`);
    
    return true;
    
  } catch (error) {
    console.error(`❌ Error importing Territory ${territoryNumber}:`, error.message);
    return false;
  }
}

async function importMultipleTerritoriesFixed() {
  try {
    console.log('🚀 Starting fixed import for territories 4, 5, 7, 8, 9, 10...');
    
    const territories = ['004', '005', '007', '008', '009', '010'];
    let successCount = 0;
    let failCount = 0;
    
    for (const territory of territories) {
      const success = await importTerritoryFixed(territory);
      if (success) {
        successCount++;
      } else {
        failCount++;
      }
      
      // Small delay between imports
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`\n🎉 Import completed!`);
    console.log(`✅ Successfully imported: ${successCount} territories`);
    console.log(`❌ Failed to import: ${failCount} territories`);
    
  } catch (error) {
    console.error('❌ Error during import:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  importMultipleTerritoriesFixed();
}

module.exports = { importTerritoryFixed, importMultipleTerritoriesFixed };
