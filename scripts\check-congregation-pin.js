/**
 * Check Congregation PIN
 * 
 * Checks the correct PIN for the congregation
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCongregationPin() {
  try {
    console.log('🔐 Checking Congregation PIN...\n');

    // Get congregation info
    const congregation = await prisma.congregation.findFirst({
      where: { id: '1441' },
      select: {
        id: true,
        name: true,
        pin: true,
      },
    });

    if (congregation) {
      console.log('✅ Congregation found:');
      console.log(`   ID: ${congregation.id}`);
      console.log(`   Name: ${congregation.name}`);
      console.log(`   PIN: ${congregation.pin}`);
    } else {
      console.log('❌ Congregation 1441 not found');
      
      // List all congregations
      const allCongregations = await prisma.congregation.findMany({
        select: {
          id: true,
          name: true,
          pin: true,
        },
      });
      
      console.log('\n📋 All congregations:');
      allCongregations.forEach(cong => {
        console.log(`   ID: ${cong.id}, Name: ${cong.name}, PIN: ${cong.pin}`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking congregation PIN:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkCongregationPin().catch(console.error);
