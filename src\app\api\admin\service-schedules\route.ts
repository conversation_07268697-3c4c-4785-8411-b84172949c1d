/**
 * Service Schedule Management API Endpoint
 *
 * Handles CRUD operations for service schedules including
 * weekly schedule creation, service time management, and conductor assignments.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import {
  ServiceScheduleService,
  ServiceScheduleInput,
  ServiceScheduleTimeInput
} from '@/lib/services/serviceScheduleService';

// Validation schemas
const CreateScheduleSchema = z.object({
  weekStartDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  weekEndDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
});

const AddServiceTimeSchema = z.object({
  scheduleId: z.string().min(1, 'Schedule ID is required'),
  serviceDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  serviceTime: z.string().regex(/^\d{2}:\d{2}$/, 'Invalid time format (HH:MM)'),
  location: z.string().min(1, 'Location is required'),
  address: z.string().optional(),
  conductorId: z.string().optional(),
  notes: z.string().optional(),
});

const GetScheduleSchema = z.object({
  weekStartDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)').optional(),
});

/**
 * GET /api/admin/service-schedules
 * Retrieve service schedule for a specific week or current week
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    const member = authResult.user;

    // Check if user has permission to view service schedules
    if (!['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view service schedules' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    const validationResult = GetScheduleSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { weekStartDate } = validationResult.data;

    if (weekStartDate) {
      // Get specific weekly schedule
      const schedule = await ServiceScheduleService.getWeeklySchedule(
        member.congregationId,
        weekStartDate
      );

      return NextResponse.json({
        success: true,
        schedule,
        weekStartDate: weekStartDate,
      });
    } else {
      // Get all service times for the congregation
      const allServiceTimes = await ServiceScheduleService.getAllServiceTimes(
        member.congregationId
      );

      return NextResponse.json({
        success: true,
        schedule: {
          serviceTimes: allServiceTimes
        },
        allServiceTimes: true,
      });
    }

  } catch (error) {
    console.error('Service schedule GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve service schedule',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/service-schedules
 * Create or update a weekly service schedule
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    const member = authResult.user;

    // Check if user has permission to manage service schedules
    if (!['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to manage service schedules' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action } = body;

    if (action === 'create_schedule') {
      // Create or update weekly schedule
      const validationResult = CreateScheduleSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: 'Invalid schedule data',
            details: validationResult.error.errors,
          },
          { status: 400 }
        );
      }

      const scheduleData: ServiceScheduleInput = validationResult.data;

      const schedule = await ServiceScheduleService.upsertWeeklySchedule(
        member.congregationId,
        scheduleData
      );

      return NextResponse.json({
        success: true,
        schedule,
        message: 'Service schedule created/updated successfully',
      }, { status: 201 });

    } else if (action === 'add_service_time') {
      // Add service time to schedule
      const validationResult = AddServiceTimeSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: 'Invalid service time data',
            details: validationResult.error.errors,
          },
          { status: 400 }
        );
      }

      const { scheduleId, ...timeData } = validationResult.data;
      const serviceTimeData: ServiceScheduleTimeInput = timeData;

      const serviceTime = await ServiceScheduleService.addServiceTime(
        member.congregationId,
        scheduleId,
        serviceTimeData
      );

      return NextResponse.json({
        success: true,
        serviceTime,
        message: 'Service time added successfully',
      }, { status: 201 });

    } else {
      return NextResponse.json(
        { error: 'Invalid action specified' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Service schedule POST error:', error);

    return NextResponse.json(
      {
        error: 'Failed to process service schedule request',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/service-schedules
 * Update a service time
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    const member = authResult.user;

    // Check if user has permission to manage service schedules
    if (!['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to manage service schedules' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { timeId, ...updateData } = body;

    if (!timeId) {
      return NextResponse.json(
        { error: 'Service time ID is required' },
        { status: 400 }
      );
    }

    // Update service time
    const serviceTime = await ServiceScheduleService.updateServiceTime(
      member.congregationId,
      timeId,
      updateData
    );

    return NextResponse.json({
      success: true,
      serviceTime,
      message: 'Service time updated successfully',
    });

  } catch (error) {
    console.error('Service schedule PUT error:', error);

    return NextResponse.json(
      {
        error: 'Failed to update service time',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/service-schedules
 * Delete a service time
 */
export async function DELETE(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    const member = authResult.user;

    // Check if user has permission to manage service schedules
    if (!['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to manage service schedules' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { timeId } = body;

    if (!timeId) {
      return NextResponse.json(
        { error: 'Service time ID is required' },
        { status: 400 }
      );
    }

    // Delete service time
    await ServiceScheduleService.deleteServiceTime(
      member.congregationId,
      timeId
    );

    return NextResponse.json({
      success: true,
      message: 'Service time deleted successfully',
    });

  } catch (error) {
    console.error('Service schedule DELETE error:', error);

    return NextResponse.json(
      {
        error: 'Failed to delete service time',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
