@echo off
echo ========================================
echo PROPER DATABASE RESTORATION
echo ========================================
echo.

REM Set PostgreSQL password
set PGPASSWORD=password

REM Use the Laragon PostgreSQL installation
set PSQL_PATH="C:\laragon\bin\postgresql\pgsql\bin\psql.exe"

echo 🔧 Using PostgreSQL from: %PSQL_PATH%
echo 📋 Database: mywebsites@localhost:5432/hermanos
echo.

echo 🗑️  Step 1: Dropping existing database...
%PSQL_PATH% -h localhost -p 5432 -U mywebsites -d postgres -c "DROP DATABASE IF EXISTS hermanos;"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to drop database
    pause
    exit /b 1
)
echo ✅ Database dropped successfully
echo.

echo 🆕 Step 2: Creating new database...
%PSQL_PATH% -h localhost -p 5432 -U mywebsites -d postgres -c "CREATE DATABASE hermanos;"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to create database
    pause
    exit /b 1
)
echo ✅ Database created successfully
echo.

echo 📥 Step 3: Restoring from backup file...
echo This may take a few minutes...
%PSQL_PATH% -h localhost -p 5432 -U mywebsites -d hermanos -f "hermanos-07-25-25E.sql"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to restore from backup
    echo.
    echo 💡 This might be normal if there are warnings.
    echo    Check if data was actually restored.
    echo.
) else (
    echo ✅ Backup restored successfully
)
echo.

echo 🔍 Step 4: Verifying restoration...
%PSQL_PATH% -h localhost -p 5432 -U mywebsites -d hermanos -c "SELECT 'Congregations: ' || COUNT(*) FROM congregations;"
%PSQL_PATH% -h localhost -p 5432 -U mywebsites -d hermanos -c "SELECT 'Members: ' || COUNT(*) FROM members;"
%PSQL_PATH% -h localhost -p 5432 -U mywebsites -d hermanos -c "SELECT 'Songs: ' || COUNT(*) FROM songs;"
echo.

echo 🎉 RESTORATION COMPLETE!
echo.
echo 📋 NEXT STEPS:
echo    1. Check Prisma Studio at http://localhost:5555
echo    2. Verify member data is not corrupted
echo    3. Test authentication with original credentials
echo    4. Restart development server if needed
echo.
pause
