/**
 * Validation Rules API Endpoint
 * 
 * Handles CRUD operations for territory validation rules configuration.
 * Allows administrators to customize validation rules per congregation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { ValidationService } from '@/services/territories/ValidationService';
import {
  ValidationRule,
  VALIDATION_RULE_TEMPLATES,
  DEFAULT_TERRITORY_NUMBER_CONFIG,
  DEFAULT_ADDRESS_CONFIG,
  DEFAULT_DUPLICATE_CONFIG
} from '@/types/territories/validation';

// Validation schema for rule updates
const ValidationRuleUpdateSchema = z.object({
  rules: z.array(z.object({
    id: z.string(),
    name: z.string().min(1, 'Rule name is required'),
    type: z.enum(['format', 'uniqueness', 'duplicate', 'address', 'custom']),
    enabled: z.boolean(),
    severity: z.enum(['warning', 'error', 'critical']),
    description: z.string(),
    configuration: z.record(z.any())
  })).optional(),
  templateId: z.string().optional()
});

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for validation rules' },
        { status: 403 }
      );
    }

    // Get validation rules for congregation
    const rules = await ValidationService.loadValidationRules(user.congregationId);

    // Get available templates
    const templates = VALIDATION_RULE_TEMPLATES;

    // Get default configurations
    const defaultConfigurations = {
      territoryNumber: DEFAULT_TERRITORY_NUMBER_CONFIG,
      address: DEFAULT_ADDRESS_CONFIG,
      duplicate: DEFAULT_DUPLICATE_CONFIG
    };

    return NextResponse.json({
      success: true,
      rules,
      templates,
      defaultConfigurations,
      congregationId: user.congregationId
    });

  } catch (error) {
    console.error('Validation rules GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get validation rules',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for validation rules' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = ValidationRuleUpdateSchema.parse(body);

    console.log(`Updating validation rules for congregation ${user.congregationId}`);

    // Handle template application
    if (validatedData.templateId) {
      const template = VALIDATION_RULE_TEMPLATES.find(t => t.id === validatedData.templateId);
      if (!template) {
        return NextResponse.json(
          { error: 'Invalid template ID' },
          { status: 400 }
        );
      }

      // Apply template rules
      const templateRules: ValidationRule[] = template.rules.map((rule, index) => ({
        ...rule,
        id: `${template.id}-${index}`,
        congregationId: user.congregationId,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      // In a real implementation, this would save to database
      // For now, we'll just return the applied template
      console.log(`Applied template ${template.name} with ${templateRules.length} rules`);

      return NextResponse.json({
        success: true,
        message: `Applied template: ${template.name}`,
        rules: templateRules,
        appliedTemplate: template
      });
    }

    // Handle individual rule updates
    if (validatedData.rules) {
      // In a real implementation, this would update the database
      // For now, we'll validate and return the updated rules
      const updatedRules: ValidationRule[] = validatedData.rules.map(rule => ({
        ...rule,
        congregationId: user.congregationId,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      console.log(`Updated ${updatedRules.length} validation rules`);

      return NextResponse.json({
        success: true,
        message: `Updated ${updatedRules.length} validation rules`,
        rules: updatedRules
      });
    }

    return NextResponse.json(
      { error: 'No rules or template specified for update' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Validation rules PUT error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to update validation rules',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve rules or PUT to update rules.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT to disable rules instead of deleting them.' },
    { status: 405 }
  );
}
