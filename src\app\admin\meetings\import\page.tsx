'use client';

/**
 * Workbook Import Admin Page
 *
 * Administrative interface for importing midweek meeting data from JW.org workbook.
 * Includes date range selection, import configuration, and detailed progress tracking.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface ImportConfig {
  startDate: string;
  endDate: string;
  language: 'es' | 'en';
  overwriteExisting: boolean;
  retryAttempts: number;
  timeout: number;
}

interface ImportResult {
  success: boolean;
  message: string;
  imported: number;
  skipped: number;
  failed: number;
  errors: string[];
  warnings: string[];
  validation: any;
  songValidation: any;
  importDetails: any;
}

export default function WorkbookImportPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isImporting, setIsImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);

  const [config, setConfig] = useState<ImportConfig>({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    language: 'es',
    overwriteExisting: false,
    retryAttempts: 3,
    timeout: 30000,
  });

  useEffect(() => {
    checkAdminAccess();
  }, []);

  const checkAdminAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();

      if (!data.permissions?.canAccessAdmin) {
        router.push('/dashboard');
        return;
      }

      setUser(data.user);
    } catch (error) {
      console.error('Admin access check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImport = async () => {
    if (!user) return;

    setIsImporting(true);
    setImportResult(null);

    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/meetings/midweek/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(config),
      });

      const result = await response.json();
      setImportResult(result);

      if (result.success) {
        // Show success message
        alert(`Import completed successfully! Imported: ${result.imported}, Skipped: ${result.skipped}`);
      } else {
        // Show error message
        alert(`Import failed: ${result.errors?.join(', ') || 'Unknown error'}`);
      }

    } catch (error) {
      console.error('Import failed:', error);
      setImportResult({
        success: false,
        message: 'Import request failed',
        imported: 0,
        skipped: 0,
        failed: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        warnings: [],
        validation: null,
        songValidation: null,
        importDetails: null,
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleConfigChange = (field: keyof ImportConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verificando acceso...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div>
            <button
              onClick={() => router.push('/admin')}
              className="text-blue-200 hover:text-white mb-2 flex items-center"
            >
              ← Volver a Administración
            </button>
            <h1 className="text-2xl font-bold">Importar Programa de Reuniones</h1>
            <p className="text-blue-200">Importar datos del cuaderno de reuniones desde jw.org</p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        {/* Import Configuration */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Configuración de Importación</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fecha de Inicio
              </label>
              <input
                type="date"
                value={config.startDate}
                onChange={(e) => handleConfigChange('startDate', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fecha de Fin
              </label>
              <input
                type="date"
                value={config.endDate}
                onChange={(e) => handleConfigChange('endDate', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Idioma
              </label>
              <select
                value={config.language}
                onChange={(e) => handleConfigChange('language', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="es">Español</option>
                <option value="en">English</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reintentos
              </label>
              <input
                type="number"
                min="1"
                max="5"
                value={config.retryAttempts}
                onChange={(e) => handleConfigChange('retryAttempts', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.overwriteExisting}
                onChange={(e) => handleConfigChange('overwriteExisting', e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">
                Sobrescribir reuniones existentes
              </span>
            </label>
          </div>

          <div className="mt-6">
            <button
              onClick={handleImport}
              disabled={isImporting}
              className={`w-full py-3 px-4 rounded-md font-medium ${
                isImporting
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              } text-white transition-colors`}
            >
              {isImporting ? 'Importando...' : 'Iniciar Importación'}
            </button>
          </div>
        </div>

        {/* Import Results */}
        {importResult && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Resultados de Importación</h2>

            <div className={`p-4 rounded-md mb-4 ${
              importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              <p className={`font-medium ${
                importResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {importResult.message}
              </p>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center p-3 bg-green-50 rounded-md">
                <div className="text-2xl font-bold text-green-600">{importResult.imported}</div>
                <div className="text-sm text-green-700">Importadas</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-md">
                <div className="text-2xl font-bold text-yellow-600">{importResult.skipped}</div>
                <div className="text-sm text-yellow-700">Omitidas</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-md">
                <div className="text-2xl font-bold text-red-600">{importResult.failed}</div>
                <div className="text-sm text-red-700">Fallidas</div>
              </div>
            </div>

            {/* Errors */}
            {importResult.errors && importResult.errors.length > 0 && (
              <div className="mb-4">
                <h3 className="font-medium text-red-800 mb-2">Errores:</h3>
                <ul className="list-disc list-inside text-sm text-red-700 space-y-1">
                  {importResult.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Warnings */}
            {importResult.warnings && importResult.warnings.length > 0 && (
              <div className="mb-4">
                <h3 className="font-medium text-yellow-800 mb-2">Advertencias:</h3>
                <ul className="list-disc list-inside text-sm text-yellow-700 space-y-1">
                  {importResult.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Song Validation Results */}
            {importResult.songValidation && (
              <div className="mt-4 p-4 bg-gray-50 rounded-md">
                <h3 className="font-medium text-gray-800 mb-2">Validación de Canciones:</h3>
                <div className="text-sm text-gray-700">
                  <p>Total: {importResult.songValidation.totalSongs}</p>
                  <p>Válidas: {importResult.songValidation.validSongs}</p>
                  <p>Faltantes: {importResult.songValidation.missingSongs}</p>
                  {importResult.songValidation.missingSongsReport && (
                    <details className="mt-2">
                      <summary className="cursor-pointer font-medium">Ver reporte de canciones faltantes</summary>
                      <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto">
                        {importResult.songValidation.missingSongsReport}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
