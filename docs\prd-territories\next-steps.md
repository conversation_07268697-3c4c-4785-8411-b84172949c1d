# Next Steps

## UX Expert Prompt

Review the Territories Management PRD and create detailed UI/UX specifications with two distinct interface requirements:

1. **Admin Interface**: Design dedicated "Territorios" admin card and comprehensive territory management dashboard separate from Field Service admin
2. **Member Interface**: Create pixel-perfect alignment with existing Field Service UI patterns (reference screenshots 1.jpg through 8.jpg in \CORAL OESTE APP\Field Service\). The member territory interface must follow the exact visual hierarchy, card layouts, navigation patterns, mobile optimization, and interaction paradigms established in the Field Service section.

Focus on territory visualization patterns, mobile-first design for field service use, and seamless integration with the existing Hermanos App design system while maintaining strict consistency with Field Service UI patterns for member-facing features.

## Architect Prompt

Design the technical architecture for the Territories Management system based on this PRD. Focus on integrating with the existing Next.js/PostgreSQL/Prisma stack, MapLibre integration approach, Excel import processing architecture, and territory data modeling. Address the technical assumptions and provide detailed implementation guidance for the development team.
