#!/usr/bin/env node

/**
 * Geocode Territory 001 Addresses
 *
 * This script gets real coordinates for Territory 001 addresses using
 * node-geocoder with multiple providers for accuracy and reliability.
 */

const NodeGeocoder = require('node-geocoder');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Configure geocoder with OpenStreetMap (free, no API key required)
const geocoder = NodeGeocoder({
  provider: 'openstreetmap',
  httpAdapter: 'https',
  formatter: null
});

// Delay between requests to be respectful to the geocoding service
const DELAY_MS = 1000;

/**
 * Geocode an address using node-geocoder
 */
async function geocodeAddress(address) {
  try {
    const results = await geocoder.geocode(address);

    if (results && results.length > 0) {
      const result = results[0];
      return {
        address: address,
        latitude: result.latitude,
        longitude: result.longitude,
        formattedAddress: result.formattedAddress,
        country: result.country,
        state: result.state,
        city: result.city,
        zipcode: result.zipcode,
        streetName: result.streetName,
        streetNumber: result.streetNumber,
        success: true
      };
    } else {
      return {
        address: address,
        success: false,
        error: 'No results found'
      };
    }
  } catch (error) {
    return {
      address: address,
      success: false,
      error: error.message
    };
  }
}

/**
 * Delay function
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Get Territory 001 addresses and geocode them
 */
async function geocodeTerritory001() {
  try {
    console.log('🗺️  Geocoding Territory 001 Addresses');
    console.log('=====================================\n');

    // Get Territory 001 addresses
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        address: true
      }
    });

    if (!territory) {
      console.log('❌ Territory 001 not found');
      return;
    }

    const addresses = territory.address.split('\n').filter(addr => addr.trim());
    console.log(`📍 Found ${addresses.length} addresses to geocode\n`);

    const coordinates = [];
    const failed = [];

    // Geocode each address
    for (let i = 0; i < addresses.length; i++) {
      const address = addresses[i].trim();
      console.log(`${i + 1}/${addresses.length}: ${address}`);

      const result = await geocodeAddress(address);

      if (result.success) {
        coordinates.push({
          address: result.address,
          latitude: result.latitude,
          longitude: result.longitude,
          formattedAddress: result.formattedAddress,
          streetName: result.streetName,
          streetNumber: result.streetNumber
        });
        console.log(`  ✅ ${result.latitude}, ${result.longitude}`);
        console.log(`     ${result.formattedAddress}`);
      } else {
        failed.push({
          address: result.address,
          error: result.error
        });
        console.log(`  ❌ ${result.error}`);
      }

      // Delay between requests
      if (i < addresses.length - 1) {
        await delay(DELAY_MS);
      }
    }

    console.log('\n📊 Geocoding Results:');
    console.log('=====================================');
    console.log(`✅ Successfully geocoded: ${coordinates.length}`);
    console.log(`❌ Failed to geocode: ${failed.length}`);

    if (coordinates.length > 0) {
      // Calculate bounding box
      const lats = coordinates.map(c => c.latitude);
      const lngs = coordinates.map(c => c.longitude);

      const bounds = {
        north: Math.max(...lats),
        south: Math.min(...lats),
        east: Math.max(...lngs),
        west: Math.min(...lngs)
      };

      console.log('\n📍 Territory 001 Bounding Box:');
      console.log('=====================================');
      console.log(`North: ${bounds.north}`);
      console.log(`South: ${bounds.south}`);
      console.log(`East: ${bounds.east}`);
      console.log(`West: ${bounds.west}`);

      // Save coordinates to file for analysis
      const fs = require('fs');
      const outputData = {
        territory: '001',
        geocoded: new Date().toISOString(),
        total_addresses: addresses.length,
        successful_geocodes: coordinates.length,
        failed_geocodes: failed.length,
        bounds: bounds,
        coordinates: coordinates,
        failed: failed
      };

      fs.writeFileSync('territory-001-coordinates.json', JSON.stringify(outputData, null, 2));
      console.log('\n💾 Coordinates saved to: territory-001-coordinates.json');

      console.log('\n🗺️  Next Steps:');
      console.log('1. Review the coordinates in the JSON file');
      console.log('2. Use the bounding box to create accurate territory boundaries');
      console.log('3. Consider street patterns and actual territory assignments');
      console.log('4. Create GeoJSON polygon based on real geographic boundaries');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the geocoding
if (require.main === module) {
  geocodeTerritory001().catch(console.error);
}

module.exports = { geocodeTerritory001 };
