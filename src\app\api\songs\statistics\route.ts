/**
 * Song Statistics API Endpoint
 *
 * Provides statistics about the song catalog including:
 * - Total songs count
 * - Songs by language
 * - Songs by category
 * - Active/inactive songs
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/songs/statistics
 * Get comprehensive song catalog statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get comprehensive statistics
    const [
      totalSongs,
      activeSongs,
      inactiveSongs,
      songsWithSpanishTitles,
      songsWithEnglishTitles,
      songsWithBothLanguages,
      categories,
      specialSongs,
    ] = await Promise.all([
      // Total songs count
      prisma.song.count(),

      // Active songs count
      prisma.song.count({ where: { isActive: true } }),

      // Inactive songs count
      prisma.song.count({ where: { isActive: false } }),

      // Songs with Spanish titles
      prisma.song.count({
        where: {
          titleEs: { not: null },
          titleEs: { not: '' }
        }
      }),

      // Songs with English titles
      prisma.song.count({
        where: {
          titleEn: { not: null },
          titleEn: { not: '' }
        }
      }),

      // Songs with both languages
      prisma.song.count({
        where: {
          AND: [
            { titleEs: { not: null } },
            { titleEs: { not: '' } },
            { titleEn: { not: null } },
            { titleEn: { not: '' } }
          ]
        }
      }),

      // Songs by category
      prisma.song.groupBy({
        by: ['category'],
        _count: { category: true },
        where: {
          category: { not: null },
          isActive: true
        },
        orderBy: { _count: { category: 'desc' } },
      }),

      // Special songs count
      prisma.specialSong.count({ where: { isActive: true } }),
    ]);

    // Calculate missing translations
    const missingSpanishTitles = totalSongs - songsWithSpanishTitles;
    const missingEnglishTitles = totalSongs - songsWithEnglishTitles;
    const completionRate = totalSongs > 0 ? (songsWithBothLanguages / totalSongs) * 100 : 0;

    // Format categories data
    const categoryStats = categories.map(cat => ({
      category: cat.category || 'Uncategorized',
      count: cat._count.category,
    }));

    // Get recent updates (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentUpdates = await prisma.song.count({
      where: {
        updatedAt: { gte: thirtyDaysAgo },
      },
    });

    return NextResponse.json({
      overview: {
        totalSongs,
        activeSongs,
        inactiveSongs,
        specialSongs,
        recentUpdates,
      },
      languages: {
        songsWithSpanishTitles,
        songsWithEnglishTitles,
        songsWithBothLanguages,
        missingSpanishTitles,
        missingEnglishTitles,
        completionRate: Math.round(completionRate * 100) / 100,
      },
      categories: categoryStats,
      lastUpdated: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error fetching song statistics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
