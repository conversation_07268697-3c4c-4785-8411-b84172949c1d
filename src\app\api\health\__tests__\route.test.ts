import { GET } from '../route'
import { NextRequest } from 'next/server'

// Mock the database connection
jest.mock('@/lib/db-test', () => ({
  testDatabaseConnection: jest.fn().mockResolvedValue({
    success: true,
    message: 'Database connection successful',
    latency: 10
  })
}))

// Mock environment validation
jest.mock('@/lib/env-validation', () => ({
  validateEnvironmentVariables: jest.fn().mockReturnValue({
    NODE_ENV: 'test',
    NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
    DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
    JWT_SECRET: 'test-secret-key-for-testing-purposes'
  }),
  getEnv: jest.fn().mockReturnValue({
    NODE_ENV: 'test',
    NEXT_PUBLIC_APP_URL: 'http://localhost:3000'
  })
}))

describe('/api/health', () => {
  it('should return health status', async () => {
    const request = new NextRequest('http://localhost:3000/api/health')
    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toHaveProperty('status')
    expect(data).toHaveProperty('timestamp')
    expect(data).toHaveProperty('services')
    expect(data).toHaveProperty('environment')
    expect(data).toHaveProperty('system')
    expect(data.status).toBe('healthy')
  })

  it('should include environment information', async () => {
    const request = new NextRequest('http://localhost:3000/api/health')
    const response = await GET(request)
    const data = await response.json()

    expect(data.services.environment).toHaveProperty('status')
    expect(data.services.environment.status).toBe('healthy')
  })

  it('should include database status', async () => {
    const request = new NextRequest('http://localhost:3000/api/health')
    const response = await GET(request)
    const data = await response.json()

    expect(data.services.database).toHaveProperty('status')
    expect(data.services.database.status).toBe('healthy')
  })
})
