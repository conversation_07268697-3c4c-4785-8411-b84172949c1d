# Story 6.6: Field Service Management Enhancement

## Story Overview

**Title:** Field Service Management Enhancement
**Epic:** Epic 6 - Language and Localization
**Story Points:** 13
**Priority:** High
**Status:** In Progress

## Story Description

Enhance the existing Field Service Management system to include comprehensive service scheduling, service groups, territories, and service records management. The system should support both admin coordination and member participation with a focus on the Spanish-first interface design shown in the reference screenshots.

## Business Value

- **Service Coordinators** can efficiently manage service schedules, groups, and territories
- **Publishers** can easily view service schedules, submit time reports, and track their service activity
- **Congregation** benefits from organized service coordination and better territory coverage
- **Elders** can monitor congregation service activity and generate reports for the branch office

## Acceptance Criteria

### Admin Side - Service Coordination

**AC1: Service Schedule Management**
- [ ] Ad<PERSON> can create and manage weekly service schedules
- [ ] Support for multiple service times (morning, afternoon, evening)
- [ ] Ability to set meeting points and conductors for each service time
- [ ] Calendar view showing all scheduled service activities
- [ ] Integration with existing member database for conductor assignments

**AC2: Service Groups Management**
- [ ] Create and manage service groups with group numbers
- [ ] Assign group overseers and assistants
- [ ] Manage group member assignments
- [ ] Track group performance and activity
- [ ] Support for group-specific service arrangements

**AC3: Territory Management**
- [ ] Create and manage territory records
- [ ] Track territory assignments and completion status
- [ ] Monitor territory coverage and work progress
- [ ] Generate territory assignment reports
- [ ] Support for territory sharing between groups

**AC4: Service Statistics Dashboard**
- [ ] Enhanced statistics view with group-level breakdowns
- [ ] Territory coverage analytics
- [ ] Service participation tracking
- [ ] Monthly and yearly service reports
- [ ] Export capabilities for branch office reporting

### Member Side - Service Participation

**AC5: Service Schedule View**
- [ ] Weekly service schedule display matching screenshot design
- [ ] Expandable date sections showing service times
- [ ] Meeting point and conductor information
- [ ] Easy navigation between weeks
- [ ] Integration with personal calendar

**AC6: Enhanced Service Records**
- [ ] Improved service record entry interface
- [ ] Territory-specific activity tracking
- [ ] Service group participation logging
- [ ] Photo upload for service documentation
- [ ] Offline capability for field use

**AC7: Personal Service Dashboard**
- [ ] Personal service statistics and trends
- [ ] Goal tracking and progress monitoring
- [ ] Service history with detailed breakdowns
- [ ] Achievement badges and milestones
- [ ] Comparison with congregation averages

## Technical Requirements

### Database Schema Enhancements

**New Tables:**
- `service_schedules` - Weekly service schedule management
- `service_schedule_times` - Individual service time slots
- `group_assignments` - Member assignments to service groups
- `territory_assignments` - Territory assignment tracking
- `service_activities` - Detailed service activity logging

**Enhanced Tables:**
- `service_groups` - Add overseer assignments and group settings
- `territories` - Add detailed territory information and boundaries
- `field_service_records` - Add territory and group references

### API Endpoints

**Admin Endpoints:**
- `GET/POST /api/admin/service-schedules` - Service schedule management
- `GET/POST/PUT /api/admin/service-groups` - Service group management
- `GET/POST/PUT /api/admin/territories` - Territory management
- `GET /api/admin/service-statistics` - Enhanced statistics

**Member Endpoints:**
- `GET /api/service-schedule` - Weekly service schedule
- `GET/POST/PUT /api/service-records` - Enhanced service records
- `GET /api/service-dashboard` - Personal service dashboard

### UI Components

**Admin Components:**
- `ServiceScheduleManager` - Weekly schedule creation and management
- `ServiceGroupManager` - Group creation and member assignment
- `TerritoryManager` - Territory management interface
- `ServiceStatsDashboard` - Enhanced statistics display

**Member Components:**
- `ServiceScheduleView` - Weekly schedule display
- `ServiceRecordForm` - Enhanced record entry
- `ServiceDashboard` - Personal statistics and goals

## Tasks

### [x] Task 1: Database Schema Enhancement
**Estimated Time:** 4 hours
- [x] Create new database tables for service schedules and enhanced tracking
- [x] Add foreign key relationships and indexes
- [x] Create migration scripts for existing data
- [x] Update Prisma schema with new models

### [ ] Task 2: Service Schedule Management (Admin)
**Estimated Time:** 6 hours
- [ ] Implement service schedule creation and management API
- [ ] Create admin interface for schedule management
- [ ] Add conductor assignment functionality
- [ ] Implement calendar view for service schedules

### [ ] Task 3: Service Groups Enhancement
**Estimated Time:** 5 hours
- [ ] Enhance service groups with overseer assignments
- [ ] Create group member management interface
- [ ] Implement group performance tracking
- [ ] Add group-specific service arrangements

### [ ] Task 4: Territory Management System
**Estimated Time:** 6 hours
- [ ] Implement territory creation and management
- [ ] Create territory assignment tracking
- [ ] Add territory coverage monitoring
- [ ] Implement territory sharing functionality

### [x] Task 5: Member Service Schedule View
**Estimated Time:** 4 hours
- [x] Create service schedule display component
- [x] Implement expandable date sections
- [x] Add meeting point and conductor information
- [x] Integrate with personal calendar

### [ ] Task 6: Enhanced Service Records
**Estimated Time:** 5 hours
- [ ] Enhance service record entry interface
- [ ] Add territory and group tracking
- [ ] Implement photo upload functionality
- [ ] Add offline capability support

### [ ] Task 7: Service Statistics Enhancement
**Estimated Time:** 4 hours
- [ ] Enhance admin statistics dashboard
- [ ] Add group-level analytics
- [ ] Implement territory coverage reports
- [ ] Create export functionality

### [ ] Task 8: Personal Service Dashboard
**Estimated Time:** 3 hours
- [ ] Create personal service statistics view
- [ ] Implement goal tracking
- [ ] Add achievement system
- [ ] Create trend analysis

### [ ] Task 9: Translation Integration
**Estimated Time:** 2 hours
- [ ] Add Spanish translations for all new components
- [ ] Integrate with existing language system
- [ ] Test language switching functionality
- [ ] Update translation files

### [ ] Task 10: Testing and Documentation
**Estimated Time:** 3 hours
- [ ] Create comprehensive test suite
- [ ] Test admin and member workflows
- [ ] Update API documentation
- [ ] Create user guides

## Definition of Done

- [ ] All acceptance criteria are met and tested
- [ ] Admin can manage service schedules, groups, and territories
- [ ] Members can view schedules and submit enhanced service records
- [ ] Service statistics dashboard shows comprehensive data
- [ ] All components support Spanish/English language switching
- [ ] Database migrations are tested and documented
- [ ] API endpoints are documented and tested
- [ ] User interface matches reference screenshot designs
- [ ] Performance testing shows acceptable load times
- [ ] Security testing confirms proper access controls

## Dependencies

- **Story 6.5** - Language and Localization (for translation support)
- **Story 2.2** - Member Management (for member assignments)
- **Existing Field Service System** - Current service records functionality

## Risks and Mitigation

**Risk:** Complex territory management requirements
**Mitigation:** Start with basic territory tracking and enhance iteratively

**Risk:** Performance issues with large datasets
**Mitigation:** Implement pagination and caching for statistics

**Risk:** Mobile usability for field service
**Mitigation:** Prioritize mobile-first design and offline capabilities

## Implementation Details

### Reference Screenshots Analysis

**Service Schedule View (Servicio-del-Campo.png):**
- Purple header with "Servicio del Campo" title and back arrow
- Date range display (18-24 noviembre de 2024)
- Expandable date sections with chevron icons
- Service times with location and conductor information
- Clean, mobile-first design with good spacing

**Admin Field Service View (Field-Service-Management.png):**
- Green header with "Field Service Management" title
- Tab navigation: Service Schedule, Service Groups, Territories, Service Records
- Table layout with Date, Time, Location, Address, Conductor columns
- Action buttons for Edit and Delete
- Add Service Time button

### Database Schema Details

```sql
-- Service schedules table
CREATE TABLE service_schedules (
    id TEXT PRIMARY KEY,
    congregation_id VARCHAR(8) NOT NULL,
    week_start_date DATE NOT NULL,
    week_end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Service schedule times table
CREATE TABLE service_schedule_times (
    id TEXT PRIMARY KEY,
    schedule_id TEXT NOT NULL,
    service_date DATE NOT NULL,
    service_time TIME NOT NULL,
    location VARCHAR(255) NOT NULL,
    address TEXT,
    conductor_id TEXT,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enhanced group assignments
CREATE TABLE group_assignments (
    id TEXT PRIMARY KEY,
    group_id TEXT NOT NULL,
    member_id TEXT NOT NULL,
    role VARCHAR(50) DEFAULT 'member', -- 'overseer', 'assistant', 'member'
    assigned_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);
```

### API Design Patterns

**RESTful Endpoints:**
- Follow existing pattern: `/api/admin/[feature]` for admin endpoints
- Member endpoints: `/api/[feature]` for member access
- Use proper HTTP methods: GET (read), POST (create), PUT (update), DELETE (remove)
- Consistent response format with success/error handling

**Authentication & Authorization:**
- Admin endpoints require elder/ministerial_servant role
- Member endpoints require valid congregation membership
- Service coordinators get additional permissions
- Proper congregation isolation for all data

## Notes

- Reference screenshots show Spanish-first design with expandable date sections
- Existing field service records system provides foundation for enhancement
- Service groups and territories tables already exist in database schema
- Focus on practical field service coordination needs
- Consider integration with JW.org service year calendar
- Mobile-first design approach for field service usage
- Offline capability important for territory work

## Dev Agent Record

### Agent Model Used
- Claude Sonnet 4

### Debug Log References
- Initial analysis of existing field service implementation
- Database schema review for service groups and territories
- Screenshot analysis for UI design requirements

### Completion Notes
- Story created based on existing field service foundation
- Database schema enhanced with service schedules and group assignments
- Service schedule management API endpoints implemented
- Member service schedule view created with expandable date sections
- Admin field service page enhanced with schedule management tab
- Integration with existing language and member management systems

### File List
- docs/stories/6.6.story.md (created)
- prisma/schema.prisma (enhanced with new models)
- src/lib/services/serviceScheduleService.ts (created)
- src/app/api/admin/service-schedules/route.ts (created)
- src/app/api/admin/service-schedules/conductors/route.ts (created)
- src/app/api/service-schedule/route.ts (created)
- src/app/service-schedule/page.tsx (created)
- src/app/admin/field-service/page.tsx (enhanced with schedule tab)

### Change Log
- 2025-01-25: Created comprehensive field service enhancement story
- 2025-01-25: Analyzed existing implementation and database schema
- 2025-01-25: Defined tasks and acceptance criteria based on screenshots
- 2025-01-25: Enhanced database schema with service schedules and group assignments
- 2025-01-25: Implemented service schedule management API endpoints
- 2025-01-25: Created member service schedule view with expandable design
- 2025-01-25: Enhanced admin field service page with schedule management tab
