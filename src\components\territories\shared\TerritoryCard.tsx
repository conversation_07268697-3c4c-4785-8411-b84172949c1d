'use client';

/**
 * Territory Card Component
 *
 * Displays territory information in a card format with status indicators.
 * Shows territory number, address, status, and assigned member information.
 */

import React from 'react';
import { Territory, TerritoryStatus } from '@/types/territories/territory';

interface TerritoryCardProps {
  territory: Territory & {
    assignedMember?: {
      id: string;
      name: string;
      assignedAt: Date;
      assignedBy: string;
    } | null;
  };
  onClick?: (territory: Territory) => void;
  className?: string;
}

const statusConfig: Record<TerritoryStatus, {
  label: string;
  color: string;
  bgColor: string;
  icon: string;
}> = {
  available: {
    label: 'Disponible',
    color: 'text-green-700',
    bgColor: 'bg-green-100',
    icon: '✓'
  },
  assigned: {
    label: 'Asignado',
    color: 'text-blue-700',
    bgColor: 'bg-blue-100',
    icon: '👤'
  },
  completed: {
    label: 'Completado',
    color: 'text-purple-700',
    bgColor: 'bg-purple-100',
    icon: '✅'
  },
  out_of_service: {
    label: 'Fuera de Servicio',
    color: 'text-red-700',
    bgColor: 'bg-red-100',
    icon: '⚠️'
  }
};

export default function TerritoryCard({ territory, onClick, className = '' }: TerritoryCardProps) {
  const statusInfo = statusConfig[territory.status];

  const handleClick = () => {
    if (onClick) {
      onClick(territory);
    }
  };

  const formatDate = (date: Date | string) => {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div
      className={`
        bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md
        transition-all duration-200 cursor-pointer hover:border-gray-300
        ${className}
      `}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label={`Territorio ${territory.territoryNumber} - ${territory.address}`}
    >
      {/* Header with Territory Number and Status */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-semibold text-gray-700">
                  {territory.territoryNumber}
                </span>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Territorio {territory.territoryNumber}
              </h3>
            </div>
          </div>

          {/* Status Badge */}
          <div className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${statusInfo.color} ${statusInfo.bgColor}
          `}>
            <span className="mr-1">{statusInfo.icon}</span>
            {statusInfo.label}
          </div>
        </div>
      </div>

      {/* Territory Details */}
      <div className="p-4">
        {/* Addresses - One per row */}
        <div className="mb-3">
          <div className="flex items-start space-x-2 mb-2">
            <svg className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-700 mb-1">Direcciones:</div>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {territory.address.split('\n').map((addr, index) => {
                  const trimmedAddr = addr.trim();
                  if (!trimmedAddr) return null;

                  return (
                    <div key={index} className="text-xs text-gray-600 py-1 px-2 bg-gray-50 rounded border-l-2 border-gray-200">
                      {trimmedAddr}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Assigned Member Information */}
        {territory.assignedMember && (
          <div className="mb-3">
            <div className="flex items-start space-x-2">
              <svg className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <div className="text-sm">
                <p className="text-gray-700 font-medium">
                  {territory.assignedMember.name}
                </p>
                <p className="text-gray-500 text-xs">
                  Asignado el {formatDate(territory.assignedMember.assignedAt)}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Notes */}
        {territory.notes && (
          <div className="mb-3">
            <div className="flex items-start space-x-2">
              <svg className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-sm text-gray-600 leading-relaxed">
                {territory.notes}
              </p>
            </div>
          </div>
        )}

        {/* Last Updated */}
        <div className="pt-2 border-t border-gray-100">
          <p className="text-xs text-gray-500">
            Actualizado el {formatDate(territory.updatedAt)}
          </p>
        </div>
      </div>
    </div>
  );
}
