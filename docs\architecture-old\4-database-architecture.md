# 4. Database Architecture

## Database Migration Strategy

The database architecture transformation from MySQL to PostgreSQL requires careful planning to ensure zero data loss while enabling multi-congregation scalability. The migration strategy preserves all existing functionality while establishing the foundation for multi-tenant operations.

**Migration Approach:**

- **Schema-First Migration**: Convert all 41 MySQL tables to PostgreSQL with enhanced constraints and indexing
- **Data Preservation**: Ensure 100% data integrity during the migration process
- **Multi-Tenant Preparation**: Add congregation isolation to all tables
- **Performance Optimization**: Implement proper indexing and query optimization for congregation-scale operations
- **Backward Compatibility**: Maintain all existing relationships and data structures

## Core Database Schema

### Congregation Management Tables

```sql
-- Congregations table - Central tenant management
CREATE TABLE congregations (
    id VARCHAR(8) PRIMARY KEY,                    -- Short, memorable congregation ID
    name VARCHAR(255) NOT NULL,                   -- Congregation name
    region VARCHAR(50),                           -- Geographic region
    pin VARCHAR(255) NOT NULL,                    -- bcrypt hashed PIN
    language VARCHAR(5) DEFAULT 'es',             -- Default language (es/en)
    timezone VARCHAR(50) DEFAULT 'America/Mexico_City',
    settings JSONB DEFAULT '{}',                  -- Flexible congregation settings
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for congregation management
CREATE INDEX idx_congregations_region ON congregations(region);
CREATE INDEX idx_congregations_active ON congregations(is_active);

-- Members table - User accounts with congregation isolation
CREATE TABLE members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    role VARCHAR(50) NOT NULL DEFAULT 'publisher',
    pin VARCHAR(255) NOT NULL,                    -- bcrypt hashed PIN
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for member management
CREATE INDEX idx_members_congregation ON members(congregation_id);
CREATE INDEX idx_members_role ON members(congregation_id, role);
CREATE INDEX idx_members_active ON members(congregation_id, is_active);
CREATE UNIQUE INDEX idx_members_email_congregation ON members(congregation_id, email) WHERE email IS NOT NULL;

-- Roles table - Role definitions
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Elder permissions table - Granular permission control
CREATE TABLE elder_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    permission_type VARCHAR(100) NOT NULL,
    granted_by UUID REFERENCES members(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Indexes for permission management
CREATE INDEX idx_elder_permissions_member ON elder_permissions(member_id);
CREATE INDEX idx_elder_permissions_congregation ON elder_permissions(congregation_id);
CREATE UNIQUE INDEX idx_elder_permissions_unique ON elder_permissions(congregation_id, member_id, permission_type) WHERE is_active = true;
```

### Meeting Management Schema

```sql
-- Midweek meetings table
CREATE TABLE midweek_meetings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    meeting_date DATE NOT NULL,
    theme VARCHAR(255),
    chairman_id UUID REFERENCES members(id),
    location VARCHAR(100) DEFAULT 'Kingdom Hall',
    zoom_link TEXT,
    notes TEXT,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Weekend meetings table
CREATE TABLE weekend_meetings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    meeting_date DATE NOT NULL,
    public_talk_theme VARCHAR(255),
    public_talk_speaker_id UUID REFERENCES members(id),
    visiting_speaker_name VARCHAR(255),
    visiting_speaker_congregation VARCHAR(255),
    watchtower_conductor_id UUID REFERENCES members(id),
    watchtower_reader_id UUID REFERENCES members(id),
    location VARCHAR(100) DEFAULT 'Kingdom Hall',
    zoom_link TEXT,
    notes TEXT,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meeting parts for midweek meetings
CREATE TABLE midweek_meeting_parts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    meeting_id UUID NOT NULL REFERENCES midweek_meetings(id) ON DELETE CASCADE,
    part_type VARCHAR(50) NOT NULL,              -- 'song', 'prayer', 'treasures', 'ministry', 'living'
    title VARCHAR(255) NOT NULL,
    duration_minutes INTEGER,
    assigned_member_id UUID REFERENCES members(id),
    assistant_id UUID REFERENCES members(id),
    display_order INTEGER NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meeting parts for weekend meetings
CREATE TABLE weekend_meeting_parts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    meeting_id UUID NOT NULL REFERENCES weekend_meetings(id) ON DELETE CASCADE,
    part_type VARCHAR(50) NOT NULL,              -- 'song', 'prayer', 'public_talk', 'watchtower'
    title VARCHAR(255) NOT NULL,
    assigned_member_id UUID REFERENCES members(id),
    display_order INTEGER NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for meeting management
CREATE INDEX idx_midweek_meetings_congregation_date ON midweek_meetings(congregation_id, meeting_date);
CREATE INDEX idx_weekend_meetings_congregation_date ON weekend_meetings(congregation_id, meeting_date);
CREATE INDEX idx_midweek_parts_meeting ON midweek_meeting_parts(meeting_id, display_order);
CREATE INDEX idx_weekend_parts_meeting ON weekend_meeting_parts(meeting_id, display_order);
```

### Task and Assignment Management

```sql
-- Tasks table - Task definitions
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    estimated_duration_minutes INTEGER,
    requires_elder BOOLEAN DEFAULT false,
    requires_ministerial_servant BOOLEAN DEFAULT false,
    service_group_specific BOOLEAN DEFAULT false,
    is_recurring BOOLEAN DEFAULT false,
    recurrence_pattern JSONB,                    -- Flexible recurrence definition
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES members(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Task assignments table
CREATE TABLE task_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    assigned_member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    assigned_date DATE NOT NULL,
    due_date DATE,
    status VARCHAR(50) DEFAULT 'assigned',        -- 'assigned', 'in_progress', 'completed', 'cancelled'
    completion_date DATE,
    notes TEXT,
    assigned_by UUID REFERENCES members(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Field service records table
CREATE TABLE field_service_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    service_month DATE NOT NULL,                  -- First day of the service month
    hours DECIMAL(4,1) DEFAULT 0,
    placements INTEGER DEFAULT 0,
    video_showings INTEGER DEFAULT 0,
    return_visits INTEGER DEFAULT 0,
    bible_studies INTEGER DEFAULT 0,
    notes TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for task and service management
CREATE INDEX idx_tasks_congregation ON tasks(congregation_id);
CREATE INDEX idx_task_assignments_congregation_date ON task_assignments(congregation_id, assigned_date);
CREATE INDEX idx_task_assignments_member ON task_assignments(assigned_member_id);
CREATE INDEX idx_field_service_congregation_month ON field_service_records(congregation_id, service_month);
CREATE UNIQUE INDEX idx_field_service_member_month ON field_service_records(member_id, service_month);
```

### Communication and Document Management

```sql
-- Letters table - Document management
CREATE TABLE letters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    category VARCHAR(100),
    visibility VARCHAR(50) DEFAULT 'ALL_MEMBERS',  -- 'ALL_MEMBERS', 'ELDERS_ONLY', 'MINISTERIAL_SERVANTS_PLUS'
    upload_date DATE DEFAULT CURRENT_DATE,
    uploaded_by UUID REFERENCES members(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Events table - Congregation events
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    location VARCHAR(255),
    event_type VARCHAR(100),                      -- 'assembly', 'convention', 'special_meeting', 'service'
    is_mandatory BOOLEAN DEFAULT false,
    created_by UUID REFERENCES members(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Songs table - Multi-language song catalog
CREATE TABLE songs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    song_number INTEGER NOT NULL,
    title_spanish VARCHAR(255),
    title_english VARCHAR(255),
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for communication and documents
CREATE INDEX idx_letters_congregation ON letters(congregation_id);
CREATE INDEX idx_letters_visibility ON letters(congregation_id, visibility);
CREATE INDEX idx_events_congregation_date ON events(congregation_id, event_date);
CREATE UNIQUE INDEX idx_songs_number ON songs(song_number);
```

## Data Migration Scripts

### MySQL to PostgreSQL Migration

```javascript
// scripts/migrate-mysql-to-postgresql.js
const mysql = require('mysql2/promise');
const { Pool } = require('pg');

class DatabaseMigrator {
  constructor() {
    this.mysqlConnection = mysql.createConnection({
      host: process.env.MYSQL_HOST,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE,
    });

    this.pgPool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });
  }

  async migrateCongregations() {
    console.log('Migrating congregations...');

    const [mysqlRows] = await this.mysqlConnection.execute('SELECT * FROM congregations');

    for (const row of mysqlRows) {
      await this.pgPool.query(
        `
        INSERT INTO congregations (id, name, region, pin, language, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (id) DO UPDATE SET
          name = EXCLUDED.name,
          region = EXCLUDED.region,
          updated_at = NOW()
      `,
        [
          row.id,
          row.name,
          row.region,
          row.pin, // Already hashed in MySQL
          row.language || 'es',
          row.created_at,
          row.updated_at,
        ]
      );
    }

    console.log(`Migrated ${mysqlRows.length} congregations`);
  }

  async migrateMembers() {
    console.log('Migrating members...');

    const [mysqlRows] = await this.mysqlConnection.execute(`
      SELECT m.*, c.id as congregation_id 
      FROM members m 
      JOIN congregations c ON m.congregation_id = c.id
    `);

    for (const row of mysqlRows) {
      await this.pgPool.query(
        `
        INSERT INTO members (id, congregation_id, name, email, role, pin, is_active, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (id) DO UPDATE SET
          name = EXCLUDED.name,
          email = EXCLUDED.email,
          role = EXCLUDED.role,
          updated_at = NOW()
      `,
        [
          row.id,
          row.congregation_id,
          row.name,
          row.email,
          row.role,
          row.pin, // Already hashed in MySQL
          row.is_active,
          row.created_at,
          row.updated_at,
        ]
      );
    }

    console.log(`Migrated ${mysqlRows.length} members`);
  }

  async migrateMeetings() {
    console.log('Migrating meetings...');

    // Migrate midweek meetings
    const [midweekRows] = await this.mysqlConnection.execute(`
      SELECT m.*, c.id as congregation_id 
      FROM midweek_meetings m 
      JOIN congregations c ON m.congregation_id = c.id
    `);

    for (const row of midweekRows) {
      await this.pgPool.query(
        `
        INSERT INTO midweek_meetings (id, congregation_id, meeting_date, theme, chairman_id, location, notes, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (id) DO UPDATE SET
          theme = EXCLUDED.theme,
          chairman_id = EXCLUDED.chairman_id,
          updated_at = NOW()
      `,
        [
          row.id,
          row.congregation_id,
          row.meeting_date,
          row.theme,
          row.chairman_id,
          row.location || 'Kingdom Hall',
          row.notes,
          row.created_at,
          row.updated_at,
        ]
      );
    }

    console.log(`Migrated ${midweekRows.length} midweek meetings`);
  }

  async validateMigration() {
    console.log('Validating migration...');

    // Count records in both databases
    const [mysqlCounts] = await this.mysqlConnection.execute(`
      SELECT 
        (SELECT COUNT(*) FROM congregations) as congregations,
        (SELECT COUNT(*) FROM members) as members,
        (SELECT COUNT(*) FROM midweek_meetings) as midweek_meetings,
        (SELECT COUNT(*) FROM tasks) as tasks
    `);

    const pgCounts = await this.pgPool.query(`
      SELECT 
        (SELECT COUNT(*) FROM congregations) as congregations,
        (SELECT COUNT(*) FROM members) as members,
        (SELECT COUNT(*) FROM midweek_meetings) as midweek_meetings,
        (SELECT COUNT(*) FROM tasks) as tasks
    `);

    const mysql = mysqlCounts[0];
    const postgres = pgCounts.rows[0];

    console.log('Migration validation:');
    console.log(
      `Congregations: MySQL ${mysql.congregations} -> PostgreSQL ${postgres.congregations}`
    );
    console.log(`Members: MySQL ${mysql.members} -> PostgreSQL ${postgres.members}`);
    console.log(
      `Midweek Meetings: MySQL ${mysql.midweek_meetings} -> PostgreSQL ${postgres.midweek_meetings}`
    );
    console.log(`Tasks: MySQL ${mysql.tasks} -> PostgreSQL ${postgres.tasks}`);

    const isValid =
      mysql.congregations === parseInt(postgres.congregations) &&
      mysql.members === parseInt(postgres.members) &&
      mysql.midweek_meetings === parseInt(postgres.midweek_meetings) &&
      mysql.tasks === parseInt(postgres.tasks);

    if (isValid) {
      console.log('✅ Migration validation successful');
    } else {
      console.log('❌ Migration validation failed');
      throw new Error('Migration validation failed');
    }
  }

  async run() {
    try {
      await this.migrateCongregations();
      await this.migrateMembers();
      await this.migrateMeetings();
      // ... migrate other tables

      await this.validateMigration();

      console.log('✅ Database migration completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    } finally {
      await this.mysqlConnection.end();
      await this.pgPool.end();
    }
  }
}

// Run migration
if (require.main === module) {
  const migrator = new DatabaseMigrator();
  migrator.run().catch(console.error);
}

module.exports = DatabaseMigrator;
```

## Database Performance Optimization

### Indexing Strategy

```sql
-- Performance indexes for congregation-scoped queries
CREATE INDEX CONCURRENTLY idx_members_congregation_role ON members(congregation_id, role) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_meetings_congregation_date_range ON midweek_meetings(congregation_id, meeting_date) WHERE meeting_date >= CURRENT_DATE - INTERVAL '1 month';
CREATE INDEX CONCURRENTLY idx_tasks_congregation_active ON tasks(congregation_id) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_assignments_member_status ON task_assignments(assigned_member_id, status) WHERE status != 'completed';

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY idx_field_service_reporting ON field_service_records(congregation_id, service_month, member_id);
CREATE INDEX CONCURRENTLY idx_letters_congregation_visibility_date ON letters(congregation_id, visibility, upload_date) WHERE is_active = true;

-- Partial indexes for performance
CREATE INDEX CONCURRENTLY idx_active_members ON members(congregation_id, name) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_published_meetings ON midweek_meetings(congregation_id, meeting_date) WHERE is_published = true;
```

### Query Optimization Examples

```sql
-- Optimized query for dashboard data
EXPLAIN (ANALYZE, BUFFERS)
SELECT
  m.id,
  m.name,
  m.role,
  COUNT(ta.id) as pending_tasks,
  MAX(fsr.service_month) as last_service_report
FROM members m
LEFT JOIN task_assignments ta ON m.id = ta.assigned_member_id
  AND ta.status = 'assigned'
  AND ta.congregation_id = m.congregation_id
LEFT JOIN field_service_records fsr ON m.id = fsr.member_id
  AND fsr.congregation_id = m.congregation_id
WHERE m.congregation_id = $1
  AND m.is_active = true
GROUP BY m.id, m.name, m.role
ORDER BY m.name;

-- Optimized query for meeting data with parts
EXPLAIN (ANALYZE, BUFFERS)
SELECT
  mm.id,
  mm.meeting_date,
  mm.theme,
  json_agg(
    json_build_object(
      'id', mmp.id,
      'title', mmp.title,
      'part_type', mmp.part_type,
      'assigned_member', mem.name,
      'display_order', mmp.display_order
    ) ORDER BY mmp.display_order
  ) as parts
FROM midweek_meetings mm
LEFT JOIN midweek_meeting_parts mmp ON mm.id = mmp.meeting_id
LEFT JOIN members mem ON mmp.assigned_member_id = mem.id
WHERE mm.congregation_id = $1
  AND mm.meeting_date >= CURRENT_DATE
  AND mm.meeting_date <= CURRENT_DATE + INTERVAL '4 weeks'
GROUP BY mm.id, mm.meeting_date, mm.theme
ORDER BY mm.meeting_date;
```

This database architecture provides a solid foundation for the Hermanos application, ensuring data integrity, performance, and scalability while maintaining all the functionality of the existing system.

---
