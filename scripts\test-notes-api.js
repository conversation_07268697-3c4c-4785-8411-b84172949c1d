const { PrismaClient } = require('@prisma/client');

async function testNotesAPI() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Testing Notes API functionality...\n');
    
    // Get a territory to test with
    const territory = await prisma.territory.findFirst({
      where: { congregationId: '1441' }
    });
    
    if (!territory) {
      console.log('❌ No territories found for testing');
      return;
    }
    
    console.log(`📋 Testing with Territory ${territory.territoryNumber}`);
    console.log(`📍 Address: ${territory.address.substring(0, 100)}...`);
    console.log(`📝 Current Notes: ${territory.notes || 'None'}`);
    
    // Test 1: Add a new note
    console.log('\n🧪 Test 1: Adding a new note...');
    const testAddress = territory.address.split('\n')[0]; // Get first address
    const testNote = 'Test note added at ' + new Date().toISOString();
    
    // Simulate the note addition logic from the frontend
    const existingNotes = territory.notes || '';
    const noteLines = existingNotes.split('\n').filter(line => line.trim());
    const filteredNotes = noteLines.filter(line => !line.startsWith(`${testAddress}:`));
    const newNoteEntry = `${testAddress}: ${testNote}`;
    const updatedNotes = [...filteredNotes, newNoteEntry].join('\n');
    
    console.log(`📝 Adding note for address: ${testAddress}`);
    console.log(`📝 Note content: ${testNote}`);
    console.log(`📝 Updated notes field: ${updatedNotes.substring(0, 200)}...`);
    
    // Update the territory
    const updatedTerritory = await prisma.territory.update({
      where: { id: territory.id },
      data: { 
        notes: updatedNotes,
        updatedAt: new Date()
      }
    });
    
    console.log('✅ Territory updated in database');
    
    // Test 2: Verify the update
    console.log('\n🧪 Test 2: Verifying the update...');
    const verifyTerritory = await prisma.territory.findUnique({
      where: { id: territory.id }
    });
    
    console.log(`📝 Notes after update: ${verifyTerritory.notes?.substring(0, 200)}...`);
    
    // Check if our note is there
    const hasOurNote = verifyTerritory.notes?.includes(testNote);
    console.log(`✅ Our note found: ${hasOurNote}`);
    
    // Test 3: Parse notes like the frontend does
    console.log('\n🧪 Test 3: Testing frontend parsing logic...');
    const addresses = territory.address.split('\n').filter(line => line.trim());
    const notesMap = new Map();
    
    if (verifyTerritory.notes) {
      const noteLines = verifyTerritory.notes.split('\n');
      noteLines.forEach(noteLine => {
        const colonIndex = noteLine.indexOf(':');
        if (colonIndex > 0) {
          const addressPart = noteLine.substring(0, colonIndex).trim();
          const notePart = noteLine.substring(colonIndex + 1).trim();
          notesMap.set(addressPart, notePart);
        }
      });
    }
    
    console.log(`📊 Parsed ${notesMap.size} notes from database`);
    console.log('📋 Sample parsed notes:');
    let count = 0;
    for (const [addr, note] of notesMap) {
      if (count < 3) {
        console.log(`  - ${addr}: ${note}`);
        count++;
      }
    }
    
    // Test 4: Check if our test address has the note
    const ourAddressNote = notesMap.get(testAddress);
    console.log(`📝 Note for our test address: ${ourAddressNote}`);
    console.log(`✅ Test note correctly parsed: ${ourAddressNote === testNote}`);
    
    // Test 5: Simulate API response
    console.log('\n🧪 Test 5: Simulating API response...');
    const apiResponse = {
      id: verifyTerritory.id,
      territoryNumber: verifyTerritory.territoryNumber,
      address: verifyTerritory.address,
      status: verifyTerritory.status,
      notes: verifyTerritory.notes,
      displayOrder: verifyTerritory.displayOrder,
      createdAt: verifyTerritory.createdAt,
      updatedAt: verifyTerritory.updatedAt
    };
    
    console.log('📡 API Response structure looks correct');
    console.log(`📝 Notes in response: ${apiResponse.notes ? 'Present' : 'Missing'}`);
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n💡 The database and API logic appear to be working correctly.');
    console.log('💡 The issue might be in the frontend component re-rendering or state management.');
    
  } catch (error) {
    console.error('❌ Error testing notes API:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testNotesAPI();
