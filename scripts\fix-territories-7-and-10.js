/**
 * Fix Territories 7 and 10 - Correct Building and Address Parsing
 * 
 * Territory 7: Use "Edificio" markers to identify buildings and their apartments
 * Territory 10: Remove "REGISTRO DE CASA EN CASA" false addresses
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const CONGREGATION_ID = '1441'; // Coral Oeste

/**
 * Parse Territory 007 - Using "Edificio" markers correctly
 */
function parseTerritory007Fixed(excelData) {
  const addresses = [];
  let currentBuilding = '';
  let isInBuilding = false;
  
  console.log('\n🔍 Parsing Territory 007 with Edificio markers...');
  
  for (let i = 0; i < excelData.length; i++) {
    const row = excelData[i];
    if (!row || row.length === 0) continue;
    
    // Skip header rows
    if (i < 8) continue;
    
    const cellB = row[1];
    const cellG = row[6]; // Notes column
    const cellH = row[7]; // Second apartment column
    
    // Check for building number with "Edificio" marker
    if (cellB && cellG && cellG.toString().trim() === 'Edificio') {
      const buildingNumber = cellB.toString().trim();
      currentBuilding = `${buildingNumber} W FLAGLER ST`;
      isInBuilding = true;
      
      // Add building address
      addresses.push({
        address: `${currentBuilding}, Miami, FL 33144`,
        isBuilding: true,
        isBuildingHeader: true
      });
      
      console.log(`🏢 Found building: ${currentBuilding}`);
      continue;
    }
    
    // Parse apartment numbers when in a building
    if (isInBuilding && cellB) {
      const cellValue = cellB.toString().trim();
      
      // Skip Excel date serials and headers
      if (typeof row[1] === 'number' && row[1] > 40000) continue;
      if (cellValue.includes('Casa') || cellValue.includes('Fechas') || cellValue.includes('REGISTRO')) continue;
      
      // Check if this is an apartment number
      if (/^\d+[a-z]?$/.test(cellValue)) {
        const apartmentNumber = cellValue;
        
        // Get notes from column G if available (and not "Edificio")
        let notes = null;
        if (cellG && typeof cellG === 'string') {
          const noteText = cellG.toString().trim();
          if (noteText && noteText !== 'null' && noteText !== '' && 
              noteText !== 'Edificio' && !noteText.includes('H-23') && !noteText.includes('T-')) {
            notes = noteText;
          }
        }
        
        addresses.push({
          address: `Apt ${apartmentNumber}`,
          notes: notes,
          isBuilding: true,
          isBuildingHeader: false,
          buildingAddress: currentBuilding
        });
        
        console.log(`   🚪 Apt ${apartmentNumber}${notes ? ` (${notes})` : ''}`);
      }
    }
    
    // Also check column H for additional apartments
    if (isInBuilding && cellH) {
      const cellHValue = cellH.toString().trim();
      if (/^\d+[a-z]?$/.test(cellHValue)) {
        const apartmentNumber = cellHValue;
        
        addresses.push({
          address: `Apt ${apartmentNumber}`,
          notes: null,
          isBuilding: true,
          isBuildingHeader: false,
          buildingAddress: currentBuilding
        });
        
        console.log(`   🚪 Apt ${apartmentNumber}`);
      }
    }
    
    // Check for new building sections (when we encounter another "Edificio")
    if (cellG && cellG.toString().trim() === 'Edificio' && cellB) {
      // This will be handled in the next iteration
      continue;
    }
    
    // Reset building context when we hit a new section header
    if (cellB && cellB.toString().includes('REGISTRO DE CASA EN CASA')) {
      isInBuilding = false;
      currentBuilding = '';
      console.log('📋 New section detected, resetting building context');
    }
  }
  
  return addresses;
}

/**
 * Parse Territory 010 - Exclude "REGISTRO DE CASA EN CASA" false addresses
 */
function parseTerritory010Fixed(excelData) {
  const addresses = [];
  let currentStreet = '';
  
  console.log('\n🔍 Parsing Territory 010 excluding false addresses...');
  
  for (let i = 8; i < excelData.length; i++) {
    const row = excelData[i];
    if (!row || row.length === 0) continue;
    
    const cellB = row[1];
    if (!cellB) continue;
    
    const cellValue = cellB.toString().trim();
    if (!cellValue) continue;
    
    // Skip "REGISTRO DE CASA EN CASA" - this is a header, not an address
    if (cellValue.includes('REGISTRO DE CASA EN CASA')) {
      console.log(`⏭️  Skipping header: ${cellValue}`);
      continue;
    }
    
    // Skip other headers
    if (cellValue.includes('Zip:') || cellValue.includes('Símbolos') || 
        cellValue.includes('Fechas') || cellValue.includes('No. de Casa') ||
        cellValue.includes('Manz.')) {
      console.log(`⏭️  Skipping header: ${cellValue}`);
      continue;
    }
    
    // Check if this is a street name
    if (cellValue.includes('AVE') || cellValue.includes('ST') || cellValue.includes('CT')) {
      currentStreet = cellValue;
      console.log(`📍 Found street: ${currentStreet}`);
      continue;
    }
    
    // Check if this is a house/business number
    if (/^\d+[a-z]?$/.test(cellValue) && currentStreet) {
      const houseNumber = cellValue;
      const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL 33144`;
      
      // Get notes
      let notes = null;
      if (row[6] && typeof row[6] === 'string') {
        const noteText = row[6].toString().trim();
        if (noteText && noteText !== 'null' && noteText !== '' && 
            !noteText.includes('TER.') && !noteText.includes('Manz.')) {
          notes = noteText;
        }
      }
      
      addresses.push({
        address: fullAddress,
        notes: notes,
        street: currentStreet,
        houseNumber: houseNumber,
        isBuilding: false
      });
      
      console.log(`🏠 Added address: ${fullAddress}${notes ? ` (${notes})` : ''}`);
    }
  }
  
  return addresses;
}

async function fixTerritory(territoryNumber) {
  try {
    console.log(`\n📂 Fixing Territory ${territoryNumber}...`);
    
    const configs = {
      '007': { displayOrder: 7, zipCode: 'Miami, FL 33144', sheetName: 'Terr 7' },
      '010': { displayOrder: 10, zipCode: 'Miami, FL 33144', sheetName: 'Terr 10' }
    };
    
    const config = configs[territoryNumber];
    if (!config) {
      console.error(`❌ No configuration found for Territory ${territoryNumber}`);
      return false;
    }

    // Read Excel file
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${territoryNumber}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    const worksheet = workbook.Sheets[config.sheetName];
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse based on territory
    let addresses = [];
    if (territoryNumber === '007') {
      addresses = parseTerritory007Fixed(excelData);
    } else if (territoryNumber === '010') {
      addresses = parseTerritory010Fixed(excelData);
    }
    
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    if (addresses.length === 0) {
      console.log(`⚠️  No addresses found for Territory ${territoryNumber}`);
      return false;
    }
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: CONGREGATION_ID }
    });

    if (!congregation) {
      console.error(`❌ Congregation ${CONGREGATION_ID} (Coral Oeste) not found`);
      return false;
    }

    // Clear existing territory
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: congregation.id,
        territory: { territoryNumber: territoryNumber }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: congregation.id,
        territoryNumber: territoryNumber
      }
    });
    
    console.log(`🗑️  Cleared existing Territory ${territoryNumber}`);
    
    // Create territory
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: congregation.id,
        territoryNumber: territoryNumber,
        address: allAddresses,
        notes: allNotes || null,
        status: 'available',
        displayOrder: config.displayOrder
      }
    });
    
    console.log(`✅ Created Territory ${territoryNumber} with ${addresses.length} addresses`);
    
    // Display summary
    if (territoryNumber === '007') {
      const buildingHeaders = addresses.filter(addr => addr.isBuildingHeader);
      const apartments = addresses.filter(addr => !addr.isBuildingHeader);
      console.log(`   🏢 Buildings: ${buildingHeaders.length}`);
      console.log(`   🚪 Apartments: ${apartments.length}`);
    } else {
      console.log(`   🏠 Houses/Businesses: ${addresses.length}`);
    }
    console.log(`   📝 With Notes: ${addresses.filter(addr => addr.notes).length}`);
    
    // Show first few addresses for verification
    console.log('\n📋 First 10 addresses:');
    addresses.slice(0, 10).forEach((addr, index) => {
      const prefix = addr.isBuildingHeader ? '🏢' : (addr.isBuilding ? '   🚪' : '🏠');
      console.log(`${prefix} ${addr.address}${addr.notes ? ` (${addr.notes})` : ''}`);
    });
    
    return true;
    
  } catch (error) {
    console.error(`❌ Error fixing Territory ${territoryNumber}:`, error.message);
    return false;
  }
}

async function fixTerritories7And10() {
  try {
    console.log('🚀 Starting fix for territories 7 and 10...');
    
    const territories = ['007', '010'];
    let successCount = 0;
    
    for (const territory of territories) {
      const success = await fixTerritory(territory);
      if (success) successCount++;
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`\n🎉 Fix completed! Successfully fixed: ${successCount} territories`);
    
  } catch (error) {
    console.error('❌ Error during fix:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  fixTerritories7And10();
}

module.exports = { fixTerritory };
