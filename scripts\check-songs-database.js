/**
 * Check Songs Database Script
 * 
 * Checks if songs exist in the database and displays basic information
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSongsDatabase() {
  try {
    console.log('🎵 Checking Songs Database...\n');

    // Check if songs table exists and has data
    const songCount = await prisma.song.count();
    console.log(`📊 Total songs in database: ${songCount}`);

    if (songCount > 0) {
      // Get first 10 songs
      const songs = await prisma.song.findMany({
        take: 10,
        orderBy: { songNumber: 'asc' },
        select: {
          songNumber: true,
          titleEs: true,
          titleEn: true,
          isActive: true,
        },
      });

      console.log('\n📋 First 10 songs:');
      console.log('Num. | Spanish Title | English Title | Active');
      console.log('-'.repeat(80));
      
      songs.forEach(song => {
        const spanishTitle = song.titleEs || 'N/A';
        const englishTitle = song.titleEn || 'N/A';
        const active = song.isActive ? 'Yes' : 'No';
        console.log(`${song.songNumber.toString().padEnd(4)} | ${spanishTitle.substring(0, 25).padEnd(25)} | ${englishTitle.substring(0, 25).padEnd(25)} | ${active}`);
      });

      // Check language distribution
      const spanishCount = await prisma.song.count({
        where: {
          titleEs: { not: null },
          titleEs: { not: '' }
        }
      });

      const englishCount = await prisma.song.count({
        where: {
          titleEn: { not: null },
          titleEn: { not: '' }
        }
      });

      console.log(`\n🌐 Language Distribution:`);
      console.log(`   Spanish titles: ${spanishCount}`);
      console.log(`   English titles: ${englishCount}`);

      // Check active/inactive distribution
      const activeCount = await prisma.song.count({ where: { isActive: true } });
      const inactiveCount = await prisma.song.count({ where: { isActive: false } });

      console.log(`\n📊 Status Distribution:`);
      console.log(`   Active songs: ${activeCount}`);
      console.log(`   Inactive songs: ${inactiveCount}`);

    } else {
      console.log('\n⚠️ No songs found in database!');
      console.log('   The songs table exists but is empty.');
      console.log('   You may need to:');
      console.log('   1. Import songs from the MySQL database');
      console.log('   2. Run the JW.org sync operation');
      console.log('   3. Manually add songs through the admin interface');
    }

  } catch (error) {
    console.error('❌ Error checking songs database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkSongsDatabase().catch(console.error);
