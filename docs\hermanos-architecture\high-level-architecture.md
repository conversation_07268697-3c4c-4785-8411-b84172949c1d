# High Level Architecture

## Technical Summary

The Hermanos multi-congregation app will be built as a **monolithic Next.js 14+ application with multi-tenant PostgreSQL architecture**, deployed on local/self-hosted infrastructure with local file storage. The frontend leverages React with TypeScript and Tailwind CSS for pixel-perfect UI preservation, while the backend uses Next.js API routes with Prisma ORM for type-safe database operations. Multi-tenancy is achieved through congregation_id-based data isolation at the database level, enabling secure separation between congregations while maintaining a shared codebase. The architecture preserves all existing Coral Oeste functionality while modernizing from MySQL/Node.js to PostgreSQL/Next.js, with critical JW.org integration logic maintained exactly as implemented in the reference system.

## Platform and Infrastructure Choice

**Recommendation: Local/Self-Hosted Infrastructure**

Given the project's focus on preserving existing functionality while modernizing the stack, local/self-hosted infrastructure provides the optimal balance of control and existing infrastructure alignment. The local PostgreSQL and file storage approach maintains operational consistency.

**Platform:** Local/Self-hosted server
**Key Services:** Local PostgreSQL database, local file storage, PM2 process management
**Deployment Host and Regions:** Local server infrastructure with nginx reverse proxy

## Repository Structure

**Structure:** Monorepo with Next.js App Router
**Monorepo Tool:** npm workspaces (built-in, simpler than Turborepo for this scale)
**Package Organization:** Feature-based packages with shared utilities, types, and UI components

The monorepo structure enables shared code between different congregation instances while maintaining clear boundaries. The Next.js App Router provides excellent organization for the multi-tenant architecture with congregation-specific routing patterns.

## Architectural Patterns

- **Jamstack Architecture:** Static site generation with serverless APIs - _Rationale:_ Optimal performance and scalability for content-heavy applications with excellent mobile performance
- **Multi-Tenant SaaS Pattern:** Single application instance serving multiple congregations with data isolation - _Rationale:_ Enables efficient resource utilization while maintaining strict data separation between congregations
- **Component-Based UI:** Reusable React components with TypeScript - _Rationale:_ Maintainability and type safety across large codebases, essential for pixel-perfect UI preservation
- **Repository Pattern:** Abstract data access logic through Prisma ORM - _Rationale:_ Enables testing and future database migration flexibility while maintaining type safety
- **API Gateway Pattern:** Centralized API routing through Next.js API routes - _Rationale:_ Centralized auth, rate limiting, and monitoring with built-in Next.js optimization
- **Middleware Chain Pattern:** Layered request processing for auth, tenant isolation, and validation - _Rationale:_ Ensures consistent security and data isolation across all API endpoints
- **Event-Driven Updates:** Real-time updates for meeting assignments and notifications - _Rationale:_ Improves user experience for collaborative features like meeting management
- **Cache-Aside Pattern:** Strategic caching for JW.org data and frequently accessed congregation data - _Rationale:_ Reduces external API calls and improves response times for mobile users
