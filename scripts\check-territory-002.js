const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkTerritory002() {
  try {
    console.log('🔍 Checking Territory 002');
    console.log('=========================\n');

    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '002'
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        boundaries: true
      }
    });

    if (!territory) {
      console.log('❌ Territory 002 not found');
      return;
    }

    console.log(`✅ Territory 002 found (ID: ${territory.id})`);
    
    // Check addresses
    const addresses = territory.address.split('\n').filter(addr => addr.trim());
    console.log(`📋 Total addresses: ${addresses.length}`);
    console.log(`📍 First address: ${addresses[0]}`);

    // Check boundaries
    if (territory.boundaries) {
      console.log('\n✅ Territory 002 has boundary data');
      console.log(`📐 Boundary type: ${territory.boundaries.type}`);
      console.log(`📍 Coordinate points: ${territory.boundaries.coordinates[0].length}`);
      
      console.log('\n📍 Territory 002 Boundary Coordinates:');
      territory.boundaries.coordinates[0].forEach((coord, index) => {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
        console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}]`);
      });

      // Calculate center
      const coords = territory.boundaries.coordinates[0].slice(0, -1);
      const centerLat = coords.reduce((sum, c) => sum + c[1], 0) / coords.length;
      const centerLng = coords.reduce((sum, c) => sum + c[0], 0) / coords.length;
      
      console.log('\n📍 Territory 002 Center:');
      console.log(`   Latitude: ${centerLat}`);
      console.log(`   Longitude: ${centerLng}`);

    } else {
      console.log('\n❌ Territory 002 has no boundary data');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTerritory002();
