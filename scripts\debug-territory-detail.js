/**
 * Debug Territory Detail Component Logic
 */

async function debugTerritoryDetail() {
  try {
    console.log('🔍 Debugging Territory Detail Component Logic...\n');

    // Territory 001 ID from our previous test
    const territory001Id = 'cmdjtpo7b000163cf8kvvkokh';

    // Login to get a token
    console.log('🔐 Logging in...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1234'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.statusText}`);
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ Login successful');

    // Fetch territory data (simulating TerritoryDetail component)
    console.log('\n📡 Fetching territory data...');
    const response = await fetch(`http://localhost:3000/api/territories/${territory001Id}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch territory: ${response.statusText}`);
    }

    const territoryData = await response.json();
    console.log('✅ Territory data fetched');

    // Simulate the TerritoryDetail component logic
    console.log('\n🔄 Simulating TerritoryDetail component logic...');

    let coordinates = territoryData.coordinates;
    let boundary = territoryData.boundary;

    console.log('📊 Raw API Response:');
    console.log(`  coordinates: ${coordinates ? 'EXISTS' : 'NULL'}`);
    console.log(`  boundary: ${boundary ? 'EXISTS' : 'NULL'}`);

    if (boundary) {
      console.log('🗺️  Boundary details:');
      console.log(`    type: ${boundary.type}`);
      console.log(`    coordinates length: ${boundary.coordinates ? boundary.coordinates.length : 'NULL'}`);
      if (boundary.coordinates && boundary.coordinates[0]) {
        console.log(`    first coordinate ring length: ${boundary.coordinates[0].length}`);
        console.log(`    sample coordinates: ${JSON.stringify(boundary.coordinates[0].slice(0, 2))}`);
      }
    } else {
      console.log('❌ No boundary data found - this is why the map is not showing!');
    }

    // If missing boundary data, create it from addresses (this is the enhancement logic)
    if (!boundary && territoryData.address) {
      console.log('\n⚠️  No boundary data, attempting enhancement...');
      try {
        // This would normally import the enhancement service
        console.log('   Enhancement would be attempted here');
        console.log('   But boundary data should already exist from our API fix');
      } catch (enhanceError) {
        console.warn('   Enhancement failed:', enhanceError);
      }
    }

    // Create mapTerritory object (what gets passed to SimpleTerritoryMap)
    const mapTerritoryData = {
      id: territoryData.id,
      territoryNumber: territoryData.territoryNumber,
      address: territoryData.address,
      status: territoryData.status,
      coordinates,
      boundary
    };

    console.log('\n🗺️  MapTerritory object that would be created:');
    console.log(`  id: ${mapTerritoryData.id}`);
    console.log(`  territoryNumber: ${mapTerritoryData.territoryNumber}`);
    console.log(`  status: ${mapTerritoryData.status}`);
    console.log(`  coordinates: ${mapTerritoryData.coordinates ? 'EXISTS' : 'NULL'}`);
    console.log(`  boundary: ${mapTerritoryData.boundary ? 'EXISTS' : 'NULL'}`);

    // Check if map would be shown
    const showMap = true; // Default state
    const mapTerritory = mapTerritoryData;
    const shouldShowMap = showMap && mapTerritory && mapTerritory.boundary;

    console.log('\n🎯 Map Display Logic:');
    console.log(`  showMap: ${showMap}`);
    console.log(`  mapTerritory exists: ${mapTerritory ? 'YES' : 'NO'}`);
    console.log(`  mapTerritory.boundary exists: ${mapTerritory?.boundary ? 'YES' : 'NO'}`);
    console.log(`  Should show map: ${shouldShowMap ? 'YES' : 'NO'}`);

    if (shouldShowMap) {
      console.log('\n🎉 SUCCESS: Map should be displayed!');
      console.log('   If map is not showing, check:');
      console.log('   1. Browser console for JavaScript errors');
      console.log('   2. Map toggle button state');
      console.log('   3. SimpleTerritoryMap component errors');
    } else {
      console.log('\n❌ ISSUE: Map will NOT be displayed');
      console.log('   Reason: Missing boundary data or other condition failed');
    }

  } catch (error) {
    console.error('\n❌ Debug failed:', error.message);
  }
}

debugTerritoryDetail();
