#!/usr/bin/env node

/**
 * Enhanced Authentication Test Script for Hermanos App
 *
 * Tests the enhanced authentication system with region validation,
 * role-based access control, and JWT token generation.
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

class AuthenticationTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = {
      congregationCreated: false,
      memberCreated: false,
      loginTested: false,
      errors: [],
    };
  }

  async initialize() {
    try {
      await this.prisma.$connect();
      console.log('✅ Database connected');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async createTestCongregation() {
    console.log('\n📝 Creating test congregation...');

    try {
      // Hash the PIN
      const hashedPin = await bcrypt.hash('testpin123', 12);

      // Create test congregation
      const congregation = await this.prisma.congregation.upsert({
        where: { id: 'TESTAUTH' },
        update: {
          name: 'Test Authentication Congregation',
          region: 'america-central',
          pin: hashedPin,
          language: 'es',
          timezone: 'America/Mexico_City',
          isActive: true,
        },
        create: {
          id: 'TESTAUTH',
          name: 'Test Authentication Congregation',
          region: 'america-central',
          pin: hashedPin,
          language: 'es',
          timezone: 'America/Mexico_City',
          isActive: true,
        },
      });

      console.log(`✅ Test congregation created: ${congregation.name} (${congregation.id})`);
      this.testResults.congregationCreated = true;

      return congregation;

    } catch (error) {
      console.error('❌ Error creating test congregation:', error.message);
      this.testResults.errors.push(`Congregation creation: ${error.message}`);
      throw error;
    }
  }

  async createTestMember(congregationId) {
    console.log('\n👤 Creating test member...');

    try {
      // Hash the member PIN
      const hashedPin = await bcrypt.hash('memberpin123', 12);

      // Create test member
      const member = await this.prisma.member.upsert({
        where: {
          congregationId_email: {
            congregationId: congregationId,
            email: '<EMAIL>'
          }
        },
        update: {
          name: 'Test Elder',
          role: 'elder',
          pin: hashedPin,
          isActive: true,
        },
        create: {
          congregationId: congregationId,
          name: 'Test Elder',
          email: '<EMAIL>',
          role: 'elder',
          pin: hashedPin,
          isActive: true,
        },
      });

      console.log(`✅ Test member created: ${member.name} (${member.role})`);
      this.testResults.memberCreated = true;

      return member;

    } catch (error) {
      console.error('❌ Error creating test member:', error.message);
      this.testResults.errors.push(`Member creation: ${error.message}`);
      throw error;
    }
  }

  async testLoginAPI() {
    console.log('\n🔐 Testing login API...');

    try {
      // Test data
      const loginData = {
        region: 'america-central',
        congregationId: 'TESTAUTH',
        pin: 'testpin123',
        rememberMe: false,
      };

      // Make API request
      const response = await fetch('http://localhost:3001/api/auth/congregation-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('✅ Login API test successful');
        console.log(`   Token: ${data.token ? 'Generated' : 'Missing'}`);
        console.log(`   User: ${data.user?.name} (${data.user?.role})`);
        console.log(`   Congregation: ${data.congregation?.name}`);
        console.log(`   Admin Access: ${data.permissions?.canAccessAdmin ? 'Yes' : 'No'}`);

        this.testResults.loginTested = true;
      } else {
        console.error('❌ Login API test failed:', data.error || 'Unknown error');
        this.testResults.errors.push(`Login API: ${data.error || 'Unknown error'}`);
      }

    } catch (error) {
      console.error('❌ Error testing login API:', error.message);
      this.testResults.errors.push(`Login API: ${error.message}`);
    }
  }

  async testInvalidLogin() {
    console.log('\n🚫 Testing invalid login scenarios...');

    const testCases = [
      {
        name: 'Invalid congregation ID',
        data: {
          region: 'america-central',
          congregationId: 'INVALID',
          pin: 'testpin123',
          rememberMe: false,
        },
        expectedError: 'Congregation not found or inactive',
        shouldFail: true,
      },
      {
        name: 'Invalid PIN',
        data: {
          region: 'america-central',
          congregationId: 'TESTAUTH',
          pin: 'wrongpin',
          rememberMe: false,
        },
        expectedError: 'Invalid congregation PIN',
        shouldFail: true,
      },
      {
        name: 'Valid login without region',
        data: {
          congregationId: 'TESTAUTH',
          pin: 'testpin123',
          rememberMe: false,
        },
        expectedError: null,
        shouldFail: false,
      },
      {
        name: 'Valid login with any region',
        data: {
          region: 'europa',
          congregationId: 'TESTAUTH',
          pin: 'testpin123',
          rememberMe: false,
        },
        expectedError: null,
        shouldFail: false,
      },
    ];

    for (const testCase of testCases) {
      try {
        const response = await fetch('http://localhost:3001/api/auth/congregation-login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testCase.data),
        });

        const data = await response.json();

        if (testCase.shouldFail) {
          // This test case should fail
          if (!response.ok && data.error) {
            console.log(`✅ ${testCase.name}: Correctly rejected`);
          } else {
            console.log(`❌ ${testCase.name}: Should have been rejected`);
            this.testResults.errors.push(`${testCase.name}: Should have been rejected`);
          }
        } else {
          // This test case should succeed
          if (response.ok && data.success) {
            console.log(`✅ ${testCase.name}: Correctly accepted`);
          } else {
            console.log(`❌ ${testCase.name}: Should have been accepted`);
            this.testResults.errors.push(`${testCase.name}: Should have been accepted`);
          }
        }

      } catch (error) {
        console.error(`❌ Error testing ${testCase.name}:`, error.message);
        this.testResults.errors.push(`${testCase.name}: ${error.message}`);
      }
    }
  }

  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      // Delete test member
      await this.prisma.member.deleteMany({
        where: { congregationId: 'TESTAUTH' },
      });

      // Delete test congregation
      await this.prisma.congregation.delete({
        where: { id: 'TESTAUTH' },
      });

      console.log('✅ Test data cleanup completed');

    } catch (error) {
      console.error('❌ Error during cleanup:', error.message);
      // Don't throw here, just log the error
    }
  }

  async generateTestReport() {
    console.log('\n📋 Generating test report...');

    const totalTests = 3;
    const passedTests = [
      this.testResults.congregationCreated,
      this.testResults.memberCreated,
      this.testResults.loginTested,
    ].filter(Boolean).length;

    console.log('\n' + '='.repeat(60));
    console.log('📊 AUTHENTICATION TEST REPORT');
    console.log('='.repeat(60));
    console.log(`✅ Tests passed: ${passedTests}/${totalTests}`);
    console.log(`❌ Errors: ${this.testResults.errors.length}`);

    if (this.testResults.errors.length > 0) {
      console.log('\n🚨 ERRORS:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    const allTestsPassed = this.testResults.errors.length === 0 && passedTests === totalTests;
    console.log('\n' + (allTestsPassed ? '✅ All tests PASSED!' : '❌ Some tests FAILED!'));
    console.log('='.repeat(60));

    return allTestsPassed;
  }

  async runFullTest() {
    console.log('🧪 Starting enhanced authentication test...\n');

    try {
      await this.initialize();

      const congregation = await this.createTestCongregation();
      await this.createTestMember(congregation.id);
      await this.testLoginAPI();
      await this.testInvalidLogin();

      const allTestsPassed = await this.generateTestReport();

      if (!allTestsPassed) {
        process.exit(1);
      }

      console.log('\n✅ Enhanced authentication test completed successfully!');

    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      process.exit(1);
    } finally {
      await this.cleanupTestData();
      await this.prisma.$disconnect();
    }
  }
}

// Main execution
async function main() {
  const tester = new AuthenticationTester();
  await tester.runFullTest();
}

// Run test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = AuthenticationTester;
