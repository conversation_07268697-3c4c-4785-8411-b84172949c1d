#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

async function testLoginDebug() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 Testing login for congregation 1441 with PIN 1930...');

    // Simulate the exact login logic from congregation-login route
    const congregation = await prisma.congregation.findFirst({
      where: {
        id: '1441',
        isActive: true,
      },
    });

    if (!congregation) {
      console.log('❌ Congregation 1441 not found');
      return;
    }

    console.log(`✅ Found congregation: ${congregation.name} (ID: ${congregation.id})`);

    // Check PIN (simulate the exact logic)
    const pin = '1930';
    let isPinValid = false;

    if (congregation.pin.startsWith('$2a$') || congregation.pin.startsWith('$2b$')) {
      const bcrypt = require('bcryptjs');
      isPinValid = await bcrypt.compare(pin, congregation.pin);
      console.log('🔐 PIN check: Using bcrypt comparison');
    } else {
      isPinValid = pin === congregation.pin;
      console.log('🔐 PIN check: Using plain text comparison');
    }

    console.log(`🔐 PIN '${pin}' valid: ${isPinValid ? '✅ YES' : '❌ NO'}`);

    if (!isPinValid) {
      console.log('❌ Login would fail - invalid PIN');
      return;
    }

    // Get members (exact same logic as updated login route)
    const members = await prisma.member.findMany({
      where: {
        congregationId: congregation.id,
        isActive: true,
      },
    });

    // Define role hierarchy (highest to lowest priority) - same as login route
    const roleHierarchy = {
      'developer': 5,
      'overseer_coordinator': 4,
      'elder': 3,
      'ministerial_servant': 2,
      'publisher': 1,
    };

    // Sort members by role hierarchy (highest first), then by creation date
    const sortedMembers = members.sort((a, b) => {
      const aRolePriority = roleHierarchy[a.role] || 0;
      const bRolePriority = roleHierarchy[b.role] || 0;

      if (aRolePriority !== bRolePriority) {
        return bRolePriority - aRolePriority; // Higher priority first
      }

      // If same role, sort by creation date (earliest first)
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    });

    console.log(`\n👥 Found ${members.length} active members`);

    if (members.length === 0) {
      console.log('❌ No active members found');
      return;
    }

    // Show first 5 members (login order) - using sorted members
    console.log('\n📋 First 5 members (login selection order):');
    const adminRoles = ['elder', 'overseer_coordinator', 'developer', 'ministerial_servant'];

    sortedMembers.slice(0, 5).forEach((member, index) => {
      const hasAdmin = adminRoles.includes(member.role);
      const priority = roleHierarchy[member.role] || 0;
      console.log(`  ${index + 1}. ${member.name} (${member.role}, priority: ${priority}) - Admin: ${hasAdmin ? '✅' : '❌'}`);
    });

    // The user that would be selected
    const selectedUser = sortedMembers[0];
    console.log(`\n🎯 User that would be logged in: ${selectedUser.name}`);
    console.log(`   Role: ${selectedUser.role}`);
    console.log(`   Created: ${selectedUser.createdAt}`);

    // Check admin permissions (exact same logic as dashboard route)
    const hasAdminAccess = adminRoles.includes(selectedUser.role);
    console.log(`\n🔑 Admin access check:`);
    console.log(`   User role: ${selectedUser.role}`);
    console.log(`   Admin roles: ${adminRoles.join(', ')}`);
    console.log(`   Has admin access: ${hasAdminAccess ? '✅ YES' : '❌ NO'}`);
    console.log(`   Should see admin button: ${hasAdminAccess ? '✅ YES' : '❌ NO'}`);

    if (!hasAdminAccess) {
      console.log('\n⚠️  ISSUE FOUND: Selected user does not have admin access!');
      console.log('   This explains why the admin button is not showing.');

      // Find first admin user
      const firstAdmin = members.find(m => adminRoles.includes(m.role));
      if (firstAdmin) {
        console.log(`\n💡 First admin user available: ${firstAdmin.name} (${firstAdmin.role})`);
        console.log('   Consider adjusting the member ordering or role assignments.');
      }
    } else {
      console.log('\n✅ Selected user should see admin button - issue might be in frontend');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testLoginDebug();
