'use client';

/**
 * Territory Search Component
 * 
 * Provides search and filtering functionality for territories.
 * Includes search bar and status filter dropdown.
 */

import React from 'react';
import { TerritoryStatus } from '@/types/territories/territory';

interface TerritorySearchProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  statusFilter: TerritoryStatus | 'all';
  onStatusFilterChange: (status: TerritoryStatus | 'all') => void;
  onClearFilters: () => void;
  className?: string;
}

const statusOptions: Array<{ value: TerritoryStatus | 'all'; label: string }> = [
  { value: 'all', label: 'Todos los Estados' },
  { value: 'available', label: 'Disponible' },
  { value: 'assigned', label: 'Asignado' },
  { value: 'completed', label: 'Completado' },
  { value: 'out_of_service', label: 'Fuera de Servicio' }
];

export default function TerritorySearch({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  onClearFilters,
  className = ''
}: TerritorySearchProps) {
  const hasActiveFilters = searchTerm.length > 0 || statusFilter !== 'all';

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg 
                className="h-5 w-5 text-gray-400" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
                />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Buscar por número de territorio o dirección..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="
                block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md 
                leading-5 bg-white placeholder-gray-500 focus:outline-none 
                focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 
                focus:border-blue-500 text-sm
              "
            />
            {searchTerm && (
              <button
                onClick={() => onSearchChange('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                aria-label="Limpiar búsqueda"
              >
                <svg className="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Status Filter */}
        <div className="sm:w-64">
          <select
            value={statusFilter}
            onChange={(e) => onStatusFilterChange(e.target.value as TerritoryStatus | 'all')}
            className="
              block w-full px-3 py-2 border border-gray-300 rounded-md 
              bg-white text-sm focus:outline-none focus:ring-1 
              focus:ring-blue-500 focus:border-blue-500
            "
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <div className="sm:w-auto">
            <button
              onClick={onClearFilters}
              className="
                w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 
                bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                transition-colors duration-200
              "
            >
              <div className="flex items-center justify-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                <span>Limpiar</span>
              </div>
            </button>
          </div>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-600">Filtros activos:</span>
            
            {searchTerm && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Búsqueda: "{searchTerm}"
                <button
                  onClick={() => onSearchChange('')}
                  className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600"
                  aria-label="Remover filtro de búsqueda"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            )}
            
            {statusFilter !== 'all' && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Estado: {statusOptions.find(opt => opt.value === statusFilter)?.label}
                <button
                  onClick={() => onStatusFilterChange('all')}
                  className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-green-400 hover:bg-green-200 hover:text-green-600"
                  aria-label="Remover filtro de estado"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
