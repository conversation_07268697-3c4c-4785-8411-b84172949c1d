# Security and Performance

## Security Requirements

**Frontend Security:**
- CSP Headers: `default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://wol.jw.org;`
- XSS Prevention: Input sanitization using DOMPurify, React's built-in XSS protection, and proper content encoding
- Secure Storage: JWT tokens stored in httpOnly cookies with secure and sameSite flags, sensitive data encrypted in localStorage

**Backend Security:**
- Input Validation: Zod schema validation on all API endpoints with comprehensive type checking and sanitization
- Rate Limiting: `express-rate-limit` with **1000 requests per 15 minutes per IP**, stricter limits for auth endpoints (100 requests per 15 minutes for login attempts)
- CORS Policy: `{ origin: process.env.FRONTEND_URL, credentials: true, methods: ['GET', 'POST', 'PUT', 'DELETE'] }`

**Authentication Security:**
- Token Storage: JWT tokens in httpOnly cookies with 60-day expiration, automatic refresh mechanism
- Session Management: Stateless JWT-based sessions with congregation and member context, secure logout clearing all tokens
- Password Policy: Minimum 4-digit PIN requirement with bcrypt hashing (12 rounds), account lockout after 5 failed attempts

## Performance Optimization

**Frontend Performance:**
- Bundle Size Target: < 500KB initial bundle, < 200KB per route chunk
- Loading Strategy: Next.js automatic code splitting, lazy loading for non-critical components, prefetching for likely navigation paths
- Caching Strategy: React Query with 5-minute stale time, service worker for offline functionality, browser caching for static assets

**Backend Performance:**
- Response Time Target: < 2 seconds for standard queries, < 10 seconds for complex reports and JW.org data fetching
- Database Optimization: Connection pooling (max 20 connections), proper indexing on congregation_id and frequently queried fields, query optimization with Prisma
- Caching Strategy: Redis caching for JW.org data (24-hour TTL), meeting data (1-hour TTL), and session data, in-memory caching for configuration data
