# API Specification

Based on the chosen tRPC API style from the Tech Stack, here are the tRPC router definitions that enable end-to-end type safety while supporting all the functionality defined in the PRD epics.

## tRPC Router Definitions

```typescript
import { z } from 'zod';
import { router, publicProcedure, protectedProcedure, adminProcedure } from './trpc';

// Input validation schemas
const congregationLoginSchema = z.object({
  congregationId: z.string().min(1),
  pin: z.string().min(4),
});

const memberLoginSchema = z.object({
  congregationId: z.string().min(1),
  memberId: z.string().min(1),
  pin: z.string().min(4),
});

const createMemberSchema = z.object({
  name: z.string().min(1),
  email: z.string().email().optional(),
  role: z.enum(['publisher', 'ministerial_servant', 'elder', 'overseer_coordinator']),
  serviceGroup: z.string().optional(),
});

const meetingSchema = z.object({
  type: z.enum(['midweek', 'weekend']),
  date: z.date(),
  chairman: z.string().optional(),
  location: z.enum(['kingdom_hall', 'zoom']),
  zoomDetails: z.object({
    meetingId: z.string(),
    password: z.string(),
    url: z.string(),
  }).optional(),
});

const fieldServiceSchema = z.object({
  date: z.date(),
  hours: z.number().min(0),
  minutes: z.number().min(0).max(59),
  activityType: z.enum(['field_service', 'return_visit', 'bible_study', 'public_witnessing']),
  notes: z.string().optional(),
});

// Main application router
export const appRouter = router({
  // Authentication routes
  auth: router({
    congregationLogin: publicProcedure
      .input(congregationLoginSchema)
      .mutation(async ({ input, ctx }) => {
        // Authenticate congregation and return JWT token
        const { congregationId, pin } = input;
        const congregation = await ctx.db.congregation.findUnique({
          where: { id: congregationId }
        });
        
        if (!congregation || !await bcrypt.compare(pin, congregation.pin)) {
          throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Invalid credentials' });
        }
        
        const token = await signJWT({ congregationId, type: 'congregation' });
        return { token, congregation: { id: congregation.id, name: congregation.name } };
      }),

    memberLogin: publicProcedure
      .input(memberLoginSchema)
      .mutation(async ({ input, ctx }) => {
        // Authenticate member within congregation
        const { congregationId, memberId, pin } = input;
        const member = await ctx.db.member.findFirst({
          where: { id: memberId, congregationId },
          include: { congregation: true }
        });
        
        if (!member || !await bcrypt.compare(pin, member.pin)) {
          throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Invalid credentials' });
        }
        
        const token = await signJWT({ 
          memberId: member.id, 
          congregationId, 
          role: member.role 
        });
        return { token, member, congregation: member.congregation };
      }),

    refreshToken: protectedProcedure
      .mutation(async ({ ctx }) => {
        // Refresh JWT token
        const token = await signJWT({
          memberId: ctx.user.memberId,
          congregationId: ctx.user.congregationId,
          role: ctx.user.role
        });
        return { token };
      }),
  }),

  // Member management routes
  members: router({
    list: protectedProcedure
      .query(async ({ ctx }) => {
        return await ctx.db.member.findMany({
          where: { congregationId: ctx.user.congregationId, isActive: true },
          select: { id: true, name: true, role: true, serviceGroup: true }
        });
      }),

    create: adminProcedure
      .input(createMemberSchema)
      .mutation(async ({ input, ctx }) => {
        const hashedPin = await bcrypt.hash(generateRandomPin(), 10);
        return await ctx.db.member.create({
          data: {
            ...input,
            congregationId: ctx.user.congregationId,
            pin: hashedPin,
            isActive: true
          }
        });
      }),

    update: adminProcedure
      .input(z.object({ id: z.string(), data: createMemberSchema.partial() }))
      .mutation(async ({ input, ctx }) => {
        return await ctx.db.member.update({
          where: { 
            id: input.id, 
            congregationId: ctx.user.congregationId 
          },
          data: input.data
        });
      }),

    resetPin: adminProcedure
      .input(z.object({ memberId: z.string() }))
      .mutation(async ({ input, ctx }) => {
        const newPin = generateRandomPin();
        const hashedPin = await bcrypt.hash(newPin, 10);
        
        await ctx.db.member.update({
          where: { 
            id: input.memberId, 
            congregationId: ctx.user.congregationId 
          },
          data: { pin: hashedPin }
        });
        
        return { newPin };
      }),
  }),

  // Meeting management routes
  meetings: router({
    list: protectedProcedure
      .input(z.object({ 
        type: z.enum(['midweek', 'weekend']).optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional()
      }))
      .query(async ({ input, ctx }) => {
        return await ctx.db.meeting.findMany({
          where: {
            congregationId: ctx.user.congregationId,
            type: input.type,
            date: {
              gte: input.startDate,
              lte: input.endDate
            }
          },
          include: {
            parts: {
              include: {
                assignedMember: { select: { id: true, name: true } },
                assistant: { select: { id: true, name: true } }
              }
            }
          },
          orderBy: { date: 'asc' }
        });
      }),

    create: adminProcedure
      .input(meetingSchema)
      .mutation(async ({ input, ctx }) => {
        return await ctx.db.meeting.create({
          data: {
            ...input,
            congregationId: ctx.user.congregationId,
            status: 'scheduled'
          }
        });
      }),

    fetchJwOrgData: adminProcedure
      .input(z.object({ date: z.date(), type: z.enum(['midweek', 'weekend']) }))
      .mutation(async ({ input, ctx }) => {
        // Preserve existing JW.org integration logic
        const jwOrgData = await fetchJwOrgMeetingData(input.date, input.type);
        return jwOrgData;
      }),

    assignPart: adminProcedure
      .input(z.object({
        partId: z.string(),
        memberId: z.string(),
        assistantId: z.string().optional()
      }))
      .mutation(async ({ input, ctx }) => {
        return await ctx.db.meetingPart.update({
          where: { 
            id: input.partId,
            meeting: { congregationId: ctx.user.congregationId }
          },
          data: {
            assignedMemberId: input.memberId,
            assistantId: input.assistantId
          }
        });
      }),
  }),

  // Field service management routes
  fieldService: router({
    records: protectedProcedure
      .input(z.object({
        memberId: z.string().optional(),
        serviceYear: z.number().optional(),
        month: z.number().optional()
      }))
      .query(async ({ input, ctx }) => {
        const memberId = input.memberId || ctx.user.memberId;
        
        return await ctx.db.fieldServiceRecord.findMany({
          where: {
            memberId,
            congregationId: ctx.user.congregationId,
            serviceYear: input.serviceYear,
            date: input.month ? {
              gte: new Date(new Date().getFullYear(), input.month - 1, 1),
              lt: new Date(new Date().getFullYear(), input.month, 1)
            } : undefined
          },
          orderBy: { date: 'desc' }
        });
      }),

    create: protectedProcedure
      .input(fieldServiceSchema)
      .mutation(async ({ input, ctx }) => {
        return await ctx.db.fieldServiceRecord.create({
          data: {
            ...input,
            memberId: ctx.user.memberId,
            congregationId: ctx.user.congregationId,
            serviceYear: getServiceYear(input.date)
          }
        });
      }),

    monthlyReport: protectedProcedure
      .input(z.object({ month: z.number(), year: z.number() }))
      .query(async ({ input, ctx }) => {
        const records = await ctx.db.fieldServiceRecord.findMany({
          where: {
            memberId: ctx.user.memberId,
            congregationId: ctx.user.congregationId,
            date: {
              gte: new Date(input.year, input.month - 1, 1),
              lt: new Date(input.year, input.month, 1)
            }
          }
        });
        
        return {
          totalHours: records.reduce((sum, r) => sum + r.hours, 0),
          totalMinutes: records.reduce((sum, r) => sum + r.minutes, 0),
          activities: records.length,
          records
        };
      }),
  }),

  // External integrations
  external: router({
    jwOrg: router({
      fetchMeetingData: adminProcedure
        .input(z.object({ date: z.date(), language: z.string().default('es') }))
        .query(async ({ input }) => {
          // Preserve existing JW.org integration logic exactly
          return await fetchJwOrgMeetingData(input.date, input.language);
        }),

      fetchSongTitle: protectedProcedure
        .input(z.object({ songNumber: z.number(), language: z.string().default('es') }))
        .query(async ({ input }) => {
          return await fetchSongTitle(input.songNumber, input.language);
        }),
    }),
  }),
});

export type AppRouter = typeof appRouter;
```
