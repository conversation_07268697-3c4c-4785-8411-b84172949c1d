/**
 * Test Events Data Script
 * 
 * Creates sample events for testing the Events Management functionality.
 * This script adds realistic congregation events to test the mobile interface.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleEvents() {
  try {
    console.log('🎯 Creating sample events for testing...');

    // Sample events data matching typical congregation activities
    const sampleEvents = [
      {
        congregationId: '1441',
        title: 'ASAMBLEA REGIONAL',
        description: 'Asamblea regional anual con el tema "Mantengámonos Firmes en la Fe". Se espera la asistencia de todos los circuitos de la región.',
        eventDate: new Date('2025-06-15'),
        startTime: '09:30',
        endTime: '16:00',
        location: 'Centro de Convenciones de Miami',
        category: 'ASSEMBLY',
        isAllDay: false,
        isRecurring: false,
        visibility: 'ALL_MEMBERS',
        isActive: true,
      },
      {
        congregationId: '1441',
        title: 'VISITA DEL SUPERINTENDENTE DE CIRCUITO',
        description: 'Visita del superintendente de circuito. Reunión especial con los ancianos y siervos ministeriales.',
        eventDate: new Date('2025-05-10'),
        startTime: '19:30',
        endTime: '21:00',
        location: 'Salón del Reino local',
        category: 'SPECIAL_MEETING',
        isAllDay: false,
        isRecurring: false,
        visibility: 'ALL_MEMBERS',
        isActive: true,
      },
      {
        congregationId: '1441',
        title: 'ASAMBLEA DE CIRCUITO CON EL SUPERINTENDENTE DE CIRCUITO',
        description: 'Asamblea de circuito con presentaciones especiales y programa de entrenamiento.',
        eventDate: new Date('2025-03-29'),
        startTime: '09:00',
        endTime: '16:30',
        location: 'Salón de Asambleas de West Palm Beach',
        category: 'ASSEMBLY',
        isAllDay: false,
        isRecurring: false,
        visibility: 'ALL_MEMBERS',
        isActive: true,
      },
      {
        congregationId: '1441',
        title: 'Actividad de Servicio Especial',
        description: 'Actividad especial de predicación en territorio comercial.',
        eventDate: new Date('2025-02-15'),
        startTime: '09:00',
        endTime: '12:00',
        location: 'Centro Comercial Dadeland',
        category: 'SERVICE_ACTIVITY',
        isAllDay: false,
        isRecurring: false,
        visibility: 'ALL_MEMBERS',
        isActive: true,
      },
      {
        congregationId: '1441',
        title: 'Reunión de Entrenamiento para Ancianos',
        description: 'Reunión especial de entrenamiento para el cuerpo de ancianos.',
        eventDate: new Date('2025-02-08'),
        startTime: '14:00',
        endTime: '17:00',
        location: 'Salón del Reino local',
        category: 'TRAINING',
        isAllDay: false,
        isRecurring: false,
        visibility: 'ELDERS_ONLY',
        isActive: true,
      }
    ];

    // Create events
    for (const eventData of sampleEvents) {
      const event = await prisma.event.create({
        data: eventData,
      });
      console.log(`✅ Created event: ${event.title}`);
    }

    console.log('🎉 Sample events created successfully!');
    console.log(`📊 Total events created: ${sampleEvents.length}`);

  } catch (error) {
    console.error('❌ Error creating sample events:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  createSampleEvents()
    .then(() => {
      console.log('✨ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createSampleEvents };
