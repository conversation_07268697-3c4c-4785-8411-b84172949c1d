'use client';

/**
 * Territory Address Table Component
 *
 * Displays territory addresses in a table format with individual rows for each address.
 * Allows elders and ministerial servants to edit addresses.
 */

import React, { useState, useEffect } from 'react';
import { Territory } from '@/types/territories/territory';
import AddressRow from './AddressRow';

interface TerritoryAddressTableProps {
  territories: Territory[];
  userRole: string;
  onRefresh?: () => void;
}

interface ParsedAddress {
  address: string;
  notes?: string;
  territoryId: string;
  territoryNumber: string;
  street?: string;
  order?: number;
}

export default function TerritoryAddressTable({
  territories,
  userRole,
  onRefresh
}: TerritoryAddressTableProps) {
  const [addresses, setAddresses] = useState<ParsedAddress[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredAddresses, setFilteredAddresses] = useState<ParsedAddress[]>([]);
  const [actionMessage, setActionMessage] = useState<string>('');
  const [showStreets, setShowStreets] = useState(false);
  const [expandedRowIndex, setExpandedRowIndex] = useState<number | null>(null);
  const [lastUpdateTimestamp, setLastUpdateTimestamp] = useState<number>(0);

  // Parse addresses from territories
  useEffect(() => {
    const parsedAddresses: ParsedAddress[] = [];

    territories.forEach(territory => {
      if (territory.address) {
        // Split addresses by newlines
        const addressLines = territory.address.split('\n').filter(line => line.trim());

        // Parse notes if available
        const noteLines = territory.notes ? territory.notes.split('\n') : [];
        const notesMap = new Map<string, string>();

        noteLines.forEach(noteLine => {
          const colonIndex = noteLine.indexOf(':');
          if (colonIndex > 0) {
            const addressPart = noteLine.substring(0, colonIndex).trim();
            const notePart = noteLine.substring(colonIndex + 1).trim();
            notesMap.set(addressPart, notePart);
          }
        });

        addressLines.forEach((addressLine, index) => {
          const trimmedAddress = addressLine.trim();
          if (trimmedAddress) {
            // Extract street name from address (everything after the house number)
            const streetMatch = trimmedAddress.match(/^\d+[a-z]?\s+(.+?)(?:,|$)/i);
            const street = streetMatch ? streetMatch[1].trim() : null;

            parsedAddresses.push({
              address: trimmedAddress,
              notes: notesMap.get(trimmedAddress),
              territoryId: territory.id,
              territoryNumber: territory.territoryNumber,
              street: street,
              order: index // Preserve Excel order
            });
          }
        });
      }
    });

    // Sort by territory number and then by Excel order (preserve original order)
    parsedAddresses.sort((a, b) => {
      const territoryCompare = a.territoryNumber.localeCompare(b.territoryNumber, undefined, { numeric: true });
      if (territoryCompare !== 0) return territoryCompare;
      // Use Excel order if available, otherwise fall back to alphabetical
      if (a.order !== undefined && b.order !== undefined) {
        return a.order - b.order;
      }
      return a.address.localeCompare(b.address);
    });

    setAddresses(parsedAddresses);
  }, [territories]);

  // Filter addresses based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredAddresses(addresses);
    } else {
      const filtered = addresses.filter(addr =>
        addr.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        addr.territoryNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (addr.notes && addr.notes.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredAddresses(filtered);
    }
  }, [addresses, searchTerm]);

  const handleAddressUpdate = async (territoryId: string, updatedAddress: string, updatedNotes?: string) => {
    try {
      // Get authentication token
      const rawToken = localStorage.getItem('hermanos_token');
      if (!rawToken) {
        throw new Error('No authentication token found');
      }

      // Clean token - remove any existing Bearer prefix if present
      let token = rawToken.trim();
      if (token.toLowerCase().startsWith('bearer ')) {
        token = token.substring(7); // Remove "Bearer " prefix
      }

      console.log('Token debug - Raw:', rawToken.substring(0, 30) + '...');
      console.log('Token debug - Cleaned:', token.substring(0, 30) + '...');

      // Find the territory and update the specific address
      const territory = territories.find(t => t.id === territoryId);
      if (!territory) {
        throw new Error('Territory not found');
      }

      console.log('Address update requested:', {
        territoryId,
        updatedAddress,
        updatedNotes
      });

      // Update territory via API
      const response = await fetch(`/api/territories/${territoryId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          address: updatedAddress,
          notes: updatedNotes
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update territory');
      }

      // Refresh the parent component data to get updated territories
      if (onRefresh) {
        await onRefresh();
      }

    } catch (error) {
      console.error('Error updating address:', error);
      alert('Error al actualizar la dirección');
      throw error;
    }
  };

  const handleToggleExpand = (index: number) => {
    setExpandedRowIndex(expandedRowIndex === index ? null : index);
  };

  const handleAddNote = async (territoryId: string, address: string, note: string) => {
    try {

      // Find the territory and update the address with the new note
      const territory = territories.find(t => t.id === territoryId);
      if (!territory) return;

      // Update the territory notes - replace existing note for this address or add new one
      const existingNotes = territory.notes || '';
      const noteLines = existingNotes.split('\n').filter(line => line.trim());

      // Remove any existing note for this address
      const filteredNotes = noteLines.filter(line => !line.startsWith(`${address}:`));

      // Add the new note only if it's not empty
      let updatedNotes;
      if (note.trim()) {
        const newNoteEntry = `${address}: ${note}`;
        updatedNotes = [...filteredNotes, newNoteEntry].join('\n');
      } else {
        // If note is empty, just remove the existing note (don't add anything)
        updatedNotes = filteredNotes.join('\n');
      }

      // Call the update function and refresh data
      await handleAddressUpdate(territoryId, territory.address, updatedNotes);

      // No success messages - all updates are silent for clean UX

      // Refresh the parent component data to get updated territories
      if (onRefresh) {
        await onRefresh();
      }
    } catch (error) {
      console.error('Error updating note:', error);
      setActionMessage('Error al actualizar la nota');
      setTimeout(() => setActionMessage(''), 3000);
      throw error;
    }
  };

  const handleFieldServiceAction = async (territoryId: string, address: string, action: string) => {
    try {
      const actionLabels: { [key: string]: string } = {
        'at_home': 'En Casa',
        'not_home': 'No Estaba',
        'do_not_call': 'No Visitar',
        'testigo': 'Testigo',
        'perros_rejas': 'Perros/Rejas',
        'no_trespassing': 'No Traspasar'
      };

      const actionLabel = actionLabels[action] || action;

      // Add the action as a note to the address
      await handleAddNote(territoryId, address, actionLabel);

    } catch (error) {
      console.error('Error recording field service action:', error);
      setActionMessage('Error al registrar la acción');
      setTimeout(() => {
        setActionMessage('');
      }, 3000);
    }
  };

  return (
    <div className="space-y-4">
      {/* Toggle Controls - Icons Only */}
      <div className="flex items-center justify-end space-x-2">
        {/* Street Toggle */}
        <button
          onClick={() => setShowStreets(!showStreets)}
          className={`p-2 rounded-lg transition-colors ${
            showStreets ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
          } hover:bg-blue-200`}
          title={showStreets ? 'Ocultar calles' : 'Mostrar calles'}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
      </div>


      {/* Action Message */}
      {actionMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{actionMessage}</span>
        </div>
      )}

      {/* Address Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">

        {filteredAddresses.length === 0 ? (
          <div className="p-8 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m14 0V9a2 2 0 00-2-2M9 7h6m-6 4h6m-6 4h6" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No se encontraron direcciones</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Intenta con otros términos de búsqueda' : 'No hay direcciones disponibles'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredAddresses.map((addr, index) => (
              <AddressRow
                key={`${addr.territoryId}-${addr.address}-${addr.notes || 'no-note'}`}
                address={addr.address}
                notes={addr.notes}
                territoryId={addr.territoryId}
                territoryNumber={addr.territoryNumber}
                index={index}
                userRole={userRole}
                showStreet={showStreets}
                street={addr.street}
                isExpanded={expandedRowIndex === index}
                onAddressUpdate={handleAddressUpdate}
                onFieldServiceAction={handleFieldServiceAction}
                onToggleExpand={handleToggleExpand}
                onAddNote={handleAddNote}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
