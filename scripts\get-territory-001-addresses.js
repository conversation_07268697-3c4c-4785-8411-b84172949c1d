const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function getTerritory001Addresses() {
  try {
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        address: true,
        notes: true
      }
    });

    if (territory) {
      console.log('🏘️  Territory 001 Addresses:');
      console.log('=====================================');
      
      const addresses = territory.address.split('\n');
      addresses.forEach((addr, index) => {
        console.log(`${index + 1}. ${addr}`);
      });
      
      console.log('\n📝 Notes:');
      console.log('=====================================');
      if (territory.notes) {
        const notes = territory.notes.split('\n');
        notes.forEach(note => {
          console.log(`- ${note}`);
        });
      } else {
        console.log('No notes available');
      }
      
      console.log('\n📍 Geographic Analysis:');
      console.log('=====================================');
      
      // Analyze street patterns
      const streets = new Set();
      addresses.forEach(addr => {
        const match = addr.match(/\d+\s+(.+?),\s*Miami/);
        if (match) {
          streets.add(match[1]);
        }
      });
      
      console.log('Streets in Territory 001:');
      Array.from(streets).sort().forEach(street => {
        console.log(`  - ${street}`);
      });
      
    } else {
      console.log('Territory 001 not found');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getTerritory001Addresses();
