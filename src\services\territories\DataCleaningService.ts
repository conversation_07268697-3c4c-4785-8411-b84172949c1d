// Territory Data Cleaning Service
// Service for cleaning and standardizing territory data

import {
  AddressValidationConfig,
  TerritoryNumberValidationConfig,
  CleaningResult,
  DEFAULT_ADDRESS_CONFIG,
  DEFAULT_TERRITORY_NUMBER_CONFIG
} from '@/types/territories/validation';

export class DataCleaningService {
  private static addressConfig: AddressValidationConfig = DEFAULT_ADDRESS_CONFIG;
  private static territoryNumberConfig: TerritoryNumberValidationConfig = DEFAULT_TERRITORY_NUMBER_CONFIG;

  /**
   * Set address validation configuration
   */
  static setAddressConfig(config: Partial<AddressValidationConfig>): void {
    this.addressConfig = { ...DEFAULT_ADDRESS_CONFIG, ...config };
  }

  /**
   * Set territory number validation configuration
   */
  static setTerritoryNumberConfig(config: Partial<TerritoryNumberValidationConfig>): void {
    this.territoryNumberConfig = { ...DEFAULT_TERRITORY_NUMBER_CONFIG, ...config };
  }

  /**
   * Clean and standardize territory number
   */
  static cleanTerritoryNumber(territoryNumber: string): CleaningResult {
    const original = territoryNumber;
    const changes: CleaningResult['changes'] = [];
    let cleaned = territoryNumber;

    if (!cleaned) {
      return {
        original,
        cleaned: '',
        changes: [],
        confidence: 1
      };
    }

    // Trim whitespace
    const trimmed = cleaned.trim();
    if (trimmed !== cleaned) {
      changes.push({
        type: 'spacing',
        description: 'Removed leading/trailing whitespace',
        before: cleaned,
        after: trimmed
      });
      cleaned = trimmed;
    }

    // Standardize case if not case sensitive
    if (!this.territoryNumberConfig.caseSensitive) {
      const upperCased = cleaned.toUpperCase();
      if (upperCased !== cleaned) {
        changes.push({
          type: 'capitalization',
          description: 'Converted to uppercase',
          before: cleaned,
          after: upperCased
        });
        cleaned = upperCased;
      }
    }

    // Remove invalid characters based on pattern
    const allowedPattern = /^[A-Za-z0-9\-_]+$/;
    if (!allowedPattern.test(cleaned)) {
      const validCleaned = cleaned.replace(/[^A-Za-z0-9\-_]/g, '');
      if (validCleaned !== cleaned) {
        changes.push({
          type: 'format',
          description: 'Removed invalid characters',
          before: cleaned,
          after: validCleaned
        });
        cleaned = validCleaned;
      }
    }

    // Calculate confidence based on number of changes
    const confidence = changes.length === 0 ? 1 : Math.max(0.5, 1 - (changes.length * 0.2));

    return {
      original,
      cleaned,
      changes,
      confidence
    };
  }

  /**
   * Clean and standardize address
   */
  static cleanAddress(address: string): CleaningResult {
    const original = address;
    const changes: CleaningResult['changes'] = [];
    let cleaned = address;

    if (!cleaned) {
      return {
        original,
        cleaned: '',
        changes: [],
        confidence: 1
      };
    }

    // Trim whitespace
    cleaned = cleaned.trim();

    // Remove extra spaces
    if (this.addressConfig.removeExtraSpaces) {
      const spaceCleaned = cleaned.replace(/\s+/g, ' ');
      if (spaceCleaned !== cleaned) {
        changes.push({
          type: 'spacing',
          description: 'Removed extra spaces',
          before: cleaned,
          after: spaceCleaned
        });
        cleaned = spaceCleaned;
      }
    }

    // Standardize capitalization
    if (this.addressConfig.standardizeCapitalization) {
      const titleCased = this.toTitleCase(cleaned);
      if (titleCased !== cleaned) {
        changes.push({
          type: 'capitalization',
          description: 'Standardized capitalization',
          before: cleaned,
          after: titleCased
        });
        cleaned = titleCased;
      }
    }

    // Standardize punctuation
    if (this.addressConfig.standardizePunctuation) {
      const punctuationCleaned = this.standardizePunctuation(cleaned);
      if (punctuationCleaned !== cleaned) {
        changes.push({
          type: 'punctuation',
          description: 'Standardized punctuation',
          before: cleaned,
          after: punctuationCleaned
        });
        cleaned = punctuationCleaned;
      }
    }

    // Apply abbreviations
    const abbreviatedCleaned = this.applyAbbreviations(cleaned);
    if (abbreviatedCleaned !== cleaned) {
      changes.push({
        type: 'abbreviation',
        description: 'Applied standard abbreviations',
        before: cleaned,
        after: abbreviatedCleaned
      });
      cleaned = abbreviatedCleaned;
    }

    // Remove forbidden characters
    const forbiddenChars = this.addressConfig.forbiddenCharacters;
    let forbiddenCleaned = cleaned;

    for (const char of forbiddenChars) {
      const escapedChar = char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      forbiddenCleaned = forbiddenCleaned.replace(new RegExp(escapedChar, 'g'), '');
    }

    if (forbiddenCleaned !== cleaned) {
      changes.push({
        type: 'format',
        description: 'Removed forbidden characters',
        before: cleaned,
        after: forbiddenCleaned
      });
      cleaned = forbiddenCleaned;
    }

    // Calculate confidence based on number of changes
    const confidence = changes.length === 0 ? 1 : Math.max(0.6, 1 - (changes.length * 0.15));

    return {
      original,
      cleaned,
      changes,
      confidence
    };
  }

  /**
   * Convert string to title case
   */
  private static toTitleCase(str: string): string {
    const exceptions = ['a', 'an', 'and', 'as', 'at', 'but', 'by', 'for', 'if', 'in', 'nor', 'of', 'on', 'or', 'so', 'the', 'to', 'up', 'yet'];

    return str.toLowerCase().replace(/\w\S*/g, (txt, offset) => {
      // Always capitalize first word
      if (offset === 0) {
        return txt.charAt(0).toUpperCase() + txt.substr(1);
      }

      // Don't capitalize exceptions unless they're the first word
      if (exceptions.includes(txt.toLowerCase())) {
        return txt.toLowerCase();
      }

      return txt.charAt(0).toUpperCase() + txt.substr(1);
    });
  }

  /**
   * Standardize punctuation in address
   */
  private static standardizePunctuation(address: string): string {
    return address
      .replace(/\s*,\s*/g, ', ') // Standardize comma spacing
      .replace(/\s*\.\s*/g, '. ') // Standardize period spacing
      .replace(/\s*;\s*/g, '; ') // Standardize semicolon spacing
      .replace(/\s*:\s*/g, ': ') // Standardize colon spacing
      .replace(/\s+/g, ' ') // Remove extra spaces
      .trim();
  }

  /**
   * Apply standard abbreviations to address
   */
  private static applyAbbreviations(address: string): string {
    let result = address;

    for (const [full, abbrev] of Object.entries(this.addressConfig.abbreviations)) {
      // Create regex to match full word (case insensitive)
      const regex = new RegExp(`\\b${full}\\b`, 'gi');
      result = result.replace(regex, abbrev);
    }

    return result;
  }

  /**
   * Clean notes field
   */
  static cleanNotes(notes: string): CleaningResult {
    const original = notes;
    const changes: CleaningResult['changes'] = [];
    let cleaned = notes;

    if (!cleaned) {
      return {
        original,
        cleaned: '',
        changes: [],
        confidence: 1
      };
    }

    // Trim whitespace
    const trimmed = cleaned.trim();
    if (trimmed !== cleaned) {
      changes.push({
        type: 'spacing',
        description: 'Removed leading/trailing whitespace',
        before: cleaned,
        after: trimmed
      });
      cleaned = trimmed;
    }

    // Remove extra spaces but preserve single line breaks
    const spaceCleaned = cleaned.replace(/[ \t]+/g, ' ').replace(/\n{3,}/g, '\n\n');
    if (spaceCleaned !== cleaned) {
      changes.push({
        type: 'spacing',
        description: 'Cleaned up spacing and line breaks',
        before: cleaned,
        after: spaceCleaned
      });
      cleaned = spaceCleaned;
    }

    // Remove forbidden characters
    const forbiddenPattern = this.addressConfig.forbiddenCharacters.map(char =>
      char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    ).join('|');

    if (forbiddenPattern) {
      const forbiddenCleaned = cleaned.replace(new RegExp(forbiddenPattern, 'g'), '');
      if (forbiddenCleaned !== cleaned) {
        changes.push({
          type: 'format',
          description: 'Removed forbidden characters',
          before: cleaned,
          after: forbiddenCleaned
        });
        cleaned = forbiddenCleaned;
      }
    }

    const confidence = changes.length === 0 ? 1 : Math.max(0.7, 1 - (changes.length * 0.1));

    return {
      original,
      cleaned,
      changes,
      confidence
    };
  }

  /**
   * Clean all territory data fields
   */
  static cleanTerritoryData(data: {
    territoryNumber: string;
    address: string;
    notes?: string;
  }): {
    territoryNumber: CleaningResult;
    address: CleaningResult;
    notes?: CleaningResult;
  } {
    const result = {
      territoryNumber: this.cleanTerritoryNumber(data.territoryNumber),
      address: this.cleanAddress(data.address),
      notes: data.notes ? this.cleanNotes(data.notes) : undefined
    };

    return result;
  }

  /**
   * Get cleaning statistics for a batch of territories
   */
  static getCleaningStatistics(results: CleaningResult[]): {
    totalProcessed: number;
    totalChanges: number;
    changesByType: Record<string, number>;
    averageConfidence: number;
    itemsChanged: number;
  } {
    const totalProcessed = results.length;
    let totalChanges = 0;
    const changesByType: Record<string, number> = {};
    let totalConfidence = 0;
    let itemsChanged = 0;

    for (const result of results) {
      totalConfidence += result.confidence;

      if (result.changes.length > 0) {
        itemsChanged++;
        totalChanges += result.changes.length;

        for (const change of result.changes) {
          changesByType[change.type] = (changesByType[change.type] || 0) + 1;
        }
      }
    }

    return {
      totalProcessed,
      totalChanges,
      changesByType,
      averageConfidence: totalProcessed > 0 ? totalConfidence / totalProcessed : 0,
      itemsChanged
    };
  }
}
