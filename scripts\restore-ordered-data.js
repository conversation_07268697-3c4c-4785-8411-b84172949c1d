#!/usr/bin/env node

/**
 * Ordered Data Restoration
 * 
 * This script restores data in the correct order to satisfy foreign key constraints.
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function restoreOrderedData() {
    console.log('🔄 RESTORING DATA IN CORRECT ORDER...');
    console.log('');

    let client;

    try {
        // 1. Check if backup file exists
        const backupFile = path.join(__dirname, '../hermanos-07-25-25E.sql');
        if (!fs.existsSync(backupFile)) {
            throw new Error('Backup file hermanos-07-25-25E.sql not found');
        }
        console.log('✅ Backup file found: hermanos-07-25-25E.sql');

        // 2. Read backup file
        console.log('📖 Reading backup file...');
        const backupContent = fs.readFileSync(backupFile, 'utf8');
        
        // 3. Parse COPY statements and data
        const copyBlocks = parseCopyBlocks(backupContent);
        console.log(`   📊 Found ${copyBlocks.length} COPY data blocks`);

        // 4. Connect to hermanos database
        console.log('🔌 Connecting to hermanos database...');
        client = new Client({
            host: 'localhost',
            port: 5432,
            user: 'mywebsites',
            password: 'password',
            database: 'hermanos'
        });

        await client.connect();
        console.log('✅ Connected to hermanos database');

        // 5. Clear existing data first
        console.log('🗑️  Clearing existing data...');
        await clearExistingData(client);

        // 6. Define insertion order based on dependencies
        const insertionOrder = [
            '_prisma_migrations',
            'health_checks',
            'roles',
            'congregations',
            'members',
            'congregation_settings',
            'service_groups',
            'songs',
            'special_songs',
            'tasks',
            'events',
            'letters',
            'territories',
            'pin_settings',
            'elder_permissions',
            'section_assignments',
            'assignment_history',
            'member_change_history',
            'pin_change_history',
            'temporary_pins',
            'account_lockouts',
            'security_audit_events',
            'field_service_records',
            'task_assignments',
            'midweek_meetings',
            'midweek_meeting_parts',
            'weekend_meetings',
            'weekend_meeting_parts',
            'document_folders',
            'document_access_logs',
            'document_comments',
            'document_workflows',
            'communication_preferences',
            'communication_templates',
            'notifications',
            'permission_audit_logs'
        ];

        // 7. Execute insertions in order
        console.log('📥 Executing insertions in dependency order...');
        
        let successCount = 0;
        let errorCount = 0;
        let totalRows = 0;

        for (const tableName of insertionOrder) {
            const block = copyBlocks.find(b => b.tableName === tableName);
            
            if (!block) {
                console.log(`   ⏭️  ${tableName}: No data block found`);
                continue;
            }

            try {
                console.log(`   🔄 Processing ${tableName} (${block.rows.length} rows)...`);
                
                if (block.rows.length > 0) {
                    // Convert COPY data to INSERT statements
                    const insertStatements = convertCopyToInserts(block);
                    
                    for (const insertSql of insertStatements) {
                        await client.query(insertSql);
                    }
                    
                    totalRows += block.rows.length;
                    successCount++;
                    console.log(`   ✅ ${tableName}: ${block.rows.length} rows inserted`);
                } else {
                    console.log(`   ⏭️  ${tableName}: No data to insert`);
                }
                
            } catch (error) {
                errorCount++;
                console.log(`   ❌ ${tableName}: ${error.message.substring(0, 100)}...`);
                
                // For debugging, show the first failed insert
                if (block.rows.length > 0) {
                    const firstInsert = convertCopyToInserts({...block, rows: [block.rows[0]]});
                    console.log(`   🔍 First insert: ${firstInsert[0].substring(0, 200)}...`);
                }
            }
        }

        console.log(`✅ Ordered data restoration completed:`);
        console.log(`   📊 Successful tables: ${successCount}`);
        console.log(`   ❌ Failed tables: ${errorCount}`);
        console.log(`   📊 Total rows inserted: ${totalRows}`);

        // 8. Verify restoration
        console.log('🔍 Verifying restoration...');
        const verificationResults = await verifyRestoration(client);
        
        console.log('');
        console.log('🎉 DATA RESTORATION SUCCESSFUL!');
        console.log('');
        console.log('📊 RESTORED DATA:');
        console.log(`   📋 Congregations: ${verificationResults.congregations}`);
        console.log(`   👥 Members: ${verificationResults.members}`);
        console.log(`   ⚙️  Settings: ${verificationResults.settings}`);
        console.log(`   🎵 Songs: ${verificationResults.songs}`);
        console.log(`   📋 Tasks: ${verificationResults.tasks}`);
        console.log(`   📧 Letters: ${verificationResults.letters}`);
        console.log(`   📅 Events: ${verificationResults.events}`);

        // 9. Get sample login data
        if (verificationResults.congregations > 0) {
            console.log('');
            console.log('📋 SAMPLE LOGIN CREDENTIALS:');
            
            const sampleCongregation = await client.query('SELECT id, name FROM congregations LIMIT 1;');
            if (sampleCongregation.rows.length > 0) {
                console.log(`   Congregation ID: ${sampleCongregation.rows[0].id}`);
                console.log(`   Congregation Name: ${sampleCongregation.rows[0].name}`);
            }
            
            if (verificationResults.members > 0) {
                const sampleMember = await client.query('SELECT email, name, role FROM members WHERE is_active = true LIMIT 1;');
                if (sampleMember.rows.length > 0) {
                    console.log(`   Sample Member: ${sampleMember.rows[0].email}`);
                    console.log(`   Member Name: ${sampleMember.rows[0].name}`);
                    console.log(`   Member Role: ${sampleMember.rows[0].role}`);
                }
            }
        }
        
        console.log('');
        console.log('🎯 NEXT STEPS:');
        console.log('   1. Start the development server: npm run dev');
        console.log('   2. Test authentication with restored data');
        console.log('   3. All original data should now be available');

    } catch (error) {
        console.error('❌ Error during data restoration:', error);
        throw error;
    } finally {
        if (client) {
            await client.end();
        }
    }
}

async function clearExistingData(client) {
    const tables = [
        'account_lockouts',
        'security_audit_events',
        'temporary_pins',
        'pin_change_history',
        'member_change_history',
        'assignment_history',
        'section_assignments',
        'elder_permissions',
        'task_assignments',
        'field_service_records',
        'weekend_meeting_parts',
        'weekend_meetings',
        'midweek_meeting_parts',
        'midweek_meetings',
        'letters',
        'events',
        'tasks',
        'territories',
        'service_groups',
        'congregation_settings',
        'members',
        'congregations',
        'roles',
        'songs',
        'special_songs',
        'pin_settings',
        'health_checks',
        '_prisma_migrations'
    ];

    for (const table of tables) {
        try {
            await client.query(`DELETE FROM ${table};`);
        } catch (error) {
            // Ignore errors for tables that don't exist
        }
    }
}

function parseCopyBlocks(content) {
    const lines = content.split('\n');
    const copyBlocks = [];
    let currentBlock = null;
    let inCopyData = false;

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // Check for COPY statement
        if (line.startsWith('COPY ')) {
            const match = line.match(/COPY\s+public\.(\w+)\s*\((.*?)\)\s+FROM\s+stdin;/);
            if (match) {
                currentBlock = {
                    tableName: match[1],
                    columns: match[2].split(',').map(col => col.trim().replace(/"/g, '')),
                    rows: []
                };
                inCopyData = true;
                continue;
            }
        }
        
        // Check for end of COPY data
        if (inCopyData && line.trim() === '\\.') {
            if (currentBlock) {
                copyBlocks.push(currentBlock);
                currentBlock = null;
            }
            inCopyData = false;
            continue;
        }
        
        // Collect data rows
        if (inCopyData && currentBlock && line.trim().length > 0) {
            currentBlock.rows.push(line);
        }
    }
    
    return copyBlocks;
}

function convertCopyToInserts(block) {
    const insertStatements = [];
    
    for (const row of block.rows) {
        // Parse tab-separated values
        const values = row.split('\t');
        
        // Escape and format values
        const formattedValues = values.map(value => {
            if (value === '\\N') {
                return 'NULL';
            }
            
            // Escape single quotes and backslashes
            const escaped = value.replace(/\\/g, '\\\\').replace(/'/g, "''");
            return `'${escaped}'`;
        });
        
        const insertSql = `INSERT INTO ${block.tableName} (${block.columns.join(', ')}) VALUES (${formattedValues.join(', ')});`;
        insertStatements.push(insertSql);
    }
    
    return insertStatements;
}

async function verifyRestoration(client) {
    try {
        const tables = [
            'congregations', 
            'members', 
            'congregation_settings', 
            'songs', 
            'tasks',
            'letters',
            'events',
            'service_groups',
            'roles'
        ];
        const results = {};
        
        for (const table of tables) {
            try {
                const result = await client.query(`SELECT COUNT(*) FROM ${table};`);
                results[table] = result.rows[0].count;
            } catch (error) {
                results[table] = 'Table not found';
            }
        }
        
        return {
            congregations: results.congregations,
            members: results.members,
            settings: results.congregation_settings,
            songs: results.songs,
            tasks: results.tasks,
            letters: results.letters,
            events: results.events,
            serviceGroups: results.service_groups,
            roles: results.roles
        };
    } catch (error) {
        console.log('   ⚠️  Verification failed, but restoration may have succeeded');
        return {
            congregations: 'Unknown',
            members: 'Unknown',
            settings: 'Unknown',
            songs: 'Unknown',
            tasks: 'Unknown',
            letters: 'Unknown',
            events: 'Unknown'
        };
    }
}

// Run the restoration
restoreOrderedData()
    .catch((error) => {
        console.error('Failed to restore ordered data:', error);
        process.exit(1);
    });
