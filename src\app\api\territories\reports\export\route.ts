import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { reportType } = await request.json();

    // For now, return a simple response indicating PDF export is not yet implemented
    // In a full implementation, you would use a library like jsPDF or Puppeteer
    
    const pdfContent = `
      Reporte de Territorios - ${reportType}
      Congregación: Coral Oeste
      Fecha: ${new Date().toLocaleDateString('es-ES')}
      
      Esta funcionalidad de exportación PDF estará disponible próximamente.
      
      Tipo de reporte solicitado: ${reportType}
    `;

    // Create a simple text response for now
    const response = new NextResponse(pdfContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Content-Disposition': `attachment; filename="reporte-territorios-${reportType}-${new Date().toISOString().split('T')[0]}.txt"`
      }
    });

    return response;

  } catch (error) {
    console.error('Error exporting report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
