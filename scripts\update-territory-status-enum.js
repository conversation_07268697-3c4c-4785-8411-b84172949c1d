#!/usr/bin/env node

/**
 * Update Territory Status Enum
 * 
 * This script updates the territory status enum from 'out_of_service' to 'unavailable'
 * using raw SQL to handle the enum migration properly.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateTerritoryStatusEnum() {
  try {
    console.log('🔄 Updating Territory Status Enum');
    console.log('==================================\n');

    // First, check if there are any territories with 'out_of_service' status
    const outOfServiceCount = await prisma.$queryRaw`
      SELECT COUNT(*) as count 
      FROM territories 
      WHERE status = 'out_of_service'
    `;
    
    console.log(`📊 Found ${outOfServiceCount[0].count} territories with 'out_of_service' status`);

    if (parseInt(outOfServiceCount[0].count) > 0) {
      // Update any existing 'out_of_service' values to 'unavailable' using raw SQL
      console.log('🔄 Updating existing out_of_service values...');
      
      await prisma.$executeRaw`
        UPDATE territories 
        SET status = 'unavailable'::text 
        WHERE status = 'out_of_service'
      `;
      
      console.log('✅ Updated existing out_of_service values to unavailable');
    }

    // Now check current status distribution
    const statusCounts = await prisma.$queryRaw`
      SELECT status, COUNT(*) as count 
      FROM territories 
      GROUP BY status
    `;

    console.log('\n📊 Current Territory Status Distribution:');
    statusCounts.forEach(row => {
      console.log(`   ${row.status}: ${row.count} territories`);
    });

    console.log('\n✅ Territory status enum update completed!');
    console.log('   Now you can run: npx prisma db push');

  } catch (error) {
    console.error('❌ Error updating territory status enum:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the update
if (require.main === module) {
  updateTerritoryStatusEnum().catch(console.error);
}

module.exports = { updateTerritoryStatusEnum };
