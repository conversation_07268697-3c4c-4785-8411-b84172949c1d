# Story 7.1: Territory Management System

**Epic:** Epic 7: Advanced Field Service & Territory Management  
**Story Points:** 13  
**Priority:** High  
**Status:** Draft  

## Story

As a service overseer,
I want a comprehensive territory management system with digital territory cards and assignment tracking,
so that I can efficiently manage territory coverage and ensure thorough witnessing in our assigned area.

## Acceptance Criteria

1. **Digital territory card system with map integration and boundary definitions**
   - Interactive digital territory cards with detailed boundary mapping and geographic visualization
   - Integration with local mapping services (Google Maps, OpenStreetMap) for accurate territory boundaries
   - Territory card customization with congregation-specific information and special instructions
   - Offline territory access for publishers working in areas with limited internet connectivity

2. **Territory assignment tracking with member assignment history and completion status**
   - Comprehensive assignment tracking system with member assignment history and performance metrics
   - Territory checkout/checkin workflow with assignment duration tracking and completion validation
   - Assignment conflict detection and resolution with availability-based assignment suggestions
   - Assignment history analytics with member performance tracking and territory coverage optimization

3. **Territory coverage analytics with last-worked dates and frequency tracking**
   - Advanced analytics dashboard showing territory coverage patterns and last-worked dates
   - Coverage frequency analysis with recommendations for territory rotation and optimization
   - Territory activity heatmaps showing high-activity areas and coverage gaps
   - Predictive analytics for territory assignment planning and coverage optimization

4. **Territory sharing capabilities between service groups and congregations**
   - Inter-service group territory sharing with coordination workflows and approval processes
   - Cross-congregation territory coordination for border areas and shared territories
   - Territory lending system with temporary assignment tracking and return workflows
   - Collaborative territory management with shared notes and coordination features

5. **Mobile-friendly territory access for publishers in the field**
   - Mobile-optimized territory interface with offline capability and GPS integration
   - Real-time territory navigation with turn-by-turn directions and location tracking
   - Field service logging directly from territory interface with time and activity tracking
   - Mobile territory notes and return visit management with photo and contact integration

6. **Territory completion reporting and documentation system**
   - Comprehensive territory completion reporting with detailed coverage documentation
   - Return visit tracking and follow-up coordination with contact management
   - Territory quality assessment with coverage thoroughness and effectiveness metrics
   - Automated reporting for service overseer review and congregation coordination

7. **Integration with local mapping services for accurate territory boundaries**
   - Real-time mapping integration with boundary accuracy validation and updates
   - GPS coordinate management for precise territory boundary definition
   - Address validation and geocoding for accurate territory assignment
   - Mapping service fallback options for reliability and offline functionality

## Dev Notes

### Technical Architecture

**Frontend Components:**
- `TerritoryManager.tsx` - Main territory management interface with map integration
- `DigitalTerritoryCard.tsx` - Interactive territory card with boundary visualization
- `TerritoryAssignmentTracker.tsx` - Assignment tracking and history management
- `TerritoryAnalyticsDashboard.tsx` - Coverage analytics and reporting interface
- `MobileTerritoryAccess.tsx` - Mobile-optimized territory interface with GPS
- `TerritorySharing.tsx` - Inter-group and inter-congregation sharing interface
- `TerritoryReporting.tsx` - Completion reporting and documentation system

**Backend Services:**
- `territory-management-service.ts` - Core territory management and assignment logic
- `territory-mapping-service.ts` - Map integration and boundary management
- `territory-assignment-service.ts` - Assignment tracking and conflict resolution
- `territory-analytics-service.ts` - Coverage analytics and optimization algorithms
- `territory-sharing-service.ts` - Inter-group and inter-congregation coordination
- `mobile-territory-service.ts` - Mobile interface and offline synchronization
- `territory-reporting-service.ts` - Completion reporting and documentation

**Database Tables:**
- `territories` - Territory definitions with boundaries and metadata
- `territory_assignments` - Assignment tracking with member and date information
- `territory_coverage` - Coverage tracking with activity and completion data
- `territory_boundaries` - Geographic boundary definitions with coordinate data
- `territory_sharing` - Inter-group and inter-congregation sharing records
- `territory_reports` - Completion reports and documentation

### API Endpoints (tRPC)

```typescript
// Territory management routes
territoryManagement: router({
  createTerritory: adminProcedure
    .input(z.object({
      name: z.string(),
      description: z.string(),
      boundaries: z.array(z.object({
        latitude: z.number(),
        longitude: z.number()
      })),
      specialInstructions: z.string().optional(),
      difficulty: z.enum(['easy', 'medium', 'hard']),
      estimatedHours: z.number()
    }))
    .mutation(async ({ input, ctx }) => {
      return await territoryManagementService.createTerritory(
        input,
        ctx.user.congregationId
      );
    }),

  assignTerritory: adminProcedure
    .input(z.object({
      territoryId: z.string(),
      memberId: z.string(),
      assignmentDate: z.date(),
      expectedReturnDate: z.date(),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await territoryAssignmentService.assignTerritory(
        input.territoryId,
        input.memberId,
        input.assignmentDate,
        input.expectedReturnDate,
        ctx.user.congregationId,
        input.notes
      );
    }),

  getTerritoryAnalytics: protectedProcedure
    .input(z.object({
      territoryId: z.string().optional(),
      dateRange: z.object({
        start: z.date(),
        end: z.date()
      }),
      analyticsType: z.enum(['coverage', 'frequency', 'performance'])
    }))
    .query(async ({ input, ctx }) => {
      return await territoryAnalyticsService.getAnalytics(
        input.territoryId,
        input.dateRange,
        input.analyticsType,
        ctx.user.congregationId
      );
    }),

  shareTerritory: adminProcedure
    .input(z.object({
      territoryId: z.string(),
      targetServiceGroup: z.string().optional(),
      targetCongregation: z.string().optional(),
      sharingType: z.enum(['temporary', 'permanent', 'collaborative']),
      duration: z.number().optional(),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await territorySharingService.shareTerritory(
        input.territoryId,
        input.targetServiceGroup,
        input.targetCongregation,
        input.sharingType,
        ctx.user.congregationId,
        input.duration,
        input.notes
      );
    }),

  submitTerritoryReport: protectedProcedure
    .input(z.object({
      assignmentId: z.string(),
      completionStatus: z.enum(['completed', 'partial', 'not_worked']),
      coveragePercentage: z.number().min(0).max(100),
      returnVisits: z.number(),
      placements: z.number(),
      notes: z.string().optional(),
      recommendations: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await territoryReportingService.submitReport(
        input.assignmentId,
        input,
        ctx.user.memberId,
        ctx.user.congregationId
      );
    })
})
```

### Data Models

```typescript
interface Territory {
  id: string;
  congregationId: string;
  name: string;
  description: string;
  boundaries: {
    latitude: number;
    longitude: number;
  }[];
  specialInstructions?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedHours: number;
  isActive: boolean;
  lastWorked?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface TerritoryAssignment {
  id: string;
  territoryId: string;
  congregationId: string;
  memberId: string;
  assignmentDate: Date;
  expectedReturnDate: Date;
  actualReturnDate?: Date;
  status: 'assigned' | 'in_progress' | 'completed' | 'overdue';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TerritoryCoverage {
  id: string;
  assignmentId: string;
  territoryId: string;
  congregationId: string;
  workDate: Date;
  coveragePercentage: number;
  hoursWorked: number;
  returnVisits: number;
  placements: number;
  activityType: 'door_to_door' | 'street_witnessing' | 'return_visits' | 'bible_studies';
  notes?: string;
  createdAt: Date;
}

interface TerritorySharing {
  id: string;
  territoryId: string;
  sourceCongregationId: string;
  targetServiceGroup?: string;
  targetCongregationId?: string;
  sharingType: 'temporary' | 'permanent' | 'collaborative';
  startDate: Date;
  endDate?: Date;
  status: 'pending' | 'approved' | 'active' | 'completed' | 'cancelled';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Critical Implementation Requirements

1. **Multi-Tenant Data Isolation**: Every database query must include congregation_id filtering
2. **Type Safety Enforcement**: All API calls must use tRPC procedures with Zod validation
3. **Authentication Required**: All protected routes must use authentication middleware
4. **Database-First Testing**: Use real database with comprehensive test data for territories
5. **Local Infrastructure Only**: Use local PostgreSQL database and local file storage
6. **Mobile Optimization**: Ensure excellent mobile performance with offline capabilities

### Testing Requirements

**Unit Tests:**
- Territory management algorithms with boundary validation
- Assignment tracking logic with conflict detection
- Coverage analytics calculations with various data scenarios
- Territory sharing workflows with approval processes

**Integration Tests:**
- Complete territory management workflow from creation to completion
- Multi-congregation territory sharing and coordination
- Mobile territory access with offline synchronization
- Mapping service integration with fallback mechanisms

**E2E Tests:**
- Full territory assignment workflow from assignment to completion
- Mobile territory interface with GPS navigation and field service logging
- Territory analytics dashboard with coverage visualization
- Inter-congregation territory sharing coordination

## Testing

### Test Data Requirements

- Seed database with diverse territory configurations and boundary definitions
- Include complex assignment scenarios with overlapping territories
- Test data should include various coverage patterns and completion rates
- Sample geographic data for mapping integration testing

### Validation Scenarios

- Test territory management with large numbers of territories and assignments
- Validate mapping integration accuracy with various geographic locations
- Test mobile interface performance with offline scenarios
- Verify territory sharing workflows across multiple congregations

## Definition of Done

- [ ] Digital territory card system with map integration implemented
- [ ] Territory assignment tracking with member history functional
- [ ] Territory coverage analytics with frequency tracking complete
- [ ] Territory sharing capabilities between groups implemented
- [ ] Mobile-friendly territory access for field service working
- [ ] Territory completion reporting and documentation functional
- [ ] Integration with local mapping services complete
- [ ] All unit tests pass with real database data
- [ ] Integration tests validate multi-congregation isolation
- [ ] E2E tests confirm complete territory management workflow
- [ ] Code review completed and approved
- [ ] Documentation updated with territory management features

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: BMad Master Task Executor
- Date: 2025-01-24

### Debug Log References
- None yet

### Completion Notes
- Story created with comprehensive territory management system
- Advanced mapping integration with offline mobile capabilities
- Multi-congregation territory sharing and coordination features
- Complete API specification with tRPC procedures for territory management
- Testing requirements defined with geographic and mobile scenario validation

### File List
- docs/stories/7.1.story.md (created)

### Change Log
- 2025-01-24: Initial story creation with territory management system specification
