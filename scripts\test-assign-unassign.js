#!/usr/bin/env node

/**
 * Test Territory Assignment and Unassignment Functionality
 *
 * This script tests the complete assign/unassign workflow to ensure
 * both functionalities work as expected.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Test assignment functionality
 */
async function testAssignmentFunctionality() {
  try {
    console.log('🧪 Testing Assignment Functionality');
    console.log('===================================\n');

    // Get a member without current assignments
    const memberWithoutAssignments = await prisma.member.findFirst({
      where: {
        congregationId: '1441',
        territoryAssignments: {
          none: {
            status: 'active'
          }
        }
      },
      select: {
        id: true,
        name: true,
        role: true
      }
    });

    if (!memberWithoutAssignments) {
      console.log('⚠️  No members without active assignments found for testing');
      return false;
    }

    // Get available territories
    const availableTerritories = await prisma.territory.findMany({
      where: {
        congregationId: '1441',
        status: 'available'
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true
      },
      take: 2 // Test with 2 territories
    });

    if (availableTerritories.length === 0) {
      console.log('⚠️  No available territories found for assignment test');
      return false;
    }

    console.log(`📋 Testing assignment:`);
    console.log(`   Member: ${memberWithoutAssignments.name} (${memberWithoutAssignments.role})`);
    console.log(`   Territories to assign: ${availableTerritories.length}`);

    availableTerritories.forEach((territory, index) => {
      console.log(`     ${index + 1}. Territory ${territory.territoryNumber} - ${territory.address}`);
    });

    // Test assignment workflow structure
    console.log('\n✅ Assignment test structure verified:');
    console.log('   - Member available for assignment');
    console.log('   - Territories available for assignment');
    console.log('   - Multiple territory assignment supported');
    console.log('   - Ready for API assignment calls');

    return {
      member: memberWithoutAssignments,
      territories: availableTerritories
    };

  } catch (error) {
    console.error('❌ Error testing assignment functionality:', error);
    return false;
  }
}

/**
 * Test unassignment functionality
 */
async function testUnassignmentFunctionality() {
  try {
    console.log('\n🧪 Testing Unassignment Functionality');
    console.log('=====================================\n');

    // Get current active assignments
    const activeAssignments = await prisma.territoryAssignment.findMany({
      where: {
        congregationId: '1441',
        status: 'active'
      },
      include: {
        member: {
          select: {
            name: true,
            role: true
          }
        },
        territory: {
          select: {
            territoryNumber: true,
            address: true
          }
        }
      },
      take: 3 // Test with first 3 assignments
    });

    console.log(`📊 Active assignments available for unassignment: ${activeAssignments.length}`);

    if (activeAssignments.length === 0) {
      console.log('⚠️  No active assignments found for unassignment test');
      return false;
    }

    console.log('\n📋 Assignments available for unassignment:');
    activeAssignments.forEach((assignment, index) => {
      console.log(`   ${index + 1}. ${assignment.member.name} → Territory ${assignment.territory.territoryNumber}`);
      console.log(`      Assignment ID: ${assignment.id}`);
      console.log(`      Assigned: ${assignment.assignedAt.toLocaleDateString()}`);
    });

    console.log('\n✅ Unassignment test structure verified:');
    console.log('   - Active assignments available');
    console.log('   - Assignment IDs accessible');
    console.log('   - Member and territory information available');
    console.log('   - Ready for API unassignment calls');

    return activeAssignments;

  } catch (error) {
    console.error('❌ Error testing unassignment functionality:', error);
    return false;
  }
}

/**
 * Test territory status changes
 */
async function testTerritoryStatusChanges() {
  try {
    console.log('\n🧪 Testing Territory Status Changes');
    console.log('===================================\n');

    // Check territory status distribution
    const territoryStats = await prisma.territory.groupBy({
      by: ['status'],
      where: {
        congregationId: '1441'
      },
      _count: {
        id: true
      }
    });

    console.log('📊 Current Territory Status Distribution:');
    territoryStats.forEach(stat => {
      console.log(`   ${stat.status}: ${stat._count.id} territories`);
    });

    // Verify that assigned territories have corresponding active assignments
    const assignedTerritories = await prisma.territory.findMany({
      where: {
        congregationId: '1441',
        status: 'assigned'
      },
      include: {
        assignments: {
          where: {
            status: 'active'
          },
          include: {
            member: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    console.log(`\n🔍 Verification of assigned territories:`);
    console.log(`   Territories with status 'assigned': ${assignedTerritories.length}`);

    let consistentAssignments = 0;
    assignedTerritories.forEach(territory => {
      const hasActiveAssignment = territory.assignments.length > 0;
      if (hasActiveAssignment) {
        consistentAssignments++;
        const assignedTo = territory.assignments[0].member.name;
        console.log(`   ✅ Territory ${territory.territoryNumber} → ${assignedTo}`);
      } else {
        console.log(`   ❌ Territory ${territory.territoryNumber} → No active assignment found!`);
      }
    });

    const consistencyRate = assignedTerritories.length > 0
      ? Math.round((consistentAssignments / assignedTerritories.length) * 100)
      : 100;

    console.log(`\n📈 Data Consistency: ${consistencyRate}% (${consistentAssignments}/${assignedTerritories.length})`);

    // Consider 80% or higher as acceptable (some territories might be in transition)
    return consistencyRate >= 80;

  } catch (error) {
    console.error('❌ Error testing territory status changes:', error);
    return false;
  }
}

/**
 * Test API endpoint availability
 */
async function testAPIEndpoints() {
  try {
    console.log('\n🧪 Testing API Endpoint Availability');
    console.log('====================================\n');

    // Note: This test only verifies the database structure is ready for API calls
    // Actual API testing would require authentication tokens

    console.log('📋 API Endpoints to verify:');
    console.log('   POST /api/territories/assign - Territory assignment');
    console.log('   DELETE /api/territories/assignments/[id] - Territory unassignment');
    console.log('   GET /api/territories/assignments - Get assignments data');
    console.log('   GET /api/territories/assignments?type=members - Get members with assignments');

    // Verify database structure supports API operations
    const dbStructureChecks = [
      {
        name: 'Members table accessible',
        test: async () => {
          const count = await prisma.member.count({ where: { congregationId: '1441' } });
          return count > 0;
        }
      },
      {
        name: 'Territories table accessible',
        test: async () => {
          const count = await prisma.territory.count({ where: { congregationId: '1441' } });
          return count > 0;
        }
      },
      {
        name: 'Territory assignments table accessible',
        test: async () => {
          const count = await prisma.territoryAssignment.count({ where: { congregationId: '1441' } });
          return count >= 0; // Can be 0 if no assignments yet
        }
      }
    ];

    console.log('\n🔍 Database Structure Verification:');
    let allPassed = true;

    for (const check of dbStructureChecks) {
      try {
        const result = await check.test();
        console.log(`   ${result ? '✅' : '❌'} ${check.name}`);
        if (!result) allPassed = false;
      } catch (error) {
        console.log(`   ❌ ${check.name} - Error: ${error.message}`);
        allPassed = false;
      }
    }

    console.log(`\n📊 Database Structure: ${allPassed ? 'Ready for API operations' : 'Issues detected'}`);

    return allPassed;

  } catch (error) {
    console.error('❌ Error testing API endpoints:', error);
    return false;
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🧪 Territory Assignment/Unassignment Functionality Test');
  console.log('=======================================================\n');

  try {
    const tests = [
      { name: 'Assignment Functionality', test: testAssignmentFunctionality },
      { name: 'Unassignment Functionality', test: testUnassignmentFunctionality },
      { name: 'Territory Status Changes', test: testTerritoryStatusChanges },
      { name: 'API Endpoint Availability', test: testAPIEndpoints }
    ];

    let passed = 0;
    let total = tests.length;

    for (const { name, test } of tests) {
      try {
        const result = await test();
        if (result) {
          passed++;
          console.log(`\n✅ ${name} test: PASSED`);
        } else {
          console.log(`\n❌ ${name} test: FAILED`);
        }
      } catch (error) {
        console.log(`\n❌ ${name} test: ERROR - ${error.message}`);
      }
    }

    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`Passed: ${passed}/${total}`);
    console.log(`Status: ${passed === total ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    if (passed === total) {
      console.log('\n🎉 Territory assignment/unassignment system is ready!');
      console.log('✅ Assignment functionality structure verified');
      console.log('✅ Unassignment functionality structure verified');
      console.log('✅ Territory status changes working correctly');
      console.log('✅ API endpoints ready for operations');
      console.log('✅ Database structure supports all operations');
    }

  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testAssignmentFunctionality,
  testUnassignmentFunctionality,
  testTerritoryStatusChanges,
  testAPIEndpoints
};
