# Story 5.1: Letters and Document Management

**Epic:** Epic 5: Communication & Deployment
**Story Points:** 8
**Priority:** High
**Status:** Ready for Review

## Story

As an elder or ministerial servant,
I want to manage congregation letters and documents with secure storage and access control,
so that I can organize important communications and ensure proper document distribution.

## Acceptance Criteria

1. **Document upload and storage with secure file management and organization**
2. **Letter categorization with tagging and classification systems**
3. **Access control with role-based permissions and visibility settings**
4. **Document search and filtering with advanced query capabilities**
5. **Version control with document history and change tracking**
6. **Document sharing with controlled distribution and access logging**
7. **Document archival with retention policies and compliance management**

## Dev Notes

### API Endpoints (tRPC)

```typescript
// Letters and document management routes
documentManagement: router({
  uploadDocument: adminProcedure
    .input(z.object({
      title: z.string(),
      category: z.string(),
      file: z.string(), // base64 encoded file
      fileName: z.string(),
      visibility: z.enum(['public', 'elders_only', 'private']),
      tags: z.array(z.string()).optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await documentService.uploadDocument(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    }),

  getDocuments: protectedProcedure
    .input(z.object({
      category: z.string().optional(),
      tags: z.array(z.string()).optional(),
      searchTerm: z.string().optional(),
      pagination: z.object({
        page: z.number().default(1),
        limit: z.number().default(20)
      }).optional()
    }))
    .query(async ({ input, ctx }) => {
      return await documentService.getDocuments(
        input,
        ctx.user.congregationId,
        ctx.user.role
      );
    })
})
```

## Definition of Done

- [x] Document upload and storage implemented
- [x] Letter categorization functional
- [x] Access control working
- [x] Document search and filtering complete
- [ ] Version control implemented (future enhancement)
- [x] Document sharing functional
- [ ] Document archival complete (future enhancement)
- [x] All tests pass
- [x] Code review completed
- [x] Documentation updated

## Tasks

### Task 1: Create Document Types File
- [x] Create `src/lib/types/document.ts` with all document-related types
- [x] Export types from enhancedDocumentService.ts to the new types file
- [x] Update imports in existing components

### Task 2: Update Letters Management UI to Match Screenshots
- [x] Update admin letters page to match the provided screenshots
- [x] Implement proper modal for "Subir Nueva Carta" (Upload New Letter)
- [x] Implement edit modal for "Editar Carta" (Edit Letter)
- [x] Add proper Spanish translations and UI elements

### Task 3: Implement Member-Side Letters View
- [x] Create mobile-friendly letters view for members
- [x] Implement card-based layout matching screenshots
- [x] Add proper navigation and PDF viewing functionality

### Task 4: Test and Validate Implementation
- [x] Test file upload functionality
- [x] Test document viewing and downloading
- [x] Test role-based access control
- [x] Verify mobile responsiveness

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: Full Stack Developer (James)
- Date: 2025-01-25

### File List
- docs/stories/5.1.story.md (updated with tasks and implementation)
- src/lib/types/document.ts (created)
- src/app/admin/letters/page.tsx (created)
- src/components/admin/LettersManager.tsx (created)
- src/app/letters/page.tsx (created)
- src/components/letters/LettersViewer.tsx (created)
- src/app/api/documents/[id]/route.ts (created)
- src/lib/services/enhancedDocumentService.ts (updated with CRUD methods)
- src/app/dashboard/page.tsx (updated letters navigation)
- scripts/test-letters-management.js (created)

### Change Log
- 2025-01-24: Story recreated with document management specification
- 2025-01-25: Added detailed implementation tasks based on screenshots analysis
- 2025-01-25: Implemented complete letters management system with Spanish UI, admin interface, member view, and API endpoints
- 2025-01-25: Fixed authentication issues - converted server components to client components, updated API routes to use extractAndVerifyToken, resolved JWT payload property access
