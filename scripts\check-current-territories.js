const { PrismaClient } = require('@prisma/client');

async function checkCurrentTerritories() {
  const prisma = new PrismaClient();
  
  try {
    const territories = await prisma.territory.findMany({
      where: { congregationId: '1441' },
      select: { territoryNumber: true, id: true },
      orderBy: { territoryNumber: 'asc' }
    });
    
    console.log('Current territories in database:');
    territories.forEach(t => {
      console.log(`- Territory ${t.territoryNumber}`);
    });
    console.log(`\nTotal: ${territories.length} territories`);
    
    // Check which Excel files we have vs what's in database
    const fs = require('fs');
    const path = require('path');
    
    const territoriosDir = path.join(process.cwd(), 'Territorios');
    const excelFiles = fs.readdirSync(territoriosDir)
      .filter(file => file.endsWith('.xlsx'))
      .map(file => {
        const match = file.match(/Terr\.\s*(\d+)/);
        return match ? match[1].padStart(3, '0') : null;
      })
      .filter(Boolean)
      .sort();
    
    console.log(`\nExcel files available: ${excelFiles.length}`);
    console.log('Excel territory numbers:', excelFiles.join(', '));
    
    const dbTerritoryNumbers = territories.map(t => t.territoryNumber).sort();
    const missing = excelFiles.filter(num => !dbTerritoryNumbers.includes(num));
    
    console.log(`\nMissing from database: ${missing.length} territories`);
    if (missing.length > 0) {
      console.log('Missing territories:', missing.join(', '));
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentTerritories();
