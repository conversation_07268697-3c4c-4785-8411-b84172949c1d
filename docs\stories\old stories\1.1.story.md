# Story 1.1: Next.js Project Setup and Development Environment

## Status

Ready for Review

## Story

**As a** developer,
**I want** to set up a Next.js fullstack project with PostgreSQL and Prisma,
**so that** I have a modern development environment for building the Hermanos app with exact UI preservation.

## Acceptance Criteria

1. Next.js 14+ project created with latest versions of TypeScript, App Router, and Tailwind CSS
2. PostgreSQL database connection established with latest Prisma ORM version
3. Development environment includes latest ESLint, Prettier for code quality
4. Complete `.env` file configuration with all ports, URLs, and environment variables
5. Never hardcode localhost, ports, or URLs in any codebase files
6. Single `npm run dev` script initializes both frontend and backend using .env variables
7. Health check API endpoint using environment configuration

## Tasks / Subtasks

- [x] Initialize Next.js 14+ project with TypeScript and App Router (AC: 1)
  - [x] Create new Next.js project using `create-next-app` with TypeScript
  - [x] Configure App Router for modern routing architecture
  - [x] Install and configure Tailwind CSS for styling
  - [x] Set up TypeScript configuration for strict type checking
- [x] Set up PostgreSQL database connection with Prisma (AC: 2)
  - [x] Install latest Prisma ORM version
  - [x] Configure Prisma schema file for PostgreSQL
  - [x] Set up database connection configuration
  - [x] Test database connectivity
- [x] Configure development environment and code quality tools (AC: 3)
  - [x] Install and configure latest ESLint with Next.js rules
  - [x] Install and configure Prettier for code formatting
  - [x] Set up pre-commit hooks for code quality
  - [x] Configure VS Code settings for consistent development
- [x] Create comprehensive .env configuration (AC: 4, 5)
  - [x] Define all required environment variables in .env file
  - [x] Configure database connection variables
  - [x] Set up port and URL configurations
  - [x] Ensure no hardcoded values in codebase files
- [x] Implement unified development script (AC: 6)
  - [x] Create single `npm run dev` script
  - [x] Configure script to use environment variables
  - [x] Test frontend and backend initialization
  - [x] Verify hot reload functionality
- [x] Create health check API endpoint (AC: 7)
  - [x] Implement health check API route using App Router
  - [x] Use environment configuration for endpoint
  - [x] Test API endpoint functionality
  - [x] Verify database connection in health check

## Dev Notes

### Previous Story Insights

This is the first story in the project, establishing the foundation for all subsequent development.

### Technology Stack Requirements

[Source: architecture/3-technology-stack-selection.md]

- **Next.js 14**: Use App Router for better organization and performance
- **TypeScript**: Native integration for better code quality
- **Tailwind CSS**: For rapid UI development and mobile-first design
- **PostgreSQL 15**: For multi-tenancy support and JSON handling
- **Prisma ORM**: Latest version for type-safe database operations

### Next.js Configuration Requirements

[Source: architecture/3-technology-stack-selection.md#next.js-configuration]

```javascript
// next.config.js - Optimized for congregation management
const nextConfig = {
  experimental: {
    appDir: true, // Use new App Router for better organization
  },
  images: {
    domains: ['localhost'], // Local image optimization
    formats: ['image/webp', 'image/avif'], // Modern image formats
  },
  compress: true, // Enable gzip compression
  poweredByHeader: false, // Remove X-Powered-By header for security
  reactStrictMode: true, // Enable React strict mode
  swcMinify: true, // Use SWC for faster builds
};
```

### Package.json Dependencies

[Source: architecture/3-technology-stack-selection.md#package.json-dependencies]

```json
{
  "dependencies": {
    "next": "14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@prisma/client": "^5.0.0",
    "prisma": "^5.0.0",
    "zustand": "^4.4.0",
    "jsonwebtoken": "^9.0.0",
    "bcryptjs": "^2.4.3",
    "zod": "^3.22.0",
    "react-hot-toast": "^2.4.0",
    "lucide-react": "^0.290.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "typescript": "^5.0.0",
    "eslint": "^8.0.0",
    "eslint-config-next": "14.0.0",
    "prettier": "^3.0.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0"
  }
}
```

### Environment Configuration Standards

[Source: prd/development-standards-and-guidelines.md]

- All configuration variables must be defined in `.env` file as single source of truth
- Never hardcode localhost, ports, URLs, or any environment-specific values in codebase files
- Use default ports specified in `.env` file for all development and production environments
- Create standardized `npm run dev` script that initializes servers using environment variables only

### Database Connection Configuration

[Source: architecture/4-database-architecture.md#database-migration-strategy]

- PostgreSQL 15 with multi-tenant architecture support
- Prisma ORM for type-safe database operations
- Connection pooling for better performance
- Congregation isolation through congregation_id foreign keys

### Project Structure Requirements

[Source: prd/technical-assumptions.md]

- Single Next.js application with App Router
- API routes for backend logic
- Shared TypeScript types throughout the stack
- Self-hosted deployment on standard VPS infrastructure

### Testing

[Source: architecture documents - testing strategy not found in current sections]
No specific testing guidance found in architecture docs for this story. Will implement basic testing setup following Next.js best practices.

## Change Log

| Date       | Version | Description            | Author      |
| ---------- | ------- | ---------------------- | ----------- |
| 2024-01-XX | 1.0     | Initial story creation | BMad Master |

## Dev Agent Record

### Agent Model Used

Claude Sonnet 4 (Development Agent)

### Debug Log References

- Starting implementation of Story 1.1: Next.js Project Setup
- Task 1: Initialize Next.js 14+ project with TypeScript and App Router - COMPLETED
- Task 2: Set up PostgreSQL database connection with Prisma - COMPLETED
- Task 3: Configure development environment and code quality tools - COMPLETED
- Task 4: Create comprehensive .env configuration - COMPLETED
- Task 5: Implement unified development script - COMPLETED
- Task 6: Create health check API endpoint - COMPLETED
- Fixed font configuration issues (replaced Geist with Inter)
- Fixed Tailwind CSS configuration and PostCSS setup
- Fixed database connection with correct credentials
- All linting and type checking passes

### Completion Notes List

- ✅ Task 1 Complete: Next.js 14+ project created with TypeScript, App Router, and Tailwind CSS
- ✅ Task 2 Complete: PostgreSQL database connection established with Prisma ORM
- ✅ Task 3 Complete: ESLint, Prettier, and TypeScript configured for code quality
- ✅ Task 4 Complete: Comprehensive .env file with all required environment variables
- ✅ Task 5 Complete: Single `npm run dev` script working with environment variables
- ✅ Task 6 Complete: Health check API endpoint implemented and tested
- Updated package.json with all required dependencies from story requirements
- Configured next.config.js with optimized settings for congregation management
- All dependencies installed successfully
- Database schema pushed successfully to PostgreSQL
- All linting and type checking passes without errors

### File List

- package.json - Updated with all required dependencies
- next.config.js - Next.js configuration with optimization settings
- tailwind.config.ts - Tailwind CSS configuration
- postcss.config.mjs - PostCSS configuration for Tailwind
- .env - Environment variables configuration
- src/app/layout.tsx - Root layout with Inter font and proper metadata
- src/app/globals.css - Global styles with Tailwind directives
- src/app/api/health/route.ts - Health check API endpoint
- src/lib/prisma.ts - Prisma client configuration
- src/lib/db-test.ts - Database connectivity test utility
- prisma/schema.prisma - Database schema with HealthCheck model
- tsconfig.json - TypeScript configuration
- eslint.config.mjs - ESLint configuration

## QA Results

_To be populated by QA agent_
