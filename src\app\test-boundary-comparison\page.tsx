'use client';

import React, { useState, useEffect } from 'react';
import SimpleTerritoryMap from '@/components/territories/shared/SimpleTerritoryMap';
import type { Territory } from '@/types/territories/map';

export default function TestBoundaryComparisonPage() {
  const [debugTerritory, setDebugTerritory] = useState<Territory | null>(null);
  const [detailTerritory, setDetailTerritory] = useState<Territory | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem('hermanos_token');
        if (!token) {
          throw new Error('No token found');
        }

        // Fetch Territory 003 data (same as debug page)
        const response = await fetch('/api/territories/cmdjwgwzb0001jk0xm9rqeni1', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch: ${response.statusText}`);
        }

        const territoryData = await response.json();
        console.log('🔍 Comparison - Raw API data:', territoryData);

        // Create debug territory (same as debug page)
        const debugTerr: Territory = {
          id: territoryData.id,
          territoryNumber: territoryData.territoryNumber,
          address: territoryData.address,
          status: territoryData.status,
          coordinates: territoryData.coordinates,
          boundary: territoryData.boundary
        };

        // Create detail territory (same as TerritoryDetail component)
        let coordinates = territoryData.coordinates;
        let boundary = territoryData.boundary;

        // Apply the same logic as TerritoryDetail
        if (!boundary && territoryData.address) {
          console.log('⚠️ No boundary data, would enhance...');
          // For this test, we'll skip enhancement to isolate the issue
        }

        const detailTerr: Territory = {
          id: territoryData.id,
          territoryNumber: territoryData.territoryNumber,
          address: territoryData.address,
          status: territoryData.status,
          coordinates,
          boundary
        };

        console.log('🗺️ Debug territory:', debugTerr);
        console.log('🗺️ Detail territory:', detailTerr);
        console.log('🔍 Boundaries equal?', JSON.stringify(debugTerr.boundary) === JSON.stringify(detailTerr.boundary));

        setDebugTerritory(debugTerr);
        setDetailTerritory(detailTerr);

      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <div className="text-sm text-gray-600">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-4 py-3">
          <h1 className="text-xl font-semibold text-gray-900">
            🔍 Boundary Rendering Comparison
          </h1>
          <p className="text-sm text-gray-500 mt-1">
            Side-by-side comparison of debug page vs territory detail rendering
          </p>
        </div>
      </div>

      {/* Comparison Grid */}
      <div className="p-4 grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Debug Page Style (Working) */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200 bg-green-50">
            <h2 className="text-lg font-medium text-green-900">✅ Debug Page Style (Working)</h2>
            <p className="text-sm text-green-700 mt-1">
              This should show boundaries correctly
            </p>
            {debugTerritory && (
              <div className="text-xs text-green-600 mt-2">
                <div>Territory: {debugTerritory.territoryNumber}</div>
                <div>Has boundary: {!!debugTerritory.boundary ? 'Yes' : 'No'}</div>
                <div>Boundary type: {debugTerritory.boundary?.type || 'N/A'}</div>
              </div>
            )}
          </div>
          <div className="h-80">
            {debugTerritory ? (
              <SimpleTerritoryMap
                territories={[debugTerritory]}
                height="100%"
                className="w-full"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                No data
              </div>
            )}
          </div>
        </div>

        {/* Territory Detail Style (Not Working) */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200 bg-red-50">
            <h2 className="text-lg font-medium text-red-900">❌ Territory Detail Style (Not Working)</h2>
            <p className="text-sm text-red-700 mt-1">
              This should show the same boundaries but doesn't
            </p>
            {detailTerritory && (
              <div className="text-xs text-red-600 mt-2">
                <div>Territory: {detailTerritory.territoryNumber}</div>
                <div>Has boundary: {!!detailTerritory.boundary ? 'Yes' : 'No'}</div>
                <div>Boundary type: {detailTerritory.boundary?.type || 'N/A'}</div>
              </div>
            )}
          </div>
          <div className="h-80">
            {detailTerritory ? (
              <SimpleTerritoryMap
                territories={[detailTerritory]}
                height="320px"
                className="w-full"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                No data
              </div>
            )}
          </div>
        </div>

      </div>

      {/* Data Comparison */}
      <div className="p-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">📊 Data Comparison</h2>
          </div>
          <div className="p-4 grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            <div>
              <h3 className="font-medium text-green-900 mb-2">Debug Territory Data</h3>
              <pre className="text-xs bg-green-50 p-3 rounded overflow-auto max-h-64">
                {debugTerritory ? JSON.stringify(debugTerritory, null, 2) : 'No data'}
              </pre>
            </div>

            <div>
              <h3 className="font-medium text-red-900 mb-2">Detail Territory Data</h3>
              <pre className="text-xs bg-red-50 p-3 rounded overflow-auto max-h-64">
                {detailTerritory ? JSON.stringify(detailTerritory, null, 2) : 'No data'}
              </pre>
            </div>

          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="p-4">
        <div className="bg-yellow-50 rounded-lg border border-yellow-200 p-4">
          <h3 className="font-medium text-yellow-900 mb-2">🔧 What to Check:</h3>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• Both maps should show identical boundary polygons</li>
            <li>• Check browser console for any differences in map initialization</li>
            <li>• Compare the data structures in the JSON output below</li>
            <li>• Look for any differences in how SimpleTerritoryMap processes the data</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
