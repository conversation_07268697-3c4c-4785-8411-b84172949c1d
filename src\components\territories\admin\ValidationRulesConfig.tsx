'use client';

/**
 * Validation Rules Configuration Component
 * 
 * Admin interface for configuring territory validation rules.
 * Allows customization of validation rules per congregation.
 */

import React, { useState, useEffect } from 'react';
import {
  ValidationRule,
  ValidationRuleTemplate,
  TerritoryNumberValidationConfig,
  AddressValidationConfig,
  DuplicateDetectionConfig
} from '@/types/territories/validation';

interface ValidationRulesConfigProps {
  onClose: () => void;
  className?: string;
}

export default function ValidationRulesConfig({ onClose, className = '' }: ValidationRulesConfigProps) {
  const [rules, setRules] = useState<ValidationRule[]>([]);
  const [templates, setTemplates] = useState<ValidationRuleTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadValidationRules();
  }, []);

  const loadValidationRules = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories/validation/rules', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRules(data.rules || []);
        setTemplates(data.templates || []);
      } else {
        throw new Error('Failed to load validation rules');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load validation rules');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveRules = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories/validation/rules', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ rules })
      });

      if (response.ok) {
        setHasChanges(false);
        console.log('Validation rules saved successfully');
      } else {
        throw new Error('Failed to save validation rules');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save validation rules');
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplyTemplate = async () => {
    if (!selectedTemplate) return;

    setIsLoading(true);
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories/validation/rules', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ templateId: selectedTemplate })
      });

      if (response.ok) {
        const data = await response.json();
        setRules(data.rules || []);
        setHasChanges(false);
        setSelectedTemplate('');
        console.log('Template applied successfully');
      } else {
        throw new Error('Failed to apply template');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to apply template');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRuleChange = (ruleId: string, field: string, value: any) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId 
        ? { ...rule, [field]: value, updatedAt: new Date() }
        : rule
    ));
    setHasChanges(true);
  };

  const handleConfigurationChange = (ruleId: string, configField: string, value: any) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId 
        ? { 
            ...rule, 
            configuration: { ...rule.configuration, [configField]: value },
            updatedAt: new Date()
          }
        : rule
    ));
    setHasChanges(true);
  };

  const getSeverityColor = (severity: 'warning' | 'error' | 'critical') => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'error': return 'bg-red-50 text-red-700';
      case 'warning': return 'bg-yellow-50 text-yellow-700';
      default: return 'bg-gray-50 text-gray-700';
    }
  };

  if (isLoading && rules.length === 0) {
    return (
      <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600">Cargando reglas de validación...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-teal-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Configuración de Reglas de Validación</h2>
              <p className="text-teal-100 text-sm mt-1">
                Personaliza las reglas de validación para tu congregación
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-teal-100 transition-colors"
              aria-label="Cerrar"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <p className="mt-1 text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Template Selection */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Plantillas de Reglas</h3>
            <div className="flex items-center space-x-4">
              <select
                value={selectedTemplate}
                onChange={(e) => setSelectedTemplate(e.target.value)}
                className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              >
                <option value="">Seleccionar plantilla...</option>
                {templates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.name} - {template.description}
                  </option>
                ))}
              </select>
              <button
                onClick={handleApplyTemplate}
                disabled={!selectedTemplate || isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Aplicar Plantilla
              </button>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              Las plantillas proporcionan configuraciones predefinidas para diferentes tipos de congregaciones.
            </p>
          </div>

          {/* Rules Configuration */}
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Reglas de Validación</h3>
            
            {rules.map((rule) => (
              <div key={rule.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={rule.enabled}
                      onChange={(e) => handleRuleChange(rule.id, 'enabled', e.target.checked)}
                      className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                    />
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{rule.name}</h4>
                      <p className="text-xs text-gray-500">{rule.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                      {rule.severity.toUpperCase()}
                    </span>
                    <select
                      value={rule.severity}
                      onChange={(e) => handleRuleChange(rule.id, 'severity', e.target.value)}
                      className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-teal-500"
                    >
                      <option value="warning">Warning</option>
                      <option value="error">Error</option>
                      <option value="critical">Critical</option>
                    </select>
                  </div>
                </div>

                {/* Rule-specific configuration */}
                {rule.type === 'format' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Longitud Mínima
                      </label>
                      <input
                        type="number"
                        value={(rule.configuration as TerritoryNumberValidationConfig).minLength || 1}
                        onChange={(e) => handleConfigurationChange(rule.id, 'minLength', parseInt(e.target.value))}
                        className="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-teal-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Longitud Máxima
                      </label>
                      <input
                        type="number"
                        value={(rule.configuration as TerritoryNumberValidationConfig).maxLength || 20}
                        onChange={(e) => handleConfigurationChange(rule.id, 'maxLength', parseInt(e.target.value))}
                        className="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-teal-500"
                      />
                    </div>
                  </div>
                )}

                {rule.type === 'address' && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={(rule.configuration as AddressValidationConfig).standardizeCapitalization || false}
                          onChange={(e) => handleConfigurationChange(rule.id, 'standardizeCapitalization', e.target.checked)}
                          className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Estandarizar capitalización</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={(rule.configuration as AddressValidationConfig).removeExtraSpaces || false}
                          onChange={(e) => handleConfigurationChange(rule.id, 'removeExtraSpaces', e.target.checked)}
                          className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Remover espacios extra</span>
                      </label>
                    </div>
                  </div>
                )}

                {rule.type === 'duplicate' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Umbral de Similitud (0-1)
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        min="0"
                        max="1"
                        value={(rule.configuration as DuplicateDetectionConfig).similarityThreshold || 0.8}
                        onChange={(e) => handleConfigurationChange(rule.id, 'similarityThreshold', parseFloat(e.target.value))}
                        className="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-teal-500"
                      />
                    </div>
                    <div>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={(rule.configuration as DuplicateDetectionConfig).enableFuzzyMatching || false}
                          onChange={(e) => handleConfigurationChange(rule.id, 'enableFuzzyMatching', e.target.checked)}
                          className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Habilitar coincidencia difusa</span>
                      </label>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-between">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
          >
            Cancelar
          </button>
          
          <div className="flex space-x-3">
            {hasChanges && (
              <span className="text-sm text-yellow-600 self-center">
                Hay cambios sin guardar
              </span>
            )}
            <button
              onClick={handleSaveRules}
              disabled={!hasChanges || isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Guardando...' : 'Guardar Reglas'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
