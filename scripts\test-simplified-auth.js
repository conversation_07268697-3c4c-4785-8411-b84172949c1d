const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testSimplifiedAuth() {
  try {
    console.log('🧪 Testing Simplified Flag-Based Authentication...\n');

    // 1. Verify congregation PIN
    console.log('1️⃣ Testing Congregation PIN...');
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (congregation) {
      const congregationPinMatches = await bcrypt.compare('1930', congregation.pin);
      console.log(`✅ Congregation PIN (1930) works: ${congregationPinMatches}`);
    }

    // 2. Check available members for default selection
    console.log('\n2️⃣ Checking Available Members for Default Selection...');
    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        isActive: true
      },
      select: { name: true, role: true, email: true },
      orderBy: [
        { role: 'desc' }, // This won't work perfectly, but gives us an idea
        { createdAt: 'asc' }
      ],
      take: 5
    });

    console.log('📋 Available members (top 5):');
    members.forEach((member, index) => {
      console.log(`   ${index + 1}. ${member.name} (${member.role}) - ${member.email}`);
    });

    if (members.length > 0) {
      console.log(`\n🎯 Default member for congregation PIN login: ${members[0].name} (${members[0].role})`);
    }

    console.log('\n🧪 TESTING INSTRUCTIONS:');
    console.log('═══════════════════════════════════════════════════════════');
    console.log('🌐 URL: http://localhost:3001/login');
    console.log('');
    console.log('📋 CONGREGATION PIN LOGIN TEST:');
    console.log('   🏛️ Congregation ID: 1441');
    console.log('   🔐 Congregation PIN: 1930');
    console.log('   ⚠️  DO NOT enter any member PIN - just use congregation PIN');
    console.log('');
    console.log('✅ EXPECTED BEHAVIOR:');
    console.log('   1. Login should succeed');
    console.log('   2. Dashboard should show "Administración" button (full admin access)');
    console.log('   3. Profile dropdown should show:');
    console.log('      - Congregation name: "Coral Oeste"');
    console.log('      - "Acceso con PIN de Congregación" (blue text)');
    console.log('      - "Cerrar Sesión" button');
    console.log('   4. Should NOT show member name or role in profile dropdown');
    console.log('');
    console.log('🔍 WHAT TO VERIFY:');
    console.log('   - Profile dropdown shows congregation info only');
    console.log('   - Full admin access is available');
    console.log('   - No member-specific information is displayed');
    console.log('   - Logout functionality works correctly');
    console.log('');
    console.log('📝 TECHNICAL DETAILS:');
    console.log('   - JWT token will contain hasCongregationPinAccess: true');
    console.log('   - User object will be based on highest-ranking member but flagged');
    console.log('   - Permissions will be elevated due to congregation PIN access');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSimplifiedAuth();
