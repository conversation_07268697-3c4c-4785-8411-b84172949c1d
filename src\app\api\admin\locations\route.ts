import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // For now, return default locations
    // In the future, this could be stored in the database
    const locations = [
      {
        id: 'kingdom_hall',
        name: 'Salón del Reino',
        address: '7790 West 4th Av Hialeah Fl 33014',
        type: 'kingdom_hall'
      },
      {
        id: 'zoom',
        name: 'Zoom',
        address: 'Reunión Virtual',
        type: 'zoom'
      },
      {
        id: 'territory_special',
        name: 'Territorio Especial',
        address: 'Centro Comercial Westland Mall',
        type: 'territory'
      }
    ];

    return NextResponse.json({ locations });
  } catch (error) {
    console.error('Error in locations API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
