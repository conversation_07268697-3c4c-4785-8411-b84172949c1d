/* MapLibre GL JS Styles */
@import 'maplibre-gl/dist/maplibre-gl.css';

/* Custom map container styles */
.maplibre-map {
  font-family: inherit;
}

/* Control customizations */
.maplibregl-ctrl-group {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.maplibregl-ctrl-group button {
  border-radius: 0;
  transition: background-color 0.2s ease;
}

.maplibregl-ctrl-group button:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.maplibregl-ctrl-group button:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.maplibregl-ctrl-group button:hover {
  background-color: #f3f4f6;
}

/* Attribution control */
.maplibregl-ctrl-attrib {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  font-size: 11px;
  padding: 2px 6px;
}

/* Popup customizations */
.maplibregl-popup {
  max-width: 300px;
}

.maplibregl-popup-content {
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.maplibregl-popup-close-button {
  font-size: 18px;
  padding: 4px;
  color: #6b7280;
  transition: color 0.2s ease;
}

.maplibregl-popup-close-button:hover {
  color: #374151;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .maplibregl-ctrl-group {
    margin: 8px;
  }
  
  .maplibregl-ctrl-group button {
    width: 36px;
    height: 36px;
  }
  
  .maplibregl-popup {
    max-width: 250px;
  }
  
  .maplibregl-popup-content {
    padding: 12px;
  }
}

/* Territory-specific styles */
.territory-marker {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.territory-marker:hover {
  transform: scale(1.1);
}

.territory-marker.selected {
  z-index: 1000;
}

/* Loading overlay */
.map-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Error overlay */
.map-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(248, 250, 252, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Territory info overlay */
.territory-info-overlay {
  position: absolute;
  top: 16px;
  left: 16px;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  z-index: 100;
}

/* Fullscreen button for mobile */
.map-fullscreen-button {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background-color: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: background-color 0.2s ease;
  z-index: 100;
}

.map-fullscreen-button:hover {
  background-color: rgba(243, 244, 246, 0.95);
}

/* Responsive map container */
.territory-map-container {
  position: relative;
  width: 100%;
  min-height: 300px;
}

@media (min-width: 768px) {
  .territory-map-container {
    min-height: 400px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .maplibregl-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .maplibregl-ctrl-group {
    background-color: #374151;
  }
  
  .maplibregl-ctrl-group button {
    background-color: #374151;
    color: #f9fafb;
  }
  
  .maplibregl-ctrl-group button:hover {
    background-color: #4b5563;
  }
  
  .maplibregl-ctrl-attrib {
    background-color: rgba(55, 65, 81, 0.9);
    color: #f9fafb;
  }
  
  .territory-info-overlay {
    background-color: rgba(55, 65, 81, 0.95);
    color: #f9fafb;
  }
  
  .map-fullscreen-button {
    background-color: rgba(55, 65, 81, 0.95);
    color: #f9fafb;
  }
  
  .map-fullscreen-button:hover {
    background-color: rgba(75, 85, 99, 0.95);
  }
}
