#!/usr/bin/env node

/**
 * Restore Congregation Data
 * 
 * This script restores the essential congregation and member data
 * that was lost during the database reset.
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function restoreCongregationData() {
    console.log('🔄 RESTORING CONGREGATION DATA...');
    console.log('');

    try {
        // 1. Create Coral Oeste congregation
        console.log('📋 Creating Coral Oeste congregation...');
        
        const congregation = await prisma.congregation.upsert({
            where: { id: '1441' },
            update: {},
            create: {
                id: '1441',
                name: 'Coral Oeste',
                region: 'Hialeah',
                pin: await bcrypt.hash('123456', 10), // Default PIN
                language: 'es',
                timezone: 'America/New_York',
                isActive: true,
                settings: {}
            }
        });
        
        console.log(`   ✅ Congregation created: ${congregation.name} (ID: ${congregation.id})`);

        // 2. Create default roles
        console.log('📋 Creating default roles...');
        
        const roles = [
            { name: 'elder', description: '<PERSON><PERSON><PERSON>' },
            { name: 'ministerial_servant', description: '<PERSON><PERSON>vo Ministerial' },
            { name: 'publisher', description: 'Publicador' },
            { name: 'pioneer', description: 'Precursor' }
        ];

        for (const roleData of roles) {
            await prisma.role.upsert({
                where: { name: roleData.name },
                update: {},
                create: roleData
            });
        }
        
        console.log(`   ✅ Created ${roles.length} default roles`);

        // 3. Create admin member
        console.log('📋 Creating admin member...');
        
        const adminMember = await prisma.member.upsert({
            where: { 
                congregationId_email: {
                    congregationId: '1441',
                    email: '<EMAIL>'
                }
            },
            update: {},
            create: {
                congregationId: '1441',
                name: 'Administrador',
                email: '<EMAIL>',
                role: 'elder',
                pin: await bcrypt.hash('123456', 10),
                isActive: true,
                preferences: {},
                contactPreferences: {},
                qualifications: [],
                serviceGroup: 'Grupo 1'
            }
        });
        
        console.log(`   ✅ Admin member created: ${adminMember.name} (${adminMember.email})`);

        // 4. Create some sample members
        console.log('📋 Creating sample members...');
        
        const sampleMembers = [
            {
                name: 'Juan Pérez',
                email: '<EMAIL>',
                role: 'elder',
                serviceGroup: 'Grupo 1'
            },
            {
                name: 'María González',
                email: '<EMAIL>',
                role: 'publisher',
                serviceGroup: 'Grupo 1'
            },
            {
                name: 'Carlos Rodríguez',
                email: '<EMAIL>',
                role: 'ministerial_servant',
                serviceGroup: 'Grupo 2'
            },
            {
                name: 'Ana Martínez',
                email: '<EMAIL>',
                role: 'pioneer',
                serviceGroup: 'Grupo 2'
            }
        ];

        for (const memberData of sampleMembers) {
            await prisma.member.upsert({
                where: {
                    congregationId_email: {
                        congregationId: '1441',
                        email: memberData.email
                    }
                },
                update: {},
                create: {
                    congregationId: '1441',
                    name: memberData.name,
                    email: memberData.email,
                    role: memberData.role,
                    pin: await bcrypt.hash('123456', 10),
                    isActive: true,
                    preferences: {},
                    contactPreferences: {},
                    qualifications: [],
                    serviceGroup: memberData.serviceGroup
                }
            });
        }
        
        console.log(`   ✅ Created ${sampleMembers.length} sample members`);

        // 5. Create congregation settings
        console.log('📋 Creating congregation settings...');
        
        const settings = [
            { settingKey: 'language', settingValue: 'es' },
            { settingKey: 'timezone', settingValue: 'America/New_York' },
            { settingKey: 'meeting_location', settingValue: 'Salon del Reino' },
            { settingKey: 'meeting_address', settingValue: '7790 West 4th Av Hialeah Fl 33014' }
        ];

        for (const setting of settings) {
            await prisma.congregationSetting.upsert({
                where: {
                    congregationId_settingKey: {
                        congregationId: '1441',
                        settingKey: setting.settingKey
                    }
                },
                update: { settingValue: setting.settingValue },
                create: {
                    congregationId: '1441',
                    settingKey: setting.settingKey,
                    settingValue: setting.settingValue
                }
            });
        }
        
        console.log(`   ✅ Created ${settings.length} congregation settings`);

        // 6. Create service groups
        console.log('📋 Creating service groups...');
        
        const serviceGroups = [
            { groupNumber: 1, name: 'Grupo 1', description: 'Primer grupo de servicio' },
            { groupNumber: 2, name: 'Grupo 2', description: 'Segundo grupo de servicio' }
        ];

        for (const groupData of serviceGroups) {
            await prisma.serviceGroup.upsert({
                where: {
                    congregationId_groupNumber: {
                        congregationId: '1441',
                        groupNumber: groupData.groupNumber
                    }
                },
                update: {},
                create: {
                    congregationId: '1441',
                    name: groupData.name,
                    groupNumber: groupData.groupNumber,
                    description: groupData.description,
                    isActive: true
                }
            });
        }
        
        console.log(`   ✅ Created ${serviceGroups.length} service groups`);

        console.log('');
        console.log('✅ CONGREGATION DATA RESTORED SUCCESSFULLY!');
        console.log('');
        console.log('📋 LOGIN CREDENTIALS:');
        console.log('   Congregation ID: 1441');
        console.log('   Congregation PIN: 123456');
        console.log('   Admin Email: <EMAIL>');
        console.log('   Admin PIN: 123456');
        console.log('');
        console.log('🎯 TESTING INSTRUCTIONS:');
        console.log('   1. Go to http://localhost:3000/login');
        console.log('   2. Enter Congregation ID: 1441');
        console.log('   3. Enter Congregation PIN: 123456');
        console.log('   4. <NAME_EMAIL> / 123456');
        console.log('   5. Verify dashboard and admin access work');

    } catch (error) {
        console.error('❌ Error restoring congregation data:', error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

// Run the restoration
restoreCongregationData()
    .catch((error) => {
        console.error('Failed to restore congregation data:', error);
        process.exit(1);
    });
