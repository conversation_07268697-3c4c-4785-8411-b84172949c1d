#!/usr/bin/env node

/**
 * Debug Authentication Script
 * This script helps debug authentication issues by checking the database
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function debugAuth() {
  console.log('🔍 Debugging Authentication...');

  try {
    // Check all congregations
    console.log('\n📋 All Congregations in Database:');
    const congregations = await prisma.congregation.findMany({
      select: {
        id: true,
        name: true,
        isActive: true,
        pin: true, // We'll show if it's hashed
        createdAt: true,
      }
    });

    if (congregations.length === 0) {
      console.log('❌ No congregations found in database!');
      return;
    }

    congregations.forEach((cong, index) => {
      console.log(`\n${index + 1}. Congregation:`);
      console.log(`   ID: ${cong.id}`);
      console.log(`   Name: ${cong.name}`);
      console.log(`   Active: ${cong.isActive}`);
      console.log(`   PIN: ${cong.pin ? (cong.pin.startsWith('$2') ? 'Hashed ✅' : 'Plain text ⚠️') : 'No PIN ❌'}`);
      console.log(`   Created: ${cong.createdAt}`);
    });

    // Check specifically for congregation 1441
    console.log('\n🎯 Checking Congregation 1441:');
    const cong1441 = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (!cong1441) {
      console.log('❌ Congregation 1441 not found!');
      
      // Check if it exists with different casing or format
      const allIds = congregations.map(c => c.id);
      console.log('Available congregation IDs:', allIds);
      
      // Check if 1441 exists as integer or different format
      const cong1441Alt = await prisma.congregation.findFirst({
        where: {
          OR: [
            { id: 1441 }, // Try as integer
            { id: '01441' }, // Try with leading zero
            { name: { contains: '1441' } }, // Try in name
          ]
        }
      });
      
      if (cong1441Alt) {
        console.log('✅ Found congregation 1441 with different format:');
        console.log(`   ID: ${cong1441Alt.id}`);
        console.log(`   Name: ${cong1441Alt.name}`);
      }
    } else {
      console.log('✅ Congregation 1441 found:');
      console.log(`   ID: ${cong1441.id}`);
      console.log(`   Name: ${cong1441.name}`);
      console.log(`   Active: ${cong1441.isActive}`);
      console.log(`   PIN: ${cong1441.pin ? (cong1441.pin.startsWith('$2') ? 'Hashed' : 'Plain text') : 'No PIN'}`);

      // Test PIN verification
      if (cong1441.pin) {
        console.log('\n🔐 Testing PIN verification:');
        
        const testPins = ['1930', '5555'];
        for (const testPin of testPins) {
          try {
            const isValid = await bcrypt.compare(testPin, cong1441.pin);
            console.log(`   PIN ${testPin}: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
          } catch (error) {
            console.log(`   PIN ${testPin}: ❌ Error testing - ${error.message}`);
          }
        }
      }

      // Check members for this congregation
      console.log('\n👥 Members in Congregation 1441:');
      const members = await prisma.member.findMany({
        where: { congregationId: cong1441.id },
        select: {
          id: true,
          name: true,
          role: true,
          isActive: true,
          createdAt: true,
        }
      });

      if (members.length === 0) {
        console.log('❌ No members found for congregation 1441!');
      } else {
        members.forEach((member, index) => {
          console.log(`   ${index + 1}. ${member.name} (${member.role}) - Active: ${member.isActive}`);
        });
      }
    }

    // Test the actual authentication flow
    console.log('\n🧪 Testing Authentication Flow:');
    
    const testAuth = async (congId, pin) => {
      console.log(`\nTesting: Congregation ${congId}, PIN ${pin}`);
      
      try {
        // Find congregation
        const congregation = await prisma.congregation.findUnique({
          where: {
            id: congId.toString().toUpperCase(),
            isActive: true,
          },
        });

        if (!congregation) {
          console.log('❌ Congregation not found or inactive');
          return;
        }

        console.log('✅ Congregation found');

        // Verify PIN
        const isPinValid = await bcrypt.compare(pin, congregation.pin);
        if (!isPinValid) {
          console.log('❌ PIN verification failed');
          return;
        }

        console.log('✅ PIN verification successful');

        // Check members
        const members = await prisma.member.findMany({
          where: {
            congregationId: congregation.id,
            isActive: true,
          },
          orderBy: [
            { role: 'desc' },
            { createdAt: 'asc' },
          ],
        });

        if (members.length === 0) {
          console.log('❌ No active members found');
          return;
        }

        console.log(`✅ Found ${members.length} active members`);
        console.log(`✅ Default member: ${members[0].name} (${members[0].role})`);
        console.log('🎉 Authentication would succeed!');

      } catch (error) {
        console.log(`❌ Authentication test failed: ${error.message}`);
      }
    };

    await testAuth('1441', '1930');
    await testAuth('1441', '5555');

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug script
if (require.main === module) {
  debugAuth()
    .then(() => {
      console.log('\n✅ Debug completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Debug failed:', error);
      process.exit(1);
    });
}

module.exports = { debugAuth };
