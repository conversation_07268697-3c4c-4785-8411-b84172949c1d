# Story 4.1: Field Service Management

**Epic:** Epic 4: Activities & Task Management  
**Story Points:** 13  
**Priority:** High  
**Status:** Draft  

## Story

As a service overseer,
I want to manage field service activities with time tracking and territory coordination,
so that I can monitor congregation service activity and provide proper support and encouragement.

## Acceptance Criteria

1. **Field service time tracking with individual and group reporting capabilities**
   - Comprehensive time tracking system with individual service hour recording and validation
   - Group service coordination with shared activity tracking and collaborative reporting
   - Service year analytics with monthly and annual reporting capabilities
   - Pioneer hour tracking with specialized requirements and goal monitoring

2. **Territory assignment and tracking with coverage monitoring and optimization**
   - Territory assignment system with member coordination and availability tracking
   - Coverage monitoring with last-worked dates and frequency analysis
   - Territory optimization with balanced distribution and efficient coverage planning
   - Return visit coordination with follow-up tracking and study progression

3. **Service group coordination with meeting planning and activity scheduling**
   - Service group management with member assignment and group overseer coordination
   - Meeting planning with location coordination and activity scheduling
   - Group performance tracking with participation rates and effectiveness monitoring
   - Service arrangement coordination with territory assignment and meeting point management

4. **Return visit and Bible study tracking with progress monitoring and follow-up coordination**
   - Return visit database with contact information and visit history tracking
   - Bible study progression monitoring with lesson tracking and student development
   - Follow-up coordination with reminder systems and elder notification
   - Study material management with resource distribution and progress tracking

5. **Service reporting with monthly submissions and congregation analytics**
   - Monthly service report system with individual submissions and validation
   - Congregation analytics with service statistics and trend analysis
   - Branch office reporting with required forms and submission tracking
   - Service goal tracking with individual and congregation-wide monitoring

6. **Service meeting integration with demonstration coordination and training support**
   - Service meeting coordination with demonstration scheduling and assignment tracking
   - Training support with skill development and improvement monitoring
   - Demonstration feedback with quality assessment and constructive guidance
   - Service training analytics with effectiveness monitoring and improvement recommendations

7. **Field service calendar with activity planning and coordination tools**
   - Service calendar with activity planning and congregation coordination
   - Special activity coordination with circuit assembly and convention integration
   - Service campaign management with goal setting and progress tracking
   - Calendar sharing with congregation communication and notification systems

## Dev Notes

### Technical Architecture

**Service Management:**
- Field service time tracking with individual and group reporting
- Territory assignment and coverage monitoring with optimization algorithms
- Service group coordination with meeting planning and activity scheduling
- Return visit and Bible study tracking with progress monitoring

**Reporting and Analytics:**
- Service reporting with monthly submissions and congregation analytics
- Branch office integration with required forms and submission tracking
- Service meeting coordination with demonstration tracking and training support
- Field service calendar with activity planning and coordination tools

### API Endpoints (tRPC)

```typescript
// Field service management routes
fieldService: router({
  recordServiceTime: protectedProcedure
    .input(z.object({
      date: z.date(),
      hours: z.number().min(0).max(24),
      minutes: z.number().min(0).max(59).default(0),
      returnVisits: z.number().min(0).default(0),
      bibleStudies: z.number().min(0).default(0),
      placements: z.number().min(0).default(0),
      serviceType: z.enum(['door_to_door', 'street_witnessing', 'return_visits', 'bible_studies', 'other']),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await fieldServiceService.recordServiceTime(
        input,
        ctx.user.id,
        ctx.user.congregationId
      );
    }),

  assignTerritory: adminProcedure
    .input(z.object({
      territoryId: z.string(),
      memberId: z.string(),
      assignmentDate: z.date(),
      expectedReturnDate: z.date(),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await territoryService.assignTerritory(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    }),

  getServiceReport: protectedProcedure
    .input(z.object({
      memberId: z.string().optional(),
      serviceMonth: z.string(),
      includeDetails: z.boolean().default(false)
    }))
    .query(async ({ input, ctx }) => {
      const targetMemberId = input.memberId || ctx.user.id;
      
      return await fieldServiceService.getServiceReport(
        targetMemberId,
        input.serviceMonth,
        ctx.user.congregationId,
        input.includeDetails
      );
    }),

  getCongregationServiceStats: adminProcedure
    .input(z.object({
      dateRange: z.object({
        start: z.date(),
        end: z.date()
      }),
      includeIndividualStats: z.boolean().default(false)
    }))
    .query(async ({ input, ctx }) => {
      return await fieldServiceService.getCongregationStats(
        input.dateRange,
        ctx.user.congregationId,
        input.includeIndividualStats
      );
    })
})
```

### Data Models

```typescript
interface FieldServiceRecord {
  id: string;
  memberId: string;
  congregationId: string;
  date: Date;
  hours: number;
  minutes: number;
  returnVisits: number;
  bibleStudies: number;
  placements: number;
  serviceType: 'door_to_door' | 'street_witnessing' | 'return_visits' | 'bible_studies' | 'other';
  territoryId?: string;
  serviceGroupId?: string;
  notes?: string;
  submittedAt: Date;
  approvedBy?: string;
  approvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface Territory {
  id: string;
  congregationId: string;
  name: string;
  description: string;
  boundaries: string;
  currentAssignee?: string;
  assignmentDate?: Date;
  expectedReturnDate?: Date;
  lastWorked?: Date;
  status: 'available' | 'assigned' | 'completed' | 'needs_attention';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ReturnVisit {
  id: string;
  memberId: string;
  congregationId: string;
  contactName: string;
  address: string;
  phone?: string;
  email?: string;
  initialVisitDate: Date;
  lastVisitDate: Date;
  nextVisitDate?: Date;
  interest: 'low' | 'medium' | 'high';
  status: 'active' | 'study_started' | 'inactive' | 'moved' | 'not_interested';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Critical Implementation Requirements

1. **Multi-Tenant Data Isolation**: Every field service query must include congregation_id filtering
2. **Data Privacy**: Secure handling of return visit contact information
3. **Type Safety Enforcement**: All API calls use tRPC procedures with Zod validation
4. **Database-First Testing**: Real database with comprehensive field service scenarios
5. **Performance Optimization**: Efficient service reporting and analytics processing
6. **Audit Logging**: Complete tracking of all service activities and territory assignments

## Definition of Done

- [ ] Field service time tracking with individual and group reporting capabilities
- [ ] Territory assignment and tracking with coverage monitoring and optimization
- [ ] Service group coordination with meeting planning and activity scheduling
- [ ] Return visit and Bible study tracking with progress monitoring and follow-up coordination
- [ ] Service reporting with monthly submissions and congregation analytics
- [ ] Service meeting integration with demonstration coordination and training support
- [ ] Field service calendar with activity planning and coordination tools
- [ ] All unit tests pass with real field service scenarios
- [ ] Integration tests validate complete service management workflow
- [ ] E2E tests confirm service overseer interface and reporting capabilities
- [ ] Code review completed and approved
- [ ] Documentation updated with field service management features

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: BMad Master Task Executor
- Date: 2025-01-24

### Debug Log References
- None yet

### Completion Notes
- Story recreated with comprehensive field service management system
- Time tracking and territory coordination with coverage monitoring
- Service group management with meeting planning and activity scheduling
- Complete API specification with tRPC procedures for field service management
- Testing requirements defined with complex service scenarios

### File List
- docs/stories/4.1.story.md (recreated)

### Change Log
- 2025-01-24: Story recreated with comprehensive field service specification
