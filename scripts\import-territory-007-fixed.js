/**
 * Import Territory 007 - Fixed Parsing Script
 * 
 * Territory 007 has a unique structure with building apartments
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const TERRITORY_NUMBER = '007';
const CONGREGATION_ID = '1441'; // Coral Oeste
const ZIP_CODE = 'Miami, FL 33144';
const DISPLAY_ORDER = 7;

/**
 * Parse addresses from Excel data for Territory 007 building structure
 */
function parseAddresses(excelData) {
  const addresses = [];
  let currentBuilding = '';
  let isInBuilding = false;
  
  for (let i = 0; i < excelData.length; i++) {
    const row = excelData[i];
    
    if (!row || row.length === 0) continue;
    
    // Skip header rows and metadata
    if (i < 7) continue;
    
    // Look for building indicators in row 3: "Edif. 6537"
    if (i === 2 && row[6] && row[6].toString().includes('Edif')) {
      const buildingInfo = row[6].toString().trim();
      // Extract building number from "Edif. 6537"
      const buildingMatch = buildingInfo.match(/Edif\.\s*(\d+)/);
      if (buildingMatch) {
        currentBuilding = `${buildingMatch[1]} W FLAGLER ST`;
        isInBuilding = true;
        console.log(`🏢 Found building: ${currentBuilding}`);
      }
      continue;
    }
    
    // Column B (index 1) contains apartment numbers
    const cellB = row[1];
    if (!cellB) continue;
    
    const cellValue = cellB.toString().trim();
    if (!cellValue) continue;
    
    // Skip Excel date serial numbers
    if (typeof cellB === 'number' && cellB > 40000) {
      console.log(`⏭️  Skipping Excel date serial: ${cellB}`);
      continue;
    }
    
    // Skip header text
    if (cellValue.includes('REGISTRO') || cellValue.includes('Casa') || 
        cellValue.includes('Fechas') || cellValue.includes('Observaciones')) {
      continue;
    }
    
    // Check if this is an apartment number
    if (/^\d+$/.test(cellValue) && isInBuilding && currentBuilding) {
      const apartmentNumber = cellValue;
      const fullAddress = `Apt ${apartmentNumber}, ${currentBuilding}, ${ZIP_CODE}`;
      
      // Get notes from column G (index 6) if available
      let notes = null;
      if (row[6] && typeof row[6] === 'string') {
        const noteText = row[6].toString().trim();
        if (noteText && noteText !== 'null' && noteText !== '' && 
            !noteText.includes('Observaciones') && !noteText.includes('H-23')) {
          notes = noteText;
        }
      }
      
      addresses.push({
        address: fullAddress,
        notes: notes,
        street: currentBuilding,
        houseNumber: apartmentNumber,
        isBuilding: true
      });
      
      console.log(`🏢 Added address: ${fullAddress}${notes ? ` (${notes})` : ''}`);
    }
    
    // Also check column H (index 7) for additional apartments
    const cellH = row[7];
    if (cellH && /^\d+$/.test(cellH.toString().trim()) && isInBuilding && currentBuilding) {
      const apartmentNumber = cellH.toString().trim();
      const fullAddress = `Apt ${apartmentNumber}, ${currentBuilding}, ${ZIP_CODE}`;
      
      addresses.push({
        address: fullAddress,
        notes: null,
        street: currentBuilding,
        houseNumber: apartmentNumber,
        isBuilding: true
      });
      
      console.log(`🏢 Added address: ${fullAddress}`);
    }
  }
  
  return addresses;
}

async function importTerritory007Fixed() {
  try {
    console.log(`📂 Importing Territory ${TERRITORY_NUMBER} (Fixed Building Parsing)...`);

    // Read Excel file
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${TERRITORY_NUMBER}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    const worksheet = workbook.Sheets['Terr 7'];
    
    if (!worksheet) {
      console.error(`❌ Sheet 'Terr 7' not found`);
      console.log(`Available sheets: ${workbook.SheetNames.join(', ')}`);
      process.exit(1);
    }
    
    console.log(`📊 Using sheet: Terr 7`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse addresses with building logic
    const addresses = parseAddresses(excelData);
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    if (addresses.length === 0) {
      console.log(`⚠️  No addresses found to import for Territory ${TERRITORY_NUMBER}`);
      return;
    }
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: CONGREGATION_ID }
    });

    if (!congregation) {
      console.error(`❌ Congregation ${CONGREGATION_ID} (Coral Oeste) not found`);
      process.exit(1);
    }

    console.log(`✅ Found congregation: ${congregation.name}`);
    
    // Clear existing territory
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: congregation.id,
        territory: {
          territoryNumber: TERRITORY_NUMBER
        }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: congregation.id,
        territoryNumber: TERRITORY_NUMBER
      }
    });
    
    console.log(`🗑️  Cleared existing Territory ${TERRITORY_NUMBER}`);
    
    // Create the territory with all addresses combined
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: congregation.id,
        territoryNumber: TERRITORY_NUMBER,
        address: allAddresses,
        notes: allNotes || null,
        status: 'available',
        displayOrder: DISPLAY_ORDER
      }
    });
    
    console.log(`✅ Created Territory ${TERRITORY_NUMBER} with ${addresses.length} addresses`);
    console.log(`📍 Display Order: ${DISPLAY_ORDER}`);
    
    // Display summary
    console.log('\n📋 Address Summary:');
    console.log(`   🏢 Building: ${addresses[0]?.street || 'Unknown'}`);
    console.log(`   🏠 Apartments: ${addresses.length}`);
    console.log(`   📝 With Notes: ${addresses.filter(addr => addr.notes).length}`);

  } catch (error) {
    console.error(`❌ Error importing Territory ${TERRITORY_NUMBER}:`, error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  importTerritory007Fixed();
}

module.exports = { importTerritory007Fixed };
