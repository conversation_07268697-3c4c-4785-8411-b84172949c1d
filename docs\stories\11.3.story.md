# Story 11.3: Territory Status Management

**Epic:** Epic 11: Territory Assignment & Management  
**Story Points:** 8  
**Priority:** High  
**Status:** Draft

## Story

**As a** congregation administrator,  
**I want** to manage territory status changes,  
**so that** territory availability and workflow states are accurately tracked.

## Acceptance Criteria

1. Territory status can be updated to: available, assigned, completed, unavailable
2. Status change interface provides reason/notes field for documentation
3. Status changes are logged with timestamp and user who made the change
4. Completed territories automatically become available for reassignment
5. Out of service territories are excluded from assignment workflows
6. Status change notifications are sent to relevant administrators

## Tasks / Subtasks

- [ ] Create territory status management interface (AC: 1, 2)
  - [ ] Build StatusManager component for territory status updates
  - [ ] Create status selection dropdown with all valid status options
  - [ ] Add reason/notes field for documenting status changes
  - [ ] Implement status change confirmation dialog
  - [ ] Add visual status indicators with appropriate colors and icons
- [ ] Implement status change workflow (AC: 3, 4)
  - [ ] Create updateTerritoryStatus API endpoint with audit logging
  - [ ] Log all status changes with timestamp and user information
  - [ ] Implement automatic status transitions (completed → available)
  - [ ] Add status change validation and business rules
  - [ ] Create status change audit trail in database
- [ ] Build status change logging system (AC: 3)
  - [ ] Create TerritoryStatusLog model for audit trail
  - [ ] Record status changes with user, timestamp, reason, and old/new status
  - [ ] Implement status change history view for administrators
  - [ ] Add status change search and filtering capabilities
  - [ ] Create status change reports and analytics
- [ ] Implement workflow automation (AC: 4, 5)
  - [ ] Auto-transition completed territories to available status
  - [ ] Exclude out-of-service territories from assignment interfaces
  - [ ] Add workflow rules for status transitions
  - [ ] Implement status-based territory filtering in assignment workflows
  - [ ] Create automated status validation and enforcement
- [ ] Create status change notifications (AC: 6)
  - [ ] Implement notification system for status changes
  - [ ] Send notifications to relevant administrators and coordinators
  - [ ] Add configurable notification preferences
  - [ ] Create email notifications for critical status changes
  - [ ] Implement in-app notification system
- [ ] Integrate with territory dashboard (Integration)
  - [ ] Add status management actions to territory cards
  - [ ] Update territory dashboard real-time after status changes
  - [ ] Refresh territory counts and status summaries
  - [ ] Add bulk status change functionality for multiple territories
- [ ] Create territory status service (Backend)
  - [ ] Implement TerritoryStatusService with status management methods
  - [ ] Add status transition validation and business logic
  - [ ] Create status change audit logging functionality
  - [ ] Implement notification triggering for status changes
  - [ ] Add status-based territory filtering methods
- [ ] Write comprehensive tests (Testing Standards)
  - [ ] Unit tests for status management logic and validation
  - [ ] Integration tests for status change API endpoints
  - [ ] Test status change audit logging and history
  - [ ] Test workflow automation and status transitions
  - [ ] E2E tests for status management interface and workflows

## Dev Notes

### Dependencies and Prerequisites
**DEPENDENCY**: This story depends on:
- Story 10.1 (Territory Database Schema) - Territory model with status field
- Story 10.3 (Territory Management Admin Interface) - Admin dashboard for integration

### Territory Status Architecture
[Source: docs/territories-architecture.md#territory-management-service]

**Key Interface:**
- `updateTerritoryStatus(id: string, status: TerritoryStatus): Promise<Territory>`

**Territory Status Enum:** available, assigned, completed, out_of_service
**Technology Stack:** TypeScript service classes, Prisma for database operations

### Status Management Requirements
[Source: docs/territories-architecture.md#coding-standards]

**Territory Status Consistency:** When updating territory assignments, always update both territory.status and assignment.status in the same transaction

### Database Schema Integration
[Source: docs/territories-architecture.md#database-schema]

**Territory Status Field:**
- status: ENUM('available', 'assigned', 'completed', 'out_of_service') with default 'available'
- Status changes tracked through audit logging system

**New TerritoryStatusLog Model:**
```sql
CREATE TABLE territory_status_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    territory_id UUID NOT NULL,
    old_status VARCHAR(20) NOT NULL,
    new_status VARCHAR(20) NOT NULL,
    changed_by UUID NOT NULL,
    reason TEXT,
    changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    congregation_id VARCHAR(8) NOT NULL,
    
    CONSTRAINT fk_territory_status_logs_territory 
        FOREIGN KEY (territory_id) REFERENCES territories(id) ON DELETE CASCADE,
    CONSTRAINT fk_territory_status_logs_changed_by 
        FOREIGN KEY (changed_by) REFERENCES members(id) ON DELETE RESTRICT,
    CONSTRAINT fk_territory_status_logs_congregation 
        FOREIGN KEY (congregation_id) REFERENCES congregations(id) ON DELETE CASCADE
);
```

### API Specification
**Status Management API Endpoints:**
- `PUT /api/territories/{id}/status` - Update territory status
- `GET /api/territories/{id}/status-history` - Get status change history
- `POST /api/territories/bulk-status` - Bulk status updates
- Request Body: `{ status: TerritoryStatus, reason?: string }`
- Response: Updated Territory object with new status

### Component Architecture
**Component Organization:**
- `src/components/territories/admin/StatusManager.tsx` - Main status management interface
- `src/components/territories/shared/TerritoryStatus.tsx` - Status indicators and display
- `src/components/territories/admin/StatusChangeDialog.tsx` - Status change confirmation
- `src/components/territories/admin/StatusHistory.tsx` - Status change audit trail

### Technology Stack
[Source: docs/territories-architecture.md#tech-stack]
- **Frontend**: Next.js 14+ with TypeScript, Tailwind CSS for status indicators
- **State Management**: React Query for status change caching and real-time updates
- **Backend**: Prisma ORM for status updates and audit logging
- **Notifications**: Integration with existing notification system
- **Validation**: Zod validation for status change requests

### Status Workflow Rules
**Status Transition Rules:**
- available → assigned (via assignment interface)
- assigned → completed (via completion workflow)
- completed → available (automatic transition)
- any status → out_of_service (manual admin action)
- out_of_service → available (manual admin action)

**Workflow Exclusions:**
- out_of_service territories excluded from assignment interfaces
- completed territories automatically become available for reassignment

### File Structure and Locations
[Source: docs/territories-architecture.md#unified-project-structure]
- **Status Manager**: `src/components/territories/admin/StatusManager.tsx`
- **API Route**: `src/app/api/territories/[id]/status/route.ts`
- **Service**: `src/services/territories/TerritoryStatusService.ts`
- **Status Log Model**: Add to `prisma/schema.prisma`
- **Types**: Extend `src/types/territories/territory.ts`

### Security and Authorization
**Security Requirements:**
- Admin role verification for status change functionality
- Congregation isolation for all status operations
- Audit trail for all status changes with user tracking
- Validation of status transition rules and business logic

### Notification System Integration
**Notification Requirements:**
- Status change notifications to administrators and coordinators
- Configurable notification preferences per user
- Email notifications for critical status changes (out_of_service)
- In-app notifications for status updates

### Performance Considerations
**Optimization Requirements:**
- Efficient status filtering for assignment workflows
- Bulk status update operations for multiple territories
- Real-time dashboard updates after status changes
- Optimized queries for status-based territory filtering

### Testing Requirements
[Source: docs/territories-architecture.md#testing-strategy]
- **Status Logic Tests**: Test all status transition rules and validation
- **API Tests**: Verify status change endpoints and audit logging
- **Workflow Tests**: Test automated status transitions and exclusions
- **Security Tests**: Verify role-based access and congregation isolation
- **Integration Tests**: Test status change notifications and dashboard updates

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-25 | 1.0 | Initial story creation for territory status management | PO Agent |

## Dev Agent Record

### Agent Model Used
*To be populated by development agent*

### Debug Log References
*To be populated by development agent*

### Completion Notes List
*To be populated by development agent*

### File List
*To be populated by development agent*

## QA Results
*To be populated by QA agent*
