#!/usr/bin/env node

/**
 * Direct Database Restore
 *
 * This script directly connects to PostgreSQL and restores the backup
 * without using Prisma migrations.
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function restoreBackupDirect() {
    console.log('🔄 RESTORING DATABASE FROM BACKUP (DIRECT METHOD)...');
    console.log('');

    let client;

    try {
        // 1. Check if backup file exists
        const backupFile = path.join(__dirname, '../hermanos-07-25-25E.sql');
        if (!fs.existsSync(backupFile)) {
            throw new Error('Backup file hermanos-07-25-25E.sql not found');
        }
        console.log('✅ Backup file found: hermanos-07-25-25E.sql');

        // 2. Read backup file
        console.log('📖 Reading backup file...');
        const backupContent = fs.readFileSync(backupFile, 'utf8');
        console.log(`   📊 Backup file size: ${(backupContent.length / 1024 / 1024).toFixed(2)} MB`);

        // 3. Connect to PostgreSQL
        console.log('🔌 Connecting to PostgreSQL...');
        client = new Client({
            host: 'localhost',
            port: 5432,
            user: 'mywebsites',
            password: 'password',
            database: 'postgres' // Connect to postgres database first
        });

        await client.connect();
        console.log('✅ Connected to PostgreSQL');

        // 4. Force disconnect all connections and drop database
        console.log('🔌 Terminating active connections...');
        try {
            await client.query(`
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = 'hermanos' AND pid <> pg_backend_pid();
            `);
            console.log('✅ Active connections terminated');
        } catch (error) {
            console.log('⚠️  Connection termination warning:', error.message);
        }

        console.log('🗑️  Dropping existing database...');
        try {
            await client.query('DROP DATABASE IF EXISTS hermanos;');
            console.log('✅ Database dropped');
        } catch (error) {
            console.log('⚠️  Database drop warning:', error.message);
        }

        console.log('🆕 Creating new database...');
        await client.query('CREATE DATABASE hermanos;');
        console.log('✅ Database created');

        // 5. Disconnect from postgres and connect to hermanos
        await client.end();

        console.log('🔌 Connecting to hermanos database...');
        client = new Client({
            host: 'localhost',
            port: 5432,
            user: 'mywebsites',
            password: 'password',
            database: 'hermanos'
        });

        await client.connect();
        console.log('✅ Connected to hermanos database');

        // 6. Execute backup SQL
        console.log('📥 Executing backup SQL...');

        // Split the backup into individual statements
        const statements = backupContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

        console.log(`   📊 Found ${statements.length} SQL statements to execute`);

        let successCount = 0;
        let errorCount = 0;

        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];

            // Skip comments and empty statements
            if (statement.startsWith('--') || statement.trim().length === 0) {
                continue;
            }

            try {
                await client.query(statement + ';');
                successCount++;

                if (i % 100 === 0) {
                    console.log(`   🔄 Progress: ${i}/${statements.length} statements executed`);
                }
            } catch (error) {
                errorCount++;

                // Log only significant errors, skip common warnings
                if (!error.message.includes('already exists') &&
                    !error.message.includes('does not exist') &&
                    !error.message.includes('NOTICE')) {
                    console.log(`   ⚠️  Statement ${i} warning: ${error.message.substring(0, 100)}...`);
                }
            }
        }

        console.log(`✅ Backup restoration completed:`);
        console.log(`   📊 Successful statements: ${successCount}`);
        console.log(`   ⚠️  Warnings/Errors: ${errorCount}`);

        // 7. Verify restoration
        console.log('🔍 Verifying restoration...');

        const congregationCount = await client.query('SELECT COUNT(*) FROM congregations;');
        const memberCount = await client.query('SELECT COUNT(*) FROM members;');

        console.log(`   📊 Congregations restored: ${congregationCount.rows[0].count}`);
        console.log(`   👥 Members restored: ${memberCount.rows[0].count}`);

        // 8. Get sample login data
        const sampleCongregation = await client.query('SELECT id, name FROM congregations LIMIT 1;');
        const sampleMember = await client.query('SELECT email, name, role FROM members WHERE is_active = true LIMIT 1;');

        console.log('');
        console.log('🎉 DATABASE RESTORATION SUCCESSFUL!');
        console.log('');

        if (sampleCongregation.rows.length > 0) {
            console.log('📋 SAMPLE LOGIN CREDENTIALS:');
            console.log(`   Congregation ID: ${sampleCongregation.rows[0].id}`);
            console.log(`   Congregation Name: ${sampleCongregation.rows[0].name}`);

            if (sampleMember.rows.length > 0) {
                console.log(`   Sample Member: ${sampleMember.rows[0].email}`);
                console.log(`   Member Name: ${sampleMember.rows[0].name}`);
                console.log(`   Member Role: ${sampleMember.rows[0].role}`);
            }
        }

        console.log('');
        console.log('🎯 NEXT STEPS:');
        console.log('   1. Restart the development server');
        console.log('   2. Test authentication with restored data');
        console.log('   3. Check congregation PIN and member PINs');
        console.log('   4. Verify all features work correctly');

    } catch (error) {
        console.error('❌ Error during database restoration:', error);
        throw error;
    } finally {
        if (client) {
            await client.end();
        }
    }
}

// Run the restoration
restoreBackupDirect()
    .catch((error) => {
        console.error('Failed to restore database:', error);
        process.exit(1);
    });
