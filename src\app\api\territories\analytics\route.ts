/**
 * Territory Analytics API Endpoint
 *
 * Provides comprehensive analytics for territories including property analytics,
 * activity-based reports, comments analysis, and completion tracking.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PropertyAnalyticsService } from '@/services/territories/PropertyAnalyticsService';
import { ActivityAnalyticsService } from '@/services/territories/ActivityAnalyticsService';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information to check role-based permissions
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: {
        role: true,
        congregationId: true
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions for analytics
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for analytics' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview';
    const territoryId = searchParams.get('territoryId');

    switch (type) {
      case 'properties':
        if (territoryId) {
          // Get analytics for specific territory
          const territoryAnalytics = await PropertyAnalyticsService.getTerritoryPropertyAnalytics(
            territoryId,
            member.congregationId
          );

          return NextResponse.json({
            success: true,
            data: territoryAnalytics,
            timestamp: new Date().toISOString()
          });
        } else {
          // Get congregation-wide property analytics
          const [analytics, summary, distribution, breakdown] = await Promise.all([
            PropertyAnalyticsService.getCongregationPropertyAnalytics(member.congregationId),
            PropertyAnalyticsService.getPropertySummary(member.congregationId),
            PropertyAnalyticsService.getPropertyDistribution(member.congregationId),
            PropertyAnalyticsService.getPropertyTypeBreakdown(member.congregationId)
          ]);

          return NextResponse.json({
            success: true,
            data: {
              analytics,
              summary,
              distribution,
              breakdown
            },
            timestamp: new Date().toISOString()
          });
        }

      case 'activities':
        if (territoryId) {
          // Get activities for specific territory
          const territoryActivities = await ActivityAnalyticsService.getTerritoryActivityAnalytics(
            territoryId,
            member.congregationId
          );

          return NextResponse.json({
            success: true,
            data: territoryActivities,
            timestamp: new Date().toISOString()
          });
        } else {
          // Get congregation-wide activity analytics
          const [summary, patterns, trends] = await Promise.all([
            ActivityAnalyticsService.getActivitySummary(member.congregationId),
            ActivityAnalyticsService.getMemberActivityPatterns(member.congregationId),
            ActivityAnalyticsService.getActivityTrends(member.congregationId, 12)
          ]);

          return NextResponse.json({
            success: true,
            data: {
              summary,
              patterns,
              trends
            },
            timestamp: new Date().toISOString()
          });
        }

      case 'comments':
        // Get territory comments and notes analytics
        const commentsData = await getCommentsAnalytics(member.congregationId, territoryId);

        return NextResponse.json({
          success: true,
          data: commentsData,
          timestamp: new Date().toISOString()
        });

      case 'completions':
        // Get territory completion analytics
        const completionsData = await getCompletionAnalytics(member.congregationId, territoryId);

        return NextResponse.json({
          success: true,
          data: completionsData,
          timestamp: new Date().toISOString()
        });

      case 'overview':
      default:
        // Get comprehensive overview analytics
        const [
          propertySummary,
          activitySummary,
          topTerritories,
          memberPatterns
        ] = await Promise.all([
          PropertyAnalyticsService.getPropertySummary(member.congregationId),
          ActivityAnalyticsService.getActivitySummary(member.congregationId),
          PropertyAnalyticsService.getTopTerritoriesByProperties(member.congregationId, 5),
          ActivityAnalyticsService.getMemberActivityPatterns(member.congregationId)
        ]);

        return NextResponse.json({
          success: true,
          data: {
            properties: propertySummary,
            activities: activitySummary,
            topTerritories,
            memberPatterns: memberPatterns.slice(0, 10) // Top 10 most active members
          },
          timestamp: new Date().toISOString()
        });
    }

  } catch (error) {
    console.error('Territory analytics GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get analytics data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * Get comments and notes analytics
 */
async function getCommentsAnalytics(congregationId: string, territoryId?: string | null) {
  try {
    // Get assignment notes
    const assignmentWhereClause: any = {
      congregationId,
      notes: {
        not: null
      }
    };
    if (territoryId) {
      assignmentWhereClause.territoryId = territoryId;
    }

    const assignmentComments = await prisma.territoryAssignment.findMany({
      where: assignmentWhereClause,
      include: {
        territory: {
          select: {
            territoryNumber: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get visit notes
    const visitWhereClause: any = {
      congregationId,
      notes: {
        not: null
      }
    };
    if (territoryId) {
      visitWhereClause.assignment = {
        territoryId
      };
    }

    const visitComments = await prisma.territoryVisit.findMany({
      where: visitWhereClause,
      include: {
        assignment: {
          include: {
            territory: {
              select: {
                territoryNumber: true
              }
            },
            member: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Combine all comments
    const comments = [
      ...assignmentComments.map(assignment => ({
        id: assignment.id,
        note: assignment.notes,
        territory: assignment.territory,
        member: assignment.member,
        createdAt: assignment.createdAt
      })),
      ...visitComments.map(visit => ({
        id: visit.id,
        note: visit.notes,
        territory: visit.assignment.territory,
        member: visit.assignment.member,
        createdAt: visit.createdAt
      }))
    ].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    // Group by territory
    const territoryComments: { [territoryNumber: string]: any[] } = {};
    comments.forEach(comment => {
      if (comment.territory) {
        const territoryNumber = comment.territory.territoryNumber;
        if (!territoryComments[territoryNumber]) {
          territoryComments[territoryNumber] = [];
        }
        territoryComments[territoryNumber].push(comment);
      }
    });

    // Calculate statistics
    const totalComments = comments.length;
    const territoriesWithComments = Object.keys(territoryComments).length;
    const averageCommentsPerTerritory = territoriesWithComments > 0
      ? Math.round((totalComments / territoriesWithComments) * 10) / 10
      : 0;

    return {
      totalComments,
      territoriesWithComments,
      averageCommentsPerTerritory,
      comments: comments.slice(0, 100), // Limit to recent 100 comments
      territoryBreakdown: Object.entries(territoryComments).map(([territoryNumber, comments]) => ({
        territoryNumber,
        commentCount: comments.length,
        lastComment: comments[0]?.createdAt || null
      })).sort((a, b) => b.commentCount - a.commentCount)
    };

  } catch (error) {
    console.error('Error getting comments analytics:', error);
    return {
      totalComments: 0,
      territoriesWithComments: 0,
      averageCommentsPerTerritory: 0,
      comments: [],
      territoryBreakdown: []
    };
  }
}

/**
 * Get territory completion analytics
 */
async function getCompletionAnalytics(congregationId: string, territoryId?: string | null) {
  try {
    const whereClause: any = {
      congregationId,
      status: 'completed'
    };
    if (territoryId) {
      whereClause.territoryId = territoryId;
    }

    const completions = await prisma.territoryAssignment.findMany({
      where: whereClause,
      include: {
        territory: {
          select: {
            territoryNumber: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        completedAt: 'desc'
      }
    });

    // Calculate completion statistics
    const totalCompletions = completions.length;
    const uniqueTerritories = new Set(completions.map(c => c.territory?.territoryNumber).filter(Boolean));
    const uniqueMembers = new Set(completions.map(c => c.member?.name).filter(Boolean));

    // Calculate average completion time
    const completionTimes = completions
      .filter(c => c.assignedAt && c.completedAt)
      .map(c => {
        const assigned = new Date(c.assignedAt);
        const completed = new Date(c.completedAt!);
        return Math.floor((completed.getTime() - assigned.getTime()) / (1000 * 60 * 60 * 24));
      });

    const averageCompletionTime = completionTimes.length > 0
      ? Math.round(completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length)
      : 0;

    // Member completion patterns
    const memberCompletions: { [memberName: string]: number } = {};
    completions.forEach(completion => {
      if (completion.member) {
        memberCompletions[completion.member.name] = (memberCompletions[completion.member.name] || 0) + 1;
      }
    });

    return {
      totalCompletions,
      uniqueTerritories: uniqueTerritories.size,
      uniqueMembers: uniqueMembers.size,
      averageCompletionTime,
      completions: completions.slice(0, 50), // Recent 50 completions
      memberCompletions: Object.entries(memberCompletions)
        .map(([memberName, count]) => ({ memberName, completions: count }))
        .sort((a, b) => b.completions - a.completions)
    };

  } catch (error) {
    console.error('Error getting completion analytics:', error);
    return {
      totalCompletions: 0,
      uniqueTerritories: 0,
      uniqueMembers: 0,
      averageCompletionTime: 0,
      completions: [],
      memberCompletions: []
    };
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET for analytics data.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET for analytics data.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET for analytics data.' },
    { status: 405 }
  );
}
