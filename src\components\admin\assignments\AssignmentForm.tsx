/**
 * Assignment Form Component
 *
 * Form for creating new section assignments with member selection,
 * section selection, and scope definition.
 */

import React, { useState } from 'react';
import { ADMINISTRATIVE_SECTIONS, ScopeDefinition } from '@/lib/constants/administrativeSections';

interface EligibleMember {
  id: string;
  name: string;
  role: string;
  roleDisplayName: string;
  currentAssignments: string[];
  eligibleSections: Array<{
    id: string;
    name: string;
    description: string;
    color: string;
    isAssigned: boolean;
  }>;
  assignmentCount: number;
}

interface AssignmentFormProps {
  members: EligibleMember[];
  onSubmit: (data: {
    memberId: string;
    sectionType: ADMINISTRATIVE_SECTIONS;
    scopeDefinition: ScopeDefinition;
    reason?: string;
  }) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export default function AssignmentForm({
  members,
  onSubmit,
  onCancel,
  isLoading,
}: AssignmentFormProps) {
  const [selectedMember, setSelectedMember] = useState<string>('');
  const [selectedSection, setSelectedSection] = useState<ADMINISTRATIVE_SECTIONS | ''>('');
  const [scopeDefinition, setScopeDefinition] = useState<ScopeDefinition>({
    description: '',
    limitations: [],
    specificAreas: [],
    notes: '',
  });
  const [reason, setReason] = useState<string>('');
  const [newLimitation, setNewLimitation] = useState<string>('');
  const [newSpecificArea, setNewSpecificArea] = useState<string>('');

  const selectedMemberData = members.find(m => m.id === selectedMember);
  const availableSections = selectedMemberData?.eligibleSections.filter(s => !s.isAssigned) || [];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedMember || !selectedSection) {
      return;
    }

    onSubmit({
      memberId: selectedMember,
      sectionType: selectedSection,
      scopeDefinition,
      reason: reason.trim() || undefined,
    });
  };

  const addLimitation = () => {
    if (newLimitation.trim()) {
      setScopeDefinition(prev => ({
        ...prev,
        limitations: [...(prev.limitations || []), newLimitation.trim()],
      }));
      setNewLimitation('');
    }
  };

  const removeLimitation = (index: number) => {
    setScopeDefinition(prev => ({
      ...prev,
      limitations: prev.limitations?.filter((_, i) => i !== index) || [],
    }));
  };

  const addSpecificArea = () => {
    if (newSpecificArea.trim()) {
      setScopeDefinition(prev => ({
        ...prev,
        specificAreas: [...(prev.specificAreas || []), newSpecificArea.trim()],
      }));
      setNewSpecificArea('');
    }
  };

  const removeSpecificArea = (index: number) => {
    setScopeDefinition(prev => ({
      ...prev,
      specificAreas: prev.specificAreas?.filter((_, i) => i !== index) || [],
    }));
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        Nueva Asignación Administrativa
      </h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Member Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Seleccionar Miembro
          </label>
          <select
            value={selectedMember}
            onChange={(e) => {
              setSelectedMember(e.target.value);
              setSelectedSection('');
            }}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          >
            <option value="">Selecciona un miembro...</option>
            {members.map(member => (
              <option key={member.id} value={member.id}>
                {member.name} ({member.roleDisplayName}) - {member.assignmentCount} asignaciones
              </option>
            ))}
          </select>
        </div>

        {/* Section Selection */}
        {selectedMemberData && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Seleccionar Sección Administrativa
            </label>
            <select
              value={selectedSection}
              onChange={(e) => setSelectedSection(e.target.value as ADMINISTRATIVE_SECTIONS)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="">Selecciona una sección...</option>
              {availableSections.map(section => (
                <option key={section.id} value={section.id}>
                  {section.name} - {section.description}
                </option>
              ))}
            </select>

            {availableSections.length === 0 && (
              <p className="text-sm text-gray-500 mt-1">
                Este miembro ya tiene asignaciones en todas las secciones elegibles.
              </p>
            )}
          </div>
        )}

        {/* Scope Definition */}
        {selectedSection && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              Definición del Alcance
            </h3>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descripción de la Responsabilidad
              </label>
              <textarea
                value={scopeDefinition.description || ''}
                onChange={(e) => setScopeDefinition(prev => ({
                  ...prev,
                  description: e.target.value,
                }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Describe las responsabilidades específicas..."
              />
            </div>

            {/* Specific Areas */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Áreas Específicas
              </label>
              <div className="flex space-x-2 mb-2">
                <input
                  type="text"
                  value={newSpecificArea}
                  onChange={(e) => setNewSpecificArea(e.target.value)}
                  className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Agregar área específica..."
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecificArea())}
                />
                <button
                  type="button"
                  onClick={addSpecificArea}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                  Agregar
                </button>
              </div>
              {scopeDefinition.specificAreas && scopeDefinition.specificAreas.length > 0 && (
                <div className="space-y-1">
                  {scopeDefinition.specificAreas.map((area, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                      <span className="text-sm">{area}</span>
                      <button
                        type="button"
                        onClick={() => removeSpecificArea(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remover
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Limitations */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Limitaciones
              </label>
              <div className="flex space-x-2 mb-2">
                <input
                  type="text"
                  value={newLimitation}
                  onChange={(e) => setNewLimitation(e.target.value)}
                  className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Agregar limitación..."
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addLimitation())}
                />
                <button
                  type="button"
                  onClick={addLimitation}
                  className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700"
                >
                  Agregar
                </button>
              </div>
              {scopeDefinition.limitations && scopeDefinition.limitations.length > 0 && (
                <div className="space-y-1">
                  {scopeDefinition.limitations.map((limitation, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                      <span className="text-sm">{limitation}</span>
                      <button
                        type="button"
                        onClick={() => removeLimitation(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remover
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notas Adicionales
              </label>
              <textarea
                value={scopeDefinition.notes || ''}
                onChange={(e) => setScopeDefinition(prev => ({
                  ...prev,
                  notes: e.target.value,
                }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={2}
                placeholder="Notas adicionales sobre la asignación..."
              />
            </div>
          </div>
        )}

        {/* Reason */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Razón de la Asignación (Opcional)
          </label>
          <input
            type="text"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Razón para esta asignación..."
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            disabled={isLoading || !selectedMember || !selectedSection}
          >
            {isLoading ? 'Asignando...' : 'Crear Asignación'}
          </button>
        </div>
      </form>
    </div>
  );
}
