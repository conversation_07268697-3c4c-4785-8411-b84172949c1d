'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Translation type definitions
export type Language = 'es' | 'en';

export interface TranslationData {
  [key: string]: string | TranslationData;
}

export interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, string>) => string;
  isLoading: boolean;
  translations: TranslationData;
}

// Create the context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Translation data - Spanish translations
const spanishTranslations: TranslationData = {
  common: {
    save: 'Guardar',
    cancel: 'Cancelar',
    edit: 'Editar',
    delete: 'Eliminar',
    search: 'Buscar',
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    yes: 'Sí',
    no: 'No',
    close: 'Cerrar',
    back: 'Volver',
    next: 'Siguiente',
    previous: 'Anterior',
    add: 'Agregar',
    remove: 'Eliminar',
    update: 'Actualizar',
    create: 'Crear',
    view: 'Ver',
    download: 'Descargar',
    upload: 'Subir',
    select: 'Seleccionar',
    confirm: 'Confirmar'
  },
  admin: {
    dashboard: 'Panel de Administración',
    welcome: 'Bienvenido a {{congregationName}}',
    cards: {
      members: {
        title: 'Miembros',
        description: 'Agregar, editar y gestionar miembros de la congregación'
      },
      midweek_meeting: {
        title: 'Entre Semana',
        description: 'Gestionar horarios y asignaciones de reuniones entre semana'
      },
      weekend_meeting: {
        title: 'Fin de Semana',
        description: 'Gestionar horarios de reuniones de fin de semana y discursos públicos'
      },
      field_service: {
        title: 'Servicio del Campo',
        description: 'Gestionar grupos de servicio del campo, territorios y horarios'
      },
      territories: {
        title: 'Territorios',
        description: 'Gestionar territorios de la congregación, asignaciones y seguimiento'
      },
      tasks: {
        title: 'Tareas',
        description: 'Gestionar tareas y asignaciones de la congregación'
      },
      assignments: {
        title: 'Asignaciones',
        description: 'Gestionar asignaciones de reuniones y servicio'
      },
      programs: {
        title: 'Programas',
        description: 'Gestionar programas de entre semana, fin de semana y especiales'
      },
      events: {
        title: 'Eventos',
        description: 'Gestionar eventos y actividades de la congregación'
      },
      letters: {
        title: 'Cartas',
        description: 'Gestionar cartas y comunicaciones de la congregación'
      },
      permissions: {
        title: 'Permisos',
        description: 'Gestionar permisos de miembros y derechos de acceso'
      },
      database: {
        title: 'Base de Datos',
        description: 'Respaldar y restaurar la base de datos de la congregación'
      },
      songs: {
        title: 'Canciones',
        description: 'Gestionar títulos de canciones y canciones personalizadas'
      },
      settings: {
        title: 'Configuración',
        description: 'Gestionar detalles y configuración de la congregación'
      }
    },
    members: {
      title: 'Gestión de Miembros',
      add_member: 'Agregar Miembro',
      edit_member: 'Editar Miembro',
      member_name: 'Nombre del Miembro',
      member_role: 'Función',
      member_email: 'Correo Electrónico',
      member_phone: 'Teléfono',
      search_members: 'Buscar miembros...',
      no_members_found: 'No se encontraron miembros',
      member_created: 'Miembro creado exitosamente',
      member_updated: 'Miembro actualizado exitosamente',
      member_deleted: 'Miembro eliminado exitosamente'
    },
    songs: {
      title: 'Administración de Canciones',
      song_number: 'Número',
      song_title: 'Título',
      sync_songs: 'Sincronizar Canciones',
      add_song: 'Añadir Canción',
      edit_song: 'Editar Canción',
      spanish_title: 'Título en Español',
      english_title: 'Título en Inglés',
      no_songs_found: 'No se encontraron canciones',
      syncing: 'Sincronizando...',
      sync_complete: 'Sincronización completada'
    },
    letters: {
      title: 'Gestión de Cartas',
      upload_letter: 'Subir Nueva Carta',
      letter_title: 'Título de la Carta',
      letter_category: 'Categoría',
      letter_date: 'Fecha',
      no_letters_found: 'No se encontraron cartas',
      letter_uploaded: 'Carta subida exitosamente',
      letter_deleted: 'Carta eliminada exitosamente'
    },
    events: {
      title: 'Gestión de Eventos',
      add_event: 'Agregar Evento',
      edit_event: 'Editar Evento',
      event_title: 'Título del Evento',
      event_date: 'Fecha del Evento',
      event_time: 'Hora del Evento',
      event_location: 'Ubicación',
      no_events_found: 'No se encontraron eventos',
      event_created: 'Evento creado exitosamente',
      event_updated: 'Evento actualizado exitosamente'
    },
    settings: {
      title: 'Configuración de la Congregación',
      congregation_info: 'Información de la Congregación',
      meeting_schedule: 'Horario de Reuniones',
      language_settings: 'Configuración de Idioma',
      congregation_name: 'Nombre de la Congregación',
      congregation_number: 'Número de Congregación',
      circuit_number: 'Número de Circuito',
      circuit_overseer: 'Superintendente de Circuito',
      address: 'Dirección',
      congregation_pin: 'PIN de la Congregación',
      language: 'Idioma de la Congregación',
      language_spanish: 'Español',
      language_english: 'Inglés',
      midweek_meeting: 'Reunión Entre Semana',
      weekend_meeting: 'Reunión de Fin de Semana',
      day: 'Día',
      time: 'Hora',
      settings_updated: 'Configuración actualizada exitosamente'
    },
    footer: {
      inicio: 'Inicio',
      territorios: 'Territorios',
      entre_semana: 'Entre Semana',
      fin_semana: 'Fin Semana',
      area_miembros: 'Area Miembros'
    }
  },
  member: {
    dashboard: 'Panel de Miembros',
    welcome: 'Bienvenido, {{memberName}}',
    field_service: 'Servicio del Campo',
    meetings: 'Reuniones',
    assignments: 'Asignaciones',
    tasks: 'Tareas',
    letters: 'Cartas',
    events: 'Eventos',
    midweek_meeting: 'Reunión Entre Semana',
    weekend_meeting: 'Reunión de Fin de Semana'
  }
};

// English translations
const englishTranslations: TranslationData = {
  common: {
    save: 'Save',
    cancel: 'Cancel',
    edit: 'Edit',
    delete: 'Delete',
    search: 'Search',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    yes: 'Yes',
    no: 'No',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    add: 'Add',
    remove: 'Remove',
    update: 'Update',
    create: 'Create',
    view: 'View',
    download: 'Download',
    upload: 'Upload',
    select: 'Select',
    confirm: 'Confirm'
  },
  admin: {
    dashboard: 'Administration Dashboard',
    welcome: 'Welcome to {{congregationName}}',
    cards: {
      members: {
        title: 'Members',
        description: 'Add, edit, and manage congregation members'
      },
      midweek_meeting: {
        title: 'Midweek Meeting',
        description: 'Manage midweek meeting schedules and assignments'
      },
      weekend_meeting: {
        title: 'Weekend Meeting',
        description: 'Manage weekend meeting schedules and public talks'
      },
      field_service: {
        title: 'Field Service',
        description: 'Manage field service groups, territories, and schedules'
      },
      territories: {
        title: 'Territories',
        description: 'Manage congregation territories, assignments and tracking'
      },
      tasks: {
        title: 'Tasks',
        description: 'Manage congregation tasks and assignments'
      },
      assignments: {
        title: 'Assignments',
        description: 'Manage meeting and service assignments'
      },
      programs: {
        title: 'Programs',
        description: 'Manage midweek, weekend and special programs'
      },
      events: {
        title: 'Events',
        description: 'Manage congregation events and activities'
      },
      letters: {
        title: 'Letters',
        description: 'Manage congregation letters and communications'
      },
      permissions: {
        title: 'Permissions',
        description: 'Manage member permissions and access rights'
      },
      database: {
        title: 'Database',
        description: 'Backup and restore the congregation database'
      },
      songs: {
        title: 'Songs',
        description: 'Manage song titles and custom songs'
      },
      settings: {
        title: 'Settings',
        description: 'Manage congregation details and settings'
      }
    },
    members: {
      title: 'Member Management',
      add_member: 'Add Member',
      edit_member: 'Edit Member',
      member_name: 'Member Name',
      member_role: 'Role',
      member_email: 'Email',
      member_phone: 'Phone',
      search_members: 'Search members...',
      no_members_found: 'No members found',
      member_created: 'Member created successfully',
      member_updated: 'Member updated successfully',
      member_deleted: 'Member deleted successfully'
    },
    songs: {
      title: 'Song Management',
      song_number: 'Number',
      song_title: 'Title',
      sync_songs: 'Sync Songs',
      add_song: 'Add Song',
      edit_song: 'Edit Song',
      spanish_title: 'Spanish Title',
      english_title: 'English Title',
      no_songs_found: 'No songs found',
      syncing: 'Syncing...',
      sync_complete: 'Sync completed'
    },
    letters: {
      title: 'Letter Management',
      upload_letter: 'Upload New Letter',
      letter_title: 'Letter Title',
      letter_category: 'Category',
      letter_date: 'Date',
      no_letters_found: 'No letters found',
      letter_uploaded: 'Letter uploaded successfully',
      letter_deleted: 'Letter deleted successfully'
    },
    events: {
      title: 'Event Management',
      add_event: 'Add Event',
      edit_event: 'Edit Event',
      event_title: 'Event Title',
      event_date: 'Event Date',
      event_time: 'Event Time',
      event_location: 'Location',
      no_events_found: 'No events found',
      event_created: 'Event created successfully',
      event_updated: 'Event updated successfully'
    },
    settings: {
      title: 'Congregation Settings',
      congregation_info: 'Congregation Information',
      meeting_schedule: 'Meeting Schedule',
      language_settings: 'Language Settings',
      congregation_name: 'Congregation Name',
      congregation_number: 'Congregation Number',
      circuit_number: 'Circuit Number',
      circuit_overseer: 'Circuit Overseer',
      address: 'Address',
      congregation_pin: 'Congregation PIN',
      language: 'Congregation Language',
      language_spanish: 'Spanish',
      language_english: 'English',
      midweek_meeting: 'Midweek Meeting',
      weekend_meeting: 'Weekend Meeting',
      day: 'Day',
      time: 'Time',
      settings_updated: 'Settings updated successfully'
    },
    footer: {
      inicio: 'Home',
      territorios: 'Territories',
      entre_semana: 'Midweek',
      fin_semana: 'Weekend',
      area_miembros: 'Members Area'
    }
  },
  member: {
    dashboard: 'Member Dashboard',
    welcome: 'Welcome, {{memberName}}',
    field_service: 'Field Service',
    meetings: 'Meetings',
    assignments: 'Assignments',
    tasks: 'Tasks',
    letters: 'Letters',
    events: 'Events',
    midweek_meeting: 'Midweek Meeting',
    weekend_meeting: 'Weekend Meeting'
  }
};

// Translation function
const getNestedValue = (obj: TranslationData, path: string): string => {
  const keys = path.split('.');
  let current: any = obj;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return path; // Return the key if translation not found
    }
  }

  return typeof current === 'string' ? current : path;
};

// Replace parameters in translation strings
const replaceParams = (text: string, params?: Record<string, string>): string => {
  if (!params) return text;

  let result = text;
  Object.entries(params).forEach(([key, value]) => {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
  });

  return result;
};

// Language Provider Component
interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('es');
  const [isLoading, setIsLoading] = useState(true);
  const [translations, setTranslations] = useState<TranslationData>(spanishTranslations);

  // Load language preference from localStorage and congregation settings
  useEffect(() => {
    const loadLanguagePreference = async () => {
      try {
        // First check localStorage for user preference
        const savedLanguage = localStorage.getItem('hermanos_language') as Language;

        // Then check congregation settings
        const token = localStorage.getItem('hermanos_token');
        if (token) {
          try {
            const response = await fetch('/api/admin/settings/congregation', {
              headers: {
                'Authorization': `Bearer ${token}`,
              },
            });

            if (response.ok) {
              const settings = await response.json();
              const congregationLanguage = settings.language || 'es';

              // Use saved preference if available, otherwise use congregation default
              const finalLanguage = savedLanguage || congregationLanguage;
              setLanguageState(finalLanguage);
              setTranslations(finalLanguage === 'en' ? englishTranslations : spanishTranslations);
            }
          } catch (error) {
            console.error('Failed to load congregation language settings:', error);
          }
        }

        // Fallback to saved language or default Spanish
        if (savedLanguage) {
          setLanguageState(savedLanguage);
          setTranslations(savedLanguage === 'en' ? englishTranslations : spanishTranslations);
        }
      } catch (error) {
        console.error('Failed to load language preference:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguagePreference();
  }, []);

  // Set language and persist to localStorage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    setTranslations(lang === 'en' ? englishTranslations : spanishTranslations);
    localStorage.setItem('hermanos_language', lang);
  };

  // Translation function
  const t = (key: string, params?: Record<string, string>): string => {
    const translation = getNestedValue(translations, key);
    return replaceParams(translation, params);
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    isLoading,
    translations
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useTranslation = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useTranslation must be used within a LanguageProvider');
  }
  return context;
};
