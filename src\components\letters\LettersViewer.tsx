'use client';

import React, { useState, useEffect } from 'react';
import { DocumentData, DocumentVisibility } from '@/lib/types/document';

interface LettersViewerProps {
  congregationId: string;
  userRole: string;
}

export default function LettersViewer({ congregationId, userRole }: LettersViewerProps) {
  const [letters, setLetters] = useState<DocumentData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLetter, setSelectedLetter] = useState<DocumentData | null>(null);

  // Load letters
  const loadLetters = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/documents?category=letters', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to load letters');
      }

      const data = await response.json();
      setLetters(data.documents || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load letters');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLetters();
  }, []);

  // Format date to Spanish
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    });
  };

  // Get category label in Spanish
  const getCategoryLabel = (category?: string) => {
    switch (category) {
      case 'letters': return 'General';
      case 'announcements': return 'Anuncios';
      case 'instructions': return 'Instrucciones';
      case 'forms': return 'Formularios';
      default: return category || 'General';
    }
  };

  // Get visibility label in Spanish
  const getVisibilityLabel = (visibility: DocumentVisibility) => {
    switch (visibility) {
      case 'ALL_MEMBERS': return 'Todos los Miembros';
      case 'ELDERS_ONLY': return 'Solo Ancianos';
      case 'MINISTERIAL_SERVANTS_PLUS': return 'Siervos Ministeriales+';
      default: return visibility;
    }
  };

  // Handle letter click
  const handleLetterClick = (letter: DocumentData) => {
    setSelectedLetter(letter);
  };

  // Close letter viewer
  const closeLetter = () => {
    setSelectedLetter(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-800">{error}</p>
        <button
          onClick={loadLetters}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Intentar de nuevo
        </button>
      </div>
    );
  }

  // If a letter is selected, show the PDF viewer
  if (selectedLetter) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex flex-col">
        {/* Header */}
        <div className="bg-red-600 text-white p-4 flex items-center justify-between">
          <button
            onClick={closeLetter}
            className="p-2 hover:bg-red-700 rounded-lg transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <h2 className="text-lg font-medium text-center flex-1 mx-4 truncate">
            {selectedLetter.title}
          </h2>
          <div className="w-10"></div> {/* Spacer for centering */}
        </div>

        {/* PDF Viewer */}
        <div className="flex-1 bg-white">
          <iframe
            src={selectedLetter.filePath}
            className="w-full h-full"
            title={selectedLetter.title}
          />
        </div>

        {/* Navigation */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex justify-center space-x-4">
            <button
              onClick={closeLetter}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Cerrar
            </button>
            <a
              href={selectedLetter.filePath}
              download={selectedLetter.filename}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Descargar
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 pb-20"> {/* Extra padding for bottom navigation */}
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Cartas de la Congregación</h2>
        <p className="text-gray-600">Accede a documentos importantes de la congregación</p>
      </div>

      {/* Letters Grid */}
      {letters.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay cartas disponibles</h3>
          <p className="text-gray-600">
            No se han subido cartas aún
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {letters.map((letter) => (
            <div
              key={letter.id}
              onClick={() => handleLetterClick(letter)}
              className="bg-white rounded-lg shadow border p-4 hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="flex items-center space-x-4">
                {/* PDF Icon */}
                <div className="flex-shrink-0">
                  <svg className="w-12 h-12 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                  </svg>
                </div>

                {/* Letter Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-medium text-gray-900 mb-1 line-clamp-2">
                    {letter.title}
                  </h3>
                  <p className="text-sm text-gray-500 mb-2">
                    {formatDate(letter.uploadDate)}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {getCategoryLabel(letter.category)}
                    </span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {getVisibilityLabel(letter.visibility)}
                    </span>
                  </div>
                </div>

                {/* Arrow Icon */}
                <div className="flex-shrink-0">
                  <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
