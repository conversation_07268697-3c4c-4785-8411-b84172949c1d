/**
 * Bulk Import Batch Status API Endpoint
 * 
 * Handles batch status retrieval, results, and retry operations.
 * Provides real-time progress tracking for bulk territory imports.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { BulkImportService } from '@/services/territories/BulkImportService';

interface RouteParams {
  params: {
    batchId: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for bulk import status' },
        { status: 403 }
      );
    }

    const { batchId } = params;

    // Validate batch ID
    if (!batchId || batchId.trim().length === 0) {
      return NextResponse.json(
        { error: 'Batch ID is required' },
        { status: 400 }
      );
    }

    // Initialize bulk import service
    BulkImportService.initialize();

    // Get batch progress
    const progress = BulkImportService.getBulkImportProgress(batchId);
    
    if (!progress) {
      return NextResponse.json(
        { error: 'Batch not found' },
        { status: 404 }
      );
    }

    // Get queue status for additional context
    const queueStatus = BulkImportService.getQueueStatus();

    return NextResponse.json({
      success: true,
      batchId,
      progress,
      queueStatus
    });

  } catch (error) {
    console.error('Bulk import status GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get batch status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for bulk import operations' },
        { status: 403 }
      );
    }

    const { batchId } = params;
    const body = await request.json();
    const action = body.action;

    // Validate batch ID
    if (!batchId || batchId.trim().length === 0) {
      return NextResponse.json(
        { error: 'Batch ID is required' },
        { status: 400 }
      );
    }

    // Initialize bulk import service
    BulkImportService.initialize();

    switch (action) {
      case 'retry':
        const retryResult = await BulkImportService.retryBulkImport(batchId);
        if (!retryResult) {
          return NextResponse.json(
            { error: 'Failed to retry batch or batch not found' },
            { status: 404 }
          );
        }

        console.log(`Bulk import batch ${batchId} retry initiated`);
        return NextResponse.json({
          success: true,
          message: 'Batch retry initiated',
          batchId
        });

      case 'cancel':
        const cancelResult = await BulkImportService.cancelBulkImport(batchId);
        if (!cancelResult) {
          return NextResponse.json(
            { error: 'Failed to cancel batch or batch not found' },
            { status: 404 }
          );
        }

        console.log(`Bulk import batch ${batchId} cancelled`);
        return NextResponse.json({
          success: true,
          message: 'Batch cancelled',
          batchId
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: retry, cancel' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Bulk import batch POST error:', error);

    return NextResponse.json(
      {
        error: 'Failed to perform batch operation',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET for status or POST for operations.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST with action=cancel to cancel batch.' },
    { status: 405 }
  );
}
