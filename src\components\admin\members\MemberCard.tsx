/**
 * Member Card Component
 *
 * Displays individual member information with role, status,
 * and management actions for congregation administrators.
 */

import React from 'react';
import { MemberProfile } from '@/lib/services/memberManagementService';

interface MemberCardProps {
  member: MemberProfile;
  onEdit: (member: MemberProfile) => void;
  onViewHistory: (memberId: string) => void;
  canManage: boolean;
}

const getRoleDisplayName = (role: string): string => {
  const roleNames = {
    'publisher': 'Publicador',
    'ministerial_servant': 'Siervo Ministerial',
    'elder': '<PERSON><PERSON><PERSON>',
    'coordinator': 'Coordinador',
  };
  return roleNames[role as keyof typeof roleNames] || role;
};

const getRoleColor = (role: string): string => {
  const roleColors = {
    'publisher': 'bg-blue-100 text-blue-800',
    'ministerial_servant': 'bg-green-100 text-green-800',
    'elder': 'bg-purple-100 text-purple-800',
    'overseer_coordinator': 'bg-orange-100 text-orange-800',
    'developer': 'bg-gray-100 text-gray-800',
  };
  return roleColors[role as keyof typeof roleColors] || 'bg-gray-100 text-gray-800';
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(date));
};

export default function MemberCard({
  member,
  onEdit,
  onViewHistory,
  canManage,
}: MemberCardProps) {
  return (
    <div className={`bg-white rounded-lg shadow-md border-2 p-6 transition-all duration-200 hover:shadow-lg ${
      member.isActive ? 'border-gray-200' : 'border-red-200 bg-red-50'
    }`}>
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className={`text-lg font-semibold mb-1 ${
            member.isActive ? 'text-gray-900' : 'text-red-800'
          }`}>
            {member.name}
          </h3>
          <p className={`text-sm ${
            member.isActive ? 'text-gray-600' : 'text-red-600'
          }`}>
            {member.email}
          </p>
        </div>

        {/* Status Indicator */}
        <div className="flex flex-col items-end space-y-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            member.isActive
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {member.isActive ? 'Activo' : 'Inactivo'}
          </span>
        </div>
      </div>

      {/* Role */}
      <div className="mb-4">
        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(member.role)}`}>
          {getRoleDisplayName(member.role)}
        </span>
      </div>

      {/* Member Details */}
      <div className="space-y-2 text-sm text-gray-600 mb-4">
        <div className="flex justify-between">
          <span>Miembro desde:</span>
          <span className="font-medium">{formatDate(member.createdAt)}</span>
        </div>
        {member.updatedAt.getTime() !== member.createdAt.getTime() && (
          <div className="flex justify-between">
            <span>Última actualización:</span>
            <span className="font-medium">{formatDate(member.updatedAt)}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>ID:</span>
          <span className="font-mono text-xs">{member.id.substring(0, 8)}...</span>
        </div>
      </div>

      {/* Actions */}
      {canManage && (
        <div className="flex space-x-2 pt-4 border-t border-gray-200">
          <button
            onClick={() => onEdit(member)}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Editar
          </button>
          <button
            onClick={() => onViewHistory(member.id)}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Historial
          </button>
        </div>
      )}

      {/* Read-only actions for non-managers */}
      {!canManage && (
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={() => onViewHistory(member.id)}
            className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Ver Historial
          </button>
        </div>
      )}
    </div>
  );
}
