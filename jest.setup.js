import '@testing-library/jest-dom'

// Mock environment variables for testing
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/hermanos_test'
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000'
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api'
process.env.JWT_SECRET = 'test-jwt-secret-key'
process.env.JWT_EXPIRES_IN = '60d'
process.env.NODE_ENV = 'test'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock fetch for API calls
global.fetch = jest.fn()

// Mock Web APIs for Next.js
Object.defineProperty(global, 'Request', {
  value: class MockRequest {
    constructor(url, options = {}) {
      this._url = url
      this.method = options.method || 'GET'
      this.headers = new Map(Object.entries(options.headers || {}))
    }

    get url() {
      return this._url
    }
  },
  writable: true
})

Object.defineProperty(global, 'Response', {
  value: class MockResponse {
    constructor(body, options = {}) {
      this.body = body
      this.status = options.status || 200
      this.headers = new Map(Object.entries(options.headers || {}))
    }

    json() {
      return Promise.resolve(JSON.parse(this.body))
    }

    static json(data, options = {}) {
      return new MockResponse(JSON.stringify(data), options)
    }
  },
  writable: true
})

// Setup and teardown for each test
beforeEach(() => {
  jest.clearAllMocks()
})

afterEach(() => {
  jest.restoreAllMocks()
})
