#!/usr/bin/env node

/**
 * Test script for enhanced document management system
 * This script tests all the enhanced document functionality
 */

const { PrismaClient } = require('@prisma/client');
const { EnhancedDocumentService } = require('../src/lib/services/enhancedDocumentService');

const prisma = new PrismaClient();

async function testEnhancedDocuments() {
  console.log('🧪 Starting enhanced document system tests...');

  try {
    // Get a test congregation
    const congregation = await prisma.congregation.findFirst();
    if (!congregation) {
      throw new Error('No congregation found for testing');
    }

    console.log(`📋 Testing with congregation: ${congregation.id}`);

    // Test 1: Create a test document
    console.log('\n1️⃣ Testing document creation...');
    
    const testDocument = {
      title: 'Test Enhanced Document',
      description: 'This is a test document for the enhanced document system',
      filename: 'test-document.pdf',
      filePath: '/uploads/documents/test-document.pdf',
      fileSize: 1024000, // 1MB
      mimeType: 'application/pdf',
      category: 'letters',
      subcategory: 'branch-letters',
      tags: ['test', 'enhanced', 'document'],
      visibility: 'ALL_MEMBERS',
      priority: 'HIGH',
      status: 'ACTIVE',
      congregationId: congregation.id,
      uploadedById: null, // We'll use null for testing
    };

    const createdDocument = await EnhancedDocumentService.createDocument(testDocument);
    console.log(`✅ Document created with ID: ${createdDocument.id}`);

    // Test 2: Get documents with filters
    console.log('\n2️⃣ Testing document retrieval with filters...');
    
    const allDocuments = await EnhancedDocumentService.getDocuments(
      congregation.id,
      {},
      10,
      0
    );
    console.log(`✅ Retrieved ${allDocuments.length} documents`);

    // Test with category filter
    const letterDocuments = await EnhancedDocumentService.getDocuments(
      congregation.id,
      { category: 'letters' },
      10,
      0
    );
    console.log(`✅ Retrieved ${letterDocuments.length} letter documents`);

    // Test with search
    const searchResults = await EnhancedDocumentService.getDocuments(
      congregation.id,
      { search: 'test' },
      10,
      0
    );
    console.log(`✅ Search returned ${searchResults.length} documents`);

    // Test with priority filter
    const highPriorityDocs = await EnhancedDocumentService.getDocuments(
      congregation.id,
      { priority: 'HIGH' },
      10,
      0
    );
    console.log(`✅ Retrieved ${highPriorityDocs.length} high priority documents`);

    // Test 3: Test role-based visibility
    console.log('\n3️⃣ Testing role-based document access...');
    
    // Create documents with different visibility levels
    const elderOnlyDoc = await EnhancedDocumentService.createDocument({
      ...testDocument,
      title: 'Elder Only Document',
      visibility: 'ELDERS_ONLY',
    });

    const msOnlyDoc = await EnhancedDocumentService.createDocument({
      ...testDocument,
      title: 'Ministerial Servant+ Document',
      visibility: 'MINISTERIAL_SERVANTS_PLUS',
    });

    // Test elder access (should see all)
    const elderDocs = await EnhancedDocumentService.getDocuments(
      congregation.id,
      {},
      10,
      0,
      'elder'
    );
    console.log(`✅ Elder can see ${elderDocs.length} documents`);

    // Test ministerial servant access (should not see elder-only)
    const msDocs = await EnhancedDocumentService.getDocuments(
      congregation.id,
      {},
      10,
      0,
      'ministerial_servant'
    );
    console.log(`✅ Ministerial Servant can see ${msDocs.length} documents`);

    // Test publisher access (should only see all-members)
    const publisherDocs = await EnhancedDocumentService.getDocuments(
      congregation.id,
      {},
      10,
      0,
      'publisher'
    );
    console.log(`✅ Publisher can see ${publisherDocs.length} documents`);

    // Test 4: Test document folders
    console.log('\n4️⃣ Testing document folders...');
    
    const folders = await prisma.documentFolder.findMany({
      where: { congregationId: congregation.id }
    });
    console.log(`✅ Found ${folders.length} document folders`);

    if (folders.length > 0) {
      // Test documents in folder
      const folderDocs = await EnhancedDocumentService.getDocuments(
        congregation.id,
        { folderId: folders[0].id },
        10,
        0
      );
      console.log(`✅ Folder "${folders[0].name}" contains ${folderDocs.length} documents`);
    }

    // Test 5: Test document statistics
    console.log('\n5️⃣ Testing document statistics...');
    
    const stats = await prisma.letter.groupBy({
      by: ['category'],
      where: { congregationId: congregation.id },
      _count: { id: true }
    });

    console.log('📊 Document statistics by category:');
    stats.forEach(stat => {
      console.log(`   • ${stat.category || 'Uncategorized'}: ${stat._count.id} documents`);
    });

    // Test 6: Test document search functionality
    console.log('\n6️⃣ Testing advanced search...');
    
    // Test tag search
    const tagResults = await EnhancedDocumentService.getDocuments(
      congregation.id,
      { tags: ['test'] },
      10,
      0
    );
    console.log(`✅ Tag search returned ${tagResults.length} documents`);

    // Test date range search
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    const recentDocs = await EnhancedDocumentService.getDocuments(
      congregation.id,
      { startDate: oneWeekAgo },
      10,
      0
    );
    console.log(`✅ Recent documents (last week): ${recentDocs.length}`);

    // Test 7: Test document validation
    console.log('\n7️⃣ Testing document validation...');
    
    try {
      await EnhancedDocumentService.createDocument({
        // Missing required fields
        congregationId: congregation.id,
      });
      console.log('❌ Validation should have failed');
    } catch (error) {
      console.log('✅ Document validation working correctly');
    }

    // Test 8: Performance test
    console.log('\n8️⃣ Testing performance...');
    
    const startTime = Date.now();
    const largeBatch = await EnhancedDocumentService.getDocuments(
      congregation.id,
      {},
      100,
      0
    );
    const endTime = Date.now();
    
    console.log(`✅ Retrieved ${largeBatch.length} documents in ${endTime - startTime}ms`);

    // Cleanup test documents
    console.log('\n🧹 Cleaning up test documents...');
    
    await prisma.letter.deleteMany({
      where: {
        congregationId: congregation.id,
        title: {
          contains: 'Test'
        }
      }
    });
    
    console.log('✅ Test documents cleaned up');

    console.log('\n🎉 All enhanced document tests passed!');

    // Print final summary
    const finalCount = await prisma.letter.count({
      where: { congregationId: congregation.id }
    });
    
    console.log('\n📊 Final Summary:');
    console.log(`   • Total documents in system: ${finalCount}`);
    console.log(`   • Total folders: ${folders.length}`);
    console.log(`   • Test congregation: ${congregation.id}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
if (require.main === module) {
  testEnhancedDocuments()
    .then(() => {
      console.log('✅ All tests completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Tests failed:', error);
      process.exit(1);
    });
}

module.exports = { testEnhancedDocuments };
