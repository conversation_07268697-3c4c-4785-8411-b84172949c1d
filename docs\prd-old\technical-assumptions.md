# Technical Assumptions

## Repository Structure: Next.js Fullstack

Single Next.js application with App Router handling both frontend UI and backend API routes, using Prisma ORM for database operations and shared TypeScript types throughout the stack.

## Service Architecture

Next.js fullstack architecture with API routes for backend logic, preserving existing JW.org integration services exactly as implemented. Multi-congregation tenancy through PostgreSQL database-level isolation using congregation_id foreign keys. Self-hosted deployment on standard VPS infrastructure.

## Testing Requirements

Unit testing for critical business logic, integration testing for API endpoints, and manual testing convenience methods for JW.org data fetching validation. Focus on testing the preserved meeting content synchronization logic and improved assignment algorithms.

## Additional Technical Assumptions and Requests

- **Database**: PostgreSQL 15 with Prisma ORM for type-safe operations and seamless migration from existing 41 MySQL tables
- **Frontend Framework**: Next.js 14+ with App Router for unified fullstack development and pixel-perfect UI preservation
- **Authentication**: Simple JWT-based authentication with 60-day mobile-friendly expiration (configurable/disableable by developers/elders), preserving existing congregation ID/PIN model
- **Multi-tenancy**: PostgreSQL database-level isolation using congregation_id foreign keys for complete data separation
- **JW.org Integration**: Exact preservation of existing URL patterns, caching mechanisms, and data fetching logic without modification
- **Styling**: Tailwind CSS for utility-first styling while preserving exact existing visual design and theme system
- **State Management**: Zustand for simple, lightweight state management without over-complication
- **File Storage**: Local filesystem storage preserving existing upload patterns and file management workflows
- **Development Tools**: TypeScript strict mode, ESLint/Prettier for code quality, maintaining development standards
- **Deployment**: Self-hosted VPS deployment with PM2 process management and Nginx reverse proxy
- **Security**: Simple bcrypt PIN hashing and basic rate limiting without over-complicating existing security model
- **Implementation Timeline**: 12-week phased approach with exact UI preservation and zero downtime migration
- **Testing Strategy**: Focus on functionality preservation and mobile performance validation
- **Package Management**: Always download latest stable versions of all packages and dependencies for security and performance
- **Environment Configuration**: All configuration variables in .env file - never hardcode localhost, ports, or URLs in codebase files
- **Single Source of Truth**: .env file contains all environment-specific configuration as the only source of truth
- **Development Scripts**: Standardized `npm run dev` script without additional parameters, using .env configuration
