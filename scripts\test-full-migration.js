#!/usr/bin/env node

/**
 * Full Migration Test Script for Hermanos App
 *
 * Tests the complete migration process including error handling,
 * rollback functionality, and data validation.
 */

const DatabaseMigrator = require('./migrate-mysql-to-postgresql');
const MigrationValidator = require('./validate-migration-enhanced');
const { PrismaClient } = require('@prisma/client');

class FullMigrationTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = {
      migrationTest: null,
      validationTest: null,
      rollbackTest: null,
      errors: [],
      warnings: [],
    };
  }

  async initialize() {
    try {
      await this.prisma.$connect();
      console.log('✅ Test environment initialized');
    } catch (error) {
      console.error('❌ Test initialization failed:', error.message);
      throw error;
    }
  }

  async testMigrationWithoutMySQL() {
    console.log('\n🧪 Testing migration functionality without MySQL...');

    try {
      // Create a mock migrator to test the structure
      const migrator = new DatabaseMigrator();

      // Test that all required methods exist
      const requiredMethods = [
        'initialize',
        'validateMySQLData',
        'createBackupPoint',
        'rollbackMigration',
        'validateRecord',
        'migrateCongregations',
        'migrateCongregationSettings',
        'migrateRoles',
        'migrateMembers',
        'migrateElderPermissions',
        'migrateServiceGroups',
        'migrateTerritories',
        'migrateTasks',
        'migrateTaskAssignments',
        'migrateFieldServiceRecords',
        'migrateLetters',
        'migrateMeetings',
        'migrateSongs',
        'executeFullMigration',
        'validateMigrationResults',
        'printMigrationSummary',
        'cleanup'
      ];

      for (const method of requiredMethods) {
        if (typeof migrator[method] !== 'function') {
          throw new Error(`Missing required method: ${method}`);
        }
      }

      console.log('✅ All required migration methods are present');

      // Test validation functionality
      const isValid = await migrator.validateRecord('test', {
        id: 'test-id',
        name: 'Test Name',
        email: '<EMAIL>',
        congregationId: 'TEST123'
      }, ['id', 'name']);

      if (!isValid) {
        throw new Error('Validation test failed');
      }

      console.log('✅ Validation functionality works correctly');

      this.testResults.migrationTest = 'PASSED';

    } catch (error) {
      console.error('❌ Migration test failed:', error.message);
      this.testResults.migrationTest = 'FAILED';
      this.testResults.errors.push(`Migration test: ${error.message}`);
    }
  }

  async testValidationScript() {
    console.log('\n🔍 Testing validation script...');

    try {
      const validator = new MigrationValidator();

      // Test that all required methods exist
      const requiredMethods = [
        'initialize',
        'validateTableCounts',
        'validateDataIntegrity',
        'validateBusinessLogic',
        'generateValidationReport',
        'cleanup',
        'runFullValidation'
      ];

      for (const method of requiredMethods) {
        if (typeof validator[method] !== 'function') {
          throw new Error(`Missing required validation method: ${method}`);
        }
      }

      console.log('✅ All required validation methods are present');

      this.testResults.validationTest = 'PASSED';

    } catch (error) {
      console.error('❌ Validation test failed:', error.message);
      this.testResults.validationTest = 'FAILED';
      this.testResults.errors.push(`Validation test: ${error.message}`);
    }
  }

  async testDatabaseSchema() {
    console.log('\n🗄️ Testing database schema...');

    try {
      // Test that all required models are available
      const requiredModels = [
        'congregation',
        'congregationSetting',
        'role',
        'member',
        'elderPermission',
        'serviceGroup',
        'territory',
        'task',
        'taskAssignment',
        'fieldServiceRecord',
        'letter',
        'midweekMeeting',
        'song',
        'specialSong'
      ];

      for (const model of requiredModels) {
        if (!this.prisma[model]) {
          throw new Error(`Missing Prisma model: ${model}`);
        }
      }

      console.log('✅ All required Prisma models are available');

      // Test basic CRUD operations
      const testCongregation = await this.prisma.congregation.create({
        data: {
          id: 'TEST001',
          name: 'Schema Test Congregation',
          region: 'Test',
          pin: '$2b$12$testpin',
          language: 'es',
          timezone: 'UTC',
        },
      });

      const foundCongregation = await this.prisma.congregation.findUnique({
        where: { id: 'TEST001' },
      });

      if (!foundCongregation) {
        throw new Error('Failed to create and retrieve test congregation');
      }

      await this.prisma.congregation.delete({
        where: { id: 'TEST001' },
      });

      console.log('✅ Basic CRUD operations work correctly');

    } catch (error) {
      console.error('❌ Schema test failed:', error.message);
      this.testResults.errors.push(`Schema test: ${error.message}`);
    }
  }

  async testRelationships() {
    console.log('\n🔗 Testing database relationships...');

    try {
      // Create test data with relationships
      const congregation = await this.prisma.congregation.create({
        data: {
          id: 'TEST002',
          name: 'Relationship Test Congregation',
          region: 'Test',
          pin: '$2b$12$testpin',
          language: 'es',
          timezone: 'UTC',
        },
      });

      const member = await this.prisma.member.create({
        data: {
          congregationId: congregation.id,
          name: 'Test Member',
          email: '<EMAIL>',
          role: 'publisher',
          pin: '$2b$12$testpin',
        },
      });

      const task = await this.prisma.task.create({
        data: {
          congregationId: congregation.id,
          title: 'Test Task',
          description: 'Test task description',
          category: 'test',
          frequency: 'weekly',
        },
      });

      // Test relationships
      const congregationWithRelations = await this.prisma.congregation.findUnique({
        where: { id: congregation.id },
        include: {
          members: true,
          tasks: true,
        },
      });

      if (congregationWithRelations.members.length !== 1) {
        throw new Error('Congregation-member relationship failed');
      }

      if (congregationWithRelations.tasks.length !== 1) {
        throw new Error('Congregation-task relationship failed');
      }

      // Cleanup
      await this.prisma.member.delete({ where: { id: member.id } });
      await this.prisma.task.delete({ where: { id: task.id } });
      await this.prisma.congregation.delete({ where: { id: congregation.id } });

      console.log('✅ Database relationships work correctly');

    } catch (error) {
      console.error('❌ Relationship test failed:', error.message);
      this.testResults.errors.push(`Relationship test: ${error.message}`);
    }
  }

  async generateTestReport() {
    console.log('\n📋 Generating test report...');

    const totalTests = 4;
    const passedTests = Object.values(this.testResults)
      .filter(result => result === 'PASSED').length;
    const failedTests = this.testResults.errors.length;

    console.log('\n' + '='.repeat(60));
    console.log('📊 MIGRATION TEST REPORT');
    console.log('='.repeat(60));
    console.log(`✅ Tests passed: ${passedTests}/${totalTests}`);
    console.log(`❌ Errors: ${failedTests}`);
    console.log(`⚠️  Warnings: ${this.testResults.warnings.length}`);

    if (this.testResults.errors.length > 0) {
      console.log('\n🚨 ERRORS:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (this.testResults.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.testResults.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning}`);
      });
    }

    const allTestsPassed = this.testResults.errors.length === 0;
    console.log('\n' + (allTestsPassed ? '✅ All tests PASSED!' : '❌ Some tests FAILED!'));
    console.log('='.repeat(60));

    return allTestsPassed;
  }

  async runFullTest() {
    console.log('🧪 Starting full migration test suite...\n');

    try {
      await this.initialize();
      await this.testMigrationWithoutMySQL();
      await this.testValidationScript();
      await this.testDatabaseSchema();
      await this.testRelationships();

      const allTestsPassed = await this.generateTestReport();

      if (!allTestsPassed) {
        process.exit(1);
      }

      console.log('\n✅ Full migration test suite completed successfully!');

    } catch (error) {
      console.error('\n❌ Test suite failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Main execution
async function main() {
  const tester = new FullMigrationTester();
  await tester.runFullTest();
}

// Run test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = FullMigrationTester;
