/**
 * Test Script: Field Service Schedules
 * 
 * Tests the field service schedules functionality to ensure:
 * 1. Data was migrated successfully from hardcoded to database
 * 2. API endpoints are working correctly
 * 3. Frontend can fetch and display the data
 */

// Use dynamic import for node-fetch
let fetch;

const BASE_URL = 'http://localhost:3000';
const CONGREGATION_ID = '1441';
const CONGREGATION_PIN = '1930';

async function authenticateAsAdmin() {
  console.log('🔐 Authenticating...');
  
  if (!fetch) {
    const fetchModule = await import('node-fetch');
    fetch = fetchModule.default;
  }
  
  const response = await fetch(`${BASE_URL}/api/auth/congregation-login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      congregationId: CONGREGATION_ID,
      pin: CONGREGATION_PIN
    }),
  });

  if (!response.ok) {
    throw new Error(`Authentication failed: ${response.status}`);
  }

  const data = await response.json();
  console.log('✅ Authentication successful');
  return data.token;
}

async function testAdminServiceSchedules(token) {
  console.log('\n📋 Testing Admin Service Schedules API...');
  
  const response = await fetch(`${BASE_URL}/api/admin/service-schedules`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Admin API failed: ${response.status}`);
  }

  const data = await response.json();
  console.log(`✅ Admin API working - Found ${data.serviceTimes?.length || 0} service times`);
  
  if (data.serviceTimes && data.serviceTimes.length > 0) {
    console.log('📅 Sample service times:');
    data.serviceTimes.slice(0, 3).forEach(time => {
      console.log(`   - ${time.serviceDate} ${time.serviceTime} at ${time.location}`);
    });
  }
  
  return data;
}

async function testMemberServiceSchedules(token) {
  console.log('\n👥 Testing Member Service Schedule API...');
  
  const response = await fetch(`${BASE_URL}/api/service-schedule`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Member API failed: ${response.status}`);
  }

  const data = await response.json();
  console.log(`✅ Member API working - Found schedule with ${data.schedule?.scheduleTimes?.length || 0} times`);
  
  if (data.schedule?.scheduleTimes && data.schedule.scheduleTimes.length > 0) {
    console.log('📅 Sample schedule times:');
    data.schedule.scheduleTimes.slice(0, 3).forEach(time => {
      console.log(`   - ${time.serviceDate} ${time.serviceTime} at ${time.location}`);
      if (time.conductor) {
        console.log(`     Conductor: ${time.conductor.name}`);
      }
    });
  }
  
  return data;
}

async function testFieldServicePage() {
  console.log('\n🌐 Testing Field Service Page...');
  
  const response = await fetch(`${BASE_URL}/field-service`);
  
  if (response.ok) {
    console.log('✅ Field Service page loads successfully');
  } else {
    console.log(`⚠️  Field Service page returned status: ${response.status}`);
  }
}

async function runTests() {
  try {
    console.log('🚀 Starting Field Service Schedule Tests...\n');

    // Step 1: Authenticate
    const token = await authenticateAsAdmin();

    // Step 2: Test Admin API
    const adminData = await testAdminServiceSchedules(token);

    // Step 3: Test Member API
    const memberData = await testMemberServiceSchedules(token);

    // Step 4: Test Frontend Page
    await testFieldServicePage();

    // Step 5: Summary
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   - Admin API: ${adminData.serviceTimes?.length || 0} service times`);
    console.log(`   - Member API: ${memberData.schedule?.scheduleTimes?.length || 0} schedule times`);
    console.log('   - Frontend: Page loads correctly');
    
    console.log('\n✨ Migration from hardcoded to database was successful!');
    console.log('💡 You can now:');
    console.log('   1. View schedules in the Field Service page');
    console.log('   2. Toggle between upcoming and historical schedules');
    console.log('   3. Expand/collapse date sections');
    console.log('   4. Edit upcoming schedules (admin only)');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run tests
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
