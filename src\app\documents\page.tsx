import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { auth } from '@/lib/auth';
import DocumentViewer from '@/components/documents/DocumentViewer';

export const metadata: Metadata = {
  title: 'Documents - Congregation Portal',
  description: 'Access congregation documents and letters',
};

export default async function DocumentsPage() {
  // Get user from auth
  const user = await auth.getUser();
  
  if (!user) {
    redirect('/login');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
                <p className="text-gray-600 mt-1">
                  Access important congregation documents and letters
                </p>
              </div>
              <div className="text-right">
                <p className="text-gray-500 text-sm">Welcome back</p>
                <p className="font-medium text-gray-900">{user.name}</p>
                <p className="text-gray-500 text-sm capitalize">{user.role.replace('_', ' ')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <DocumentViewer 
          congregationId={user.congregationId}
          userRole={user.role}
        />
      </div>

      {/* Navigation */}
      <div className="fixed bottom-4 left-4">
        <a
          href="/dashboard"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
        >
          ← Back to Dashboard
        </a>
      </div>

      {/* Admin Link */}
      {['elder', 'ministerial_servant'].includes(user.role) && (
        <div className="fixed bottom-4 right-4">
          <a
            href="/admin/documents"
            className="bg-gray-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-gray-700 transition-colors"
          >
            Manage Documents
          </a>
        </div>
      )}
    </div>
  );
}
