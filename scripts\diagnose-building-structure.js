/**
 * Diagnose Building Structure in Territories 7, 9, 10
 * 
 * Examines Excel sheets to identify multiple buildings and distinguish buildings vs houses
 */

const XLSX = require('xlsx');
const path = require('path');

async function diagnoseBuildingStructure(territoryNumber) {
  try {
    console.log(`\n🔍 Detailed Building Analysis - Territory ${territoryNumber}...`);
    
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${territoryNumber.padStart(3, '0')}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    let sheetName = '';
    if (territoryNumber === '007') sheetName = 'Terr 7';
    else if (territoryNumber === '009') sheetName = 'Terr 9';
    else if (territoryNumber === '010') sheetName = 'Terr 10';
    
    const worksheet = workbook.Sheets[sheetName];
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    console.log(`📊 Total rows: ${excelData.length}`);
    
    // Show ALL rows to understand structure
    console.log(`\n📋 Complete Excel Structure for Territory ${territoryNumber}:`);
    for (let i = 0; i < Math.min(100, excelData.length); i++) {
      const row = excelData[i];
      if (row && row.length > 0) {
        // Show all columns to understand structure
        const rowData = [];
        for (let col = 0; col < Math.min(10, row.length); col++) {
          const cell = row[col];
          if (cell !== undefined && cell !== null && cell !== '') {
            rowData.push(`Col${String.fromCharCode(65 + col)}:${cell}`);
          }
        }
        if (rowData.length > 0) {
          console.log(`Row ${i + 1}: ${rowData.join(' | ')}`);
        }
      }
    }
    
    // Look for building indicators
    console.log(`\n🏢 Building Indicators Analysis for Territory ${territoryNumber}:`);
    let buildingIndicators = [];
    
    for (let i = 0; i < excelData.length; i++) {
      const row = excelData[i];
      if (row && row.length > 0) {
        for (let col = 0; col < row.length; col++) {
          const cell = row[col];
          if (cell && typeof cell === 'string') {
            const cellValue = cell.toString().toLowerCase();
            if (cellValue.includes('edif') || cellValue.includes('edificio') || 
                cellValue.includes('building') || cellValue.includes('flagler') ||
                cellValue.includes('hoja')) {
              buildingIndicators.push({
                row: i + 1,
                col: String.fromCharCode(65 + col),
                value: cell
              });
            }
          }
        }
      }
    }
    
    console.log(`Found ${buildingIndicators.length} building indicators:`);
    buildingIndicators.forEach(indicator => {
      console.log(`   Row ${indicator.row}, Col ${indicator.col}: "${indicator.value}"`);
    });
    
    // Look for address patterns
    console.log(`\n📍 Address Pattern Analysis for Territory ${territoryNumber}:`);
    let addressPatterns = [];
    
    for (let i = 7; i < Math.min(50, excelData.length); i++) {
      const row = excelData[i];
      if (row && row[1]) {
        const cellB = row[1];
        if (cellB) {
          const cellValue = cellB.toString().trim();
          if (cellValue && cellValue.length > 0) {
            let pattern = 'unknown';
            if (/^\d{4}$/.test(cellValue)) pattern = 'building_number';
            else if (/^\d+[a-z]?$/.test(cellValue)) pattern = 'apartment_or_house';
            else if (cellValue.includes('FLAGLER')) pattern = 'street_address';
            else if (cellValue.includes('AVE') || cellValue.includes('ST') || cellValue.includes('CT')) pattern = 'street_name';
            else if (/^[A-Z]/.test(cellValue)) pattern = 'text_header';
            
            addressPatterns.push({
              row: i + 1,
              value: cellValue,
              pattern: pattern,
              notes: row[6] ? row[6].toString().trim() : ''
            });
          }
        }
      }
    }
    
    console.log(`Address patterns found:`);
    addressPatterns.slice(0, 30).forEach(pattern => {
      console.log(`   Row ${pattern.row}: "${pattern.value}" (${pattern.pattern})${pattern.notes ? ` - Notes: "${pattern.notes}"` : ''}`);
    });
    
  } catch (error) {
    console.error(`❌ Error diagnosing Territory ${territoryNumber}:`, error.message);
  }
}

async function diagnoseAllBuildingStructures() {
  console.log('🔍 Starting detailed building structure analysis...');
  
  await diagnoseBuildingStructure('007');
  await diagnoseBuildingStructure('009');
  await diagnoseBuildingStructure('010');
  
  console.log('\n✅ Building structure analysis completed!');
}

diagnoseAllBuildingStructures();
