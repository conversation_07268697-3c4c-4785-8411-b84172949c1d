const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

async function debugTerritory025() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  const fileName = 'Terr. 025.xlsx';
  const filePath = path.join(territoriosDir, fileName);
  
  console.log(`🔍 Debugging Territory 025 structure...\n`);
  
  const workbook = XLSX.readFile(filePath);
  const sheet = workbook.Sheets['Terr 25'];
  const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
  
  console.log(`Total rows: ${data.length}\n`);
  
  // Show rows 8-30 where the actual data should be
  for (let i = 7; i < Math.min(data.length, 30); i++) {
    const row = data[i];
    if (row && row.length > 0) {
      console.log(`Row ${i + 1}:`);
      for (let j = 0; j < Math.min(row.length, 8); j++) {
        const cell = row[j];
        if (cell !== undefined && cell !== null && cell !== '') {
          console.log(`  Col ${j + 1}: "${cell}" (type: ${typeof cell})`);
        }
      }
      
      // Test our patterns
      const firstCell = row[0] ? row[0].toString().trim() : '';
      const secondCell = row[1] ? row[1].toString().trim() : '';
      
      console.log(`  First cell: "${firstCell}"`);
      console.log(`  Second cell: "${secondCell}"`);
      
      // Test street pattern
      if (firstCell && /\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(firstCell)) {
        console.log(`  🛣️  STREET DETECTED: ${firstCell}`);
      }
      
      // Test house number patterns
      if (!row[0] && row[1] && /^\d+[A-Z]?$/.test(secondCell)) {
        console.log(`  🏠 HOUSE NUMBER (col 2): ${secondCell}`);
      }
      if (row[0] && /^\d+[A-Z]?$/.test(firstCell)) {
        console.log(`  🏠 HOUSE NUMBER (col 1): ${firstCell}`);
      }
      
      console.log('');
    }
  }
}

debugTerritory025().catch(console.error);
