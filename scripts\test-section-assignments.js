#!/usr/bin/env node

/**
 * Section Assignments Test Script for Hermanos App
 *
 * Tests the administrative delegation system including section assignments,
 * transfers, history tracking, and permission validation.
 *
 * Usage: node scripts/test-section-assignments.js
 */

const { PrismaClient } = require('@prisma/client');

class SectionAssignmentTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = [];
  }

  addTestResult(testName, success, message) {
    this.testResults.push({
      test: testName,
      success,
      message,
    });

    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  async testDatabaseSchema() {
    console.log('\n🗄️ Testing Database Schema...');

    try {
      // Test section_assignments table
      const assignmentCount = await this.prisma.sectionAssignment.count();
      this.addTestResult('Section Assignments Table', true, `Table exists with ${assignmentCount} records`);

      // Test assignment_history table
      const historyCount = await this.prisma.assignmentHistory.count();
      this.addTestResult('Assignment History Table', true, `Table exists with ${historyCount} records`);

      // Test elder_permissions table
      const permissionCount = await this.prisma.elderPermission.count();
      this.addTestResult('Elder Permissions Table', true, `Table exists with ${permissionCount} records`);

      return true;
    } catch (error) {
      this.addTestResult('Database Schema', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testEligibleMembers() {
    console.log('\n👥 Testing Eligible Members...');

    try {
      // Get congregation
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      if (!congregation) {
        this.addTestResult('Congregation Lookup', false, 'No active congregation found');
        return false;
      }

      // Get eligible members (elders and ministerial servants)
      const eligibleMembers = await this.prisma.member.findMany({
        where: {
          congregationId: congregation.id,
          isActive: true,
          role: {
            in: ['elder', 'ministerial_servant'],
          },
        },
      });

      this.addTestResult('Eligible Members', eligibleMembers.length > 0,
        `Found ${eligibleMembers.length} eligible members`);

      // Test role distribution
      const elders = eligibleMembers.filter(m => m.role === 'elder');
      const ministerialServants = eligibleMembers.filter(m => m.role === 'ministerial_servant');

      this.addTestResult('Elder Count', elders.length > 0, `Found ${elders.length} elders`);
      this.addTestResult('Ministerial Servant Count', ministerialServants.length > 0,
        `Found ${ministerialServants.length} ministerial servants`);

      return eligibleMembers.length > 0;
    } catch (error) {
      this.addTestResult('Eligible Members', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testSectionAssignmentCreation() {
    console.log('\n📋 Testing Section Assignment Creation...');

    try {
      // Get test data
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      const coordinator = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          role: 'overseer_coordinator',
          isActive: true,
        },
      });

      const elder = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          role: 'elder',
          isActive: true,
        },
      });

      if (!coordinator || !elder) {
        this.addTestResult('Test Data Setup', false, 'Missing coordinator or elder for testing');
        return false;
      }

      // Create a test assignment (or use existing one)
      let assignment = await this.prisma.sectionAssignment.findFirst({
        where: {
          congregationId: congregation.id,
          memberId: elder.id,
          sectionType: 'field_service',
        },
      });

      if (!assignment) {
        assignment = await this.prisma.sectionAssignment.create({
          data: {
            congregationId: congregation.id,
            memberId: elder.id,
            sectionType: 'field_service',
            scopeDefinition: {
              description: 'Responsabilidad completa para Servicio del Campo',
              limitations: [],
              specificAreas: ['Territorios', 'Grupos de servicio'],
              notes: 'Asignación de prueba',
            },
            assignedBy: coordinator.id,
          },
        });
      }

      this.addTestResult('Assignment Creation', !!assignment.id,
        `Created assignment ${assignment.id} for ${elder.name}`);

      // Create history record
      const historyRecord = await this.prisma.assignmentHistory.create({
        data: {
          congregationId: congregation.id,
          memberId: elder.id,
          sectionType: 'field_service',
          action: 'assigned',
          assignedBy: coordinator.id,
          reason: 'Test assignment creation',
        },
      });

      this.addTestResult('History Record Creation', !!historyRecord.id,
        'Created assignment history record');

      return assignment.id;
    } catch (error) {
      this.addTestResult('Section Assignment Creation', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testAssignmentQueries() {
    console.log('\n🔍 Testing Assignment Queries...');

    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Test getting all assignments
      const allAssignments = await this.prisma.sectionAssignment.findMany({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
        include: {
          member: {
            select: {
              name: true,
              role: true,
            },
          },
          assignedByMember: {
            select: {
              name: true,
            },
          },
        },
      });

      this.addTestResult('Assignment Query', true,
        `Retrieved ${allAssignments.length} active assignments`);

      // Test getting assignments by section
      const fieldServiceAssignments = await this.prisma.sectionAssignment.findMany({
        where: {
          congregationId: congregation.id,
          sectionType: 'field_service',
          isActive: true,
        },
      });

      this.addTestResult('Section-Specific Query', true,
        `Found ${fieldServiceAssignments.length} field service assignments`);

      // Test assignment history query
      const history = await this.prisma.assignmentHistory.findMany({
        where: {
          congregationId: congregation.id,
        },
        include: {
          member: {
            select: {
              name: true,
            },
          },
          assignedByMember: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10,
      });

      this.addTestResult('History Query', true,
        `Retrieved ${history.length} history records`);

      return true;
    } catch (error) {
      this.addTestResult('Assignment Queries', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testAssignmentConstraints() {
    console.log('\n🔒 Testing Assignment Constraints...');

    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      const elder = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          role: 'elder',
          isActive: true,
        },
      });

      // Test unique constraint (member + section)
      try {
        await this.prisma.sectionAssignment.create({
          data: {
            congregationId: congregation.id,
            memberId: elder.id,
            sectionType: 'field_service', // Same section as previous test
            scopeDefinition: {},
          },
        });
        this.addTestResult('Unique Constraint', false, 'Should have failed due to unique constraint');
      } catch (error) {
        this.addTestResult('Unique Constraint', true, 'Correctly prevented duplicate assignment');
      }

      // Test congregation isolation
      const assignments = await this.prisma.sectionAssignment.findMany({
        where: {
          congregationId: congregation.id,
        },
      });

      const allAssignments = await this.prisma.sectionAssignment.findMany();

      const isolationWorking = assignments.length <= allAssignments.length;
      this.addTestResult('Congregation Isolation', isolationWorking,
        isolationWorking ? 'Congregation isolation working' : 'Potential isolation issue');

      return true;
    } catch (error) {
      this.addTestResult('Assignment Constraints', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testAssignmentRemoval() {
    console.log('\n🗑️ Testing Assignment Removal...');

    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Find an active assignment to remove, or create one for testing
      let assignment = await this.prisma.sectionAssignment.findFirst({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
      });

      if (!assignment) {
        // Create a test assignment for removal testing
        const elder = await this.prisma.member.findFirst({
          where: {
            congregationId: congregation.id,
            role: 'elder',
            isActive: true,
          },
        });

        if (elder) {
          assignment = await this.prisma.sectionAssignment.create({
            data: {
              congregationId: congregation.id,
              memberId: elder.id,
              sectionType: 'meetings',
              scopeDefinition: {
                description: 'Test assignment for removal',
              },
            },
          });
        }
      }

      if (!assignment) {
        this.addTestResult('Assignment Removal Setup', false, 'Could not create assignment for removal test');
        return false;
      }

      // Deactivate the assignment
      await this.prisma.sectionAssignment.update({
        where: { id: assignment.id },
        data: { isActive: false },
      });

      // Create removal history record
      await this.prisma.assignmentHistory.create({
        data: {
          congregationId: congregation.id,
          memberId: assignment.memberId,
          sectionType: assignment.sectionType,
          action: 'removed',
          reason: 'Test assignment removal',
        },
      });

      this.addTestResult('Assignment Removal', true, 'Successfully removed assignment');

      // Verify assignment is inactive
      const removedAssignment = await this.prisma.sectionAssignment.findUnique({
        where: { id: assignment.id },
      });

      this.addTestResult('Removal Verification', !removedAssignment.isActive,
        'Assignment correctly marked as inactive');

      return true;
    } catch (error) {
      this.addTestResult('Assignment Removal', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testPerformance() {
    console.log('\n⚡ Testing Assignment Performance...');

    try {
      const startTime = Date.now();

      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Run complex query with joins
      await this.prisma.sectionAssignment.findMany({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
        include: {
          member: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
          assignedByMember: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: [
          { sectionType: 'asc' },
          { assignedAt: 'desc' },
        ],
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      const isPerformant = duration < 500; // Should complete in under 500ms
      this.addTestResult('Assignment Query Performance', isPerformant,
        `Complex query completed in ${duration}ms ${isPerformant ? '(good)' : '(slow)'}`);

      return isPerformant;
    } catch (error) {
      this.addTestResult('Assignment Performance', false, `Error: ${error.message}`);
      return false;
    }
  }

  generateReport() {
    console.log('\n📋 Section Assignment Test Report');
    console.log('==================================');

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.test}: ${r.message}`));
    }

    const allPassed = failedTests === 0;
    if (allPassed) {
      console.log('\n🎉 All section assignment tests passed!');
      console.log('\n📝 Administrative delegation system is ready:');
      console.log('1. Start the development server: npm run dev');
      console.log('2. Login as a coordinator elder');
      console.log('3. Go to Admin → Assignments');
      console.log('4. Create and manage section assignments');
    } else {
      console.log('\n⚠️ Some section assignment tests failed!');
    }

    return allPassed;
  }

  async run() {
    try {
      console.log('🚀 Starting section assignment tests...');

      await this.prisma.$connect();
      console.log('✅ Database connection established');

      // Run all tests
      await this.testDatabaseSchema();
      await this.testEligibleMembers();
      await this.testSectionAssignmentCreation();
      await this.testAssignmentQueries();
      await this.testAssignmentConstraints();
      await this.testAssignmentRemoval();
      await this.testPerformance();

      // Generate report
      const success = this.generateReport();

      if (!success) {
        process.exit(1);
      }

    } catch (error) {
      console.error('\n💥 Test execution failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new SectionAssignmentTester();
  tester.run().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = SectionAssignmentTester;
