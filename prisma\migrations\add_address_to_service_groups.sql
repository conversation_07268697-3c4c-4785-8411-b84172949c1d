-- Add address field to service_groups table
ALTER TABLE service_groups ADD COLUMN address TEXT;

-- Insert sample service groups for Coral Oeste congregation (ID: 1441)
INSERT INTO service_groups (id, congregation_id, name, group_number, overseer_id, assistant_id, address, is_active, created_at, updated_at)
VALUES 
  ('sg1', '1441', 'Grupo 1', 1, NULL, NULL, '123 Main St, Miami, FL', true, NOW(), NOW()),
  ('sg2', '1441', 'Grupo 2', 2, NULL, NULL, '456 Oak Ave, Miami, FL', true, NOW(), NOW()),
  ('sg3', '1441', 'Grupo 3', 3, NULL, NULL, '789 Pine Rd, Miami, FL', true, NOW(), NOW()),
  ('sg4', '1441', 'Grupo 4', 4, NULL, NULL, '321 Elm St, Miami, FL', true, NOW(), NOW()),
  ('sg5', '1441', 'Grupo 5', 5, NULL, NULL, '654 Maple Dr, Miami, FL', true, NOW(), NOW()),
  ('sg6', '1441', 'Grupo 6', 6, NULL, NULL, '987 Cedar Ln, Miami, FL', true, NOW(), NOW()),
  ('sg7', '1441', 'Grupo 7', 7, NULL, NULL, '147 Birch Way, Miami, FL', true, NOW(), NOW())
ON CONFLICT (congregation_id, group_number) DO NOTHING;
