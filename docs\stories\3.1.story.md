# Story 3.1: Midweek Meeting Management

**Epic:** Epic 3: Meeting Management & JW.org Integration
**Story Points:** 13
**Priority:** High
**Status:** Draft

## Story

As a meeting coordinator,
I want to manage midweek meetings with JW.org integration and assignment tracking,
so that I can efficiently coordinate meeting parts and ensure proper preparation and participation.

## Acceptance Criteria

1. **JW.org workbook integration with automatic meeting data fetching and parsing**
   - Automated JW.org workbook data fetching with reliable parsing and content extraction
   - Meeting structure recognition with part identification and assignment categorization
   - Content synchronization with weekly updates and change detection
   - Fallback mechanisms with offline capability and manual override options

2. **Meeting part assignment with member coordination and conflict detection**
   - Comprehensive assignment interface with member selection and availability checking
   - Conflict detection with scheduling validation and alternative suggestions
   - Assignment history tracking with member development and rotation management
   - Bulk assignment capabilities with template-based scheduling and optimization

3. **Assignment tracking with preparation monitoring and completion validation**
   - Assignment preparation tracking with deadline monitoring and reminder systems
   - Completion validation with quality assessment and feedback collection
   - Progress monitoring with milestone tracking and development analytics
   - Performance reporting with member development and improvement recommendations

4. **Meeting schedule management with location coordination and format flexibility**
   - Meeting schedule configuration with recurring patterns and exception handling
   - Location management with Kingdom Hall and virtual meeting coordination
   - Format flexibility with hybrid meeting support and technical requirements
   - Schedule conflict resolution with automatic detection and alternative suggestions

5. **Member availability tracking with preference management and blackout dates**
   - Availability calendar with member preference management and scheduling constraints
   - Blackout date management with vacation and unavailability tracking
   - Preference-based assignment with member skill matching and development goals
   - Availability analytics with participation patterns and optimization recommendations

6. **Meeting preparation resources with study materials and reference integration**
   - Study material integration with JW.org resources and reference linking
   - Preparation guidelines with part-specific instructions and best practices
   - Resource sharing with document distribution and collaborative preparation
   - Preparation tracking with time management and effectiveness monitoring

7. **Meeting attendance tracking with participation monitoring and analytics**
   - Attendance tracking with digital check-in and participation monitoring
   - Participation analytics with engagement metrics and trend analysis
   - Attendance reporting with congregation statistics and improvement insights
   - Member engagement tracking with participation quality and development progress

## Dev Notes

### Technical Architecture

**JW.org Integration:**
- Automated workbook fetching with reliable parsing and content extraction
- Content synchronization with change detection and update management
- Fallback mechanisms with offline capability and manual data entry
- API integration with rate limiting and respectful data access

**Meeting Management:**
- Comprehensive meeting scheduling with recurring patterns and exceptions
- Assignment coordination with conflict detection and resolution
- Preparation tracking with deadline monitoring and reminder systems
- Attendance management with digital check-in and analytics

### API Endpoints (tRPC)

```typescript
// Midweek meeting management routes
midweekMeetings: router({
  fetchWorkbookData: adminProcedure
    .input(z.object({
      weekDate: z.date(),
      language: z.string().default('es'),
      forceRefresh: z.boolean().default(false)
    }))
    .mutation(async ({ input, ctx }) => {
      return await jwOrgService.fetchWorkbookData(
        input.weekDate,
        input.language,
        ctx.user.congregationId,
        input.forceRefresh
      );
    }),

  createMeeting: adminProcedure
    .input(z.object({
      date: z.date(),
      workbookData: z.object({
        weeklyBibleReading: z.string(),
        treasuresFromGodsWord: z.string(),
        diggingForGems: z.string(),
        bibleReading: z.string(),
        initialCall: z.string(),
        returnVisit: z.string(),
        bibleStudy: z.string(),
        livingAsChristians: z.string(),
        congregationBibleStudy: z.string()
      }),
      chairman: z.string().optional(),
      location: z.enum(['kingdom_hall', 'zoom', 'hybrid']),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await meetingService.createMidweekMeeting(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    }),

  assignMeetingPart: adminProcedure
    .input(z.object({
      meetingId: z.string(),
      partType: z.enum(['chairman', 'prayer', 'bible_reading', 'initial_call', 'return_visit', 'bible_study', 'talk']),
      memberId: z.string(),
      assistantId: z.string().optional(),
      preparationDeadline: z.date(),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      // Check for assignment conflicts
      const conflicts = await assignmentService.checkConflicts(
        input.memberId,
        input.meetingId,
        ctx.user.congregationId
      );

      if (conflicts.length > 0) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Assignment conflicts detected',
          cause: conflicts
        });
      }

      return await assignmentService.assignMeetingPart(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    }),

  getMeetingSchedule: protectedProcedure
    .input(z.object({
      startDate: z.date(),
      endDate: z.date(),
      includeAssignments: z.boolean().default(true)
    }))
    .query(async ({ input, ctx }) => {
      return await meetingService.getMeetingSchedule(
        input.startDate,
        input.endDate,
        ctx.user.congregationId,
        input.includeAssignments
      );
    }),

  trackAttendance: protectedProcedure
    .input(z.object({
      meetingId: z.string(),
      attendees: z.array(z.object({
        memberId: z.string(),
        checkInTime: z.date(),
        participationType: z.enum(['in_person', 'virtual', 'hybrid'])
      }))
    }))
    .mutation(async ({ input, ctx }) => {
      return await attendanceService.trackMeetingAttendance(
        input.meetingId,
        input.attendees,
        ctx.user.congregationId,
        ctx.user.id
      );
    })
})
```

### Data Models

```typescript
interface MidweekMeeting {
  id: string;
  congregationId: string;
  date: Date;
  workbookData: {
    weeklyBibleReading: string;
    treasuresFromGodsWord: string;
    diggingForGems: string;
    bibleReading: string;
    initialCall: string;
    returnVisit: string;
    bibleStudy: string;
    livingAsChristians: string;
    congregationBibleStudy: string;
  };
  chairman?: string;
  location: 'kingdom_hall' | 'zoom' | 'hybrid';
  zoomDetails?: {
    meetingId: string;
    password: string;
    joinUrl: string;
  };
  assignments: MeetingAssignment[];
  attendance: MeetingAttendance[];
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MeetingAssignment {
  id: string;
  meetingId: string;
  congregationId: string;
  partType: 'chairman' | 'prayer' | 'bible_reading' | 'initial_call' | 'return_visit' | 'bible_study' | 'talk';
  memberId: string;
  assistantId?: string;
  preparationDeadline: Date;
  status: 'assigned' | 'prepared' | 'completed' | 'cancelled';
  feedback?: string;
  quality?: number;
  notes?: string;
  assignedBy: string;
  assignedAt: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface MemberAvailability {
  id: string;
  memberId: string;
  congregationId: string;
  availabilityType: 'general' | 'blackout' | 'preferred';
  startDate: Date;
  endDate?: Date;
  dayOfWeek?: number;
  partTypes: string[];
  reason?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface MeetingAttendance {
  id: string;
  meetingId: string;
  memberId: string;
  congregationId: string;
  checkInTime: Date;
  checkOutTime?: Date;
  participationType: 'in_person' | 'virtual' | 'hybrid';
  notes?: string;
  recordedBy: string;
  createdAt: Date;
}
```

### Critical Implementation Requirements

1. **JW.org Integration**: Reliable data fetching with respectful API usage and fallback mechanisms
2. **Multi-Tenant Data Isolation**: Every meeting query must include congregation_id filtering
3. **Type Safety Enforcement**: All API calls use tRPC procedures with Zod validation
4. **Database-First Testing**: Real database with comprehensive meeting scenarios
5. **Local Infrastructure Only**: Local PostgreSQL and file storage for meeting data
6. **Performance Optimization**: Efficient meeting data loading and assignment processing

### Testing Requirements

**Unit Tests:**
- JW.org data fetching and parsing logic
- Meeting assignment conflict detection algorithms
- Attendance tracking and analytics calculations
- Member availability and scheduling optimization

**Integration Tests:**
- Complete meeting management workflow from creation to completion
- Multi-congregation meeting isolation and security validation
- JW.org integration with fallback mechanism testing
- Assignment coordination with conflict resolution

**E2E Tests:**
- Full meeting coordinator workflow with JW.org integration
- Meeting assignment and preparation tracking interface
- Attendance tracking with digital check-in functionality
- Meeting analytics and reporting dashboard

## Testing

### Test Data Requirements

- Sample JW.org workbook data for various weeks and languages
- Complex meeting scenarios with multiple assignments and conflicts
- Test member availability patterns and scheduling constraints
- Sample attendance data for analytics and reporting validation

### Validation Scenarios

- Test JW.org integration with various network conditions and data formats
- Validate assignment conflict detection with complex scheduling scenarios
- Test meeting coordination with large congregations and multiple meetings
- Verify attendance tracking accuracy with various participation types

## Definition of Done

- [ ] JW.org workbook integration with automatic meeting data fetching implemented
- [ ] Meeting part assignment with member coordination and conflict detection functional
- [ ] Assignment tracking with preparation monitoring and completion validation working
- [ ] Meeting schedule management with location coordination and format flexibility complete
- [ ] Member availability tracking with preference management and blackout dates implemented
- [ ] Meeting preparation resources with study materials and reference integration functional
- [ ] Meeting attendance tracking with participation monitoring and analytics complete
- [ ] All unit tests pass with real meeting management scenarios
- [ ] Integration tests validate complete meeting workflow
- [ ] E2E tests confirm JW.org integration and coordinator interface
- [ ] Code review completed and approved
- [ ] Documentation updated with meeting management features

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: BMad Master Task Executor
- Date: 2025-01-24

### Debug Log References
- None yet

### Completion Notes
- Story recreated with comprehensive midweek meeting management system
- JW.org integration with reliable data fetching and fallback mechanisms
- Advanced assignment coordination with conflict detection and resolution
- Complete API specification with tRPC procedures for meeting management
- Testing requirements defined with complex meeting scenarios

### File List
- docs/stories/3.1.story.md (recreated)

### Change Log
- 2025-01-24: Story recreated with comprehensive meeting management specification
