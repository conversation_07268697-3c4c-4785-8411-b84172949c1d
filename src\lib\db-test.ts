// Database connectivity test utility
// Tests PostgreSQL connection and basic operations

import { prisma } from './prisma';

export async function testDatabaseConnection(): Promise<{
  success: boolean;
  message: string;
  details?: Record<string, unknown>;
}> {
  try {
    // Test basic connection
    await prisma.$connect();

    // Test query execution
    const result = await prisma.$queryRaw`SELECT 1 as test`;

    // Test model operations (if tables exist)
    try {
      const healthCheck = await prisma.HealthCheck.create({
        data: {
          status: 'connection_test',
        },
      });

      // Clean up test record
      await prisma.HealthCheck.delete({
        where: { id: healthCheck.id },
      });

      return {
        success: true,
        message: 'Database connection successful - Full functionality verified',
        details: { queryResult: result, modelTest: 'passed' },
      };
    } catch {
      // Tables might not exist yet, but connection works
      return {
        success: true,
        message: 'Database connection successful - Tables not yet created',
        details: { queryResult: result, modelTest: 'skipped - tables not found' },
      };
    }
  } catch (error) {
    return {
      success: false,
      message: 'Database connection failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
    };
  } finally {
    await prisma.$disconnect();
  }
}
