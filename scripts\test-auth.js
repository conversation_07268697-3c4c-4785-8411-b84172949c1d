#!/usr/bin/env node

/**
 * Authentication System Test Script for Hermanos App
 * 
 * Tests the JWT authentication system, role-based access control,
 * and congregation login functionality.
 * 
 * Usage: node scripts/test-auth.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

// Import our authentication modules
const { SimpleJWTManager } = require('../src/lib/auth/simpleJWT');
const { ROLES, PERMISSIONS, hasPermission, canAccessAdmin } = require('../src/lib/auth/simpleRBAC');

class AuthenticationTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = [];
  }

  addTestResult(testName, success, message) {
    this.testResults.push({
      test: testName,
      success,
      message,
    });
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  async testJWTTokenGeneration() {
    console.log('\n🔐 Testing JWT Token Generation...');
    
    try {
      const payload = {
        userId: 'test-user-123',
        congregationId: 'CORALOES',
        role: 'elder',
        name: 'Test Elder',
      };

      // Test token generation
      const token = SimpleJWTManager.generateToken(payload);
      this.addTestResult('JWT Token Generation', !!token, `Token generated: ${token.substring(0, 20)}...`);

      // Test token verification
      const decoded = SimpleJWTManager.verifyToken(token);
      const isValid = decoded && decoded.userId === payload.userId;
      this.addTestResult('JWT Token Verification', isValid, isValid ? 'Token verified successfully' : 'Token verification failed');

      // Test token decoding
      const decodedInfo = SimpleJWTManager.decodeToken(token);
      const hasCorrectData = decodedInfo && decodedInfo.congregationId === payload.congregationId;
      this.addTestResult('JWT Token Decoding', hasCorrectData, hasCorrectData ? 'Token decoded correctly' : 'Token decoding failed');

      return true;
    } catch (error) {
      this.addTestResult('JWT Token Generation', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testRoleBasedAccessControl() {
    console.log('\n👥 Testing Role-Based Access Control...');
    
    try {
      // Test publisher permissions
      const publisherCanView = hasPermission(ROLES.PUBLISHER, PERMISSIONS.VIEW_DASHBOARD);
      const publisherCanAdmin = hasPermission(ROLES.PUBLISHER, PERMISSIONS.VIEW_ADMIN);
      
      this.addTestResult('Publisher View Permission', publisherCanView, 'Publisher can view dashboard');
      this.addTestResult('Publisher Admin Restriction', !publisherCanAdmin, 'Publisher cannot access admin');

      // Test elder permissions
      const elderCanAdmin = hasPermission(ROLES.ELDER, PERMISSIONS.VIEW_ADMIN);
      const elderCanManage = hasPermission(ROLES.ELDER, PERMISSIONS.MANAGE_MEMBERS);
      
      this.addTestResult('Elder Admin Permission', elderCanAdmin, 'Elder can access admin');
      this.addTestResult('Elder Management Permission', elderCanManage, 'Elder can manage members');

      // Test admin access function
      const publisherAdminAccess = canAccessAdmin(ROLES.PUBLISHER);
      const elderAdminAccess = canAccessAdmin(ROLES.ELDER);
      
      this.addTestResult('Publisher Admin Access', !publisherAdminAccess, 'Publisher denied admin access');
      this.addTestResult('Elder Admin Access', elderAdminAccess, 'Elder granted admin access');

      return true;
    } catch (error) {
      this.addTestResult('RBAC Testing', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testCongregationAuthentication() {
    console.log('\n🏛️ Testing Congregation Authentication...');
    
    try {
      // Get congregation from database
      const congregation = await this.prisma.congregation.findUnique({
        where: { id: 'CORALOES' },
      });

      if (!congregation) {
        this.addTestResult('Congregation Lookup', false, 'Congregation CORALOES not found');
        return false;
      }

      this.addTestResult('Congregation Lookup', true, `Found congregation: ${congregation.name}`);

      // Test PIN verification
      const correctPin = 'coralpin123';
      const wrongPin = 'wrongpin';
      
      const correctPinValid = await bcrypt.compare(correctPin, congregation.pin);
      const wrongPinValid = await bcrypt.compare(wrongPin, congregation.pin);
      
      this.addTestResult('Correct PIN Verification', correctPinValid, 'Correct PIN verified successfully');
      this.addTestResult('Wrong PIN Rejection', !wrongPinValid, 'Wrong PIN rejected correctly');

      // Test member lookup
      const members = await this.prisma.member.findMany({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
      });

      this.addTestResult('Member Lookup', members.length > 0, `Found ${members.length} active members`);

      return true;
    } catch (error) {
      this.addTestResult('Congregation Authentication', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testEndToEndAuthentication() {
    console.log('\n🔄 Testing End-to-End Authentication Flow...');
    
    try {
      // Simulate the complete login flow
      const congregationId = 'CORALOES';
      const pin = 'coralpin123';

      // 1. Find congregation
      const congregation = await this.prisma.congregation.findUnique({
        where: { id: congregationId, isActive: true },
      });

      if (!congregation) {
        this.addTestResult('E2E: Congregation Lookup', false, 'Congregation not found');
        return false;
      }

      // 2. Verify PIN
      const isPinValid = await bcrypt.compare(pin, congregation.pin);
      if (!isPinValid) {
        this.addTestResult('E2E: PIN Verification', false, 'Invalid PIN');
        return false;
      }

      // 3. Get members
      const members = await this.prisma.member.findMany({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
        orderBy: [
          { role: 'desc' },
          { createdAt: 'asc' },
        ],
      });

      if (members.length === 0) {
        this.addTestResult('E2E: Member Lookup', false, 'No active members found');
        return false;
      }

      const defaultMember = members[0];

      // 4. Generate token
      const tokenPayload = {
        userId: defaultMember.id,
        congregationId: congregation.id,
        role: defaultMember.role,
        name: defaultMember.name,
      };

      const token = SimpleJWTManager.generateToken(tokenPayload);

      // 5. Verify token
      const decoded = SimpleJWTManager.verifyToken(token);
      const isTokenValid = decoded && decoded.userId === defaultMember.id;

      this.addTestResult('E2E: Complete Flow', isTokenValid, `Authentication flow completed for ${defaultMember.name} (${defaultMember.role})`);

      return isTokenValid;
    } catch (error) {
      this.addTestResult('End-to-End Authentication', false, `Error: ${error.message}`);
      return false;
    }
  }

  generateReport() {
    console.log('\n📋 Authentication Test Report');
    console.log('==============================');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.test}: ${r.message}`));
    }
    
    const allPassed = failedTests === 0;
    if (allPassed) {
      console.log('\n🎉 All authentication tests passed!');
    } else {
      console.log('\n⚠️ Some authentication tests failed!');
    }
    
    return allPassed;
  }

  async run() {
    try {
      console.log('🚀 Starting authentication system tests...');
      
      await this.prisma.$connect();
      console.log('✅ Database connection established');
      
      // Run all tests
      await this.testJWTTokenGeneration();
      await this.testRoleBasedAccessControl();
      await this.testCongregationAuthentication();
      await this.testEndToEndAuthentication();
      
      // Generate report
      const success = this.generateReport();
      
      if (success) {
        console.log('\n✅ Authentication system is working correctly!');
      } else {
        console.log('\n❌ Authentication system has issues!');
        process.exit(1);
      }
      
    } catch (error) {
      console.error('\n💥 Test execution failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new AuthenticationTester();
  tester.run().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = AuthenticationTester;
