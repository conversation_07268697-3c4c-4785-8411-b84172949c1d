// Health check API endpoint for Hermanos App
// Tests system status, database connectivity, and environment configuration
// Uses environment variables for all configuration

import { NextResponse } from 'next/server';
import { testDatabaseConnection } from '@/lib/db-test';
import { validateEnvironmentVariables } from '@/lib/env-validation';

export async function GET() {
  try {
    const startTime = Date.now();

    // Validate and get environment configuration
    let envValidation;
    try {
      const env = validateEnvironmentVariables();
      envValidation = {
        status: 'valid',
        nodeEnv: env.NODE_ENV,
        appUrl: env.NEXT_PUBLIC_APP_URL,
        databaseConfigured: !!env.DATABASE_URL,
        jwtConfigured: !!env.JWT_SECRET,
      };
    } catch (error) {
      envValidation = {
        status: 'invalid',
        error: error instanceof Error ? error.message : 'Unknown validation error',
      };
    }

    // Test database connection
    const dbTest = await testDatabaseConnection();

    // Calculate response time
    const responseTime = Date.now() - startTime;

    // Determine overall health status
    const isHealthy = dbTest.success && envValidation.status === 'valid';
    const status = isHealthy ? 'healthy' : 'unhealthy';

    const healthData = {
      status,
      timestamp: new Date().toISOString(),
      environment: envValidation.status === 'valid' ? envValidation.nodeEnv : 'unknown',
      version: '1.0.0',
      uptime: process.uptime(),
      responseTime: `${responseTime}ms`,
      services: {
        database: {
          status: dbTest.success ? 'healthy' : 'unhealthy',
          message: dbTest.message,
          details: dbTest.details,
        },
        environment: {
          status: envValidation.status === 'valid' ? 'healthy' : 'unhealthy',
          validation: envValidation,
        },
      },
      system: {
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          unit: 'MB',
        },
        node: process.version,
        platform: process.platform,
      },
    };

    return NextResponse.json(healthData, {
      status: isHealthy ? 200 : 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        message: errorMessage,
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

// Support HEAD requests for simple health checks
export async function HEAD() {
  try {
    const dbTest = await testDatabaseConnection();
    return new NextResponse(null, {
      status: dbTest.success ? 200 : 503,
    });
  } catch {
    return new NextResponse(null, {
      status: 500,
    });
  }
}
