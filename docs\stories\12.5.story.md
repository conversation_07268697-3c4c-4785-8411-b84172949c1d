# Story 12.5: Territory Boundary Visualization

**Epic:** Epic 12: Territory Visualization & Member Interface
**Story Points:** 13
**Priority:** High
**Status:** Draft

## Story

**As a** congregation administrator,
**I want** to define and display territory boundaries on the map,
**so that** territory coverage areas are clearly defined and visible.

## Acceptance Criteria

1. Territory boundaries can be drawn and edited using map drawing tools
2. Boundary polygons are stored and associated with territory records
3. Territory boundaries are displayed as colored overlays on the map
4. Boundary editing is restricted to administrators with appropriate permissions
5. Overlapping territory boundaries are detected and highlighted for resolution
6. Territory boundaries can be imported from KML or GeoJSON files if available

## Tasks / Subtasks

- [ ] Implement map drawing tools for boundary creation (AC: 1)
  - [ ] Add MapLibre Draw plugin for polygon drawing functionality
  - [ ] Create boundary drawing interface with drawing tools (polygon, edit, delete)
  - [ ] Implement drawing mode toggle for boundary creation and editing
  - [ ] Add drawing controls and toolbar for boundary management
  - [ ] Create boundary drawing validation and error handling
- [ ] Create boundary storage and management (AC: 2)
  - [ ] Implement boundary polygon storage in territory.boundaries field (GeoJSON format)
  - [ ] Create API endpoints for boundary CRUD operations
  - [ ] Add boundary validation for valid GeoJSON polygon format
  - [ ] Implement boundary versioning and change tracking
  - [ ] Create boundary backup and restore functionality
- [ ] Implement boundary visualization (AC: 3)
  - [ ] Display territory boundaries as colored polygon overlays on map
  - [ ] Add boundary styling with territory-specific colors and transparency
  - [ ] Implement boundary hover effects and interaction
  - [ ] Create boundary visibility toggle controls
  - [ ] Add boundary labeling with territory numbers
- [ ] Add administrative access control (AC: 4)
  - [ ] Restrict boundary editing to Elder and Ministerial Servant roles
  - [ ] Implement boundary editing permissions in admin interface
  - [ ] Add boundary editing audit trail and change logging
  - [ ] Create boundary editing confirmation dialogs
  - [ ] Implement boundary editing session management
- [ ] Create overlap detection system (AC: 5)
  - [ ] Implement geometric intersection detection for boundary polygons
  - [ ] Add overlap visualization with warning colors and highlighting
  - [ ] Create overlap resolution workflow and suggestions
  - [ ] Implement overlap reporting and conflict management
  - [ ] Add automatic overlap validation during boundary creation
- [ ] Implement boundary file import (AC: 6)
  - [ ] Add KML file import functionality for territory boundaries
  - [ ] Implement GeoJSON file import for boundary data
  - [ ] Create file validation and format conversion utilities
  - [ ] Add batch boundary import with progress tracking
  - [ ] Implement import error handling and validation reporting
- [ ] Create boundary management interface (Admin Interface)
  - [ ] Build BoundaryManager component for territory boundary administration
  - [ ] Add boundary list view with territory association
  - [ ] Create boundary editing modal with drawing tools
  - [ ] Implement boundary import/export functionality
  - [ ] Add boundary statistics and coverage analysis
- [ ] Integrate with territory dashboard (Dashboard Integration)
  - [ ] Add boundary visualization toggle to territory dashboard
  - [ ] Integrate boundary editing with territory management workflow
  - [ ] Create boundary-based territory filtering and search
  - [ ] Add boundary coverage reports and analytics
  - [ ] Implement boundary-based territory assignment suggestions
- [ ] Write comprehensive tests (Testing Standards)
  - [ ] Unit tests for boundary drawing and validation logic
  - [ ] Integration tests for boundary storage and retrieval
  - [ ] Test overlap detection accuracy and performance
  - [ ] Test boundary file import functionality
  - [ ] E2E tests for complete boundary management workflow

## Dev Notes

### Dependencies and Prerequisites
**DEPENDENCY**: This story depends on:
- Story 12.2 (Territory Location Mapping) - Territory mapping infrastructure required
- Story 10.1 (Territory Database Schema) - Territory.boundaries field already exists

### Boundary Storage Architecture
[Source: docs/territories-architecture.md#territory-database-schema]

**Existing Boundary Support:**
- `boundaries: JSON` - Optional GeoJSON polygon data for territory boundaries
- `boundaries?: GeoJSON.Polygon` - TypeScript interface already defined
- Database field already exists and supports GeoJSON polygon storage

### Technology Stack
[Source: docs/territories-architecture.md#tech-stack]
- **Drawing Tools**: MapLibre GL Draw for polygon drawing and editing
- **Geometry Processing**: Turf.js for geometric operations and overlap detection
- **File Processing**: Custom parsers for KML and GeoJSON import
- **Storage**: PostgreSQL JSON field for GeoJSON polygon storage

### MapLibre Draw Integration
**Drawing Tools Setup:**
```typescript
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';

const draw = new MapboxDraw({
  displayControlsDefault: false,
  controls: {
    polygon: true,
    trash: true
  },
  defaultMode: 'draw_polygon'
});

map.addControl(draw);
```

### GeoJSON Polygon Format
**Boundary Storage Format:**
```typescript
interface TerritoryBoundary {
  type: 'Polygon';
  coordinates: number[][][]; // Array of linear rings
}

// Example GeoJSON Polygon
const boundary: GeoJSON.Polygon = {
  type: 'Polygon',
  coordinates: [
    [
      [-74.0059, 40.7128], // Longitude, Latitude
      [-74.0059, 40.7200],
      [-74.0000, 40.7200],
      [-74.0000, 40.7128],
      [-74.0059, 40.7128]  // Close the polygon
    ]
  ]
};
```

### Overlap Detection Implementation
**Geometric Intersection Detection:**
```typescript
import * as turf from '@turf/turf';

const detectOverlaps = (boundaries: GeoJSON.Polygon[]): OverlapResult[] => {
  const overlaps: OverlapResult[] = [];

  for (let i = 0; i < boundaries.length; i++) {
    for (let j = i + 1; j < boundaries.length; j++) {
      const intersection = turf.intersect(boundaries[i], boundaries[j]);
      if (intersection) {
        overlaps.push({
          territory1: i,
          territory2: j,
          intersection: intersection,
          area: turf.area(intersection)
        });
      }
    }
  }

  return overlaps;
};
```

### File Structure and Locations
[Source: docs/territories-architecture.md#unified-project-structure]
- **Boundary Manager**: `src/components/territories/admin/BoundaryManager.tsx`
- **Drawing Tools**: `src/components/territories/admin/BoundaryDrawingTools.tsx`
- **Boundary Service**: `src/services/territories/BoundaryService.ts`
- **File Import**: `src/services/territories/BoundaryImportService.ts`
- **API Routes**: `src/app/api/territories/[id]/boundaries/route.ts`

### API Specification
**Boundary Management API Endpoints:**
- `GET /api/territories/{id}/boundaries` - Get territory boundary data
- `PUT /api/territories/{id}/boundaries` - Update territory boundary
- `POST /api/territories/boundaries/import` - Import boundaries from file
- `POST /api/territories/boundaries/validate` - Validate boundary overlaps
- `GET /api/territories/boundaries/overlaps` - Get overlap detection results

### Boundary Visualization
**Polygon Overlay Styling:**
```typescript
const boundaryLayer = {
  id: 'territory-boundaries',
  type: 'fill',
  source: 'territories',
  paint: {
    'fill-color': [
      'case',
      ['==', ['get', 'status'], 'available'], '#10B981',
      ['==', ['get', 'status'], 'assigned'], '#3B82F6',
      ['==', ['get', 'status'], 'completed'], '#F59E0B',
      '#EF4444' // out_of_service
    ],
    'fill-opacity': 0.3,
    'fill-outline-color': '#000000'
  }
};
```

### Administrative Access Control
**Boundary Editing Permissions:**
- Elder role: Full boundary editing access
- Ministerial Servant role: Limited boundary editing access
- Publisher role: View-only access to boundaries
- Audit trail for all boundary modifications

### File Import Support
**Supported File Formats:**
- **KML**: Google Earth format with polygon extraction
- **GeoJSON**: Native format with direct import
- **Shapefile**: Future enhancement (not in this story)

**Import Validation:**
- Valid polygon geometry validation
- Coordinate system conversion (WGS84)
- Territory association validation
- Duplicate boundary detection

### Performance Considerations
**Boundary Performance:**
- Efficient polygon rendering for large boundary sets
- Optimized overlap detection algorithms
- Lazy loading of boundary data based on map bounds
- Cached overlap detection results

### Security and Authorization
**Boundary Security:**
- Admin role verification for boundary editing
- Congregation isolation for all boundary operations
- Audit trail for boundary modifications
- Secure file upload validation for import functionality

### Error Handling Strategy
**Boundary Error Scenarios:**
- Invalid GeoJSON polygon format
- Self-intersecting polygon boundaries
- Boundary overlap conflicts
- File import format errors
- Drawing tool interaction conflicts

### Testing Requirements
[Source: docs/territories-architecture.md#testing-strategy]
- **Drawing Tests**: Test polygon drawing and editing functionality
- **Overlap Tests**: Verify overlap detection accuracy and performance
- **Import Tests**: Test KML and GeoJSON file import functionality
- **Visualization Tests**: Test boundary display and styling
- **Permission Tests**: Verify administrative access control

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-25 | 1.0 | Initial story creation for territory boundary visualization | PO Agent |

## Dev Agent Record

### Agent Model Used
*To be populated by development agent*

### Debug Log References
*To be populated by development agent*

### Completion Notes List
*To be populated by development agent*

### File List
*To be populated by development agent*

## QA Results
*To be populated by QA agent*
