/**
 * Authentication Middleware for Hermanos App
 *
 * Provides middleware functions for protecting API routes with JWT authentication,
 * role-based access control, and congregation isolation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { SimpleJWTManager, JWTPayload } from '../auth/simpleJWT';
import { ROLES, PERMISSIONS, validatePermissions, isValidRole } from '../auth/simpleRBAC';

export interface AuthenticatedRequest extends NextRequest {
  user?: JWTPayload;
  congregationId?: string;
}

export interface AuthOptions {
  requiredPermissions?: PERMISSIONS[];
  requireAll?: boolean;
  allowedRoles?: ROLES[];
}

export interface AuthResponse {
  success: boolean;
  user?: JWTPayload;
  error?: string;
  statusCode?: number;
}

/**
 * Extract and verify JWT token from request
 */
export function extractAndVerifyToken(request: NextRequest): AuthResponse {
  try {
    // Extract token from Authorization header
    const authHeader = request.headers.get('authorization');
    console.log('Auth middleware - Authorization header:', authHeader ? `Bearer ${authHeader.substring(0, 20)}...` : 'Missing');

    const token = SimpleJWTManager.extractTokenFromHeader(authHeader || undefined);
    console.log('Auth middleware - Token extracted:', token ? 'Yes' : 'No');

    if (!token) {
      console.log('Auth middleware - No token provided');
      return {
        success: false,
        error: 'No authentication token provided',
        statusCode: 401,
      };
    }

    // Verify token
    const user = SimpleJWTManager.verifyToken(token);
    console.log('Auth middleware - Token verification:', user ? 'Success' : 'Failed');

    if (!user) {
      return {
        success: false,
        error: 'Invalid or expired authentication token',
        statusCode: 401,
      };
    }

    // Validate role
    if (!isValidRole(user.role)) {
      return {
        success: false,
        error: 'Invalid user role',
        statusCode: 403,
      };
    }

    return {
      success: true,
      user,
    };
  } catch (error) {
    console.error('Token extraction/verification error:', error);
    return {
      success: false,
      error: 'Authentication failed',
      statusCode: 500,
    };
  }
}

/**
 * Validate user permissions
 */
export function validateUserPermissions(user: JWTPayload, options: AuthOptions): AuthResponse {
  try {
    // Check allowed roles
    if (options.allowedRoles && !options.allowedRoles.includes(user.role as ROLES)) {
      return {
        success: false,
        error: 'Insufficient role privileges',
        statusCode: 403,
      };
    }

    // Check required permissions
    if (options.requiredPermissions) {
      const hasPermission = validatePermissions({
        role: user.role as ROLES,
        requiredPermissions: options.requiredPermissions,
        requireAll: options.requireAll,
      });

      if (!hasPermission) {
        return {
          success: false,
          error: 'Insufficient permissions',
          statusCode: 403,
        };
      }
    }

    return {
      success: true,
      user,
    };
  } catch (error) {
    console.error('Permission validation error:', error);
    return {
      success: false,
      error: 'Permission validation failed',
      statusCode: 500,
    };
  }
}

/**
 * Main authentication middleware
 */
export function withAuth(options: AuthOptions = {}) {
  return async (request: NextRequest) => {
    try {
      // Extract and verify token
      const authResult = extractAndVerifyToken(request);
      if (!authResult.success) {
        return NextResponse.json(
          { error: authResult.error },
          { status: authResult.statusCode || 401 }
        );
      }

      const user = authResult.user!;

      // Validate permissions
      const permissionResult = validateUserPermissions(user, options);
      if (!permissionResult.success) {
        return NextResponse.json(
          { error: permissionResult.error },
          { status: permissionResult.statusCode || 403 }
        );
      }

      // Add user and congregation info to request headers for the API route
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('x-user-id', user.userId);
      requestHeaders.set('x-congregation-id', user.congregationId);
      requestHeaders.set('x-user-role', user.role);
      requestHeaders.set('x-user-name', user.name);

      // Create new request with user context
      const authenticatedRequest = new NextRequest(request, {
        headers: requestHeaders,
      });

      return authenticatedRequest;
    } catch (error) {
      console.error('Authentication middleware error:', error);
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 500 }
      );
    }
  };
}

/**
 * Convenience middleware for admin-only routes
 */
export function withAdminAuth() {
  return withAuth({
    requiredPermissions: [PERMISSIONS.VIEW_ADMIN],
  });
}

/**
 * Convenience middleware for elder-only routes
 */
export function withElderAuth() {
  return withAuth({
    allowedRoles: [ROLES.ELDER, ROLES.OVERSEER_COORDINATOR, ROLES.COORDINATOR, ROLES.DEVELOPER],
  });
}

/**
 * Convenience middleware for member management routes
 */
export function withMemberManagementAuth() {
  return withAuth({
    requiredPermissions: [PERMISSIONS.MANAGE_MEMBERS],
  });
}

/**
 * Extract user context from authenticated request headers
 */
export function getUserFromRequest(request: NextRequest): JWTPayload | null {
  try {
    const userId = request.headers.get('x-user-id');
    const congregationId = request.headers.get('x-congregation-id');
    const role = request.headers.get('x-user-role');
    const name = request.headers.get('x-user-name');

    if (!userId || !congregationId || !role || !name) {
      return null;
    }

    return {
      userId,
      congregationId,
      role,
      name,
    };
  } catch (error) {
    console.error('Error extracting user from request:', error);
    return null;
  }
}

/**
 * Middleware for congregation isolation
 * Ensures users can only access data from their own congregation
 */
export function withCongregationIsolation() {
  return withAuth(); // Basic auth already includes congregation isolation
}

/**
 * Create error response for authentication failures
 */
export function createAuthErrorResponse(message: string, statusCode: number = 401) {
  return NextResponse.json(
    {
      error: message,
      timestamp: new Date().toISOString(),
    },
    { status: statusCode }
  );
}

/**
 * Check if request is from an authenticated user
 */
export function isAuthenticated(request: NextRequest): boolean {
  const authResult = extractAndVerifyToken(request);
  return authResult.success;
}

/**
 * Get congregation ID from authenticated request
 */
export function getCongregationId(request: NextRequest): string | null {
  const user = getUserFromRequest(request);
  return user?.congregationId || null;
}
