'use client';

/**
 * Territory Management Admin Page
 *
 * Administrative interface for territory management.
 * Provides comprehensive territory viewing, searching, and filtering capabilities.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import TerritoryDashboard from '@/components/territories/admin/TerritoryDashboard';
import AdminFooter from '@/components/admin/AdminFooter';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

export default function TerritoriosAdminPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAdminAccess();
  }, []);

  const checkAdminAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      // Verify token and check admin permissions
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();

      // Check if user has admin access
      if (!data.permissions.canAccessAdmin) {
        router.push('/dashboard');
        return;
      }

      setUser(data.user);
    } catch (error) {
      console.error('Error checking admin access:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/admin');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Verificando acceso...</span>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-teal-600 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="text-white hover:text-teal-100 transition-colors duration-200"
                aria-label="Volver al panel de administración"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div>
                <h1 className="text-xl font-semibold text-white">
                  Territorios
                </h1>
              </div>
            </div>

            {/* Action Icons */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => {
                  // TODO: Implement territory import functionality
                  console.log('Import territories');
                }}
                className="
                  p-2 rounded-lg text-white bg-teal-700 hover:bg-teal-800
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500
                  transition-colors duration-200
                "
                title="Importar territorios"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              </button>

              <button
                onClick={() => {
                  // TODO: Implement add territory functionality
                  console.log('Add territory');
                }}
                className="
                  p-2 rounded-lg text-white bg-teal-700 hover:bg-teal-800
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500
                  transition-colors duration-200
                "
                title="Agregar territorio"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-20">
        <TerritoryDashboard />
      </div>

      {/* Admin Footer */}
      <AdminFooter currentSection="territorios" />
    </div>
  );
}
