/**
 * Test Database Management Functionality
 * 
 * Tests the database backup, download, and delete functionality
 */

const fs = require('fs').promises;
const path = require('path');

async function testDatabaseManagement() {
  try {
    console.log('🗄️ Testing Database Management Functionality...\n');

    // Test 1: Check backup directory structure
    console.log('📁 Testing backup directory structure...');
    const backupDir = path.join(process.cwd(), 'backups');
    
    try {
      await fs.access(backupDir);
      console.log('✅ Backup directory exists');
    } catch {
      console.log('📁 Creating backup directory...');
      await fs.mkdir(backupDir, { recursive: true });
      console.log('✅ Backup directory created');
    }

    // Test 2: List existing backup files
    console.log('\n📋 Testing backup file listing...');
    const files = await fs.readdir(backupDir);
    const sqlFiles = files.filter(file => file.endsWith('.sql'));
    
    console.log(`✅ Found ${sqlFiles.length} backup files:`);
    for (const file of sqlFiles.slice(0, 5)) { // Show first 5
      const filePath = path.join(backupDir, file);
      const stats = await fs.stat(filePath);
      console.log(`  - ${file} (${formatFileSize(stats.size)}) - ${stats.birthtime.toLocaleString()}`);
    }

    // Test 3: Check database connection environment variables
    console.log('\n🔗 Testing database connection configuration...');
    const requiredEnvVars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];
    let envConfigured = true;

    for (const envVar of requiredEnvVars) {
      if (process.env[envVar]) {
        console.log(`✅ ${envVar}: configured`);
      } else {
        console.log(`❌ ${envVar}: missing`);
        envConfigured = false;
      }
    }

    if (!envConfigured) {
      console.log('\n⚠️ Warning: Some database environment variables are missing.');
      console.log('Backup creation may fail without proper database configuration.');
    }

    // Test 4: Check pg_dump availability (PostgreSQL)
    console.log('\n🔧 Testing pg_dump availability...');
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    try {
      const { stdout } = await execAsync('pg_dump --version');
      console.log(`✅ pg_dump available: ${stdout.trim()}`);
    } catch (error) {
      console.log('❌ pg_dump not found in PATH');
      console.log('   Install PostgreSQL client tools to enable backup functionality');
    }

    // Test 5: Simulate backup file operations
    console.log('\n📝 Testing backup file operations...');
    
    // Create a test backup file
    const testFilename = `test_backup_${Date.now()}.sql`;
    const testFilePath = path.join(backupDir, testFilename);
    const testContent = `-- Test backup file created at ${new Date().toISOString()}\n-- This is a test backup\nSELECT 1;`;
    
    await fs.writeFile(testFilePath, testContent);
    console.log(`✅ Test backup file created: ${testFilename}`);
    
    // Test file stats
    const testStats = await fs.stat(testFilePath);
    console.log(`✅ File size: ${formatFileSize(testStats.size)}`);
    console.log(`✅ Created at: ${testStats.birthtime.toLocaleString()}`);
    
    // Test file reading (download simulation)
    const fileContent = await fs.readFile(testFilePath);
    console.log(`✅ File read successfully (${fileContent.length} bytes)`);
    
    // Clean up test file
    await fs.unlink(testFilePath);
    console.log(`✅ Test backup file deleted`);

    // Test 6: API endpoint structure validation
    console.log('\n🌐 Testing API endpoint structure...');
    const apiEndpoints = [
      'src/app/api/admin/database/backup/route.ts',
      'src/app/api/admin/database/backup/[filename]/download/route.ts',
      'src/app/api/admin/database/backup/[filename]/route.ts'
    ];

    for (const endpoint of apiEndpoints) {
      try {
        await fs.access(endpoint);
        console.log(`✅ ${endpoint} exists`);
      } catch {
        console.log(`❌ ${endpoint} missing`);
      }
    }

    // Test 7: Frontend component validation
    console.log('\n🖥️ Testing frontend component...');
    try {
      await fs.access('src/app/admin/database/page.tsx');
      console.log('✅ Database management page exists');
    } catch {
      console.log('❌ Database management page missing');
    }

    console.log('\n🎉 Database Management tests completed!');
    console.log('\n📋 Summary:');
    console.log('- Backup directory structure: Ready');
    console.log('- API endpoints: Implemented');
    console.log('- Frontend interface: Available');
    console.log('- File operations: Working');
    
    if (envConfigured) {
      console.log('- Database configuration: Complete');
    } else {
      console.log('- Database configuration: Needs attention');
    }

    console.log('\n🚀 Ready to test Database Management interface!');
    console.log('Navigate to /admin/database to test the functionality.');

  } catch (error) {
    console.error('❌ Database Management test failed:', error);
  }
}

// Helper function to format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run the test
testDatabaseManagement().catch(console.error);
