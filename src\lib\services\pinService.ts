/**
 * PIN Management Service
 *
 * Handles PIN generation, validation, reset, and security management
 * with configurable settings per congregation.
 */

import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export interface PinSettings {
  id: string;
  congregationId: string;
  minLength: number;
  maxLength: number;
  requireNumeric: boolean;
  requireAlphanumeric: boolean;
  requireSpecialChars: boolean;
  allowSequential: boolean;
  allowRepeated: boolean;
  expirationDays: number | null;
  bcryptRounds: number;
  maxAttempts: number;
  lockoutDurationMinutes: number;
  preventReuseCount: number;
  temporaryPinExpirationHours: number;
  createdBy: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface PinChangeRecord {
  id: string;
  congregationId: string;
  memberId: string | null;
  memberName: string | null;
  changedBy: string;
  changedByName: string;
  changeType: 'generated' | 'reset' | 'updated' | 'expired';
  reason: string | null;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: Date;
}

export interface PinValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface TemporaryPinRecord {
  id: string;
  congregationId: string;
  memberId: string;
  temporaryPinHash: string;
  resetType: 'temporary' | 'permanent' | 'emergency';
  createdBy: string;
  reason: string | null;
  expirationDate: Date;
  requireChange: boolean;
  used: boolean;
  usedAt: Date | null;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface AccountLockoutRecord {
  id: string;
  congregationId: string;
  memberId: string;
  lockoutReason: string;
  attemptCount: number;
  lockedAt: Date;
  unlockAt: Date;
  unlockedBy: string | null;
  unlockedAt: Date | null;
  isActive: boolean;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityAuditEvent {
  id: string;
  congregationId: string;
  memberId: string | null;
  eventType: 'pin_reset' | 'login_attempt' | 'lockout' | 'policy_change' | 'temporary_pin_created' | 'account_unlocked';
  success: boolean;
  ipAddress: string | null;
  userAgent: string | null;
  details: Record<string, any> | null;
  performedBy: string | null;
  timestamp: Date;
  createdAt: Date;
}

export class PinService {
  /**
   * Get PIN settings for a congregation (with defaults if not configured)
   */
  static async getPinSettings(congregationId: string): Promise<PinSettings> {
    let settings = await prisma.pinSettings.findUnique({
      where: { congregationId },
    });

    // Create default settings if none exist
    if (!settings) {
      settings = await prisma.pinSettings.create({
        data: {
          congregationId,
          minLength: 4,
          maxLength: 8,
          requireNumeric: true,
          requireAlphanumeric: false,
          requireSpecialChars: false,
          allowSequential: true,
          allowRepeated: true,
          expirationDays: null,
          bcryptRounds: 12,
          maxAttempts: 5,
          lockoutDurationMinutes: 30,
          preventReuseCount: 3,
          temporaryPinExpirationHours: 24,
        },
      });
    }

    return {
      id: settings.id,
      congregationId: settings.congregationId,
      minLength: settings.minLength,
      maxLength: settings.maxLength,
      requireNumeric: settings.requireNumeric,
      requireAlphanumeric: settings.requireAlphanumeric,
      requireSpecialChars: settings.requireSpecialChars,
      allowSequential: settings.allowSequential,
      allowRepeated: settings.allowRepeated,
      expirationDays: settings.expirationDays,
      bcryptRounds: settings.bcryptRounds,
      maxAttempts: settings.maxAttempts,
      lockoutDurationMinutes: settings.lockoutDurationMinutes,
      preventReuseCount: settings.preventReuseCount,
      temporaryPinExpirationHours: settings.temporaryPinExpirationHours,
      createdBy: settings.createdBy,
      createdAt: settings.createdAt,
      updatedAt: settings.updatedAt,
    };
  }

  /**
   * Update PIN settings for a congregation
   */
  static async updatePinSettings(
    congregationId: string,
    updates: Partial<Omit<PinSettings, 'id' | 'congregationId' | 'createdAt' | 'updatedAt'>>,
    updatedBy: string
  ): Promise<PinSettings> {
    await prisma.pinSettings.upsert({
      where: { congregationId },
      update: {
        ...updates,
        createdBy: updatedBy,
      },
      create: {
        congregationId,
        minLength: updates.minLength || 4,
        maxLength: updates.maxLength || 8,
        requireNumeric: updates.requireNumeric ?? true,
        requireAlphanumeric: updates.requireAlphanumeric ?? false,
        requireSpecialChars: updates.requireSpecialChars ?? false,
        allowSequential: updates.allowSequential ?? true,
        allowRepeated: updates.allowRepeated ?? true,
        expirationDays: updates.expirationDays || null,
        bcryptRounds: updates.bcryptRounds || 12,
        createdBy: updatedBy,
      },
    });

    return this.getPinSettings(congregationId);
  }

  /**
   * Generate a random PIN based on congregation settings
   */
  static async generatePin(congregationId: string): Promise<string> {
    const settings = await this.getPinSettings(congregationId);
    let pin: string;
    let attempts = 0;
    const maxAttempts = 100;

    do {
      pin = this.createRandomPin(settings);
      attempts++;

      if (attempts >= maxAttempts) {
        throw new Error('Unable to generate unique PIN after maximum attempts');
      }
    } while (!(await this.validatePinUniqueness(pin, congregationId)));

    return pin;
  }

  /**
   * Create a random PIN based on settings
   */
  private static createRandomPin(settings: PinSettings): string {
    const length = Math.floor(Math.random() * (settings.maxLength - settings.minLength + 1)) + settings.minLength;

    let charset = '';
    if (settings.requireNumeric && !settings.requireAlphanumeric) {
      charset = '0123456789';
    } else if (settings.requireAlphanumeric) {
      charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    } else {
      charset = '0123456789';
    }

    if (settings.requireSpecialChars) {
      charset += '!@#$%^&*';
    }

    let pin = '';
    for (let i = 0; i < length; i++) {
      pin += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    // Apply additional rules
    if (!settings.allowSequential && this.hasSequentialChars(pin)) {
      return this.createRandomPin(settings); // Regenerate
    }

    if (!settings.allowRepeated && this.hasRepeatedChars(pin)) {
      return this.createRandomPin(settings); // Regenerate
    }

    return pin;
  }

  /**
   * Check if PIN has sequential characters
   */
  private static hasSequentialChars(pin: string): boolean {
    for (let i = 0; i < pin.length - 2; i++) {
      const char1 = pin.charCodeAt(i);
      const char2 = pin.charCodeAt(i + 1);
      const char3 = pin.charCodeAt(i + 2);

      if (char2 === char1 + 1 && char3 === char2 + 1) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if PIN has repeated characters
   */
  private static hasRepeatedChars(pin: string): boolean {
    for (let i = 0; i < pin.length - 1; i++) {
      if (pin[i] === pin[i + 1]) {
        return true;
      }
    }
    return false;
  }

  /**
   * Validate PIN format based on congregation settings
   */
  static async validatePinFormat(pin: string, congregationId: string): Promise<PinValidationResult> {
    const settings = await this.getPinSettings(congregationId);
    const errors: string[] = [];

    // Length validation
    if (pin.length < settings.minLength) {
      errors.push(`PIN debe tener al menos ${settings.minLength} caracteres`);
    }
    if (pin.length > settings.maxLength) {
      errors.push(`PIN no puede tener más de ${settings.maxLength} caracteres`);
    }

    // Character type validation
    if (settings.requireNumeric && !/\d/.test(pin)) {
      errors.push('PIN debe contener al menos un número');
    }

    if (settings.requireAlphanumeric && !/[a-zA-Z]/.test(pin)) {
      errors.push('PIN debe contener al menos una letra');
    }

    if (settings.requireSpecialChars && !/[!@#$%^&*]/.test(pin)) {
      errors.push('PIN debe contener al menos un carácter especial (!@#$%^&*)');
    }

    // Sequential characters validation
    if (!settings.allowSequential && this.hasSequentialChars(pin)) {
      errors.push('PIN no puede contener caracteres secuenciales');
    }

    // Repeated characters validation
    if (!settings.allowRepeated && this.hasRepeatedChars(pin)) {
      errors.push('PIN no puede contener caracteres repetidos consecutivos');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate PIN uniqueness within congregation
   */
  static async validatePinUniqueness(pin: string, congregationId: string): Promise<boolean> {
    // Check against congregation PIN
    const congregation = await prisma.congregation.findUnique({
      where: { id: congregationId },
    });

    if (congregation && await bcrypt.compare(pin, congregation.pin)) {
      return false;
    }

    // Check against member PINs
    const members = await prisma.member.findMany({
      where: { congregationId },
      select: { pin: true },
    });

    for (const member of members) {
      if (await bcrypt.compare(pin, member.pin)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validate PIN completely (format + uniqueness)
   */
  static async validatePin(pin: string, congregationId: string): Promise<PinValidationResult> {
    const formatResult = await this.validatePinFormat(pin, congregationId);

    if (!formatResult.isValid) {
      return formatResult;
    }

    const isUnique = await this.validatePinUniqueness(pin, congregationId);
    if (!isUnique) {
      return {
        isValid: false,
        errors: ['Este PIN ya está en uso en la congregación'],
      };
    }

    return { isValid: true, errors: [] };
  }

  /**
   * Hash a PIN using congregation-specific bcrypt rounds
   */
  static async hashPin(pin: string, congregationId: string): Promise<string> {
    const settings = await this.getPinSettings(congregationId);
    return bcrypt.hash(pin, settings.bcryptRounds);
  }

  /**
   * Reset a member's PIN
   */
  static async resetMemberPin(
    congregationId: string,
    memberId: string,
    resetBy: string,
    reason?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ newPin: string; hashedPin: string }> {
    // Get current member
    const member = await prisma.member.findFirst({
      where: {
        id: memberId,
        congregationId,
      },
    });

    if (!member) {
      throw new Error('Member not found');
    }

    // Generate new PIN
    const newPin = await this.generatePin(congregationId);
    const hashedPin = await this.hashPin(newPin, congregationId);

    // Update member and create history record in transaction
    await prisma.$transaction(async (tx) => {
      // Update member PIN
      await tx.member.update({
        where: { id: memberId },
        data: { pin: hashedPin },
      });

      // Create history record
      await tx.pinChangeHistory.create({
        data: {
          congregationId,
          memberId,
          changedBy: resetBy,
          changeType: 'reset',
          oldPinHash: member.pin,
          newPinHash: hashedPin,
          reason: reason || 'PIN reset by administrator',
          ipAddress,
          userAgent,
        },
      });
    });

    return { newPin, hashedPin };
  }

  /**
   * Get PIN change history
   */
  static async getPinChangeHistory(
    congregationId: string,
    memberId?: string,
    limit: number = 50
  ): Promise<PinChangeRecord[]> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = { congregationId };
    if (memberId) {
      where.memberId = memberId;
    }

    const history = await prisma.pinChangeHistory.findMany({
      where,
      include: {
        member: {
          select: {
            name: true,
          },
        },
        changedByMember: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });

    return history.map(record => ({
      id: record.id,
      congregationId: record.congregationId,
      memberId: record.memberId,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      memberName: record.member ? (record as any).member.name : null,
      changedBy: record.changedBy,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      changedByName: (record as any).changedByMember.name,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      changeType: record.changeType as any,
      reason: record.reason,
      ipAddress: record.ipAddress,
      userAgent: record.userAgent,
      createdAt: record.createdAt,
    }));
  }

  /**
   * Create a temporary PIN for a member
   */
  static async createTemporaryPin(
    congregationId: string,
    memberId: string,
    createdBy: string,
    resetType: 'temporary' | 'permanent' | 'emergency' = 'temporary',
    reason?: string,
    expirationHours?: number,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ temporaryPin: string; expirationDate: Date }> {
    const settings = await this.getPinSettings(congregationId);

    // Generate temporary PIN
    const temporaryPin = await this.generatePin(congregationId);
    const hashedPin = await this.hashPin(temporaryPin, congregationId);

    // Calculate expiration date
    const expirationDate = new Date();
    expirationDate.setHours(expirationDate.getHours() + (expirationHours || settings.temporaryPinExpirationHours));

    // Create temporary PIN record
    await prisma.temporaryPin.create({
      data: {
        congregationId,
        memberId,
        temporaryPinHash: hashedPin,
        resetType,
        createdBy,
        reason,
        expirationDate,
        requireChange: true,
        ipAddress,
        userAgent,
      },
    });

    // Log security event
    await this.logSecurityEvent(
      congregationId,
      memberId,
      'temporary_pin_created',
      true,
      { resetType, expirationDate },
      createdBy,
      ipAddress,
      userAgent
    );

    return { temporaryPin, expirationDate };
  }

  /**
   * Check if member account is locked
   */
  static async isAccountLocked(congregationId: string, memberId: string): Promise<boolean> {
    const activeLockout = await prisma.accountLockout.findFirst({
      where: {
        congregationId,
        memberId,
        isActive: true,
        unlockAt: {
          gt: new Date(),
        },
      },
    });

    return !!activeLockout;
  }

  /**
   * Lock member account due to failed attempts
   */
  static async lockAccount(
    congregationId: string,
    memberId: string,
    attemptCount: number,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AccountLockoutRecord> {
    const settings = await this.getPinSettings(congregationId);

    const unlockAt = new Date();
    unlockAt.setMinutes(unlockAt.getMinutes() + settings.lockoutDurationMinutes);

    const lockout = await prisma.accountLockout.create({
      data: {
        congregationId,
        memberId,
        lockoutReason: 'failed_attempts',
        attemptCount,
        unlockAt,
        ipAddress,
        userAgent,
      },
    });

    // Log security event
    await this.logSecurityEvent(
      congregationId,
      memberId,
      'lockout',
      true,
      { attemptCount, unlockAt },
      null,
      ipAddress,
      userAgent
    );

    return {
      id: lockout.id,
      congregationId: lockout.congregationId,
      memberId: lockout.memberId,
      lockoutReason: lockout.lockoutReason,
      attemptCount: lockout.attemptCount,
      lockedAt: lockout.lockedAt,
      unlockAt: lockout.unlockAt,
      unlockedBy: lockout.unlockedBy,
      unlockedAt: lockout.unlockedAt,
      isActive: lockout.isActive,
      ipAddress: lockout.ipAddress,
      userAgent: lockout.userAgent,
      createdAt: lockout.createdAt,
      updatedAt: lockout.updatedAt,
    };
  }

  /**
   * Unlock member account
   */
  static async unlockAccount(
    congregationId: string,
    memberId: string,
    unlockedBy: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await prisma.accountLockout.updateMany({
      where: {
        congregationId,
        memberId,
        isActive: true,
      },
      data: {
        isActive: false,
        unlockedBy,
        unlockedAt: new Date(),
      },
    });

    // Log security event
    await this.logSecurityEvent(
      congregationId,
      memberId,
      'account_unlocked',
      true,
      { unlockedBy },
      unlockedBy,
      ipAddress,
      userAgent
    );
  }

  /**
   * Log security audit event
   */
  static async logSecurityEvent(
    congregationId: string,
    memberId: string | null,
    eventType: 'pin_reset' | 'login_attempt' | 'lockout' | 'policy_change' | 'temporary_pin_created' | 'account_unlocked',
    success: boolean,
    details?: Record<string, any>,
    performedBy?: string | null,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await prisma.securityAuditEvent.create({
      data: {
        congregationId,
        memberId,
        eventType,
        success,
        details: details || {},
        performedBy,
        ipAddress,
        userAgent,
      },
    });
  }

  /**
   * Get security audit events
   */
  static async getSecurityAuditEvents(
    congregationId: string,
    memberId?: string,
    eventType?: string,
    limit: number = 50
  ): Promise<SecurityAuditEvent[]> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = { congregationId };
    if (memberId) where.memberId = memberId;
    if (eventType) where.eventType = eventType;

    const events = await prisma.securityAuditEvent.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: limit,
    });

    return events.map(event => ({
      id: event.id,
      congregationId: event.congregationId,
      memberId: event.memberId,
      eventType: event.eventType as any,
      success: event.success,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      details: event.details as Record<string, any> | null,
      performedBy: event.performedBy,
      timestamp: event.timestamp,
      createdAt: event.createdAt,
    }));
  }
}
