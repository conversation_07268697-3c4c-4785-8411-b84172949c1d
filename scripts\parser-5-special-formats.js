const XLSX = require('xlsx');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Parser 5: Special formats for the remaining 5 territories
function parseFormat5(data, territoryNum) {
  const addresses = [];
  const notes = [];
  
  if (territoryNum === '014') {
    // Territory 014: Building format with apartments
    let buildingAddress = '';
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      if (!row || row.length === 0) continue;
      
      // Look for building number and street
      if (row[4] && row[4].toString().trim() === 'FLAGLER') {
        buildingAddress = '6580 FLAGLER ST, Miami, FL'; // From the analysis
      }
      
      // Look for apartment numbers in column 2
      if (row[1] && typeof row[1] === 'number') {
        const aptNum = row[1].toString();
        if (/^\d{1,3}$/.test(aptNum) && aptNum !== '6580') { // Apartment numbers
          const fullAddress = `${buildingAddress} Apt ${aptNum}`;
          addresses.push(fullAddress);
          
          // Look for observations in column 7
          if (row[6] && row[6].toString().trim()) {
            const obs = row[6].toString().trim();
            if (obs !== 'Observaciones' && obs.length > 2) {
              notes.push(`${fullAddress}: ${obs}`);
            }
          }
        }
      }
    }
  } else {
    // Territories 051, 053, 068, 069: Multi-column format with split streets
    let currentStreet = '';
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      if (!row || row.length === 0) continue;
      
      // Look for split street format in columns 3, 4, 5
      if (row[2] && row[3] && row[4]) {
        const part1 = row[2].toString().trim();
        const part2 = row[3].toString().trim();
        const part3 = row[4].toString().trim();
        
        // Check if this forms a street (SW + number + ST/AVE)
        if (/^(SW|NW|SE|NE|W|E|N|S)$/i.test(part1) && 
            /^\d+$/.test(part2) && 
            /^(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE|TER)$/i.test(part3)) {
          currentStreet = `${part1} ${part2} ${part3}`;
          continue;
        }
      }
      
      // Look for single street format in column 3
      if (row[2] && /\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE|TER)\b/i.test(row[2].toString())) {
        currentStreet = row[2].toString().trim();
        continue;
      }
      
      // Process house numbers in column 1
      if (row[0] && currentStreet) {
        const cellValue = row[0].toString().trim();
        if (/^\d+[A-Z]?$/i.test(cellValue) && cellValue !== 'No. de Casa' && cellValue.length < 6) {
          const houseNumber = cellValue;
          const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL`;
          addresses.push(fullAddress);
          
          // Look for observations in column 6
          if (row[5] && row[5].toString().trim()) {
            const obs = row[5].toString().trim();
            if (obs !== 'Observaciones' && obs !== 'A' && obs.length > 1) {
              notes.push(`${fullAddress}: ${obs}`);
            }
          }
        }
      }
      
      // Process house numbers in column 7 (second column of addresses)
      if (row[6] && currentStreet) {
        const cellValue = row[6].toString().trim();
        if (/^\d+[A-Z]?$/i.test(cellValue) && cellValue !== 'No. de Casa' && cellValue.length < 6) {
          const houseNumber = cellValue;
          const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL`;
          addresses.push(fullAddress);
          
          // These typically don't have separate observations
        }
      }
    }
  }
  
  return { addresses, notes };
}

async function importWithParser5() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  
  // The 5 specific territories that need Parser 5
  const specialTerritories = ['014', '051', '053', '068', '069'];
  
  console.log(`🚀 Testing Parser 5 on ${specialTerritories.length} special territories...\n`);
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const number of specialTerritories) {
    try {
      console.log(`📋 Processing Territory ${number} with Parser 5...`);
      
      const fileName = `Terr. ${number}.xlsx`;
      const filePath = path.join(territoriosDir, fileName);
      
      if (!fs.existsSync(filePath)) {
        console.log(`  ❌ File not found: ${fileName}`);
        continue;
      }
      
      const workbook = XLSX.readFile(filePath);
      const territorySheet = workbook.SheetNames.find(name => 
        name.toLowerCase().includes('terr') && !name.toLowerCase().includes('mapa')
      ) || workbook.SheetNames[0];
      
      const sheet = workbook.Sheets[territorySheet];
      const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      
      // Use Parser 5
      const result = parseFormat5(data, number);
      
      if (result.addresses.length === 0) {
        console.log(`  ⚠️  No addresses found with Parser 5, skipping...`);
        continue;
      }
      
      // Create territory record
      const territory = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: number,
          address: result.addresses.join('\n'),
          notes: result.notes.length > 0 ? result.notes.join('\n') : null,
          status: 'available',
          displayOrder: parseInt(number)
        }
      });
      
      console.log(`  ✅ Created territory ${number} with ${result.addresses.length} addresses (Parser 5)`);
      if (result.notes.length > 0) {
        console.log(`     📝 ${result.notes.length} notes imported`);
      }
      
      successCount++;
      
    } catch (error) {
      console.log(`  ❌ Error processing territory ${number}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`\n🎉 Parser 5 import completed!`);
  console.log(`✅ Successfully imported: ${successCount} territories`);
  console.log(`❌ Errors: ${errorCount} territories`);
  
  await prisma.$disconnect();
}

importWithParser5().catch(console.error);
