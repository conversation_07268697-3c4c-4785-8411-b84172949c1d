/**
 * Simple Role-Based Access Control (RBAC) for Herman<PERSON> App
 *
 * Defines roles, permissions, and access control functions
 * for congregation management system.
 */

export enum ROLES {
  PUBLISHER = 'publisher',
  MINISTERIAL_SERVANT = 'ministerial_servant',
  ELDER = 'elder',
  COORDINATOR = 'coordinator',
}

export enum PERMISSIONS {
  // Basic permissions
  VIEW_DASHBOARD = 'view_dashboard',
  VIEW_PROFILE = 'view_profile',
  EDIT_PROFILE = 'edit_profile',

  // Admin permissions
  VIEW_ADMIN = 'view_admin',
  MANAGE_MEMBERS = 'manage_members',
  MANAGE_TASKS = 'manage_tasks',
  MANAGE_MEETINGS = 'manage_meetings',
  MANAGE_LETTERS = 'manage_letters',
  MANAGE_EVENTS = 'manage_events',

  // Advanced admin permissions
  MANAGE_CONGREGATION_SETTINGS = 'manage_congregation_settings',
  MANAGE_PERMISSIONS = 'manage_permissions',
  VIEW_FIELD_SERVICE_RECORDS = 'view_field_service_records',
  MANAGE_FIELD_SERVICE_RECORDS = 'manage_field_service_records',

  // Developer permissions
  MANAGE_DATABASE = 'manage_database',
  VIEW_SYSTEM_LOGS = 'view_system_logs',
  MANAGE_SYSTEM_SETTINGS = 'manage_system_settings',
}

/**
 * Role-Permission mapping
 * Defines which permissions each role has
 */
export const ROLE_PERMISSIONS: Record<ROLES, PERMISSIONS[]> = {
  [ROLES.PUBLISHER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_PROFILE,
    PERMISSIONS.EDIT_PROFILE,
  ],

  [ROLES.MINISTERIAL_SERVANT]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_PROFILE,
    PERMISSIONS.EDIT_PROFILE,
    PERMISSIONS.VIEW_ADMIN,
    PERMISSIONS.MANAGE_TASKS,
    PERMISSIONS.VIEW_FIELD_SERVICE_RECORDS,
  ],

  [ROLES.ELDER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_PROFILE,
    PERMISSIONS.EDIT_PROFILE,
    PERMISSIONS.VIEW_ADMIN,
    PERMISSIONS.MANAGE_TASKS,
    PERMISSIONS.MANAGE_MEETINGS,
    PERMISSIONS.MANAGE_LETTERS,
    PERMISSIONS.MANAGE_EVENTS,
    PERMISSIONS.VIEW_FIELD_SERVICE_RECORDS,
    PERMISSIONS.MANAGE_FIELD_SERVICE_RECORDS,
  ],

  [ROLES.COORDINATOR]: [
    // Coordinators have full access by default (can be modified with congregation PIN)
    ...Object.values(PERMISSIONS),
  ],
};

/**
 * Check if a role has a specific permission
 */
export function hasPermission(role: ROLES, permission: PERMISSIONS): boolean {
  const rolePermissions = ROLE_PERMISSIONS[role];
  return rolePermissions.includes(permission);
}

/**
 * Check if a role has any of the specified permissions
 */
export function hasAnyPermission(role: ROLES, permissions: PERMISSIONS[]): boolean {
  return permissions.some(permission => hasPermission(role, permission));
}

/**
 * Check if a role has all of the specified permissions
 */
export function hasAllPermissions(role: ROLES, permissions: PERMISSIONS[]): boolean {
  return permissions.every(permission => hasPermission(role, permission));
}

/**
 * Check if a role can access admin features
 */
export function canAccessAdmin(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.VIEW_ADMIN);
}

/**
 * Check if a role can manage congregation settings
 */
export function canManageCongregation(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.MANAGE_CONGREGATION_SETTINGS);
}

/**
 * Check if a role can manage members
 */
export function canManageMembers(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.MANAGE_MEMBERS);
}

/**
 * Check if a role can disable token expiration
 */
export function canDisableTokenExpiration(role: ROLES): boolean {
  return role === ROLES.COORDINATOR || role === ROLES.ELDER;
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role: ROLES): PERMISSIONS[] {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * Check if a role string is valid
 */
export function isValidRole(role: string): role is ROLES {
  return Object.values(ROLES).includes(role as ROLES);
}

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export function getRoleLevel(role: ROLES): number {
  const levels = {
    [ROLES.PUBLISHER]: 1,
    [ROLES.MINISTERIAL_SERVANT]: 2,
    [ROLES.ELDER]: 3,
    [ROLES.COORDINATOR]: 4,
  };

  return levels[role] || 0;
}

/**
 * Check if one role has higher privileges than another
 */
export function hasHigherPrivileges(role1: ROLES, role2: ROLES): boolean {
  return getRoleLevel(role1) > getRoleLevel(role2);
}

/**
 * Get user-friendly role name in Spanish
 */
export function getRoleDisplayName(role: ROLES): string {
  const displayNames = {
    [ROLES.PUBLISHER]: 'Publicador',
    [ROLES.MINISTERIAL_SERVANT]: 'Siervo Ministerial',
    [ROLES.ELDER]: 'Anciano',
    [ROLES.COORDINATOR]: 'Coordinador',
  };

  return displayNames[role] || role;
}

/**
 * Permission validation for API routes
 */
export interface PermissionCheck {
  role: ROLES;
  requiredPermissions?: PERMISSIONS[];
  requireAll?: boolean; // If true, user must have ALL permissions; if false, ANY permission
}

export function validatePermissions(check: PermissionCheck): boolean {
  if (!check.requiredPermissions || check.requiredPermissions.length === 0) {
    return true; // No specific permissions required
  }

  if (check.requireAll) {
    return hasAllPermissions(check.role, check.requiredPermissions);
  } else {
    return hasAnyPermission(check.role, check.requiredPermissions);
  }
}
