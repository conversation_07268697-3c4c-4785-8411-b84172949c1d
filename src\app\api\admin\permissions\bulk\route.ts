/**
 * Bulk Permissions Management API Endpoint
 *
 * Handles bulk permission updates for comprehensive permissions management interface.
 * Allows updating multiple sections and permissions for a member in a single request.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PermissionDelegationService } from '@/lib/services/permissionDelegationService';

// Validation schemas
const BulkPermissionUpdateSchema = z.object({
  memberId: z.string().min(1, 'Member ID is required'),
  permissions: z.record(z.object({
    sectionId: z.string(),
    permissions: z.array(z.string())
  })),
  notes: z.string().optional(),
  reason: z.string().optional(),
});

/**
 * POST - Update multiple permissions for a member
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has permission to assign permissions (only coordinators or congregation PIN holders)
    if (user.role !== 'coordinator' && !user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Insufficient permissions to assign permissions. Only coordinators or congregation PIN holders can assign permissions.' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = BulkPermissionUpdateSchema.parse(body);

    // Get client IP and user agent for audit logging
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const delegationContext = {
      performedBy: user.userId,
      hasCongregationPinAccess: user.hasCongregationPinAccess,
      ipAddress,
      userAgent,
    };

    // First, revoke all existing permissions for this member
    await PermissionDelegationService.revokeAllPermissions(
      user.congregationId,
      validatedData.memberId,
      delegationContext,
      'Bulk permission update - clearing existing permissions'
    );

    // Then assign new permissions
    const assignments = [];
    for (const [sectionId, sectionData] of Object.entries(validatedData.permissions)) {
      if (sectionData.permissions.length > 0) {
        const assignment = await PermissionDelegationService.assignPermissions(
          user.congregationId,
          {
            userId: validatedData.memberId,
            sectionId,
            permissions: sectionData.permissions,
            notes: validatedData.notes,
            reason: validatedData.reason,
          },
          delegationContext
        );
        assignments.push(assignment);
      }
    }

    return NextResponse.json({
      success: true,
      assignments,
      message: 'Permissions updated successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Bulk permission update POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to update permissions',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to update permissions.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to update permissions.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to update permissions.' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to update permissions.' },
    { status: 405 }
  );
}
