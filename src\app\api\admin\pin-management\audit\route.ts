/**
 * Security Audit API Endpoint
 *
 * Provides access to security audit events and compliance monitoring.
 * Only accessible to elders, coordinators, and developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PinService } from '@/lib/services/pinService';

/**
 * GET - Get security audit events
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view security audit' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('memberId') || undefined;
    const eventType = searchParams.get('eventType') || undefined;
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : 50;

    // Validate limit
    if (isNaN(limit) || limit < 1 || limit > 200) {
      return NextResponse.json(
        { error: 'Invalid limit parameter. Must be between 1 and 200.' },
        { status: 400 }
      );
    }

    // Validate event type if provided
    const validEventTypes = ['pin_reset', 'login_attempt', 'lockout', 'policy_change', 'temporary_pin_created', 'account_unlocked'];
    if (eventType && !validEventTypes.includes(eventType)) {
      return NextResponse.json(
        { error: `Invalid event type. Must be one of: ${validEventTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Get security audit events
    const auditEvents = await PinService.getSecurityAuditEvents(
      user.congregationId,
      memberId,
      eventType,
      limit
    );

    // Get summary statistics
    const totalEvents = auditEvents.length;
    const successfulEvents = auditEvents.filter(event => event.success).length;
    const failedEvents = totalEvents - successfulEvents;

    // Group events by type
    const eventsByType = auditEvents.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      auditEvents,
      summary: {
        totalEvents,
        successfulEvents,
        failedEvents,
        successRate: totalEvents > 0 ? (successfulEvents / totalEvents * 100).toFixed(2) + '%' : '0%',
        eventsByType,
      },
      filters: {
        memberId: memberId || null,
        eventType: eventType || null,
        limit,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Security audit GET error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch security audit events',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Log a security audit event (for testing purposes)
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Only allow developers to manually log events
    if (user.role !== 'developer') {
      return NextResponse.json(
        { error: 'Only developers can manually log security events' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { memberId, eventType, success, details } = body;

    // Extract client information
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Log the security event
    await PinService.logSecurityEvent(
      user.congregationId,
      memberId,
      eventType,
      success,
      details,
      user.userId,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: true,
      message: 'Security event logged successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Security audit POST error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to log security event',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
