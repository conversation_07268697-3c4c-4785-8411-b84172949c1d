-- CreateTable
CREATE TABLE "member_change_history" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "changed_by" TEXT NOT NULL,
    "change_type" VARCHAR(50) NOT NULL,
    "field_name" VARCHAR(100),
    "old_value" TEXT,
    "new_value" TEXT,
    "reason" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "member_change_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "member_change_history_congregation_id_member_id_idx" ON "member_change_history"("congregation_id", "member_id");

-- CreateIndex
CREATE INDEX "member_change_history_congregation_id_change_type_idx" ON "member_change_history"("congregation_id", "change_type");

-- CreateIndex
CREATE INDEX "member_change_history_created_at_idx" ON "member_change_history"("created_at");

-- AddForeignKey
ALTER TABLE "member_change_history" ADD CONSTRAINT "member_change_history_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "member_change_history" ADD CONSTRAINT "member_change_history_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "member_change_history" ADD CONSTRAINT "member_change_history_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "members"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
