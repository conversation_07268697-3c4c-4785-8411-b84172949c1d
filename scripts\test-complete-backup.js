/**
 * Test Complete Database Backup
 *
 * Tests the improved backup that includes ALL tables
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testCompleteBackup() {
  try {
    console.log('🗄️ Testing Complete Database Backup...\n');

    // Test 1: Get all tables and their counts
    console.log('1. Getting all tables and record counts...');

    const allTables = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
      AND table_name != '_prisma_migrations'
      ORDER BY table_name;
    `;

    console.log(`Found ${allTables.length} tables:`);

    let totalExpectedRecords = 0;
    for (const table of allTables) {
      try {
        const result = await prisma.$queryRawUnsafe(`SELECT COUNT(*) as count FROM "${table.table_name}"`);
        const count = parseInt(result[0].count);
        totalExpectedRecords += count;
        console.log(`  ${table.table_name}: ${count} records`);
      } catch (error) {
        console.log(`  ${table.table_name}: Error - ${error.message}`);
      }
    }

    console.log(`\n📊 Total expected records: ${totalExpectedRecords}`);

    // Test 2: Test the backup creation logic
    console.log('\n2. Testing backup creation logic...');

    const timestamp = new Date().toISOString();
    let sqlContent = `-- Database Backup Created: ${timestamp}\n`;
    sqlContent += `-- Coral Oeste Congregation Database\n\n`;

    let totalActualRecords = 0;
    let tablesProcessed = 0;

    for (const table of allTables) {
      try {
        const tableName = table.table_name;
        console.log(`  Processing table: ${tableName}...`);

        const tableData = await prisma.$queryRawUnsafe(`SELECT * FROM "${tableName}"`);

        if (Array.isArray(tableData) && tableData.length > 0) {
          const columns = Object.keys(tableData[0]);
          const columnList = columns.map(col => `"${col}"`).join(', ');

          sqlContent += `-- Table: ${tableName} (${tableData.length} records)\n`;
          sqlContent += `INSERT INTO "${tableName}" (${columnList}) VALUES\n`;

          // Process first few records for testing
          const sampleSize = Math.min(tableData.length, 3);
          const values = tableData.slice(0, sampleSize).map(row => {
            const rowValues = columns.map(col => {
              const value = row[col];
              if (value === null || value === undefined) return 'NULL';
              if (typeof value === 'string') {
                const escaped = value.replace(/'/g, "''").replace(/\\/g, '\\\\');
                return `'${escaped}'`;
              }
              if (value instanceof Date) return `'${value.toISOString()}'`;
              if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
              if (typeof value === 'object') {
                return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
              }
              return String(value);
            });
            return `(${rowValues.join(', ')})`;
          });

          sqlContent += values.join(',\n');
          if (tableData.length > sampleSize) {
            sqlContent += `\n-- ... and ${tableData.length - sampleSize} more records`;
          }
          sqlContent += ';\n\n';

          totalActualRecords += tableData.length;
          tablesProcessed++;
          console.log(`    ✅ ${tableData.length} records`);
        } else {
          sqlContent += `-- No data in table ${tableName}\n\n`;
          console.log(`    ⚠️ No data`);
          tablesProcessed++;
        }

      } catch (error) {
        console.log(`    ❌ Error: ${error.message}`);
        sqlContent += `-- Error processing table ${table.table_name}: ${error.message}\n\n`;
      }
    }

    sqlContent += `-- Backup completed: ${new Date().toISOString()}\n`;
    sqlContent += `-- Total records processed: ${totalActualRecords}\n`;
    sqlContent += `-- Tables processed: ${tablesProcessed}\n`;

    console.log('\n3. Backup summary:');
    console.log(`✅ Tables discovered: ${allTables.length}`);
    console.log(`✅ Tables processed: ${tablesProcessed}`);
    console.log(`✅ Expected total records: ${totalExpectedRecords}`);
    console.log(`✅ Actual records processed: ${totalActualRecords}`);
    console.log(`✅ Records match: ${totalExpectedRecords === totalActualRecords ? 'YES' : 'NO'}`);
    console.log(`✅ Backup content size: ${(sqlContent.length / 1024).toFixed(2)} KB`);

    // Test 3: Show sample of backup content
    console.log('\n4. Sample backup content:');
    const lines = sqlContent.split('\n');
    console.log(lines.slice(0, 20).join('\n') + '...');

    console.log('\n🎯 COMPLETE BACKUP TEST RESULTS:');
    if (totalExpectedRecords === totalActualRecords && tablesProcessed === allTables.length) {
      console.log('✅ SUCCESS: Complete backup includes ALL tables and ALL records!');
      console.log(`✅ Full database backup: ${allTables.length} tables, ${totalActualRecords} records`);
    } else {
      console.log('❌ ISSUE: Backup is incomplete');
      console.log(`Expected: ${allTables.length} tables, ${totalExpectedRecords} records`);
      console.log(`Actual: ${tablesProcessed} tables, ${totalActualRecords} records`);
    }

  } catch (error) {
    console.error('❌ Complete backup test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testCompleteBackup().catch(console.error);
