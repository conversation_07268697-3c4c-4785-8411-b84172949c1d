#!/usr/bin/env node

/**
 * Footer Visibility Test
 * 
 * Tests that the bottom navigation footer is visible at all scroll positions
 */

const puppeteer = require('puppeteer');

async function testFooterVisibility() {
  console.log('🧪 TESTING FOOTER VISIBILITY');
  console.log('='.repeat(40));
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 375, height: 812 }
    });
    
    const page = await browser.newPage();
    
    // Navigate to dashboard
    console.log('📱 Loading dashboard...');
    await page.goto('http://localhost:3001/login', { waitUntil: 'networkidle0' });
    
    // Login
    console.log('🔐 Logging in...');
    await page.waitForSelector('input[type="text"]', { timeout: 10000 });
    await page.type('input[type="text"]', '1441');
    await page.type('input[type="password"]', '1441');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard
    await page.waitForSelector('[data-testid="dashboard-grid"]', { timeout: 10000 });
    
    // Test 1: Footer visible at page load (top)
    console.log('\n🔍 TEST 1: Footer visibility at page load (top)');
    const footerAtTop = await page.evaluate(() => {
      const footer = document.querySelector('nav.fixed.bottom-0');
      if (!footer) return { found: false };
      
      const rect = footer.getBoundingClientRect();
      const styles = getComputedStyle(footer);
      
      return {
        found: true,
        visible: rect.height > 0 && rect.width > 0,
        position: styles.position,
        bottom: styles.bottom,
        zIndex: styles.zIndex,
        display: styles.display,
        opacity: styles.opacity,
        height: rect.height,
        width: rect.width,
        top: rect.top,
        bottom: rect.bottom
      };
    });
    
    console.log('   Footer found:', footerAtTop.found);
    console.log('   Footer visible:', footerAtTop.visible);
    console.log('   Position:', footerAtTop.position);
    console.log('   Z-index:', footerAtTop.zIndex);
    console.log('   Dimensions:', `${footerAtTop.width}x${footerAtTop.height}`);
    
    // Test 2: Footer visible after scrolling down
    console.log('\n🔍 TEST 2: Footer visibility after scrolling down');
    await page.evaluate(() => window.scrollTo(0, 300));
    await page.waitForTimeout(500);
    
    const footerAfterScroll = await page.evaluate(() => {
      const footer = document.querySelector('nav.fixed.bottom-0');
      const rect = footer.getBoundingClientRect();
      return {
        visible: rect.height > 0 && rect.width > 0,
        top: rect.top,
        bottom: rect.bottom,
        inViewport: rect.bottom <= window.innerHeight && rect.top >= 0
      };
    });
    
    console.log('   Footer visible after scroll:', footerAfterScroll.visible);
    console.log('   Footer in viewport:', footerAfterScroll.inViewport);
    
    // Test 3: Footer visible at bottom of page
    console.log('\n🔍 TEST 3: Footer visibility at bottom of page');
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await page.waitForTimeout(500);
    
    const footerAtBottom = await page.evaluate(() => {
      const footer = document.querySelector('nav.fixed.bottom-0');
      const rect = footer.getBoundingClientRect();
      return {
        visible: rect.height > 0 && rect.width > 0,
        inViewport: rect.bottom <= window.innerHeight && rect.top >= 0
      };
    });
    
    console.log('   Footer visible at bottom:', footerAtBottom.visible);
    console.log('   Footer in viewport at bottom:', footerAtBottom.inViewport);
    
    // Test 4: Button clickability
    console.log('\n🔍 TEST 4: Button clickability test');
    const buttonsClickable = await page.evaluate(() => {
      const buttons = document.querySelectorAll('nav.fixed.bottom-0 button');
      return {
        buttonCount: buttons.length,
        allVisible: Array.from(buttons).every(btn => {
          const rect = btn.getBoundingClientRect();
          return rect.height > 0 && rect.width > 0;
        })
      };
    });
    
    console.log('   Button count:', buttonsClickable.buttonCount);
    console.log('   All buttons visible:', buttonsClickable.allVisible);
    
    // Summary
    console.log('\n📊 TEST SUMMARY:');
    console.log('   ✅ Footer found:', footerAtTop.found);
    console.log('   ✅ Visible at top:', footerAtTop.visible);
    console.log('   ✅ Visible after scroll:', footerAfterScroll.visible);
    console.log('   ✅ Visible at bottom:', footerAtBottom.visible);
    console.log('   ✅ Buttons clickable:', buttonsClickable.allVisible);
    console.log('   ✅ Z-index set:', footerAtTop.zIndex !== 'auto');
    
    const allTestsPassed = footerAtTop.found && 
                          footerAtTop.visible && 
                          footerAfterScroll.visible && 
                          footerAtBottom.visible && 
                          buttonsClickable.allVisible;
    
    console.log('\n🎯 OVERALL RESULT:', allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ TESTS FAILED');
    
    if (!allTestsPassed) {
      console.log('\n🚨 CRITICAL ISSUES DETECTED - Footer visibility problems remain');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) await browser.close();
  }
}

testFooterVisibility();
