'use client';

/**
 * Territory Assignment Interface Component
 * 
 * Admin interface for managing territory assignments to congregation members.
 * Provides assignment creation, member management, and territory tracking.
 */

import React, { useState, useEffect } from 'react';
import {
  MemberWithAssignments,
  TerritoryWithAssignment,
  AssignmentRequest,
  ASSIGNMENT_STATUS_LABELS,
  WORKLOAD_STATUS_LABELS,
  WORKLOAD_STATUS_COLORS
} from '@/types/territories/assignment';

interface TerritoryAssignmentInterfaceProps {
  className?: string;
}

interface AssignmentSummary {
  totalMembers: number;
  membersWithAssignments: number;
  totalTerritories: number;
  assignedTerritories: number;
  availableTerritories: number;
  totalActiveAssignments: number;
}

export default function TerritoryAssignmentInterface({ className = '' }: TerritoryAssignmentInterfaceProps) {
  const [members, setMembers] = useState<MemberWithAssignments[]>([]);
  const [territories, setTerritories] = useState<TerritoryWithAssignment[]>([]);
  const [summary, setSummary] = useState<AssignmentSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'assign' | 'members' | 'territories'>('overview');
  const [selectedMember, setSelectedMember] = useState<string>('');
  const [selectedTerritory, setSelectedTerritory] = useState<string>('');
  const [assignmentNotes, setAssignmentNotes] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadAssignmentData();
  }, []);

  const loadAssignmentData = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories/assignments?type=overview', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMembers(data.members || []);
        setTerritories(data.territories || []);
        setSummary(data.summary || null);
      } else {
        throw new Error('Failed to load assignment data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load assignment data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssignTerritory = async () => {
    if (!selectedMember || !selectedTerritory) {
      setError('Please select both a member and a territory');
      return;
    }

    setIsLoading(true);
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories/assignments', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'assign',
          territoryId: selectedTerritory,
          memberId: selectedMember,
          notes: assignmentNotes
        })
      });

      const result = await response.json();

      if (result.success) {
        // Reset form
        setSelectedMember('');
        setSelectedTerritory('');
        setAssignmentNotes('');
        
        // Reload data
        await loadAssignmentData();
        
        console.log('Territory assigned successfully');
      } else {
        setError(result.error || 'Failed to assign territory');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to assign territory');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReturnTerritory = async (assignmentId: string) => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories/assignments', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'return',
          assignmentId
        })
      });

      const result = await response.json();

      if (result.success) {
        // Reload data
        await loadAssignmentData();
        console.log('Territory returned successfully');
      } else {
        setError(result.error || 'Failed to return territory');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to return territory');
    } finally {
      setIsLoading(false);
    }
  };

  const getAvailableMembers = () => {
    return members.filter(member => member.isAvailable);
  };

  const getAvailableTerritories = () => {
    return territories.filter(territory => territory.status === 'available');
  };

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredTerritories = territories.filter(territory =>
    territory.territoryNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    territory.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading && !summary) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Cargando datos de asignaciones...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Asignación de Territorios</h2>
          <p className="text-sm text-gray-600 mt-1">
            Gestiona las asignaciones de territorios a los miembros de la congregación
          </p>
        </div>
        <button
          onClick={loadAssignmentData}
          disabled={isLoading}
          className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Actualizando...' : 'Actualizar'}
        </button>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Miembros con Asignaciones</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.membersWithAssignments}</p>
                <p className="text-xs text-gray-500">de {summary.totalMembers} total</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Territorios Asignados</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.assignedTerritories}</p>
                <p className="text-xs text-gray-500">de {summary.totalTerritories} total</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Territorios Disponibles</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.availableTerritories}</p>
                <p className="text-xs text-gray-500">listos para asignar</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Resumen', icon: '📊' },
            { id: 'assign', name: 'Asignar', icon: '➕' },
            { id: 'members', name: 'Miembros', icon: '👥' },
            { id: 'territories', name: 'Territorios', icon: '🗺️' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Assignments */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Asignaciones Recientes</h3>
              <div className="space-y-3">
                {territories
                  .filter(t => t.currentAssignment)
                  .slice(0, 5)
                  .map((territory) => (
                    <div key={territory.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{territory.territoryNumber}</p>
                        <p className="text-xs text-gray-500">{territory.address}</p>
                        <p className="text-xs text-gray-600">
                          Asignado a: {territory.currentAssignment?.member?.name}
                        </p>
                      </div>
                      <button
                        onClick={() => handleReturnTerritory(territory.currentAssignment!.id)}
                        className="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                      >
                        Devolver
                      </button>
                    </div>
                  ))}
              </div>
            </div>

            {/* Member Workload */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Carga de Trabajo</h3>
              <div className="space-y-3">
                {members
                  .filter(m => m.activeAssignments > 0)
                  .sort((a, b) => b.activeAssignments - a.activeAssignments)
                  .slice(0, 5)
                  .map((member) => (
                    <div key={member.id} className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{member.name}</p>
                        <p className="text-xs text-gray-500">{member.role}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">{member.activeAssignments} territorios</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${WORKLOAD_STATUS_COLORS[member.workloadStatus]}`}>
                          {WORKLOAD_STATUS_LABELS[member.workloadStatus]}
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'assign' && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">Nueva Asignación</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Member Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Seleccionar Miembro
                </label>
                <select
                  value={selectedMember}
                  onChange={(e) => setSelectedMember(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">Seleccionar miembro...</option>
                  {getAvailableMembers().map((member) => (
                    <option key={member.id} value={member.id}>
                      {member.name} ({member.role}) - {member.activeAssignments} asignaciones
                    </option>
                  ))}
                </select>
              </div>

              {/* Territory Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Seleccionar Territorio
                </label>
                <select
                  value={selectedTerritory}
                  onChange={(e) => setSelectedTerritory(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">Seleccionar territorio...</option>
                  {getAvailableTerritories().map((territory) => (
                    <option key={territory.id} value={territory.id}>
                      {territory.territoryNumber} - {territory.address}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Notes */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notas (Opcional)
              </label>
              <textarea
                value={assignmentNotes}
                onChange={(e) => setAssignmentNotes(e.target.value)}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                placeholder="Agregar notas sobre la asignación..."
              />
            </div>

            {/* Assign Button */}
            <div className="mt-6">
              <button
                onClick={handleAssignTerritory}
                disabled={!selectedMember || !selectedTerritory || isLoading}
                className="px-6 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Asignando...' : 'Asignar Territorio'}
              </button>
            </div>
          </div>
        )}

        {activeTab === 'members' && (
          <div className="bg-white rounded-lg border border-gray-200">
            {/* Search */}
            <div className="p-4 border-b border-gray-200">
              <input
                type="text"
                placeholder="Buscar miembros..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              />
            </div>

            {/* Members List */}
            <div className="divide-y divide-gray-200">
              {filteredMembers.map((member) => (
                <div key={member.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{member.name}</h4>
                      <p className="text-sm text-gray-500">{member.role}</p>
                      {member.email && (
                        <p className="text-xs text-gray-400">{member.email}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-gray-600">{member.activeAssignments} activos</p>
                        <p className="text-xs text-gray-500">{member.totalAssignments} total</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${WORKLOAD_STATUS_COLORS[member.workloadStatus]}`}>
                        {WORKLOAD_STATUS_LABELS[member.workloadStatus]}
                      </span>
                      {member.isAvailable ? (
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                          Disponible
                        </span>
                      ) : (
                        <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium">
                          No Disponible
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Active Assignments */}
                  {member.assignments && member.assignments.length > 0 && (
                    <div className="mt-3 pl-4 border-l-2 border-gray-200">
                      <p className="text-xs font-medium text-gray-700 mb-2">Asignaciones Activas:</p>
                      <div className="space-y-1">
                        {member.assignments.map((assignment) => (
                          <div key={assignment.id} className="flex items-center justify-between">
                            <span className="text-xs text-gray-600">
                              {assignment.territory?.territoryNumber} - {assignment.territory?.address}
                            </span>
                            <button
                              onClick={() => handleReturnTerritory(assignment.id)}
                              className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                            >
                              Devolver
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'territories' && (
          <div className="bg-white rounded-lg border border-gray-200">
            {/* Search */}
            <div className="p-4 border-b border-gray-200">
              <input
                type="text"
                placeholder="Buscar territorios..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              />
            </div>

            {/* Territories List */}
            <div className="divide-y divide-gray-200">
              {filteredTerritories.map((territory) => (
                <div key={territory.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{territory.territoryNumber}</h4>
                      <p className="text-sm text-gray-500">{territory.address}</p>
                      {territory.notes && (
                        <p className="text-xs text-gray-400 mt-1">{territory.notes}</p>
                      )}
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-gray-600">{territory.totalAssignments} asignaciones</p>
                        {territory.averageAssignmentDuration && (
                          <p className="text-xs text-gray-500">
                            Promedio: {Math.round(territory.averageAssignmentDuration)} días
                          </p>
                        )}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        territory.status === 'available' ? 'bg-green-100 text-green-800' :
                        territory.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                        territory.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {territory.status === 'available' ? 'Disponible' :
                         territory.status === 'assigned' ? 'Asignado' :
                         territory.status === 'completed' ? 'Completado' :
                         'Fuera de Servicio'}
                      </span>
                    </div>
                  </div>

                  {/* Current Assignment */}
                  {territory.currentAssignment && (
                    <div className="mt-3 pl-4 border-l-2 border-blue-200">
                      <p className="text-xs font-medium text-gray-700 mb-1">Asignación Actual:</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600">
                          {territory.currentAssignment.member?.name} - 
                          Asignado el {new Date(territory.currentAssignment.assignedAt).toLocaleDateString()}
                        </span>
                        <button
                          onClick={() => handleReturnTerritory(territory.currentAssignment!.id)}
                          className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                        >
                          Devolver
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
