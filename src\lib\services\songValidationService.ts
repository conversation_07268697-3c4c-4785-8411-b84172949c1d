/**
 * Song Validation Service
 * 
 * Validates song numbers extracted from workbook data against the songs database.
 * Provides detailed reporting of missing songs without fallback handling.
 */

import { prisma } from '@/lib/prisma';

export interface SongValidationResult {
  isValid: boolean;
  validSongs: ValidatedSong[];
  missingSongs: MissingSong[];
  errors: string[];
  warnings: string[];
  totalSongs: number;
  validCount: number;
  missingCount: number;
}

export interface ValidatedSong {
  songNumber: number;
  titleEs?: string;
  titleEn?: string;
  category?: string;
  isActive: boolean;
}

export interface MissingSong {
  songNumber: number;
  context: string; // Where the song was found (meeting date, part)
  needsManualAddition: boolean;
}

export interface SongExtractionResult {
  extractedSongs: ExtractedSong[];
  extractionErrors: string[];
  extractionWarnings: string[];
}

export interface ExtractedSong {
  songNumber: number;
  context: string;
  meetingDate: string;
  partTitle?: string;
}

export class SongValidationService {
  /**
   * Extract song numbers from workbook meeting data
   */
  static extractSongsFromMeetings(meetings: any[]): SongExtractionResult {
    const result: SongExtractionResult = {
      extractedSongs: [],
      extractionErrors: [],
      extractionWarnings: [],
    };

    for (const meeting of meetings) {
      try {
        // Extract songs from meeting data
        if (meeting.songs && Array.isArray(meeting.songs)) {
          for (const songNumber of meeting.songs) {
            if (Number.isInteger(songNumber) && songNumber > 0) {
              result.extractedSongs.push({
                songNumber,
                context: `Meeting songs`,
                meetingDate: meeting.date,
              });
            } else {
              result.extractionWarnings.push(
                `Invalid song number ${songNumber} in meeting ${meeting.date}`
              );
            }
          }
        }

        // Extract songs from meeting parts
        if (meeting.parts && Array.isArray(meeting.parts)) {
          for (const part of meeting.parts) {
            const songNumbers = this.extractSongNumbersFromText(part.title || '');
            for (const songNumber of songNumbers) {
              result.extractedSongs.push({
                songNumber,
                context: `Part: ${part.title}`,
                meetingDate: meeting.date,
                partTitle: part.title,
              });
            }

            // Also check notes and study points
            if (part.notes) {
              const noteSongs = this.extractSongNumbersFromText(part.notes);
              for (const songNumber of noteSongs) {
                result.extractedSongs.push({
                  songNumber,
                  context: `Part notes: ${part.title}`,
                  meetingDate: meeting.date,
                  partTitle: part.title,
                });
              }
            }

            if (part.studyPoints && Array.isArray(part.studyPoints)) {
              for (const point of part.studyPoints) {
                const pointSongs = this.extractSongNumbersFromText(point);
                for (const songNumber of pointSongs) {
                  result.extractedSongs.push({
                    songNumber,
                    context: `Study point: ${part.title}`,
                    meetingDate: meeting.date,
                    partTitle: part.title,
                  });
                }
              }
            }
          }
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        result.extractionErrors.push(
          `Failed to extract songs from meeting ${meeting.date}: ${errorMessage}`
        );
      }
    }

    // Remove duplicates
    const uniqueSongs = new Map<number, ExtractedSong>();
    for (const song of result.extractedSongs) {
      if (!uniqueSongs.has(song.songNumber)) {
        uniqueSongs.set(song.songNumber, song);
      }
    }
    result.extractedSongs = Array.from(uniqueSongs.values());

    return result;
  }

  /**
   * Extract song numbers from text using regex patterns
   */
  private static extractSongNumbersFromText(text: string): number[] {
    const songNumbers: number[] = [];
    
    // Common patterns for song references
    const patterns = [
      /(?:canción|song|cántico)\s*(\d+)/gi,
      /(?:himno|hymn)\s*(\d+)/gi,
      /(?:núm\.|num\.|no\.|#)\s*(\d+)/gi,
      /\b(\d{1,3})\s*(?:canción|song|cántico|himno|hymn)\b/gi,
    ];

    for (const pattern of patterns) {
      const matches = text.matchAll(pattern);
      for (const match of matches) {
        const songNumber = parseInt(match[1], 10);
        if (!isNaN(songNumber) && songNumber > 0 && songNumber <= 999) {
          if (!songNumbers.includes(songNumber)) {
            songNumbers.push(songNumber);
          }
        }
      }
    }

    return songNumbers;
  }

  /**
   * Validate extracted songs against the database
   */
  static async validateSongs(extractedSongs: ExtractedSong[]): Promise<SongValidationResult> {
    const result: SongValidationResult = {
      isValid: true,
      validSongs: [],
      missingSongs: [],
      errors: [],
      warnings: [],
      totalSongs: extractedSongs.length,
      validCount: 0,
      missingCount: 0,
    };

    try {
      // Get unique song numbers
      const songNumbers = [...new Set(extractedSongs.map(s => s.songNumber))];
      
      if (songNumbers.length === 0) {
        result.warnings.push('No songs found to validate');
        return result;
      }

      // Fetch songs from database
      const dbSongs = await prisma.song.findMany({
        where: {
          songNumber: {
            in: songNumbers,
          },
        },
        select: {
          songNumber: true,
          titleEs: true,
          titleEn: true,
          category: true,
          isActive: true,
        },
      });

      // Create map for quick lookup
      const dbSongMap = new Map<number, ValidatedSong>();
      for (const song of dbSongs) {
        dbSongMap.set(song.songNumber, song);
      }

      // Validate each extracted song
      for (const extractedSong of extractedSongs) {
        const dbSong = dbSongMap.get(extractedSong.songNumber);
        
        if (dbSong) {
          result.validSongs.push(dbSong);
          result.validCount++;
          
          // Check if song is inactive
          if (!dbSong.isActive) {
            result.warnings.push(
              `Song ${extractedSong.songNumber} is marked as inactive in database (${extractedSong.context})`
            );
          }
        } else {
          result.missingSongs.push({
            songNumber: extractedSong.songNumber,
            context: extractedSong.context,
            needsManualAddition: true,
          });
          result.missingCount++;
          result.isValid = false;
        }
      }

      // Generate summary warnings
      if (result.missingCount > 0) {
        result.warnings.push(
          `${result.missingCount} songs are missing from the database and need to be added manually`
        );
      }

      if (result.validCount === 0 && result.totalSongs > 0) {
        result.errors.push('No valid songs found in database');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(`Database validation failed: ${errorMessage}`);
      result.isValid = false;
    }

    return result;
  }

  /**
   * Generate detailed missing songs report
   */
  static generateMissingSongsReport(validationResult: SongValidationResult): string {
    if (validationResult.missingSongs.length === 0) {
      return 'All songs are available in the database.';
    }

    let report = `Missing Songs Report\n`;
    report += `===================\n\n`;
    report += `Total missing songs: ${validationResult.missingCount}\n`;
    report += `Total valid songs: ${validationResult.validCount}\n\n`;

    report += `Songs that need to be added manually:\n`;
    report += `------------------------------------\n`;

    for (const missingSong of validationResult.missingSongs) {
      report += `• Song ${missingSong.songNumber}\n`;
      report += `  Context: ${missingSong.context}\n`;
      report += `  Action: Add to songs database manually\n\n`;
    }

    report += `Instructions:\n`;
    report += `1. Add missing songs to the songs database\n`;
    report += `2. Include both Spanish (titleEs) and English (titleEn) titles\n`;
    report += `3. Set appropriate category if known\n`;
    report += `4. Ensure isActive is set to true\n`;
    report += `5. Re-run the import after adding missing songs\n`;

    return report;
  }

  /**
   * Validate song numbers for reasonable ranges
   */
  static validateSongNumberRanges(songNumbers: number[]): { valid: number[]; invalid: number[] } {
    const valid: number[] = [];
    const invalid: number[] = [];

    for (const songNumber of songNumbers) {
      if (Number.isInteger(songNumber) && songNumber >= 1 && songNumber <= 999) {
        valid.push(songNumber);
      } else {
        invalid.push(songNumber);
      }
    }

    return { valid, invalid };
  }

  /**
   * Get song statistics from database
   */
  static async getSongStatistics(): Promise<{
    totalSongs: number;
    activeSongs: number;
    inactiveSongs: number;
    songsWithSpanishTitles: number;
    songsWithEnglishTitles: number;
    categories: { category: string; count: number }[];
  }> {
    try {
      const [
        totalSongs,
        activeSongs,
        inactiveSongs,
        songsWithSpanishTitles,
        songsWithEnglishTitles,
        categories,
      ] = await Promise.all([
        prisma.song.count(),
        prisma.song.count({ where: { isActive: true } }),
        prisma.song.count({ where: { isActive: false } }),
        prisma.song.count({ where: { titleEs: { not: null } } }),
        prisma.song.count({ where: { titleEn: { not: null } } }),
        prisma.song.groupBy({
          by: ['category'],
          _count: { category: true },
          where: { category: { not: null } },
        }),
      ]);

      return {
        totalSongs,
        activeSongs,
        inactiveSongs,
        songsWithSpanishTitles,
        songsWithEnglishTitles,
        categories: categories.map(c => ({
          category: c.category || 'Unknown',
          count: c._count.category,
        })),
      };
    } catch (error) {
      console.error('Failed to get song statistics:', error);
      return {
        totalSongs: 0,
        activeSongs: 0,
        inactiveSongs: 0,
        songsWithSpanishTitles: 0,
        songsWithEnglishTitles: 0,
        categories: [],
      };
    }
  }
}
