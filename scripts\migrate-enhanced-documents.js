#!/usr/bin/env node

/**
 * Migration script for enhanced document management
 * This script ensures the database has all the necessary fields for the enhanced document system
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateEnhancedDocuments() {
  console.log('🚀 Starting enhanced document migration...');

  try {
    // Check if we need to migrate existing letters
    const existingLetters = await prisma.letter.findMany({
      where: {
        OR: [
          { visibility: null },
          { priority: null },
          { status: null },
        ]
      }
    });

    if (existingLetters.length > 0) {
      console.log(`📝 Found ${existingLetters.length} letters that need migration...`);

      // Update existing letters with default values
      for (const letter of existingLetters) {
        await prisma.letter.update({
          where: { id: letter.id },
          data: {
            visibility: letter.visibility || 'ALL_MEMBERS',
            priority: letter.priority || 'NORMAL',
            status: letter.status || 'ACTIVE',
            version: letter.version || 1,
            downloadCount: letter.downloadCount || 0,
            viewCount: letter.viewCount || 0,
            isActive: letter.isActive !== false,
          }
        });
      }

      console.log(`✅ Migrated ${existingLetters.length} existing letters`);
    }

    // Create default document folders for each congregation
    const congregations = await prisma.congregation.findMany();
    
    for (const congregation of congregations) {
      // Check if default folders already exist
      const existingFolders = await prisma.documentFolder.findMany({
        where: { congregationId: congregation.id }
      });

      if (existingFolders.length === 0) {
        console.log(`📁 Creating default folders for congregation ${congregation.id}...`);

        const defaultFolders = [
          {
            name: 'Letters from Branch',
            description: 'Official letters and communications from the branch office',
            color: '#3B82F6',
            icon: '✉️',
          },
          {
            name: 'Forms',
            description: 'Service reports, applications, and other forms',
            color: '#10B981',
            icon: '📋',
          },
          {
            name: 'Announcements',
            description: 'Congregation announcements and notices',
            color: '#F59E0B',
            icon: '📢',
          },
          {
            name: 'Guidelines',
            description: 'Guidelines and instructions for various activities',
            color: '#8B5CF6',
            icon: '📖',
          },
          {
            name: 'Reports',
            description: 'Monthly reports and statistics',
            color: '#EF4444',
            icon: '📊',
          },
        ];

        for (const folder of defaultFolders) {
          await prisma.documentFolder.create({
            data: {
              ...folder,
              congregationId: congregation.id,
            }
          });
        }

        console.log(`✅ Created ${defaultFolders.length} default folders for congregation ${congregation.id}`);
      }
    }

    // Update existing letters to categorize them
    const uncategorizedLetters = await prisma.letter.findMany({
      where: {
        category: null,
      }
    });

    if (uncategorizedLetters.length > 0) {
      console.log(`🏷️ Categorizing ${uncategorizedLetters.length} uncategorized letters...`);

      for (const letter of uncategorizedLetters) {
        let category = 'letters';
        let subcategory = null;

        // Try to categorize based on title
        const title = letter.title.toLowerCase();
        
        if (title.includes('form') || title.includes('report') || title.includes('application')) {
          category = 'forms';
        } else if (title.includes('announcement') || title.includes('notice')) {
          category = 'announcements';
        } else if (title.includes('guideline') || title.includes('instruction')) {
          category = 'guidelines';
        } else if (title.includes('monthly') || title.includes('statistics')) {
          category = 'reports';
        }

        await prisma.letter.update({
          where: { id: letter.id },
          data: {
            category,
            subcategory,
          }
        });
      }

      console.log(`✅ Categorized ${uncategorizedLetters.length} letters`);
    }

    // Add search indexes if they don't exist
    console.log('🔍 Ensuring search indexes are in place...');
    
    // The fulltext index should already be created by Prisma migrations
    // This is just a verification step
    const indexCheck = await prisma.$queryRaw`
      SELECT indexname 
      FROM pg_indexes 
      WHERE tablename = 'letters' 
      AND indexname LIKE '%fulltext%'
    `;

    if (indexCheck.length === 0) {
      console.log('⚠️ Fulltext search index not found. Please run: npx prisma db push');
    } else {
      console.log('✅ Search indexes are in place');
    }

    console.log('🎉 Enhanced document migration completed successfully!');

    // Print summary
    const totalDocuments = await prisma.letter.count();
    const totalFolders = await prisma.documentFolder.count();
    
    console.log('\n📊 Migration Summary:');
    console.log(`   • Total documents: ${totalDocuments}`);
    console.log(`   • Total folders: ${totalFolders}`);
    console.log(`   • Congregations: ${congregations.length}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  migrateEnhancedDocuments()
    .then(() => {
      console.log('✅ Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateEnhancedDocuments };
