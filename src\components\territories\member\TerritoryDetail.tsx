'use client';

/**
 * Territory Detail Component
 *
 * Member interface for viewing territory details with integrated map and address management.
 * Follows Field Service UI patterns with mobile-optimized design.
 */

import React, { useState, useEffect } from 'react';
import { Territory } from '@/types/territories/territory';
import SimpleTerritoryMap from '@/components/territories/shared/SimpleTerritoryMap';
import TerritoryAddressTable from '@/components/territories/admin/TerritoryAddressTable';
import type { Territory as MapTerritory } from '@/types/territories/map';

interface TerritoryDetailProps {
  territoryId: string;
  onBack?: () => void;
  className?: string;
}

export default function TerritoryDetail({
  territoryId,
  onBack,
  className = ''
}: TerritoryDetailProps) {
  const [territory, setTerritory] = useState<Territory | null>(null);
  const [mapTerritory, setMapTerritory] = useState<MapTerritory | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showMap, setShowMap] = useState(true);
  const [userRole, setUserRole] = useState('publisher');

  // Debug effect to track state changes
  useEffect(() => {
    console.log('🔍 TerritoryDetail State Update:', {
      showMap,
      territory: territory ? `${territory.territoryNumber} (${territory.id})` : 'NULL',
      mapTerritory: mapTerritory ? `${mapTerritory.territoryNumber} (has boundary: ${!!mapTerritory.boundary})` : 'NULL',
      shouldShowMap: showMap && mapTerritory && mapTerritory.boundary
    });
  }, [showMap, territory, mapTerritory]);

  // Fetch territory details
  useEffect(() => {
    const fetchTerritoryDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get user info for role
        const token = localStorage.getItem('hermanos_token');
        if (token) {
          try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            setUserRole(payload.role || 'publisher');
          } catch (e) {
            console.warn('Could not parse token for role');
          }
        }

        // Fetch territory data
        const response = await fetch(`/api/territories/${territoryId}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('hermanos_token')}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch territory: ${response.statusText}`);
        }

        const territoryData = await response.json();
        console.log('🗺️ TerritoryDetail - Raw API response:', territoryData);
        console.log('🔍 TerritoryDetail - Boundary data from API:', territoryData.boundary);
        console.log('🔍 TerritoryDetail - Coordinates from API:', territoryData.coordinates);

        // Enhanced debugging for boundary data
        if (territoryData.boundary) {
          console.log('✅ TerritoryDetail - Boundary exists:', {
            type: territoryData.boundary.type,
            hasCoordinates: !!territoryData.boundary.coordinates,
            coordinatesLength: territoryData.boundary.coordinates?.[0]?.length,
            firstPoint: territoryData.boundary.coordinates?.[0]?.[0],
            isValidGeoJSON: territoryData.boundary.type === 'Polygon' && Array.isArray(territoryData.boundary.coordinates)
          });
        } else {
          console.log('❌ TerritoryDetail - No boundary data in API response');
        }

        setTerritory(territoryData);

        // Convert to map territory format for the map component
        // If no boundary data, enhance the territory on-the-fly
        let coordinates = territoryData.coordinates;
        let boundary = territoryData.boundary;

        // Only use real boundary data from database - no mock generation
        if (territoryData.boundaries) {
          console.log('✅ TerritoryDetail - Using real boundary data from database');
          boundary = territoryData.boundaries as any;

          // Extract coordinates from boundary if available
          if (boundary && boundary.coordinates && boundary.coordinates[0]) {
            const coords = boundary.coordinates[0];
            const lats = coords.map((c: number[]) => c[1]);
            const lngs = coords.map((c: number[]) => c[0]);

            coordinates = {
              latitude: lats.reduce((a: number, b: number) => a + b, 0) / lats.length,
              longitude: lngs.reduce((a: number, b: number) => a + b, 0) / lngs.length
            };
          }
        } else {
          console.log('⚠️ TerritoryDetail - No real boundary data available for this territory');
          // Use default Miami coordinates for map centering
          coordinates = { latitude: 25.7617, longitude: -80.1918 };
          boundary = null;
        }

        const mapTerritoryData: MapTerritory = {
          id: territoryData.id,
          territoryNumber: territoryData.territoryNumber,
          address: territoryData.address,
          status: territoryData.status,
          coordinates,
          boundary
        };

        console.log('🗺️ TerritoryDetail - Final map territory data:', mapTerritoryData);
        console.log('🔍 TerritoryDetail - Final boundary for map:', mapTerritoryData.boundary);

        setMapTerritory(mapTerritoryData);

      } catch (err) {
        console.error('Error fetching territory details:', err);
        setError(err instanceof Error ? err.message : 'Failed to load territory');
      } finally {
        setIsLoading(false);
      }
    };

    if (territoryId) {
      fetchTerritoryDetails();
    }
  }, [territoryId]);

  const handleRefresh = () => {
    // Refresh territory data
    window.location.reload();
  };

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <div className="text-sm text-gray-600">Cargando territorio...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !territory) {
    return (
      <div className={`min-h-screen bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-600 mb-2">❌</div>
            <div className="text-sm text-gray-600">{error || 'Territorio no encontrado'}</div>
            {onBack && (
              <button
                onClick={onBack}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Volver
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {onBack && (
                <button
                  onClick={onBack}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  Territorio {territory.territoryNumber}
                </h1>
                <p className="text-sm text-gray-500">
                  {territory.address.split('\n').length} direcciones
                </p>
              </div>
            </div>

            {/* Map Toggle */}
            <button
              onClick={() => {
                console.log('🗺️ Map toggle clicked. Current showMap:', showMap, 'New showMap:', !showMap);
                setShowMap(!showMap);
              }}
              className={`p-2 rounded-lg transition-colors ${
                showMap
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title={showMap ? 'Ocultar mapa' : 'Mostrar mapa'}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Map Section */}
      {showMap && mapTerritory && (
        <div className="bg-white border-b border-gray-200">
          <SimpleTerritoryMap
            territories={[mapTerritory]}
            height="300px"
            className="w-full"
          />
        </div>
      )}

      {/* Address List */}
      <div className="flex-1">
        <TerritoryAddressTable
          territories={[territory]}
          userRole={userRole}
          onRefresh={handleRefresh}
        />
      </div>
    </div>
  );
}
