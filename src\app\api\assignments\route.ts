/**
 * Assignment Coordination API Endpoint
 *
 * Handles meeting part assignments, member assignment summaries,
 * and assignment coordination for congregation management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { AssignmentCoordinationService, AssignmentInput } from '@/lib/services/assignmentCoordinationService';

// Validation schema for assignment creation
const AssignmentSchema = z.object({
  meetingId: z.string().min(1, 'Meeting ID is required'),
  partId: z.string().min(1, 'Part ID is required'),
  assignedMemberId: z.string().optional(),
  assistantId: z.string().optional(),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional(),
});

// Validation schema for GET requests
const GetAssignmentsSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  meetingType: z.enum(['midweek', 'weekend']).optional(),
  memberId: z.string().optional(),
  includeUnassigned: z.string().transform(val => val === 'true').optional(),
  limit: z.string().transform(val => parseInt(val, 10)).optional(),
});

/**
 * GET /api/assignments
 * Retrieve meeting part assignments with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    const validationResult = GetAssignmentsSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { startDate, endDate, meetingType, memberId, includeUnassigned, limit } = validationResult.data;

    // If memberId is specified and it's not the current user, check permissions
    if (memberId && memberId !== member.id) {
      if (!['elder', 'ministerial_servant'].includes(member.role)) {
        return NextResponse.json(
          { error: 'Insufficient permissions to view other members\' assignments' },
          { status: 403 }
        );
      }
    }

    // Get assignments
    const assignments = await AssignmentCoordinationService.getUpcomingAssignments(
      member.congregationId,
      {
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        meetingType,
        memberId: memberId || (member.role === 'publisher' ? member.id : undefined),
        includeUnassigned: includeUnassigned ?? true,
        limit: limit || 100,
      }
    );

    return NextResponse.json({
      success: true,
      assignments,
      count: assignments.length,
    });

  } catch (error) {
    console.error('Assignments GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve assignments',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/assignments
 * Create or update a meeting part assignment
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can assign meeting parts
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to assign meeting parts' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = AssignmentSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const assignmentData: AssignmentInput = validationResult.data;

    // Create the assignment
    const assignment = await AssignmentCoordinationService.assignMeetingPart(
      member.congregationId,
      assignmentData
    );

    return NextResponse.json({
      success: true,
      assignment,
      message: 'Meeting part assigned successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Assignments POST error:', error);

    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      if (error.message.includes('not active')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        error: 'Failed to assign meeting part',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/assignments
 * Remove assignment from a meeting part
 */
export async function DELETE(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can remove assignments
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to remove assignments' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { partId } = body;

    if (!partId) {
      return NextResponse.json(
        { error: 'Part ID is required' },
        { status: 400 }
      );
    }

    // Remove the assignment
    const assignment = await AssignmentCoordinationService.removeAssignment(
      member.congregationId,
      partId
    );

    return NextResponse.json({
      success: true,
      assignment,
      message: 'Assignment removed successfully',
    });

  } catch (error) {
    console.error('Assignments DELETE error:', error);

    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      {
        error: 'Failed to remove assignment',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create assignments and DELETE to remove them.' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to update assignments.' },
    { status: 405 }
  );
}
