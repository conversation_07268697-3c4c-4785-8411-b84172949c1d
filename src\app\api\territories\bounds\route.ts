/**
 * Territory Bounds API Endpoint
 *
 * Provides congregation territory boundaries for map display.
 * Calculates optimal map bounds based on territory coordinates.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import type { MapBounds } from '@/types/territories/map';

// Validation schema for query parameters
const BoundsQuerySchema = z.object({
  status: z.enum(['available', 'assigned', 'completed', 'out_of_service']).nullable().optional(),
  padding: z.number().min(0).max(1).optional().default(0.1),
  congregationId: z.string().nullable().optional()
});

/**
 * GET /api/territories/bounds - Get congregation territory boundaries
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Get member details for permission checking
    const member = await prisma.member.findFirst({
      where: {
        congregationId: user.congregationId,
        pin: user.pin
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for territory bounds' },
        { status: 403 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      status: searchParams.get('status'),
      padding: parseFloat(searchParams.get('padding') || '0.1'),
      congregationId: searchParams.get('congregationId') || user.congregationId
    };

    const validatedParams = BoundsQuerySchema.parse(queryParams);

    // Build where clause for territory filtering
    const whereClause: any = {
      congregationId: validatedParams.congregationId
    };

    if (validatedParams.status) {
      whereClause.status = validatedParams.status;
    }

    // Fetch territories with coordinates
    const territories = await prisma.territory.findMany({
      where: whereClause,
      select: {
        id: true,
        territoryNumber: true,
        boundaries: true,
        status: true
      }
    });

    // Extract coordinates from territories
    const coordinates: Array<{ latitude: number; longitude: number }> = [];
    
    territories.forEach(territory => {
      if (territory.boundaries && typeof territory.boundaries === 'object') {
        const boundaries = territory.boundaries as any;
        if (boundaries.coordinates && Array.isArray(boundaries.coordinates)) {
          coordinates.push({
            latitude: boundaries.coordinates[1],
            longitude: boundaries.coordinates[0]
          });
        }
      }
    });

    if (coordinates.length === 0) {
      return NextResponse.json({
        success: true,
        bounds: null,
        message: 'No territories with coordinates found',
        statistics: {
          totalTerritories: territories.length,
          territoriesWithCoordinates: 0,
          geocodingPercentage: 0
        }
      });
    }

    // Calculate bounds
    const latitudes = coordinates.map(coord => coord.latitude);
    const longitudes = coordinates.map(coord => coord.longitude);

    const bounds: MapBounds = {
      north: Math.max(...latitudes),
      south: Math.min(...latitudes),
      east: Math.max(...longitudes),
      west: Math.min(...longitudes)
    };

    // Add padding to bounds
    const latSpan = bounds.north - bounds.south;
    const lngSpan = bounds.east - bounds.west;
    const latPadding = latSpan * validatedParams.padding;
    const lngPadding = lngSpan * validatedParams.padding;

    const paddedBounds: MapBounds = {
      north: bounds.north + latPadding,
      south: bounds.south - latPadding,
      east: bounds.east + lngPadding,
      west: bounds.west - lngPadding
    };

    // Calculate center point
    const center = {
      latitude: (paddedBounds.north + paddedBounds.south) / 2,
      longitude: (paddedBounds.east + paddedBounds.west) / 2
    };

    // Calculate optimal zoom level based on bounds size
    const maxSpan = Math.max(latSpan, lngSpan);
    let optimalZoom = 12;
    
    if (maxSpan > 0.1) optimalZoom = 10;
    else if (maxSpan > 0.05) optimalZoom = 12;
    else if (maxSpan > 0.02) optimalZoom = 14;
    else optimalZoom = 16;

    // Group territories by status for statistics
    const statusCounts = territories.reduce((counts, territory) => {
      const status = territory.status;
      counts[status] = (counts[status] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    const response = {
      success: true,
      bounds: paddedBounds,
      originalBounds: bounds,
      center,
      optimalZoom,
      statistics: {
        totalTerritories: territories.length,
        territoriesWithCoordinates: coordinates.length,
        geocodingPercentage: (coordinates.length / territories.length) * 100,
        statusDistribution: statusCounts,
        boundingBoxArea: latSpan * lngSpan,
        padding: validatedParams.padding
      },
      metadata: {
        status: validatedParams.status,
        congregationId: validatedParams.congregationId,
        calculatedAt: new Date().toISOString()
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Territory bounds GET error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to calculate territory bounds',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/territories/bounds - Recalculate and cache territory bounds
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Get member details for permission checking
    const member = await prisma.member.findFirst({
      where: {
        congregationId: user.congregationId,
        pin: user.pin
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check if user has elder permissions for bounds recalculation
    const hasElderAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(member.role);

    if (!hasElderAccess) {
      return NextResponse.json(
        { error: 'Elder access required for bounds recalculation' },
        { status: 403 }
      );
    }

    // This endpoint could be used to trigger bounds recalculation
    // For now, we'll just return the current bounds
    const boundsResponse = await GET(request);
    
    return NextResponse.json({
      success: true,
      message: 'Territory bounds recalculated successfully',
      data: await boundsResponse.json()
    });

  } catch (error) {
    console.error('Territory bounds POST error:', error);

    return NextResponse.json(
      {
        error: 'Failed to recalculate territory bounds',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve bounds or POST to recalculate.' },
    { status: 405 }
  );
}
