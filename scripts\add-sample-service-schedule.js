/**
 * <PERSON><PERSON><PERSON> to add sample service schedule data for testing
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addSampleServiceSchedule() {
  try {
    console.log('Adding sample service schedule data...');

    // Get the congregation (assuming Coral Oeste with ID 1441)
    const congregation = await prisma.congregation.findFirst({
      where: { id: '1441' }
    });

    if (!congregation) {
      console.error('Congregation not found. Please ensure congregation 1441 exists.');
      return;
    }

    // Get some members to assign as conductors
    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        role: { in: ['elder', 'ministerial_servant'] },
        isActive: true
      },
      take: 3
    });

    if (members.length === 0) {
      console.error('No elders or ministerial servants found to assign as conductors.');
      return;
    }

    // Create a weekly schedule for this week
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Saturday

    console.log(`Creating schedule for week: ${startOfWeek.toISOString().split('T')[0]} to ${endOfWeek.toISOString().split('T')[0]}`);

    // Create or update the weekly schedule
    const schedule = await prisma.serviceSchedule.upsert({
      where: {
        congregationId_weekStartDate: {
          congregationId: '1441',
          weekStartDate: startOfWeek
        }
      },
      update: {
        weekEndDate: endOfWeek,
        updatedAt: new Date()
      },
      create: {
        congregationId: '1441',
        weekStartDate: startOfWeek,
        weekEndDate: endOfWeek
      }
    });

    console.log('Weekly schedule created:', schedule.id);

    // Add sample service times
    const serviceTimes = [
      {
        serviceDate: new Date(startOfWeek.getTime() + 4 * 24 * 60 * 60 * 1000), // Thursday
        serviceTime: '18:15',
        location: 'Salón del Reino',
        address: '7790 West 4th Av Hialeah Fl 33014',
        conductorId: members[0]?.id
      },
      {
        serviceDate: new Date(startOfWeek.getTime() + 4 * 24 * 60 * 60 * 1000), // Thursday
        serviceTime: '19:30',
        location: 'Salón del Reino',
        address: '7790 West 4th Av Hialeah Fl 33014',
        conductorId: members[1]?.id
      },
      {
        serviceDate: new Date(startOfWeek.getTime() + 5 * 24 * 60 * 60 * 1000), // Friday
        serviceTime: '18:30',
        location: 'Zoom',
        address: 'ID: 123-456-789 Contraseña: servicio123',
        conductorId: members[2]?.id
      },
      {
        serviceDate: new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000), // Saturday
        serviceTime: '09:30',
        location: 'Salón del Reino',
        address: '7790 West 4th Av Hialeah Fl 33014',
        conductorId: members[0]?.id
      },
      {
        serviceDate: new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000), // Saturday
        serviceTime: '14:00',
        location: 'Territorio Especial',
        address: 'Centro Comercial Westland Mall',
        conductorId: members[1]?.id
      }
    ];

    for (const timeData of serviceTimes) {
      const serviceTime = await prisma.serviceScheduleTime.create({
        data: {
          scheduleId: schedule.id,
          ...timeData
        },
        include: {
          conductor: {
            select: {
              id: true,
              name: true,
              role: true
            }
          }
        }
      });

      console.log(`Service time created: ${timeData.serviceDate.toISOString().split('T')[0]} at ${timeData.serviceTime} - ${timeData.location} (Conductor: ${serviceTime.conductor?.name || 'Sin asignar'})`);
    }

    console.log('\nSample service schedule data added successfully!');
    console.log('You can now test the field service page with real data.');

  } catch (error) {
    console.error('Error adding sample service schedule:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addSampleServiceSchedule();
