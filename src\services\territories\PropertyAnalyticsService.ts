/**
 * Property Analytics Service
 *
 * Provides comprehensive analytics for territory properties including
 * house counts, apartment analysis, and property type distribution.
 */

import { prisma } from '@/lib/prisma';

export interface PropertyAnalytics {
  territoryId: string;
  territoryNumber: string;
  totalProperties: number;
  houses: number;
  apartments: number;
  buildings: number;
  propertyTypes: {
    [type: string]: number;
  };
  lastUpdated: Date;
}

export interface PropertySummary {
  totalProperties: number;
  totalHouses: number;
  totalApartments: number;
  totalBuildings: number;
  averagePropertiesPerTerritory: number;
  territoryCount: number;
}

export interface PropertyDistribution {
  territoryNumber: string;
  houses: number;
  apartments: number;
  buildings: number;
  total: number;
  percentage: number;
}

export class PropertyAnalyticsService {
  /**
   * Analyze property types from address strings
   */
  private static analyzePropertyTypes(addresses: string[]): {
    houses: number;
    apartments: number;
    buildings: number;
    propertyTypes: { [type: string]: number };
  } {
    let houses = 0;
    let apartments = 0;
    let buildings = 0;
    const propertyTypes: { [type: string]: number } = {};

    addresses.forEach(address => {
      const lowerAddress = address.toLowerCase();

      // Count apartments (look for "apt", "unit", "#", building indicators)
      if (lowerAddress.includes('apt') ||
          lowerAddress.includes('unit') ||
          lowerAddress.includes('#') ||
          lowerAddress.includes('apartamento') ||
          lowerAddress.includes('apto') ||
          /\d+[a-z]/.test(lowerAddress)) { // Pattern like "123a"
        apartments++;
        propertyTypes['apartment'] = (propertyTypes['apartment'] || 0) + 1;
      }
      // Count buildings (look for building indicators)
      else if (lowerAddress.includes('building') ||
               lowerAddress.includes('bldg') ||
               lowerAddress.includes('edificio') ||
               lowerAddress.includes('edif') ||
               lowerAddress.includes('complex') ||
               lowerAddress.includes('complejo') ||
               lowerAddress.includes('torre') ||
               lowerAddress.includes('tower')) {
        buildings++;
        propertyTypes['building'] = (propertyTypes['building'] || 0) + 1;
      }
      // Default to house
      else {
        houses++;
        propertyTypes['house'] = (propertyTypes['house'] || 0) + 1;
      }

      // Additional property type analysis
      if (lowerAddress.includes('condo')) {
        propertyTypes['condo'] = (propertyTypes['condo'] || 0) + 1;
      }
      if (lowerAddress.includes('townhouse') || lowerAddress.includes('town house')) {
        propertyTypes['townhouse'] = (propertyTypes['townhouse'] || 0) + 1;
      }
      if (lowerAddress.includes('mobile') || lowerAddress.includes('trailer')) {
        propertyTypes['mobile_home'] = (propertyTypes['mobile_home'] || 0) + 1;
      }
    });

    return { houses, apartments, buildings, propertyTypes };
  }

  /**
   * Get property analytics for a specific territory
   */
  static async getTerritoryPropertyAnalytics(territoryId: string, congregationId: string): Promise<PropertyAnalytics | null> {
    try {
      const territory = await prisma.territory.findUnique({
        where: {
          id: territoryId,
          congregationId
        },
        select: {
          id: true,
          territoryNumber: true,
          address: true,
          updatedAt: true
        }
      });

      if (!territory) {
        return null;
      }

      // Split addresses by newlines to get individual properties
      const addresses = territory.address.split('\n').filter(addr => addr.trim().length > 0);
      const analysis = this.analyzePropertyTypes(addresses);

      return {
        territoryId: territory.id,
        territoryNumber: territory.territoryNumber,
        totalProperties: addresses.length,
        houses: analysis.houses,
        apartments: analysis.apartments,
        buildings: analysis.buildings,
        propertyTypes: analysis.propertyTypes,
        lastUpdated: territory.updatedAt
      };

    } catch (error) {
      console.error('Error getting territory property analytics:', error);
      return null;
    }
  }

  /**
   * Get property analytics for all territories in a congregation
   */
  static async getCongregationPropertyAnalytics(congregationId: string): Promise<PropertyAnalytics[]> {
    try {
      const territories = await prisma.territory.findMany({
        where: { congregationId },
        select: {
          id: true,
          territoryNumber: true,
          address: true,
          updatedAt: true
        },
        orderBy: {
          territoryNumber: 'asc'
        }
      });

      const analytics: PropertyAnalytics[] = [];

      for (const territory of territories) {
        const addresses = territory.address.split('\n').filter(addr => addr.trim().length > 0);
        const analysis = this.analyzePropertyTypes(addresses);

        analytics.push({
          territoryId: territory.id,
          territoryNumber: territory.territoryNumber,
          totalProperties: addresses.length,
          houses: analysis.houses,
          apartments: analysis.apartments,
          buildings: analysis.buildings,
          propertyTypes: analysis.propertyTypes,
          lastUpdated: territory.updatedAt
        });
      }

      return analytics;

    } catch (error) {
      console.error('Error getting congregation property analytics:', error);
      return [];
    }
  }

  /**
   * Get property summary for the entire congregation
   */
  static async getPropertySummary(congregationId: string): Promise<PropertySummary> {
    try {
      const analytics = await this.getCongregationPropertyAnalytics(congregationId);

      const summary = analytics.reduce((acc, territory) => {
        acc.totalProperties += territory.totalProperties;
        acc.totalHouses += territory.houses;
        acc.totalApartments += territory.apartments;
        acc.totalBuildings += territory.buildings;
        return acc;
      }, {
        totalProperties: 0,
        totalHouses: 0,
        totalApartments: 0,
        totalBuildings: 0,
        averagePropertiesPerTerritory: 0,
        territoryCount: analytics.length
      });

      summary.averagePropertiesPerTerritory = analytics.length > 0
        ? Math.round((summary.totalProperties / analytics.length) * 10) / 10
        : 0;

      return summary;

    } catch (error) {
      console.error('Error getting property summary:', error);
      return {
        totalProperties: 0,
        totalHouses: 0,
        totalApartments: 0,
        totalBuildings: 0,
        averagePropertiesPerTerritory: 0,
        territoryCount: 0
      };
    }
  }

  /**
   * Get property distribution across territories
   */
  static async getPropertyDistribution(congregationId: string): Promise<PropertyDistribution[]> {
    try {
      const analytics = await this.getCongregationPropertyAnalytics(congregationId);
      const totalProperties = analytics.reduce((sum, territory) => sum + territory.totalProperties, 0);

      return analytics.map(territory => ({
        territoryNumber: territory.territoryNumber,
        houses: territory.houses,
        apartments: territory.apartments,
        buildings: territory.buildings,
        total: territory.totalProperties,
        percentage: totalProperties > 0
          ? Math.round((territory.totalProperties / totalProperties) * 1000) / 10
          : 0
      })).sort((a, b) => b.total - a.total);

    } catch (error) {
      console.error('Error getting property distribution:', error);
      return [];
    }
  }

  /**
   * Get territories with highest property counts
   */
  static async getTopTerritoriesByProperties(congregationId: string, limit: number = 10): Promise<PropertyDistribution[]> {
    try {
      const distribution = await this.getPropertyDistribution(congregationId);
      return distribution.slice(0, limit);

    } catch (error) {
      console.error('Error getting top territories by properties:', error);
      return [];
    }
  }

  /**
   * Get property type breakdown across all territories
   */
  static async getPropertyTypeBreakdown(congregationId: string): Promise<{ [type: string]: number }> {
    try {
      const analytics = await this.getCongregationPropertyAnalytics(congregationId);
      const breakdown: { [type: string]: number } = {};

      analytics.forEach(territory => {
        Object.entries(territory.propertyTypes).forEach(([type, count]) => {
          breakdown[type] = (breakdown[type] || 0) + count;
        });
      });

      return breakdown;

    } catch (error) {
      console.error('Error getting property type breakdown:', error);
      return {};
    }
  }
}
