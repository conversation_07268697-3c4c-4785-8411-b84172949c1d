# 5. Authentication and Authorization

## Authentication Strategy

The authentication system for the Hermanos application maintains the simplicity and user-friendliness of the existing system while adding robust security measures and multi-congregation support. The approach balances security requirements with the practical needs of congregation members who primarily access the system via mobile devices.

**Authentication Approach:**

- **Congregation-Based Login**: Users connect to their specific congregation using congregation ID and PIN
- **Member Authentication**: Individual members authenticate using their personal PIN within their congregation
- **JWT Token Management**: Secure token-based authentication with mobile-friendly expiration times
- **Role-Based Access**: Automatic role assignment based on member status within the congregation
- **Persistent Sessions**: Long-lived sessions optimized for mobile usage patterns

## Simple JWT Implementation

```typescript
// lib/auth/simpleJWT.ts - Lightweight JWT management
import jwt from 'jsonwebtoken';

interface JWTPayload {
  userId: string;
  congregationId: string;
  role: string;
  name: string;
  iat?: number;
  exp?: number;
}

export class SimpleJWTManager {
  private static readonly JWT_SECRET = process.env.JWT_SECRET!;
  private static readonly TOKEN_EXPIRY = '30d'; // Mobile-friendly long expiration

  // Generate JWT token for authenticated user
  static generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    return jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.TOKEN_EXPIRY,
      issuer: 'hermanos-app',
      audience: payload.congregationId,
    });
  }

  // Verify and decode JWT token
  static verifyToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as JWTPayload;
      return decoded;
    } catch (error) {
      console.error('JWT verification failed:', error);
      return null;
    }
  }

  // Extract token from Authorization header
  static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }

  // Check if token is expired
  static isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as JWTPayload;
      if (!decoded || !decoded.exp) return true;

      return Date.now() >= decoded.exp * 1000;
    } catch {
      return true;
    }
  }

  // Refresh token if needed (within 7 days of expiry)
  static shouldRefreshToken(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as JWTPayload;
      if (!decoded || !decoded.exp) return false;

      const sevenDaysInSeconds = 7 * 24 * 60 * 60;
      const timeUntilExpiry = decoded.exp - Date.now() / 1000;

      return timeUntilExpiry < sevenDaysInSeconds;
    } catch {
      return false;
    }
  }
}
```

## Role-Based Access Control

```typescript
// lib/auth/simpleRBAC.ts - Simple role-based access control
export enum ROLES {
  PUBLISHER = 'publisher',
  MINISTERIAL_SERVANT = 'ministerial_servant',
  ELDER = 'elder',
  OVERSEER_COORDINATOR = 'overseer_coordinator',
  DEVELOPER = 'developer', // Special role for system administration
}

export enum PERMISSIONS {
  VIEW_DASHBOARD = 'view_dashboard',
  VIEW_MEETINGS = 'view_meetings',
  VIEW_TASKS = 'view_tasks',
  VIEW_LETTERS = 'view_letters',
  VIEW_FIELD_SERVICE = 'view_field_service',

  // Administrative permissions
  VIEW_ADMIN = 'view_admin',
  MANAGE_MEMBERS = 'manage_members',
  MANAGE_MEETINGS = 'manage_meetings',
  MANAGE_TASKS = 'manage_tasks',
  MANAGE_LETTERS = 'manage_letters',
  UPLOAD_LETTERS = 'upload_letters',
  ASSIGN_MEETING_PARTS = 'assign_meeting_parts',
  VIEW_ALL_SERVICE_REPORTS = 'view_all_service_reports',

  // Elder-specific permissions
  MANAGE_CONGREGATION_SETTINGS = 'manage_congregation_settings',
  VIEW_SENSITIVE_LETTERS = 'view_sensitive_letters',
  MANAGE_PERMISSIONS = 'manage_permissions',
}

// Role permission mapping
const ROLE_PERMISSIONS: Record<ROLES, PERMISSIONS[]> = {
  [ROLES.PUBLISHER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MEETINGS,
    PERMISSIONS.VIEW_TASKS,
    PERMISSIONS.VIEW_LETTERS,
    PERMISSIONS.VIEW_FIELD_SERVICE,
  ],

  [ROLES.MINISTERIAL_SERVANT]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MEETINGS,
    PERMISSIONS.VIEW_TASKS,
    PERMISSIONS.VIEW_LETTERS,
    PERMISSIONS.VIEW_FIELD_SERVICE,
    PERMISSIONS.VIEW_ADMIN,
    PERMISSIONS.MANAGE_TASKS,
    PERMISSIONS.UPLOAD_LETTERS,
    PERMISSIONS.ASSIGN_MEETING_PARTS,
  ],

  [ROLES.ELDER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MEETINGS,
    PERMISSIONS.VIEW_TASKS,
    PERMISSIONS.VIEW_LETTERS,
    PERMISSIONS.VIEW_FIELD_SERVICE,
    PERMISSIONS.VIEW_ADMIN,
    PERMISSIONS.MANAGE_MEMBERS,
    PERMISSIONS.MANAGE_MEETINGS,
    PERMISSIONS.MANAGE_TASKS,
    PERMISSIONS.MANAGE_LETTERS,
    PERMISSIONS.UPLOAD_LETTERS,
    PERMISSIONS.ASSIGN_MEETING_PARTS,
    PERMISSIONS.VIEW_ALL_SERVICE_REPORTS,
    PERMISSIONS.VIEW_SENSITIVE_LETTERS,
  ],

  [ROLES.COORDINATOR]: Object.values(PERMISSIONS), // Full permissions by default
};

// Permission checking functions
export function hasPermission(role: ROLES, permission: PERMISSIONS): boolean {
  const rolePermissions = ROLE_PERMISSIONS[role];
  return rolePermissions.includes(permission);
}

export function canViewAdmin(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.VIEW_ADMIN);
}

export function canManageMembers(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.MANAGE_MEMBERS);
}

export function canUploadLetters(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.UPLOAD_LETTERS);
}

export function canAssignMeetingParts(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.ASSIGN_MEETING_PARTS);
}

// Get all permissions for a role
export function getRolePermissions(role: ROLES): PERMISSIONS[] {
  return ROLE_PERMISSIONS[role] || [];
}

// Check if user has any of the specified roles
export function hasAnyRole(userRole: ROLES, allowedRoles: ROLES[]): boolean {
  return allowedRoles.includes(userRole);
}
```

## Authentication Middleware

```typescript
// lib/middleware/auth.ts - Authentication middleware for API routes
import { NextApiRequest, NextApiResponse } from 'next';
import { SimpleJWTManager } from '@/lib/auth/simpleJWT';
import { ROLES, hasPermission, PERMISSIONS } from '@/lib/auth/simpleRBAC';

interface AuthenticatedRequest extends NextApiRequest {
  user?: {
    id: string;
    name: string;
    role: ROLES;
    congregationId: string;
  };
}

interface AuthContext {
  user: {
    id: string;
    name: string;
    role: ROLES;
    congregationId: string;
  };
  congregation: {
    id: string;
  };
}

type AuthenticatedHandler = (
  req: AuthenticatedRequest,
  res: NextApiResponse,
  context: AuthContext
) => Promise<void>;

interface AuthOptions {
  requireRole?: ROLES[];
  requirePermission?: PERMISSIONS;
  allowSameUser?: boolean; // Allow access if user is accessing their own data
}

// Main authentication middleware
export function withAuth(handler: AuthenticatedHandler, options: AuthOptions = {}) {
  return async (req: AuthenticatedRequest, res: NextApiResponse) => {
    try {
      // Extract token from Authorization header
      const authHeader = req.headers.authorization;
      const token = SimpleJWTManager.extractTokenFromHeader(authHeader);

      if (!token) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication token required',
          },
        });
      }

      // Verify token
      const payload = SimpleJWTManager.verifyToken(token);
      if (!payload) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid or expired authentication token',
          },
        });
      }

      // Set user context
      req.user = {
        id: payload.userId,
        name: payload.name,
        role: payload.role as ROLES,
        congregationId: payload.congregationId,
      };

      // Check role requirements
      if (options.requireRole && !options.requireRole.includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_ROLE',
            message: 'Insufficient role for this operation',
          },
        });
      }

      // Check permission requirements
      if (options.requirePermission && !hasPermission(req.user.role, options.requirePermission)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Insufficient permissions for this operation',
          },
        });
      }

      // Create context object
      const context: AuthContext = {
        user: req.user,
        congregation: {
          id: req.user.congregationId,
        },
      };

      // Call the handler with context
      await handler(req, res, context);
    } catch (error) {
      console.error('Authentication middleware error:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      });
    }
  };
}

// Convenience middleware for common permission checks
export const requireElder = (handler: AuthenticatedHandler) =>
  withAuth(handler, { requireRole: [ROLES.ELDER, ROLES.OVERSEER_COORDINATOR, ROLES.DEVELOPER] });

export const requireMinisterialServant = (handler: AuthenticatedHandler) =>
  withAuth(handler, {
    requireRole: [
      ROLES.MINISTERIAL_SERVANT,
      ROLES.ELDER,
      ROLES.OVERSEER_COORDINATOR,
      ROLES.DEVELOPER,
    ],
  });

export const requireAdmin = (handler: AuthenticatedHandler) =>
  withAuth(handler, { requirePermission: PERMISSIONS.VIEW_ADMIN });
```

## Login API Implementation

```typescript
// pages/api/auth/congregation-login.ts - Congregation authentication endpoint
import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import { prisma } from '@/lib/prisma';
import { SimpleJWTManager } from '@/lib/auth/simpleJWT';
import { validateCongregationLogin } from '@/lib/validation/simple';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  try {
    // Validate input
    const validation = validateCongregationLogin(req.body);
    if (validation.length > 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: validation,
        },
      });
    }

    const { congregationId, pin, region } = req.body;

    // Find congregation
    const congregation = await prisma.congregation.findUnique({
      where: { id: congregationId },
    });

    if (!congregation) {
      return res.status(401).json({
        success: false,
        message: 'Invalid congregation credentials',
      });
    }

    // Verify congregation PIN
    const isPinValid = await bcrypt.compare(pin, congregation.pin);
    if (!isPinValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid congregation credentials',
      });
    }

    // Find a default user for this congregation (first elder or overseer)
    const defaultUser = await prisma.member.findFirst({
      where: {
        congregationId: congregation.id,
        isActive: true,
        role: {
          in: ['elder', 'overseer_coordinator'],
        },
      },
      orderBy: {
        role: 'desc', // Prefer overseer_coordinator over elder
      },
    });

    if (!defaultUser) {
      return res.status(404).json({
        success: false,
        message: 'No active administrators found for this congregation',
      });
    }

    // Generate JWT token
    const token = SimpleJWTManager.generateToken({
      userId: defaultUser.id,
      congregationId: congregation.id,
      role: defaultUser.role,
      name: defaultUser.name,
    });

    // Return success response
    return res.status(200).json({
      success: true,
      token,
      user: {
        id: defaultUser.id,
        name: defaultUser.name,
        role: defaultUser.role,
        congregationId: congregation.id,
        congregationName: congregation.name,
      },
      congregation: {
        id: congregation.id,
        name: congregation.name,
        region: congregation.region,
        language: congregation.language,
      },
    });
  } catch (error) {
    console.error('Congregation login error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
}
```

## Frontend Authentication Hook

```typescript
// hooks/useAuth.ts - Frontend authentication management
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { SimpleJWTManager } from '@/lib/auth/simpleJWT';

export function useAuth() {
  const router = useRouter();
  const {
    user,
    token,
    isAuthenticated,
    login,
    logout,
    canViewAdmin,
    canManageMembers,
    canUploadLetters,
  } = useAuthStore();

  // Check token validity on mount and periodically
  useEffect(() => {
    if (token && SimpleJWTManager.isTokenExpired(token)) {
      logout();
      router.push('/login');
    }
  }, [token, logout, router]);

  // Login function
  const handleLogin = async (congregationId: string, pin: string, region: string) => {
    try {
      const response = await fetch('/api/auth/congregation-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ congregationId, pin, region }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      // Store authentication data
      login(data.user, data.token);

      return { success: true, user: data.user };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    }
  };

  // Logout function
  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  // Check if user needs to refresh token
  const shouldRefreshToken = () => {
    return token ? SimpleJWTManager.shouldRefreshToken(token) : false;
  };

  return {
    user,
    token,
    isAuthenticated,
    login: handleLogin,
    logout: handleLogout,
    canViewAdmin,
    canManageMembers,
    canUploadLetters,
    shouldRefreshToken,
  };
}

// Protected route wrapper
export function useRequireAuth() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  return isAuthenticated;
}

// Role-based access hook
export function useRequireRole(allowedRoles: string[]) {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user && !allowedRoles.includes(user.role)) {
      router.push('/dashboard'); // Redirect to dashboard if insufficient role
    }
  }, [user, isAuthenticated, allowedRoles, router]);

  return user && allowedRoles.includes(user.role);
}
```

## Login Page Implementation

```typescript
// app/login/page.tsx - Login page with exact UI replication
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';

export default function LoginPage() {
  const [formData, setFormData] = useState({
    region: '',
    congregationId: '',
    congregationPin: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await login(
        formData.congregationId,
        formData.congregationPin,
        formData.region
      );

      if (result.success) {
        router.push('/dashboard');
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (error) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        {/* Header - Exact replication */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">HERMANOS</h1>
          <h2 className="text-lg text-gray-600">Connect to your Congregation</h2>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* Login form - Exact replication */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="region" className="block text-sm font-medium text-gray-700 mb-1">
              Region
            </label>
            <Select
              id="region"
              value={formData.region}
              onChange={(value) => handleInputChange('region', value)}
              required
              className="w-full"
            >
              <option value="">Select Region</option>
              <option value="North America">North America</option>
              <option value="Central America">Central America</option>
              <option value="South America">South America</option>
              <option value="Europe">Europe</option>
              <option value="Asia">Asia</option>
              <option value="Africa">Africa</option>
              <option value="Oceania">Oceania</option>
            </Select>
          </div>

          <div>
            <label htmlFor="congregationId" className="block text-sm font-medium text-gray-700 mb-1">
              Congregation ID
            </label>
            <Input
              id="congregationId"
              type="text"
              value={formData.congregationId}
              onChange={(e) => handleInputChange('congregationId', e.target.value)}
              placeholder="Enter congregation ID"
              required
              className="w-full"
            />
          </div>

          <div>
            <label htmlFor="congregationPin" className="block text-sm font-medium text-gray-700 mb-1">
              Congregation PIN
            </label>
            <Input
              id="congregationPin"
              type="password"
              value={formData.congregationPin}
              onChange={(e) => handleInputChange('congregationPin', e.target.value)}
              placeholder="Enter congregation PIN"
              required
              className="w-full"
            />
          </div>

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-md font-medium"
          >
            {isLoading ? 'Connecting...' : 'Connect'}
          </Button>
        </form>

        {/* Footer */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>Congregation Management System</p>
          <p className="mt-1">Version 2.0</p>
        </div>
      </div>
    </div>
  );
}
```

This authentication system provides secure, user-friendly access control while maintaining the simplicity that congregation members expect. The implementation preserves the exact login experience while adding robust security measures and multi-congregation support.

---
