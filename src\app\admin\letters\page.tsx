'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LettersManager from '@/components/admin/LettersManager';
import AdminFooter from '@/components/admin/AdminFooter';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
}

export default function LettersManagementPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      console.log('LettersPage: Starting authentication check...');
      const token = localStorage.getItem('hermanos_token');
      console.log('LettersPage: Token found:', !!token);

      if (!token) {
        console.log('LettersPage: No token, redirecting to login');
        router.push('/login');
        return;
      }

      console.log('LettersPage: Verifying token...');
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      console.log('LettersPage: Auth verify response status:', response.status);

      if (!response.ok) {
        console.log('LettersPage: Auth verification failed, redirecting to login');
        router.push('/login');
        return;
      }

      const data = await response.json();
      const userData = data.user;
      console.log('LettersPage: User data received:', { role: userData.role, name: userData.name });

      // Check if user has admin access
      if (!['elder', 'ministerial_servant', 'coordinator'].includes(userData.role)) {
        console.log('LettersPage: User does not have admin access, redirecting to dashboard');
        router.push('/dashboard');
        return;
      }

      console.log('LettersPage: Authentication successful, setting user');
      setUser(userData);
    } catch (error) {
      console.error('LettersPage: Authentication check failed:', error);
      router.push('/login');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-purple-500 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/admin')}
                  className="mr-4 p-2 hover:bg-purple-600 rounded-lg transition-colors"
                  aria-label="Volver"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <h1 className="text-xl font-bold">Cartas</h1>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 pb-20">
        <LettersManager
          congregationId={user.congregationId}
          userRole={user.role}
        />
      </div>

      {/* Admin Footer */}
      <AdminFooter currentSection="cartas" />
    </div>
  );
}
