const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function fixPins() {
  try {
    console.log('🔧 Fixing authentication PINs...\n');

    // 1. Fix congregation PIN to 1930
    console.log('1️⃣ Fixing congregation PIN...');
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (congregation) {
      const pinMatches = await bcrypt.compare('1930', congregation.pin);
      console.log(`Current congregation PIN matches 1930: ${pinMatches}`);
      
      if (!pinMatches) {
        const newPin = await bcrypt.hash('1930', 10);
        await prisma.congregation.update({
          where: { id: '1441' },
          data: { pin: newPin }
        });
        console.log('✅ Updated congregation PIN to 1930');
      } else {
        console.log('✅ Congregation PIN already correct');
      }
    }

    // 2. Fix coordinator member PIN to 5488
    console.log('\n2️⃣ Fixing coordinator member PIN...');
    const coordinator = await prisma.member.findFirst({
      where: {
        congregationId: '1441',
        role: 'coordinator',
        isActive: true
      }
    });

    if (coordinator) {
      console.log(`Found coordinator: ${coordinator.name}`);
      const memberPinMatches = await bcrypt.compare('5488', coordinator.pin);
      console.log(`Current coordinator PIN matches 5488: ${memberPinMatches}`);
      
      if (!memberPinMatches) {
        const newMemberPin = await bcrypt.hash('5488', 10);
        await prisma.member.update({
          where: { id: coordinator.id },
          data: { pin: newMemberPin }
        });
        console.log('✅ Updated coordinator PIN to 5488');
      } else {
        console.log('✅ Coordinator PIN already correct');
      }
    } else {
      console.log('❌ No coordinator found');
    }

    console.log('\n🧪 Test these credentials:');
    console.log('🌐 URL: http://localhost:3000 or http://localhost:3001');
    console.log('🏛️ Congregation ID: 1441');
    console.log('🔐 Congregation PIN: 1930');
    console.log('👤 Then use coordinator PIN: 5488');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixPins();
