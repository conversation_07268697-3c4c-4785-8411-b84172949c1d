/**
 * Territory Completion API Endpoint
 *
 * Handles territory completion by members, following Field Service
 * completion patterns and workflow.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { TerritoryStatusService } from '@/services/territories/TerritoryStatusService';

// Validation schema
const CompletionSchema = z.object({
  notes: z.string().optional(),
  completionDate: z.string().optional().transform(str => str ? new Date(str) : new Date()),
  visitNotes: z.string().optional(),
  addressesWorked: z.string().optional()
});

/**
 * PUT /api/territories/[id]/complete
 * Mark territory as completed by member
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: {
        id: true,
        name: true,
        role: true,
        congregationId: true
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Find the territory and verify member has access
    const territory = await prisma.territory.findFirst({
      where: {
        id: params.id,
        congregationId: member.congregationId
      },
      include: {
        currentAssignment: {
          where: {
            status: 'active'
          },
          include: {
            member: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!territory) {
      return NextResponse.json(
        { error: 'Territory not found' },
        { status: 404 }
      );
    }

    // Verify member is assigned to this territory
    if (!territory.currentAssignment || territory.currentAssignment.memberId !== member.id) {
      return NextResponse.json(
        { error: 'You are not assigned to this territory' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = CompletionSchema.parse(body);

    // Log the completion visit
    const completionVisit = await TerritoryStatusService.logTerritoryVisit({
      assignmentId: territory.currentAssignment.id,
      visitDate: validatedData.completionDate,
      isCompleted: true,
      notes: validatedData.visitNotes || validatedData.notes || 'Territory completed',
      addressesWorked: validatedData.addressesWorked,
      congregationId: member.congregationId
    });

    // Complete the assignment
    const completedAssignment = await TerritoryStatusService.completeAssignment(
      territory.currentAssignment.id,
      member.congregationId
    );

    // Get updated territory information
    const updatedTerritory = await prisma.territory.findUnique({
      where: { id: params.id },
      include: {
        assignments: {
          where: {
            status: 'completed'
          },
          orderBy: {
            completedAt: 'desc'
          },
          take: 1,
          include: {
            member: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        territory: {
          id: updatedTerritory?.id,
          territoryNumber: updatedTerritory?.territoryNumber,
          address: updatedTerritory?.address,
          status: updatedTerritory?.status
        },
        completion: {
          completedAt: validatedData.completionDate,
          completedBy: member.name,
          notes: validatedData.notes,
          visitId: completionVisit.visit.id
        },
        message: `Territorio ${territory.territoryNumber} marcado como completado exitosamente`
      }
    });

  } catch (error) {
    console.error('Territory completion error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}




