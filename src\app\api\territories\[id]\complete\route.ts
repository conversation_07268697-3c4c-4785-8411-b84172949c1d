/**
 * Territory Completion API Endpoint
 *
 * Allows members to mark their assigned territories as completed.
 * Updates territory status and assignment records.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

/**
 * POST /api/territories/[id]/complete - Mark territory as completed
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;
    const territoryId = params.id;

    // Get member details
    const member = await prisma.member.findFirst({
      where: {
        congregationId: user.congregationId,
        pin: user.pin
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Get territory and verify it exists and belongs to the congregation
    const territory = await prisma.territory.findFirst({
      where: {
        id: territoryId,
        congregationId: user.congregationId
      },
      include: {
        assignments: {
          where: {
            returnedAt: null
          },
          include: {
            member: true
          }
        }
      }
    });

    if (!territory) {
      return NextResponse.json(
        { error: 'Territory not found' },
        { status: 404 }
      );
    }

    // Check if territory is assigned to the current member
    const currentAssignment = territory.assignments.find(
      assignment => assignment.member.id === member.id
    );

    if (!currentAssignment) {
      return NextResponse.json(
        { error: 'Territory is not assigned to you' },
        { status: 403 }
      );
    }

    // Parse request body for completion details
    const body = await request.json().catch(() => ({}));
    const completionNotes = body.notes || '';

    // Start transaction to update territory and assignment
    const result = await prisma.$transaction(async (tx) => {
      // Update territory status to completed
      const updatedTerritory = await tx.territory.update({
        where: { id: territoryId },
        data: {
          status: 'completed',
          updatedAt: new Date()
        }
      });

      // Update assignment with completion details
      const updatedAssignment = await tx.territoryAssignment.update({
        where: { id: currentAssignment.id },
        data: {
          returnedAt: new Date(),
          completionNotes: completionNotes,
          status: 'completed'
        }
      });

      // Create completion record for tracking
      const completionRecord = await tx.territoryCompletion.create({
        data: {
          territoryId: territoryId,
          memberId: member.id,
          assignmentId: currentAssignment.id,
          completedAt: new Date(),
          notes: completionNotes
        }
      });

      return {
        territory: updatedTerritory,
        assignment: updatedAssignment,
        completion: completionRecord
      };
    });

    return NextResponse.json({
      success: true,
      message: `Territory ${territory.territoryNumber} marked as completed`,
      data: {
        territoryId: result.territory.id,
        territoryNumber: territory.territoryNumber,
        completedAt: result.completion.completedAt,
        completedBy: `${member.firstName} ${member.lastName}`.trim()
      }
    });

  } catch (error) {
    console.error('Error completing territory:', error);
    return NextResponse.json(
      { 
        error: 'Failed to complete territory',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/territories/[id]/complete - Get territory completion status
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;
    const territoryId = params.id;

    // Get territory completion history
    const completions = await prisma.territoryCompletion.findMany({
      where: {
        territoryId: territoryId,
        territory: {
          congregationId: user.congregationId
        }
      },
      include: {
        member: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        },
        territory: {
          select: {
            territoryNumber: true,
            address: true
          }
        }
      },
      orderBy: {
        completedAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        territoryId,
        completions: completions.map(completion => ({
          id: completion.id,
          completedAt: completion.completedAt,
          completedBy: `${completion.member.firstName} ${completion.member.lastName}`.trim(),
          notes: completion.notes,
          territory: {
            number: completion.territory.territoryNumber,
            address: completion.territory.address
          }
        }))
      }
    });

  } catch (error) {
    console.error('Error fetching territory completion status:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch completion status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/territories/[id]/complete - Undo territory completion (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;
    const territoryId = params.id;

    // Get member details and check admin permissions
    const member = await prisma.member.findFirst({
      where: {
        congregationId: user.congregationId,
        pin: user.pin
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check admin permissions
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(member.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required to undo territory completion' },
        { status: 403 }
      );
    }

    // Get the most recent completion record
    const latestCompletion = await prisma.territoryCompletion.findFirst({
      where: {
        territoryId: territoryId,
        territory: {
          congregationId: user.congregationId
        }
      },
      include: {
        assignment: true,
        territory: true
      },
      orderBy: {
        completedAt: 'desc'
      }
    });

    if (!latestCompletion) {
      return NextResponse.json(
        { error: 'No completion record found for this territory' },
        { status: 404 }
      );
    }

    // Undo the completion in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Revert territory status to assigned
      const updatedTerritory = await tx.territory.update({
        where: { id: territoryId },
        data: {
          status: 'assigned',
          updatedAt: new Date()
        }
      });

      // Revert assignment status
      const updatedAssignment = await tx.territoryAssignment.update({
        where: { id: latestCompletion.assignmentId },
        data: {
          returnedAt: null,
          completionNotes: null,
          status: 'active'
        }
      });

      // Remove the completion record
      await tx.territoryCompletion.delete({
        where: { id: latestCompletion.id }
      });

      return {
        territory: updatedTerritory,
        assignment: updatedAssignment
      };
    });

    return NextResponse.json({
      success: true,
      message: `Territory ${latestCompletion.territory.territoryNumber} completion undone`,
      data: {
        territoryId: result.territory.id,
        territoryNumber: latestCompletion.territory.territoryNumber,
        undoneAt: new Date(),
        undoneBy: `${member.firstName} ${member.lastName}`.trim()
      }
    });

  } catch (error) {
    console.error('Error undoing territory completion:', error);
    return NextResponse.json(
      { 
        error: 'Failed to undo territory completion',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
