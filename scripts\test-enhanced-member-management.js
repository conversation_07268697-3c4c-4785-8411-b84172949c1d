/**
 * Test Enhanced Member Management System
 *
 * Tests the Story 2.2 implementation with permission delegation integration,
 * enhanced member profiles, and role-based access control.
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testEnhancedMemberManagement() {
  console.log('🧪 Testing Enhanced Member Management System (Story 2.2)');
  console.log('=' .repeat(60));

  try {
    // Test 1: Database Schema Verification
    console.log('\n1. Testing Database Schema...');
    await testDatabaseSchema();

    // Test 2: Enhanced Member Creation
    console.log('\n2. Testing Enhanced Member Creation...');
    await testEnhancedMemberCreation();

    // Test 3: Member Search and Filtering
    console.log('\n3. Testing Member Search and Filtering...');
    await testMemberSearchAndFiltering();

    // Test 4: Permission Integration
    console.log('\n4. Testing Permission Integration...');
    await testPermissionIntegration();

    // Test 5: Role Assignment and Validation
    console.log('\n5. Testing Role Assignment and Validation...');
    await testRoleAssignmentValidation();

    console.log('\n✅ All Enhanced Member Management tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function testDatabaseSchema() {
  // Test that enhanced member fields exist
  const member = await prisma.member.findFirst({
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      address: true,
      birthDate: true,
      role: true,
      serviceGroup: true,
      contactPreferences: true,
      qualifications: true,
      notes: true,
    },
  });

  console.log('   ✅ Enhanced member schema fields accessible');

  // Test that we can query by service group
  const membersByServiceGroup = await prisma.member.findMany({
    where: {
      serviceGroup: { not: null },
    },
    take: 1,
  });

  console.log('   ✅ Service group filtering works');
}

async function testEnhancedMemberCreation() {
  const testCongregation = await prisma.congregation.findFirst();
  if (!testCongregation) {
    throw new Error('No test congregation found');
  }

  // Create a test member with enhanced fields
  const hashedPin = await bcrypt.hash('test1234', 10);

  const testMember = await prisma.member.create({
    data: {
      congregationId: testCongregation.id,
      name: 'Juan Carlos Pérez',
      email: `juan.perez.${Date.now()}@test.com`,
      phone: '+****************',
      address: '123 Main Street, Anytown, ST 12345',
      birthDate: new Date('1985-06-15'),
      role: 'elder',
      serviceGroup: 'Grupo 1',
      pin: hashedPin,
      contactPreferences: {
        preferredMethod: 'email',
        allowEmergencyContact: true,
        privacyLevel: 'elders_only',
      },
      qualifications: ['Lector', 'Oración', 'Discurso', 'Presidente'],
      notes: 'Miembro activo con experiencia en presentaciones',
    },
  });

  console.log(`   ✅ Enhanced member created: ${testMember.name}`);
  console.log(`   📧 Email: ${testMember.email}`);
  console.log(`   📱 Phone: ${testMember.phone}`);
  console.log(`   🏠 Address: ${testMember.address}`);
  console.log(`   👥 Service Group: ${testMember.serviceGroup}`);
  console.log(`   🎯 Qualifications: ${JSON.stringify(testMember.qualifications)}`);

  // Test member history tracking
  const historyCount = await prisma.memberChangeHistory.count({
    where: { memberId: testMember.id },
  });

  console.log(`   ✅ Member change history created: ${historyCount} record(s)`);

  return testMember;
}

async function testMemberSearchAndFiltering() {
  const testCongregation = await prisma.congregation.findFirst();
  if (!testCongregation) {
    throw new Error('No test congregation found');
  }

  // Test search by name
  const nameSearch = await prisma.member.findMany({
    where: {
      congregationId: testCongregation.id,
      name: { contains: 'Juan', mode: 'insensitive' },
    },
  });

  console.log(`   ✅ Name search found ${nameSearch.length} member(s)`);

  // Test search by email
  const emailSearch = await prisma.member.findMany({
    where: {
      congregationId: testCongregation.id,
      email: { contains: 'test.com', mode: 'insensitive' },
    },
  });

  console.log(`   ✅ Email search found ${emailSearch.length} member(s)`);

  // Test filter by role
  const roleFilter = await prisma.member.findMany({
    where: {
      congregationId: testCongregation.id,
      role: { in: ['elder', 'coordinator'] },
    },
  });

  console.log(`   ✅ Role filter found ${roleFilter.length} elder(s)/coordinator(s)`);

  // Test filter by service group
  const serviceGroupFilter = await prisma.member.findMany({
    where: {
      congregationId: testCongregation.id,
      serviceGroup: 'Grupo 1',
    },
  });

  console.log(`   ✅ Service group filter found ${serviceGroupFilter.length} member(s) in Grupo 1`);

  // Test combined filters
  const combinedFilter = await prisma.member.findMany({
    where: {
      congregationId: testCongregation.id,
      AND: [
        { role: 'elder' },
        { isActive: true },
        { serviceGroup: { not: null } },
      ],
    },
  });

  console.log(`   ✅ Combined filter found ${combinedFilter.length} active elder(s) with service groups`);
}

async function testPermissionIntegration() {
  const testCongregation = await prisma.congregation.findFirst();
  if (!testCongregation) {
    throw new Error('No test congregation found');
  }

  // Find a coordinator or elder for testing
  const coordinator = await prisma.member.findFirst({
    where: {
      congregationId: testCongregation.id,
      role: { in: ['coordinator', 'elder'] },
    },
  });

  if (!coordinator) {
    console.log('   ⚠️  No coordinator/elder found for permission testing');
    return;
  }

  console.log(`   ✅ Found test user: ${coordinator.name} (${coordinator.role})`);

  // Test permission delegation for member management
  const existingPermission = await prisma.elderPermission.findFirst({
    where: {
      congregationId: testCongregation.id,
      sectionId: 'members',
    },
  });

  if (existingPermission) {
    console.log(`   ✅ Found existing member management permission delegation`);
  } else {
    console.log(`   ℹ️  No existing member management permission delegation found`);
  }

  // Test permission audit log
  const auditLogCount = await prisma.permissionAuditLog.count({
    where: {
      congregationId: testCongregation.id,
      sectionId: 'members',
    },
  });

  console.log(`   ✅ Permission audit log has ${auditLogCount} record(s) for member management`);
}

async function testRoleAssignmentValidation() {
  const testCongregation = await prisma.congregation.findFirst();
  if (!testCongregation) {
    throw new Error('No test congregation found');
  }

  // Test role hierarchy validation
  const roleHierarchy = {
    'publisher': 1,
    'ministerial_servant': 2,
    'elder': 3,
    'coordinator': 4,
  };

  console.log('   ✅ Role hierarchy defined correctly');

  // Test role distribution
  const roleDistribution = await prisma.member.groupBy({
    by: ['role'],
    where: {
      congregationId: testCongregation.id,
      isActive: true,
    },
    _count: {
      role: true,
    },
  });

  console.log('   📊 Role distribution:');
  roleDistribution.forEach(group => {
    console.log(`      ${group.role}: ${group._count.role} member(s)`);
  });

  // Test service group assignments
  const serviceGroupDistribution = await prisma.member.groupBy({
    by: ['serviceGroup'],
    where: {
      congregationId: testCongregation.id,
      isActive: true,
      serviceGroup: { not: null },
    },
    _count: {
      serviceGroup: true,
    },
  });

  console.log('   📊 Service group distribution:');
  serviceGroupDistribution.forEach(group => {
    console.log(`      ${group.serviceGroup}: ${group._count.serviceGroup} member(s)`);
  });

  // Test qualifications tracking
  const membersWithQualifications = await prisma.member.findMany({
    where: {
      congregationId: testCongregation.id,
      qualifications: { not: { equals: [] } },
    },
    select: {
      name: true,
      role: true,
      qualifications: true,
    },
    take: 3,
  });

  console.log('   🎯 Sample members with qualifications:');
  membersWithQualifications.forEach(member => {
    console.log(`      ${member.name} (${member.role}): ${JSON.stringify(member.qualifications)}`);
  });
}

// Run the test if this script is executed directly
if (require.main === module) {
  testEnhancedMemberManagement()
    .then(() => {
      console.log('\n🎉 Enhanced Member Management test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Enhanced Member Management test failed:', error);
      process.exit(1);
    });
}

module.exports = { testEnhancedMemberManagement };
