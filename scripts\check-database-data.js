#!/usr/bin/env node

/**
 * Database Data Comparison Script
 * Checks current PostgreSQL database data and compares with expected MySQL data
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabaseData() {
  console.log('🔍 Checking current PostgreSQL database data...\n');

  try {
    // Check congregations
    console.log('📊 CONGREGATIONS:');
    const congregations = await prisma.congregation.findMany();
    console.log(`Found ${congregations.length} congregations:`);
    congregations.forEach(cong => {
      console.log(`  - ID: ${cong.id}, Name: ${cong.name}, Region: ${cong.region}`);
    });
    console.log();

    // Check members
    console.log('👥 MEMBERS:');
    const members = await prisma.member.findMany({
      include: {
        congregation: true
      }
    });
    console.log(`Found ${members.length} members:`);
    members.forEach(member => {
      console.log(`  - ID: ${member.id}, Name: ${member.name}, Role: ${member.role}, Congregation: ${member.congregation?.name || 'N/A'}`);
    });
    console.log();

    // Check letters
    console.log('📄 LETTERS:');
    const letters = await prisma.letter.findMany();
    console.log(`Found ${letters.length} letters:`);
    letters.forEach(letter => {
      console.log(`  - ID: ${letter.id}, Title: ${letter.title}, Category: ${letter.category}`);
    });
    console.log();

    // Check tasks
    console.log('📋 TASKS:');
    const tasks = await prisma.task.findMany();
    console.log(`Found ${tasks.length} tasks:`);
    tasks.forEach(task => {
      console.log(`  - ID: ${task.id}, Title: ${task.title}, Category: ${task.category}`);
    });
    console.log();

    // Check midweek meetings
    console.log('🏛️ MIDWEEK MEETINGS:');
    const midweekMeetings = await prisma.midweekMeeting.findMany();
    console.log(`Found ${midweekMeetings.length} midweek meetings:`);
    midweekMeetings.forEach(meeting => {
      console.log(`  - ID: ${meeting.id}, Date: ${meeting.meetingDate}, Chairman: ${meeting.chairman}`);
    });
    console.log();

    // Check songs
    console.log('🎵 SONGS:');
    const songs = await prisma.song.findMany({
      take: 10 // Just show first 10
    });
    console.log(`Found ${songs.length} songs (showing first 10):`);
    songs.forEach(song => {
      console.log(`  - Number: ${song.songNumber}, Title ES: ${song.titleEs}`);
    });
    console.log();

    // Check service groups
    console.log('👨‍👩‍👧‍👦 SERVICE GROUPS:');
    const serviceGroups = await prisma.serviceGroup.findMany();
    console.log(`Found ${serviceGroups.length} service groups:`);
    serviceGroups.forEach(group => {
      console.log(`  - ID: ${group.id}, Name: ${group.name}, Number: ${group.groupNumber}`);
    });
    console.log();

    // Check elder permissions
    console.log('🔐 ELDER PERMISSIONS:');
    const elderPermissions = await prisma.elderPermission.findMany();
    console.log(`Found ${elderPermissions.length} elder permissions:`);
    elderPermissions.forEach(perm => {
      console.log(`  - Member ID: ${perm.memberId}, Section: ${perm.section}, Can Access: ${perm.canAccess}`);
    });
    console.log();

    console.log('✅ Database data check completed!');

  } catch (error) {
    console.error('❌ Error checking database data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkDatabaseData();
