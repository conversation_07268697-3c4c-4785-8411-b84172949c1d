/**
 * Assignment Card Component
 *
 * Displays individual section assignments with member information,
 * assignment details, and management actions.
 */

import React from 'react';
import { SectionAssignmentData } from '@/lib/services/sectionAssignmentService';
import { getAdministrativeSection } from '@/lib/constants/administrativeSections';

interface AssignmentCardProps {
  assignment: SectionAssignmentData;
  onRemove: (assignmentId: string) => void;
  onTransfer: (assignment: SectionAssignmentData) => void;
  canManage: boolean;
}

const getRoleDisplayName = (role: string): string => {
  const roleNames = {
    'elder': '<PERSON><PERSON><PERSON>',
    'ministerial_servant': 'Siervo Ministerial',
    'overseer_coordinator': 'Superintendente/Coordinador',
  };
  return roleNames[role as keyof typeof roleNames] || role;
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date));
};

export default function AssignmentCard({
  assignment,
  onRemove,
  onTransfer,
  canManage,
}: AssignmentCardProps) {
  const section = getAdministrativeSection(assignment.sectionType);

  if (!section) {
    return null;
  }

  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    green: 'bg-green-50 border-green-200 text-green-800',
    orange: 'bg-orange-50 border-orange-200 text-orange-800',
    indigo: 'bg-indigo-50 border-indigo-200 text-indigo-800',
    pink: 'bg-pink-50 border-pink-200 text-pink-800',
    yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  };

  const cardClass = colorClasses[section.color as keyof typeof colorClasses] ||
    'bg-gray-50 border-gray-200 text-gray-800';

  return (
    <div className={`border-2 rounded-lg p-6 ${cardClass}`}>
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold mb-1">
            {section.name}
          </h3>
          <p className="text-sm opacity-75">
            {section.description}
          </p>
        </div>
        {canManage && (
          <div className="flex space-x-2">
            <button
              onClick={() => onTransfer(assignment)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              title="Transferir asignación"
            >
              Transferir
            </button>
            <button
              onClick={() => onRemove(assignment.id)}
              className="text-red-600 hover:text-red-800 text-sm font-medium"
              title="Remover asignación"
            >
              Remover
            </button>
          </div>
        )}
      </div>

      {/* Member Information */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="font-medium text-base">
            {assignment.memberName}
          </span>
          <span className="text-sm px-2 py-1 bg-white bg-opacity-50 rounded">
            {getRoleDisplayName(assignment.memberRole)}
          </span>
        </div>

        {/* Assignment Details */}
        <div className="text-sm space-y-1">
          <div className="flex justify-between">
            <span className="opacity-75">Asignado:</span>
            <span>{formatDate(assignment.assignedAt)}</span>
          </div>
          {assignment.assignedByName && (
            <div className="flex justify-between">
              <span className="opacity-75">Por:</span>
              <span>{assignment.assignedByName}</span>
            </div>
          )}
        </div>
      </div>

      {/* Scope Definition */}
      {assignment.scopeDefinition && (
        <div className="border-t border-white border-opacity-30 pt-4">
          <h4 className="text-sm font-medium mb-2">Alcance de la Asignación</h4>

          {assignment.scopeDefinition.description && (
            <p className="text-sm mb-2 opacity-90">
              {assignment.scopeDefinition.description}
            </p>
          )}

          {assignment.scopeDefinition.specificAreas &&
           assignment.scopeDefinition.specificAreas.length > 0 && (
            <div className="mb-2">
              <span className="text-sm font-medium">Áreas específicas:</span>
              <ul className="text-sm mt-1 space-y-1">
                {assignment.scopeDefinition.specificAreas.map((area, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-1 h-1 bg-current rounded-full mr-2"></span>
                    {area}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {assignment.scopeDefinition.limitations &&
           assignment.scopeDefinition.limitations.length > 0 && (
            <div className="mb-2">
              <span className="text-sm font-medium">Limitaciones:</span>
              <ul className="text-sm mt-1 space-y-1">
                {assignment.scopeDefinition.limitations.map((limitation, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-1 h-1 bg-current rounded-full mr-2"></span>
                    {limitation}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {assignment.scopeDefinition.expirationDate && (
            <div className="text-sm">
              <span className="font-medium">Vence:</span>
              <span className="ml-2">
                {formatDate(new Date(assignment.scopeDefinition.expirationDate))}
              </span>
            </div>
          )}

          {assignment.scopeDefinition.notes && (
            <div className="mt-2 text-sm">
              <span className="font-medium">Notas:</span>
              <p className="mt-1 opacity-90">
                {assignment.scopeDefinition.notes}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
