/**
 * Events Summary API Endpoint
 * 
 * Provides event summary data for dashboard and overview displays.
 * Includes upcoming events, event statistics, and quick insights.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { EventManagementService } from '@/lib/services/eventManagementService';

/**
 * GET /api/events/summary
 * Get event summary and upcoming events for dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const upcomingLimit = parseInt(searchParams.get('upcomingLimit') || '10', 10);

    // Get event summary and upcoming events
    const [summary, upcomingEvents] = await Promise.all([
      EventManagementService.getEventSummary(member.congregationId),
      EventManagementService.getUpcomingEvents(member.congregationId, upcomingLimit),
    ]);

    return NextResponse.json({
      success: true,
      summary,
      upcomingEvents,
      count: upcomingEvents.length,
    });

  } catch (error) {
    console.error('Events summary error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve events summary',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
