--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.5

-- Started on 2025-07-25 01:37:56

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 5 (class 2615 OID 99250)
-- Name: public; Type: SCHEMA; Schema: -; Owner: mywebsites
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO mywebsites;

--
-- TOC entry 5364 (class 0 OID 0)
-- Dependencies: 5
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: mywebsites
--

COMMENT ON SCHEMA public IS '';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 217 (class 1259 OID 99251)
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO mywebsites;

--
-- TOC entry 251 (class 1259 OID 111470)
-- Name: account_lockouts; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.account_lockouts (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text NOT NULL,
    lockout_reason character varying(100) NOT NULL,
    attempt_count integer DEFAULT 0 NOT NULL,
    locked_at timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    unlock_at timestamp(6) with time zone NOT NULL,
    unlocked_by text,
    unlocked_at timestamp(6) with time zone,
    is_active boolean DEFAULT true NOT NULL,
    ip_address character varying(45),
    user_agent text,
    created_at timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.account_lockouts OWNER TO mywebsites;

--
-- TOC entry 224 (class 1259 OID 99324)
-- Name: assignment_history; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.assignment_history (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text NOT NULL,
    section_type character varying(100) NOT NULL,
    action character varying(50) NOT NULL,
    assigned_by text,
    assigned_to text,
    reason text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.assignment_history OWNER TO mywebsites;

--
-- TOC entry 243 (class 1259 OID 103397)
-- Name: communication_preferences; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.communication_preferences (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text NOT NULL,
    email_notifications boolean DEFAULT true NOT NULL,
    sms_notifications boolean DEFAULT false NOT NULL,
    in_app_notifications boolean DEFAULT true NOT NULL,
    quiet_hours_start character varying(10),
    quiet_hours_end character varying(10),
    category_preferences json DEFAULT '{}'::json NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.communication_preferences OWNER TO mywebsites;

--
-- TOC entry 244 (class 1259 OID 103409)
-- Name: communication_templates; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.communication_templates (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    name character varying(255) NOT NULL,
    title character varying(255) NOT NULL,
    message text NOT NULL,
    category character varying(50) NOT NULL,
    variables text[] DEFAULT ARRAY[]::text[],
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.communication_templates OWNER TO mywebsites;

--
-- TOC entry 246 (class 1259 OID 105922)
-- Name: congregation_settings; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.congregation_settings (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    setting_key character varying(100) NOT NULL,
    setting_value text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.congregation_settings OWNER TO mywebsites;

--
-- TOC entry 219 (class 1259 OID 99269)
-- Name: congregations; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.congregations (
    id character varying(8) NOT NULL,
    name character varying(255) NOT NULL,
    region character varying(50),
    pin character varying(255) NOT NULL,
    language character varying(5) DEFAULT 'es'::character varying NOT NULL,
    timezone character varying(50) DEFAULT 'America/Mexico_City'::character varying NOT NULL,
    settings jsonb DEFAULT '{}'::jsonb NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.congregations OWNER TO mywebsites;

--
-- TOC entry 239 (class 1259 OID 103359)
-- Name: document_access_logs; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.document_access_logs (
    id text NOT NULL,
    document_id text NOT NULL,
    member_id text NOT NULL,
    "accessType" character varying(20) NOT NULL,
    ip_address character varying(45),
    user_agent text,
    accessed_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.document_access_logs OWNER TO mywebsites;

--
-- TOC entry 240 (class 1259 OID 103367)
-- Name: document_comments; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.document_comments (
    id text NOT NULL,
    document_id text NOT NULL,
    member_id text NOT NULL,
    parent_id text,
    content text NOT NULL,
    is_internal boolean DEFAULT false NOT NULL,
    is_resolved boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.document_comments OWNER TO mywebsites;

--
-- TOC entry 238 (class 1259 OID 103348)
-- Name: document_folders; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.document_folders (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    parent_id text,
    path text NOT NULL,
    color character varying(20),
    icon character varying(50),
    visibility character varying(50) DEFAULT 'ALL_MEMBERS'::character varying NOT NULL,
    sort_order integer DEFAULT 0 NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.document_folders OWNER TO mywebsites;

--
-- TOC entry 241 (class 1259 OID 103377)
-- Name: document_workflows; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.document_workflows (
    id text NOT NULL,
    document_id text NOT NULL,
    "workflowType" character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    assigned_to_id text,
    assigned_by_id text NOT NULL,
    priority character varying(20) DEFAULT 'NORMAL'::character varying NOT NULL,
    due_date date,
    comments text,
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.document_workflows OWNER TO mywebsites;

--
-- TOC entry 222 (class 1259 OID 99302)
-- Name: elder_permissions; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.elder_permissions (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    assigned_at timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    assigned_by text NOT NULL,
    expiration_date timestamp(6) with time zone,
    is_active boolean DEFAULT true NOT NULL,
    notes text,
    permissions jsonb DEFAULT '[]'::jsonb NOT NULL,
    section_id character varying(100) NOT NULL
);


ALTER TABLE public.elder_permissions OWNER TO mywebsites;

--
-- TOC entry 233 (class 1259 OID 99412)
-- Name: events; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.events (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    event_date date NOT NULL,
    start_time character varying(10),
    end_time character varying(10),
    location character varying(255),
    category character varying(100) NOT NULL,
    is_all_day boolean DEFAULT false NOT NULL,
    is_recurring boolean DEFAULT false NOT NULL,
    recurrence_rule text,
    visibility character varying(50) DEFAULT 'ALL_MEMBERS'::character varying NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.events OWNER TO mywebsites;

--
-- TOC entry 231 (class 1259 OID 99388)
-- Name: field_service_records; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.field_service_records (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text NOT NULL,
    service_month date NOT NULL,
    hours numeric(5,2),
    placements integer DEFAULT 0,
    video_showings integer DEFAULT 0,
    return_visits integer DEFAULT 0,
    bible_studies integer DEFAULT 0,
    notes text,
    is_submitted boolean DEFAULT false NOT NULL,
    submitted_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.field_service_records OWNER TO mywebsites;

--
-- TOC entry 218 (class 1259 OID 99260)
-- Name: health_checks; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.health_checks (
    id text NOT NULL,
    status text DEFAULT 'ok'::text NOT NULL,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.health_checks OWNER TO mywebsites;

--
-- TOC entry 232 (class 1259 OID 99401)
-- Name: letters; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.letters (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    title character varying(255) NOT NULL,
    filename character varying(255) NOT NULL,
    file_path text NOT NULL,
    file_size integer,
    mime_type character varying(100),
    category character varying(100),
    visibility character varying(50) DEFAULT 'ALL_MEMBERS'::character varying NOT NULL,
    upload_date date DEFAULT CURRENT_TIMESTAMP NOT NULL,
    uploaded_by_id text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    approved_at timestamp with time zone,
    approved_by_id text,
    description text,
    download_count integer DEFAULT 0 NOT NULL,
    expiration_date date,
    folder_id text,
    parent_id text,
    priority character varying(20) DEFAULT 'NORMAL'::character varying NOT NULL,
    publish_date date,
    searchable_text text,
    status character varying(20) DEFAULT 'ACTIVE'::character varying NOT NULL,
    subcategory character varying(100),
    tags text[] DEFAULT ARRAY[]::text[],
    thumbnail_path text,
    version integer DEFAULT 1 NOT NULL,
    view_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.letters OWNER TO mywebsites;

--
-- TOC entry 235 (class 1259 OID 99579)
-- Name: member_change_history; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.member_change_history (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text NOT NULL,
    changed_by text NOT NULL,
    change_type character varying(50) NOT NULL,
    field_name character varying(100),
    old_value text,
    new_value text,
    reason text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.member_change_history OWNER TO mywebsites;

--
-- TOC entry 221 (class 1259 OID 99291)
-- Name: members; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.members (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    name character varying(255) NOT NULL,
    email character varying(255),
    role character varying(50) DEFAULT 'publisher'::character varying NOT NULL,
    pin character varying(255) NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    last_login timestamp with time zone,
    preferences jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    address text,
    birth_date date,
    contact_preferences jsonb DEFAULT '{}'::jsonb,
    notes text,
    phone character varying(20),
    qualifications jsonb DEFAULT '[]'::jsonb,
    service_group character varying(100)
);


ALTER TABLE public.members OWNER TO mywebsites;

--
-- TOC entry 226 (class 1259 OID 99342)
-- Name: midweek_meeting_parts; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.midweek_meeting_parts (
    id text NOT NULL,
    meeting_id text NOT NULL,
    part_number integer NOT NULL,
    part_type character varying(100) NOT NULL,
    title character varying(255) NOT NULL,
    assigned_member character varying(255),
    assistant character varying(255),
    time_allocation integer,
    notes text,
    is_completed boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.midweek_meeting_parts OWNER TO mywebsites;

--
-- TOC entry 225 (class 1259 OID 99332)
-- Name: midweek_meetings; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.midweek_meetings (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    meeting_date date NOT NULL,
    chairman character varying(255),
    opening_prayer character varying(255),
    closing_prayer character varying(255),
    location character varying(100) DEFAULT 'Kingdom Hall'::character varying NOT NULL,
    zoom_link text,
    notes text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.midweek_meetings OWNER TO mywebsites;

--
-- TOC entry 242 (class 1259 OID 103386)
-- Name: notifications; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.notifications (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    recipient_id text NOT NULL,
    sender_id text,
    title character varying(255) NOT NULL,
    message text NOT NULL,
    category character varying(50) NOT NULL,
    priority character varying(20) DEFAULT 'NORMAL'::character varying NOT NULL,
    delivery_method text[],
    status character varying(20) DEFAULT 'DELIVERED'::character varying NOT NULL,
    scheduled_for timestamp with time zone,
    delivered_at timestamp with time zone,
    read_at timestamp with time zone,
    metadata json,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.notifications OWNER TO mywebsites;

--
-- TOC entry 249 (class 1259 OID 108618)
-- Name: permission_audit_logs; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.permission_audit_logs (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    user_id text NOT NULL,
    action character varying(50) NOT NULL,
    section_id character varying(100) NOT NULL,
    permissions jsonb DEFAULT '[]'::jsonb NOT NULL,
    performed_by text NOT NULL,
    reason text,
    ip_address character varying(45),
    user_agent text,
    "timestamp" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_at timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.permission_audit_logs OWNER TO mywebsites;

--
-- TOC entry 237 (class 1259 OID 101705)
-- Name: pin_change_history; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.pin_change_history (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text,
    changed_by text NOT NULL,
    change_type character varying(50) NOT NULL,
    old_pin_hash character varying(255),
    new_pin_hash character varying(255),
    reason text,
    ip_address character varying(45),
    user_agent text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.pin_change_history OWNER TO mywebsites;

--
-- TOC entry 236 (class 1259 OID 101689)
-- Name: pin_settings; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.pin_settings (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    min_length integer DEFAULT 4 NOT NULL,
    max_length integer DEFAULT 8 NOT NULL,
    require_numeric boolean DEFAULT true NOT NULL,
    require_alphanumeric boolean DEFAULT false NOT NULL,
    require_special_chars boolean DEFAULT false NOT NULL,
    allow_sequential boolean DEFAULT true NOT NULL,
    allow_repeated boolean DEFAULT true NOT NULL,
    expiration_days integer,
    bcrypt_rounds integer DEFAULT 12 NOT NULL,
    created_by text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    lockout_duration_minutes integer DEFAULT 30 NOT NULL,
    max_attempts integer DEFAULT 5 NOT NULL,
    prevent_reuse_count integer DEFAULT 3 NOT NULL,
    temporary_pin_expiration_hours integer DEFAULT 24 NOT NULL
);


ALTER TABLE public.pin_settings OWNER TO mywebsites;

--
-- TOC entry 220 (class 1259 OID 99281)
-- Name: roles; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.roles (
    id text NOT NULL,
    name character varying(50) NOT NULL,
    description text,
    permissions jsonb DEFAULT '[]'::jsonb NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.roles OWNER TO mywebsites;

--
-- TOC entry 223 (class 1259 OID 99313)
-- Name: section_assignments; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.section_assignments (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text NOT NULL,
    section_type character varying(100) NOT NULL,
    scope_definition jsonb DEFAULT '{}'::jsonb NOT NULL,
    assigned_by text,
    assigned_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.section_assignments OWNER TO mywebsites;

--
-- TOC entry 252 (class 1259 OID 111481)
-- Name: security_audit_events; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.security_audit_events (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text,
    event_type character varying(100) NOT NULL,
    success boolean NOT NULL,
    ip_address character varying(45),
    user_agent text,
    details jsonb,
    performed_by text,
    "timestamp" timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_at timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.security_audit_events OWNER TO mywebsites;

--
-- TOC entry 247 (class 1259 OID 105930)
-- Name: service_groups; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.service_groups (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    name character varying(100) NOT NULL,
    group_number integer NOT NULL,
    description text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.service_groups OWNER TO mywebsites;

--
-- TOC entry 234 (class 1259 OID 99424)
-- Name: songs; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.songs (
    id text NOT NULL,
    song_number integer NOT NULL,
    title_es character varying(255),
    title_en character varying(255),
    category character varying(100),
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.songs OWNER TO mywebsites;

--
-- TOC entry 245 (class 1259 OID 105912)
-- Name: special_songs; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.special_songs (
    id text NOT NULL,
    key_name character varying(50) NOT NULL,
    title_es character varying(255) NOT NULL,
    title_en character varying(255),
    is_custom boolean DEFAULT false NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.special_songs OWNER TO mywebsites;

--
-- TOC entry 230 (class 1259 OID 99379)
-- Name: task_assignments; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.task_assignments (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    task_id text NOT NULL,
    assigned_member_id text,
    assigned_date date NOT NULL,
    due_date date,
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    notes text,
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.task_assignments OWNER TO mywebsites;

--
-- TOC entry 229 (class 1259 OID 99370)
-- Name: tasks; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.tasks (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    frequency character varying(50) NOT NULL,
    estimated_time integer,
    instructions text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.tasks OWNER TO mywebsites;

--
-- TOC entry 250 (class 1259 OID 111459)
-- Name: temporary_pins; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.temporary_pins (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    member_id text NOT NULL,
    temporary_pin_hash character varying(255) NOT NULL,
    reset_type character varying(50) DEFAULT 'temporary'::character varying NOT NULL,
    created_by text NOT NULL,
    reason text,
    expiration_date timestamp(6) with time zone NOT NULL,
    require_change boolean DEFAULT true NOT NULL,
    used boolean DEFAULT false NOT NULL,
    used_at timestamp(6) with time zone,
    ip_address character varying(45),
    user_agent text,
    created_at timestamp(6) with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(6) with time zone NOT NULL
);


ALTER TABLE public.temporary_pins OWNER TO mywebsites;

--
-- TOC entry 248 (class 1259 OID 105939)
-- Name: territories; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.territories (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    status character varying(20) DEFAULT 'available'::character varying NOT NULL,
    assigned_to_id text,
    assigned_at timestamp with time zone,
    completed_at timestamp with time zone,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.territories OWNER TO mywebsites;

--
-- TOC entry 228 (class 1259 OID 99361)
-- Name: weekend_meeting_parts; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.weekend_meeting_parts (
    id text NOT NULL,
    meeting_id text NOT NULL,
    part_type character varying(100) NOT NULL,
    assigned_member character varying(255),
    notes text,
    is_completed boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.weekend_meeting_parts OWNER TO mywebsites;

--
-- TOC entry 227 (class 1259 OID 99351)
-- Name: weekend_meetings; Type: TABLE; Schema: public; Owner: mywebsites
--

CREATE TABLE public.weekend_meetings (
    id text NOT NULL,
    congregation_id character varying(8) NOT NULL,
    meeting_date date NOT NULL,
    public_talk_title character varying(255),
    public_talk_speaker character varying(255),
    speaker_congregation character varying(255),
    watchtower_conductor character varying(255),
    watchtower_reader character varying(255),
    chairman character varying(255),
    opening_prayer character varying(255),
    closing_prayer character varying(255),
    location character varying(100) DEFAULT 'Kingdom Hall'::character varying NOT NULL,
    zoom_link text,
    notes text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.weekend_meetings OWNER TO mywebsites;

--
-- TOC entry 5323 (class 0 OID 99251)
-- Dependencies: 217
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
fde151dd-87de-4936-b690-5f707ddce943	12d58a1a8cbe171d8906df046b70650314a89758b34d9896d56a12fdbeef1e63	2025-07-23 10:40:44.284289-04	20250723140926_add_section_assignments	\N	\N	2025-07-23 10:40:44.157254-04	1
653a82a8-50bd-460d-a00e-002b88f65e4d	ab9b251c3f09123301d01af1ca4f3c696f4b909e4eca3e720c2a5b6083f8919b	2025-07-23 10:40:44.299913-04	20250723142911_add_member_change_history	\N	\N	2025-07-23 10:40:44.285286-04	1
50fdcc45-be36-404f-ac80-e3480e1818d5	f2864a6247a833d348f706175f608ed5d83851c1a70a63ba4e11ee90f52b31d6	2025-07-23 10:40:44.305131-04	20250723143842_add_email_unique_constraint	\N	\N	2025-07-23 10:40:44.301073-04	1
70c31f01-c040-4aa2-8341-42ea823350f0	96881c31af2e4a6d08f855fc5be57186d6d281b1540d2915b172ca3f7b6767da	2025-07-23 10:51:11.605078-04	20250723145111_add_pin_management_tables	\N	\N	2025-07-23 10:51:11.586119-04	1
71fe01e2-0cad-4cc6-88b4-0988691c0ed3	fa747d2fce0c41c17a0f879497d34f3249ff92e8baaa32e882b072214670adae	2025-07-24 00:44:17.449645-04	20250724044417_init	\N	\N	2025-07-24 00:44:17.378629-04	1
1a0112c2-3147-4969-b99f-33096bd8b04e	7463a0ad3dd0aac18bdd84c4807203321727e206ca479f85516152c753cae33a	2025-07-24 00:59:36.292156-04	20250724045936_add_missing_tables	\N	\N	2025-07-24 00:59:36.257334-04	1
cbb5120a-0c89-4c8a-b90d-a5703dcc43d8	989bfeb91fc430a29bba6625dc69cd1040b09ab523c9069c5781b3ef98cd0d9d	2025-07-24 09:42:22.872887-04	20250724134222_enhance_elder_permissions_for_delegation	\N	\N	2025-07-24 09:42:22.830291-04	1
0c0bf29c-51d3-40e8-ab20-fdfef58fc554	80a2faa9a3e7656bf2e96d128f093f31ac87c613e8078df962293cfeb2a93cfe	2025-07-24 10:46:02.349131-04	20250724144602_enhance_member_profile_for_story_2_2	\N	\N	2025-07-24 10:46:02.331925-04	1
\.


--
-- TOC entry 5357 (class 0 OID 111470)
-- Dependencies: 251
-- Data for Name: account_lockouts; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.account_lockouts (id, congregation_id, member_id, lockout_reason, attempt_count, locked_at, unlock_at, unlocked_by, unlocked_at, is_active, ip_address, user_agent, created_at, updated_at) FROM stdin;
cmdhxikmo0003g4rhxuyooqvl	1441	5	failed_attempts	5	2025-07-24 17:53:19.345-04	2025-07-24 18:23:19.343-04	\N	\N	t	127.0.0.1	Test Script	2025-07-24 17:53:19.345-04	2025-07-24 17:53:19.345-04
cmdhxk0ak000311prnv13cut4	1441	11	failed_attempts	5	2025-07-24 17:54:26.3-04	2025-07-24 18:24:26.299-04	\N	\N	t	127.0.0.1	Test Script	2025-07-24 17:54:26.3-04	2025-07-24 17:54:26.3-04
\.


--
-- TOC entry 5330 (class 0 OID 99324)
-- Dependencies: 224
-- Data for Name: assignment_history; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.assignment_history (id, congregation_id, member_id, section_type, action, assigned_by, assigned_to, reason, created_at) FROM stdin;
\.


--
-- TOC entry 5349 (class 0 OID 103397)
-- Dependencies: 243
-- Data for Name: communication_preferences; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.communication_preferences (id, congregation_id, member_id, email_notifications, sms_notifications, in_app_notifications, quiet_hours_start, quiet_hours_end, category_preferences, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5350 (class 0 OID 103409)
-- Dependencies: 244
-- Data for Name: communication_templates; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.communication_templates (id, congregation_id, name, title, message, category, variables, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5352 (class 0 OID 105922)
-- Dependencies: 246
-- Data for Name: congregation_settings; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.congregation_settings (id, congregation_id, setting_key, setting_value, created_at, updated_at) FROM stdin;
cmdh998p70006tea05sfviiqa	1441	congregation_name	Coral Oeste	2025-07-24 06:34:13.194-04	2025-07-24 06:34:57.716-04
cmdh998pb000atea0qygscz92	1441	number_of_groups	7	2025-07-24 06:34:13.198-04	2025-07-24 06:34:57.718-04
cmdh998pb000ctea0hv9dw7um	1441	language	es	2025-07-24 06:34:13.199-04	2025-07-24 06:34:57.719-04
cmdh998pd000etea0cxnzego2	1441	timezone	America/New_York	2025-07-24 06:34:13.2-04	2025-07-24 06:34:57.72-04
cmdhs785d0009o2jm4pb1f5dm	1441	midweek_day	Thursday	2025-07-24 15:24:31.873-04	2025-07-24 15:46:19.428-04
cmdhs785e000bo2jm4kacgoca	1441	midweek_time	7:00 PM	2025-07-24 15:24:31.875-04	2025-07-24 15:46:19.432-04
cmdhs785g000do2jm87cra5nj	1441	weekend_day	Sunday	2025-07-24 15:24:31.877-04	2025-07-24 15:46:19.434-04
cmdhs785j000fo2jm1cjkmgbt	1441	weekend_time	10:00 AM	2025-07-24 15:24:31.879-04	2025-07-24 15:46:19.435-04
cmdhs785m000ho2jm7l0srt4d	1441	default_congregation_id	1441	2025-07-24 15:24:31.882-04	2025-07-24 15:46:19.436-04
cmdhs784s0001o2jm1733klwn	1441	congregation_number	1441	2025-07-24 15:24:31.852-04	2025-07-24 15:55:15.722-04
cmdhs78580005o2jm7lz40gpj	1441	circuit_number	23	2025-07-24 15:24:31.869-04	2025-07-24 15:55:15.725-04
cmdhs785b0007o2jmq5znuyac	1441	circuit_overseer	Elvis Avalo	2025-07-24 15:24:31.871-04	2025-07-24 15:55:15.728-04
cmdhsz9290009l0274384wwq1	1441	address	 1480 SW 69th Ave, Miami, FL 33144	2025-07-24 15:46:19.425-04	2025-07-24 15:55:15.731-04
cmdh998p90008tea02gm6lfst	1441	congregation_pin	1930	2025-07-24 06:34:13.197-04	2025-07-24 15:55:15.735-04
cmdhs785o000jo2jmnq2m38ca	1441	default_congregation_pin	1930	2025-07-24 15:24:31.884-04	2025-07-24 15:55:15.738-04
\.


--
-- TOC entry 5325 (class 0 OID 99269)
-- Dependencies: 219
-- Data for Name: congregations; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.congregations (id, name, region, pin, language, timezone, settings, is_active, created_at, updated_at) FROM stdin;
1441	Coral Oeste	North America	$2a$12$pA6bAbsifSWflfFbZilMYOAeFyKl/OGOeCuiELE1YmBnMHEx4osVm	es	America/New_York	{}	t	2025-07-23 20:02:32.703-04	2025-07-24 15:55:15.719-04
\.


--
-- TOC entry 5345 (class 0 OID 103359)
-- Dependencies: 239
-- Data for Name: document_access_logs; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.document_access_logs (id, document_id, member_id, "accessType", ip_address, user_agent, accessed_at) FROM stdin;
\.


--
-- TOC entry 5346 (class 0 OID 103367)
-- Dependencies: 240
-- Data for Name: document_comments; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.document_comments (id, document_id, member_id, parent_id, content, is_internal, is_resolved, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5344 (class 0 OID 103348)
-- Dependencies: 238
-- Data for Name: document_folders; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.document_folders (id, congregation_id, name, description, parent_id, path, color, icon, visibility, sort_order, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5347 (class 0 OID 103377)
-- Dependencies: 241
-- Data for Name: document_workflows; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.document_workflows (id, document_id, "workflowType", status, assigned_to_id, assigned_by_id, priority, due_date, comments, completed_at, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5328 (class 0 OID 99302)
-- Dependencies: 222
-- Data for Name: elder_permissions; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.elder_permissions (id, congregation_id, member_id, created_at, updated_at, assigned_at, assigned_by, expiration_date, is_active, notes, permissions, section_id) FROM stdin;
\.


--
-- TOC entry 5339 (class 0 OID 99412)
-- Dependencies: 233
-- Data for Name: events; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.events (id, congregation_id, title, description, event_date, start_time, end_time, location, category, is_all_day, is_recurring, recurrence_rule, visibility, is_active, created_at, updated_at) FROM stdin;
cmdi5gzzu0001oz90gqto4cg2	1441	ASAMBLEA REGIONAL	Asamblea regional anual con el tema "Mantengámonos Firmes en la Fe". Se espera la asistencia de todos los circuitos de la región.	2025-06-15	09:30	16:00	Centro de Convenciones de Miami	ASSEMBLY	f	f	\N	ALL_MEMBERS	t	2025-07-24 21:36:02.874-04	2025-07-24 21:36:02.874-04
cmdi5h0040003oz90tw0l9rp2	1441	VISITA DEL SUPERINTENDENTE DE CIRCUITO	Visita del superintendente de circuito. Reunión especial con los ancianos y siervos ministeriales.	2025-05-10	19:30	21:00	Salón del Reino local	SPECIAL_MEETING	f	f	\N	ALL_MEMBERS	t	2025-07-24 21:36:02.884-04	2025-07-24 21:36:02.884-04
cmdi5h0060005oz90201p4shv	1441	ASAMBLEA DE CIRCUITO CON EL SUPERINTENDENTE DE CIRCUITO	Asamblea de circuito con presentaciones especiales y programa de entrenamiento.	2025-03-29	09:00	16:30	Salón de Asambleas de West Palm Beach	ASSEMBLY	f	f	\N	ALL_MEMBERS	t	2025-07-24 21:36:02.886-04	2025-07-24 21:36:02.886-04
cmdi5h0080007oz90ay8xsm1q	1441	Actividad de Servicio Especial	Actividad especial de predicación en territorio comercial.	2025-02-15	09:00	12:00	Centro Comercial Dadeland	SERVICE_ACTIVITY	f	f	\N	ALL_MEMBERS	t	2025-07-24 21:36:02.888-04	2025-07-24 21:36:02.888-04
cmdi5h00b0009oz9053vjd6hz	1441	Reunión de Entrenamiento para Ancianos	Reunión especial de entrenamiento para el cuerpo de ancianos.	2025-02-08	14:00	17:00	Salón del Reino local	TRAINING	f	f	\N	ELDERS_ONLY	t	2025-07-24 21:36:02.891-04	2025-07-24 21:36:02.891-04
\.


--
-- TOC entry 5337 (class 0 OID 99388)
-- Dependencies: 231
-- Data for Name: field_service_records; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.field_service_records (id, congregation_id, member_id, service_month, hours, placements, video_showings, return_visits, bible_studies, notes, is_submitted, submitted_at, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5324 (class 0 OID 99260)
-- Dependencies: 218
-- Data for Name: health_checks; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.health_checks (id, status, "timestamp") FROM stdin;
\.


--
-- TOC entry 5338 (class 0 OID 99401)
-- Dependencies: 232
-- Data for Name: letters; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.letters (id, congregation_id, title, filename, file_path, file_size, mime_type, category, visibility, upload_date, uploaded_by_id, is_active, created_at, updated_at, approved_at, approved_by_id, description, download_count, expiration_date, folder_id, parent_id, priority, publish_date, searchable_text, status, subcategory, tags, thumbnail_path, version, view_count) FROM stdin;
cmdh9a72l001ak22po3x4d3rd	1441	Vida y Ministerio Cristianos - Marzo 2019	vida_y_ministerio_cristianos_marzo_2019.pdf	/uploads/letters/vida_y_ministerio_cristianos_marzo_2019.pdf	\N	\N	Importante	ALL_MEMBERS	2019-02-28	\N	t	2025-07-24 06:34:57.739-04	2025-07-24 06:34:57.739-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdh9a72n001ck22p11fh8gyu	1441	Reserva de Hoteles para la Asamblea Regional	reserva_de_hoteles_para_la_asamblea_regional.pdf	/uploads/letters/reserva_de_hoteles_para_la_asamblea_regional.pdf	\N	\N	Anuncios	ALL_MEMBERS	2025-01-14	\N	t	2025-07-24 06:34:57.741-04	2025-07-24 06:34:57.741-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdh9a72o001ek22pbtlqs8gz	1441	Puntos a recordar al reservar hotel para la asamblea regional	puntos_a_recordar_al_reservar_hotel_para_la_asamblea_regional.pdf	/uploads/letters/puntos_a_recordar_al_reservar_hotel_para_la_asamblea_regional.pdf	\N	\N	Importante	ALL_MEMBERS	2025-01-19	\N	t	2025-07-24 06:34:57.742-04	2025-07-24 06:34:57.742-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdh9a72p001gk22ptq061m4h	1441	Asignacion Asamblea Regional 2025	asignacion_asamblea_regional_2025.pdf	/uploads/letters/asignacion_asamblea_regional_2025.pdf	\N	\N	Importante	ALL_MEMBERS	2025-01-31	\N	t	2025-07-24 06:34:57.743-04	2025-07-24 06:34:57.743-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdh9a72q001ik22pml2h761c	1441	Instruccion para la Reunion Vida y Ministerio Cristianos	1745602360146-CA-copgm25_E.pdf	/uploads/letters/1745602360146-CA-copgm25_E.pdf	\N	\N	General	ELDERS_ONLY	2025-03-14	\N	t	2025-07-24 06:34:57.744-04	2025-07-24 06:34:57.744-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdh9a72r001kk22p3grr73m6	1441	Pautas para la Predicacion Publica	1745602387368-mwb_S_201903.pdf	/uploads/letters/1745602387368-mwb_S_201903.pdf	\N	\N	General	ELDERS_ONLY	2025-02-28	\N	t	2025-07-24 06:34:57.745-04	2025-07-24 06:34:57.745-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdh9a72s001mk22p9xen8hmn	1441	Asambleas Especiales 2025	1745602457258-mwb_S_201903.pdf	/uploads/letters/1745602457258-mwb_S_201903.pdf	\N	\N	General	ELDERS_ONLY	2025-02-14	\N	t	2025-07-24 06:34:57.747-04	2025-07-24 06:34:57.747-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdha12rd0001p9w2zy2q2ymr	1441	Vida y Ministerio Cristianos - Marzo 2019	vida_y_ministerio_cristianos_marzo_2019.pdf	/uploads/letters/vida_y_ministerio_cristianos_marzo_2019.pdf	\N	\N	Importante	ALL_MEMBERS	2019-02-28	\N	t	2025-07-24 06:55:51.863-04	2025-07-24 06:55:51.863-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdha12rg0003p9w2krzc42nc	1441	Reserva de Hoteles para la Asamblea Regional	reserva_de_hoteles_para_la_asamblea_regional.pdf	/uploads/letters/reserva_de_hoteles_para_la_asamblea_regional.pdf	\N	\N	Anuncios	ALL_MEMBERS	2025-01-14	\N	t	2025-07-24 06:55:51.866-04	2025-07-24 06:55:51.866-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdha12rh0005p9w2zhrwg2bi	1441	Puntos a recordar al reservar hotel para la asamblea regional	puntos_a_recordar_al_reservar_hotel_para_la_asamblea_regional.pdf	/uploads/letters/puntos_a_recordar_al_reservar_hotel_para_la_asamblea_regional.pdf	\N	\N	Importante	ALL_MEMBERS	2025-01-19	\N	t	2025-07-24 06:55:51.868-04	2025-07-24 06:55:51.868-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdha12rj0007p9w2me78cmww	1441	Asignacion Asamblea Regional 2025	asignacion_asamblea_regional_2025.pdf	/uploads/letters/asignacion_asamblea_regional_2025.pdf	\N	\N	Importante	ALL_MEMBERS	2025-01-31	\N	t	2025-07-24 06:55:51.87-04	2025-07-24 06:55:51.87-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdha12rl0009p9w2ymavycg1	1441	Instruccion para la Reunion Vida y Ministerio Cristianos	1745602360146-CA-copgm25_E.pdf	/uploads/letters/1745602360146-CA-copgm25_E.pdf	\N	\N	General	ELDERS_ONLY	2025-03-14	\N	t	2025-07-24 06:55:51.872-04	2025-07-24 06:55:51.872-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdha12ro000bp9w2cne6sjxh	1441	Pautas para la Predicacion Publica	1745602387368-mwb_S_201903.pdf	/uploads/letters/1745602387368-mwb_S_201903.pdf	\N	\N	General	ELDERS_ONLY	2025-02-28	\N	t	2025-07-24 06:55:51.875-04	2025-07-24 06:55:51.875-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
cmdha12rq000dp9w214jxmsnq	1441	Asambleas Especiales 2025	1745602457258-mwb_S_201903.pdf	/uploads/letters/1745602457258-mwb_S_201903.pdf	\N	\N	General	ELDERS_ONLY	2025-02-14	\N	t	2025-07-24 06:55:51.877-04	2025-07-24 06:55:51.877-04	\N	\N	\N	0	\N	\N	\N	NORMAL	\N	\N	ACTIVE	\N	{}	\N	1	0
\.


--
-- TOC entry 5341 (class 0 OID 99579)
-- Dependencies: 235
-- Data for Name: member_change_history; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.member_change_history (id, congregation_id, member_id, changed_by, change_type, field_name, old_value, new_value, reason, created_at) FROM stdin;
cmdhqzl9n00053rrqgxw3rkfn	1441	3	1	deactivated	isActive	true	false	Member suspender from member management interface	2025-07-24 14:50:36.011-04
cmdhqzoyu00083rrq1y3hf17x	1441	3	1	reactivated	isActive	false	true	Member reactivar from member management interface	2025-07-24 14:50:40.806-04
cmdhr4lmg000b3rrqfv1fzlb5	1441	7	1	deactivated	isActive	true	false	Member suspender from member management interface	2025-07-24 14:54:29.753-04
cmdhr9qd8000e3rrq9srrz797	1441	17	1	deactivated	isActive	true	false	Member suspender from member management interface	2025-07-24 14:58:29.18-04
cmdhra0iz000h3rrq1ihhgotz	1441	17	1	reactivated	isActive	false	true	Member reactivar from member management interface	2025-07-24 14:58:42.347-04
cmdhramj0000k3rrqgyhsfwok	1441	17	1	deactivated	isActive	true	false	Member suspender from member management interface	2025-07-24 14:59:10.86-04
cmdhraqtv000n3rrqexpb1l0i	1441	17	1	reactivated	isActive	false	true	Member reactivar from member management interface	2025-07-24 14:59:16.435-04
cmdhrautu000q3rrquajzyjji	1441	20	1	deactivated	isActive	true	false	Member suspender from member management interface	2025-07-24 14:59:21.619-04
\.


--
-- TOC entry 5327 (class 0 OID 99291)
-- Dependencies: 221
-- Data for Name: members; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.members (id, congregation_id, name, email, role, pin, is_active, last_login, preferences, created_at, updated_at, address, birth_date, contact_preferences, notes, phone, qualifications, service_group) FROM stdin;
20	1441	Carlos De La Torre	<EMAIL>	ministerial_servant	$2a$12$WTDoaWgXCijdFDe1Gp2wKuQcF/OldeaXzZ7xj9HMCAt02pdxAu0d6	f	\N	{}	2025-07-24 06:34:57.669-04	2025-07-24 14:59:21.612-04	\N	\N	{}	\N	\N	[]	\N
19	1441	Eduardo Luscinian 	<EMAIL>	elder	$2a$12$ZbmR7GmuUm7MnjclE.fVwuJcbwmWWvq6wEqTSvGFI6JRxEHTurAsK	t	2025-07-25 01:07:40.744-04	{}	2025-07-24 06:34:57.667-04	2025-07-25 01:07:40.746-04	\N	\N	{}	\N	\N	[]	\N
4	1441	Lourdes Rubi	<EMAIL>	publisher	$2a$12$fZhbCXE7oqu06CYYcN.V1.Xv8vCtoMC2PzgE0RYna2fv16vUjXhui	t	\N	{}	2025-07-24 06:34:57.648-04	2025-07-24 17:45:56.598-04	\N	\N	{}	\N	\N	[]	\N
5	1441	Danay Valiente	<EMAIL>	publisher	$2a$12$wROCn6kFmpyw6KURw/BX1eb9PFV86iaXrwgszoQJeGxtK/jigXEUK	t	\N	{}	2025-07-24 06:34:57.649-04	2025-07-24 17:54:19.885-04	\N	\N	{}	\N	\N	[]	\N
1	1441	Richard Rubi	<EMAIL>	coordinator	$2a$12$SFMAt2u2zFqkMwkQevuW5edAWWYjY.pL9JVb2g8TBT5iF893E/dbi	t	2025-07-25 01:32:54.1-04	{}	2025-07-24 06:34:57.642-04	2025-07-25 01:32:54.102-04	\N	\N	{}	\N	\N	[]	\N
6	1441	Jorge Diaz	<EMAIL>	elder	$2a$12$BZ.yKrqcIV2FOEDkQ7mmJuz//CRi1Y0y0jBY3Wjn4PKTZZxNLk0HW	t	2025-07-24 23:40:28.22-04	{}	2025-07-24 06:34:57.659-04	2025-07-24 23:40:28.221-04	\N	\N	{}	\N	\N	[]	\N
2	1441	Horacio Cerda	<EMAIL>	elder	$2a$12$Z224mZEy4LGT61WlSNE0bOoXCLlYarrc/dkHikADxF9Db/akajR/u	t	2025-07-24 12:20:25.237-04	{}	2025-07-23 20:03:51.949-04	2025-07-24 12:20:25.238-04	\N	\N	{}	\N	\N	[]	\N
3	1441	Yoan Valiente	<EMAIL>	ministerial_servant	$2a$12$dTdDzNCsGkUW3X1hm4RK4uQ/MDBpgNFrh5RfinpAGSaOd.5WZauEG	t	\N	{}	2025-07-23 20:03:52.177-04	2025-07-24 14:50:40.802-04	\N	\N	{}	\N	\N	[]	\N
7	1441	Tania Diaz	<EMAIL>	publisher	$2a$12$NA2D5km80aSiXBZH3ac17ekD5i5a.YGigzlGGAMqHh6P03hQYVEJ6	f	\N	{}	2025-07-24 06:34:57.66-04	2025-07-24 14:54:29.748-04	\N	\N	{}	\N	\N	[]	\N
17	1441	Aysa Ramirez	<EMAIL>	publisher	$2a$12$HPHchYDqcK4r2ZkRCEUcPeVgR12d5QsI7T66ZxF4JI2l.UrKa11qi	t	\N	{}	2025-07-24 06:34:57.666-04	2025-07-24 14:59:16.43-04	\N	\N	{}	\N	\N	[]	\N
8	1441	James Rubi	<EMAIL>	elder	$2a$12$ONaqSeM9hdfAM4E9s7U62O14ttVR9/3mqT.2MJlFs2O.JY3PnSzm.	t	2025-07-24 23:42:52.628-04	{}	2025-07-23 20:03:52.627-04	2025-07-24 23:42:52.63-04	\N	\N	{}	\N	\N	[]	\N
10	1441	David Fernandez	<EMAIL>	elder	$2a$12$m/8w7vMpK7IDrJ6guTLw6.53IQt08BWA.7FiNcBqkim.E3w88d8Ca	t	2025-07-25 00:01:02.159-04	{}	2025-07-24 06:34:57.656-04	2025-07-25 00:01:02.16-04	\N	\N	{}	\N	\N	[]	\N
11	1441	Ester Fernandez	<EMAIL>	publisher	$2a$12$Hl1iPO9e6otFOFSR5LR2MOqu3GRathCBqrvSTvro59.dEgGC3YuZC	t	2025-07-25 00:01:33.016-04	{}	2025-07-24 06:34:57.657-04	2025-07-25 00:01:33.018-04	\N	\N	{}	\N	\N	[]	\N
12	1441	Javier Torrez	<EMAIL>	elder	$2a$12$m0HKYJyl2ckZthozzPmNZee7sdE6T304fGM5OmvnPEo.J5uuD6kRS	t	2025-07-25 00:01:54.706-04	{}	2025-07-24 06:34:57.661-04	2025-07-25 00:01:54.708-04	\N	\N	{}	\N	\N	[]	\N
13	1441	Jessi Torrez	<EMAIL>	elder	$2a$12$Te9.v8dG2kVqOz/reUCZre1wOIuWixTR6PDoJP/MgAUQbWxliiVFa	t	2025-07-25 00:11:27.23-04	{}	2025-07-24 06:34:57.662-04	2025-07-25 00:11:27.232-04	\N	\N	{}	\N	\N	[]	\N
14	1441	Jenny Torrez	<EMAIL>	publisher	$2a$12$SBU4.3K8GJ2UeW31F50WHe7.BxytxpcLO99jUcMif0xKEs682Cctu	t	2025-07-25 00:26:20.923-04	{}	2025-07-24 06:34:57.663-04	2025-07-25 00:26:20.924-04	\N	\N	{}	\N	\N	[]	\N
15	1441	Laura Torrez	<EMAIL>	publisher	$2a$12$y82js6r8bU1pCaGBxJJSdOzWTfXiUooqyOQc9fJNz7oeSb4lC8YBa	t	2025-07-25 00:30:23.299-04	{}	2025-07-24 06:34:57.664-04	2025-07-25 00:30:23.301-04	\N	\N	{}	\N	\N	[]	\N
16	1441	Raul Ramirez	<EMAIL>	ministerial_servant	$2a$12$LK9FboUvuzxxOpPHrhoiMOjYBWrvbWo2viKwAtCb6mZ7GGgXBffoi	t	2025-07-25 00:37:18.137-04	{}	2025-07-24 06:34:57.665-04	2025-07-25 00:37:18.139-04	\N	\N	{}	\N	\N	[]	\N
18	1441	Luis Cantos	<EMAIL>	elder	$2a$12$dUkPr9XRgdbjUA2AxrJx7.3hPH.EVRReWPtRUSKKSss18Noh84JrW	t	2025-07-25 00:40:57.422-04	{}	2025-07-24 06:34:57.666-04	2025-07-25 00:40:57.424-04	\N	\N	{}	\N	\N	[]	\N
cmdhx712600012twhz4yq2bc3	1441	Juan Carlos Pérez	<EMAIL>	elder	$2a$10$xWPLve3HNim0XB51aPDeb.ENJVCdZjeC4pZ6TiWLP7kg9R4d3jGs6	t	\N	{}	2025-07-24 17:44:20.767-04	2025-07-24 17:44:20.767-04	123 Main Street, Anytown, ST 12345	1985-06-15	{"privacyLevel": "elders_only", "preferredMethod": "email", "allowEmergencyContact": true}	Miembro activo con experiencia en presentaciones	+1 (555) 123-4567	["Lector", "Oración", "Discurso", "Presidente"]	Grupo 1
\.


--
-- TOC entry 5332 (class 0 OID 99342)
-- Dependencies: 226
-- Data for Name: midweek_meeting_parts; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.midweek_meeting_parts (id, meeting_id, part_number, part_type, title, assigned_member, assistant, time_allocation, notes, is_completed, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5331 (class 0 OID 99332)
-- Dependencies: 225
-- Data for Name: midweek_meetings; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.midweek_meetings (id, congregation_id, meeting_date, chairman, opening_prayer, closing_prayer, location, zoom_link, notes, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5348 (class 0 OID 103386)
-- Dependencies: 242
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.notifications (id, congregation_id, recipient_id, sender_id, title, message, category, priority, delivery_method, status, scheduled_for, delivered_at, read_at, metadata, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5355 (class 0 OID 108618)
-- Dependencies: 249
-- Data for Name: permission_audit_logs; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.permission_audit_logs (id, congregation_id, user_id, action, section_id, permissions, performed_by, reason, ip_address, user_agent, "timestamp", created_at) FROM stdin;
\.


--
-- TOC entry 5343 (class 0 OID 101705)
-- Dependencies: 237
-- Data for Name: pin_change_history; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.pin_change_history (id, congregation_id, member_id, changed_by, change_type, old_pin_hash, new_pin_hash, reason, ip_address, user_agent, created_at) FROM stdin;
cmdhx9308000apkxk4p6xw6al	1441	4	6	reset	$2a$12$.VERihpPMEoW/APziPwQtuSlNmwn8heYLV5zwsoygV4940YMVBWka	$2a$12$fZhbCXE7oqu06CYYcN.V1.Xv8vCtoMC2PzgE0RYna2fv16vUjXhui	Test PIN reset	127.0.0.1	Test Script	2025-07-24 17:45:56.6-04
cmdhxjvcf000a134t7q3lp4w0	1441	5	6	reset	$2a$12$K2UQ2j4Nu25dNZZiPsj6hOEmZPV3lhd5zqfgKOUT0ucbfzsGtaeFC	$2a$12$wROCn6kFmpyw6KURw/BX1eb9PFV86iaXrwgszoQJeGxtK/jigXEUK	Test PIN reset	127.0.0.1	Test Script	2025-07-24 17:54:19.887-04
\.


--
-- TOC entry 5342 (class 0 OID 101689)
-- Dependencies: 236
-- Data for Name: pin_settings; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.pin_settings (id, congregation_id, min_length, max_length, require_numeric, require_alphanumeric, require_special_chars, allow_sequential, allow_repeated, expiration_days, bcrypt_rounds, created_by, created_at, updated_at, lockout_duration_minutes, max_attempts, prevent_reuse_count, temporary_pin_expiration_hours) FROM stdin;
cmdhizxjo0001ovaysf0zx79i	1441	8	10	t	t	t	t	t	\N	12	\N	2025-07-24 11:06:54.997-04	2025-07-24 17:54:19.527-04	30	5	3	24
\.


--
-- TOC entry 5326 (class 0 OID 99281)
-- Dependencies: 220
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.roles (id, name, description, permissions, is_active, created_at, updated_at) FROM stdin;
cmdg2mwh600005gtp2i05g5zm	publisher	Publicador	["view_dashboard", "view_profile", "edit_profile"]	t	2025-07-23 10:41:07.051-04	2025-07-24 07:32:13.389-04
cmdg2mwha00015gtp9yh0nt53	ministerial_servant	Siervo Ministerial	["view_dashboard", "view_profile", "edit_profile", "view_admin", "manage_tasks"]	t	2025-07-23 10:41:07.055-04	2025-07-24 07:32:13.391-04
cmdg2mwhc00025gtp3gg10tup	elder	Anciano	["view_dashboard", "view_profile", "edit_profile", "view_admin", "manage_members", "manage_tasks", "manage_meetings", "manage_letters"]	t	2025-07-23 10:41:07.056-04	2025-07-24 07:32:13.393-04
cmdg2mwhe00035gtpksu5n28s	overseer_coordinator	Superintendente/Coordinador	["view_dashboard", "view_profile", "edit_profile", "view_admin", "manage_members", "manage_tasks", "manage_meetings", "manage_letters", "manage_congregation_settings"]	t	2025-07-23 10:41:07.058-04	2025-07-24 07:32:13.395-04
cmdg2mwhg00045gtp3ehrw6gf	developer	Desarrollador	["all"]	t	2025-07-23 10:41:07.06-04	2025-07-24 07:32:13.396-04
cmdhj8cqf00001nhh83ajc01h	coordinator	Coordinador del Cuerpo de Ancianos	["MANAGE_MEMBERS", "MANAGE_MEETINGS", "MANAGE_ASSIGNMENTS", "MANAGE_FIELD_SERVICE", "MANAGE_LETTERS", "MANAGE_EVENTS", "MANAGE_TASKS", "VIEW_ANALYTICS", "MANAGE_SETTINGS", "DELEGATE_PERMISSIONS"]	t	2025-07-24 11:13:27.927-04	2025-07-24 11:13:27.927-04
\.


--
-- TOC entry 5329 (class 0 OID 99313)
-- Dependencies: 223
-- Data for Name: section_assignments; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.section_assignments (id, congregation_id, member_id, section_type, scope_definition, assigned_by, assigned_at, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5358 (class 0 OID 111481)
-- Dependencies: 252
-- Data for Name: security_audit_events; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.security_audit_events (id, congregation_id, member_id, event_type, success, ip_address, user_agent, details, performed_by, "timestamp", created_at) FROM stdin;
cmdhxikmx0005g4rhbaeh2plf	1441	5	temporary_pin_created	t	127.0.0.1	Test Script	{"memberId": "5", "resetType": "temporary"}	6	2025-07-24 17:53:19.354-04	2025-07-24 17:53:19.354-04
cmdhxikn10007g4rhnk7dos3l	1441	5	lockout	t	127.0.0.1	Test Script	{"reason": "failed_attempts", "attemptCount": 5}	6	2025-07-24 17:53:19.357-04	2025-07-24 17:53:19.357-04
cmdhxikn20009g4rhe5rxnsau	1441	5	login_attempt	f	127.0.0.1	Test Script	{"reason": "invalid_pin"}	6	2025-07-24 17:53:19.359-04	2025-07-24 17:53:19.359-04
cmdhxk0aq000511prf0qzjif0	1441	11	temporary_pin_created	t	127.0.0.1	Test Script	{"memberId": "11", "resetType": "temporary"}	6	2025-07-24 17:54:26.307-04	2025-07-24 17:54:26.307-04
cmdhxk0at000711prv8de7a1c	1441	11	lockout	t	127.0.0.1	Test Script	{"reason": "failed_attempts", "attemptCount": 5}	6	2025-07-24 17:54:26.309-04	2025-07-24 17:54:26.309-04
cmdhxk0av000911pr9ofx4krb	1441	11	login_attempt	f	127.0.0.1	Test Script	{"reason": "invalid_pin"}	6	2025-07-24 17:54:26.311-04	2025-07-24 17:54:26.311-04
\.


--
-- TOC entry 5353 (class 0 OID 105930)
-- Dependencies: 247
-- Data for Name: service_groups; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.service_groups (id, congregation_id, name, group_number, description, is_active, created_at, updated_at) FROM stdin;
cmdh9a725000gk22p1crnq54g	1441	Grupo 1	1	\N	t	2025-07-24 06:34:57.723-04	2025-07-24 06:55:52.003-04
cmdh9a727000ik22pcaevnc3b	1441	Grupo 2	2	\N	t	2025-07-24 06:34:57.726-04	2025-07-24 06:55:52.005-04
cmdh9a728000kk22p95irszv1	1441	Grupo 3	3	\N	t	2025-07-24 06:34:57.727-04	2025-07-24 06:55:52.007-04
cmdh9a729000mk22pg4n9ud3s	1441	Grupo 4	4	\N	t	2025-07-24 06:34:57.727-04	2025-07-24 06:55:52.009-04
cmdh9a72a000ok22p3qozk4iz	1441	Grupo 5	5	\N	t	2025-07-24 06:34:57.728-04	2025-07-24 06:55:52.01-04
cmdh9a72a000qk22p2st81xtk	1441	Grupo 6	6	\N	t	2025-07-24 06:34:57.729-04	2025-07-24 06:55:52.012-04
cmdh9a72b000sk22p0rffibbo	1441	Grupo 7	7	\N	t	2025-07-24 06:34:57.729-04	2025-07-24 06:55:52.013-04
\.


--
-- TOC entry 5340 (class 0 OID 99424)
-- Dependencies: 234
-- Data for Name: songs; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.songs (id, song_number, title_es, title_en, category, is_active, created_at, updated_at) FROM stdin;
cmdh8wub7000f11pd0lm2hcm7	2	Tu nombre es Jehová	Jehovah Is Your Name	\N	t	2025-07-24 06:24:34.674-04	2025-07-24 06:55:51.885-04
cmdh8wub7000g11pds6amfj5c	3	Tú me das fuerza	confianza y valor	\N	t	2025-07-24 06:24:34.675-04	2025-07-24 06:55:51.886-04
cmdh8wub8000h11pdik8ma216	4	“Jehová es mi Pastor”	\\"Jehovah Is My Shepherd\\"	\N	t	2025-07-24 06:24:34.675-04	2025-07-24 06:55:51.888-04
cmdh8wub9000i11pdy4wtn7wu	5	Las maravillosas obras de Dios	God\\'s Wondrous Works	\N	t	2025-07-24 06:24:34.676-04	2025-07-24 06:55:51.89-04
cmdh8wuba000j11pd8472zpwi	10	¡Alabemos a nuestro Dios	Jehová!	\N	t	2025-07-24 06:24:34.677-04	2025-07-24 06:55:51.892-04
cmdh8wubb000l11pdew1fea3e	57	Predicamos a toda clase de personas	Preaching to All Sorts of People	\N	t	2025-07-24 06:24:34.678-04	2025-07-24 06:55:51.893-04
cmdh8wubb000m11pdsjqzst89	100	Seamos hospitalarios	Receive Them With Hospitality	\N	t	2025-07-24 06:24:34.679-04	2025-07-24 06:55:51.894-04
cmdh8wubc000n11pd0qf7cf8q	144	No dejes de mirar allí	Keep Your Eyes on the Prize!	\N	t	2025-07-24 06:24:34.679-04	2025-07-24 06:55:51.894-04
cmdh8wubd000o11pdto9dvbt3	155	Mi mayor felicidad	Our Joy Eternally	\N	t	2025-07-24 06:24:34.68-04	2025-07-24 06:55:51.895-04
cmdh8wube000p11pdnehy1bli	6	Los cielos proclaman la gloria de Dios	The Heavens Declare God\\'s Glory	\N	t	2025-07-24 06:24:34.681-04	2025-07-24 06:55:51.896-04
cmdh8wubg000q11pdvl7pe484	7	Jehová es mi fuerza y mi salvación	Jehovah	\N	t	2025-07-24 06:24:34.683-04	2025-07-24 06:55:51.896-04
cmdh8wubg000r11pda0om08wb	8	Jehová es mi Refugio	Jehovah Is Our Refuge	\N	t	2025-07-24 06:24:34.684-04	2025-07-24 06:55:51.897-04
cmdh8wubh000s11pdyjlpr4zq	9	¡Jehová es nuestro Rey!	Jehovah Is Our King!	\N	t	2025-07-24 06:24:34.684-04	2025-07-24 06:55:51.897-04
cmdh8wubi000t11pd7gnmv6je	11	La creación alaba a Jehová	Creation Praises God	\N	t	2025-07-24 06:24:34.685-04	2025-07-24 06:55:51.898-04
cmdh8wubi000u11pd7qm5bqxk	12	Jehová	nuestro gran Dios	\N	t	2025-07-24 06:24:34.686-04	2025-07-24 06:55:51.899-04
cmdh8wubj000v11pd71adbg7z	13	Cristo es nuestro modelo	Christ	\N	t	2025-07-24 06:24:34.686-04	2025-07-24 06:55:51.899-04
cmdh8wubk000w11pd4e7wnjki	14	Honremos al nuevo Rey de la Tierra	Praising Earth\\'s New King	\N	t	2025-07-24 06:24:34.687-04	2025-07-24 06:55:51.9-04
cmdh8wubk000x11pd19itb215	15	Alabemos al Primogénito de Jehová	Praise Jehovah\\'s Firstborn!	\N	t	2025-07-24 06:24:34.687-04	2025-07-24 06:55:51.9-04
cmdh8wubl000y11pdmsga7p9b	16	Alabemos a Jehová por su Hijo	el Ungido	\N	t	2025-07-24 06:24:34.688-04	2025-07-24 06:55:51.901-04
cmdh8wubl000z11pd4mia0ka4	17	“Quiero”	\\"I Want To\\"	\N	t	2025-07-24 06:24:34.688-04	2025-07-24 06:55:51.901-04
cmdh8wubm001011pdbnemfc6b	18	Gracias por el rescate	Grateful for the Ransom	\N	t	2025-07-24 06:24:34.689-04	2025-07-24 06:55:51.902-04
cmdh8wubm001111pdkr19y6nf	19	La Cena del Señor	The Lord\\'s Evening Meal	\N	t	2025-07-24 06:24:34.69-04	2025-07-24 06:55:51.902-04
cmdh8wubn001211pd99uryy6b	20	Enviaste a Jesús	tu Hijo amado	\N	t	2025-07-24 06:24:34.69-04	2025-07-24 06:55:51.903-04
cmdh8wubo001311pds2gdq0ti	22	¡Que venga el Reino que Dios ha establecido!	The Kingdom Is in Place—Let It Come!	\N	t	2025-07-24 06:24:34.691-04	2025-07-24 06:55:51.904-04
cmdh8wubo001411pdx52u8c0u	23	Jehová ha empezado su gobierno	Jehovah Begins His Rule	\N	t	2025-07-24 06:24:34.692-04	2025-07-24 06:55:51.906-04
cmdh8wubp001511pduf0t8fj1	24	Subamos a la montaña de Jehová	Come to Jehovah\\'s Mountain	\N	t	2025-07-24 06:24:34.692-04	2025-07-24 06:55:51.907-04
cmdh8wubp001611pdus21ynpp	25	Una posesión especial	A Special Possession	\N	t	2025-07-24 06:24:34.693-04	2025-07-24 06:55:51.908-04
cmdh8wubq001711pdh7hx14nk	26	“Si lo haces por él	lo haces por mí”	\N	t	2025-07-24 06:24:34.693-04	2025-07-24 06:55:51.909-04
cmdh8wubr001811pd21gp28u7	27	La revelación de los hijos de Dios	The Revealing of God\\'s Sons	\N	t	2025-07-24 06:24:34.694-04	2025-07-24 06:55:51.91-04
cmdh8wubr001911pd5zff1h9x	28	Cómo hacernos amigos de Jehová	Gaining Jehovah\\'s Friendship	\N	t	2025-07-24 06:24:34.694-04	2025-07-24 06:55:51.91-04
cmdh8wubs001a11pd5su5lncm	29	Hagamos honor a nuestro nombre	Living Up to Our Name	\N	t	2025-07-24 06:24:34.695-04	2025-07-24 06:55:51.911-04
cmdh8wubs001b11pd3c88v80e	30	Mi Amigo	mi Padre	\N	t	2025-07-24 06:24:34.695-04	2025-07-24 06:55:51.911-04
cmdh8wubt001c11pdu3ydqaii	31	Camina siempre con Jehová	Oh	\N	t	2025-07-24 06:24:34.696-04	2025-07-24 06:55:51.912-04
cmdh8wubu001d11pdqh95ryhn	32	¡Ponte de parte de Dios!	Take Sides With Jehovah!	\N	t	2025-07-24 06:24:34.697-04	2025-07-24 06:55:51.912-04
cmdh8wubw001e11pdl2uc5yfx	33	Echa sobre Dios tu carga	Throw Your Burden on Jehovah	\N	t	2025-07-24 06:24:34.699-04	2025-07-24 06:55:51.913-04
cmdh8wubx001f11pdx7r0nmgl	34	Caminaré en integridad	Walking in Integrity	\N	t	2025-07-24 06:24:34.7-04	2025-07-24 06:55:51.914-04
cmdh8wuby001h11pdq2t9kb07	36	Cuidemos nuestro corazón	We Guard Our Hearts	\N	t	2025-07-24 06:24:34.701-04	2025-07-24 06:55:51.915-04
cmdh8wubz001i11pd01876n22	37	Serviré a Jehová con el corazón	Serving Jehovah Whole-Souled	\N	t	2025-07-24 06:24:34.702-04	2025-07-24 06:55:51.916-04
cmdh8wubz001j11pdrhmjgung	38	Jehová te cuidará	He Will Make You Strong	\N	t	2025-07-24 06:24:34.702-04	2025-07-24 06:55:51.916-04
cmdh8wuc0001k11pd081iiqt6	39	Un buen nombre ante Dios	Make a Good Name With God	\N	t	2025-07-24 06:24:34.703-04	2025-07-24 06:55:51.917-04
cmdh8wuc0001l11pdd1qeufw6	40	¿A quién servimos?	To Whom Do We Belong?	\N	t	2025-07-24 06:24:34.704-04	2025-07-24 06:55:51.918-04
cmdh8wuc1001m11pdgz4b69xk	41	Padre	escucha mi oración	\N	t	2025-07-24 06:24:34.704-04	2025-07-24 06:55:51.918-04
cmdh8wuc1001n11pd2hwl5n99	42	La oración del siervo de Dios	The Prayer of God\\'s Servant	\N	t	2025-07-24 06:24:34.705-04	2025-07-24 06:55:51.919-04
cmdh8wuc2001o11pdcrjigvro	43	Una oración para dar gracias a Dios	A Prayer of Thanks	\N	t	2025-07-24 06:24:34.705-04	2025-07-24 06:55:51.92-04
cmdh8wuc3001p11pd33hpowwh	44	Una súplica ferviente	A Prayer of the Lowly One	\N	t	2025-07-24 06:24:34.706-04	2025-07-24 06:55:51.921-04
cmdh8wuc3001q11pdps619gw1	45	“La meditación de mi corazón”	The Meditation of My Heart	\N	t	2025-07-24 06:24:34.707-04	2025-07-24 06:55:51.922-04
cmdh8wuc4001r11pdwj1u8ucx	46	Gracias	Jehová	\N	t	2025-07-24 06:24:34.707-04	2025-07-24 06:55:51.924-04
cmdh8wuc5001s11pdn6fam4ht	47	Oremos a Dios todos los días	Pray to Jehovah Each Day	\N	t	2025-07-24 06:24:34.708-04	2025-07-24 06:55:51.925-04
cmdh8wuc5001t11pdopn2t3p8	48	Caminemos diariamente con Jehová	Daily Walking With Jehovah	\N	t	2025-07-24 06:24:34.708-04	2025-07-24 06:55:51.925-04
cmdh8wuc6001u11pd6hjoaos8	49	Alegremos el corazón de Jehová	Making Jehovah\\'s Heart Glad	\N	t	2025-07-24 06:24:34.709-04	2025-07-24 06:55:51.926-04
cmdh8wuc6001v11pdjra497po	50	Mi oración de dedicación	My Prayer of Dedication	\N	t	2025-07-24 06:24:34.709-04	2025-07-24 06:55:51.927-04
cmdh8wuc7001w11pdap7kwpqs	51	Estamos dedicados a Dios	To God We Are Dedicated!	\N	t	2025-07-24 06:24:34.71-04	2025-07-24 06:55:51.927-04
cmdh8wuc7001x11pdwlk6znu9	52	La dedicación cristiana	Christian Dedication	\N	t	2025-07-24 06:24:34.711-04	2025-07-24 06:55:51.928-04
cmdh8wuc8001y11pd67b61pa2	53	Hoy voy a salir a predicar	Preparing to Preach	\N	t	2025-07-24 06:24:34.711-04	2025-07-24 06:55:51.929-04
cmdh8wuc9001z11pd4cihs7tm	54	“Este es el camino”	\\"This Is the Way\\"	\N	t	2025-07-24 06:24:34.712-04	2025-07-24 06:55:51.929-04
cmdh8wuc9002011pdjqz3wsn2	55	¡No los temas!	Fear Them Not!	\N	t	2025-07-24 06:24:34.713-04	2025-07-24 06:55:51.93-04
cmdh8wuca002111pdbjexhyrn	56	Vive la verdad	Make the Truth Your Own	\N	t	2025-07-24 06:24:34.713-04	2025-07-24 06:55:51.931-04
cmdh8wucc002211pd2n7ojkx7	58	Voy a buscar a los amigos de la paz	Searching for Friends of Peace	\N	t	2025-07-24 06:24:34.715-04	2025-07-24 06:55:51.931-04
cmdh8wucd002311pd6f9rshk3	59	Ven a alabar a Jehová	Praise Jah With Me	\N	t	2025-07-24 06:24:34.717-04	2025-07-24 06:55:51.932-04
cmdh8wub5000e11pdvb4zktcl	1	Las cualidades principales de Jehová	Jehovah\\'s Attributes	\N	t	2025-07-24 06:24:34.672-04	2025-07-24 06:55:51.884-04
cmdh8wucg002611pdxah4r7pn	62	La nueva canción	The New Song	\N	t	2025-07-24 06:24:34.719-04	2025-07-24 06:55:51.934-04
cmdh8wuch002811pd85csrm8n	64	Participemos con gozo en la cosecha	Sharing Joyfully in the Harvest	\N	t	2025-07-24 06:24:34.721-04	2025-07-24 06:55:51.935-04
cmdh8wuci002911pdxrlzwz2r	65	Lucha por progresar	Move Ahead!	\N	t	2025-07-24 06:24:34.721-04	2025-07-24 06:55:51.936-04
cmdh8wucj002a11pd5s5hrhb7	66	Proclamemos las buenas noticias	Declare the Good News	\N	t	2025-07-24 06:24:34.722-04	2025-07-24 06:55:51.937-04
cmdh8wucj002b11pddeue5tp4	67	“Predica la palabra”	\\"Preach the Word\\"	\N	t	2025-07-24 06:24:34.722-04	2025-07-24 06:55:51.938-04
cmdh8wuck002c11pd5ve1wsiy	68	Sembremos semillas del Reino	Sowing Kingdom Seed	\N	t	2025-07-24 06:24:34.723-04	2025-07-24 06:55:51.94-04
cmdh8wuck002d11pd2xljwteq	69	Prediquen las nuevas del Reino	Go Forward in Preaching the Kingdom!	\N	t	2025-07-24 06:24:34.724-04	2025-07-24 06:55:51.941-04
cmdh8wucl002e11pdeq7rxhaq	70	Busquemos a los merecedores	Search Out Deserving Ones	\N	t	2025-07-24 06:24:34.724-04	2025-07-24 06:55:51.941-04
cmdh8wucl002f11pdtikcqpvj	71	¡Somos los guerreros de Jehová!	We Are Jehovah\\'s Army!	\N	t	2025-07-24 06:24:34.725-04	2025-07-24 06:55:51.942-04
cmdh8wucm002g11pdm47cl3rq	72	Anunciaré la verdad del Reino	Making Known the Kingdom Truth	\N	t	2025-07-24 06:24:34.725-04	2025-07-24 06:55:51.943-04
cmdh8wucm002h11pd2pckf49b	73	Danos fuerzas y valor	Grant Us Boldness	\N	t	2025-07-24 06:24:34.726-04	2025-07-24 06:55:51.943-04
cmdh8wucn002i11pd7jfxbboq	74	Ven a cantar la gran canción del Reino	Join in the Kingdom Song!	\N	t	2025-07-24 06:24:34.726-04	2025-07-24 06:55:51.944-04
cmdh8wuco002j11pdae07lp7n	75	“¡Aquí estoy	envíame!”	\N	t	2025-07-24 06:24:34.727-04	2025-07-24 06:55:51.944-04
cmdh8wuco002k11pddbjpp0e2	76	Cuéntame lo que sientes	How Does It Make You Feel?	\N	t	2025-07-24 06:24:34.727-04	2025-07-24 06:55:51.945-04
cmdh8wucp002l11pd8eyld97r	77	Luz en un mundo oscuro	Light in a Darkened World	\N	t	2025-07-24 06:24:34.728-04	2025-07-24 06:55:51.945-04
cmdh8wucp002m11pdl3bvu4dy	78	Enseñemos la Palabra de Dios	\\"Teaching the Word of God\\"	\N	t	2025-07-24 06:24:34.729-04	2025-07-24 06:55:51.946-04
cmdh8wucq002n11pdqmp191ea	79	Que sigan firmes en la fe	Teach Them to Stand Firm	\N	t	2025-07-24 06:24:34.729-04	2025-07-24 06:55:51.946-04
cmdh8wucs002p11pdh2vvuf0a	81	La vida del precursor	The Life of a Pioneer	\N	t	2025-07-24 06:24:34.731-04	2025-07-24 06:55:51.948-04
cmdh8wuct002q11pdfd9elxrg	82	Hagamos que brille nuestra luz	\\"Let Your Light Shine\\"	\N	t	2025-07-24 06:24:34.732-04	2025-07-24 06:55:51.949-04
cmdh8wucu002r11pdvfmbag6g	83	“De casa en casa”	\\"From House to House\\"	\N	t	2025-07-24 06:24:34.733-04	2025-07-24 06:55:51.95-04
cmdh8wucv002s11pdimp09x61	84	Servimos donde se nos necesite	Reaching Out	\N	t	2025-07-24 06:24:34.734-04	2025-07-24 06:55:51.95-04
cmdh8wucv002t11pd5r584jq8	85	“Recíbanse con gusto unos a otros”	Welcome One Another	\N	t	2025-07-24 06:24:34.734-04	2025-07-24 06:55:51.951-04
cmdh8wucw002u11pdu069a0t7	86	Necesitamos que Jehová nos enseñe	We Must Be Taught	\N	t	2025-07-24 06:24:34.735-04	2025-07-24 06:55:51.951-04
cmdh8wucw002v11pdlmvbvij9	87	Ven a recibir ánimo	Come! Be Refreshed	\N	t	2025-07-24 06:24:34.735-04	2025-07-24 06:55:51.952-04
cmdh8wucx002w11pdourri8oy	88	Hazme conocer tus caminos	Make Me Know Your Ways	\N	t	2025-07-24 06:24:34.736-04	2025-07-24 06:55:51.953-04
cmdh8wucz002x11pd3ar3ipyj	89	Jehová bendice al que escucha y obedece	Listen	\N	t	2025-07-24 06:24:34.738-04	2025-07-24 06:55:51.954-04
cmdh8wud0002y11pdvyulbctq	90	Animémonos unos a otros	Encourage One Another	\N	t	2025-07-24 06:24:34.739-04	2025-07-24 06:55:51.955-04
cmdh8wud0002z11pdwvftqn2l	91	Una obra de amor	Our Labor of Love	\N	t	2025-07-24 06:24:34.739-04	2025-07-24 06:55:51.956-04
cmdh8wud1003011pdv9u2qmdg	92	Un lugar que lleva tu nombre	A Place Bearing Your Name	\N	t	2025-07-24 06:24:34.74-04	2025-07-24 06:55:51.957-04
cmdh8wud2003111pdhwmqkcj3	93	Bendice nuestras reuniones	Bless Our Meeting Together	\N	t	2025-07-24 06:24:34.741-04	2025-07-24 06:55:51.957-04
cmdh8wud5003311pdd0bfb3zm	95	La luz brilla más cada día	The Light Gets Brighter	\N	t	2025-07-24 06:24:34.744-04	2025-07-24 06:55:51.959-04
cmdh8wud6003411pdhfqq6o3d	96	El libro de Dios es un tesoro	God\\'s Own Book—A Treasure	\N	t	2025-07-24 06:24:34.745-04	2025-07-24 06:55:51.959-04
cmdh8wud7003511pdunabbrqq	97	Nuestra vida depende de la Palabra de Dios	Life Depends on God\\'s Word	\N	t	2025-07-24 06:24:34.746-04	2025-07-24 06:55:51.96-04
cmdh8wud9003611pd376vws00	98	Las Escrituras están inspiradas por Dios	The Scriptures—Inspired of God	\N	t	2025-07-24 06:24:34.748-04	2025-07-24 06:55:51.96-04
cmdh8wuda003711pdftb4yfmn	99	Miles de fieles hermanos	Myriads of Brothers	\N	t	2025-07-24 06:24:34.749-04	2025-07-24 06:55:51.961-04
cmdh8wudb003811pdf87oauq9	101	Sirvamos a Dios en unidad	Working Together in Unity	\N	t	2025-07-24 06:24:34.75-04	2025-07-24 06:55:51.961-04
cmdh8wudc003911pdkk12b1jz	102	Ayudemos a los débiles	\\"Assist Those Who Are Weak\\"	\N	t	2025-07-24 06:24:34.751-04	2025-07-24 06:55:51.962-04
cmdh8wude003a11pdj53ibpgw	103	Nuestros pastores son un regalo de Dios	Shepherds—Gifts in Men	\N	t	2025-07-24 06:24:34.753-04	2025-07-24 06:55:51.963-04
cmdh8wude003b11pdvcn93s1e	104	El espíritu santo es un regalo de Dios	God\\'s Gift of Holy Spirit	\N	t	2025-07-24 06:24:34.754-04	2025-07-24 06:55:51.963-04
cmdh8wudf003c11pd07onwolc	105	“Dios es amor”	\\"God Is Love\\"	\N	t	2025-07-24 06:24:34.754-04	2025-07-24 06:55:51.964-04
cmdh8wudg003d11pd1icu951r	106	Cultivemos amor verdadero	Cultivating the Quality of Love	\N	t	2025-07-24 06:24:34.755-04	2025-07-24 06:55:51.965-04
cmdh8wudh003e11pdx3f7gkc8	107	Dios nos enseñó a amar	The Divine Pattern of Love	\N	t	2025-07-24 06:24:34.756-04	2025-07-24 06:55:51.965-04
cmdh8wudi003f11pd09lprpme	108	El amor leal de Jehová	God\\'s Loyal Love	\N	t	2025-07-24 06:24:34.757-04	2025-07-24 06:55:51.966-04
cmdh8wudi003g11pdppnjn74j	109	Amémonos de todo corazón	Love Intensely From the Heart	\N	t	2025-07-24 06:24:34.758-04	2025-07-24 06:55:51.966-04
cmdh8wudj003h11pdtrtzfbpz	110	El gozo de Jehová	\\"The Joy of Jehovah\\"	\N	t	2025-07-24 06:24:34.758-04	2025-07-24 06:55:51.967-04
cmdh8wudk003i11pdfskb32tl	111	Los motivos de nuestro gozo	Our Reasons for Joy	\N	t	2025-07-24 06:24:34.759-04	2025-07-24 06:55:51.968-04
cmdh8wudl003j11pd1wtt54r4	112	Jehová	el Dios de la paz	\N	t	2025-07-24 06:24:34.76-04	2025-07-24 06:55:51.969-04
cmdh8wudl003k11pdfueuvuit	113	La paz del pueblo de Dios	Our Possession of Peace	\N	t	2025-07-24 06:24:34.761-04	2025-07-24 06:55:51.97-04
cmdh8wudm003l11pdaaq6lx6x	114	Demostremos paciencia	\\"Exercise Patience\\"	\N	t	2025-07-24 06:24:34.761-04	2025-07-24 06:55:51.971-04
cmdh8wudn003m11pd8uew3p3t	115	Gratitud por la paciencia de Dios	Gratitude for Divine Patience	\N	t	2025-07-24 06:24:34.762-04	2025-07-24 06:55:51.972-04
cmdh8wudo003n11pdow7gy3ub	116	Seamos amables y bondadosos	The Power of Kindness	\N	t	2025-07-24 06:24:34.763-04	2025-07-24 06:55:51.973-04
cmdh8wudp003o11pdfpz4ddne	117	Imitemos la bondad de Jehová	The Quality of Goodness	\N	t	2025-07-24 06:24:34.764-04	2025-07-24 06:55:51.974-04
cmdh8wudr003p11pd3zruqzny	118	“Danos más fe”	\\"Give Us More Faith\\"	\N	t	2025-07-24 06:24:34.766-04	2025-07-24 06:55:51.974-04
cmdh8wuds003q11pdmq7dn27z	119	Necesitamos una fe fuerte	We Must Have Faith	\N	t	2025-07-24 06:24:34.767-04	2025-07-24 06:55:51.975-04
cmdh8wuds003r11pdl2txg12w	120	Seamos apacibles y humildes como Cristo	Imitate Christ\\'s Mildness	\N	t	2025-07-24 06:24:34.768-04	2025-07-24 06:55:51.975-04
cmdh8wudt003s11pd2hq3xwb8	121	Necesitamos autodominio	We Need Self-Control	\N	t	2025-07-24 06:24:34.768-04	2025-07-24 06:55:51.976-04
cmdh8wudu003t11pdultuggl2	122	¡Mantengámonos firmes	inmovibles!	\N	t	2025-07-24 06:24:34.769-04	2025-07-24 06:55:51.977-04
cmdh8wucg002511pdqwsl50fr	61	¡Avancen	Testigos!	\N	t	2025-07-24 06:24:34.719-04	2025-07-24 06:55:51.933-04
cmdh8wudv003v11pddoqfj8q5	124	Siempre fieles y leales	Ever Loyal	\N	t	2025-07-24 06:24:34.77-04	2025-07-24 06:55:51.978-04
cmdh8wue3004811pdlck6pagd	137	Fieles	valiosas	\N	t	2025-07-24 06:24:34.778-04	2025-07-24 06:55:51.985-04
cmdh8wue4004911pdljsm36sl	138	Los cabellos blancos	una hermosa corona	\N	t	2025-07-24 06:24:34.779-04	2025-07-24 06:55:51.986-04
cmdh8wue4004a11pdkxbj7n9m	139	¿Te ves en el nuevo mundo?	See Yourself When All Is New	\N	t	2025-07-24 06:24:34.779-04	2025-07-24 06:55:51.987-04
cmdh8wue5004b11pd27ao7zi2	140	¡Vida sin fin	al fin!	\N	t	2025-07-24 06:24:34.78-04	2025-07-24 06:55:51.988-04
cmdh8wue7004c11pdta06vmph	141	El maravilloso regalo de la vida	The Miracle of Life	\N	t	2025-07-24 06:24:34.782-04	2025-07-24 06:55:51.989-04
cmdh8wue8004d11pdagdtphak	142	Aferrémonos a nuestra esperanza	Holding Fast to Our Hope	\N	t	2025-07-24 06:24:34.783-04	2025-07-24 06:55:51.99-04
cmdh8wue9004e11pdmfc5t9jv	143	¡Hay que seguir vigilantes!	Keep Working	\N	t	2025-07-24 06:24:34.784-04	2025-07-24 06:55:51.99-04
cmdh8wue9004f11pdzvww4ojo	145	Dios prometió un Paraíso	God\\'s Promise of Paradise	\N	t	2025-07-24 06:24:34.784-04	2025-07-24 06:55:51.991-04
cmdh8wuea004g11pdd2whmvy5	146	“Estoy haciendo todo nuevo”	\\"Making All Things New\\"	\N	t	2025-07-24 06:24:34.785-04	2025-07-24 06:55:51.992-04
cmdh8wuea004h11pdy42kcmue	147	Dios nos promete la vida eterna	Life Everlasting Is Promised	\N	t	2025-07-24 06:24:34.786-04	2025-07-24 06:55:51.992-04
cmdh8wueb004i11pdh4f91ben	148	Jehová es mi Roca de salvación	Jehovah Provides Escape	\N	t	2025-07-24 06:24:34.786-04	2025-07-24 06:55:51.993-04
cmdh8wueb004j11pdefv4ksmo	149	La canción de la victoria	A Victory Song	\N	t	2025-07-24 06:24:34.787-04	2025-07-24 06:55:51.993-04
cmdh8wuec004k11pdu3951u4p	150	¡Jehová será tu Salvador!	Seek God for Your Deliverance	\N	t	2025-07-24 06:24:34.787-04	2025-07-24 06:55:51.994-04
cmdh8wued004l11pdsfayau3d	151	Jehová los llamará	He Will Call	\N	t	2025-07-24 06:24:34.788-04	2025-07-24 06:55:51.994-04
cmdh8wued004m11pdvg0h1mbn	152	Un lugar que te honrará	A Place That Will Bring You Praise	\N	t	2025-07-24 06:24:34.788-04	2025-07-24 06:55:51.995-04
cmdh8wuee004n11pdraq4wke0	153	Por ti seré valiente	Give Me Courage	\N	t	2025-07-24 06:24:34.789-04	2025-07-24 06:55:51.995-04
cmdh8wuee004o11pdr0e0bpkt	154	Un amor sin final	Unfailing Love	\N	t	2025-07-24 06:24:34.79-04	2025-07-24 06:55:51.996-04
cmdh8wuef004p11pdxkm0ryib	156	Si tienes fe	With Eyes of Faith	\N	t	2025-07-24 06:24:34.79-04	2025-07-24 06:55:51.996-04
cmdh8wueg004q11pdyal4pfr2	157	Solo paz	Peace at Last!	\N	t	2025-07-24 06:24:34.791-04	2025-07-24 06:55:51.997-04
cmdh8wueg004r11pdg2jul4yd	158	Danos paciencia	“It Will Not Be Late!”	\N	t	2025-07-24 06:24:34.791-04	2025-07-24 06:55:51.998-04
cmdh8wueh004s11pdde5sbyfx	159	Demos gloria a Jehová	Give Jehovah Glory	\N	t	2025-07-24 06:24:34.792-04	2025-07-24 06:55:51.998-04
cmdh8wueh004t11pdf9f2qa18	160	¡Buenas noticias!	“Good News”!	\N	t	2025-07-24 06:24:34.792-04	2025-07-24 06:55:51.999-04
cmdh8wuei004u11pdw6xc5k5c	161	Hacer tu voluntad	To Do Your Will Is My Delight	\N	t	2025-07-24 06:24:34.793-04	2025-07-24 06:55:52-04
cmdh8wuba000k11pdalvkxun2	21	Busquemos primero el Reino	Keep On Seeking First the Kingdom	\N	t	2025-07-24 06:24:34.677-04	2025-07-24 06:55:51.892-04
cmdh8wuby001g11pd1ouunwes	35	Asegurémonos de lo más importante	\\"Make Sure of the More Important Things\\"	\N	t	2025-07-24 06:24:34.701-04	2025-07-24 06:55:51.914-04
cmdh8wuce002411pd4q1ueb63	60	Hay vidas en juego	It Means Their Life	\N	t	2025-07-24 06:24:34.717-04	2025-07-24 06:55:51.933-04
cmdh8wuch002711pdz3tvdg2v	63	¡Soy testigo de Jehová!	We\\'re Jehovah\\'s Witnesses!	\N	t	2025-07-24 06:24:34.72-04	2025-07-24 06:55:51.935-04
cmdh8wucr002o11pdnysgp7fc	80	“Prueben y vean que Jehová es bueno”	\\"Taste and See That Jehovah Is Good\\"	\N	t	2025-07-24 06:24:34.73-04	2025-07-24 06:55:51.947-04
cmdh8wud4003211pddmf7dhg6	94	Agradecidos por la Palabra de Dios	Grateful for God\\'s Word	\N	t	2025-07-24 06:24:34.743-04	2025-07-24 06:55:51.958-04
cmdh8wudu003u11pdmkwjcsum	123	Seamos leales y sumisos al orden teocrático	Loyally Submitting to Theocratic Order	\N	t	2025-07-24 06:24:34.77-04	2025-07-24 06:55:51.977-04
cmdh8wudw003w11pdnojbslrm	125	Felices los misericordiosos	\\"Happy Are the Merciful!\\"	\N	t	2025-07-24 06:24:34.771-04	2025-07-24 06:55:51.978-04
cmdh8wudx003x11pd59j3ett7	126	Siempre fuertes	fieles y firmes	\N	t	2025-07-24 06:24:34.772-04	2025-07-24 06:55:51.979-04
cmdh8wudx003y11pd27a8g1mx	127	La clase de persona que debo ser	The Sort of Person I Should Be	\N	t	2025-07-24 06:24:34.773-04	2025-07-24 06:55:51.979-04
cmdh8wudy003z11pd69lmvygy	128	Aguantemos hasta el fin	Enduring to the End	\N	t	2025-07-24 06:24:34.773-04	2025-07-24 06:55:51.98-04
cmdh8wudy004011pdyaxzcvd4	129	Servimos con aguante	We Will Keep Enduring	\N	t	2025-07-24 06:24:34.774-04	2025-07-24 06:55:51.981-04
cmdh8wudz004111pdcosei4oh	130	Aprendamos a perdonar	Be Forgiving	\N	t	2025-07-24 06:24:34.774-04	2025-07-24 06:55:51.981-04
cmdh8wue0004211pdf6s8ish3	131	“Lo que Dios ha unido”	\\"What God Has Yoked Together\\"	\N	t	2025-07-24 06:24:34.775-04	2025-07-24 06:55:51.982-04
cmdh8wue0004311pd09h102vm	132	Ahora ya somos uno	Now We Are One	\N	t	2025-07-24 06:24:34.775-04	2025-07-24 06:55:51.982-04
cmdh8wue1004411pd84okxloj	133	Sirvamos a Jehová en nuestra juventud	Worship Jehovah During Youth	\N	t	2025-07-24 06:24:34.776-04	2025-07-24 06:55:51.983-04
cmdh8wue1004511pdamf9n510	134	Los hijos son un regalo de Dios	Children Are a Trust From God	\N	t	2025-07-24 06:24:34.777-04	2025-07-24 06:55:51.984-04
cmdh8wue2004611pdutf1pc1j	135	Jehová te pide: “Sé sabio	hijo mío”	\N	t	2025-07-24 06:24:34.777-04	2025-07-24 06:55:51.984-04
cmdh8wue2004711pd50h3shzf	136	Que Jehová te bendiga	\\"A Perfect Wage\\" From Jehovah	\N	t	2025-07-24 06:24:34.778-04	2025-07-24 06:55:51.985-04
\.


--
-- TOC entry 5351 (class 0 OID 105912)
-- Dependencies: 245
-- Data for Name: special_songs; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.special_songs (id, key_name, title_es, title_en, is_custom, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5336 (class 0 OID 99379)
-- Dependencies: 230
-- Data for Name: task_assignments; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.task_assignments (id, congregation_id, task_id, assigned_member_id, assigned_date, due_date, status, notes, completed_at, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5335 (class 0 OID 99370)
-- Dependencies: 229
-- Data for Name: tasks; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.tasks (id, congregation_id, title, description, category, frequency, estimated_time, instructions, is_active, created_at, updated_at) FROM stdin;
cmdh9a72d000uk22p4luor9v8	1441	Limpieza del salón interior -congregacion	Limpieza general del interior del Salón del Reino	general	one-time	\N	\N	t	2025-07-24 06:34:57.731-04	2025-07-24 06:34:57.731-04
cmdh9a72e000wk22pcjpq3tgj	1441	Limpieza despues de la reunion (Grupo	\N	general	one-time	\N	\N	t	2025-07-24 06:34:57.733-04	2025-07-24 06:34:57.733-04
cmdh9a72f000yk22p09l485p0	1441	Audio/Video	Manejo del sistema de audio/video para la reunión	general	one-time	\N	\N	t	2025-07-24 06:34:57.733-04	2025-07-24 06:34:57.733-04
cmdh9a72f0010k22py0g664xb	1441	Manejo de micrófonos	Manejar los micrófonos durante la reunión	general	one-time	\N	\N	t	2025-07-24 06:34:57.734-04	2025-07-24 06:34:57.734-04
cmdh9a72g0012k22poe6nbj1b	1441	Plataforma	Servicio en la plataforma	general	one-time	\N	\N	t	2025-07-24 06:34:57.734-04	2025-07-24 06:34:57.734-04
cmdh9a72g0014k22p6dpk9d5n	1441	Servicio del campo	Organización del servicio del campo	general	one-time	\N	\N	t	2025-07-24 06:34:57.735-04	2025-07-24 06:34:57.735-04
cmdh9a72h0016k22pl6cdk4yo	1441	Preparación de la reunión	Preparar el Salón para la reunión	general	one-time	\N	\N	t	2025-07-24 06:34:57.736-04	2025-07-24 06:34:57.736-04
cmdh9a72i0018k22psjhnnmk9	1441	Limpieza del salón exterior -congregacion	Limpieza del salon por la congregacion	general	one-time	\N	\N	t	2025-07-24 06:34:57.736-04	2025-07-24 06:34:57.736-04
cmdha12vk005ap9w2ag3bmvm0	1441	Limpieza del salón interior -congregacion	Limpieza general del interior del Salón del Reino	general	one-time	\N	\N	t	2025-07-24 06:55:52.015-04	2025-07-24 06:55:52.015-04
cmdha12vm005cp9w2pzf9gs2b	1441	Limpieza despues de la reunion (Grupo	\N	general	one-time	\N	\N	t	2025-07-24 06:55:52.017-04	2025-07-24 06:55:52.017-04
cmdha12vo005ep9w2w1z0xznb	1441	Audio/Video	Manejo del sistema de audio/video para la reunión	general	one-time	\N	\N	t	2025-07-24 06:55:52.018-04	2025-07-24 06:55:52.018-04
cmdha12vp005gp9w23tjfa4jl	1441	Manejo de micrófonos	Manejar los micrófonos durante la reunión	general	one-time	\N	\N	t	2025-07-24 06:55:52.02-04	2025-07-24 06:55:52.02-04
cmdha12vr005ip9w2v7ib7z3z	1441	Plataforma	Servicio en la plataforma	general	one-time	\N	\N	t	2025-07-24 06:55:52.021-04	2025-07-24 06:55:52.021-04
cmdha12vs005kp9w27ddd3wa6	1441	Servicio del campo	Organización del servicio del campo	general	one-time	\N	\N	t	2025-07-24 06:55:52.023-04	2025-07-24 06:55:52.023-04
cmdha12vt005mp9w2io3c3swl	1441	Preparación de la reunión	Preparar el Salón para la reunión	general	one-time	\N	\N	t	2025-07-24 06:55:52.024-04	2025-07-24 06:55:52.024-04
cmdha12vv005op9w2voox8a9u	1441	Limpieza del salón exterior -congregacion	Limpieza del salon por la congregacion	general	one-time	\N	\N	t	2025-07-24 06:55:52.025-04	2025-07-24 06:55:52.025-04
\.


--
-- TOC entry 5356 (class 0 OID 111459)
-- Dependencies: 250
-- Data for Name: temporary_pins; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.temporary_pins (id, congregation_id, member_id, temporary_pin_hash, reset_type, created_by, reason, expiration_date, require_change, used, used_at, ip_address, user_agent, created_at, updated_at) FROM stdin;
cmdhxikmi0001g4rhlq8k0grv	1441	5	hashed_temp_pin_1753393999336	temporary	6	Test temporary PIN creation	2025-07-25 17:53:19.336-04	t	f	\N	127.0.0.1	Test Script	2025-07-24 17:53:19.338-04	2025-07-24 17:53:19.338-04
cmdhxk0ad000111prpzv9h5z2	1441	11	hashed_temp_pin_1753394066292	temporary	6	Test temporary PIN creation	2025-07-25 17:54:26.292-04	t	f	\N	127.0.0.1	Test Script	2025-07-24 17:54:26.293-04	2025-07-24 17:54:26.293-04
\.


--
-- TOC entry 5354 (class 0 OID 105939)
-- Dependencies: 248
-- Data for Name: territories; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.territories (id, congregation_id, name, description, status, assigned_to_id, assigned_at, completed_at, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5334 (class 0 OID 99361)
-- Dependencies: 228
-- Data for Name: weekend_meeting_parts; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.weekend_meeting_parts (id, meeting_id, part_type, assigned_member, notes, is_completed, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 5333 (class 0 OID 99351)
-- Dependencies: 227
-- Data for Name: weekend_meetings; Type: TABLE DATA; Schema: public; Owner: mywebsites
--

COPY public.weekend_meetings (id, congregation_id, meeting_date, public_talk_title, public_talk_speaker, speaker_congregation, watchtower_conductor, watchtower_reader, chairman, opening_prayer, closing_prayer, location, zoom_link, notes, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4959 (class 2606 OID 99259)
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- TOC entry 5103 (class 2606 OID 111480)
-- Name: account_lockouts account_lockouts_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.account_lockouts
    ADD CONSTRAINT account_lockouts_pkey PRIMARY KEY (id);


--
-- TOC entry 4988 (class 2606 OID 99331)
-- Name: assignment_history assignment_history_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.assignment_history
    ADD CONSTRAINT assignment_history_pkey PRIMARY KEY (id);


--
-- TOC entry 5068 (class 2606 OID 103408)
-- Name: communication_preferences communication_preferences_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.communication_preferences
    ADD CONSTRAINT communication_preferences_pkey PRIMARY KEY (id);


--
-- TOC entry 5072 (class 2606 OID 103418)
-- Name: communication_templates communication_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.communication_templates
    ADD CONSTRAINT communication_templates_pkey PRIMARY KEY (id);


--
-- TOC entry 5079 (class 2606 OID 105929)
-- Name: congregation_settings congregation_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.congregation_settings
    ADD CONSTRAINT congregation_settings_pkey PRIMARY KEY (id);


--
-- TOC entry 4963 (class 2606 OID 99280)
-- Name: congregations congregations_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.congregations
    ADD CONSTRAINT congregations_pkey PRIMARY KEY (id);


--
-- TOC entry 5049 (class 2606 OID 103366)
-- Name: document_access_logs document_access_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_access_logs
    ADD CONSTRAINT document_access_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 5053 (class 2606 OID 103376)
-- Name: document_comments document_comments_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_comments
    ADD CONSTRAINT document_comments_pkey PRIMARY KEY (id);


--
-- TOC entry 5045 (class 2606 OID 103358)
-- Name: document_folders document_folders_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_folders
    ADD CONSTRAINT document_folders_pkey PRIMARY KEY (id);


--
-- TOC entry 5058 (class 2606 OID 103385)
-- Name: document_workflows document_workflows_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_workflows
    ADD CONSTRAINT document_workflows_pkey PRIMARY KEY (id);


--
-- TOC entry 4978 (class 2606 OID 99312)
-- Name: elder_permissions elder_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.elder_permissions
    ADD CONSTRAINT elder_permissions_pkey PRIMARY KEY (id);


--
-- TOC entry 5025 (class 2606 OID 99423)
-- Name: events events_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_pkey PRIMARY KEY (id);


--
-- TOC entry 5012 (class 2606 OID 99400)
-- Name: field_service_records field_service_records_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.field_service_records
    ADD CONSTRAINT field_service_records_pkey PRIMARY KEY (id);


--
-- TOC entry 4961 (class 2606 OID 99268)
-- Name: health_checks health_checks_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.health_checks
    ADD CONSTRAINT health_checks_pkey PRIMARY KEY (id);


--
-- TOC entry 5020 (class 2606 OID 99411)
-- Name: letters letters_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.letters
    ADD CONSTRAINT letters_pkey PRIMARY KEY (id);


--
-- TOC entry 5033 (class 2606 OID 99586)
-- Name: member_change_history member_change_history_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.member_change_history
    ADD CONSTRAINT member_change_history_pkey PRIMARY KEY (id);


--
-- TOC entry 4972 (class 2606 OID 99301)
-- Name: members members_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.members
    ADD CONSTRAINT members_pkey PRIMARY KEY (id);


--
-- TOC entry 4994 (class 2606 OID 99350)
-- Name: midweek_meeting_parts midweek_meeting_parts_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.midweek_meeting_parts
    ADD CONSTRAINT midweek_meeting_parts_pkey PRIMARY KEY (id);


--
-- TOC entry 4991 (class 2606 OID 99341)
-- Name: midweek_meetings midweek_meetings_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.midweek_meetings
    ADD CONSTRAINT midweek_meetings_pkey PRIMARY KEY (id);


--
-- TOC entry 5064 (class 2606 OID 103396)
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- TOC entry 5092 (class 2606 OID 108627)
-- Name: permission_audit_logs permission_audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.permission_audit_logs
    ADD CONSTRAINT permission_audit_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 5041 (class 2606 OID 101712)
-- Name: pin_change_history pin_change_history_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.pin_change_history
    ADD CONSTRAINT pin_change_history_pkey PRIMARY KEY (id);


--
-- TOC entry 5036 (class 2606 OID 101704)
-- Name: pin_settings pin_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.pin_settings
    ADD CONSTRAINT pin_settings_pkey PRIMARY KEY (id);


--
-- TOC entry 4966 (class 2606 OID 99290)
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- TOC entry 4983 (class 2606 OID 99323)
-- Name: section_assignments section_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.section_assignments
    ADD CONSTRAINT section_assignments_pkey PRIMARY KEY (id);


--
-- TOC entry 5108 (class 2606 OID 111489)
-- Name: security_audit_events security_audit_events_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.security_audit_events
    ADD CONSTRAINT security_audit_events_pkey PRIMARY KEY (id);


--
-- TOC entry 5083 (class 2606 OID 105938)
-- Name: service_groups service_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.service_groups
    ADD CONSTRAINT service_groups_pkey PRIMARY KEY (id);


--
-- TOC entry 5027 (class 2606 OID 99432)
-- Name: songs songs_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.songs
    ADD CONSTRAINT songs_pkey PRIMARY KEY (id);


--
-- TOC entry 5075 (class 2606 OID 105921)
-- Name: special_songs special_songs_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.special_songs
    ADD CONSTRAINT special_songs_pkey PRIMARY KEY (id);


--
-- TOC entry 5008 (class 2606 OID 99387)
-- Name: task_assignments task_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.task_assignments
    ADD CONSTRAINT task_assignments_pkey PRIMARY KEY (id);


--
-- TOC entry 5004 (class 2606 OID 99378)
-- Name: tasks tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_pkey PRIMARY KEY (id);


--
-- TOC entry 5098 (class 2606 OID 111469)
-- Name: temporary_pins temporary_pins_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.temporary_pins
    ADD CONSTRAINT temporary_pins_pkey PRIMARY KEY (id);


--
-- TOC entry 5087 (class 2606 OID 105948)
-- Name: territories territories_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.territories
    ADD CONSTRAINT territories_pkey PRIMARY KEY (id);


--
-- TOC entry 5000 (class 2606 OID 99369)
-- Name: weekend_meeting_parts weekend_meeting_parts_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.weekend_meeting_parts
    ADD CONSTRAINT weekend_meeting_parts_pkey PRIMARY KEY (id);


--
-- TOC entry 4997 (class 2606 OID 99360)
-- Name: weekend_meetings weekend_meetings_pkey; Type: CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.weekend_meetings
    ADD CONSTRAINT weekend_meetings_pkey PRIMARY KEY (id);


--
-- TOC entry 5100 (class 1259 OID 111494)
-- Name: account_lockouts_congregation_id_member_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX account_lockouts_congregation_id_member_id_idx ON public.account_lockouts USING btree (congregation_id, member_id);


--
-- TOC entry 5101 (class 1259 OID 111495)
-- Name: account_lockouts_is_active_unlock_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX account_lockouts_is_active_unlock_at_idx ON public.account_lockouts USING btree (is_active, unlock_at);


--
-- TOC entry 4984 (class 1259 OID 99441)
-- Name: assignment_history_congregation_id_member_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX assignment_history_congregation_id_member_id_idx ON public.assignment_history USING btree (congregation_id, member_id);


--
-- TOC entry 4985 (class 1259 OID 99442)
-- Name: assignment_history_congregation_id_section_type_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX assignment_history_congregation_id_section_type_idx ON public.assignment_history USING btree (congregation_id, section_type);


--
-- TOC entry 4986 (class 1259 OID 99443)
-- Name: assignment_history_created_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX assignment_history_created_at_idx ON public.assignment_history USING btree (created_at);


--
-- TOC entry 5065 (class 1259 OID 103432)
-- Name: communication_preferences_congregation_id_member_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX communication_preferences_congregation_id_member_id_idx ON public.communication_preferences USING btree (congregation_id, member_id);


--
-- TOC entry 5066 (class 1259 OID 103433)
-- Name: communication_preferences_congregation_id_member_id_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX communication_preferences_congregation_id_member_id_key ON public.communication_preferences USING btree (congregation_id, member_id);


--
-- TOC entry 5069 (class 1259 OID 103434)
-- Name: communication_templates_congregation_id_category_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX communication_templates_congregation_id_category_idx ON public.communication_templates USING btree (congregation_id, category);


--
-- TOC entry 5070 (class 1259 OID 103435)
-- Name: communication_templates_congregation_id_name_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX communication_templates_congregation_id_name_idx ON public.communication_templates USING btree (congregation_id, name);


--
-- TOC entry 5076 (class 1259 OID 105950)
-- Name: congregation_settings_congregation_id_setting_key_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX congregation_settings_congregation_id_setting_key_idx ON public.congregation_settings USING btree (congregation_id, setting_key);


--
-- TOC entry 5077 (class 1259 OID 105951)
-- Name: congregation_settings_congregation_id_setting_key_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX congregation_settings_congregation_id_setting_key_key ON public.congregation_settings USING btree (congregation_id, setting_key);


--
-- TOC entry 5046 (class 1259 OID 103421)
-- Name: document_access_logs_document_id_accessed_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_access_logs_document_id_accessed_at_idx ON public.document_access_logs USING btree (document_id, accessed_at);


--
-- TOC entry 5047 (class 1259 OID 103422)
-- Name: document_access_logs_member_id_accessed_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_access_logs_member_id_accessed_at_idx ON public.document_access_logs USING btree (member_id, accessed_at);


--
-- TOC entry 5050 (class 1259 OID 103423)
-- Name: document_comments_document_id_created_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_comments_document_id_created_at_idx ON public.document_comments USING btree (document_id, created_at);


--
-- TOC entry 5051 (class 1259 OID 103424)
-- Name: document_comments_member_id_created_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_comments_member_id_created_at_idx ON public.document_comments USING btree (member_id, created_at);


--
-- TOC entry 5042 (class 1259 OID 103419)
-- Name: document_folders_congregation_id_parent_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_folders_congregation_id_parent_id_idx ON public.document_folders USING btree (congregation_id, parent_id);


--
-- TOC entry 5043 (class 1259 OID 103420)
-- Name: document_folders_congregation_id_path_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_folders_congregation_id_path_idx ON public.document_folders USING btree (congregation_id, path);


--
-- TOC entry 5054 (class 1259 OID 103426)
-- Name: document_workflows_assigned_to_id_status_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_workflows_assigned_to_id_status_idx ON public.document_workflows USING btree (assigned_to_id, status);


--
-- TOC entry 5055 (class 1259 OID 103425)
-- Name: document_workflows_document_id_status_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_workflows_document_id_status_idx ON public.document_workflows USING btree (document_id, status);


--
-- TOC entry 5056 (class 1259 OID 103427)
-- Name: document_workflows_due_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX document_workflows_due_date_idx ON public.document_workflows USING btree (due_date);


--
-- TOC entry 4973 (class 1259 OID 108633)
-- Name: elder_permissions_assigned_by_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX elder_permissions_assigned_by_idx ON public.elder_permissions USING btree (assigned_by);


--
-- TOC entry 4974 (class 1259 OID 108632)
-- Name: elder_permissions_congregation_id_section_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX elder_permissions_congregation_id_section_id_idx ON public.elder_permissions USING btree (congregation_id, section_id);


--
-- TOC entry 4975 (class 1259 OID 108634)
-- Name: elder_permissions_is_active_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX elder_permissions_is_active_idx ON public.elder_permissions USING btree (is_active);


--
-- TOC entry 4976 (class 1259 OID 108635)
-- Name: elder_permissions_member_id_section_id_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX elder_permissions_member_id_section_id_key ON public.elder_permissions USING btree (member_id, section_id);


--
-- TOC entry 5022 (class 1259 OID 99457)
-- Name: events_congregation_id_category_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX events_congregation_id_category_idx ON public.events USING btree (congregation_id, category);


--
-- TOC entry 5023 (class 1259 OID 99456)
-- Name: events_congregation_id_event_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX events_congregation_id_event_date_idx ON public.events USING btree (congregation_id, event_date);


--
-- TOC entry 5009 (class 1259 OID 99452)
-- Name: field_service_records_congregation_id_service_month_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX field_service_records_congregation_id_service_month_idx ON public.field_service_records USING btree (congregation_id, service_month);


--
-- TOC entry 5010 (class 1259 OID 99453)
-- Name: field_service_records_member_id_service_month_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX field_service_records_member_id_service_month_key ON public.field_service_records USING btree (member_id, service_month);


--
-- TOC entry 5013 (class 1259 OID 99455)
-- Name: letters_congregation_id_category_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX letters_congregation_id_category_idx ON public.letters USING btree (congregation_id, category);


--
-- TOC entry 5014 (class 1259 OID 103439)
-- Name: letters_congregation_id_expiration_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX letters_congregation_id_expiration_date_idx ON public.letters USING btree (congregation_id, expiration_date);


--
-- TOC entry 5015 (class 1259 OID 103437)
-- Name: letters_congregation_id_folder_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX letters_congregation_id_folder_id_idx ON public.letters USING btree (congregation_id, folder_id);


--
-- TOC entry 5016 (class 1259 OID 103436)
-- Name: letters_congregation_id_status_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX letters_congregation_id_status_idx ON public.letters USING btree (congregation_id, status);


--
-- TOC entry 5017 (class 1259 OID 103438)
-- Name: letters_congregation_id_upload_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX letters_congregation_id_upload_date_idx ON public.letters USING btree (congregation_id, upload_date);


--
-- TOC entry 5018 (class 1259 OID 99454)
-- Name: letters_congregation_id_visibility_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX letters_congregation_id_visibility_idx ON public.letters USING btree (congregation_id, visibility);


--
-- TOC entry 5021 (class 1259 OID 103440)
-- Name: letters_title_description_searchable_text_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX letters_title_description_searchable_text_idx ON public.letters USING btree (title, description, searchable_text);


--
-- TOC entry 5029 (class 1259 OID 99588)
-- Name: member_change_history_congregation_id_change_type_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX member_change_history_congregation_id_change_type_idx ON public.member_change_history USING btree (congregation_id, change_type);


--
-- TOC entry 5030 (class 1259 OID 99587)
-- Name: member_change_history_congregation_id_member_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX member_change_history_congregation_id_member_id_idx ON public.member_change_history USING btree (congregation_id, member_id);


--
-- TOC entry 5031 (class 1259 OID 99589)
-- Name: member_change_history_created_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX member_change_history_created_at_idx ON public.member_change_history USING btree (created_at);


--
-- TOC entry 4967 (class 1259 OID 99605)
-- Name: members_congregation_id_email_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX members_congregation_id_email_key ON public.members USING btree (congregation_id, email);


--
-- TOC entry 4968 (class 1259 OID 99435)
-- Name: members_congregation_id_is_active_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX members_congregation_id_is_active_idx ON public.members USING btree (congregation_id, is_active);


--
-- TOC entry 4969 (class 1259 OID 99434)
-- Name: members_congregation_id_role_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX members_congregation_id_role_idx ON public.members USING btree (congregation_id, role);


--
-- TOC entry 4970 (class 1259 OID 111454)
-- Name: members_congregation_id_service_group_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX members_congregation_id_service_group_idx ON public.members USING btree (congregation_id, service_group);


--
-- TOC entry 4992 (class 1259 OID 99445)
-- Name: midweek_meeting_parts_meeting_id_part_number_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX midweek_meeting_parts_meeting_id_part_number_idx ON public.midweek_meeting_parts USING btree (meeting_id, part_number);


--
-- TOC entry 4989 (class 1259 OID 99444)
-- Name: midweek_meetings_congregation_id_meeting_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX midweek_meetings_congregation_id_meeting_date_idx ON public.midweek_meetings USING btree (congregation_id, meeting_date);


--
-- TOC entry 5059 (class 1259 OID 103429)
-- Name: notifications_congregation_id_category_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX notifications_congregation_id_category_idx ON public.notifications USING btree (congregation_id, category);


--
-- TOC entry 5060 (class 1259 OID 103428)
-- Name: notifications_congregation_id_recipient_id_read_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX notifications_congregation_id_recipient_id_read_at_idx ON public.notifications USING btree (congregation_id, recipient_id, read_at);


--
-- TOC entry 5061 (class 1259 OID 103431)
-- Name: notifications_congregation_id_scheduled_for_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX notifications_congregation_id_scheduled_for_idx ON public.notifications USING btree (congregation_id, scheduled_for);


--
-- TOC entry 5062 (class 1259 OID 103430)
-- Name: notifications_congregation_id_status_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX notifications_congregation_id_status_idx ON public.notifications USING btree (congregation_id, status);


--
-- TOC entry 5088 (class 1259 OID 108629)
-- Name: permission_audit_logs_congregation_id_section_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX permission_audit_logs_congregation_id_section_id_idx ON public.permission_audit_logs USING btree (congregation_id, section_id);


--
-- TOC entry 5089 (class 1259 OID 108628)
-- Name: permission_audit_logs_congregation_id_user_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX permission_audit_logs_congregation_id_user_id_idx ON public.permission_audit_logs USING btree (congregation_id, user_id);


--
-- TOC entry 5090 (class 1259 OID 108630)
-- Name: permission_audit_logs_performed_by_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX permission_audit_logs_performed_by_idx ON public.permission_audit_logs USING btree (performed_by);


--
-- TOC entry 5093 (class 1259 OID 108631)
-- Name: permission_audit_logs_timestamp_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX permission_audit_logs_timestamp_idx ON public.permission_audit_logs USING btree ("timestamp");


--
-- TOC entry 5037 (class 1259 OID 101715)
-- Name: pin_change_history_congregation_id_change_type_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX pin_change_history_congregation_id_change_type_idx ON public.pin_change_history USING btree (congregation_id, change_type);


--
-- TOC entry 5038 (class 1259 OID 101714)
-- Name: pin_change_history_congregation_id_member_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX pin_change_history_congregation_id_member_id_idx ON public.pin_change_history USING btree (congregation_id, member_id);


--
-- TOC entry 5039 (class 1259 OID 101716)
-- Name: pin_change_history_created_at_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX pin_change_history_created_at_idx ON public.pin_change_history USING btree (created_at);


--
-- TOC entry 5034 (class 1259 OID 101713)
-- Name: pin_settings_congregation_id_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX pin_settings_congregation_id_key ON public.pin_settings USING btree (congregation_id);


--
-- TOC entry 4964 (class 1259 OID 99433)
-- Name: roles_name_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX roles_name_key ON public.roles USING btree (name);


--
-- TOC entry 4979 (class 1259 OID 99439)
-- Name: section_assignments_congregation_id_is_active_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX section_assignments_congregation_id_is_active_idx ON public.section_assignments USING btree (congregation_id, is_active);


--
-- TOC entry 4980 (class 1259 OID 99438)
-- Name: section_assignments_congregation_id_section_type_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX section_assignments_congregation_id_section_type_idx ON public.section_assignments USING btree (congregation_id, section_type);


--
-- TOC entry 4981 (class 1259 OID 99440)
-- Name: section_assignments_member_id_section_type_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX section_assignments_member_id_section_type_key ON public.section_assignments USING btree (member_id, section_type);


--
-- TOC entry 5104 (class 1259 OID 111496)
-- Name: security_audit_events_congregation_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX security_audit_events_congregation_id_idx ON public.security_audit_events USING btree (congregation_id);


--
-- TOC entry 5105 (class 1259 OID 111498)
-- Name: security_audit_events_event_type_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX security_audit_events_event_type_idx ON public.security_audit_events USING btree (event_type);


--
-- TOC entry 5106 (class 1259 OID 111497)
-- Name: security_audit_events_member_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX security_audit_events_member_id_idx ON public.security_audit_events USING btree (member_id);


--
-- TOC entry 5109 (class 1259 OID 111499)
-- Name: security_audit_events_timestamp_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX security_audit_events_timestamp_idx ON public.security_audit_events USING btree ("timestamp");


--
-- TOC entry 5080 (class 1259 OID 105953)
-- Name: service_groups_congregation_id_group_number_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX service_groups_congregation_id_group_number_key ON public.service_groups USING btree (congregation_id, group_number);


--
-- TOC entry 5081 (class 1259 OID 105952)
-- Name: service_groups_congregation_id_is_active_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX service_groups_congregation_id_is_active_idx ON public.service_groups USING btree (congregation_id, is_active);


--
-- TOC entry 5028 (class 1259 OID 99458)
-- Name: songs_song_number_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX songs_song_number_key ON public.songs USING btree (song_number);


--
-- TOC entry 5073 (class 1259 OID 105949)
-- Name: special_songs_key_name_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX special_songs_key_name_key ON public.special_songs USING btree (key_name);


--
-- TOC entry 5005 (class 1259 OID 99451)
-- Name: task_assignments_assigned_member_id_status_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX task_assignments_assigned_member_id_status_idx ON public.task_assignments USING btree (assigned_member_id, status);


--
-- TOC entry 5006 (class 1259 OID 99450)
-- Name: task_assignments_congregation_id_assigned_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX task_assignments_congregation_id_assigned_date_idx ON public.task_assignments USING btree (congregation_id, assigned_date);


--
-- TOC entry 5001 (class 1259 OID 99448)
-- Name: tasks_congregation_id_category_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX tasks_congregation_id_category_idx ON public.tasks USING btree (congregation_id, category);


--
-- TOC entry 5002 (class 1259 OID 99449)
-- Name: tasks_congregation_id_is_active_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX tasks_congregation_id_is_active_idx ON public.tasks USING btree (congregation_id, is_active);


--
-- TOC entry 5094 (class 1259 OID 111490)
-- Name: temporary_pins_congregation_id_member_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX temporary_pins_congregation_id_member_id_idx ON public.temporary_pins USING btree (congregation_id, member_id);


--
-- TOC entry 5095 (class 1259 OID 111493)
-- Name: temporary_pins_congregation_id_member_id_key; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE UNIQUE INDEX temporary_pins_congregation_id_member_id_key ON public.temporary_pins USING btree (congregation_id, member_id);


--
-- TOC entry 5096 (class 1259 OID 111491)
-- Name: temporary_pins_expiration_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX temporary_pins_expiration_date_idx ON public.temporary_pins USING btree (expiration_date);


--
-- TOC entry 5099 (class 1259 OID 111492)
-- Name: temporary_pins_used_expiration_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX temporary_pins_used_expiration_date_idx ON public.temporary_pins USING btree (used, expiration_date);


--
-- TOC entry 5084 (class 1259 OID 105955)
-- Name: territories_assigned_to_id_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX territories_assigned_to_id_idx ON public.territories USING btree (assigned_to_id);


--
-- TOC entry 5085 (class 1259 OID 105954)
-- Name: territories_congregation_id_status_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX territories_congregation_id_status_idx ON public.territories USING btree (congregation_id, status);


--
-- TOC entry 4998 (class 1259 OID 99447)
-- Name: weekend_meeting_parts_meeting_id_part_type_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX weekend_meeting_parts_meeting_id_part_type_idx ON public.weekend_meeting_parts USING btree (meeting_id, part_type);


--
-- TOC entry 4995 (class 1259 OID 99446)
-- Name: weekend_meetings_congregation_id_meeting_date_idx; Type: INDEX; Schema: public; Owner: mywebsites
--

CREATE INDEX weekend_meetings_congregation_id_meeting_date_idx ON public.weekend_meetings USING btree (congregation_id, meeting_date);


--
-- TOC entry 5172 (class 2606 OID 111515)
-- Name: account_lockouts account_lockouts_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.account_lockouts
    ADD CONSTRAINT account_lockouts_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5173 (class 2606 OID 111520)
-- Name: account_lockouts account_lockouts_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.account_lockouts
    ADD CONSTRAINT account_lockouts_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5174 (class 2606 OID 111525)
-- Name: account_lockouts account_lockouts_unlocked_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.account_lockouts
    ADD CONSTRAINT account_lockouts_unlocked_by_fkey FOREIGN KEY (unlocked_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5118 (class 2606 OID 99504)
-- Name: assignment_history assignment_history_assigned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.assignment_history
    ADD CONSTRAINT assignment_history_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5119 (class 2606 OID 99509)
-- Name: assignment_history assignment_history_assigned_to_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.assignment_history
    ADD CONSTRAINT assignment_history_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5120 (class 2606 OID 99494)
-- Name: assignment_history assignment_history_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.assignment_history
    ADD CONSTRAINT assignment_history_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5121 (class 2606 OID 99499)
-- Name: assignment_history assignment_history_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.assignment_history
    ADD CONSTRAINT assignment_history_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5159 (class 2606 OID 103521)
-- Name: communication_preferences communication_preferences_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.communication_preferences
    ADD CONSTRAINT communication_preferences_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5160 (class 2606 OID 103526)
-- Name: communication_preferences communication_preferences_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.communication_preferences
    ADD CONSTRAINT communication_preferences_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5161 (class 2606 OID 103531)
-- Name: communication_templates communication_templates_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.communication_templates
    ADD CONSTRAINT communication_templates_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5162 (class 2606 OID 105956)
-- Name: congregation_settings congregation_settings_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.congregation_settings
    ADD CONSTRAINT congregation_settings_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5148 (class 2606 OID 103466)
-- Name: document_access_logs document_access_logs_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_access_logs
    ADD CONSTRAINT document_access_logs_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.letters(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5149 (class 2606 OID 103471)
-- Name: document_access_logs document_access_logs_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_access_logs
    ADD CONSTRAINT document_access_logs_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5150 (class 2606 OID 103476)
-- Name: document_comments document_comments_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_comments
    ADD CONSTRAINT document_comments_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.letters(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5151 (class 2606 OID 103481)
-- Name: document_comments document_comments_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_comments
    ADD CONSTRAINT document_comments_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5152 (class 2606 OID 103486)
-- Name: document_comments document_comments_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_comments
    ADD CONSTRAINT document_comments_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.document_comments(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5146 (class 2606 OID 103456)
-- Name: document_folders document_folders_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_folders
    ADD CONSTRAINT document_folders_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5147 (class 2606 OID 103461)
-- Name: document_folders document_folders_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_folders
    ADD CONSTRAINT document_folders_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.document_folders(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5153 (class 2606 OID 103501)
-- Name: document_workflows document_workflows_assigned_by_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_workflows
    ADD CONSTRAINT document_workflows_assigned_by_id_fkey FOREIGN KEY (assigned_by_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5154 (class 2606 OID 103496)
-- Name: document_workflows document_workflows_assigned_to_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_workflows
    ADD CONSTRAINT document_workflows_assigned_to_id_fkey FOREIGN KEY (assigned_to_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5155 (class 2606 OID 103491)
-- Name: document_workflows document_workflows_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.document_workflows
    ADD CONSTRAINT document_workflows_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.letters(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5112 (class 2606 OID 108636)
-- Name: elder_permissions elder_permissions_assigned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.elder_permissions
    ADD CONSTRAINT elder_permissions_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 5113 (class 2606 OID 99469)
-- Name: elder_permissions elder_permissions_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.elder_permissions
    ADD CONSTRAINT elder_permissions_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5114 (class 2606 OID 99474)
-- Name: elder_permissions elder_permissions_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.elder_permissions
    ADD CONSTRAINT elder_permissions_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5137 (class 2606 OID 99574)
-- Name: events events_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5130 (class 2606 OID 99554)
-- Name: field_service_records field_service_records_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.field_service_records
    ADD CONSTRAINT field_service_records_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5131 (class 2606 OID 99559)
-- Name: field_service_records field_service_records_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.field_service_records
    ADD CONSTRAINT field_service_records_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5132 (class 2606 OID 103441)
-- Name: letters letters_approved_by_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.letters
    ADD CONSTRAINT letters_approved_by_id_fkey FOREIGN KEY (approved_by_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5133 (class 2606 OID 99564)
-- Name: letters letters_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.letters
    ADD CONSTRAINT letters_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5134 (class 2606 OID 103446)
-- Name: letters letters_folder_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.letters
    ADD CONSTRAINT letters_folder_id_fkey FOREIGN KEY (folder_id) REFERENCES public.document_folders(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5135 (class 2606 OID 103451)
-- Name: letters letters_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.letters
    ADD CONSTRAINT letters_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.letters(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5136 (class 2606 OID 99569)
-- Name: letters letters_uploaded_by_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.letters
    ADD CONSTRAINT letters_uploaded_by_id_fkey FOREIGN KEY (uploaded_by_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5138 (class 2606 OID 99600)
-- Name: member_change_history member_change_history_changed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.member_change_history
    ADD CONSTRAINT member_change_history_changed_by_fkey FOREIGN KEY (changed_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 5139 (class 2606 OID 99590)
-- Name: member_change_history member_change_history_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.member_change_history
    ADD CONSTRAINT member_change_history_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5140 (class 2606 OID 99595)
-- Name: member_change_history member_change_history_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.member_change_history
    ADD CONSTRAINT member_change_history_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5110 (class 2606 OID 99459)
-- Name: members members_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.members
    ADD CONSTRAINT members_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5111 (class 2606 OID 99464)
-- Name: members members_role_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.members
    ADD CONSTRAINT members_role_fkey FOREIGN KEY (role) REFERENCES public.roles(name) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 5123 (class 2606 OID 99519)
-- Name: midweek_meeting_parts midweek_meeting_parts_meeting_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.midweek_meeting_parts
    ADD CONSTRAINT midweek_meeting_parts_meeting_id_fkey FOREIGN KEY (meeting_id) REFERENCES public.midweek_meetings(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5122 (class 2606 OID 99514)
-- Name: midweek_meetings midweek_meetings_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.midweek_meetings
    ADD CONSTRAINT midweek_meetings_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5156 (class 2606 OID 103506)
-- Name: notifications notifications_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5157 (class 2606 OID 103511)
-- Name: notifications notifications_recipient_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_recipient_id_fkey FOREIGN KEY (recipient_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5158 (class 2606 OID 103516)
-- Name: notifications notifications_sender_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_sender_id_fkey FOREIGN KEY (sender_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5166 (class 2606 OID 108641)
-- Name: permission_audit_logs permission_audit_logs_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.permission_audit_logs
    ADD CONSTRAINT permission_audit_logs_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5167 (class 2606 OID 108651)
-- Name: permission_audit_logs permission_audit_logs_performed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.permission_audit_logs
    ADD CONSTRAINT permission_audit_logs_performed_by_fkey FOREIGN KEY (performed_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5168 (class 2606 OID 108646)
-- Name: permission_audit_logs permission_audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.permission_audit_logs
    ADD CONSTRAINT permission_audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5143 (class 2606 OID 101737)
-- Name: pin_change_history pin_change_history_changed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.pin_change_history
    ADD CONSTRAINT pin_change_history_changed_by_fkey FOREIGN KEY (changed_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 5144 (class 2606 OID 101727)
-- Name: pin_change_history pin_change_history_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.pin_change_history
    ADD CONSTRAINT pin_change_history_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5145 (class 2606 OID 101732)
-- Name: pin_change_history pin_change_history_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.pin_change_history
    ADD CONSTRAINT pin_change_history_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5141 (class 2606 OID 101717)
-- Name: pin_settings pin_settings_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.pin_settings
    ADD CONSTRAINT pin_settings_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5142 (class 2606 OID 101722)
-- Name: pin_settings pin_settings_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.pin_settings
    ADD CONSTRAINT pin_settings_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5115 (class 2606 OID 99489)
-- Name: section_assignments section_assignments_assigned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.section_assignments
    ADD CONSTRAINT section_assignments_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5116 (class 2606 OID 99479)
-- Name: section_assignments section_assignments_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.section_assignments
    ADD CONSTRAINT section_assignments_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5117 (class 2606 OID 99484)
-- Name: section_assignments section_assignments_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.section_assignments
    ADD CONSTRAINT section_assignments_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5175 (class 2606 OID 111530)
-- Name: security_audit_events security_audit_events_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.security_audit_events
    ADD CONSTRAINT security_audit_events_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5176 (class 2606 OID 111535)
-- Name: security_audit_events security_audit_events_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.security_audit_events
    ADD CONSTRAINT security_audit_events_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5177 (class 2606 OID 111540)
-- Name: security_audit_events security_audit_events_performed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.security_audit_events
    ADD CONSTRAINT security_audit_events_performed_by_fkey FOREIGN KEY (performed_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5163 (class 2606 OID 105961)
-- Name: service_groups service_groups_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.service_groups
    ADD CONSTRAINT service_groups_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5127 (class 2606 OID 99549)
-- Name: task_assignments task_assignments_assigned_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.task_assignments
    ADD CONSTRAINT task_assignments_assigned_member_id_fkey FOREIGN KEY (assigned_member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5128 (class 2606 OID 99539)
-- Name: task_assignments task_assignments_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.task_assignments
    ADD CONSTRAINT task_assignments_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5129 (class 2606 OID 99544)
-- Name: task_assignments task_assignments_task_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.task_assignments
    ADD CONSTRAINT task_assignments_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5126 (class 2606 OID 99534)
-- Name: tasks tasks_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5169 (class 2606 OID 111500)
-- Name: temporary_pins temporary_pins_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.temporary_pins
    ADD CONSTRAINT temporary_pins_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5170 (class 2606 OID 111510)
-- Name: temporary_pins temporary_pins_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.temporary_pins
    ADD CONSTRAINT temporary_pins_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5171 (class 2606 OID 111505)
-- Name: temporary_pins temporary_pins_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.temporary_pins
    ADD CONSTRAINT temporary_pins_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5164 (class 2606 OID 105971)
-- Name: territories territories_assigned_to_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.territories
    ADD CONSTRAINT territories_assigned_to_id_fkey FOREIGN KEY (assigned_to_id) REFERENCES public.members(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 5165 (class 2606 OID 105966)
-- Name: territories territories_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.territories
    ADD CONSTRAINT territories_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5125 (class 2606 OID 99529)
-- Name: weekend_meeting_parts weekend_meeting_parts_meeting_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.weekend_meeting_parts
    ADD CONSTRAINT weekend_meeting_parts_meeting_id_fkey FOREIGN KEY (meeting_id) REFERENCES public.weekend_meetings(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5124 (class 2606 OID 99524)
-- Name: weekend_meetings weekend_meetings_congregation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: mywebsites
--

ALTER TABLE ONLY public.weekend_meetings
    ADD CONSTRAINT weekend_meetings_congregation_id_fkey FOREIGN KEY (congregation_id) REFERENCES public.congregations(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 5365 (class 0 OID 0)
-- Dependencies: 5
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: mywebsites
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- TOC entry 2183 (class 826 OID 95271)
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: -; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres GRANT ALL ON TABLES TO mywebsites;


-- Completed on 2025-07-25 01:37:56

--
-- PostgreSQL database dump complete
--

