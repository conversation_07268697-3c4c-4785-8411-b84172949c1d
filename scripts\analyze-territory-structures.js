const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

async function analyzeTerritoryStructures() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  
  // Sample a few different territories to understand structures
  const samplesToCheck = ['012', '025', '051', '070', '100'];
  
  console.log('🔍 Analyzing territory Excel file structures...\n');
  
  for (const territoryNum of samplesToCheck) {
    const fileName = `Terr. ${territoryNum}.xlsx`;
    const filePath = path.join(territoriosDir, fileName);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ File not found: ${fileName}`);
      continue;
    }
    
    try {
      console.log(`📋 Territory ${territoryNum} (${fileName}):`);
      
      const workbook = XLSX.readFile(filePath);
      const sheetNames = workbook.SheetNames;
      console.log(`  Sheets: ${sheetNames.join(', ')}`);
      
      // Analyze first sheet
      const firstSheet = workbook.Sheets[sheetNames[0]];
      const data = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
      
      console.log(`  Rows: ${data.length}`);
      console.log(`  First few rows:`);
      
      // Show first 5 non-empty rows
      let rowCount = 0;
      for (let i = 0; i < Math.min(data.length, 10) && rowCount < 5; i++) {
        const row = data[i];
        if (row && row.length > 0 && row.some(cell => cell && cell.toString().trim())) {
          console.log(`    Row ${i + 1}: [${row.map(cell => `"${cell || ''}"`).join(', ')}]`);
          rowCount++;
        }
      }
      
      // Look for address patterns
      const addressRows = data.filter(row => {
        if (!row || row.length === 0) return false;
        const firstCell = row[0];
        if (!firstCell) return false;
        const cellStr = firstCell.toString().trim();
        // Look for address patterns (numbers followed by street names)
        return /^\d+\s+[A-Z]/.test(cellStr) || /^\d+\s+(NW|SW|NE|SE|W|E|N|S)/.test(cellStr);
      });
      
      console.log(`  Potential addresses found: ${addressRows.length}`);
      if (addressRows.length > 0) {
        console.log(`  Sample addresses:`);
        addressRows.slice(0, 3).forEach((row, idx) => {
          console.log(`    ${idx + 1}. ${row[0]}`);
        });
      }
      
      console.log('');
      
    } catch (error) {
      console.log(`  ❌ Error reading file: ${error.message}`);
      console.log('');
    }
  }
}

analyzeTerritoryStructures();
