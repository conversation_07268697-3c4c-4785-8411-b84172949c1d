import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyToken } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { congregationId } = authResult;

    // Get territory statistics
    const territoryStats = await prisma.territory.groupBy({
      by: ['status'],
      where: {
        congregationId: congregationId
      },
      _count: {
        id: true
      }
    });

    // Calculate totals
    const stats = {
      totalTerritories: 0,
      assignedTerritories: 0,
      availableTerritories: 0,
      completedTerritories: 0,
      outOfServiceTerritories: 0
    };

    territoryStats.forEach(stat => {
      stats.totalTerritories += stat._count.id;
      
      switch (stat.status) {
        case 'assigned':
          stats.assignedTerritories = stat._count.id;
          break;
        case 'available':
          stats.availableTerritories = stat._count.id;
          break;
        case 'completed':
          stats.completedTerritories = stat._count.id;
          break;
        case 'out_of_service':
          stats.outOfServiceTerritories = stat._count.id;
          break;
      }
    });

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error fetching territory statistics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
