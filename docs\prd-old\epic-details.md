# Epic Details

## Epic 1: Foundation & Database Migration (Weeks 1-3)

**Epic Goal:** Establish the foundational Next.js/PostgreSQL application with complete database migration from MySQL, simple authentication system, and multi-congregation architecture. This epic delivers a deployable application with working authentication, complete data migration, and preserved login experience.

### Story 1.1: Next.js Project Setup and Development Environment

As a developer,
I want to set up a Next.js fullstack project with PostgreSQL and Prisma,
so that I have a modern development environment for building the Hermanos app with exact UI preservation.

**Acceptance Criteria:**

1. Next.js 14+ project created with latest versions of TypeScript, App Router, and Tailwind CSS
2. PostgreSQL database connection established with latest Prisma ORM version
3. Development environment includes latest ESLint, Prettier for code quality
4. Complete `.env` file configuration with all ports, URLs, and environment variables
5. Never hardcode localhost, ports, or URLs in any codebase files
6. Single `npm run dev` script initializes both frontend and backend using .env variables
7. Health check API endpoint using environment configuration

### Story 1.2: Complete MySQL to PostgreSQL Migration

As a system administrator,
I want to migrate all 41 MySQL tables to PostgreSQL with zero data loss,
so that the Hermanos app preserves all existing functionality and data.

**Acceptance Criteria:**

1. Prisma schema defines all 41 tables from existing MySQL structure
2. All tables include congregation_id foreign key for multi-congregation isolation
3. Complete data migration script preserves all existing records and relationships
4. Coral Oeste Spanish congregation data migrated successfully
5. Database indexes optimized for congregation-scoped queries
6. Migration validation confirms 100% data integrity
7. All existing table relationships and constraints preserved

### Story 1.3: Single Authentication System with Role-Based Access

As a congregation member,
I want to authenticate using the exact same login process as before,
so that I can access my congregation's features, and if I'm an elder/ministerial servant, see the admin button.

**Acceptance Criteria:**

1. Single login page for all users preserving exact existing UI design and workflow
2. Simple JWT authentication with 60-day mobile-friendly expiration (configurable by developers/elders)
3. Role-based access control preserves existing roles and permissions
4. Authentication middleware protects API routes with congregation isolation
5. Secure bcrypt PIN hashing without over-complicating security
6. Coral Oeste Spanish congregation authentication works with existing credentials
7. Admin token expiration can be disabled by developers or elders with proper rights

### Story 1.4: Dashboard with Conditional Admin Access

As a congregation member,
I want the dashboard to look and work exactly like it does now,
and if I'm an elder/ministerial servant/overseer, I want to see the "Administración" button.

**Acceptance Criteria:**

1. Dashboard preserves exact visual design, colors, spacing, and typography for all users
2. Card-based navigation maintains identical layout and touch targets
3. "Administración" button appears only for elders, ministerial servants, and overseers
4. Admin button matches existing design language and positioning from screenshots
5. Mobile-first responsive design maintains current interface behavior
6. Spanish-first interface preserves all existing terminology and text
7. Publishers see standard dashboard without admin access button

## Epic 2: Core Member & Authentication System

**Epic Goal:** Implement comprehensive member management, role-based permissions, congregation-specific authentication workflows, and administrative delegation system with full CRUD operations for members. This epic delivers complete user management capabilities with proper security, multi-congregation support, and administrative delegation hierarchy.

### Story 2.1: Administrative Delegation System

As a coordinator elder,
I want to assign specific administrative sections to elders and ministerial servants,
so that I can delegate administrative responsibilities while maintaining oversight and control.

**Acceptance Criteria:**

1. Administrative section assignment interface for coordinator elders to delegate responsibilities
2. Section-based permission system (Field Service, Meetings, Tasks, Letters, Events, etc.)
3. Elder and ministerial servant assignment to multiple sections with defined scope
4. Assignment history tracking and modification capabilities
5. Section responsibility transfer workflow with proper handover procedures
6. Administrative assignment notifications and confirmation system
7. Coordinator oversight dashboard showing all section assignments and activities

### Story 2.2: Enhanced Member Profile Management

As a congregation administrator,
I want to create and manage member profiles with roles and permissions,
so that I can maintain accurate congregation membership records with proper delegation authority.

**Acceptance Criteria:**

1. Admin interface for creating new members with name, contact information, and role assignment
2. Member profile editing with validation for required fields and delegation authority validation
3. Role assignment supports all congregation roles with proper authorization checking
4. Member profiles are congregation-specific and isolated by congregation_id
5. Member search and filtering functionality by name, role, and status
6. Member deactivation/reactivation without data deletion
7. Audit trail for member profile changes with timestamp and admin identification

### Story 2.3: Enhanced PIN Management and Security

As a congregation administrator,
I want to manage member PINs with configurable length requirements,
so that members can securely access their congregation features with proper security controls.

**Acceptance Criteria:**

1. PIN generation and assignment for new members with length validation
2. PIN reset functionality for existing members with proper authorization
3. PIN validation ensures uniqueness within congregation and length constraints
4. Secure PIN storage with proper hashing for both congregation and member PINs
5. PIN change history tracking for security audit
6. Developer role can configure PIN length requirements per congregation
7. PIN complexity requirements configurable per congregation with default settings
