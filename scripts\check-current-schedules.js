/**
 * Check Current Service Schedules
 *
 * This script checks what service schedules are currently in the database
 */

const { PrismaClient } = require('@prisma/client');

async function checkSchedules() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 CHECKING CURRENT SERVICE SCHEDULES');
    console.log('=====================================');

    // Get today's date
    const today = new Date().toISOString().split('T')[0];
    console.log('📅 Today:', today);

    // Get all active service schedule times
    const schedules = await prisma.serviceScheduleTime.findMany({
      where: {
        isActive: true,
        schedule: {
          congregationId: "1441",
          isActive: true
        }
      },
      include: {
        conductor: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      },
      orderBy: [
        { serviceDate: 'asc' },
        { serviceTime: 'asc' }
      ]
    });

    console.log(`\n📊 Found ${schedules.length} active schedules:`);

    schedules.forEach((schedule, index) => {
      const serviceDateStr = schedule.serviceDate.toISOString().split('T')[0];
      const isUpcoming = serviceDateStr >= today;
      const isHistorical = serviceDateStr < today;

      console.log(`\n${index + 1}. Schedule ID: ${schedule.id}`);
      console.log(`   📅 Date: ${serviceDateStr} (${schedule.serviceDate.toDateString()})`);
      console.log(`   ⏰ Time: ${schedule.serviceTime}`);
      console.log(`   📍 Location: ${schedule.location}`);
      console.log(`   👤 Conductor: ${schedule.conductor?.name || 'No conductor'}`);
      console.log(`   📊 Status: ${isUpcoming ? '✅ UPCOMING' : '📜 HISTORICAL'}`);
    });

    // Summary
    const upcomingCount = schedules.filter(s => s.serviceDate.toISOString().split('T')[0] >= today).length;
    const historicalCount = schedules.filter(s => s.serviceDate.toISOString().split('T')[0] < today).length;

    console.log('\n📊 SUMMARY:');
    console.log(`   ✅ Upcoming: ${upcomingCount}`);
    console.log(`   📜 Historical: ${historicalCount}`);
    console.log(`   📋 Total: ${schedules.length}`);

  } catch (error) {
    console.error('❌ Error checking schedules:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSchedules();
