# Story 4.3: Service Group Coordination

**Epic:** Epic 4: Activities & Task Management  
**Story Points:** 8  
**Priority:** High  
**Status:** Draft  

## Story

As a group overseer,
I want to coordinate my service group activities with member management and meeting planning,
so that I can effectively organize field service and support group members.

## Acceptance Criteria

1. **Service group member management with assignment and coordination**
2. **Group meeting scheduling with location and activity planning**
3. **Group service arrangements with territory coordination**
4. **Group communication with announcements and updates**
5. **Group performance tracking with participation monitoring**
6. **Group goal setting with progress tracking**
7. **Group reporting with activity summaries and analytics**

## Dev Notes

### API Endpoints (tRPC)

```typescript
// Service group coordination routes
serviceGroupCoordination: router({
  manageGroupMembers: adminProcedure
    .input(z.object({
      groupId: z.string(),
      memberIds: z.array(z.string()),
      action: z.enum(['add', 'remove', 'transfer'])
    }))
    .mutation(async ({ input, ctx }) => {
      return await serviceGroupService.manageMembers(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    }),

  scheduleGroupMeeting: protectedProcedure
    .input(z.object({
      groupId: z.string(),
      date: z.date(),
      location: z.string(),
      activity: z.string()
    }))
    .mutation(async ({ input, ctx }) => {
      return await serviceGroupService.scheduleMeeting(
        input,
        ctx.user.congregationId,
        ctx.user.id
      );
    })
})
```

## Definition of Done

- [ ] Service group member management implemented
- [ ] Group meeting scheduling functional
- [ ] Group service arrangements working
- [ ] Group communication system complete
- [ ] Group performance tracking implemented
- [ ] Group goal setting functional
- [ ] Group reporting complete
- [ ] All tests pass
- [ ] Code review completed
- [ ] Documentation updated

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: BMad Master Task Executor
- Date: 2025-01-24

### File List
- docs/stories/4.3.story.md (recreated)

### Change Log
- 2025-01-24: Story recreated with service group coordination specification
