/**
 * <PERSON>OL Scraper for JW.org
 *
 * This utility fetches meeting data from the JW.org website
 * and parses it into a structured format for our database.
 */

const puppeteer = require('puppeteer');
const { getSongTitle } = require('./song-titles');
const { getDynamicSongTitle } = require('../services/song-title-service');

/**
 * Base URL for WOL meetings
 */
const BASE_URL = 'https://wol.jw.org/es/wol/meetings/r4/lp-s';

/**
 * Fetches available workbooks (years) from WOL
 * @returns {Promise<Array>} Array of workbook objects
 */
async function fetchWorkbooks() {
  let browser = null;

  try {
    const workbooks = [];

    // Get current year and a few years ahead
    const currentYear = new Date().getFullYear();

    // Launch browser
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-http2']
    });

    const page = await browser.newPage();

    // Create workbooks for current year and next year
    for (let year = currentYear; year <= currentYear + 1; year++) {
      // Check if any weeks exist for this year
      try {
        const testUrl = `${BASE_URL}/${year}/1`;
        console.log(`Checking if year ${year} exists at ${testUrl}`);

        const response = await page.goto(testUrl, {
          waitUntil: 'networkidle2',
          timeout: 30000
        });

        // Check if page exists (status code 200)
        if (response.status() === 200) {
          // If we get here, the year exists
          const startDate = `${year}-01-01`;
          const endDate = `${year}-12-31`;

          workbooks.push({
            workbook_id: `${year}`,
            title: `Reuniones ${year}`,
            url: `${BASE_URL}/${year}`,
            start_date: startDate,
            end_date: endDate
          });

          console.log(`Year ${year} exists and was added to workbooks`);
        }
      } catch (yearError) {
        // For errors, log and continue
        console.error(`Error checking year ${year}:`, yearError.message);
      }
    }

    // Close browser
    await browser.close();

    return workbooks;
  } catch (error) {
    console.error('Error fetching workbooks:', error);
    if (browser) await browser.close();
    throw error;
  }
}

/**
 * Fetches weeks for a specific year
 * @param {number} year - The year to fetch weeks for
 * @returns {Promise<Array>} Array of week objects
 */
async function fetchWeeks(year = new Date().getFullYear()) {
  try {
    console.log(`Generating weeks for ${year}...`);

    const weeks = [];

    // Generate all 52 weeks for any year
    const maxWeek = 52;

    // Generate week data for each week
    for (let weekNumber = 1; weekNumber <= maxWeek; weekNumber++) {
      // Create a unique week ID
      const weekId = `${year}/${weekNumber}`;

      // JW.org follows a specific pattern where:
      // - Week 1 of a year starts on the last Monday of the previous year (if it exists in December)
      // - The last week of a year ends on the last Sunday of December
      // - Each week starts on Monday and ends on Sunday

      let startDate, endDate;

      if (year === 2025) {
        // For 2025, we know exactly how JW.org defines the weeks
        if (weekNumber === 1) {
          // Week 1 starts on Monday, December 30, 2024
          startDate = new Date(2024, 11, 30); // December 30, 2024 (Monday)
          endDate = new Date(2025, 0, 5);     // January 5, 2025 (Sunday)
        } else if (weekNumber === 52) {
          // Week 52 (last week) is December 22-28, 2025
          startDate = new Date(2025, 11, 22); // December 22, 2025 (Monday)
          endDate = new Date(2025, 11, 28);   // December 28, 2025 (Sunday)
        } else {
          // For weeks 2-51, calculate based on week 1
          // Week 2 starts on January 6, 2025
          startDate = new Date(2025, 0, 6 + (weekNumber - 2) * 7);
          endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6); // End date is 6 days after start date
        }
      } else {
        // For other years, calculate based on the last Monday of the previous December
        // Find the last Monday of December in the previous year
        const lastDayPrevYear = new Date(year - 1, 11, 31); // December 31 of previous year
        const lastDayDayOfWeek = lastDayPrevYear.getDay(); // 0 = Sunday, 1 = Monday, etc.

        // Calculate days to subtract to get to the last Monday
        // If the last day is Monday (1), subtract 0 days
        // If the last day is Tuesday (2), subtract 1 day
        // If the last day is Sunday (0), subtract 6 days, etc.
        const daysToLastMonday = lastDayDayOfWeek === 1 ? 0 : (lastDayDayOfWeek === 0 ? 6 : lastDayDayOfWeek - 1);

        // Calculate the date of the last Monday of the previous year
        const lastMondayPrevYear = new Date(lastDayPrevYear);
        lastMondayPrevYear.setDate(lastDayPrevYear.getDate() - daysToLastMonday);

        // Calculate start date (Monday) for the current week
        startDate = new Date(lastMondayPrevYear);
        startDate.setDate(lastMondayPrevYear.getDate() + (weekNumber - 1) * 7);

        // Calculate end date (Sunday) for the current week
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
      }

      // Format dates for display
      const startDay = startDate.getDate();
      const startMonth = startDate.getMonth();
      const endDay = endDate.getDate();
      const endMonth = endDate.getMonth();

      // Month names in Spanish
      const monthNames = [
        'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
        'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
      ];

      // Create title in the format "Semana del 30 de diciembre al 5 de enero"
      const title = `Semana del ${startDay} de ${monthNames[startMonth]} al ${endDay} de ${monthNames[endMonth]} de ${year}`;

      // Format dates as ISO strings (YYYY-MM-DD)
      const formattedStartDate = `${year}-${String(startMonth + 1).padStart(2, '0')}-${String(startDay).padStart(2, '0')}`;
      const formattedEndDate = `${year}-${String(endMonth + 1).padStart(2, '0')}-${String(endDay).padStart(2, '0')}`;

      // Create the URL for the week
      const wolUrl = `${BASE_URL}/${year}/${weekNumber}`;

      // Add the week to the list
      weeks.push({
        week_id: weekId,
        workbook_id: `${year}`,
        title,
        url: wolUrl,
        start_date: formattedStartDate,
        end_date: formattedEndDate,
        week_number: weekNumber
      });
    }

    console.log(`Generated ${weeks.length} weeks for ${year}`);
    return weeks;
  } catch (error) {
    console.error(`Error generating weeks for year ${year}:`, error);
    throw error;
  }
}



/**
 * Fetches meeting content for a specific week
 * @param {string} weekUrl - The URL of the week
 * @returns {Promise<Object>} Meeting content object
 */
async function fetchMeetingContent(weekUrl) {
  let browser = null;

  try {
    console.log(`Fetching meeting content from: ${weekUrl}`);

    // Check if the URL is valid
    if (!weekUrl) {
      throw new Error('Invalid week URL');
    }

    // Ensure we have a full URL
    if (!weekUrl.startsWith('http')) {
      // If it's just a path like 'r4/lp-s/2025/1', convert it to a full URL
      weekUrl = `https://wol.jw.org/es/wol/meetings/${weekUrl}`;
    }

    console.log(`Launching browser...`);
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-http2']
    });

    const page = await browser.newPage();

    // Set user agent
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    console.log(`Navigating to: ${weekUrl}`);
    const response = await page.goto(weekUrl, {
      waitUntil: 'networkidle2',
      timeout: 60000 // 60 seconds timeout
    });

    console.log(`Got response from JW.org: ${response.status()}`);

    // Get the HTML content
    const html = await page.content();
    console.log(`Response data length: ${html.length} characters`);

    // For debugging, save the HTML to a file
    const fs = require('fs');
    const path = require('path');
    const debugDir = path.join(__dirname, '../../debug');

    // Create debug directory if it doesn't exist
    if (!fs.existsSync(debugDir)) {
      fs.mkdirSync(debugDir, { recursive: true });
    }

    // Save the HTML to a file
    const debugFile = path.join(debugDir, `wol-response-${Date.now()}.html`);
    fs.writeFileSync(debugFile, html);
    console.log(`Saved HTML to ${debugFile}`);

    // Extract the meeting theme and scripture using Puppeteer
    const extractedData = await page.evaluate(() => {
      const theme = document.querySelector('.publicationMeetingTheme')?.textContent.trim() || 'Vida y Ministerio Cristianos';
      const scripture = document.querySelector('.scriptureCitation')?.textContent.trim() || '';

      return { theme, scripture };
    });

    const theme = extractedData.theme;
    const scripture = extractedData.scripture;

    // Initialize content structure
    const content = {
      treasures: [],
      ministry: [],
      living: [],
      christian_life: [], // Alias for living
      songs: [],
      theme,
      scripture
    };

    // Extract songs using Puppeteer with a URL pattern approach
    const songs = await page.evaluate(() => {
      const songs = [];

      // Function to extract song information from a link element
      function extractSongInfo(link, section, displayOrder) {
        if (!link) return null;

        // Get the text content and href
        const songText = link.textContent.trim();
        const songHref = link.getAttribute('href');

        // Extract song number using a language-agnostic approach
        // Look for patterns like "Song 10", "Canción 10", etc.
        let songNumber = null;
        const songMatch = songText.match(/\d+/);

        if (songMatch) {
          songNumber = parseInt(songMatch[0]);
        }

        // Get the base URL for constructing the full URL
        const baseUrl = window.location.origin;
        const fullUrl = new URL(songHref, baseUrl).href;

        console.log(`Found ${section} song: ${songText} (Number: ${songNumber}, URL: ${songHref})`);

        return {
          number: songNumber,
          title: songText,
          full_text: songText,
          displayOrder: displayOrder,
          section: section,
          url: songHref,
          full_url: fullUrl
        };
      }

      // Find all song links with the specific pattern
      // This works for any language since we're looking for the URL pattern
      const songLinks = Array.from(document.querySelectorAll('a[href*="/wol/pc/r"]'))
        .filter(link => {
          // Filter to only include links that contain song numbers and song keywords
          const text = link.textContent.trim();
          const songKeywords = ['Canción', 'Song', 'Cântico', 'Chant', 'Lied', 'Canto'];
          return text.match(/\d+/) !== null &&
                 songKeywords.some(keyword => text.includes(keyword));
        });

      console.log(`Found ${songLinks.length} song links`);

      // Process each song link
      if (songLinks.length > 0) {
        // Typically there are 3 songs: opening, middle, and closing
        // We'll assign them based on their position in the document

        // Sort links by their position in the document
        songLinks.sort((a, b) => {
          const posA = a.getBoundingClientRect().top;
          const posB = b.getBoundingClientRect().top;
          return posA - posB;
        });

        // Assign songs based on position
        if (songLinks.length >= 1) {
          const openingSong = extractSongInfo(songLinks[0], 'opening', 0);
          if (openingSong) {
            songs.push(openingSong);
          }
        }

        if (songLinks.length >= 2) {
          const middleSong = extractSongInfo(songLinks[1], 'middle', 1);
          if (middleSong) {
            songs.push(middleSong);
          }
        }

        if (songLinks.length >= 3) {
          const closingSong = extractSongInfo(songLinks[2], 'closing', 2);
          if (closingSong) {
            songs.push(closingSong);
          }
        }
      } else {
        // Fallback: Try to find songs using text content
        // This is a more language-dependent approach

        // Find all paragraphs that might contain songs
        const paragraphs = Array.from(document.querySelectorAll('.bodyTxt p, .bodyTxt div'));

        // Common song keywords in different languages
        const songKeywords = ['Canción', 'Song', 'Cântico', 'Chant', 'Lied', 'Canto'];

        // Function to check if text contains any song keyword
        function containsSongKeyword(text) {
          return songKeywords.some(keyword => text.includes(keyword));
        }

        // 1. Find the opening song (usually at the beginning of the meeting)
        let openingSong = null;
        for (let i = 0; i < paragraphs.length; i++) {
          const element = paragraphs[i];
          if (containsSongKeyword(element.textContent) && !openingSong) {
            // Look for links within this element
            const links = element.querySelectorAll('a');
            if (links.length > 0) {
              openingSong = extractSongInfo(links[0], 'opening', 0);
              if (openingSong) {
                songs.push(openingSong);
                break;
              }
            }
          }
        }

        // 2. Find the middle song (usually in the middle of the meeting)
        let middleSong = null;
        const middleIndex = Math.floor(paragraphs.length / 2);
        for (let i = middleIndex - 10; i < middleIndex + 10; i++) {
          if (i >= 0 && i < paragraphs.length) {
            const element = paragraphs[i];
            if (containsSongKeyword(element.textContent) && !middleSong) {
              // Look for links within this element
              const links = element.querySelectorAll('a');
              if (links.length > 0) {
                middleSong = extractSongInfo(links[0], 'middle', 1);
                if (middleSong) {
                  songs.push(middleSong);
                  break;
                }
              }
            }
          }
        }

        // 3. Find the closing song (at the end of the meeting)
        let closingSong = null;
        for (let i = paragraphs.length - 1; i >= 0; i--) {
          const element = paragraphs[i];
          if (containsSongKeyword(element.textContent) && !closingSong) {
            // Look for links within this element
            const links = element.querySelectorAll('a');
            if (links.length > 0) {
              closingSong = extractSongInfo(links[0], 'closing', 2);
              if (closingSong) {
                songs.push(closingSong);
                break;
              }
            }
          }
        }
      }

      // Ensure we have all three songs
      if (songs.filter(s => s.section === 'opening').length === 0) {
        console.log('No opening song found, using default');
        songs.push({
          number: null,
          title: 'Canción de apertura',
          full_text: 'Canción de apertura',
          displayOrder: 0,
          section: 'opening'
        });
      }

      if (songs.filter(s => s.section === 'middle').length === 0) {
        console.log('No middle song found, using default');
        songs.push({
          number: null,
          title: 'Canción intermedia',
          full_text: 'Canción intermedia',
          displayOrder: 1,
          section: 'middle'
        });
      }

      if (songs.filter(s => s.section === 'closing').length === 0) {
        console.log('No closing song found, using default');
        songs.push({
          number: null,
          title: 'Canción de cierre',
          full_text: 'Canción de cierre',
          displayOrder: 2,
          section: 'closing'
        });
      }

      // Sort songs by display order
      return songs.sort((a, b) => a.displayOrder - b.displayOrder);
    });

    // Store the songs in the content object
    content.songs = songs;
    console.log(`Found ${songs.length} songs`);

    // Extract language from the URL
    const urlLanguage = weekUrl.includes('/es/') ? 'es' :
                        weekUrl.includes('/en/') ? 'en' : 'es'; // Default to Spanish

    // Get song titles dynamically
    if (songs.length > 0) {
      console.log(`Getting song titles dynamically for language: ${urlLanguage}`);

      // Process each song
      for (let i = 0; i < songs.length; i++) {
        const song = songs[i];

        if (song.number) {
          try {
            // Construct the full URL if we have a relative URL
            let fullSongUrl = null;
            if (song.url) {
              fullSongUrl = new URL(song.url, 'https://wol.jw.org').href;
            }

            // Get the song title dynamically
            const songTitle = await getDynamicSongTitle(song.number, urlLanguage, fullSongUrl);

            console.log(`Song ${song.number} title: "${songTitle}"`);

            // Update the song title
            content.songs[i].title = songTitle;
            content.songs[i].full_title = songTitle;
          } catch (error) {
            console.error(`Error getting dynamic title for song ${song.number}:`, error.message);

            // Fallback to database
            const dbSongTitle = getSongTitle(song.number, urlLanguage);
            if (dbSongTitle) {
              console.log(`Fallback to database title for song ${song.number}: "${dbSongTitle}"`);
              content.songs[i].title = dbSongTitle;
              content.songs[i].full_title = dbSongTitle;
            } else {
              // Last resort: use the song number
              const genericTitle = `Canción ${song.number}`;
              console.log(`Using generic title: "${genericTitle}"`);
              content.songs[i].title = genericTitle;
              content.songs[i].full_title = genericTitle;
            }
          }
        }
      }
    }

    // Extract sections and parts using Puppeteer
    console.log('Extracting meeting content from HTML...');

    // Extract sections data
    const sectionsData = await page.evaluate(() => {
      // First, try to find the sections using the new format (div with dc-icon classes)
      const treasuresSection = document.querySelectorAll('.dc-icon--gem');
      const ministrySection = document.querySelectorAll('.dc-icon--wheat');
      const christianLifeSection = document.querySelectorAll('.dc-icon--sheep');

      return {
        treasuresCount: treasuresSection.length,
        ministryCount: ministrySection.length,
        christianLifeCount: christianLifeSection.length,
        hasNewFormat: treasuresSection.length > 0 && ministrySection.length > 0 && christianLifeSection.length > 0
      };
    });

    console.log(`Found sections using dc-icon classes: Treasures=${sectionsData.treasuresCount}, Ministry=${sectionsData.ministryCount}, ChristianLife=${sectionsData.christianLifeCount}`);

    let currentSection = null;
    let sortOrder = 0;

    if (sectionsData.hasNewFormat) {
      console.log('Using new format with dc-icon classes');

      // Extract all meeting parts using Puppeteer
      const meetingParts = await page.evaluate(() => {
        // Helper function to extract parts from a section
        function extractPartsFromSection(sectionClass, sectionName) {
          const parts = [];
          const sectionElement = document.querySelector(`.${sectionClass}`);

          if (!sectionElement) return parts;

          // Find all h3 elements after the section
          let currentElement = sectionElement.nextElementSibling;
          const nextSectionClass = sectionClass === 'dc-icon--gem' ? 'dc-icon--wheat' :
                                  sectionClass === 'dc-icon--wheat' ? 'dc-icon--sheep' : null;

          while (currentElement && (!nextSectionClass || !currentElement.classList.contains(nextSectionClass))) {
            if (currentElement.tagName === 'H3' || currentElement.querySelector('h3')) {
              const h3Element = currentElement.tagName === 'H3' ? currentElement : currentElement.querySelector('h3');
              const partTitle = h3Element.textContent.trim();

              if (partTitle) {
                // Try to extract duration from the title
                let duration = 0;
                const durationMatch = partTitle.match(/\((\d+)\s*min/);
                if (durationMatch) {
                  duration = parseInt(durationMatch[1]);
                }

                // Extract JW.org part number if available
                let jwNumber = null;
                let cleanTitle = partTitle;
                const jwNumberMatch = partTitle.match(/^(\d+)\.\s+(.+)$/);
                if (jwNumberMatch) {
                  jwNumber = parseInt(jwNumberMatch[1]);
                  cleanTitle = jwNumberMatch[2];
                }

                // Special handling for Bible Reading
                const isBibleReading = partTitle.toLowerCase().includes('lectura de la biblia');

                // Look for timing element in the parent or nearby elements
                let timingElement = currentElement.querySelector('.timing');
                if (!timingElement && currentElement.nextElementSibling) {
                  timingElement = currentElement.nextElementSibling.querySelector('.timing');
                }

                // Extract duration from timing element if available and not already found
                if (timingElement && !duration) {
                  const timingMatch = timingElement.textContent.trim().match(/(\d+)/);
                  if (timingMatch) {
                    duration = parseInt(timingMatch[1]);
                  }
                }

                parts.push({
                  title: cleanTitle,
                  description: '',
                  duration: isBibleReading ? (duration || 4) : (duration || 10),
                  part_type: isBibleReading ? 'bible_reading' : sectionName,
                  section: sectionName,
                  isBibleReading,
                  jw_number: jwNumber
                });
              }
            }

            currentElement = currentElement.nextElementSibling;
          }

          return parts;
        }

        // Extract parts from each section
        const treasuresParts = extractPartsFromSection('dc-icon--gem', 'treasures');
        const ministryParts = extractPartsFromSection('dc-icon--wheat', 'ministry');
        const christianLifeParts = extractPartsFromSection('dc-icon--sheep', 'christian_life');

        return {
          treasuresParts,
          ministryParts,
          christianLifeParts
        };
      });

      console.log(`Found ${meetingParts.treasuresParts.length} parts in Treasures section`);

      // Process Treasures section
      currentSection = 'treasures';
      sortOrder = 0;

      // Add the section header as a part
      content.treasures.push({
        title: 'Tesoros de la Biblia',
        duration: 10,
        part_type: 'treasures',
        section: 'treasures',
        sort_order: sortOrder++,
        is_section_header: true
      });

      // Process each part in Treasures section
      meetingParts.treasuresParts.forEach(part => {
        console.log(`Processing Treasures part: ${part.title}`);

        // Special handling for Bible Reading
        if (part.isBibleReading) {
          content.treasures.push({
            title: part.title,
            description: part.description,
            duration: part.duration,
            part_type: 'bible_reading',
            section: 'treasures',
            sort_order: sortOrder++,
            jw_number: part.jw_number
          });

          // Also add as bible_reading for backward compatibility
          content.bible_reading = {
            title: part.title,
            description: part.description,
            duration: part.duration,
            part_type: 'bible_reading',
            section: 'treasures',
            sort_order: 1,
            jw_number: part.jw_number
          };
        } else {
          content.treasures.push({
            title: part.title,
            description: part.description,
            duration: part.duration,
            part_type: 'treasures',
            section: 'treasures',
            sort_order: sortOrder++,
            jw_number: part.jw_number
          });
        }
      });

      // Process Ministry section
      currentSection = 'ministry';
      sortOrder = 0;

      // Add the section header as a part
      content.ministry.push({
        title: 'Seamos Mejores Maestros',
        duration: 15,
        part_type: 'ministry',
        section: 'ministry',
        sort_order: sortOrder++,
        is_section_header: true
      });

      console.log(`Found ${meetingParts.ministryParts.length} parts in Ministry section`);

      // Process each part in Ministry section
      meetingParts.ministryParts.forEach(part => {
        console.log(`Processing Ministry part: ${part.title}`);

        // Determine if this part needs a student
        const needsStudent = part.title.toLowerCase().includes('conversación') ||
                            part.title.toLowerCase().includes('revisita') ||
                            part.title.toLowerCase().includes('curso bíblico') ||
                            part.title.toLowerCase().includes('discípulos');

        content.ministry.push({
          title: part.title,
          description: part.description,
          duration: part.duration || 5,
          part_type: 'ministry',
          section: 'ministry',
          sort_order: sortOrder++,
          needs_student: needsStudent,
          jw_number: part.jw_number
        });
      });

      // Process Christian Life section
      currentSection = 'christian_life';
      sortOrder = 0;

      // Add the section header as a part
      content.christian_life.push({
        title: 'Nuestra Vida Cristiana',
        duration: 15,
        part_type: 'living',
        section: 'christian_life',
        sort_order: sortOrder++,
        is_section_header: true
      });

      // Also add to living for backward compatibility
      content.living.push({
        title: 'Nuestra Vida Cristiana',
        duration: 15,
        part_type: 'living',
        section: 'living',
        sort_order: 0
      });

      console.log(`Found ${meetingParts.christianLifeParts.length} parts in Christian Life section`);

      // Process each part in Christian Life section
      meetingParts.christianLifeParts.forEach(part => {
        console.log(`Processing Christian Life part: ${part.title}`);

        const christianLifePart = {
          title: part.title,
          description: part.description,
          duration: part.duration || 10,
          part_type: 'living',
          section: 'christian_life',
          sort_order: sortOrder++,
          jw_number: part.jw_number
        };

        content.christian_life.push(christianLifePart);

        // Special handling for Congregation Bible Study
        if (part.title.toLowerCase().includes('estudio bíblico de la congregación')) {
          content.living.push({
            title: part.title,
            description: part.description,
            duration: part.duration || 30,
            part_type: 'living',
            section: 'living',
            sort_order: 1,
            jw_number: part.jw_number
          });
        }
      });
    } else {
      console.log('Using old format with sectionHeader class');

      // Extract sections using the old format with Puppeteer
      const oldFormatSections = await page.evaluate(() => {
        const sections = [];
        const sectionHeaders = document.querySelectorAll('.sectionHeader');

        sectionHeaders.forEach(header => {
          const sectionTitle = header.textContent.trim();
          sections.push({
            title: sectionTitle,
            element: header
          });
        });

        return sections;
      });

      // Process each section
      for (const section of oldFormatSections) {
        const sectionTitle = section.title;
        console.log(`Found section: ${sectionTitle}`);

        // Determine which section we're in
        if (sectionTitle.includes('TESOROS')) {
          currentSection = 'treasures';
          sortOrder = 0;

          // Add the section header as a part
          content.treasures.push({
            title: 'Tesoros de la Biblia',
            duration: 10,
            part_type: 'treasures',
            section: 'treasures',
            sort_order: sortOrder++,
            is_section_header: true
          });
        } else if (sectionTitle.includes('SEAMOS MEJORES MAESTROS')) {
          currentSection = 'ministry';
          sortOrder = 0;

          // Add the section header as a part
          content.ministry.push({
            title: 'Seamos Mejores Maestros',
            duration: 15,
            part_type: 'ministry',
            section: 'ministry',
            sort_order: sortOrder++,
            is_section_header: true
          });
        } else if (sectionTitle.includes('NUESTRA VIDA CRISTIANA')) {
          currentSection = 'christian_life';
          sortOrder = 0;

          // Add the section header as a part
          content.christian_life.push({
            title: 'Nuestra Vida Cristiana',
            duration: 15,
            part_type: 'living',
            section: 'christian_life',
            sort_order: sortOrder++,
            is_section_header: true
          });

          // Also add to living for backward compatibility
          content.living.push({
            title: 'Nuestra Vida Cristiana',
            duration: 15,
            part_type: 'living',
            section: 'living',
            sort_order: 0
          });
        }

        // Process parts within the current section
        if (currentSection) {
          console.log(`Processing parts for section: ${currentSection}`);

          // Extract parts for this section using Puppeteer
          const sectionParts = await page.evaluate((sectionTitle) => {
            // Find the section container
            const sectionHeader = Array.from(document.querySelectorAll('.sectionHeader'))
              .find(header => header.textContent.trim().includes(sectionTitle));

            if (!sectionHeader) return [];

            const sectionContainer = sectionHeader.closest('.bodyTxt');
            if (!sectionContainer) return [];

            // Find all parts within this section
            const partElements = Array.from(sectionContainer.querySelectorAll('.title, .subTitle'))
              .filter(el => !el.classList.contains('sectionHeader'));

            // Process each part
            return partElements.map(partEl => {
              const partTitle = partEl.textContent.trim();

              // Skip if empty
              if (!partTitle) return null;

              let partDescription = '';

              // Look for description in the next paragraph
              const nextPara = partEl.nextElementSibling;
              if (nextPara && nextPara.tagName === 'P') {
                partDescription = nextPara.textContent.trim();
              }

              // Try to extract duration from the title
              let duration = 0;
              const durationMatch = partTitle.match(/\((\d+)\s*min/);
              if (durationMatch) {
                duration = parseInt(durationMatch[1]);
              }

              // Extract JW.org part number if available
              let jwNumber = null;
              let cleanTitle = partTitle;
              const jwNumberMatch = partTitle.match(/^(\d+)\.\s+(.+)$/);
              if (jwNumberMatch) {
                jwNumber = parseInt(jwNumberMatch[1]);
                cleanTitle = jwNumberMatch[2];
              }

              return {
                title: cleanTitle,
                description: partDescription,
                duration,
                isBibleReading: partTitle.toLowerCase().includes('lectura de la biblia'),
                isCongregationStudy: partTitle.toLowerCase().includes('estudio bíblico de la congregación'),
                needsStudent: partTitle.toLowerCase().includes('conversación') ||
                             partTitle.toLowerCase().includes('revisita') ||
                             partTitle.toLowerCase().includes('curso bíblico') ||
                             partTitle.toLowerCase().includes('discurso'),
                jw_number: jwNumber
              };
            }).filter(part => part !== null);
          }, sectionTitle);

          console.log(`Found ${sectionParts.length} potential parts in ${currentSection} section`);

          // Process each part
          sectionParts.forEach(part => {
            console.log(`Processing part: ${part.title}`);

            // Add the part to the appropriate section
            if (currentSection === 'treasures') {
              // Special handling for Bible Reading
              if (part.isBibleReading) {
                content.treasures.push({
                  title: part.title,
                  description: part.description,
                  duration: part.duration || 4,
                  part_type: 'bible_reading',
                  section: 'treasures',
                  sort_order: sortOrder++,
                  jw_number: part.jw_number
                });

                // Also add as bible_reading for backward compatibility
                content.bible_reading = {
                  title: part.title,
                  description: part.description,
                  duration: part.duration || 4,
                  part_type: 'bible_reading',
                  section: 'treasures',
                  sort_order: 1,
                  jw_number: part.jw_number
                };
              } else {
                content.treasures.push({
                  title: part.title,
                  description: part.description,
                  duration: part.duration || 10,
                  part_type: 'treasures',
                  section: 'treasures',
                  sort_order: sortOrder++,
                  jw_number: part.jw_number
                });
              }
            } else if (currentSection === 'ministry') {
              content.ministry.push({
                title: part.title,
                description: part.description,
                duration: part.duration || 5,
                part_type: 'ministry',
                section: 'ministry',
                sort_order: sortOrder++,
                needs_student: part.needsStudent,
                jw_number: part.jw_number
              });
            } else if (currentSection === 'christian_life') {
              const christianLifePart = {
                title: part.title,
                description: part.description,
                duration: part.duration || 10,
                part_type: 'living',
                section: 'christian_life',
                sort_order: sortOrder++,
                jw_number: part.jw_number
              };

              content.christian_life.push(christianLifePart);

              // Special handling for Congregation Bible Study
              if (part.isCongregationStudy) {
                content.living.push({
                  title: part.title,
                  description: part.description,
                  duration: part.duration || 30,
                  part_type: 'living',
                  section: 'living',
                  sort_order: 1,
                  jw_number: part.jw_number
                });
              }
            }
          });
        }
      }

    // Log the extracted content for debugging
    console.log(`Extracted ${content.treasures.length} treasures parts`);
    console.log(`Extracted ${content.ministry.length} ministry parts`);
    console.log(`Extracted ${content.christian_life.length} christian life parts`);
    console.log(`Extracted ${content.songs.length} songs`);

    // If we couldn't extract parts, use default structure
    if (content.treasures.length === 0) {
      content.treasures.push({
        title: 'Tesoros de la Biblia',
        duration: 10,
        part_type: 'treasures',
        section: 'treasures',
        sort_order: 0,
        is_section_header: true
      });

      content.treasures.push({
        title: 'Lectura de la Biblia',
        duration: 4,
        part_type: 'bible_reading',
        section: 'treasures',
        sort_order: 1
      });

      content.bible_reading = {
        title: 'Lectura de la Biblia',
        duration: 4,
        part_type: 'bible_reading',
        section: 'treasures',
        sort_order: 1
      };
    }

    if (content.ministry.length === 0) {
      content.ministry.push({
        title: 'Primera conversación',
        duration: 2,
        part_type: 'ministry',
        section: 'ministry',
        sort_order: 0,
        needs_student: true
      });

      content.ministry.push({
        title: 'Revisita',
        duration: 4,
        part_type: 'ministry',
        section: 'ministry',
        sort_order: 1,
        needs_student: true
      });

      content.ministry.push({
        title: 'Curso bíblico',
        duration: 6,
        part_type: 'ministry',
        section: 'ministry',
        sort_order: 2,
        needs_student: true
      });
    }

    if (content.christian_life.length === 0) {
      content.christian_life.push({
        title: 'Nuestra Vida Cristiana',
        duration: 15,
        part_type: 'living',
        section: 'christian_life',
        sort_order: 0,
        is_section_header: true
      });

      content.christian_life.push({
        title: 'Estudio bíblico de la congregación',
        duration: 30,
        part_type: 'living',
        section: 'christian_life',
        sort_order: 1
      });

      content.living.push({
        title: 'Nuestra Vida Cristiana',
        duration: 15,
        part_type: 'living',
        section: 'living',
        sort_order: 0
      });

      content.living.push({
        title: 'Estudio bíblico de la congregación',
        duration: 30,
        part_type: 'living',
        section: 'living',
        sort_order: 1
      });
    }

    if (content.songs.length === 0) {
      content.songs.push({
        number: 1,
        title: 'Canción 1',
        displayOrder: 0
      });

      content.songs.push({
        number: 2,
        title: 'Canción 2',
        displayOrder: 1
      });

      content.songs.push({
        number: 3,
        title: 'Canción 3',
        displayOrder: 2
      });
    }
    }

    // Close the browser
    await browser.close();
    console.log('Browser closed');

    return {
      content,
      html
    };
  } catch (error) {
    console.error(`Error fetching meeting content for ${weekUrl}:`, error);

    // Close the browser if it's open
    if (browser) {
      try {
        await browser.close();
        console.log('Browser closed after error');
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
      }
    }

    // If the week doesn't exist yet, return a default structure
    if (error.message && error.message.includes('404')) {
      console.log(`Week not found at ${weekUrl}, using default structure`);

      // Use default data structure
      const defaultContent = {
        treasures: [
          {
            title: 'Tesoros de la Biblia',
            duration: 10,
            part_type: 'treasures',
            section: 'treasures',
            sort_order: 0
          },
          {
            title: 'Lectura de la Biblia',
            duration: 4,
            part_type: 'bible_reading',
            section: 'treasures',
            sort_order: 1
          }
        ],
        ministry: [
          {
            title: 'Primera conversación',
            duration: 2,
            part_type: 'ministry',
            section: 'ministry',
            sort_order: 0,
            needs_student: true
          },
          {
            title: 'Revisita',
            duration: 4,
            part_type: 'ministry',
            section: 'ministry',
            sort_order: 1,
            needs_student: true
          },
          {
            title: 'Curso bíblico',
            duration: 6,
            part_type: 'ministry',
            section: 'ministry',
            sort_order: 2,
            needs_student: true
          }
        ],
        living: [
          {
            title: 'Nuestra Vida Cristiana',
            duration: 15,
            part_type: 'living',
            section: 'living',
            sort_order: 0
          },
          {
            title: 'Estudio bíblico de la congregación',
            duration: 30,
            part_type: 'living',
            section: 'living',
            sort_order: 1
          }
        ],
        christian_life: [
          {
            title: 'Nuestra Vida Cristiana',
            duration: 15,
            part_type: 'living',
            section: 'christian_life',
            sort_order: 0
          },
          {
            title: 'Estudio bíblico de la congregación',
            duration: 30,
            part_type: 'living',
            section: 'christian_life',
            sort_order: 1
          }
        ],
        bible_reading: {
          title: 'Lectura de la Biblia',
          duration: 4,
          part_type: 'bible_reading',
          section: 'treasures',
          sort_order: 1
        },
        songs: [
          {
            number: 1,
            title: 'Canción 1',
            displayOrder: 0
          },
          {
            number: 2,
            title: 'Canción 2',
            displayOrder: 1
          },
          {
            number: 3,
            title: 'Canción 3',
            displayOrder: 2
          }
        ],
        theme: 'Vida y Ministerio Cristianos',
        scripture: 'Mateo 24:14'
      };

      return {
        content: defaultContent,
        html: '<html><body>Default content for unavailable week</body></html>'
      };
    }

    throw error;
  }
}

/**
 * Fetches midweek meeting data for a specific year and week
 * @param {number} year - The year to fetch meeting data for
 * @param {number} weekNumber - The week number to fetch meeting data for
 * @returns {Promise<Object>} Meeting data object
 */
async function fetchMidweekMeetingData(year, weekNumber) {
  try {
    console.log(`Fetching midweek meeting data for ${year}, week ${weekNumber}...`);

    // Get the week data
    const weeks = await fetchWeeks(year);
    const weekData = weeks.find(w => w.week_number === weekNumber);

    if (!weekData) {
      throw new Error(`Week ${weekNumber} of ${year} not found`);
    }

    // Check if the week is available (only weeks 1-35 for 2025 are currently available)
    const currentYear = new Date().getFullYear();
    const maxAvailableWeek = (year === 2025) ? 35 : 52; // For 2025, only weeks 1-35 are available

    if (year > currentYear + 1 || (year === 2025 && weekNumber > maxAvailableWeek)) {
      console.log(`Week ${weekNumber} of ${year} is not yet available, using default structure`);

      // Return default structure for future weeks
      return {
        date: weekData.title,
        theme: 'Vida y Ministerio Cristianos',
        scripture: 'Mateo 24:14',
        sections: {
          treasures: [
            {
              title: 'Tesoros de la Biblia',
              duration: 10,
              part_type: 'treasures',
              section: 'treasures',
              sort_order: 0
            },
            {
              title: 'Lectura de la Biblia',
              duration: 4,
              part_type: 'bible_reading',
              section: 'treasures',
              sort_order: 1
            }
          ],
          ministry: [
            {
              title: 'Primera conversación',
              duration: 2,
              part_type: 'ministry',
              section: 'ministry',
              sort_order: 0,
              needs_student: true
            },
            {
              title: 'Revisita',
              duration: 4,
              part_type: 'ministry',
              section: 'ministry',
              sort_order: 1,
              needs_student: true
            },
            {
              title: 'Curso bíblico',
              duration: 6,
              part_type: 'ministry',
              section: 'ministry',
              sort_order: 2,
              needs_student: true
            }
          ],
          christian_life: [
            {
              title: 'Nuestra Vida Cristiana',
              duration: 15,
              part_type: 'living',
              section: 'christian_life',
              sort_order: 0
            },
            {
              title: 'Estudio bíblico de la congregación',
              duration: 30,
              part_type: 'living',
              section: 'christian_life',
              sort_order: 1
            }
          ],
          songs: [
            {
              number: 1,
              title: 'Canción 1',
              displayOrder: 0
            },
            {
              number: 2,
              title: 'Canción 2',
              displayOrder: 1
            },
            {
              number: 3,
              title: 'Canción 3',
              displayOrder: 2
            }
          ]
        }
      };
    }

    // Fetch the actual meeting content from JW.org
    const meetingContent = await fetchMeetingContent(weekData.url);

    // Format the meeting data
    const meetingData = {
      date: weekData.title,
      theme: meetingContent.content.theme || 'Vida y Ministerio Cristianos',
      scripture: meetingContent.content.scripture || '',
      sections: {
        treasures: meetingContent.content.treasures || [],
        ministry: meetingContent.content.ministry || [],
        christian_life: meetingContent.content.christian_life || [],
        songs: meetingContent.content.songs || []
      }
    };

    return meetingData;
  } catch (error) {
    console.error(`Error fetching midweek meeting data for ${year}, week ${weekNumber}:`, error);

    // If there's an error, return a default structure
    return {
      date: `Semana ${weekNumber} de ${year}`,
      theme: 'Vida y Ministerio Cristianos',
      scripture: 'Mateo 24:14',
      sections: {
        treasures: [
          {
            title: 'Tesoros de la Biblia',
            duration: 10,
            part_type: 'treasures',
            section: 'treasures',
            sort_order: 0
          },
          {
            title: 'Lectura de la Biblia',
            duration: 4,
            part_type: 'bible_reading',
            section: 'treasures',
            sort_order: 1
          }
        ],
        ministry: [
          {
            title: 'Primera conversación',
            duration: 2,
            part_type: 'ministry',
            section: 'ministry',
            sort_order: 0,
            needs_student: true
          },
          {
            title: 'Revisita',
            duration: 4,
            part_type: 'ministry',
            section: 'ministry',
            sort_order: 1,
            needs_student: true
          },
          {
            title: 'Curso bíblico',
            duration: 6,
            part_type: 'ministry',
            section: 'ministry',
            sort_order: 2,
            needs_student: true
          }
        ],
        christian_life: [
          {
            title: 'Nuestra Vida Cristiana',
            duration: 15,
            part_type: 'living',
            section: 'christian_life',
            sort_order: 0
          },
          {
            title: 'Estudio bíblico de la congregación',
            duration: 30,
            part_type: 'living',
            section: 'christian_life',
            sort_order: 1
          }
        ],
        songs: [
          {
            number: 1,
            title: 'Canción 1',
            displayOrder: 0
          },
          {
            number: 2,
            title: 'Canción 2',
            displayOrder: 1
          },
          {
            number: 3,
            title: 'Canción 3',
            displayOrder: 2
          }
        ]
      }
    };
  }
}

module.exports = {
  fetchWorkbooks,
  fetchWeeks,
  fetchMeetingContent,
  fetchMidweekMeetingData
};
