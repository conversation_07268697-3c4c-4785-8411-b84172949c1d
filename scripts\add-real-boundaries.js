#!/usr/bin/env node

/**
 * Add Real Territory Boundaries Script
 *
 * This script helps administrators add real boundary data to territories.
 * It provides examples and templates for adding actual geographic boundaries
 * instead of using mock/generated boundaries.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Example: Add a real boundary to a territory
 * This shows how to add actual boundary coordinates for a territory
 */
async function addRealBoundaryExample() {
  try {
    console.log('📍 Adding real boundary example for Territory 001...\n');

    // Example real boundary coordinates for Territory 001
    // These should be replaced with actual surveyed boundary data
    const realBoundary = {
      type: 'Polygon',
      coordinates: [[
        // Example coordinates - replace with real boundary data
        [-80.2725, 25.7630], // Northwest corner
        [-80.2705, 25.7630], // Northeast corner
        [-80.2705, 25.7610], // Southeast corner
        [-80.2725, 25.7610], // Southwest corner
        [-80.2725, 25.7630]  // Close polygon
      ]]
    };

    // Find Territory 001
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      }
    });

    if (!territory) {
      console.log('❌ Territory 001 not found');
      return;
    }

    // Update with real boundary data
    await prisma.territory.update({
      where: { id: territory.id },
      data: {
        boundaries: realBoundary
      }
    });

    console.log('✅ Added real boundary to Territory 001');
    console.log('📍 Boundary coordinates:', realBoundary.coordinates[0].length, 'points');

  } catch (error) {
    console.error('❌ Error adding real boundary:', error);
  }
}

/**
 * Remove all mock boundaries from territories
 * This clears any algorithmically generated boundaries
 */
async function removeMockBoundaries() {
  try {
    console.log('🧹 Removing all mock boundaries...\n');

    // Get all territories with boundaries
    const territories = await prisma.territory.findMany({
      where: {
        congregationId: '1441',
        boundaries: { not: null }
      },
      select: {
        id: true,
        territoryNumber: true,
        boundaries: true
      }
    });

    console.log(`📊 Found ${territories.length} territories with boundary data`);

    let removedCount = 0;
    for (const territory of territories) {
      // Check if this looks like a mock boundary (perfect rectangle)
      const boundary = territory.boundaries;
      if (boundary && boundary.coordinates && boundary.coordinates[0]) {
        const coords = boundary.coordinates[0];

        // Simple heuristic: if it's a perfect rectangle with 5 points, it's likely mock
        if (coords.length === 5) {
          const isRectangle = (
            coords[0][0] === coords[3][0] && // Same longitude for first and fourth point
            coords[1][0] === coords[2][0] && // Same longitude for second and third point
            coords[0][1] === coords[1][1] && // Same latitude for first and second point
            coords[2][1] === coords[3][1]    // Same latitude for third and fourth point
          );

          if (isRectangle) {
            await prisma.territory.update({
              where: { id: territory.id },
              data: { boundaries: null }
            });
            console.log(`  🗑️  Removed mock boundary from Territory ${territory.territoryNumber}`);
            removedCount++;
          }
        }
      }
    }

    console.log(`\n✅ Removed ${removedCount} mock boundaries`);
    console.log(`📊 ${territories.length - removedCount} territories kept their boundaries (likely real data)`);

  } catch (error) {
    console.error('❌ Error removing mock boundaries:', error);
  }
}

/**
 * Show territories without boundary data
 */
async function showTerritoriesWithoutBoundaries() {
  try {
    console.log('📋 Territories without boundary data:\n');

    const territories = await prisma.territory.findMany({
      where: {
        congregationId: '1441',
        boundaries: { equals: null }
      },
      select: {
        territoryNumber: true,
        address: true
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });

    console.log(`📊 ${territories.length} territories need boundary data:\n`);

    territories.forEach(territory => {
      const firstAddress = territory.address.split('\n')[0] || 'No address';
      console.log(`  Territory ${territory.territoryNumber}: ${firstAddress}`);
    });

    console.log('\n💡 To add real boundaries:');
    console.log('   1. Obtain actual boundary coordinates from surveying or GIS data');
    console.log('   2. Use this script to add them to the database');
    console.log('   3. Boundaries should be in GeoJSON Polygon format with [longitude, latitude] coordinates');

  } catch (error) {
    console.error('❌ Error listing territories:', error);
  }
}

/**
 * Template for adding a real boundary
 */
function showBoundaryTemplate() {
  console.log('📝 Template for adding real boundary data:\n');

  const template = `
// Example: Add real boundary to Territory XXX
const realBoundary = {
  type: 'Polygon',
  coordinates: [[
    [-80.2725, 25.7630], // Point 1: [longitude, latitude]
    [-80.2705, 25.7630], // Point 2: [longitude, latitude]
    [-80.2705, 25.7610], // Point 3: [longitude, latitude]
    [-80.2725, 25.7610], // Point 4: [longitude, latitude]
    [-80.2725, 25.7630]  // Close polygon (same as first point)
  ]]
};

await prisma.territory.update({
  where: {
    congregationId: '1441',
    territoryNumber: 'XXX'
  },
  data: { boundaries: realBoundary }
});
`;

  console.log(template);
  console.log('📍 Important notes:');
  console.log('   - Coordinates must be in [longitude, latitude] format');
  console.log('   - First and last coordinates must be identical to close the polygon');
  console.log('   - Use actual surveyed boundary data, not estimated coordinates');
  console.log('   - Boundaries should follow actual street patterns and property lines');
}

/**
 * Main function
 */
async function main() {
  const command = process.argv[2];

  switch (command) {
    case 'example':
      await addRealBoundaryExample();
      break;
    case 'remove-mock':
      await removeMockBoundaries();
      break;
    case 'list':
      await showTerritoriesWithoutBoundaries();
      break;
    case 'template':
      showBoundaryTemplate();
      break;
    default:
      console.log('🗺️  Real Territory Boundaries Management\n');
      console.log('Usage: node scripts/add-real-boundaries.js <command>\n');
      console.log('Commands:');
      console.log('  example      - Add example real boundary to Territory 001');
      console.log('  remove-mock  - Remove algorithmically generated mock boundaries');
      console.log('  list         - Show territories without boundary data');
      console.log('  template     - Show template for adding real boundaries');
      console.log('\n💡 This script helps replace mock boundaries with real geographic data');
  }

  await prisma.$disconnect();
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  addRealBoundaryExample,
  removeMockBoundaries,
  showTerritoriesWithoutBoundaries,
  showBoundaryTemplate
};
