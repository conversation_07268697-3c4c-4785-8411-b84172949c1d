#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function fixPins() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 Fixing congregation PINs...');
    
    // Fix congregation 1441
    const hashedPin1930 = await bcrypt.hash('1930', 12);
    await prisma.congregation.update({
      where: { id: '1441' },
      data: { pin: hashedPin1930 }
    });
    console.log('✅ Fixed PIN for congregation 1441 (PIN: 1930)');
    
    // Also create a developer PIN option
    const hashedPin5555 = await bcrypt.hash('5555', 12);
    await prisma.congregation.upsert({
      where: { id: 'DEV' },
      update: { pin: hashedPin5555 },
      create: {
        id: 'DEV',
        name: 'Developer Congregation',
        region: 'Development',
        pin: hashedPin5555,
        language: 'es',
        timezone: 'America/Mexico_City',
        isActive: true
      }
    });
    console.log('✅ Created/Updated DEV congregation (PIN: 5555)');
    
    // Test the fixes
    console.log('\n🧪 Testing fixes...');
    
    const cong1441 = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });
    
    if (cong1441) {
      const isValid1930 = await bcrypt.compare('1930', cong1441.pin);
      console.log(`PIN '1930' for 1441: ${isValid1930 ? '✅ VALID' : '❌ INVALID'}`);
    }
    
    const congDev = await prisma.congregation.findUnique({
      where: { id: 'DEV' }
    });
    
    if (congDev) {
      const isValid5555 = await bcrypt.compare('5555', congDev.pin);
      console.log(`PIN '5555' for DEV: ${isValid5555 ? '✅ VALID' : '❌ INVALID'}`);
    }
    
    console.log('\n📋 Available congregations:');
    console.log('- ID: 1441, PIN: 1930 (Coral Oeste)');
    console.log('- ID: CORALOES, PIN: coralpin123 (Coral Oeste)');
    console.log('- ID: DEV, PIN: 5555 (Developer)');
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

fixPins();
