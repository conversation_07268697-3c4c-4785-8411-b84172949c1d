const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function fixAuthenticationIssues() {
  try {
    console.log('🔧 Fixing authentication issues...\n');

    // 1. Check congregation PIN
    console.log('1️⃣ Checking congregation PIN...');
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' },
      select: { id: true, name: true, pin: true }
    });

    if (congregation) {
      console.log(`✅ Congregation found: ${congregation.name}`);
      
      // Test if congregation PIN matches 1930
      const congregationPinMatches = await bcrypt.compare('1930', congregation.pin);
      console.log(`🔐 Congregation PIN (1930) matches: ${congregationPinMatches}`);
      
      if (!congregationPinMatches) {
        console.log('🔄 Updating congregation PIN to 1930...');
        const hashedCongregationPin = await bcrypt.hash('1930', 10);
        await prisma.congregation.update({
          where: { id: '1441' },
          data: { pin: hashedCongregationPin }
        });
        console.log('✅ Congregation PIN updated to 1930');
      }
    }

    // 2. Check and fix coordinator user PIN
    console.log('\n2️⃣ Checking coordinator user PIN...');
    const coordinator = await prisma.member.findFirst({
      where: {
        congregationId: '1441',
        role: 'coordinator',
        isActive: true
      },
      select: { id: true, name: true, email: true, role: true, pin: true }
    });

    if (coordinator) {
      console.log(`✅ Coordinator found: ${coordinator.name}`);
      
      // Test if coordinator PIN matches 5488
      const coordinatorPinMatches = await bcrypt.compare('5488', coordinator.pin);
      console.log(`🔐 Coordinator PIN (5488) matches: ${coordinatorPinMatches}`);
      
      if (!coordinatorPinMatches) {
        console.log('🔄 Updating coordinator PIN to 5488...');
        const hashedCoordinatorPin = await bcrypt.hash('5488', 10);
        await prisma.member.update({
          where: { id: coordinator.id },
          data: { pin: hashedCoordinatorPin }
        });
        console.log('✅ Coordinator PIN updated to 5488');
      }
    } else {
      console.log('❌ No coordinator user found');
    }

    // 3. Check admin access permissions
    console.log('\n3️⃣ Checking admin access for different roles...');
    
    const roleChecks = [
      { role: 'coordinator', shouldHaveAdmin: true },
      { role: 'elder', shouldHaveAdmin: true },
      { role: 'ministerial_servant', shouldHaveAdmin: true },
      { role: 'developer', shouldHaveAdmin: true },
      { role: 'publisher', shouldHaveAdmin: false }
    ];

    for (const check of roleChecks) {
      const users = await prisma.member.findMany({
        where: {
          congregationId: '1441',
          role: check.role,
          isActive: true
        },
        select: { name: true, role: true },
        take: 2
      });

      console.log(`👥 ${check.role}: ${users.length} user(s) - Should have admin: ${check.shouldHaveAdmin}`);
      users.forEach(user => console.log(`   - ${user.name}`));
    }

    // 4. Test login flow
    console.log('\n4️⃣ Testing login credentials...');
    console.log('🏛️ Congregation Login:');
    console.log('   - Congregation ID: 1441');
    console.log('   - Congregation PIN: 1930');
    
    console.log('\n👤 Coordinator Member Login:');
    console.log('   - After congregation login, use PIN: 5488');
    console.log('   - User: Carlos Coordinador');
    console.log('   - Should see Admin button: YES');

    // 5. Check elder permissions table
    console.log('\n5️⃣ Checking elder permissions...');
    try {
      const elderPermissions = await prisma.elderPermission.findMany({
        where: { congregationId: '1441' },
        include: {
          member: {
            select: { name: true, role: true }
          }
        }
      });

      console.log(`📋 Elder permissions found: ${elderPermissions.length}`);
      elderPermissions.forEach(perm => {
        console.log(`   - ${perm.member.name} (${perm.member.role}): ${perm.section}`);
      });
    } catch (error) {
      console.log('❌ Elder permissions table not accessible or empty');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixAuthenticationIssues();
