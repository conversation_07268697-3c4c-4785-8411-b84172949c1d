#!/usr/bin/env node

/**
 * Migration script to migrate from old MySQL database structure to new Prisma schema
 * This script handles the authentication and member data migration
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Role mapping from old role_id to new role strings
const ROLE_MAPPING = {
  1: 'elder',                    // Elder
  2: 'ministerial_servant',     // Ministerial Servant
  3: 'ministerial_servant',     // Also Ministerial Servant
  4: 'publisher',               // Publisher
  5: 'developer',               // Developer/Admin
};

async function migrateFromOldDatabase() {
  console.log('🚀 Starting migration from old database structure...');

  try {
    // Step 1: Check current congregations table structure
    console.log('\n1️⃣ Checking congregations table...');

    const existingCongregations = await prisma.congregation.findMany();
    console.log(`Found ${existingCongregations.length} existing congregations`);

    // Step 2: Migrate congregation 1441 if it doesn't exist in the new format
    console.log('\n2️⃣ Migrating congregation 1441...');

    let congregation1441 = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (!congregation1441) {
      console.log('Creating congregation 1441 with proper format...');

      // Hash the PIN
      const hashedPin = await bcrypt.hash('1930', 12);

      congregation1441 = await prisma.congregation.create({
        data: {
          id: '1441',
          name: 'Coral Oeste',
          region: 'North America',
          language: 'es',
          timezone: 'America/New_York',
          pin: hashedPin,
          isActive: true,
        }
      });

      console.log('✅ Congregation 1441 created successfully');
    } else {
      console.log('✅ Congregation 1441 already exists');

      // Check if PIN needs to be hashed
      if (!congregation1441.pin.startsWith('$2')) {
        console.log('Updating PIN to hashed format...');
        const hashedPin = await bcrypt.hash('1930', 12);

        await prisma.congregation.update({
          where: { id: '1441' },
          data: { pin: hashedPin }
        });

        console.log('✅ PIN updated to hashed format');
      }
    }

    // Step 3: Check if we need to migrate members
    console.log('\n3️⃣ Checking members migration...');

    const existingMembers = await prisma.member.findMany({
      where: { congregationId: '1441' }
    });

    console.log(`Found ${existingMembers.length} existing members for congregation 1441`);

    if (existingMembers.length === 0) {
      console.log('No members found, creating default members...');

      // Create some default members based on the old database
      const defaultMembers = [
        {
          name: 'Richard Rubi',
          role: 'elder',
          pin: '5488',
          email: '<EMAIL>',
        },
        {
          name: 'Horacio Cerda',
          role: 'elder',
          pin: '5447',
          email: '<EMAIL>',
        },
        {
          name: 'Yoan Valiente',
          role: 'ministerial_servant',
          pin: '6574',
          email: '<EMAIL>',
        },
        {
          name: 'Yoan Developer',
          role: 'developer',
          pin: '5555',
          email: '<EMAIL>',
        },
        {
          name: 'James Rubi',
          role: 'elder',
          pin: '4445',
          email: '<EMAIL>',
        },
      ];

      for (const memberData of defaultMembers) {
        const hashedPin = await bcrypt.hash(memberData.pin, 12);

        const member = await prisma.member.create({
          data: {
            name: memberData.name,
            role: memberData.role,
            pin: hashedPin,
            email: memberData.email,
            congregationId: '1441',
            isActive: true,
          }
        });

        console.log(`✅ Created member: ${member.name} (${member.role})`);
      }
    } else {
      console.log('Members already exist, checking PIN format...');

      // Check if any members need PIN hashing
      for (const member of existingMembers) {
        if (member.pin && !member.pin.startsWith('$2')) {
          console.log(`Updating PIN for ${member.name}...`);
          const hashedPin = await bcrypt.hash(member.pin, 12);

          await prisma.member.update({
            where: { id: member.id },
            data: { pin: hashedPin }
          });
        }
      }
    }

    // Step 4: Test authentication
    console.log('\n4️⃣ Testing authentication...');

    const testAuth = async (congId, pin) => {
      console.log(`\nTesting: Congregation ${congId}, PIN ${pin}`);

      try {
        // Find congregation
        const congregation = await prisma.congregation.findUnique({
          where: {
            id: congId.toString().toUpperCase(),
            isActive: true,
          },
        });

        if (!congregation) {
          console.log('❌ Congregation not found or inactive');
          return false;
        }

        console.log('✅ Congregation found');

        // Verify PIN
        const isPinValid = await bcrypt.compare(pin, congregation.pin);
        if (!isPinValid) {
          console.log('❌ PIN verification failed');
          return false;
        }

        console.log('✅ PIN verification successful');

        // Check members
        const members = await prisma.member.findMany({
          where: {
            congregationId: congregation.id,
            isActive: true,
          },
          orderBy: [
            { role: 'desc' },
            { createdAt: 'asc' },
          ],
        });

        if (members.length === 0) {
          console.log('❌ No active members found');
          return false;
        }

        console.log(`✅ Found ${members.length} active members`);
        console.log(`✅ Default member: ${members[0].name} (${members[0].role})`);
        console.log('🎉 Authentication would succeed!');
        return true;

      } catch (error) {
        console.log(`❌ Authentication test failed: ${error.message}`);
        return false;
      }
    };

    const auth1930 = await testAuth('1441', '1930');
    const auth5555 = await testAuth('1441', '5555');

    // Step 5: Create additional test user if 5555 PIN is needed
    if (!auth5555) {
      console.log('\n5️⃣ Creating test user with PIN 5555...');

      const hashedPin5555 = await bcrypt.hash('5555', 12);

      const testMember = await prisma.member.create({
        data: {
          name: 'Test Developer',
          role: 'developer',
          pin: hashedPin5555,
          email: '<EMAIL>',
          congregationId: '1441',
          isActive: true,
        }
      });

      console.log(`✅ Created test member: ${testMember.name}`);

      // Test again
      await testAuth('1441', '5555');
    }

    console.log('\n🎉 Migration completed successfully!');

    // Print summary
    const finalCongregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    const finalMembers = await prisma.member.findMany({
      where: { congregationId: '1441' }
    });

    console.log('\n📊 Migration Summary:');
    console.log(`   • Congregation: ${finalCongregation?.name} (ID: ${finalCongregation?.id})`);
    console.log(`   • PIN: ${finalCongregation?.pin ? 'Hashed ✅' : 'Missing ❌'}`);
    console.log(`   • Members: ${finalMembers.length}`);
    console.log(`   • Active members: ${finalMembers.filter(m => m.isActive).length}`);

    console.log('\n🔐 Authentication Test Results:');
    console.log(`   • Congregation 1441 + PIN 1930: ${auth1930 ? '✅ Working' : '❌ Failed'}`);
    console.log(`   • Congregation 1441 + PIN 5555: ${auth5555 ? '✅ Working' : '❌ Failed'}`);

    if (auth1930 || auth5555) {
      console.log('\n🎉 You can now authenticate with:');
      if (auth1930) console.log('   • Congregation ID: 1441, PIN: 1930');
      if (auth5555) console.log('   • Congregation ID: 1441, PIN: 5555');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  migrateFromOldDatabase()
    .then(() => {
      console.log('✅ Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateFromOldDatabase };
