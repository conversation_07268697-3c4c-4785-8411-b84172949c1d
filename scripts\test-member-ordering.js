#!/usr/bin/env node

/**
 * Test Member Ordering for Territory Assignment
 * 
 * This script tests the member ordering logic to ensure members appear
 * in the correct order: Elders, Ministerial Servants, Publishers
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Test member ordering logic
 */
async function testMemberOrdering() {
  try {
    console.log('🧪 Testing Member Ordering for Territory Assignment');
    console.log('==================================================\n');

    // Get all congregation members
    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441'
      },
      select: {
        id: true,
        name: true,
        role: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`👥 Total members: ${members.length}`);

    // Apply the same sorting logic as the component
    const sortedMembers = members.sort((a, b) => {
      // Define role priority: elders first, then ministerial servants, then publishers
      const roleOrder = {
        'elder': 1,
        'overseer_coordinator': 1,
        'coordinator': 1,
        'ministerial_servant': 2,
        'publisher': 3,
        'auxiliary_pioneer': 3,
        'regular_pioneer': 3,
        'special_pioneer': 3
      };
      
      const aOrder = roleOrder[a.role] || 4; // Unknown roles go last
      const bOrder = roleOrder[b.role] || 4;
      
      // First sort by role priority
      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }
      
      // Then sort alphabetically by name within the same role
      return a.name.localeCompare(b.name);
    });

    // Helper functions for role grouping
    const getRoleGroup = (role) => {
      if (['elder', 'overseer_coordinator', 'coordinator'].includes(role)) {
        return 1; // Elders
      }
      if (role === 'ministerial_servant') {
        return 2; // Ministerial Servants
      }
      return 3; // Publishers and others
    };

    const getRoleGroupLabel = (role) => {
      const group = getRoleGroup(role);
      switch (group) {
        case 1: return 'Ancianos';
        case 2: return 'Siervos Ministeriales';
        case 3: return 'Publicadores';
        default: return 'Otros';
      }
    };

    // Group members by role for display
    const groupedMembers = {};
    let currentGroup = null;

    sortedMembers.forEach((member, index) => {
      const group = getRoleGroup(member.role);
      const groupLabel = getRoleGroupLabel(member.role);
      
      if (group !== currentGroup) {
        currentGroup = group;
        if (!groupedMembers[groupLabel]) {
          groupedMembers[groupLabel] = [];
        }
      }
      
      groupedMembers[groupLabel].push(member);
    });

    // Display the ordered results
    console.log('\n📋 Member Ordering Results:');
    console.log('===========================\n');

    Object.entries(groupedMembers).forEach(([groupLabel, groupMembers]) => {
      console.log(`${groupLabel.toUpperCase()} (${groupMembers.length} members):`);
      groupMembers.forEach((member, index) => {
        console.log(`   ${index + 1}. ${member.name} (${member.role})`);
      });
      console.log('');
    });

    // Verify the ordering is correct
    console.log('✅ Verification:');
    console.log('================');

    const elders = sortedMembers.filter(m => ['elder', 'overseer_coordinator', 'coordinator'].includes(m.role));
    const ministerialServants = sortedMembers.filter(m => m.role === 'ministerial_servant');
    const publishers = sortedMembers.filter(m => !['elder', 'overseer_coordinator', 'coordinator', 'ministerial_servant'].includes(m.role));

    console.log(`✅ Elders appear first: ${elders.length} members`);
    console.log(`✅ Ministerial Servants appear second: ${ministerialServants.length} members`);
    console.log(`✅ Publishers appear third: ${publishers.length} members`);

    // Check that the order is maintained
    let elderIndex = -1;
    let msIndex = -1;
    let publisherIndex = -1;

    sortedMembers.forEach((member, index) => {
      if (['elder', 'overseer_coordinator', 'coordinator'].includes(member.role) && elderIndex === -1) {
        elderIndex = index;
      }
      if (member.role === 'ministerial_servant' && msIndex === -1) {
        msIndex = index;
      }
      if (!['elder', 'overseer_coordinator', 'coordinator', 'ministerial_servant'].includes(member.role) && publisherIndex === -1) {
        publisherIndex = index;
      }
    });

    const orderingCorrect = (elderIndex === -1 || msIndex === -1 || elderIndex < msIndex) &&
                           (msIndex === -1 || publisherIndex === -1 || msIndex < publisherIndex) &&
                           (elderIndex === -1 || publisherIndex === -1 || elderIndex < publisherIndex);

    console.log(`✅ Ordering is correct: ${orderingCorrect ? 'YES' : 'NO'}`);

    if (orderingCorrect) {
      console.log('\n🎉 Member ordering is working correctly!');
      console.log('   1. Elders appear first');
      console.log('   2. Ministerial Servants appear second');
      console.log('   3. Publishers appear third');
      console.log('   4. Within each group, members are sorted alphabetically');
    } else {
      console.log('\n❌ Member ordering needs adjustment');
    }

    return orderingCorrect;

  } catch (error) {
    console.error('❌ Error testing member ordering:', error);
    return false;
  }
}

/**
 * Test role distribution
 */
async function testRoleDistribution() {
  try {
    console.log('\n🧪 Testing Role Distribution');
    console.log('============================\n');

    // Get role distribution
    const roleStats = await prisma.member.groupBy({
      by: ['role'],
      where: {
        congregationId: '1441'
      },
      _count: {
        id: true
      }
    });

    console.log('📊 Role Distribution:');
    roleStats.forEach(stat => {
      console.log(`   ${stat.role}: ${stat._count.id} members`);
    });

    // Calculate percentages
    const totalMembers = roleStats.reduce((sum, stat) => sum + stat._count.id, 0);
    
    console.log('\n📈 Role Percentages:');
    roleStats.forEach(stat => {
      const percentage = Math.round((stat._count.id / totalMembers) * 100);
      console.log(`   ${stat.role}: ${percentage}%`);
    });

    return true;

  } catch (error) {
    console.error('❌ Error testing role distribution:', error);
    return false;
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🧪 Member Ordering Test for Territory Assignment');
  console.log('================================================\n');

  try {
    const tests = [
      { name: 'Member Ordering', test: testMemberOrdering },
      { name: 'Role Distribution', test: testRoleDistribution }
    ];

    let passed = 0;
    let total = tests.length;

    for (const { name, test } of tests) {
      try {
        const result = await test();
        if (result) {
          passed++;
          console.log(`\n✅ ${name} test: PASSED`);
        } else {
          console.log(`\n❌ ${name} test: FAILED`);
        }
      } catch (error) {
        console.log(`\n❌ ${name} test: ERROR - ${error.message}`);
      }
    }

    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`Passed: ${passed}/${total}`);
    console.log(`Status: ${passed === total ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    if (passed === total) {
      console.log('\n🎉 Member ordering for territory assignment is working correctly!');
      console.log('✅ Elders will appear first in the assignment interface');
      console.log('✅ Ministerial Servants will appear second');
      console.log('✅ Publishers will appear third');
      console.log('✅ Members within each group are sorted alphabetically');
    }

  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testMemberOrdering,
  testRoleDistribution
};
