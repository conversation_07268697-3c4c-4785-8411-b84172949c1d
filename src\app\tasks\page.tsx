'use client';

/**
 * Tasks Management Page
 *
 * Main interface for task management following the Tareas.png design.
 * Provides task viewing, status updates, and assignment management.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface Task {
  id: string;
  title: string;
  description: string | null;
  category: string;
  frequency: string;
  estimatedTime: number | null;
  instructions: string | null;
}

interface TaskAssignment {
  id: string;
  taskId: string;
  assignedDate: string;
  dueDate: string | null;
  status: string;
  notes: string | null;
  completedAt: string | null;
  task: Task;
}

export default function TasksPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [assignments, setAssignments] = useState<TaskAssignment[]>([]);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [isUpdating, setIsUpdating] = useState<string | null>(null);

  useEffect(() => {
    checkAuthentication();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      fetchTaskAssignments();
    }
  }, [user, activeFilter]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();
      setUser(data.member);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTaskAssignments = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('hermanos_token');
      const statusFilter = activeFilter === 'all' ? '' : `&status=${activeFilter}`;

      const response = await fetch(`/api/tasks/assignments?memberId=${user.id}${statusFilter}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAssignments(data.assignments || []);
      }
    } catch (error) {
      console.error('Error fetching task assignments:', error);
    }
  };

  const updateTaskStatus = async (assignmentId: string, newStatus: string, notes?: string) => {
    if (!user) return;

    setIsUpdating(assignmentId);
    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/tasks/assignments', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          action: 'update_status',
          assignmentId,
          status: newStatus,
          notes,
        }),
      });

      if (response.ok) {
        await fetchTaskAssignments();
        alert('Estado de tarea actualizado exitosamente');
      } else {
        const errorData = await response.json();
        alert(`Error al actualizar: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error updating task status:', error);
      alert('Error al actualizar el estado de la tarea');
    } finally {
      setIsUpdating(null);
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'pending':
        return 'Pendiente';
      case 'in_progress':
        return 'En Progreso';
      case 'completed':
        return 'Completado';
      case 'cancelled':
        return 'Cancelado';
      default:
        return status;
    }
  };

  const getFrequencyText = (frequency: string): string => {
    switch (frequency) {
      case 'weekly':
        return 'Semanal';
      case 'monthly':
        return 'Mensual';
      case 'quarterly':
        return 'Trimestral';
      case 'yearly':
        return 'Anual';
      case 'one-time':
        return 'Una vez';
      default:
        return frequency;
    }
  };

  const formatEstimatedTime = (minutes: number | null): string => {
    if (!minutes) return 'No especificado';

    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const isOverdue = (assignment: TaskAssignment): boolean => {
    if (!assignment.dueDate || assignment.status === 'completed' || assignment.status === 'cancelled') {
      return false;
    }
    return new Date(assignment.dueDate) < new Date();
  };

  const filteredAssignments = assignments.filter(assignment => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'overdue') return isOverdue(assignment);
    return assignment.status === activeFilter;
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-orange-600 text-white p-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-orange-200 hover:text-white mb-2 flex items-center"
            >
              ← Volver al Panel
            </button>
            <h1 className="text-2xl font-bold">Tareas</h1>
            <p className="text-orange-200">Gestiona tus responsabilidades de la congregación</p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-6">
        {/* Filter Tabs */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'Todas', count: assignments.length },
              { key: 'pending', label: 'Pendientes', count: assignments.filter(a => a.status === 'pending').length },
              { key: 'in_progress', label: 'En Progreso', count: assignments.filter(a => a.status === 'in_progress').length },
              { key: 'completed', label: 'Completadas', count: assignments.filter(a => a.status === 'completed').length },
              { key: 'overdue', label: 'Vencidas', count: assignments.filter(a => isOverdue(a)).length },
            ].map(filter => (
              <button
                key={filter.key}
                onClick={() => setActiveFilter(filter.key)}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  activeFilter === filter.key
                    ? 'bg-orange-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>
        </div>

        {/* Task Assignments */}
        {filteredAssignments.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p className="text-gray-500 text-lg">No hay tareas {activeFilter === 'all' ? '' : getStatusText(activeFilter).toLowerCase()}</p>
            <p className="text-gray-400">Las tareas asignadas aparecerán aquí</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAssignments.map((assignment) => (
              <div
                key={assignment.id}
                className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${
                  isOverdue(assignment) ? 'border-red-500' :
                  assignment.status === 'completed' ? 'border-green-500' :
                  assignment.status === 'in_progress' ? 'border-blue-500' :
                  'border-orange-500'
                }`}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{assignment.task.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(assignment.status)}`}>
                        {getStatusText(assignment.status)}
                      </span>
                      {isOverdue(assignment) && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Vencida
                        </span>
                      )}
                    </div>

                    <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a2 2 0 012-2z" />
                        </svg>
                        {assignment.task.category}
                      </span>

                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {formatEstimatedTime(assignment.task.estimatedTime)}
                      </span>

                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        {getFrequencyText(assignment.task.frequency)}
                      </span>
                    </div>

                    {assignment.task.description && (
                      <p className="text-gray-700 mb-3">{assignment.task.description}</p>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Fecha asignada:</span>
                        <p className="text-gray-600">{formatDate(assignment.assignedDate)}</p>
                      </div>

                      {assignment.dueDate && (
                        <div>
                          <span className="font-medium text-gray-700">Fecha límite:</span>
                          <p className={`${isOverdue(assignment) ? 'text-red-600 font-medium' : 'text-gray-600'}`}>
                            {formatDate(assignment.dueDate)}
                          </p>
                        </div>
                      )}
                    </div>

                    {assignment.task.instructions && (
                      <div className="mt-3 p-3 bg-blue-50 rounded-md">
                        <span className="font-medium text-blue-900">Instrucciones:</span>
                        <p className="text-blue-800 text-sm mt-1">{assignment.task.instructions}</p>
                      </div>
                    )}

                    {assignment.notes && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-md">
                        <span className="font-medium text-gray-700">Notas:</span>
                        <p className="text-gray-600 text-sm mt-1">{assignment.notes}</p>
                      </div>
                    )}

                    {assignment.completedAt && (
                      <div className="mt-3 text-sm text-green-600">
                        ✓ Completado el {formatDate(assignment.completedAt)}
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                {assignment.status !== 'completed' && assignment.status !== 'cancelled' && (
                  <div className="flex gap-2 pt-4 border-t border-gray-200">
                    {assignment.status === 'pending' && (
                      <button
                        onClick={() => updateTaskStatus(assignment.id, 'in_progress')}
                        disabled={isUpdating === assignment.id}
                        className={`px-4 py-2 rounded-md font-medium transition-colors ${
                          isUpdating === assignment.id
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        } text-white`}
                      >
                        {isUpdating === assignment.id ? 'Actualizando...' : 'Comenzar'}
                      </button>
                    )}

                    {assignment.status === 'in_progress' && (
                      <button
                        onClick={() => {
                          const notes = prompt('Notas de finalización (opcional):');
                          if (notes !== null) {
                            updateTaskStatus(assignment.id, 'completed', notes);
                          }
                        }}
                        disabled={isUpdating === assignment.id}
                        className={`px-4 py-2 rounded-md font-medium transition-colors ${
                          isUpdating === assignment.id
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700'
                        } text-white`}
                      >
                        {isUpdating === assignment.id ? 'Actualizando...' : 'Completar'}
                      </button>
                    )}

                    <button
                      onClick={() => {
                        const notes = prompt('Notas adicionales (opcional):');
                        if (notes !== null) {
                          updateTaskStatus(assignment.id, assignment.status, notes);
                        }
                      }}
                      disabled={isUpdating === assignment.id}
                      className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md font-medium hover:bg-gray-200 transition-colors"
                    >
                      Agregar Notas
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
