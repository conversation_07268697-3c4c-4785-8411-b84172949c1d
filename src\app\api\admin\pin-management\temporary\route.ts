/**
 * Temporary PIN Management API Endpoint
 *
 * Handles temporary PIN creation and management for enhanced security.
 * Only accessible to elders, coordinators, and developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PinService } from '@/lib/services/pinService';

// Validation schema for temporary PIN creation
const TemporaryPinSchema = z.object({
  memberId: z.string().min(1, 'Member ID is required'),
  resetType: z.enum(['temporary', 'permanent', 'emergency']).default('temporary'),
  expirationHours: z.number().min(1).max(168).optional(), // 1 hour to 1 week
  reason: z.string().optional(),
  requireChange: z.boolean().default(true),
});

/**
 * POST - Create temporary PIN for a member
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create temporary PINs' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = TemporaryPinSchema.parse(body);

    // Extract client information for audit trail
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Check if member account is locked
    const isLocked = await PinService.isAccountLocked(user.congregationId, validatedData.memberId);
    if (isLocked) {
      return NextResponse.json(
        { error: 'Cannot create temporary PIN for locked account' },
        { status: 423 } // Locked status
      );
    }

    // Create temporary PIN
    const result = await PinService.createTemporaryPin(
      user.congregationId,
      validatedData.memberId,
      user.userId,
      validatedData.resetType,
      validatedData.reason,
      validatedData.expirationHours,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: true,
      temporaryPin: result.temporaryPin,
      expirationDate: result.expirationDate,
      resetType: validatedData.resetType,
      requireChange: validatedData.requireChange,
      message: 'Temporary PIN created successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Temporary PIN creation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid temporary PIN data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to create temporary PIN';
    
    return NextResponse.json(
      { 
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET - Get temporary PIN status for a member
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view temporary PIN status' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('memberId');

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    // Get active temporary PINs for the member
    const temporaryPins = await PinService.getSecurityAuditEvents(
      user.congregationId,
      memberId,
      'temporary_pin_created',
      10
    );

    return NextResponse.json({
      success: true,
      temporaryPins,
      count: temporaryPins.length,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Temporary PIN status error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get temporary PIN status',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
