/**
 * Territory Batch Geocoding API Endpoint
 *
 * Handles batch geocoding of territory addresses and coordinate caching.
 * Provides rate-limited geocoding with progress tracking.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { geocodingService } from '@/services/territories/GeocodingService';

// Validation schema for batch geocoding request
const BatchGeocodeSchema = z.object({
  territoryIds: z.array(z.string()).min(1).max(50), // Limit batch size
  forceUpdate: z.boolean().optional().default(false),
  congregationId: z.string().optional()
});

/**
 * POST /api/territories/geocode - Batch geocode territory addresses
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Get member details for permission checking
    const member = await prisma.member.findFirst({
      where: {
        congregationId: user.congregationId,
        pin: user.pin
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for territory geocoding' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = BatchGeocodeSchema.parse(body);

    const congregationId = validatedData.congregationId || user.congregationId;

    // Fetch territories to geocode
    const territories = await prisma.territory.findMany({
      where: {
        id: { in: validatedData.territoryIds },
        congregationId: congregationId
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        boundaries: true
      }
    });

    if (territories.length === 0) {
      return NextResponse.json(
        { error: 'No territories found for geocoding' },
        { status: 404 }
      );
    }

    // Filter territories that need geocoding
    const territoriesToGeocode = territories.filter(territory => {
      if (validatedData.forceUpdate) return true;
      
      // Check if territory already has coordinates
      if (territory.boundaries && typeof territory.boundaries === 'object') {
        const boundaries = territory.boundaries as any;
        return !(boundaries.coordinates && Array.isArray(boundaries.coordinates));
      }
      
      return true;
    });

    console.log(`Geocoding ${territoriesToGeocode.length} territories out of ${territories.length} requested`);

    // Batch geocode addresses
    const geocodingResults = [];
    const updatePromises = [];

    for (const territory of territoriesToGeocode) {
      try {
        console.log(`Geocoding territory ${territory.territoryNumber}: ${territory.address}`);
        
        const result = await geocodingService.geocodeAddress(territory.address);
        
        if (result) {
          // Cache coordinates in boundaries field
          const updatePromise = prisma.territory.update({
            where: { id: territory.id },
            data: {
              boundaries: {
                type: 'Point',
                coordinates: [result.longitude, result.latitude]
              }
            }
          });

          updatePromises.push(updatePromise);

          geocodingResults.push({
            territoryId: territory.id,
            territoryNumber: territory.territoryNumber,
            address: territory.address,
            success: true,
            coordinates: {
              latitude: result.latitude,
              longitude: result.longitude
            },
            geocodingResult: {
              display_name: result.display_name,
              address: result.address
            }
          });

          console.log(`✅ Successfully geocoded territory ${territory.territoryNumber}`);
        } else {
          geocodingResults.push({
            territoryId: territory.id,
            territoryNumber: territory.territoryNumber,
            address: territory.address,
            success: false,
            error: 'Geocoding failed - address not found'
          });

          console.log(`❌ Failed to geocode territory ${territory.territoryNumber}`);
        }
      } catch (error) {
        geocodingResults.push({
          territoryId: territory.id,
          territoryNumber: territory.territoryNumber,
          address: territory.address,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown geocoding error'
        });

        console.error(`❌ Error geocoding territory ${territory.territoryNumber}:`, error);
      }
    }

    // Wait for all database updates to complete
    try {
      await Promise.allSettled(updatePromises);
      console.log(`✅ Updated ${updatePromises.length} territories with coordinates`);
    } catch (error) {
      console.error('Error updating territories with coordinates:', error);
    }

    // Calculate statistics
    const successCount = geocodingResults.filter(r => r.success).length;
    const failureCount = geocodingResults.filter(r => !r.success).length;
    const skippedCount = territories.length - territoriesToGeocode.length;

    const response = {
      success: true,
      message: `Geocoding completed: ${successCount} successful, ${failureCount} failed, ${skippedCount} skipped`,
      statistics: {
        total: territories.length,
        processed: territoriesToGeocode.length,
        successful: successCount,
        failed: failureCount,
        skipped: skippedCount,
        successRate: territoriesToGeocode.length > 0 ? (successCount / territoriesToGeocode.length) * 100 : 0
      },
      results: geocodingResults,
      metadata: {
        forceUpdate: validatedData.forceUpdate,
        congregationId: congregationId,
        processedAt: new Date().toISOString()
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Territory geocoding POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to geocode territories',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/territories/geocode - Get geocoding status and statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Get territories with geocoding status
    const territories = await prisma.territory.findMany({
      where: {
        congregationId: user.congregationId
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        boundaries: true,
        status: true
      }
    });

    // Analyze geocoding status
    const geocodedTerritories = territories.filter(territory => {
      if (territory.boundaries && typeof territory.boundaries === 'object') {
        const boundaries = territory.boundaries as any;
        return boundaries.coordinates && Array.isArray(boundaries.coordinates);
      }
      return false;
    });

    const needsGeocodingTerritories = territories.filter(territory => {
      if (territory.boundaries && typeof territory.boundaries === 'object') {
        const boundaries = territory.boundaries as any;
        return !(boundaries.coordinates && Array.isArray(boundaries.coordinates));
      }
      return true;
    });

    const response = {
      success: true,
      statistics: {
        total: territories.length,
        geocoded: geocodedTerritories.length,
        needsGeocoding: needsGeocodingTerritories.length,
        geocodingPercentage: territories.length > 0 ? (geocodedTerritories.length / territories.length) * 100 : 0
      },
      geocodedTerritories: geocodedTerritories.map(t => ({
        id: t.id,
        territoryNumber: t.territoryNumber,
        status: t.status
      })),
      needsGeocodingTerritories: needsGeocodingTerritories.map(t => ({
        id: t.id,
        territoryNumber: t.territoryNumber,
        address: t.address,
        status: t.status
      }))
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Territory geocoding GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get geocoding status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to geocode territories or GET to check status.' },
    { status: 405 }
  );
}
