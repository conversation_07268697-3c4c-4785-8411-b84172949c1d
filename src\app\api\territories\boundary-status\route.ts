/**
 * Territory Boundary Status API Endpoint
 * 
 * Provides information about which territories have real boundary data
 * and which territories need boundary data to be added.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/territories/boundary-status
 * Get boundary status for all territories
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders can access boundary management
    const hasElderAccess = member.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(member.role);

    if (!hasElderAccess) {
      return NextResponse.json(
        { error: 'Elder access required for boundary management' },
        { status: 403 }
      );
    }

    // Get all territories with boundary status
    const territories = await prisma.territory.findMany({
      where: {
        congregationId: member.congregationId
      },
      select: {
        territoryNumber: true,
        address: true,
        boundaries: true
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });

    // Calculate statistics
    const totalTerritories = territories.length;
    const territoriesWithBoundaries = territories.filter(t => t.boundaries !== null).length;
    const territoriesWithoutBoundaries = totalTerritories - territoriesWithBoundaries;

    // Prepare territory list with boundary status
    const territoryList = territories.map(territory => ({
      territoryNumber: territory.territoryNumber,
      hasBoundaries: territory.boundaries !== null,
      address: territory.address
    }));

    const response = {
      totalTerritories,
      territoriesWithBoundaries,
      territoriesWithoutBoundaries,
      territories: territoryList,
      summary: {
        percentage: totalTerritories > 0 
          ? Math.round((territoriesWithBoundaries / totalTerritories) * 100)
          : 0,
        needsBoundaries: territoriesWithoutBoundaries > 0,
        message: territoriesWithoutBoundaries === 0 
          ? 'All territories have boundary data'
          : `${territoriesWithoutBoundaries} territories need boundary data`
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching boundary status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch boundary status' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/territories/boundary-status
 * Refresh boundary status (useful after bulk updates)
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders can refresh boundary status
    const hasElderAccess = member.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(member.role);

    if (!hasElderAccess) {
      return NextResponse.json(
        { error: 'Elder access required for boundary management' },
        { status: 403 }
      );
    }

    // This could trigger a boundary validation or cleanup process
    // For now, just return the current status
    const getResponse = await GET(request);
    const data = await getResponse.json();

    return NextResponse.json({
      success: true,
      message: 'Boundary status refreshed',
      data
    });

  } catch (error) {
    console.error('Error refreshing boundary status:', error);
    return NextResponse.json(
      { error: 'Failed to refresh boundary status' },
      { status: 500 }
    );
  }
}
