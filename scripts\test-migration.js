#!/usr/bin/env node

/**
 * Test Migration Script for Hermanos App
 * 
 * Tests the migration functionality with sample data to ensure
 * the migration script works correctly before running on production data.
 */

const { PrismaClient } = require('@prisma/client');

class MigrationTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testData = {
      congregations: [],
      members: [],
      tasks: [],
      letters: [],
    };
  }

  async initialize() {
    try {
      await this.prisma.$connect();
      console.log('✅ Prisma client connected');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async createTestData() {
    console.log('\n📝 Creating test data...');

    try {
      // Create test congregation
      const congregation = await this.prisma.congregation.create({
        data: {
          id: 'TEST1441',
          name: 'Test Congregation',
          region: 'Test Region',
          pin: '$2b$12$testhashedpin',
          language: 'es',
          timezone: 'America/New_York',
          settings: {
            numberOfGroups: 7,
            defaultMeetingLocation: 'Kingdom Hall'
          },
        },
      });
      this.testData.congregations.push(congregation);
      console.log('✅ Created test congregation');

      // Create congregation settings
      await this.prisma.congregationSetting.create({
        data: {
          congregationId: congregation.id,
          settingKey: 'language',
          settingValue: 'es',
        },
      });

      await this.prisma.congregationSetting.create({
        data: {
          congregationId: congregation.id,
          settingKey: 'timezone',
          settingValue: 'America/New_York',
        },
      });
      console.log('✅ Created congregation settings');

      // Create test role
      const role = await this.prisma.role.create({
        data: {
          name: 'test_elder',
          description: 'Test Elder Role',
          permissions: ['letters_edit', 'letters_delete'],
        },
      });
      console.log('✅ Created test role');

      // Create test member
      const member = await this.prisma.member.create({
        data: {
          congregationId: congregation.id,
          name: 'Test Elder',
          email: '<EMAIL>',
          role: 'elder',
          pin: '$2b$12$testhashedpin',
          preferences: {
            language: 'es',
            notifications: true
          },
        },
      });
      this.testData.members.push(member);
      console.log('✅ Created test member');

      // Create service group
      await this.prisma.serviceGroup.create({
        data: {
          congregationId: congregation.id,
          name: 'Test Group 1',
          groupNumber: 1,
          description: 'Test service group',
        },
      });
      console.log('✅ Created test service group');

      // Create territory
      await this.prisma.territory.create({
        data: {
          congregationId: congregation.id,
          name: 'Test Territory 1',
          description: 'Test territory for migration testing',
          status: 'available',
        },
      });
      console.log('✅ Created test territory');

      // Create test task
      const task = await this.prisma.task.create({
        data: {
          congregationId: congregation.id,
          title: 'Test Task',
          description: 'Test task for migration testing',
          category: 'maintenance',
          frequency: 'weekly',
          estimatedTime: 30,
        },
      });
      this.testData.tasks.push(task);
      console.log('✅ Created test task');

      // Create task assignment
      await this.prisma.taskAssignment.create({
        data: {
          congregationId: congregation.id,
          taskId: task.id,
          assignedMemberId: member.id,
          assignedDate: new Date(),
          status: 'pending',
        },
      });
      console.log('✅ Created test task assignment');

      // Create test letter
      const letter = await this.prisma.letter.create({
        data: {
          congregationId: congregation.id,
          title: 'Test Letter',
          filename: 'test-letter.pdf',
          filePath: '/uploads/letters/test-letter.pdf',
          fileSize: 1024,
          mimeType: 'application/pdf',
          category: 'announcements',
          uploadedById: member.id,
        },
      });
      this.testData.letters.push(letter);
      console.log('✅ Created test letter');

      // Create test songs
      await this.prisma.song.create({
        data: {
          songNumber: 999,
          titleEs: 'Canción de Prueba',
          titleEn: 'Test Song',
          category: 'test',
        },
      });

      await this.prisma.specialSong.create({
        data: {
          keyName: 'test_special',
          titleEs: 'Canción Especial de Prueba',
          titleEn: 'Test Special Song',
          isCustom: true,
        },
      });
      console.log('✅ Created test songs');

      // Create field service record
      await this.prisma.fieldServiceRecord.create({
        data: {
          congregationId: congregation.id,
          memberId: member.id,
          serviceMonth: new Date('2024-01-01'),
          hours: 10.5,
          placements: 5,
          returnVisits: 3,
          bibleStudies: 1,
        },
      });
      console.log('✅ Created test field service record');

    } catch (error) {
      console.error('❌ Error creating test data:', error.message);
      throw error;
    }
  }

  async validateTestData() {
    console.log('\n🔍 Validating test data...');

    try {
      // Validate congregation and relationships
      const congregation = await this.prisma.congregation.findFirst({
        where: { name: 'Test Congregation' },
        include: {
          members: true,
          tasks: true,
          letters: true,
          congregationSettings: true,
          serviceGroups: true,
          territories: true,
        },
      });

      if (!congregation) {
        throw new Error('Test congregation not found');
      }

      console.log(`✅ Congregation found with ${congregation.members.length} members`);
      console.log(`✅ Congregation has ${congregation.tasks.length} tasks`);
      console.log(`✅ Congregation has ${congregation.letters.length} letters`);
      console.log(`✅ Congregation has ${congregation.congregationSettings.length} settings`);
      console.log(`✅ Congregation has ${congregation.serviceGroups.length} service groups`);
      console.log(`✅ Congregation has ${congregation.territories.length} territories`);

      // Validate task assignments
      const taskAssignments = await this.prisma.taskAssignment.findMany({
        where: { congregationId: congregation.id },
        include: { task: true, assignedMember: true },
      });

      console.log(`✅ Found ${taskAssignments.length} task assignments`);

      // Validate field service records
      const serviceRecords = await this.prisma.fieldServiceRecord.findMany({
        where: { congregationId: congregation.id },
        include: { member: true },
      });

      console.log(`✅ Found ${serviceRecords.length} field service records`);

      // Validate songs
      const songs = await this.prisma.song.count();
      const specialSongs = await this.prisma.specialSong.count();

      console.log(`✅ Found ${songs} songs and ${specialSongs} special songs`);

      console.log('✅ All test data validation passed');

    } catch (error) {
      console.error('❌ Error validating test data:', error.message);
      throw error;
    }
  }

  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');

    try {
      // Delete in reverse order of dependencies
      await this.prisma.fieldServiceRecord.deleteMany({
        where: { congregation: { name: 'Test Congregation' } },
      });

      await this.prisma.taskAssignment.deleteMany({
        where: { congregation: { name: 'Test Congregation' } },
      });

      await this.prisma.letter.deleteMany({
        where: { congregation: { name: 'Test Congregation' } },
      });

      await this.prisma.task.deleteMany({
        where: { congregation: { name: 'Test Congregation' } },
      });

      await this.prisma.territory.deleteMany({
        where: { congregation: { name: 'Test Congregation' } },
      });

      await this.prisma.serviceGroup.deleteMany({
        where: { congregation: { name: 'Test Congregation' } },
      });

      await this.prisma.congregationSetting.deleteMany({
        where: { congregation: { name: 'Test Congregation' } },
      });

      await this.prisma.member.deleteMany({
        where: { congregation: { name: 'Test Congregation' } },
      });

      await this.prisma.congregation.deleteMany({
        where: { name: 'Test Congregation' },
      });

      await this.prisma.role.deleteMany({
        where: { name: 'test_elder' },
      });

      await this.prisma.song.deleteMany({
        where: { songNumber: 999 },
      });

      await this.prisma.specialSong.deleteMany({
        where: { keyName: 'test_special' },
      });

      console.log('✅ Test data cleanup completed');

    } catch (error) {
      console.error('❌ Error during cleanup:', error.message);
      throw error;
    }
  }

  async runTest() {
    console.log('🧪 Starting migration test...\n');

    try {
      await this.initialize();
      await this.createTestData();
      await this.validateTestData();
      
      console.log('\n✅ Migration test completed successfully!');
      
    } catch (error) {
      console.error('\n❌ Migration test failed:', error.message);
      throw error;
    } finally {
      await this.cleanupTestData();
      await this.prisma.$disconnect();
    }
  }
}

// Main execution
async function main() {
  const tester = new MigrationTester();
  await tester.runTest();
}

// Run test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = MigrationTester;
