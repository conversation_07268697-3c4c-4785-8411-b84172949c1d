/**
 * Assignment History Service
 * 
 * Provides methods for retrieving and analyzing territory assignment history,
 * including filtering, pagination, and statistics calculation.
 */

import { prisma } from '@/lib/prisma';
import type { TerritoryAssignment, AssignmentSummary } from '@/types/territories/assignment';

export interface AssignmentHistoryFilters {
  member?: string;
  dateFrom?: Date;
  dateTo?: Date;
  status?: 'active' | 'completed' | 'overdue' | 'cancelled';
}

export interface AssignmentHistoryOptions {
  page?: number;
  limit?: number;
  filters?: AssignmentHistoryFilters;
}

export interface AssignmentHistoryResult {
  assignments: TerritoryAssignment[];
  totalCount: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export class AssignmentHistoryService {
  /**
   * Get assignment history for a specific territory
   */
  static async getAssignmentHistory(
    territoryId: string,
    congregationId: string,
    options: AssignmentHistoryOptions = {}
  ): Promise<AssignmentHistoryResult> {
    const { page = 1, limit = 20, filters = {} } = options;

    // Build where clause
    const whereClause: any = {
      territoryId,
      congregationId
    };

    // Add member filter
    if (filters.member) {
      whereClause.member = {
        name: {
          contains: filters.member,
          mode: 'insensitive'
        }
      };
    }

    // Add date range filters
    if (filters.dateFrom || filters.dateTo) {
      whereClause.assignedAt = {};
      if (filters.dateFrom) {
        whereClause.assignedAt.gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        whereClause.assignedAt.lte = filters.dateTo;
      }
    }

    // Add status filter
    if (filters.status) {
      whereClause.status = filters.status;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get assignments and total count
    const [assignments, totalCount] = await Promise.all([
      prisma.territoryAssignment.findMany({
        where: whereClause,
        include: {
          member: {
            select: {
              id: true,
              name: true,
              role: true,
              email: true
            }
          },
          assignedByMember: {
            select: {
              id: true,
              name: true,
              role: true
            }
          },
          territory: {
            select: {
              id: true,
              territoryNumber: true,
              address: true,
              status: true
            }
          }
        },
        orderBy: {
          assignedAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.territoryAssignment.count({
        where: whereClause
      })
    ]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      assignments: assignments as TerritoryAssignment[],
      totalCount,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPreviousPage
    };
  }

  /**
   * Get assignment statistics for a territory
   */
  static async getAssignmentStatistics(
    territoryId: string,
    congregationId: string
  ): Promise<AssignmentSummary> {
    const assignments = await prisma.territoryAssignment.findMany({
      where: {
        territoryId,
        congregationId
      },
      include: {
        member: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      }
    });

    const totalAssignments = assignments.length;
    const activeAssignments = assignments.filter(a => a.status === 'active').length;
    const completedAssignments = assignments.filter(a => a.status === 'completed').length;
    const overdueAssignments = assignments.filter(a => a.status === 'overdue').length;

    // Calculate average duration for completed assignments
    const completedWithDuration = assignments
      .filter(a => a.status === 'completed' && a.completedAt)
      .map(a => {
        const assignedDate = new Date(a.assignedAt);
        const completedDate = new Date(a.completedAt!);
        return Math.ceil((completedDate.getTime() - assignedDate.getTime()) / (1000 * 60 * 60 * 24));
      });

    const averageDuration = completedWithDuration.length > 0
      ? Math.round(completedWithDuration.reduce((sum, duration) => sum + duration, 0) / completedWithDuration.length)
      : 0;

    return {
      totalAssignments,
      activeAssignments,
      completedAssignments,
      overdueAssignments,
      averageDuration
    };
  }

  /**
   * Get assignment duration in days
   */
  static calculateAssignmentDuration(assignedAt: Date, completedAt?: Date): number | null {
    if (!completedAt) {
      return null;
    }

    const assignedDate = new Date(assignedAt);
    const completedDate = new Date(completedAt);
    return Math.ceil((completedDate.getTime() - assignedDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  /**
   * Get member assignment statistics
   */
  static async getMemberAssignmentStats(
    memberId: string,
    congregationId: string
  ): Promise<{
    totalAssignments: number;
    activeAssignments: number;
    completedAssignments: number;
    averageDuration: number;
    territories: string[];
  }> {
    const assignments = await prisma.territoryAssignment.findMany({
      where: {
        memberId,
        congregationId
      },
      include: {
        territory: {
          select: {
            territoryNumber: true
          }
        }
      }
    });

    const totalAssignments = assignments.length;
    const activeAssignments = assignments.filter(a => a.status === 'active').length;
    const completedAssignments = assignments.filter(a => a.status === 'completed').length;

    // Calculate average duration
    const completedWithDuration = assignments
      .filter(a => a.status === 'completed' && a.completedAt)
      .map(a => this.calculateAssignmentDuration(a.assignedAt, a.completedAt!))
      .filter(duration => duration !== null) as number[];

    const averageDuration = completedWithDuration.length > 0
      ? Math.round(completedWithDuration.reduce((sum, duration) => sum + duration, 0) / completedWithDuration.length)
      : 0;

    // Get unique territories
    const territories = [...new Set(assignments.map(a => a.territory.territoryNumber))];

    return {
      totalAssignments,
      activeAssignments,
      completedAssignments,
      averageDuration,
      territories
    };
  }

  /**
   * Get current assignments for a member
   */
  static async getCurrentAssignments(
    memberId: string,
    congregationId: string
  ): Promise<TerritoryAssignment[]> {
    const assignments = await prisma.territoryAssignment.findMany({
      where: {
        memberId,
        congregationId,
        status: 'active'
      },
      include: {
        territory: {
          select: {
            id: true,
            territoryNumber: true,
            address: true,
            status: true
          }
        },
        assignedByMember: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      },
      orderBy: {
        assignedAt: 'desc'
      }
    });

    return assignments as TerritoryAssignment[];
  }

  /**
   * Check if a member can be assigned more territories
   */
  static async canMemberReceiveAssignment(
    memberId: string,
    congregationId: string,
    maxAssignments: number = 3
  ): Promise<boolean> {
    const activeAssignments = await prisma.territoryAssignment.count({
      where: {
        memberId,
        congregationId,
        status: 'active'
      }
    });

    return activeAssignments < maxAssignments;
  }
}
