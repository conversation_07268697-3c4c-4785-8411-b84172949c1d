/**
 * Territory Status Management API Endpoint
 *
 * Handles territory status updates, partial completion tracking, and visit logging.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { TerritoryStatusService } from '@/services/territories/TerritoryStatusService';

// Validation schemas
const StatusUpdateSchema = z.object({
  status: z.enum(['available', 'assigned', 'completed', 'unavailable']),
  reason: z.string().optional(),
  notes: z.string().optional()
});

/**
 * PUT /api/territories/[id]/status
 * Update territory status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information for role verification
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { role: true, congregationId: true, name: true }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions for status changes
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for territory status changes' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = StatusUpdateSchema.parse(body);

    // Update territory status
    const updatedTerritory = await TerritoryStatusService.updateTerritoryStatus({
      territoryId: params.id,
      newStatus: validatedData.status,
      reason: validatedData.reason,
      notes: validatedData.notes,
      changedBy: user.userId,
      congregationId: member.congregationId
    });

    return NextResponse.json({
      success: true,
      data: {
        territory: updatedTerritory,
        message: `Territory ${updatedTerritory.territoryNumber} status updated to ${validatedData.status}`
      }
    });

  } catch (error) {
    console.error('Territory status update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/territories/[id]/status
 * Get territory status and assignment information
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { congregationId: true }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Get territory with current assignment and status information
    const territory = await prisma.territory.findFirst({
      where: {
        id: params.id,
        congregationId: member.congregationId
      },
      include: {
        currentAssignment: {
          include: {
            member: {
              select: {
                id: true,
                name: true,
                role: true
              }
            },
            assignedByMember: {
              select: {
                name: true
              }
            },
            visits: {
              orderBy: {
                visitDate: 'desc'
              },
              take: 5 // Last 5 visits
            }
          }
        }
      }
    });

    if (!territory) {
      return NextResponse.json(
        { error: 'Territory not found' },
        { status: 404 }
      );
    }

    // Calculate assignment statistics if there's an active assignment
    let assignmentStats = null;
    if (territory.currentAssignment) {
      const stats = await TerritoryStatusService.getAssignmentStatistics(
        territory.currentAssignment.id,
        member.congregationId
      );
      assignmentStats = stats.statistics;
    }

    return NextResponse.json({
      success: true,
      data: {
        territory: {
          id: territory.id,
          territoryNumber: territory.territoryNumber,
          address: territory.address,
          status: territory.status,
          notes: territory.notes,
          updatedAt: territory.updatedAt
        },
        currentAssignment: territory.currentAssignment,
        assignmentStatistics: assignmentStats
      }
    });

  } catch (error) {
    console.error('Territory status get error:', error);

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
