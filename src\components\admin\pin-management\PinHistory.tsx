/**
 * PIN History Component
 * 
 * Displays the PIN change history and audit trail with detailed
 * information about PIN operations and security events.
 */

import React from 'react';
import { PinChangeRecord } from '@/lib/services/pinService';

interface PinHistoryProps {
  history: PinChangeRecord[];
  memberName?: string;
  isLoading: boolean;
  onClose: () => void;
}

const getChangeTypeDisplayName = (changeType: string): string => {
  const changeTypes = {
    'generated': 'Generado',
    'reset': 'Restablecido',
    'updated': 'Actualizado',
    'expired': 'Expirado',
  };
  return changeTypes[changeType as keyof typeof changeTypes] || changeType;
};

const getChangeTypeColor = (changeType: string): string => {
  const changeColors = {
    'generated': 'bg-green-100 text-green-800',
    'reset': 'bg-orange-100 text-orange-800',
    'updated': 'bg-blue-100 text-blue-800',
    'expired': 'bg-red-100 text-red-800',
  };
  return changeColors[changeType as keyof typeof changeColors] || 'bg-gray-100 text-gray-800';
};

const getChangeTypeIcon = (changeType: string): string => {
  const changeIcons = {
    'generated': '🔑',
    'reset': '🔄',
    'updated': '✏️',
    'expired': '⏰',
  };
  return changeIcons[changeType as keyof typeof changeIcons] || '📝';
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(new Date(date));
};

const formatUserAgent = (userAgent: string | null): string => {
  if (!userAgent) return 'Desconocido';
  
  // Simple user agent parsing
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  
  return 'Navegador desconocido';
};

export default function PinHistory({
  history,
  memberName,
  isLoading,
  onClose,
}: PinHistoryProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              Historial de Cambios de PIN
              {memberName && (
                <span className="text-gray-600 font-normal"> - {memberName}</span>
              )}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Cargando historial...</span>
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No hay cambios de PIN registrados</p>
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((record, index) => (
                <div
                  key={record.id}
                  className="bg-gray-50 rounded-lg p-4 border border-gray-200"
                >
                  {/* Change Header */}
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getChangeTypeIcon(record.changeType)}</span>
                      <div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getChangeTypeColor(record.changeType)}`}>
                          {getChangeTypeDisplayName(record.changeType)}
                        </span>
                        {record.memberName && (
                          <span className="ml-2 text-sm text-gray-600">
                            Miembro: <span className="font-medium">{record.memberName}</span>
                          </span>
                        )}
                      </div>
                    </div>
                    <span className="text-xs text-gray-500">
                      #{index + 1}
                    </span>
                  </div>

                  {/* Change Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                    <div>
                      <span className="text-sm text-gray-600">Realizado por:</span>
                      <p className="text-sm font-medium text-gray-900">{record.changedByName}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Fecha y hora:</span>
                      <p className="text-sm font-medium text-gray-900">{formatDate(record.createdAt)}</p>
                    </div>
                  </div>

                  {/* Reason */}
                  {record.reason && (
                    <div className="mb-3">
                      <span className="text-sm text-gray-600">Razón:</span>
                      <p className="text-sm text-gray-800 mt-1 bg-white p-2 rounded border">
                        {record.reason}
                      </p>
                    </div>
                  )}

                  {/* Technical Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500">
                    <div>
                      <span className="text-gray-600">Dirección IP:</span>
                      <p className="font-mono">{record.ipAddress || 'No disponible'}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Navegador:</span>
                      <p>{formatUserAgent(record.userAgent)}</p>
                    </div>
                  </div>

                  {/* Security Notice for Sensitive Operations */}
                  {(record.changeType === 'reset' || record.changeType === 'generated') && (
                    <div className="mt-3 bg-yellow-50 border border-yellow-200 rounded p-2">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span className="text-xs text-yellow-800">
                          Operación de seguridad: Los PINs anteriores fueron invalidados
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {history.length > 0 && (
                <>
                  Mostrando {history.length} cambio{history.length !== 1 ? 's' : ''} de PIN
                  {memberName ? ` para ${memberName}` : ' de la congregación'}
                </>
              )}
            </div>
            <button
              onClick={onClose}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Cerrar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
