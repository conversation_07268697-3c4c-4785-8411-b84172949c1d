#!/usr/bin/env node

/**
 * Create Test Congregation Script
 * 
 * Creates a test congregation for manual testing of the login functionality.
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function createTestCongregation() {
  const prisma = new PrismaClient();

  try {
    await prisma.$connect();
    console.log('✅ Database connected');

    // Hash the PIN
    const hashedPin = await bcrypt.hash('test123', 12);

    // Create test congregation
    const congregation = await prisma.congregation.upsert({
      where: { id: 'TESTCONG' },
      update: {
        name: 'Congregación de Prueba',
        region: 'america-central',
        pin: hashedPin,
        language: 'es',
        timezone: 'America/Mexico_City',
        isActive: true,
      },
      create: {
        id: 'TESTCONG',
        name: 'Congregación de Prueba',
        region: 'america-central',
        pin: hashedPin,
        language: 'es',
        timezone: 'America/Mexico_City',
        isActive: true,
      },
    });

    console.log(`✅ Test congregation created: ${congregation.name} (${congregation.id})`);
    console.log('📝 Login credentials:');
    console.log('   Región: América Central (or leave empty)');
    console.log('   ID de Congregación: TESTCONG');
    console.log('   PIN: test123');

    // Create test member
    const hashedMemberPin = await bcrypt.hash('member123', 12);

    const member = await prisma.member.upsert({
      where: {
        congregationId_email: {
          congregationId: congregation.id,
          email: '<EMAIL>'
        }
      },
      update: {
        name: 'Anciano de Prueba',
        role: 'elder',
        pin: hashedMemberPin,
        isActive: true,
      },
      create: {
        congregationId: congregation.id,
        name: 'Anciano de Prueba',
        email: '<EMAIL>',
        role: 'elder',
        pin: hashedMemberPin,
        isActive: true,
      },
    });

    console.log(`✅ Test member created: ${member.name} (${member.role})`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createTestCongregation();
