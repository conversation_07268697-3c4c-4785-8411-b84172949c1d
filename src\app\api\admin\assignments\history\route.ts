/**
 * Assignment History API Endpoint
 * 
 * Provides access to assignment history and audit trail.
 * Only accessible to coordinator elders and elders with proper permissions.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { SectionAssignmentService } from '@/lib/services/sectionAssignmentService';

/**
 * GET - Retrieve assignment history for the congregation
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view assignment history' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : 50;

    // Validate limit
    if (isNaN(limit) || limit < 1 || limit > 200) {
      return NextResponse.json(
        { error: 'Invalid limit parameter. Must be between 1 and 200.' },
        { status: 400 }
      );
    }

    // Get assignment history for the congregation
    const history = await SectionAssignmentService.getAssignmentHistory(
      user.congregationId,
      limit
    );

    return NextResponse.json({
      success: true,
      history,
      count: history.length,
      limit,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Assignment history GET error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch assignment history',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
