'use client';

/**
 * Assignments Page
 *
 * Member assignment dashboard following the Asignaciones.png design.
 * Displays member's current and upcoming meeting part assignments.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface MeetingPartAssignment {
  id: string;
  meetingId: string;
  partType: string;
  title: string;
  assignedMember: string | null;
  assistant: string | null;
  timeAllocation: number | null;
  notes: string | null;
  isCompleted: boolean;
  meetingDate: Date;
  meetingType: 'midweek' | 'weekend';
  member?: {
    id: string;
    name: string;
    role: string;
  };
  assistantMember?: {
    id: string;
    name: string;
    role: string;
  };
}

export default function AssignmentsPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [assignments, setAssignments] = useState<MeetingPartAssignment[]>([]);
  const [activeFilter, setActiveFilter] = useState<string>('upcoming');

  useEffect(() => {
    checkAuthentication();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      fetchAssignments();
    }
  }, [user, activeFilter]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();
      setUser(data.member);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAssignments = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('hermanos_token');
      
      // Set date range based on filter
      const today = new Date();
      let startDate: string | undefined;
      let endDate: string | undefined;

      if (activeFilter === 'upcoming') {
        startDate = today.toISOString().split('T')[0];
        const futureDate = new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000); // 3 months
        endDate = futureDate.toISOString().split('T')[0];
      } else if (activeFilter === 'recent') {
        const pastDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000); // 3 months ago
        startDate = pastDate.toISOString().split('T')[0];
        endDate = today.toISOString().split('T')[0];
      }

      const params = new URLSearchParams({
        memberId: user.id,
        includeUnassigned: 'false',
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
      });
      
      const response = await fetch(`/api/assignments?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAssignments(data.assignments || []);
      }
    } catch (error) {
      console.error('Error fetching assignments:', error);
    }
  };

  const getPartTypeDisplayText = (partType: string): string => {
    const partTypeMap: Record<string, string> = {
      'song': 'Canción',
      'prayer': 'Oración',
      'treasures': 'Tesoros de la Palabra de Dios',
      'ministry': 'Seamos mejores maestros',
      'living': 'Nuestra vida cristiana',
      'bible_reading': 'Lectura de la Biblia',
      'initial_call': 'Primera conversación',
      'return_visit': 'Revisita',
      'bible_study': 'Curso bíblico',
      'talk': 'Discurso',
      'congregation_study': 'Estudio de la congregación',
      'public_talk': 'Discurso público',
      'watchtower': 'Estudio de La Atalaya',
      'opening_prayer': 'Oración inicial',
      'closing_prayer': 'Oración final',
      'chairman': 'Presidente',
      'reader': 'Lector',
    };

    return partTypeMap[partType] || partType;
  };

  const getMeetingTypeDisplayText = (meetingType: 'midweek' | 'weekend'): string => {
    return meetingType === 'midweek' ? 'Entre Semana' : 'Fin de Semana';
  };

  const formatDate = (dateString: string | Date): string => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (minutes: number | null): string => {
    if (!minutes) return '';
    return `${minutes} min`;
  };

  const isUpcoming = (assignment: MeetingPartAssignment): boolean => {
    const assignmentDate = new Date(assignment.meetingDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return assignmentDate >= today;
  };

  const filteredAssignments = assignments.filter(assignment => {
    if (activeFilter === 'upcoming') return isUpcoming(assignment);
    if (activeFilter === 'recent') return !isUpcoming(assignment);
    return true;
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-purple-600 text-white p-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-purple-200 hover:text-white mb-2 flex items-center"
            >
              ← Volver al Panel
            </button>
            <h1 className="text-2xl font-bold">Asignaciones</h1>
            <p className="text-purple-200">Gestiona tus asignaciones de reunión</p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-6">
        {/* Filter Tabs */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'upcoming', label: 'Próximas', count: assignments.filter(a => isUpcoming(a)).length },
              { key: 'recent', label: 'Recientes', count: assignments.filter(a => !isUpcoming(a)).length },
              { key: 'all', label: 'Todas', count: assignments.length },
            ].map(filter => (
              <button
                key={filter.key}
                onClick={() => setActiveFilter(filter.key)}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  activeFilter === filter.key
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>
        </div>

        {/* Assignments */}
        {filteredAssignments.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p className="text-gray-500 text-lg">No hay asignaciones {activeFilter === 'all' ? '' : activeFilter === 'upcoming' ? 'próximas' : 'recientes'}</p>
            <p className="text-gray-400">Las asignaciones aparecerán aquí cuando sean programadas</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAssignments.map((assignment) => (
              <div
                key={assignment.id}
                className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${
                  assignment.isCompleted ? 'border-green-500' :
                  isUpcoming(assignment) ? 'border-purple-500' :
                  'border-gray-400'
                }`}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{assignment.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        assignment.isCompleted ? 'bg-green-100 text-green-800' :
                        isUpcoming(assignment) ? 'bg-purple-100 text-purple-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {assignment.isCompleted ? 'Completado' : isUpcoming(assignment) ? 'Próximo' : 'Pasado'}
                      </span>
                    </div>
                    
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a2 2 0 012-2z" />
                        </svg>
                        {getPartTypeDisplayText(assignment.partType)}
                      </span>
                      
                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        {getMeetingTypeDisplayText(assignment.meetingType)}
                      </span>
                      
                      {assignment.timeAllocation && (
                        <span className="flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {formatTime(assignment.timeAllocation)}
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Fecha de reunión:</span>
                        <p className="text-gray-600">{formatDate(assignment.meetingDate)}</p>
                      </div>
                      
                      {assignment.assistant && assignment.assistantMember && (
                        <div>
                          <span className="font-medium text-gray-700">Ayudante:</span>
                          <p className="text-gray-600">{assignment.assistantMember.name}</p>
                        </div>
                      )}
                    </div>

                    {assignment.notes && (
                      <div className="mt-3 p-3 bg-blue-50 rounded-md">
                        <span className="font-medium text-blue-900">Notas:</span>
                        <p className="text-blue-800 text-sm mt-1">{assignment.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
