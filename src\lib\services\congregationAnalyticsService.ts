/**
 * Congregation Analytics Service
 *
 * Comprehensive analytics service that aggregates data from all Epic 4 systems
 * to provide congregation-wide insights, trends, and reporting capabilities.
 */

import { prisma } from '@/lib/prisma';
import { FieldServiceManagementService } from './fieldServiceManagementService';
import { TaskManagementService } from './taskManagementService';
import { AssignmentCoordinationService } from './assignmentCoordinationService';

export interface CongregationOverview {
  congregationId: string;
  congregationName: string;
  totalMembers: number;
  activeMembers: number;
  lastUpdated: Date;
  healthScore: number;
  alerts: Alert[];
  quickInsights: QuickInsight[];
}

export interface Alert {
  id: string;
  type: 'warning' | 'info' | 'critical';
  category: 'field_service' | 'tasks' | 'assignments' | 'meetings';
  title: string;
  description: string;
  actionRequired: boolean;
  createdAt: Date;
}

export interface QuickInsight {
  id: string;
  category: 'field_service' | 'tasks' | 'assignments' | 'meetings' | 'general';
  title: string;
  value: string | number;
  trend: 'up' | 'down' | 'stable';
  description: string;
}

export interface ActivityMetrics {
  fieldService: {
    currentMonthHours: number;
    previousMonthHours: number;
    averageHours: number;
    activePublishers: number;
    submissionRate: number;
    trend: 'up' | 'down' | 'stable';
  };
  tasks: {
    totalActiveTasks: number;
    completionRate: number;
    overdueCount: number;
    averageCompletionTime: number;
    memberParticipation: number;
    trend: 'up' | 'down' | 'stable';
  };
  assignments: {
    totalUpcomingAssignments: number;
    unassignedParts: number;
    assignmentRate: number;
    averageAssignmentsPerMember: number;
    conflictCount: number;
    trend: 'up' | 'down' | 'stable';
  };
  meetings: {
    attendanceRate: number;
    participationRate: number;
    assignmentDistribution: number;
    preparationQuality: number;
    trend: 'up' | 'down' | 'stable';
  };
}

export interface MemberEngagementProfile {
  memberId: string;
  memberName: string;
  memberRole: string;
  engagementScore: number;
  lastActivity: Date;
  activities: {
    fieldService: {
      lastReport: Date | null;
      averageHours: number;
      consistency: number;
    };
    tasks: {
      completedTasks: number;
      onTimeCompletion: number;
      averageRating: number;
    };
    assignments: {
      totalAssignments: number;
      completionRate: number;
      lastAssignment: Date | null;
    };
    meetings: {
      attendanceRate: number;
      participationCount: number;
      preparationQuality: number;
    };
  };
  recommendations: string[];
  needsAttention: boolean;
}

export interface TrendAnalysis {
  period: 'monthly' | 'quarterly' | 'yearly';
  startDate: Date;
  endDate: Date;
  metrics: {
    fieldService: Array<{
      period: string;
      hours: number;
      publishers: number;
      submissionRate: number;
    }>;
    tasks: Array<{
      period: string;
      completionRate: number;
      averageTime: number;
      memberParticipation: number;
    }>;
    assignments: Array<{
      period: string;
      assignmentRate: number;
      conflicts: number;
      distribution: number;
    }>;
    meetings: Array<{
      period: string;
      attendance: number;
      participation: number;
      quality: number;
    }>;
  };
  predictions: {
    nextPeriod: {
      fieldServiceHours: number;
      taskCompletion: number;
      assignmentNeeds: number;
      meetingAttendance: number;
    };
    confidence: number;
  };
}

export interface ExportData {
  reportType: string;
  generatedAt: Date;
  congregationId: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  data: {
    overview: CongregationOverview;
    metrics: ActivityMetrics;
    memberProfiles: MemberEngagementProfile[];
    trends: TrendAnalysis;
    rawData: {
      fieldService: unknown[];
      tasks: unknown[];
      assignments: unknown[];
      meetings: unknown[];
    };
  };
}

export class CongregationAnalyticsService {
  /**
   * Get comprehensive congregation overview with health metrics
   */
  static async getCongregationOverview(congregationId: string): Promise<CongregationOverview> {
    try {
      // Get basic congregation info
      const congregation = await prisma.congregation.findFirst({
        where: { id: congregationId },
        select: { id: true, name: true },
      });

      if (!congregation) {
        throw new Error('Congregation not found');
      }

      // Get member counts
      const [totalMembers, activeMembers] = await Promise.all([
        prisma.member.count({
          where: { congregationId },
        }),
        prisma.member.count({
          where: { congregationId, isActive: true },
        }),
      ]);

      // Get current month for analysis
      const currentDate = new Date();
      const currentMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

      // Get data from all systems
      const [fieldServiceStats, taskStats, assignmentStats] = await Promise.all([
        FieldServiceManagementService.calculateMonthlyStatistics(congregationId, currentMonth),
        TaskManagementService.getTaskStatistics(congregationId),
        AssignmentCoordinationService.getAssignmentStatistics(congregationId),
      ]);

      // Calculate health score (0-100)
      const healthScore = this.calculateHealthScore({
        fieldService: fieldServiceStats,
        tasks: taskStats,
        assignments: assignmentStats,
        totalMembers,
        activeMembers,
      });

      // Generate alerts
      const alerts = await this.generateAlerts(congregationId, {
        fieldService: fieldServiceStats,
        tasks: taskStats,
        assignments: assignmentStats,
      });

      // Generate quick insights
      const quickInsights = this.generateQuickInsights({
        fieldService: fieldServiceStats,
        tasks: taskStats,
        assignments: assignmentStats,
        totalMembers,
        activeMembers,
      });

      return {
        congregationId: congregation.id,
        congregationName: congregation.name,
        totalMembers,
        activeMembers,
        lastUpdated: new Date(),
        healthScore,
        alerts,
        quickInsights,
      };

    } catch (error) {
      console.error('Error getting congregation overview:', error);
      throw new Error('Failed to get congregation overview');
    }
  }

  /**
   * Get comprehensive activity metrics from all systems
   */
  static async getActivityMetrics(congregationId: string): Promise<ActivityMetrics> {
    try {
      const currentDate = new Date();
      const currentMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

      // Get previous month for comparison
      const previousDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
      const previousMonth = `${previousDate.getFullYear()}-${String(previousDate.getMonth() + 1).padStart(2, '0')}`;

      // Get data from all systems
      const [
        currentFieldService,
        previousFieldService,
        taskStats,
        assignmentStats
      ] = await Promise.all([
        FieldServiceManagementService.calculateMonthlyStatistics(congregationId, currentMonth),
        FieldServiceManagementService.calculateMonthlyStatistics(congregationId, previousMonth),
        TaskManagementService.getTaskStatistics(congregationId),
        AssignmentCoordinationService.getAssignmentStatistics(congregationId),
      ]);

      // Calculate trends
      const fieldServiceTrend = this.calculateTrend(
        currentFieldService.totalHours,
        previousFieldService.totalHours
      );

      const taskCompletionRate = taskStats.completedAssignments > 0
        ? (taskStats.completedAssignments / (taskStats.completedAssignments + taskStats.pendingAssignments + taskStats.inProgressAssignments)) * 100
        : 0;

      const assignmentRate = assignmentStats.totalUpcomingAssignments > 0
        ? ((assignmentStats.totalUpcomingAssignments - assignmentStats.unassignedParts) / assignmentStats.totalUpcomingAssignments) * 100
        : 0;

      return {
        fieldService: {
          currentMonthHours: currentFieldService.totalHours,
          previousMonthHours: previousFieldService.totalHours,
          averageHours: currentFieldService.averageHours,
          activePublishers: currentFieldService.activePublishers,
          submissionRate: currentFieldService.submittedReports > 0
            ? (currentFieldService.submittedReports / (currentFieldService.submittedReports + currentFieldService.pendingReports)) * 100
            : 0,
          trend: fieldServiceTrend,
        },
        tasks: {
          totalActiveTasks: taskStats.activeTasks,
          completionRate: taskCompletionRate,
          overdueCount: taskStats.overdueTasks,
          averageCompletionTime: 0, // Would need additional data tracking
          memberParticipation: taskStats.memberWorkload.length,
          trend: 'stable', // Would need historical data for trend
        },
        assignments: {
          totalUpcomingAssignments: assignmentStats.totalUpcomingAssignments,
          unassignedParts: assignmentStats.unassignedParts,
          assignmentRate,
          averageAssignmentsPerMember: assignmentStats.averageAssignmentsPerMember,
          conflictCount: 0, // Would need to call conflict detection
          trend: 'stable', // Would need historical data for trend
        },
        meetings: {
          attendanceRate: 85, // Placeholder - would need meeting attendance tracking
          participationRate: 78, // Placeholder - would need participation tracking
          assignmentDistribution: assignmentRate,
          preparationQuality: 82, // Placeholder - would need quality tracking
          trend: 'stable',
        },
      };

    } catch (error) {
      console.error('Error getting activity metrics:', error);
      throw new Error('Failed to get activity metrics');
    }
  }

  /**
   * Calculate congregation health score (0-100)
   */
  private static calculateHealthScore(data: {
    fieldService: {
      submittedReports: number;
      pendingReports: number;
    };
    tasks: {
      completedAssignments: number;
      pendingAssignments: number;
      inProgressAssignments: number;
    };
    assignments: {
      totalUpcomingAssignments: number;
      unassignedParts: number;
    };
    totalMembers: number;
    activeMembers: number;
  }): number {
    let score = 0;
    let maxScore = 0;

    // Field service health (25 points)
    const submissionRate = data.fieldService.submittedReports > 0
      ? (data.fieldService.submittedReports / (data.fieldService.submittedReports + data.fieldService.pendingReports)) * 100
      : 0;
    score += (submissionRate / 100) * 25;
    maxScore += 25;

    // Task completion health (25 points)
    const taskCompletionRate = data.tasks.completedAssignments > 0
      ? (data.tasks.completedAssignments / (data.tasks.completedAssignments + data.tasks.pendingAssignments + data.tasks.inProgressAssignments)) * 100
      : 0;
    score += (taskCompletionRate / 100) * 25;
    maxScore += 25;

    // Assignment health (25 points)
    const assignmentRate = data.assignments.totalUpcomingAssignments > 0
      ? ((data.assignments.totalUpcomingAssignments - data.assignments.unassignedParts) / data.assignments.totalUpcomingAssignments) * 100
      : 0;
    score += (assignmentRate / 100) * 25;
    maxScore += 25;

    // Member engagement health (25 points)
    const memberEngagement = data.activeMembers > 0 ? (data.activeMembers / data.totalMembers) * 100 : 0;
    score += (memberEngagement / 100) * 25;
    maxScore += 25;

    return Math.round((score / maxScore) * 100);
  }

  /**
   * Calculate trend direction
   */
  private static calculateTrend(current: number, previous: number): 'up' | 'down' | 'stable' {
    const threshold = 0.05; // 5% threshold for stability
    const change = (current - previous) / previous;

    if (Math.abs(change) < threshold) return 'stable';
    return change > 0 ? 'up' : 'down';
  }

  /**
   * Generate alerts based on congregation data
   */
  private static async generateAlerts(congregationId: string, data: {
    fieldService: {
      submittedReports: number;
      pendingReports: number;
    };
    tasks: {
      overdueTasks: number;
    };
    assignments: {
      unassignedParts: number;
    };
  }): Promise<Alert[]> {
    const alerts: Alert[] = [];

    // Field service alerts
    const submissionRate = data.fieldService.submittedReports > 0
      ? (data.fieldService.submittedReports / (data.fieldService.submittedReports + data.fieldService.pendingReports)) * 100
      : 0;

    if (submissionRate < 70) {
      alerts.push({
        id: `fs-submission-${Date.now()}`,
        type: 'warning',
        category: 'field_service',
        title: 'Baja tasa de entrega de informes',
        description: `Solo el ${Math.round(submissionRate)}% de los informes han sido entregados este mes`,
        actionRequired: true,
        createdAt: new Date(),
      });
    }

    if (data.fieldService.pendingReports > 10) {
      alerts.push({
        id: `fs-pending-${Date.now()}`,
        type: 'info',
        category: 'field_service',
        title: 'Informes pendientes',
        description: `${data.fieldService.pendingReports} hermanos aún no han entregado su informe`,
        actionRequired: true,
        createdAt: new Date(),
      });
    }

    // Task alerts
    if (data.tasks.overdueTasks > 0) {
      alerts.push({
        id: `task-overdue-${Date.now()}`,
        type: 'critical',
        category: 'tasks',
        title: 'Tareas vencidas',
        description: `${data.tasks.overdueTasks} tareas están vencidas y requieren atención`,
        actionRequired: true,
        createdAt: new Date(),
      });
    }

    // Assignment alerts
    if (data.assignments.unassignedParts > 5) {
      alerts.push({
        id: `assign-unassigned-${Date.now()}`,
        type: 'warning',
        category: 'assignments',
        title: 'Partes sin asignar',
        description: `${data.assignments.unassignedParts} partes de reunión necesitan ser asignadas`,
        actionRequired: true,
        createdAt: new Date(),
      });
    }

    return alerts;
  }

  /**
   * Generate quick insights
   */
  private static generateQuickInsights(data: {
    fieldService: {
      totalHours: number;
      activePublishers: number;
    };
    tasks: {
      completedAssignments: number;
      pendingAssignments: number;
      inProgressAssignments: number;
    };
    assignments: {
      totalUpcomingAssignments: number;
      unassignedParts: number;
    };
    totalMembers: number;
    activeMembers: number;
  }): QuickInsight[] {
    const insights: QuickInsight[] = [];

    // Field service insights
    insights.push({
      id: 'fs-hours',
      category: 'field_service',
      title: 'Horas de Servicio',
      value: data.fieldService.totalHours,
      trend: 'stable',
      description: 'Total de horas reportadas este mes',
    });

    insights.push({
      id: 'fs-publishers',
      category: 'field_service',
      title: 'Publicadores Activos',
      value: data.fieldService.activePublishers,
      trend: 'stable',
      description: 'Hermanos que reportaron servicio',
    });

    // Task insights
    const taskCompletionRate = data.tasks.completedAssignments > 0
      ? Math.round((data.tasks.completedAssignments / (data.tasks.completedAssignments + data.tasks.pendingAssignments + data.tasks.inProgressAssignments)) * 100)
      : 0;

    insights.push({
      id: 'task-completion',
      category: 'tasks',
      title: 'Tareas Completadas',
      value: `${taskCompletionRate}%`,
      trend: 'stable',
      description: 'Porcentaje de tareas completadas',
    });

    // Assignment insights
    const assignmentRate = data.assignments.totalUpcomingAssignments > 0
      ? Math.round(((data.assignments.totalUpcomingAssignments - data.assignments.unassignedParts) / data.assignments.totalUpcomingAssignments) * 100)
      : 0;

    insights.push({
      id: 'assign-rate',
      category: 'assignments',
      title: 'Asignaciones Cubiertas',
      value: `${assignmentRate}%`,
      trend: 'stable',
      description: 'Porcentaje de partes asignadas',
    });

    // Member engagement
    insights.push({
      id: 'member-active',
      category: 'general',
      title: 'Miembros Activos',
      value: data.activeMembers,
      trend: 'stable',
      description: 'Total de miembros activos',
    });

    return insights;
  }

  /**
   * Get member engagement profiles
   */
  static async getMemberEngagementProfiles(
    congregationId: string,
    options: {
      includeInactive?: boolean;
      limit?: number;
    } = {}
  ): Promise<MemberEngagementProfile[]> {
    try {
      const { includeInactive = false, limit = 50 } = options;

      // Get members
      const members = await prisma.member.findMany({
        where: {
          congregationId,
          ...(includeInactive === false && { isActive: true }),
        },
        select: {
          id: true,
          name: true,
          role: true,
          isActive: true,
        },
        take: limit,
        orderBy: { name: 'asc' },
      });

      const profiles: MemberEngagementProfile[] = [];

      for (const member of members) {
        // Get current month for field service
        const currentDate = new Date();

        // Get member's field service record
        const fieldServiceRecord = await prisma.fieldServiceRecord.findFirst({
          where: {
            congregationId,
            memberId: member.id,
            serviceMonth: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1),
          },
        });

        // Get member's task assignments
        const memberTasks = await TaskManagementService.getMemberTasks(congregationId, member.id);

        // Get member's meeting assignments
        const memberAssignments = await AssignmentCoordinationService.getMemberAssignmentSummary(
          congregationId,
          member.id
        );

        // Calculate engagement score (0-100)
        let engagementScore = 0;
        let maxScore = 0;

        // Field service engagement (25 points)
        if (fieldServiceRecord && fieldServiceRecord.isSubmitted) {
          engagementScore += 25;
        }
        maxScore += 25;

        // Task engagement (25 points)
        const completedTasks = memberTasks.filter(t => t.status === 'completed').length;
        const totalTasks = memberTasks.length;
        if (totalTasks > 0) {
          engagementScore += (completedTasks / totalTasks) * 25;
        }
        maxScore += 25;

        // Assignment engagement (25 points)
        if (memberAssignments.totalAssignments > 0) {
          engagementScore += 25;
        }
        maxScore += 25;

        // General activity (25 points)
        if (member.isActive) {
          engagementScore += 25;
        }
        maxScore += 25;

        const finalScore = maxScore > 0 ? Math.round((engagementScore / maxScore) * 100) : 0;

        // Generate recommendations
        const recommendations: string[] = [];
        let needsAttention = false;

        if (!fieldServiceRecord || !fieldServiceRecord.isSubmitted) {
          recommendations.push('Entregar informe de servicio del campo');
          needsAttention = true;
        }

        if (memberTasks.filter(t => t.status === 'pending').length > 3) {
          recommendations.push('Completar tareas pendientes');
          needsAttention = true;
        }

        if (memberAssignments.totalAssignments === 0) {
          recommendations.push('Considerar para asignaciones de reunión');
        }

        if (finalScore < 50) {
          needsAttention = true;
        }

        profiles.push({
          memberId: member.id,
          memberName: member.name,
          memberRole: member.role,
          engagementScore: finalScore,
          lastActivity: new Date(), // Would need to track actual last activity
          activities: {
            fieldService: {
              lastReport: fieldServiceRecord?.submittedAt || null,
              averageHours: fieldServiceRecord?.hours ? parseFloat(fieldServiceRecord.hours.toString()) : 0,
              consistency: fieldServiceRecord?.isSubmitted ? 100 : 0,
            },
            tasks: {
              completedTasks,
              onTimeCompletion: 100, // Would need due date tracking
              averageRating: 0, // Would need rating system
            },
            assignments: {
              totalAssignments: memberAssignments.totalAssignments,
              completionRate: 100, // Would need completion tracking
              lastAssignment: memberAssignments.lastAssignmentDate,
            },
            meetings: {
              attendanceRate: 85, // Placeholder - would need attendance tracking
              participationCount: memberAssignments.totalAssignments,
              preparationQuality: 80, // Placeholder - would need quality tracking
            },
          },
          recommendations,
          needsAttention,
        });
      }

      return profiles.sort((a, b) => {
        if (a.needsAttention && !b.needsAttention) return -1;
        if (!a.needsAttention && b.needsAttention) return 1;
        return b.engagementScore - a.engagementScore;
      });

    } catch (error) {
      console.error('Error getting member engagement profiles:', error);
      throw new Error('Failed to get member engagement profiles');
    }
  }

  /**
   * Generate comprehensive export data
   */
  static async generateExportData(
    congregationId: string,
    options: {
      reportType: string;
      startDate: Date;
      endDate: Date;
      includeMembers?: boolean;
    }
  ): Promise<ExportData> {
    try {
      const { reportType, startDate, endDate, includeMembers = true } = options;

      // Get all data
      const [overview, metrics, memberProfiles] = await Promise.all([
        this.getCongregationOverview(congregationId),
        this.getActivityMetrics(congregationId),
        includeMembers ? this.getMemberEngagementProfiles(congregationId) : [],
      ]);

      // Get raw data for the date range
      const [fieldServiceData, taskData, assignmentData] = await Promise.all([
        FieldServiceManagementService.getMonthlyServiceSummary(congregationId, `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`),
        TaskManagementService.getTaskStatistics(congregationId),
        AssignmentCoordinationService.getAssignmentStatistics(congregationId),
      ]);

      return {
        reportType,
        generatedAt: new Date(),
        congregationId,
        dateRange: { from: startDate, to: endDate },
        data: {
          overview,
          metrics,
          memberProfiles,
          trends: {
            period: 'monthly',
            startDate,
            endDate,
            metrics: {
              fieldService: [],
              tasks: [],
              assignments: [],
              meetings: [],
            },
            predictions: {
              nextPeriod: {
                fieldServiceHours: metrics.fieldService.currentMonthHours * 1.05,
                taskCompletion: metrics.tasks.completionRate,
                assignmentNeeds: metrics.assignments.totalUpcomingAssignments,
                meetingAttendance: metrics.meetings.attendanceRate,
              },
              confidence: 75,
            },
          },
          rawData: {
            fieldService: [fieldServiceData],
            tasks: [taskData],
            assignments: [assignmentData],
            meetings: [],
          },
        },
      };

    } catch (error) {
      console.error('Error generating export data:', error);
      throw new Error('Failed to generate export data');
    }
  }

  /**
   * Format date for display
   */
  static formatDate(date: Date): string {
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  /**
   * Format percentage for display
   */
  static formatPercentage(value: number): string {
    return `${Math.round(value)}%`;
  }

  /**
   * Get health score color
   */
  static getHealthScoreColor(score: number): string {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  }

  /**
   * Get trend icon
   */
  static getTrendIcon(trend: 'up' | 'down' | 'stable'): string {
    switch (trend) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      case 'stable': return '➡️';
      default: return '➡️';
    }
  }
}
