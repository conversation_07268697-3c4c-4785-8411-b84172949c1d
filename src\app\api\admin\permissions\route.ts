/**
 * Permission Delegation API Endpoint
 *
 * Handles CRUD operations for administrative permission delegation.
 * Implements story 2.1 requirements for granular permission management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PermissionDelegationService } from '@/lib/services/permissionDelegationService';
import { ADMINISTRATIVE_SECTIONS } from '@/lib/constants/administrativeSections';

// Validation schemas
const AssignPermissionsSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  sectionId: z.enum(Object.values(ADMINISTRATIVE_SECTIONS) as [string, ...string[]]),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
  expirationDate: z.string().datetime().optional(),
  notes: z.string().optional(),
  reason: z.string().optional(),
});

const RevokePermissionsSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  sectionId: z.enum(Object.values(ADMINISTRATIVE_SECTIONS) as [string, ...string[]]),
  permissions: z.array(z.string()).optional(),
  reason: z.string().optional(),
});

/**
 * GET - Retrieve permission assignments
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has permission to view assignments
    // Elders, coordinators, or congregation PIN holders can view assignments
    if (!['elder', 'coordinator'].includes(user.role) && !user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view permission assignments' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    let assignments;
    if (userId) {
      // Get permissions for specific user
      assignments = await PermissionDelegationService.getUserPermissions(
        user.congregationId,
        userId
      );
    } else {
      // Get all permission assignments for the congregation
      assignments = await PermissionDelegationService.getAllPermissionAssignments(
        user.congregationId
      );
    }

    return NextResponse.json({
      success: true,
      assignments,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Permission assignments GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve permission assignments',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Assign permissions to a user
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has permission to assign permissions (only coordinators or congregation PIN holders)
    if (user.role !== 'coordinator' && !user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Insufficient permissions to assign permissions. Only coordinators or congregation PIN holders can assign permissions.' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = AssignPermissionsSchema.parse(body);

    // Get client IP and user agent for audit logging
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Convert expiration date string to Date object if provided
    const assignmentRequest = {
      ...validatedData,
      expirationDate: validatedData.expirationDate ?
        new Date(validatedData.expirationDate) : undefined,
    };

    // Assign permissions
    const assignment = await PermissionDelegationService.assignPermissions(
      user.congregationId,
      assignmentRequest,
      {
        performedBy: user.userId,
        hasCongregationPinAccess: user.hasCongregationPinAccess,
        ipAddress,
        userAgent,
      }
    );

    return NextResponse.json({
      success: true,
      assignment,
      message: 'Permissions assigned successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Permission assignment POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to assign permissions',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE - Revoke permissions from a user
 */
export async function DELETE(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has permission to revoke permissions (only coordinators or congregation PIN holders)
    if (user.role !== 'coordinator' && !user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Insufficient permissions to revoke permissions. Only coordinators or congregation PIN holders can revoke permissions.' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = RevokePermissionsSchema.parse(body);

    // Get client IP and user agent for audit logging
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Revoke permissions
    await PermissionDelegationService.revokePermissions(
      user.congregationId,
      validatedData,
      {
        performedBy: user.userId,
        hasCongregationPinAccess: user.hasCongregationPinAccess,
        ipAddress,
        userAgent,
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Permissions revoked successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Permission revocation DELETE error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to revoke permissions',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to assign or DELETE to revoke permissions.' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to assign or DELETE to revoke permissions.' },
    { status: 405 }
  );
}
