/**
 * Permission Audit Log API Endpoint
 *
 * Provides access to permission change audit trail.
 * Only accessible to overseer/coordinator and developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PermissionDelegationService } from '@/lib/services/permissionDelegationService';
import { ADMINISTRATIVE_SECTIONS } from '@/lib/constants/administrativeSections';

// Validation schema for query parameters
const AuditQuerySchema = z.object({
  userId: z.string().optional(),
  sectionId: z.enum(Object.values(ADMINISTRATIVE_SECTIONS) as [string, ...string[]]).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

/**
 * GET - Retrieve permission audit log
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has permission to view audit logs (only coordinators or congregation PIN holders)
    if (user.role !== 'coordinator' && !user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view permission audit logs. Only coordinators or congregation PIN holders can view audit logs.' },
        { status: 403 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      userId: searchParams.get('userId') || undefined,
      sectionId: searchParams.get('sectionId') || undefined,
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined,
    };

    const validatedParams = AuditQuerySchema.parse(queryParams);

    // Build date range if provided
    let dateRange;
    if (validatedParams.startDate && validatedParams.endDate) {
      dateRange = {
        start: new Date(validatedParams.startDate),
        end: new Date(validatedParams.endDate),
      };
    } else if (validatedParams.startDate) {
      // If only start date provided, get from start date to now
      dateRange = {
        start: new Date(validatedParams.startDate),
        end: new Date(),
      };
    } else if (validatedParams.endDate) {
      // If only end date provided, get last 30 days up to end date
      const endDate = new Date(validatedParams.endDate);
      const startDate = new Date(endDate);
      startDate.setDate(startDate.getDate() - 30);
      dateRange = {
        start: startDate,
        end: endDate,
      };
    }

    // Get audit log entries
    const auditEntries = await PermissionDelegationService.getPermissionAuditLog(
      user.congregationId,
      validatedParams.userId,
      validatedParams.sectionId,
      dateRange
    );

    return NextResponse.json({
      success: true,
      auditEntries,
      filters: {
        userId: validatedParams.userId,
        sectionId: validatedParams.sectionId,
        dateRange,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Permission audit log GET error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to retrieve permission audit log',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Audit logs are read-only.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Audit logs are read-only.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Audit logs are read-only.' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Audit logs are read-only.' },
    { status: 405 }
  );
}
