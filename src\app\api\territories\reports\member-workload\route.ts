import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyToken } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { congregationId } = authResult;

    // Get members with territory assignments
    const membersWithAssignments = await prisma.member.findMany({
      where: {
        congregationId: congregationId,
        territoryAssignments: {
          some: {}
        }
      },
      include: {
        territoryAssignments: {
          include: {
            territory: {
              select: {
                territoryNumber: true
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    // Calculate workload statistics for each member
    const workloadData = membersWithAssignments.map(member => {
      const assignments = member.territoryAssignments;
      const activeAssignments = assignments.filter(a => a.status === 'active');
      const completedAssignments = assignments.filter(a => a.status === 'completed');

      // Calculate average days for completed assignments
      let averageDays = 0;
      if (completedAssignments.length > 0) {
        const totalDays = completedAssignments.reduce((sum, assignment) => {
          if (assignment.completedAt && assignment.assignedAt) {
            const days = Math.floor(
              (assignment.completedAt.getTime() - assignment.assignedAt.getTime()) / (1000 * 60 * 60 * 24)
            );
            return sum + days;
          }
          return sum;
        }, 0);
        averageDays = Math.round(totalDays / completedAssignments.length);
      }

      return {
        memberName: member.name,
        totalTerritories: assignments.length,
        activeTerritories: activeAssignments.length,
        completedTerritories: completedAssignments.length,
        averageDays: averageDays
      };
    });

    return NextResponse.json(workloadData);

  } catch (error) {
    console.error('Error fetching member workload report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
