/**
 * Authentication Module Index
 * 
 * Exports all authentication-related functionality for the Hermanos app
 */

// JWT functionality
export {
  SimpleJWTManager,
  type JWTPayload,
  type TokenOptions,
} from './simpleJWT';

// RBAC functionality
export {
  ROLES,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  isValidRole,
  hasPermission,
  validatePermissions,
  canDisableTokenExpiration,
  type PermissionCheck,
} from './simpleRBAC';

// Middleware functionality
export {
  withAuth,
  withAdminAuth,
  withElderAuth,
  withMemberManagementAuth,
  withCongregationIsolation,
  extractAndVerifyToken,
  validateUserPermissions,
  createAuthErrorResponse,
  isAuthenticated,
  type AuthenticatedRequest,
  type AuthOptions,
  type AuthResponse,
} from '../middleware/auth';
