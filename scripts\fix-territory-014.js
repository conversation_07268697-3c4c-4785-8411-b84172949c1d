const XLSX = require('xlsx');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Improved Parser for Territory 014 - Multiple Buildings
function parseTerritory014Buildings(data) {
  const addresses = [];
  const notes = [];
  let currentBuilding = '';
  let currentBuildingAddress = '';
  
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    if (!row || row.length === 0) continue;
    
    // Look for building indicators and building numbers
    for (let j = 0; j < row.length; j++) {
      const cell = row[j];
      if (!cell) continue;
      
      const cellStr = cell.toString().trim();
      
      // Detect building numbers (4-digit numbers that are building addresses)
      if (/^\d{4}$/.test(cellStr)) {
        const buildingNum = cellStr;
        
        // Check if this is a building address (not an apartment)
        // Building numbers are usually in specific positions and followed by apartment numbers
        let isBuilding = false;
        
        // Check if next rows have apartment-style numbers (1-3 digits)
        for (let k = i + 1; k < Math.min(i + 5, data.length); k++) {
          const nextRow = data[k];
          if (nextRow && nextRow[j] && /^\d{1,3}$/.test(nextRow[j].toString().trim())) {
            isBuilding = true;
            break;
          }
        }
        
        if (isBuilding) {
          currentBuildingAddress = `${buildingNum} FLAGLER ST, Miami, FL`;
          currentBuilding = `Building ${buildingNum}`;
          console.log(`Found building: ${currentBuildingAddress}`);
        }
      }
      
      // Look for apartment numbers (1-3 digits) when we have a current building
      if (currentBuildingAddress && /^\d{1,3}$/.test(cellStr) && cellStr.length <= 3) {
        const aptNum = cellStr;
        
        // Skip if this looks like a building number
        if (aptNum.length === 4) continue;
        
        const fullAddress = `${currentBuildingAddress} Apt ${aptNum}`;
        addresses.push(fullAddress);
        
        // Look for observations in column 7 (index 6)
        if (row[6] && row[6].toString().trim()) {
          const obs = row[6].toString().trim();
          if (obs !== 'Observaciones' && obs !== 'Edificio' && obs.length > 2 && !obs.includes('H-') && !obs.includes('T-')) {
            notes.push(`${fullAddress}: ${obs}`);
          }
        }
      }
    }
  }
  
  return { addresses, notes };
}

async function fixTerritory014() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  
  try {
    console.log(`🔧 Fixing Territory 014 with proper building structure...`);
    
    // Delete existing Territory 014
    await prisma.territory.deleteMany({
      where: {
        congregationId: '1441',
        territoryNumber: '014'
      }
    });
    console.log(`🗑️  Deleted existing Territory 014`);
    
    // Read and parse the Excel file
    const fileName = 'Terr. 014.xlsx';
    const filePath = path.join(territoriosDir, fileName);
    
    const workbook = XLSX.readFile(filePath);
    const sheet = workbook.Sheets['Terr 14'];
    const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    
    // Parse with improved building logic
    const result = parseTerritory014Buildings(data);
    
    if (result.addresses.length === 0) {
      console.log(`❌ No addresses found, aborting...`);
      return;
    }
    
    // Create new Territory 014 with proper building structure
    const territory = await prisma.territory.create({
      data: {
        congregationId: '1441',
        territoryNumber: '014',
        address: result.addresses.join('\n'),
        notes: result.notes.length > 0 ? result.notes.join('\n') : null,
        status: 'available',
        displayOrder: 14
      }
    });
    
    console.log(`✅ Recreated Territory 014 with ${result.addresses.length} addresses (multiple buildings)`);
    if (result.notes.length > 0) {
      console.log(`📝 ${result.notes.length} notes imported`);
    }
    
    // Show sample addresses
    console.log(`\n📋 Sample addresses:`);
    result.addresses.slice(0, 10).forEach((addr, idx) => {
      console.log(`  ${idx + 1}. ${addr}`);
    });
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  } finally {
    await prisma.$disconnect();
  }
}

fixTerritory014().catch(console.error);
