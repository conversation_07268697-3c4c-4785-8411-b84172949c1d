const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function testPin() {
  try {
    // Get the congregation
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });
    
    if (!congregation) {
      console.log('❌ Congregation 1441 not found');
      return;
    }
    
    console.log('✅ Found congregation:', congregation.name);
    console.log('📍 Region:', congregation.region);
    console.log('🔐 Stored PIN hash:', congregation.pin);
    
    // Test different PIN values
    const testPins = ['1930', '1441', 'coraloes123', 'admin', '123456'];
    
    console.log('\n🧪 Testing PIN values:');
    for (const testPin of testPins) {
      const isValid = await bcrypt.compare(testPin, congregation.pin);
      console.log(`   ${isValid ? '✅' : '❌'} PIN "${testPin}": ${isValid ? 'VALID' : 'Invalid'}`);
    }
    
    // Check if the stored PIN is actually a hash
    console.log('\n🔍 PIN Analysis:');
    console.log('   PIN starts with $2a$:', congregation.pin.startsWith('$2a$'));
    console.log('   PIN length:', congregation.pin.length);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testPin();
