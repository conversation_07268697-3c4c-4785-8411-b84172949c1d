'use client';

/**
 * Permissions Management Page
 *
 * Comprehensive permissions management interface matching the provided screenshot.
 * Allows coordinators and congregation PIN holders to assign granular permissions
 * to elders and ministerial servants across all administrative sections.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminFooter from '@/components/admin/AdminFooter';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface Member {
  id: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
}

interface PermissionSection {
  id: string;
  name: string;
  icon: string;
  permissions: string[];
  description: string;
}

interface MemberPermissions {
  [sectionId: string]: {
    [permissionType: string]: boolean;
  };
}

// Permission sections matching the screenshot
const PERMISSION_SECTIONS: PermissionSection[] = [
  {
    id: 'field_service',
    name: 'Field Service',
    icon: '🛡️',
    permissions: ['view', 'edit', 'manage'],
    description: 'Permissions for field service management'
  },
  {
    id: 'midweek_meeting',
    name: 'Midweek Meeting',
    icon: '👥',
    permissions: ['view', 'edit', 'assign'],
    description: 'Permissions for midweek meeting management'
  },
  {
    id: 'weekend_meeting',
    name: 'Weekend Meeting',
    icon: '🏛️',
    permissions: ['view', 'edit', 'assign'],
    description: 'Permissions for weekend meeting management'
  },
  {
    id: 'tasks',
    name: 'Tasks',
    icon: '📋',
    permissions: ['view', 'edit', 'assign'],
    description: 'Permissions for tasks management'
  },
  {
    id: 'members',
    name: 'Members',
    icon: '👤',
    permissions: ['view', 'edit', 'reset_pin'],
    description: 'Permissions for members management'
  },
  {
    id: 'letters',
    name: 'Letters',
    icon: '✉️',
    permissions: ['view', 'add', 'delete'],
    description: 'Permissions for letters management'
  },
  {
    id: 'programs',
    name: 'Programs',
    icon: '📺',
    permissions: ['view', 'edit', 'manage'],
    description: 'Permissions for programs management'
  },
  {
    id: 'events',
    name: 'Events',
    icon: '🏛️',
    permissions: ['view', 'edit', 'manage'],
    description: 'Permissions for events management'
  },
  {
    id: 'assignments',
    name: 'Assignments',
    icon: '📋',
    permissions: ['view', 'edit', 'assign'],
    description: 'Permissions for assignments management'
  },
  {
    id: 'congregation',
    name: 'Congregation',
    icon: '🏛️',
    permissions: ['view', 'edit'],
    description: 'Permissions for congregation management'
  },
  {
    id: 'database',
    name: 'Database',
    icon: '💾',
    permissions: ['backup', 'restore', 'manage'],
    description: 'Permissions for database management'
  },
  {
    id: 'permissions',
    name: 'Permissions',
    icon: '🔒',
    permissions: ['view', 'edit'],
    description: 'Permissions for permissions management'
  }
];

export default function PermissionsManagementPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [members, setMembers] = useState<Member[]>([]);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [memberPermissions, setMemberPermissions] = useState<MemberPermissions>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get token from localStorage
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        setError('Authentication required. Please log in again.');
        return;
      }

      // Get current user
      const userResponse = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!userResponse.ok) {
        throw new Error('Failed to get user information');
      }
      const userData = await userResponse.json();
      setUser(userData.user);

      // Check if user has permission to manage permissions
      if (!userData.user.hasCongregationPinAccess && userData.user.role !== 'coordinator') {
        setError('You do not have permission to manage permissions. Only coordinators and congregation PIN holders can access this page.');
        return;
      }

      // Get eligible members (elders and ministerial servants)
      const membersResponse = await fetch('/api/admin/members?roles=elder,ministerial_servant&active=true&limit=100', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!membersResponse.ok) {
        throw new Error('Failed to load members');
      }
      const membersData = await membersResponse.json();

      // Transform member data to match our interface
      const transformedMembers = (membersData.members || []).map((member: any) => ({
        id: member.id,
        firstName: member.name.split(' ')[0] || member.name,
        lastName: member.name.split(' ').slice(1).join(' ') || '',
        role: member.role,
        isActive: member.isActive
      }));

      setMembers(transformedMembers);

    } catch (error) {
      console.error('Error loading initial data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadMemberPermissions = async (memberId: string) => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/admin/permissions?userId=${memberId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load member permissions');
      }
      const data = await response.json();

      // Transform the permissions data to match our UI structure
      const permissions: MemberPermissions = {};
      PERMISSION_SECTIONS.forEach(section => {
        permissions[section.id] = {};
        section.permissions.forEach(permission => {
          permissions[section.id][permission] = false;
        });
      });

      // Apply existing permissions
      if (data.assignments) {
        data.assignments.forEach((assignment: any) => {
          if (permissions[assignment.sectionId]) {
            assignment.permissions.forEach((permission: string) => {
              permissions[assignment.sectionId][permission] = true;
            });
          }
        });
      }

      setMemberPermissions(permissions);
    } catch (error) {
      console.error('Error loading member permissions:', error);
      setError('Failed to load member permissions');
    }
  };

  const handleMemberSelect = async (member: Member) => {
    setSelectedMember(member);
    setError(null);
    setSuccessMessage(null);
    await loadMemberPermissions(member.id);
  };

  const handlePermissionChange = (sectionId: string, permissionType: string, granted: boolean) => {
    setMemberPermissions(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        [permissionType]: granted
      }
    }));
  };

  const handleSaveChanges = async () => {
    if (!selectedMember) return;

    try {
      setSaving(true);
      setError(null);
      setSuccessMessage(null);

      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Transform permissions data for bulk API
      const permissionsToSave: Record<string, { sectionId: string; permissions: string[] }> = {};

      Object.entries(memberPermissions).forEach(([sectionId, permissions]) => {
        const grantedPermissions = Object.entries(permissions)
          .filter(([_, granted]) => granted)
          .map(([permission, _]) => permission);

        permissionsToSave[sectionId] = {
          sectionId,
          permissions: grantedPermissions
        };
      });

      const response = await fetch('/api/admin/permissions/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          memberId: selectedMember.id,
          permissions: permissionsToSave,
          notes: `Permissions updated for ${selectedMember.firstName} ${selectedMember.lastName}`,
          reason: 'Bulk permission update via admin interface'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save permissions');
      }

      setSuccessMessage('Permissions saved successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);

    } catch (error) {
      console.error('Error saving permissions:', error);
      setError(error instanceof Error ? error.message : 'Failed to save permissions');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading permissions management...</p>
        </div>
      </div>
    );
  }

  if (error && !user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/admin')}
            className="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 transition-colors"
          >
            Return to Admin Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Matching the target design */}
      <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button
                  onClick={() => router.push('/admin')}
                  className="text-white hover:text-yellow-200 mr-4 flex items-center transition-colors"
                >
                  <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Permissions Management
                </button>
              </div>
              {selectedMember && (
                <button
                  onClick={handleSaveChanges}
                  disabled={saving}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                      </svg>
                      Save Changes
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Title Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-2xl font-bold text-gray-900">Assign Permissions to Members</h1>
          <p className="text-gray-600 mt-1">
            Use this page to assign specific permissions to Elders and Ministerial Servants.
          </p>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Error/Success Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-800">{successMessage}</p>
              </div>
            </div>
          </div>
        )}

        {/* Member Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Select Member:</h2>
            <div className="relative">
              <select
                value={selectedMember?.id || ''}
                onChange={(e) => {
                  const member = members.find(m => m.id === e.target.value);
                  if (member) handleMemberSelect(member);
                }}
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white text-gray-900"
              >
                <option value="">Choose a member...</option>
                {members.map(member => (
                  <option key={member.id} value={member.id}>
                    {member.firstName} {member.lastName} ({member.role === 'ministerial_servant' ? 'Ministerial Servant' : 'Elder'})
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Selected Member Info */}
        {selectedMember && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                {selectedMember.firstName.charAt(0)}{selectedMember.lastName.charAt(0)}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-blue-900">
                  {selectedMember.firstName} {selectedMember.lastName}
                </h3>
                <p className="text-blue-700">
                  {selectedMember.role === 'ministerial_servant' ? 'Ministerial Servant' : 'Elder'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Permissions Matrix - Always visible */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {PERMISSION_SECTIONS.map((section) => (
                <div key={section.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  {/* Section Header */}
                  <div className="flex items-center mb-4">
                    <div className={`w-8 h-8 rounded-md flex items-center justify-center text-white text-sm mr-3 ${
                      section.id === 'field_service' ? 'bg-blue-500' :
                      section.id === 'midweek_meeting' ? 'bg-blue-500' :
                      section.id === 'weekend_meeting' ? 'bg-blue-500' :
                      section.id === 'tasks' ? 'bg-blue-500' :
                      section.id === 'members' ? 'bg-blue-500' :
                      section.id === 'letters' ? 'bg-blue-500' :
                      section.id === 'programs' ? 'bg-blue-500' :
                      section.id === 'events' ? 'bg-blue-500' :
                      section.id === 'assignments' ? 'bg-blue-500' :
                      section.id === 'congregation' ? 'bg-blue-500' :
                      section.id === 'database' ? 'bg-blue-500' :
                      'bg-blue-500'
                    }`}>
                      {section.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 text-sm">{section.name}</h3>
                      <p className="text-xs text-gray-600">{section.description}</p>
                    </div>
                  </div>

                  {/* Permission Checkboxes */}
                  <div className="space-y-2">
                    {section.permissions.map((permission) => (
                      <label key={permission} className={`flex items-center ${!selectedMember ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}>
                        <input
                          type="checkbox"
                          checked={selectedMember ? (memberPermissions[section.id]?.[permission] || false) : false}
                          onChange={(e) => selectedMember && handlePermissionChange(section.id, permission, e.target.checked)}
                          disabled={!selectedMember}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                        />
                        <span className="ml-2 text-sm text-gray-700 capitalize">
                          {permission.replace('_', ' ')}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Instructions when no member selected */}
        {!selectedMember && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-semibold text-yellow-800">Select a Member</h3>
                <p className="text-sm text-yellow-700">
                  Choose an elder or ministerial servant from the dropdown above to manage their permissions.
                </p>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Admin Footer */}
      <AdminFooter currentSection="permissions" />
    </div>
  );
}
