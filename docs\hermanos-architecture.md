# Hermanos App Full-Stack Architecture Document

## Introduction

### Starter Template or Existing Project

This is **not a greenfield project**. The Hermanos app is being built as a modernization and expansion of the existing **Coral Oeste App**.

**Key Context:**
- **Reference Architecture**: The project explicitly states "Use Coral Oeste App as reference architecture for building the new 'Hermanos' multi-congregation system"
- **Existing Codebase**: There's a reference codebase at `C:\laragon\www\SalonDelReino\` that contains proven working implementation
- **UI Screenshots**: User has screenshots of existing app UI at `C:\laragon\www\HERMANOS\IMAGES OF OUR APP\` that should be replicated
- **Database Migration**: The project involves migrating from existing 41 MySQL tables to PostgreSQL
- **Preserved Logic**: Critical JW.org data fetching logic and URL patterns must be preserved exactly

**Architectural Constraints from Existing System:**
- Must preserve pixel-perfect UI compatibility with existing interface
- Must maintain all existing functionality without changes
- Must preserve existing authentication model and workflows
- Must preserve critical JW.org integration logic exactly as implemented
- Must support migration from MySQL/Node.js to PostgreSQL/Next.js stack

This is a **brownfield modernization project** rather than a greenfield development, with the primary goal being architectural modernization and multi-tenancy enablement while preserving all existing functionality.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-24 | 1.0 | Initial architecture document creation for Hermanos multi-congregation app | Architect |

## High Level Architecture

### Technical Summary

The Hermanos multi-congregation app will be built as a **monolithic Next.js 14+ application with multi-tenant PostgreSQL architecture**, deployed on local/self-hosted infrastructure with local file storage. The frontend leverages React with TypeScript and Tailwind CSS for pixel-perfect UI preservation, while the backend uses Next.js API routes with Prisma ORM for type-safe database operations. Multi-tenancy is achieved through congregation_id-based data isolation at the database level, enabling secure separation between congregations while maintaining a shared codebase. The architecture preserves all existing Coral Oeste functionality while modernizing from MySQL/Node.js to PostgreSQL/Next.js, with critical JW.org integration logic maintained exactly as implemented in the reference system.

### Platform and Infrastructure Choice

**Recommendation: Local/Self-Hosted Infrastructure**

Given the project's focus on preserving existing functionality while modernizing the stack, local/self-hosted infrastructure provides the optimal balance of control and existing infrastructure alignment. The local PostgreSQL and file storage approach maintains operational consistency.

**Platform:** Local/Self-hosted server
**Key Services:** Local PostgreSQL database, local file storage, PM2 process management
**Deployment Host and Regions:** Local server infrastructure with nginx reverse proxy

### Repository Structure

**Structure:** Monorepo with Next.js App Router
**Monorepo Tool:** npm workspaces (built-in, simpler than Turborepo for this scale)
**Package Organization:** Feature-based packages with shared utilities, types, and UI components

The monorepo structure enables shared code between different congregation instances while maintaining clear boundaries. The Next.js App Router provides excellent organization for the multi-tenant architecture with congregation-specific routing patterns.

### Architectural Patterns

- **Jamstack Architecture:** Static site generation with serverless APIs - _Rationale:_ Optimal performance and scalability for content-heavy applications with excellent mobile performance
- **Multi-Tenant SaaS Pattern:** Single application instance serving multiple congregations with data isolation - _Rationale:_ Enables efficient resource utilization while maintaining strict data separation between congregations
- **Component-Based UI:** Reusable React components with TypeScript - _Rationale:_ Maintainability and type safety across large codebases, essential for pixel-perfect UI preservation
- **Repository Pattern:** Abstract data access logic through Prisma ORM - _Rationale:_ Enables testing and future database migration flexibility while maintaining type safety
- **API Gateway Pattern:** Centralized API routing through Next.js API routes - _Rationale:_ Centralized auth, rate limiting, and monitoring with built-in Next.js optimization
- **Middleware Chain Pattern:** Layered request processing for auth, tenant isolation, and validation - _Rationale:_ Ensures consistent security and data isolation across all API endpoints
- **Event-Driven Updates:** Real-time updates for meeting assignments and notifications - _Rationale:_ Improves user experience for collaborative features like meeting management
- **Cache-Aside Pattern:** Strategic caching for JW.org data and frequently accessed congregation data - _Rationale:_ Reduces external API calls and improves response times for mobile users

## Tech Stack

This is the **DEFINITIVE technology selection** for the entire Hermanos project. All development must use these exact versions and technologies.

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.3+ | Type-safe frontend development | Essential for large codebase maintainability and preventing runtime errors in multi-tenant environment |
| Frontend Framework | Next.js | 14.1+ | React framework with SSR/SSG | App Router provides excellent multi-tenant routing, built-in optimization, and seamless deployment |
| UI Component Library | Tailwind CSS + Headless UI | 3.4+ / 1.7+ | Utility-first styling + accessible components | Enables pixel-perfect UI preservation while maintaining accessibility and mobile responsiveness |
| State Management | Zustand + React Query | 4.4+ / 5.17+ | Client state + server state management | Lightweight state management with excellent server state caching for multi-congregation data |
| Backend Language | TypeScript | 5.3+ | Type-safe backend development | Shared types between frontend/backend, essential for API contract safety |
| Backend Framework | Next.js API Routes | 14.1+ | Serverless API endpoints | Integrated with frontend, excellent optimization, simplified deployment |
| API Style | REST + tRPC | tRPC 10.45+ | Type-safe API layer | End-to-end type safety while maintaining REST compatibility for external integrations |
| Database | PostgreSQL (Local) | 15+ | Multi-tenant relational database | Superior multi-tenancy support, JSON capabilities, excellent Prisma integration |
| Cache | Redis (Local) | 7.0+ | Session and data caching | Local Redis for JW.org data caching and session management |
| File Storage | Local File System | Latest | PDF and image storage | Local filesystem for complete control and existing infrastructure alignment |
| Authentication | Custom JWT | Latest | User authentication and authorization | Custom JWT implementation with role-based access control |
| Frontend Testing | Vitest + React Testing Library | 1.2+ / 14.0+ | Unit and component testing | Fast test runner with excellent React component testing capabilities |
| Backend Testing | Vitest + Supertest | 1.2+ / 6.3+ | API endpoint testing | Consistent testing framework across frontend/backend |
| E2E Testing | Playwright | 1.40+ | End-to-end testing | Excellent mobile testing capabilities, critical for responsive design validation |
| Build Tool | Next.js built-in | 14.1+ | Compilation and bundling | Optimized for Next.js applications with excellent performance |
| Bundler | Turbopack (Next.js) | Latest | Fast development builds | Next.js integrated bundler for optimal development experience |
| IaC Tool | Local Scripts | Latest | Infrastructure management | Local deployment scripts for self-hosted infrastructure |
| CI/CD | GitHub Actions | Latest | Automated testing and deployment | Automated testing with local deployment integration |
| Monitoring | Winston + Local Logs | Latest | Performance and error monitoring | Local logging with structured JSON format |
| Logging | Winston + Pino | Latest / 8.17+ | Application logging | Structured logging with local file storage |
| CSS Framework | Tailwind CSS | 3.4+ | Utility-first styling | Essential for pixel-perfect UI preservation and rapid development |

**Additional Key Dependencies:**
- **Prisma ORM**: 5.8+ - Type-safe database operations with excellent PostgreSQL support
- **React Hook Form**: 7.48+ - Performant form handling with Zod validation
- **Zod**: 3.22+ - Runtime type validation for API endpoints and forms
- **bcryptjs**: 2.4+ - Password hashing for PIN authentication
- **jose**: 5.2+ - JWT token handling for authentication
- **date-fns**: 3.2+ - Date manipulation for meeting and service management

## Data Models

Based on the PRD requirements and the need to preserve the existing 41 MySQL tables while enabling multi-congregation support, here are the core data models that will be shared between frontend and backend.

### Congregation

**Purpose:** Central tenant entity that enables multi-congregation support and data isolation

**Key Attributes:**
- id: string - Unique congregation identifier (primary key)
- name: string - Display name of the congregation
- region: string - Geographic region for organization
- pin: string - Hashed congregation access PIN
- language: string - Default language (es, en, etc.)
- timezone: string - Congregation timezone for meeting scheduling
- settings: JSON - Congregation-specific configuration
- createdAt: Date - Registration timestamp
- updatedAt: Date - Last modification timestamp

#### TypeScript Interface
```typescript
interface Congregation {
  id: string;
  name: string;
  region: string;
  pin: string;
  language: 'es' | 'en' | string;
  timezone: string;
  settings: {
    theme: Record<string, string>;
    meetingLocation: 'kingdom_hall' | 'zoom';
    zoomDetails?: {
      meetingId: string;
      password: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- One-to-many with Members
- One-to-many with Meetings
- One-to-many with Tasks
- One-to-many with Letters

### Member

**Purpose:** User accounts with role-based access control and congregation association

**Key Attributes:**
- id: string - Unique member identifier
- congregationId: string - Foreign key to congregation
- name: string - Full member name
- email: string - Contact email (optional)
- pin: string - Hashed personal PIN for authentication
- role: enum - Access level (publisher, ministerial_servant, elder, overseer_coordinator, developer)
- serviceGroup: string - Service group assignment
- isActive: boolean - Account status
- lastLogin: Date - Last authentication timestamp

#### TypeScript Interface
```typescript
interface Member {
  id: string;
  congregationId: string;
  name: string;
  email?: string;
  pin: string;
  role: 'publisher' | 'ministerial_servant' | 'elder' | 'overseer_coordinator' | 'developer';
  serviceGroup?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- Many-to-one with Congregation
- One-to-many with FieldServiceRecords
- Many-to-many with MeetingParts (assignments)
- One-to-many with TaskAssignments

### Meeting

**Purpose:** Base entity for both midweek and weekend meetings with assignment tracking

**Key Attributes:**
- id: string - Unique meeting identifier
- congregationId: string - Tenant isolation
- type: enum - Meeting type (midweek, weekend)
- date: Date - Meeting date
- chairman: string - Meeting chairman member ID
- location: enum - Meeting location type
- zoomDetails: JSON - Virtual meeting information
- status: enum - Meeting status (scheduled, completed, cancelled)

#### TypeScript Interface
```typescript
interface Meeting {
  id: string;
  congregationId: string;
  type: 'midweek' | 'weekend';
  date: Date;
  chairman?: string;
  location: 'kingdom_hall' | 'zoom';
  zoomDetails?: {
    meetingId: string;
    password: string;
    url: string;
  };
  status: 'scheduled' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- Many-to-one with Congregation
- One-to-many with MeetingParts
- Many-to-one with Member (chairman)

### MeetingPart

**Purpose:** Individual meeting segments with member assignments and JW.org integration

**Key Attributes:**
- id: string - Unique part identifier
- meetingId: string - Parent meeting reference
- congregationId: string - Tenant isolation
- partType: string - Type of meeting part
- title: string - Part title from JW.org or custom
- duration: number - Duration in minutes
- assignedMemberId: string - Assigned member
- assistantId: string - Assistant member (if applicable)
- songNumber: number - Associated song number
- notes: string - Additional notes

#### TypeScript Interface
```typescript
interface MeetingPart {
  id: string;
  meetingId: string;
  congregationId: string;
  partType: 'treasures' | 'digging' | 'living' | 'public_talk' | 'watchtower';
  title: string;
  duration: number;
  assignedMemberId?: string;
  assistantId?: string;
  songNumber?: number;
  notes?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- Many-to-one with Meeting
- Many-to-one with Congregation
- Many-to-one with Member (assigned)
- Many-to-one with Member (assistant)

### Task

**Purpose:** Congregation task definitions with assignment and tracking capabilities

**Key Attributes:**
- id: string - Unique task identifier
- congregationId: string - Tenant isolation
- title: string - Task title
- description: string - Detailed description
- category: string - Task categorization
- frequency: enum - Recurrence pattern
- isActive: boolean - Task availability
- createdBy: string - Creator member ID

#### TypeScript Interface
```typescript
interface Task {
  id: string;
  congregationId: string;
  title: string;
  description: string;
  category: 'cleaning' | 'maintenance' | 'service' | 'meeting' | 'administrative';
  frequency: 'once' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- Many-to-one with Congregation
- Many-to-one with Member (creator)
- One-to-many with TaskAssignments

### FieldServiceRecord

**Purpose:** Individual service time tracking with monthly reporting capabilities

**Key Attributes:**
- id: string - Unique record identifier
- memberId: string - Member reference
- congregationId: string - Tenant isolation
- date: Date - Service date
- hours: number - Hours spent in service
- minutes: number - Additional minutes
- activityType: enum - Type of service activity
- notes: string - Optional notes
- serviceYear: number - Service year for reporting

#### TypeScript Interface
```typescript
interface FieldServiceRecord {
  id: string;
  memberId: string;
  congregationId: string;
  date: Date;
  hours: number;
  minutes: number;
  activityType: 'field_service' | 'return_visit' | 'bible_study' | 'public_witnessing';
  notes?: string;
  serviceYear: number;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- Many-to-one with Member
- Many-to-one with Congregation

### Letter

**Purpose:** Document management with categorization and access control

**Key Attributes:**
- id: string - Unique letter identifier
- congregationId: string - Tenant isolation
- title: string - Document title
- filename: string - Stored file name
- category: string - Document category
- visibility: enum - Access control level
- uploadedBy: string - Uploader member ID
- uploadDate: Date - Upload timestamp

#### TypeScript Interface
```typescript
interface Letter {
  id: string;
  congregationId: string;
  title: string;
  filename: string;
  category: 'announcement' | 'instruction' | 'form' | 'schedule' | 'other';
  visibility: 'public' | 'elders_only' | 'ms_and_elders';
  uploadedBy: string;
  uploadDate: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- Many-to-one with Congregation
- Many-to-one with Member (uploader)

## API Specification

Based on the chosen tRPC API style from the Tech Stack, here are the tRPC router definitions that enable end-to-end type safety while supporting all the functionality defined in the PRD epics.

### tRPC Router Definitions

```typescript
import { z } from 'zod';
import { router, publicProcedure, protectedProcedure, adminProcedure } from './trpc';

// Input validation schemas
const congregationLoginSchema = z.object({
  congregationId: z.string().min(1),
  pin: z.string().min(4),
});

const memberLoginSchema = z.object({
  congregationId: z.string().min(1),
  memberId: z.string().min(1),
  pin: z.string().min(4),
});

const createMemberSchema = z.object({
  name: z.string().min(1),
  email: z.string().email().optional(),
  role: z.enum(['publisher', 'ministerial_servant', 'elder', 'overseer_coordinator']),
  serviceGroup: z.string().optional(),
});

const meetingSchema = z.object({
  type: z.enum(['midweek', 'weekend']),
  date: z.date(),
  chairman: z.string().optional(),
  location: z.enum(['kingdom_hall', 'zoom']),
  zoomDetails: z.object({
    meetingId: z.string(),
    password: z.string(),
    url: z.string(),
  }).optional(),
});

const fieldServiceSchema = z.object({
  date: z.date(),
  hours: z.number().min(0),
  minutes: z.number().min(0).max(59),
  activityType: z.enum(['field_service', 'return_visit', 'bible_study', 'public_witnessing']),
  notes: z.string().optional(),
});

// Main application router
export const appRouter = router({
  // Authentication routes
  auth: router({
    congregationLogin: publicProcedure
      .input(congregationLoginSchema)
      .mutation(async ({ input, ctx }) => {
        // Authenticate congregation and return JWT token
        const { congregationId, pin } = input;
        const congregation = await ctx.db.congregation.findUnique({
          where: { id: congregationId }
        });

        if (!congregation || !await bcrypt.compare(pin, congregation.pin)) {
          throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Invalid credentials' });
        }

        const token = await signJWT({ congregationId, type: 'congregation' });
        return { token, congregation: { id: congregation.id, name: congregation.name } };
      }),

    memberLogin: publicProcedure
      .input(memberLoginSchema)
      .mutation(async ({ input, ctx }) => {
        // Authenticate member within congregation
        const { congregationId, memberId, pin } = input;
        const member = await ctx.db.member.findFirst({
          where: { id: memberId, congregationId },
          include: { congregation: true }
        });

        if (!member || !await bcrypt.compare(pin, member.pin)) {
          throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Invalid credentials' });
        }

        const token = await signJWT({
          memberId: member.id,
          congregationId,
          role: member.role
        });
        return { token, member, congregation: member.congregation };
      }),

    refreshToken: protectedProcedure
      .mutation(async ({ ctx }) => {
        // Refresh JWT token
        const token = await signJWT({
          memberId: ctx.user.memberId,
          congregationId: ctx.user.congregationId,
          role: ctx.user.role
        });
        return { token };
      }),
  }),

  // Member management routes
  members: router({
    list: protectedProcedure
      .query(async ({ ctx }) => {
        return await ctx.db.member.findMany({
          where: { congregationId: ctx.user.congregationId, isActive: true },
          select: { id: true, name: true, role: true, serviceGroup: true }
        });
      }),

    create: adminProcedure
      .input(createMemberSchema)
      .mutation(async ({ input, ctx }) => {
        const hashedPin = await bcrypt.hash(generateRandomPin(), 10);
        return await ctx.db.member.create({
          data: {
            ...input,
            congregationId: ctx.user.congregationId,
            pin: hashedPin,
            isActive: true
          }
        });
      }),

    update: adminProcedure
      .input(z.object({ id: z.string(), data: createMemberSchema.partial() }))
      .mutation(async ({ input, ctx }) => {
        return await ctx.db.member.update({
          where: {
            id: input.id,
            congregationId: ctx.user.congregationId
          },
          data: input.data
        });
      }),

    resetPin: adminProcedure
      .input(z.object({ memberId: z.string() }))
      .mutation(async ({ input, ctx }) => {
        const newPin = generateRandomPin();
        const hashedPin = await bcrypt.hash(newPin, 10);

        await ctx.db.member.update({
          where: {
            id: input.memberId,
            congregationId: ctx.user.congregationId
          },
          data: { pin: hashedPin }
        });

        return { newPin };
      }),
  }),

  // Meeting management routes
  meetings: router({
    list: protectedProcedure
      .input(z.object({
        type: z.enum(['midweek', 'weekend']).optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional()
      }))
      .query(async ({ input, ctx }) => {
        return await ctx.db.meeting.findMany({
          where: {
            congregationId: ctx.user.congregationId,
            type: input.type,
            date: {
              gte: input.startDate,
              lte: input.endDate
            }
          },
          include: {
            parts: {
              include: {
                assignedMember: { select: { id: true, name: true } },
                assistant: { select: { id: true, name: true } }
              }
            }
          },
          orderBy: { date: 'asc' }
        });
      }),

    create: adminProcedure
      .input(meetingSchema)
      .mutation(async ({ input, ctx }) => {
        return await ctx.db.meeting.create({
          data: {
            ...input,
            congregationId: ctx.user.congregationId,
            status: 'scheduled'
          }
        });
      }),

    fetchJwOrgData: adminProcedure
      .input(z.object({ date: z.date(), type: z.enum(['midweek', 'weekend']) }))
      .mutation(async ({ input, ctx }) => {
        // Preserve existing JW.org integration logic
        const jwOrgData = await fetchJwOrgMeetingData(input.date, input.type);
        return jwOrgData;
      }),

    assignPart: adminProcedure
      .input(z.object({
        partId: z.string(),
        memberId: z.string(),
        assistantId: z.string().optional()
      }))
      .mutation(async ({ input, ctx }) => {
        return await ctx.db.meetingPart.update({
          where: {
            id: input.partId,
            meeting: { congregationId: ctx.user.congregationId }
          },
          data: {
            assignedMemberId: input.memberId,
            assistantId: input.assistantId
          }
        });
      }),
  }),

  // Field service management routes
  fieldService: router({
    records: protectedProcedure
      .input(z.object({
        memberId: z.string().optional(),
        serviceYear: z.number().optional(),
        month: z.number().optional()
      }))
      .query(async ({ input, ctx }) => {
        const memberId = input.memberId || ctx.user.memberId;

        return await ctx.db.fieldServiceRecord.findMany({
          where: {
            memberId,
            congregationId: ctx.user.congregationId,
            serviceYear: input.serviceYear,
            date: input.month ? {
              gte: new Date(new Date().getFullYear(), input.month - 1, 1),
              lt: new Date(new Date().getFullYear(), input.month, 1)
            } : undefined
          },
          orderBy: { date: 'desc' }
        });
      }),

    create: protectedProcedure
      .input(fieldServiceSchema)
      .mutation(async ({ input, ctx }) => {
        return await ctx.db.fieldServiceRecord.create({
          data: {
            ...input,
            memberId: ctx.user.memberId,
            congregationId: ctx.user.congregationId,
            serviceYear: getServiceYear(input.date)
          }
        });
      }),

    monthlyReport: protectedProcedure
      .input(z.object({ month: z.number(), year: z.number() }))
      .query(async ({ input, ctx }) => {
        const records = await ctx.db.fieldServiceRecord.findMany({
          where: {
            memberId: ctx.user.memberId,
            congregationId: ctx.user.congregationId,
            date: {
              gte: new Date(input.year, input.month - 1, 1),
              lt: new Date(input.year, input.month, 1)
            }
          }
        });

        return {
          totalHours: records.reduce((sum, r) => sum + r.hours, 0),
          totalMinutes: records.reduce((sum, r) => sum + r.minutes, 0),
          activities: records.length,
          records
        };
      }),
  }),

  // External integrations
  external: router({
    jwOrg: router({
      fetchMeetingData: adminProcedure
        .input(z.object({ date: z.date(), language: z.string().default('es') }))
        .query(async ({ input }) => {
          // Preserve existing JW.org integration logic exactly
          return await fetchJwOrgMeetingData(input.date, input.language);
        }),

      fetchSongTitle: protectedProcedure
        .input(z.object({ songNumber: z.number(), language: z.string().default('es') }))
        .query(async ({ input }) => {
          return await fetchSongTitle(input.songNumber, input.language);
        }),
    }),
  }),
});

export type AppRouter = typeof appRouter;
```

## External APIs

### JW.org Data Scraping

- **Purpose:** Fetch Life and Ministry Meeting Workbook content using proven web scraping logic from existing wol-scraper.js implementation
- **Documentation:** No official API - uses web scraping with Puppeteer to extract meeting data from public JW.org pages
- **Base URL(s):** `https://wol.jw.org/es/wol/meetings/r4/lp-s` (Spanish), similar pattern for other languages
- **Authentication:** No authentication required - public data scraping
- **Rate Limits:** No official limits, but respectful scraping with caching implemented

**Key Endpoints Scraped:**
- `GET https://wol.jw.org/es/wol/meetings/r4/lp-s/{year}/{week}` - Weekly meeting content
- `GET https://wol.jw.org/es/wol/meetings/r4/lp-s/{year}` - Year overview for workbook discovery
- Dynamic song URLs extracted from meeting content for song title retrieval

**Integration Notes:**
- **Must preserve exact wol-scraper.js logic** - Copy the proven Puppeteer-based implementation that handles:
  - Browser automation with proper wait conditions
  - HTML parsing for meeting themes, scriptures, and parts
  - Song number extraction and dynamic title fetching
  - Robust error handling and fallback mechanisms
- **Alternative implementation**: Create MCP server using the same scraping logic for better modularity
- **Caching strategy**: Implement local caching to reduce scraping frequency and improve performance

**Technical Implementation Details from wol-scraper.js:**
- Uses Puppeteer for browser automation
- Extracts meeting themes using `.publicationMeetingTheme` selector
- Parses scripture citations with `.scriptureCitation` selector
- Handles dynamic song title fetching with fallback to generic titles
- Implements year/week URL pattern discovery
- Includes comprehensive error handling and browser cleanup

## Database Schema

Based on the data models defined earlier and the local PostgreSQL database configuration, here's the concrete database schema using Prisma ORM.

```sql
-- Hermanos Multi-Congregation Database Schema
-- PostgreSQL with Prisma ORM
-- Database: hermanos (localhost:5432)

-- Enable UUID extension for primary keys
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Congregations table - Central tenant entity
CREATE TABLE congregations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    region VARCHAR(100) NOT NULL,
    pin VARCHAR(255) NOT NULL, -- bcrypt hashed
    language VARCHAR(10) NOT NULL DEFAULT 'es',
    timezone VARCHAR(50) NOT NULL DEFAULT 'America/Mexico_City',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for performance
    CONSTRAINT congregations_name_region_unique UNIQUE (name, region)
);

-- Members table - User accounts with role-based access
CREATE TABLE members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    pin VARCHAR(255) NOT NULL, -- bcrypt hashed
    role VARCHAR(50) NOT NULL CHECK (role IN ('publisher', 'ministerial_servant', 'elder', 'overseer_coordinator', 'developer')),
    service_group VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for performance and tenant isolation
    INDEX idx_members_congregation_id (congregation_id),
    INDEX idx_members_role (role),
    INDEX idx_members_active (is_active),
    CONSTRAINT members_email_congregation_unique UNIQUE (email, congregation_id)
);

-- Meetings table - Base entity for midweek and weekend meetings
CREATE TABLE meetings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('midweek', 'weekend')),
    date DATE NOT NULL,
    chairman_id UUID REFERENCES members(id) ON DELETE SET NULL,
    location VARCHAR(20) NOT NULL DEFAULT 'kingdom_hall' CHECK (location IN ('kingdom_hall', 'zoom')),
    zoom_details JSONB,
    status VARCHAR(20) NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for performance and tenant isolation
    INDEX idx_meetings_congregation_id (congregation_id),
    INDEX idx_meetings_date (date),
    INDEX idx_meetings_type (type),
    CONSTRAINT meetings_congregation_date_type_unique UNIQUE (congregation_id, date, type)
);

-- Meeting parts table - Individual meeting segments with assignments
CREATE TABLE meeting_parts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    meeting_id UUID NOT NULL REFERENCES meetings(id) ON DELETE CASCADE,
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    part_type VARCHAR(50) NOT NULL CHECK (part_type IN ('treasures', 'digging', 'living', 'public_talk', 'watchtower')),
    title VARCHAR(500) NOT NULL,
    duration INTEGER NOT NULL DEFAULT 0, -- minutes
    assigned_member_id UUID REFERENCES members(id) ON DELETE SET NULL,
    assistant_id UUID REFERENCES members(id) ON DELETE SET NULL,
    song_number INTEGER,
    notes TEXT,
    display_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for performance and tenant isolation
    INDEX idx_meeting_parts_meeting_id (meeting_id),
    INDEX idx_meeting_parts_congregation_id (congregation_id),
    INDEX idx_meeting_parts_assigned_member (assigned_member_id),
    INDEX idx_meeting_parts_order (display_order)
);

-- Field service records table - Service time tracking
CREATE TABLE field_service_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    hours INTEGER NOT NULL DEFAULT 0,
    minutes INTEGER NOT NULL DEFAULT 0 CHECK (minutes >= 0 AND minutes < 60),
    activity_type VARCHAR(30) NOT NULL CHECK (activity_type IN ('field_service', 'return_visit', 'bible_study', 'public_witnessing')),
    notes TEXT,
    service_year INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for performance and tenant isolation
    INDEX idx_field_service_member_id (member_id),
    INDEX idx_field_service_congregation_id (congregation_id),
    INDEX idx_field_service_date (date),
    INDEX idx_field_service_year (service_year),
    CONSTRAINT field_service_member_date_unique UNIQUE (member_id, date)
);

-- Letters table - Document management
CREATE TABLE letters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    congregation_id UUID NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('announcement', 'instruction', 'form', 'schedule', 'other')),
    visibility VARCHAR(20) NOT NULL CHECK (visibility IN ('public', 'elders_only', 'ms_and_elders')),
    uploaded_by UUID NOT NULL REFERENCES members(id) ON DELETE RESTRICT,
    upload_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    file_size INTEGER,
    mime_type VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for performance and tenant isolation
    INDEX idx_letters_congregation_id (congregation_id),
    INDEX idx_letters_category (category),
    INDEX idx_letters_visibility (visibility),
    INDEX idx_letters_upload_date (upload_date)
);

-- Row Level Security (RLS) policies for multi-tenancy
ALTER TABLE members ENABLE ROW LEVEL SECURITY;
ALTER TABLE meetings ENABLE ROW LEVEL SECURITY;
ALTER TABLE meeting_parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE field_service_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE letters ENABLE ROW LEVEL SECURITY;

-- Example RLS policy for members table
CREATE POLICY members_congregation_isolation ON members
    FOR ALL
    USING (congregation_id = current_setting('app.current_congregation_id')::UUID);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_congregations_updated_at BEFORE UPDATE ON congregations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_members_updated_at BEFORE UPDATE ON members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meetings_updated_at BEFORE UPDATE ON meetings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meeting_parts_updated_at BEFORE UPDATE ON meeting_parts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_field_service_records_updated_at BEFORE UPDATE ON field_service_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_letters_updated_at BEFORE UPDATE ON letters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Unified Project Structure

Based on the chosen monorepo approach with npm workspaces and the local infrastructure requirements, here's the complete project structure for the Hermanos multi-congregation app:

```plaintext
hermanos/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml            # Continuous integration
│       ├── test.yaml          # Automated testing
│       └── deploy.yaml        # Deployment pipeline
├── apps/                       # Application packages
│   └── web/                    # Next.js frontend application
│       ├── src/
│       │   ├── app/            # Next.js App Router
│       │   │   ├── (auth)/     # Auth route group
│       │   │   │   ├── login/
│       │   │   │   │   └── page.tsx
│       │   │   │   └── layout.tsx
│       │   │   ├── (dashboard)/ # Protected dashboard routes
│       │   │   │   ├── dashboard/
│       │   │   │   │   └── page.tsx
│       │   │   │   ├── meetings/
│       │   │   │   │   ├── page.tsx
│       │   │   │   │   ├── [id]/
│       │   │   │   │   │   └── page.tsx
│       │   │   │   │   └── import/
│       │   │   │   │       └── page.tsx
│       │   │   │   ├── field-service/
│       │   │   │   │   ├── page.tsx
│       │   │   │   │   └── reports/
│       │   │   │   │       └── page.tsx
│       │   │   │   ├── tasks/
│       │   │   │   │   └── page.tsx
│       │   │   │   ├── letters/
│       │   │   │   │   └── page.tsx
│       │   │   │   ├── events/
│       │   │   │   │   └── page.tsx
│       │   │   │   ├── admin/
│       │   │   │   │   ├── page.tsx
│       │   │   │   │   ├── members/
│       │   │   │   │   │   └── page.tsx
│       │   │   │   │   └── settings/
│       │   │   │   │       └── page.tsx
│       │   │   │   └── layout.tsx
│       │   │   ├── api/         # API routes
│       │   │   │   ├── trpc/
│       │   │   │   │   └── [trpc]/
│       │   │   │   │       └── route.ts
│       │   │   │   ├── auth/
│       │   │   │   │   ├── login/
│       │   │   │   │   │   └── route.ts
│       │   │   │   │   └── refresh/
│       │   │   │   │       └── route.ts
│       │   │   │   ├── upload/
│       │   │   │   │   └── letters/
│       │   │   │   │       └── route.ts
│       │   │   │   └── health/
│       │   │   │       └── route.ts
│       │   │   ├── globals.css
│       │   │   ├── layout.tsx
│       │   │   └── page.tsx
│       │   ├── components/      # UI components
│       │   │   ├── ui/          # Base UI components
│       │   │   │   ├── button.tsx
│       │   │   │   ├── card.tsx
│       │   │   │   ├── form.tsx
│       │   │   │   └── input.tsx
│       │   │   ├── dashboard/   # Dashboard components
│       │   │   │   ├── dashboard-card.tsx
│       │   │   │   ├── dashboard-grid.tsx
│       │   │   │   └── welcome-header.tsx
│       │   │   ├── meetings/    # Meeting components
│       │   │   │   ├── meeting-list.tsx
│       │   │   │   ├── meeting-part-assignment.tsx
│       │   │   │   └── jw-org-import.tsx
│       │   │   ├── field-service/ # Field service components
│       │   │   │   ├── time-entry-form.tsx
│       │   │   │   ├── monthly-report.tsx
│       │   │   │   └── service-history.tsx
│       │   │   ├── auth/         # Auth components
│       │   │   │   ├── login-form.tsx
│       │   │   │   └── congregation-selector.tsx
│       │   │   └── layout/       # Layout components
│       │   │       ├── header.tsx
│       │   │       ├── navigation.tsx
│       │   │       └── mobile-nav.tsx
│       │   ├── hooks/           # Custom React hooks
│       │   │   ├── use-auth.ts
│       │   │   ├── use-congregation.ts
│       │   │   ├── use-meetings.ts
│       │   │   └── use-field-service.ts
│       │   ├── lib/             # Frontend utilities
│       │   │   ├── trpc.ts      # tRPC client setup
│       │   │   ├── auth.ts      # Auth utilities
│       │   │   ├── utils.ts     # General utilities
│       │   │   └── validations.ts # Form validations
│       │   ├── stores/          # Zustand stores
│       │   │   ├── auth-store.ts
│       │   │   ├── congregation-store.ts
│       │   │   ├── ui-store.ts
│       │   │   └── meeting-store.ts
│       │   └── styles/          # Global styles
│       │       ├── globals.css
│       │       └── components.css
│       ├── server/              # Backend services
│       │   ├── api/
│       │   │   ├── routers/     # tRPC routers
│       │   │   │   ├── auth.ts
│       │   │   │   ├── meetings.ts
│       │   │   │   ├── members.ts
│       │   │   │   ├── field-service.ts
│       │   │   │   ├── tasks.ts
│       │   │   │   ├── letters.ts
│       │   │   │   └── events.ts
│       │   │   ├── root.ts      # Main tRPC router
│       │   │   └── trpc.ts      # tRPC setup
│       │   ├── services/        # Business logic services
│       │   │   ├── auth-service.ts
│       │   │   ├── meeting-service.ts
│       │   │   ├── jw-org-service.ts
│       │   │   ├── file-service.ts
│       │   │   ├── member-service.ts
│       │   │   └── field-service-service.ts
│       │   ├── middleware/      # API middleware
│       │   │   ├── auth.ts
│       │   │   ├── tenant.ts
│       │   │   ├── validation.ts
│       │   │   └── error-handler.ts
│       │   ├── lib/             # Backend utilities
│       │   │   ├── db.ts        # Prisma client
│       │   │   ├── auth.ts      # JWT utilities
│       │   │   ├── cache.ts     # Redis cache client
│       │   │   ├── file-storage.ts # Local file operations
│       │   │   └── jw-org-scraper.ts # Preserved wol-scraper logic
│       │   └── types/           # Backend TypeScript types
│       │       ├── auth.ts
│       │       ├── meetings.ts
│       │       ├── api.ts
│       │       └── database.ts
│       ├── public/              # Static assets
│       │   ├── uploads/         # Local file storage
│       │   │   ├── letters/     # PDF documents
│       │   │   └── images/      # Image uploads
│       │   ├── icons/           # App icons
│       │   ├── images/          # Static images
│       │   │   └── ocean-bg.jpg # Dashboard background
│       │   └── favicon.ico
│       ├── prisma/              # Database schema and migrations
│       │   ├── schema.prisma    # Prisma schema definition
│       │   ├── migrations/      # Database migrations
│       │   │   └── 001_initial_schema/
│       │   │       └── migration.sql
│       │   └── seed.ts          # Database seeding
│       ├── tests/               # Application tests
│       │   ├── __mocks__/       # Test mocks
│       │   ├── components/      # Component tests
│       │   ├── api/             # API endpoint tests
│       │   ├── services/        # Service layer tests
│       │   ├── e2e/             # End-to-end tests
│       │   └── setup.ts         # Test setup
│       ├── .env.example         # Environment template
│       ├── .env.local           # Local environment (gitignored)
│       ├── next.config.js       # Next.js configuration
│       ├── tailwind.config.js   # Tailwind CSS configuration
│       ├── tsconfig.json        # TypeScript configuration
│       ├── package.json         # App dependencies
│       └── README.md            # App documentation
├── packages/                    # Shared packages
│   ├── shared/                  # Shared types and utilities
│   │   ├── src/
│   │   │   ├── types/           # Shared TypeScript interfaces
│   │   │   │   ├── congregation.ts
│   │   │   │   ├── member.ts
│   │   │   │   ├── meeting.ts
│   │   │   │   ├── field-service.ts
│   │   │   │   └── index.ts
│   │   │   ├── constants/       # Shared constants
│   │   │   │   ├── roles.ts
│   │   │   │   ├── meeting-types.ts
│   │   │   │   └── index.ts
│   │   │   ├── utils/           # Shared utilities
│   │   │   │   ├── date.ts
│   │   │   │   ├── validation.ts
│   │   │   │   └── index.ts
│   │   │   └── index.ts
│   │   ├── package.json
│   │   └── tsconfig.json
│   └── config/                  # Shared configuration
│       ├── eslint/
│       │   ├── base.js
│       │   ├── next.js
│       │   └── react.js
│       ├── typescript/
│       │   ├── base.json
│       │   ├── next.json
│       │   └── react.json
│       ├── tailwind/
│       │   └── base.js
│       └── jest/
│           └── base.js
├── scripts/                     # Build and utility scripts
│   ├── build.sh                # Build script
│   ├── dev.sh                  # Development startup
│   ├── test.sh                 # Test runner
│   ├── db-migrate.sh           # Database migration
│   ├── db-seed.sh              # Database seeding
│   ├── backup-db.sh            # Database backup
│   └── setup.sh                # Initial project setup
├── docs/                        # Project documentation
│   ├── prd.md                  # Product Requirements Document
│   ├── hermanos-architecture.md # This architecture document
│   ├── api/                    # API documentation
│   │   ├── authentication.md
│   │   ├── meetings.md
│   │   └── field-service.md
│   ├── deployment/             # Deployment guides
│   │   ├── local-setup.md
│   │   └── production.md
│   └── migration/              # Migration documentation
│       ├── mysql-to-postgres.md
│       └── data-migration.md
├── database/                    # Database setup and migrations
│   ├── init/                   # Initial database setup
│   │   ├── 001-create-database.sql
│   │   └── 002-create-extensions.sql
│   ├── migrations/             # Manual migrations (if needed)
│   └── backups/                # Database backup storage
├── .env.example                # Environment template
├── .env                        # Local environment (gitignored)
├── .gitignore                  # Git ignore rules
├── package.json                # Root package.json with workspaces
├── tsconfig.json               # Root TypeScript configuration
├── docker-compose.yml          # Local development services
├── Dockerfile                  # Production container (if needed)
└── README.md                   # Project documentation
```

## Security and Performance

### Security Requirements

**Frontend Security:**
- CSP Headers: `default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://wol.jw.org;`
- XSS Prevention: Input sanitization using DOMPurify, React's built-in XSS protection, and proper content encoding
- Secure Storage: JWT tokens stored in httpOnly cookies with secure and sameSite flags, sensitive data encrypted in localStorage

**Backend Security:**
- Input Validation: Zod schema validation on all API endpoints with comprehensive type checking and sanitization
- Rate Limiting: `express-rate-limit` with **1000 requests per 15 minutes per IP**, stricter limits for auth endpoints (100 requests per 15 minutes for login attempts)
- CORS Policy: `{ origin: process.env.FRONTEND_URL, credentials: true, methods: ['GET', 'POST', 'PUT', 'DELETE'] }`

**Authentication Security:**
- Token Storage: JWT tokens in httpOnly cookies with 60-day expiration, automatic refresh mechanism
- Session Management: Stateless JWT-based sessions with congregation and member context, secure logout clearing all tokens
- Password Policy: Minimum 4-digit PIN requirement with bcrypt hashing (12 rounds), account lockout after 5 failed attempts

### Performance Optimization

**Frontend Performance:**
- Bundle Size Target: < 500KB initial bundle, < 200KB per route chunk
- Loading Strategy: Next.js automatic code splitting, lazy loading for non-critical components, prefetching for likely navigation paths
- Caching Strategy: React Query with 5-minute stale time, service worker for offline functionality, browser caching for static assets

**Backend Performance:**
- Response Time Target: < 2 seconds for standard queries, < 10 seconds for complex reports and JW.org data fetching
- Database Optimization: Connection pooling (max 20 connections), proper indexing on congregation_id and frequently queried fields, query optimization with Prisma
- Caching Strategy: Redis caching for JW.org data (24-hour TTL), meeting data (1-hour TTL), and session data, in-memory caching for configuration data

## Coding Standards

### Critical Fullstack Rules

- **Database-First Testing**: Always use real database with sample data instead of mocks - seed comprehensive test data for existing midweek meetings, letters, and other entities
- **Preserve Existing Data**: Never delete or modify existing midweek meetings, letters, or other production data during development or testing
- **Multi-Tenant Data Isolation**: Every database query must include congregation_id filtering to ensure proper tenant isolation
- **Type Safety Enforcement**: All API calls must use tRPC procedures with Zod validation - never bypass type checking
- **Authentication Required**: All protected routes must use authentication middleware - never skip auth for convenience
- **JW.org Integration Preservation**: Always use the exact wol-scraper.js logic for JW.org data fetching - never create alternative implementations
- **Local Infrastructure Only**: Use local PostgreSQL database and local file storage - never assume cloud services
- **PIN Security**: Always use bcrypt for PIN hashing with 12 rounds - never store plain text PINs
- **Error Handling Consistency**: All API routes must use standardized error responses with proper HTTP status codes
- **Congregation Context**: Always pass congregation context through middleware - never hardcode congregation IDs

### Naming Conventions

| Element | Frontend | Backend | Example |
|---------|----------|---------|---------|
| Components | PascalCase | - | `DashboardCard.tsx` |
| Hooks | camelCase with 'use' | - | `useAuth.ts` |
| API Routes | - | kebab-case | `/api/midweek-meetings` |
| Database Tables | - | snake_case | `midweek_meetings` |
| tRPC Procedures | - | camelCase | `getMidweekMeetings` |
| Environment Variables | - | UPPER_SNAKE_CASE | `DATABASE_URL` |
| File Names | kebab-case | kebab-case | `meeting-service.ts` |
| Constants | UPPER_SNAKE_CASE | UPPER_SNAKE_CASE | `MAX_FILE_SIZE` |

## Monitoring and Logging (MoSCoW Prioritized)

### MoSCoW Prioritization

**MUST HAVE (MVP):**
- **Authentication Events**: Login attempts, token generation, congregation access logging
- **Database Operations**: Basic query logging, connection status, critical error tracking
- **API Requests**: Request/response logging with congregation context for debugging
- **Security Events**: Failed authentication, permission violations, suspicious activities
- **Health Checks**: Basic database connectivity and file system access verification

**SHOULD HAVE (Post-MVP):**
- **File Operations**: Upload/download activities, storage operations tracking
- **JW.org Integration**: Scraping attempts, data fetching results, caching operations
- **Business Metrics**: Meeting creation rates, field service entries, document uploads

**COULD HAVE (Future Enhancement):**
- **Performance Monitoring**: Response time tracking, memory usage, query optimization
- **Advanced Dashboards**: System overview, application metrics visualization
- **Automated Alerting**: Proactive notification systems, threshold-based alerts

**WON'T HAVE (This Release):**
- **Real-time Performance Dashboards**: Complex monitoring interfaces
- **Advanced Analytics**: Usage patterns, congregation activity analysis
- **External Monitoring Services**: Third-party monitoring tool integration

### Authentication Requirements (MUST HAVE)

**Congregation ID + PIN Login Logic:**
- **Preserve Existing Logic**: Use exact authentication flow from existing Coral Oeste system
- **PIN Generator**: Implement PIN generation system as shown in screenshots for new members
- **Congregation Context**: Maintain congregation-based authentication with proper tenant isolation
- **Security Logging**: Track all authentication attempts with congregation and member context

## Conclusion and Next Steps

### Architecture Summary

The Hermanos multi-congregation app architecture successfully modernizes the existing Coral Oeste system while preserving all critical functionality and user experience. The solution leverages **Next.js 14+ with local PostgreSQL** infrastructure, maintaining the proven patterns while enabling multi-tenant scalability.

**Key Architectural Achievements:**
- **Multi-Tenant Foundation**: PostgreSQL-based tenant isolation using congregation_id ensures secure data separation
- **Preserved Functionality**: All existing features maintained with pixel-perfect UI preservation
- **Technology Modernization**: Migration from MySQL/Node.js to PostgreSQL/Next.js with type safety throughout
- **Local Infrastructure**: Maintains control with local database and file storage, aligning with existing operational preferences
- **JW.org Integration**: Preserves exact wol-scraper.js logic for reliable meeting data fetching

### Implementation Roadmap

**Phase 1: Foundation (Weeks 1-4)**
- Epic 1: Foundation & Authentication Infrastructure
- Set up Next.js project with PostgreSQL and Prisma ORM
- Implement congregation ID + PIN authentication with PIN generator
- Establish multi-tenant database architecture with proper isolation

**Phase 2: Core Interface (Weeks 5-8)**
- Epic 2: Core Dashboard & Navigation
- Implement pixel-perfect dashboard with ocean background and color-coded cards
- Build role-based "Administración" button visibility
- Create responsive navigation system

**Phase 3: Member & Meeting Management (Weeks 9-16)**
- Epic 3: Member Management & Administrative Foundation
- Epic 4: Meeting Management System
- Implement comprehensive member CRUD operations
- Build meeting management with JW.org integration using preserved scraping logic

**Phase 4: Service & Task Management (Weeks 17-24)**
- Epic 5: Field Service & Task Management
- Implement field service time tracking and reporting
- Build task assignment system with service group support

**Phase 5: Communication & Documents (Weeks 25-28)**
- Epic 6: Communication & Document Management
- Implement letters management with local file storage
- Build events management and calendar integration

### Critical Success Factors

1. **Data Migration Strategy**: Successful migration from existing 41 MySQL tables to PostgreSQL without data loss
2. **UI Preservation**: Maintaining pixel-perfect compatibility with existing interface to ensure zero learning curve
3. **JW.org Integration**: Preserving exact wol-scraper.js logic to maintain reliable meeting data fetching
4. **Multi-Tenant Security**: Ensuring proper congregation data isolation throughout all system layers
5. **Performance Maintenance**: Achieving target response times (< 2s standard, < 10s complex) with local infrastructure

### Risk Mitigation

**Technical Risks:**
- **Database Migration**: Comprehensive testing with staging environment and rollback procedures
- **JW.org Integration**: Fallback mechanisms and caching strategies for external dependency reliability
- **Multi-Tenant Isolation**: Row-level security and application-level filtering with comprehensive testing

**Operational Risks:**
- **User Adoption**: Pixel-perfect UI preservation minimizes change management requirements
- **Data Security**: Robust authentication and authorization with audit logging
- **System Reliability**: Health monitoring and backup strategies for local infrastructure

### Immediate Next Steps

1. **Project Setup**: Initialize Next.js project with PostgreSQL connection and Prisma schema
2. **Database Migration**: Create migration scripts from existing MySQL schema to PostgreSQL
3. **Authentication Implementation**: Build congregation ID + PIN login with PIN generator functionality
4. **UI Component Library**: Create base components with exact color schemes and styling
5. **JW.org Service**: Implement wol-scraper.js logic preservation in new architecture

---

**Architecture Document Complete**

This comprehensive architecture document provides the foundation for building the Hermanos multi-congregation app. The design preserves all existing Coral Oeste functionality while enabling multi-congregation support through modern technology stack migration.

**Ready for Implementation**: The architecture is sufficiently detailed for development teams to begin implementation, with clear technical specifications, component boundaries, and integration patterns defined throughout all system layers.
