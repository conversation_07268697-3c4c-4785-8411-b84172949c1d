-- CreateTable
CREATE TABLE "special_songs" (
    "id" TEXT NOT NULL,
    "key_name" VARCHAR(50) NOT NULL,
    "title_es" VARCHAR(255) NOT NULL,
    "title_en" VARCHAR(255),
    "is_custom" BOOLEAN NOT NULL DEFAULT false,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "special_songs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "congregation_settings" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "setting_key" VARCHAR(100) NOT NULL,
    "setting_value" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "congregation_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_groups" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "group_number" INTEGER NOT NULL,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "service_groups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "territories" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" TEXT,
    "status" VARCHAR(20) NOT NULL DEFAULT 'available',
    "assigned_to_id" TEXT,
    "assigned_at" TIMESTAMPTZ,
    "completed_at" TIMESTAMPTZ,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "territories_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "special_songs_key_name_key" ON "special_songs"("key_name");

-- CreateIndex
CREATE INDEX "congregation_settings_congregation_id_setting_key_idx" ON "congregation_settings"("congregation_id", "setting_key");

-- CreateIndex
CREATE UNIQUE INDEX "congregation_settings_congregation_id_setting_key_key" ON "congregation_settings"("congregation_id", "setting_key");

-- CreateIndex
CREATE INDEX "service_groups_congregation_id_is_active_idx" ON "service_groups"("congregation_id", "is_active");

-- CreateIndex
CREATE UNIQUE INDEX "service_groups_congregation_id_group_number_key" ON "service_groups"("congregation_id", "group_number");

-- CreateIndex
CREATE INDEX "territories_congregation_id_status_idx" ON "territories"("congregation_id", "status");

-- CreateIndex
CREATE INDEX "territories_assigned_to_id_idx" ON "territories"("assigned_to_id");

-- AddForeignKey
ALTER TABLE "congregation_settings" ADD CONSTRAINT "congregation_settings_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_groups" ADD CONSTRAINT "service_groups_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "territories" ADD CONSTRAINT "territories_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "territories" ADD CONSTRAINT "territories_assigned_to_id_fkey" FOREIGN KEY ("assigned_to_id") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;
