# Introduction

## Starter Template or Existing Project

This is **not a greenfield project**. The Hermanos app is being built as a modernization and expansion of the existing **Coral Oeste App**.

**Key Context:**
- **Reference Architecture**: The project explicitly states "Use Coral Oeste App as reference architecture for building the new 'Hermanos' multi-congregation system"
- **Existing Codebase**: There's a reference codebase at `C:\laragon\www\SalonDelReino\` that contains proven working implementation
- **UI Screenshots**: User has screenshots of existing app UI at `C:\laragon\www\HERMANOS\IMAGES OF OUR APP\` that should be replicated
- **Database Migration**: The project involves migrating from existing 41 MySQL tables to PostgreSQL
- **Preserved Logic**: Critical JW.org data fetching logic and URL patterns must be preserved exactly

**Architectural Constraints from Existing System:**
- Must preserve pixel-perfect UI compatibility with existing interface
- Must maintain all existing functionality without changes
- Must preserve existing authentication model and workflows
- Must preserve critical JW.org integration logic exactly as implemented
- Must support migration from MySQL/Node.js to PostgreSQL/Next.js stack

This is a **brownfield modernization project** rather than a greenfield development, with the primary goal being architectural modernization and multi-tenancy enablement while preserving all existing functionality.

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-24 | 1.0 | Initial architecture document creation for Hermanos multi-congregation app | Architect |
