/**
 * Unified Authentication API Endpoint
 *
 * Handles both member-specific and congregation-level authentication for the Hermanos app.
 *
 * Authentication Flow:
 * 1. Check if PIN matches any member's personal PIN → Member-specific access (hasCongregationPinAccess: false)
 * 2. If no member PIN matches, check congregation PIN → Congregation-level access (hasCongregationPinAccess: true)
 *
 * This unified approach allows one login form to handle both authentication types
 * while maintaining distinct profile dropdown behaviors based on the access type.
 */

import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { SimpleJWTManager } from '@/lib/auth/simpleJWT';
import { isValidRole, canDisableTokenExpiration } from '@/lib/auth/simpleRBAC';

// Input validation schema
const loginSchema = z.object({
  region: z.string().optional(), // Region is optional for now
  congregationId: z.string().min(1, 'Congregation ID is required').max(8, 'Congregation ID too long'),
  pin: z.string().min(1, 'PIN is required'),
  rememberMe: z.boolean().optional().default(false),
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validationResult = loginSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid input data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { congregationId, pin, rememberMe } = validationResult.data;

    // Find congregation in database
    const congregation = await prisma.congregation.findUnique({
      where: {
        id: congregationId.toUpperCase(),
        isActive: true,
      },
    });

    if (!congregation) {
      return NextResponse.json(
        { error: 'Congregation not found or inactive' },
        { status: 404 }
      );
    }

    // Note: Region validation is optional for now
    // In the future, this could be used for region-specific features or validation
    // For now, we accept any region as long as the congregation exists

    // UNIFIED AUTHENTICATION: Check both member PINs and congregation PIN
    // Step 1: Try to find a member with matching PIN (member-specific access)
    let authenticatedMember = null;
    let hasCongregationPinAccess = false;

    // Get all active members for PIN checking
    const members = await prisma.member.findMany({
      where: {
        congregationId: congregation.id,
        isActive: true,
      },
    });

    if (members.length === 0) {
      return NextResponse.json(
        { error: 'No active members found in congregation' },
        { status: 404 }
      );
    }

    // Check if PIN matches any member's PIN
    for (const member of members) {
      let isMemberPinValid = false;

      if (member.pin && (member.pin.startsWith('$2a$') || member.pin.startsWith('$2b$'))) {
        // Member PIN is hashed with bcrypt
        isMemberPinValid = await bcrypt.compare(pin, member.pin);
      } else if (member.pin) {
        // Member PIN is stored as plain text (legacy format)
        isMemberPinValid = pin === member.pin;
      }

      if (isMemberPinValid) {
        authenticatedMember = member;
        hasCongregationPinAccess = false; // This is member-specific access
        break;
      }
    }

    // Step 2: If no member PIN matched, check congregation PIN (congregation-level access)
    if (!authenticatedMember) {
      let isCongregationPinValid = false;

      if (congregation.pin.startsWith('$2a$') || congregation.pin.startsWith('$2b$')) {
        // Congregation PIN is hashed with bcrypt
        isCongregationPinValid = await bcrypt.compare(pin, congregation.pin);
      } else {
        // Congregation PIN is stored as plain text (legacy format)
        isCongregationPinValid = pin === congregation.pin;
      }

      if (isCongregationPinValid) {
        // Use default member for congregation PIN access but flag as congregation access
        hasCongregationPinAccess = true;

        // Define role hierarchy for selecting default member
        const roleHierarchy: Record<string, number> = {
          'coordinator': 4,
          'elder': 3,
          'ministerial_servant': 2,
          'publisher': 1,
        };

        // Sort members by role hierarchy (highest first), then by creation date
        const sortedMembers = members.sort((a, b) => {
          const aRolePriority = roleHierarchy[a.role] || 0;
          const bRolePriority = roleHierarchy[b.role] || 0;

          if (aRolePriority !== bRolePriority) {
            return bRolePriority - aRolePriority; // Higher priority first
          }

          // If same role, sort by creation date (earliest first)
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });

        authenticatedMember = sortedMembers[0];
      } else {
        // Neither member PIN nor congregation PIN matched
        return NextResponse.json(
          { error: 'Invalid PIN' },
          { status: 401 }
        );
      }
    }

    // Validate authenticated member role
    if (!isValidRole(authenticatedMember.role)) {
      return NextResponse.json(
        { error: 'Invalid member role configuration' },
        { status: 500 }
      );
    }

    // Generate JWT token with appropriate access flag
    const tokenPayload = {
      userId: authenticatedMember.id,
      congregationId: congregation.id,
      role: authenticatedMember.role,
      name: authenticatedMember.name,
      hasCongregationPinAccess: hasCongregationPinAccess,
    };

    // Generate token based on access type and role
    const canExtendToken = canDisableTokenExpiration(authenticatedMember.role) && (rememberMe || hasCongregationPinAccess);
    const token = canExtendToken
      ? SimpleJWTManager.generateAdminToken(tokenPayload, true)
      : SimpleJWTManager.generateToken(tokenPayload);

    // Update member's last login
    await prisma.member.update({
      where: { id: authenticatedMember.id },
      data: { lastLogin: new Date() },
    });

    // Determine permissions based on access type and role
    const permissions = {
      canAccessAdmin: hasCongregationPinAccess || ['elder', 'coordinator', 'ministerial_servant'].includes(authenticatedMember.role),
      canManageSettings: hasCongregationPinAccess || ['elder', 'coordinator'].includes(authenticatedMember.role),
      canExtendToken: canExtendToken,
      hasCongregationPinAccess: hasCongregationPinAccess,
    };

    // Return successful authentication response with appropriate access flag
    return NextResponse.json({
      success: true,
      token,
      user: {
        id: authenticatedMember.id,
        name: authenticatedMember.name,
        role: authenticatedMember.role,
        congregationId: congregation.id,
        congregationName: congregation.name,
        hasCongregationPinAccess: hasCongregationPinAccess, // Key flag for profile dropdown behavior
      },
      congregation: {
        id: congregation.id,
        name: congregation.name,
        language: congregation.language,
        timezone: congregation.timezone,
      },
      permissions,
    });

  } catch (error) {
    console.error('Congregation login error:', error);

    // Don't expose internal errors to client
    return NextResponse.json(
      {
        error: 'Authentication failed',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
