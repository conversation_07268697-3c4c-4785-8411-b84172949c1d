const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkMemberRoles() {
  try {
    console.log('🔍 Checking member roles in congregation 1441...\n');

    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        isActive: true
      },
      select: {
        name: true,
        role: true,
        email: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // Group by role
    const roleGroups = {};
    members.forEach(member => {
      if (!roleGroups[member.role]) {
        roleGroups[member.role] = [];
      }
      roleGroups[member.role].push(member);
    });

    console.log('📊 Members by Role:');
    Object.keys(roleGroups).forEach(role => {
      console.log(`\n${role.toUpperCase()}:`);
      roleGroups[role].forEach(member => {
        console.log(`  - ${member.name} (${member.email})`);
      });
    });

    // Test the sorting logic
    console.log('\n🔄 Testing Role Hierarchy Sorting...');
    const roleHierarchy = {
      'coordinator': 4,
      'elder': 3,
      'ministerial_servant': 2,
      'publisher': 1,
    };

    const sortedMembers = members.sort((a, b) => {
      const aRolePriority = roleHierarchy[a.role] || 0;
      const bRolePriority = roleHierarchy[b.role] || 0;

      if (aRolePriority !== bRolePriority) {
        return bRolePriority - aRolePriority; // Higher priority first
      }

      // If same role, sort by creation date (earliest first)
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    });

    console.log('\n🎯 Top 5 members after hierarchy sorting:');
    sortedMembers.slice(0, 5).forEach((member, index) => {
      const priority = roleHierarchy[member.role] || 0;
      console.log(`  ${index + 1}. ${member.name} (${member.role}) - Priority: ${priority}`);
    });

    if (sortedMembers.length > 0) {
      console.log(`\n✅ Default member for congregation PIN: ${sortedMembers[0].name} (${sortedMembers[0].role})`);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMemberRoles();
