const XLSX = require('xlsx');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Parser 1: Original format (street in col 2, numbers in col 2, observations in col 7)
function parseFormat1(data) {
  const addresses = [];
  const notes = [];
  let currentStreet = '';
  
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    if (!row || row.length === 0) continue;
    
    // Check for street names in second column
    const secondCell = row[1] ? row[1].toString().trim() : '';
    if (secondCell && /\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(secondCell)) {
      currentStreet = secondCell;
      continue;
    }
    
    // Check for house numbers in second column
    if (!row[0] && row[1] !== undefined && row[1] !== null && row[1] !== '') {
      const cellValue = row[1].toString().trim();
      if (/^\d+[A-Z]?$/i.test(cellValue) && cellValue !== 'No. de Casa') {
        const houseNumber = cellValue;
        let observaciones = '';
        
        // Look for observations in column 7 (index 6)
        if (row[6] && row[6].toString().trim()) {
          const obs = row[6].toString().trim();
          if (obs !== 'Observaciones' && obs !== 'NO CAMBIAR ESTAS LETRAS') {
            observaciones = obs;
          }
        }
        
        if (currentStreet) {
          const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL`;
          addresses.push(fullAddress);
          if (observaciones) {
            notes.push(`${fullAddress}: ${observaciones}`);
          }
        }
      }
    }
  }
  
  return { addresses, notes };
}

// Parser 2: Street in col 4, numbers in col 2
function parseFormat2(data) {
  const addresses = [];
  const notes = [];
  let currentStreet = '';
  
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    if (!row || row.length === 0) continue;
    
    // Check for street names in fourth column
    const fourthCell = row[3] ? row[3].toString().trim() : '';
    if (fourthCell && /\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(fourthCell)) {
      currentStreet = fourthCell;
      continue;
    }
    
    // Check for house numbers in second column
    if (row[1] !== undefined && row[1] !== null && row[1] !== '') {
      const cellValue = row[1].toString().trim();
      if (/^\d+[A-Z]?$/i.test(cellValue) && cellValue !== 'No. de Casa' && cellValue.length < 6) {
        const houseNumber = cellValue;
        
        if (currentStreet) {
          const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL`;
          addresses.push(fullAddress);
        }
      }
    }
  }
  
  return { addresses, notes };
}

// Parser 3: Street in col 1, numbers in col 1, observations in col 6
function parseFormat3(data) {
  const addresses = [];
  const notes = [];
  let currentStreet = '';
  
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    if (!row || row.length === 0) continue;
    
    const firstCell = row[0] ? row[0].toString().trim() : '';
    
    // Check for street names in first column
    if (firstCell && /\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(firstCell)) {
      currentStreet = firstCell;
      continue;
    }
    
    // Check for house numbers in first column
    if (firstCell && /^\d+[A-Z]?$/i.test(firstCell) && firstCell !== 'No. de Casa' && firstCell.length < 6) {
      const houseNumber = firstCell;
      let observaciones = '';
      
      // Look for observations in column 6 (index 5)
      if (row[5] && row[5].toString().trim()) {
        const obs = row[5].toString().trim();
        if (obs !== 'Observaciones' && obs !== 'NO CAMBIAR ESTAS LETRAS') {
          observaciones = obs;
        }
      }
      
      if (currentStreet) {
        const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL`;
        addresses.push(fullAddress);
        if (observaciones) {
          notes.push(`${fullAddress}: ${observaciones}`);
        }
      }
    }
  }
  
  return { addresses, notes };
}

// Parser 4: Different layout format
function parseFormat4(data) {
  const addresses = [];
  const notes = [];
  let currentStreet = '';
  
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    if (!row || row.length === 0) continue;
    
    const firstCell = row[0] ? row[0].toString().trim() : '';
    
    // Check for street names (standalone rows)
    if (firstCell && /\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(firstCell) && !row[1]) {
      currentStreet = firstCell;
      continue;
    }
    
    // Check for house numbers
    if (firstCell && /^\d+[A-Z]?$/i.test(firstCell) && firstCell.length < 6) {
      const houseNumber = firstCell;
      let observaciones = '';
      
      // Look for observations in later columns
      for (let j = 1; j < row.length; j++) {
        if (row[j] && row[j].toString().trim()) {
          const obs = row[j].toString().trim();
          if (obs !== 'Observaciones' && obs !== 'NO CAMBIAR ESTAS LETRAS' && obs.length > 1) {
            observaciones = obs;
            break;
          }
        }
      }
      
      if (currentStreet) {
        const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL`;
        addresses.push(fullAddress);
        if (observaciones) {
          notes.push(`${fullAddress}: ${observaciones}`);
        }
      }
    }
  }
  
  return { addresses, notes };
}

async function importWithMultipleParsers() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  
  // Missing territories to try with multiple parsers
  const missingTerritories = ['014', '027', '032', '038', '040', '045', '046', '047', '051', '053', '055', '058', '065', '068', '069', '074', '075', '082', '086', '096', '098', '102', '103'];
  
  console.log(`🚀 Trying multiple parsers on ${missingTerritories.length} territories...\n`);
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const number of missingTerritories) {
    try {
      console.log(`📋 Processing Territory ${number}...`);
      
      const fileName = `Terr. ${number}.xlsx`;
      const filePath = path.join(territoriosDir, fileName);
      
      if (!fs.existsSync(filePath)) {
        console.log(`  ❌ File not found: ${fileName}`);
        continue;
      }
      
      const workbook = XLSX.readFile(filePath);
      const territorySheet = workbook.SheetNames.find(name => 
        name.toLowerCase().includes('terr') && !name.toLowerCase().includes('mapa')
      ) || workbook.SheetNames[0];
      
      const sheet = workbook.Sheets[territorySheet];
      const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      
      // Try all parsers
      const parsers = [parseFormat1, parseFormat2, parseFormat3, parseFormat4];
      let bestResult = { addresses: [], notes: [] };
      let bestParser = 0;
      
      for (let i = 0; i < parsers.length; i++) {
        const result = parsers[i](data);
        if (result.addresses.length > bestResult.addresses.length) {
          bestResult = result;
          bestParser = i + 1;
        }
      }
      
      if (bestResult.addresses.length === 0) {
        console.log(`  ⚠️  No addresses found with any parser, skipping...`);
        continue;
      }
      
      // Create territory record
      const territory = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: number,
          address: bestResult.addresses.join('\n'),
          notes: bestResult.notes.length > 0 ? bestResult.notes.join('\n') : null,
          status: 'available',
          displayOrder: parseInt(number)
        }
      });
      
      console.log(`  ✅ Created territory ${number} with ${bestResult.addresses.length} addresses (Parser ${bestParser})`);
      if (bestResult.notes.length > 0) {
        console.log(`     📝 ${bestResult.notes.length} notes imported`);
      }
      
      successCount++;
      
    } catch (error) {
      console.log(`  ❌ Error processing territory ${number}: ${error.message}`);
      errorCount++;
    }
  }
  
  console.log(`\n🎉 Multi-parser import completed!`);
  console.log(`✅ Successfully imported: ${successCount} territories`);
  console.log(`❌ Errors: ${errorCount} territories`);
  
  await prisma.$disconnect();
}

importWithMultipleParsers().catch(console.error);
