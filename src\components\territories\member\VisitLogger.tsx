'use client';

import React, { useState } from 'react';

interface VisitLoggerProps {
  assignmentId: string;
  territoryNumber: string;
  onVisitLogged?: () => void;
  onClose?: () => void;
}

export default function VisitLogger({
  assignmentId,
  territoryNumber,
  onVisitLogged,
  onClose
}: VisitLoggerProps) {
  const [visitDate, setVisitDate] = useState(new Date().toISOString().split('T')[0]);
  const [isCompleted, setIsCompleted] = useState(false);
  const [notes, setNotes] = useState('');
  const [addressesWorked, setAddressesWorked] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/territories/assignments/${assignmentId}/visits`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          visitDate: new Date(visitDate).toISOString(),
          isCompleted,
          notes: notes.trim() || undefined,
          addressesWorked: addressesWorked.trim() || undefined
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al registrar la visita');
      }

      const result = await response.json();

      // Show success message
      alert(result.data.message || 'Visita registrada exitosamente');

      // Notify parent component
      onVisitLogged?.();

      // Reset form or close
      if (isCompleted) {
        onClose?.();
      } else {
        // Reset form for another visit
        setNotes('');
        setAddressesWorked('');
        setVisitDate(new Date().toISOString().split('T')[0]);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Registrar Visita
            </h2>
            <p className="text-sm text-gray-600">
              Territorio {territoryNumber}
            </p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={loading}
            >
              ✕
            </button>
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Visit Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fecha de Visita *
            </label>
            <input
              type="date"
              value={visitDate}
              onChange={(e) => setVisitDate(e.target.value)}
              max={new Date().toISOString().split('T')[0]}
              required
              disabled={loading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Completion Status */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isCompleted}
                onChange={(e) => setIsCompleted(e.target.checked)}
                disabled={loading}
                className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm font-medium text-gray-700">
                Territorio completado en esta visita
              </span>
            </label>
            <p className="text-xs text-gray-500 mt-1 ml-7">
              Marcar solo si se completó todo el territorio en esta visita
            </p>
          </div>

          {/* Addresses Worked */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Direcciones Trabajadas
            </label>
            <textarea
              value={addressesWorked}
              onChange={(e) => setAddressesWorked(e.target.value)}
              placeholder="Ej: Calles 1-15, Avenida Principal 100-200, etc."
              rows={3}
              disabled={loading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              Especifica qué direcciones o áreas trabajaste en esta visita
            </p>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notas de la Visita
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Notas sobre la visita, personas contactadas, observaciones, etc."
              rows={4}
              disabled={loading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="text-red-600 text-sm">
                ⚠️ {error}
              </div>
            </div>
          )}

          {/* Completion Warning */}
          {isCompleted && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="text-yellow-800 text-sm">
                <strong>⚠️ Atención:</strong> Al marcar como completado, el territorio se marcará como disponible para reasignación.
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              type="submit"
              disabled={loading}
              className={`flex-1 py-2 px-4 rounded-md font-medium transition-colors ${
                loading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Registrando...
                </div>
              ) : (
                'Registrar Visita'
              )}
            </button>

            {onClose && (
              <button
                type="button"
                onClick={onClose}
                disabled={loading}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              >
                Cancelar
              </button>
            )}
          </div>

          {/* Help Text */}
          <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
            <strong>💡 Consejos:</strong>
            <ul className="mt-1 space-y-1">
              <li>• Registra cada visita al territorio, aunque no lo completes</li>
              <li>• Especifica las direcciones trabajadas para llevar un mejor control</li>
              <li>• Usa las notas para recordar detalles importantes para la próxima visita</li>
              <li>• Solo marca como completado cuando hayas trabajado todo el territorio</li>
            </ul>
          </div>
        </form>
      </div>
    </div>
  );
}
