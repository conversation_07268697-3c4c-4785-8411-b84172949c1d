/**
 * Events API Endpoint
 * 
 * Handles CRUD operations for congregation events and activities.
 * Provides event management, scheduling, and coordination functionality.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { 
  EventManagementService, 
  EventCategory, 
  EventVisibility,
  CreateEventData,
  UpdateEventData,
  EventFilters
} from '@/lib/services/eventManagementService';

// Validation schemas
const CreateEventSchema = z.object({
  title: z.string().min(1, 'Event title is required').max(255, 'Title too long'),
  description: z.string().optional(),
  eventDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  startTime: z.string().regex(/^\d{2}:\d{2}$/, 'Invalid time format (HH:MM)').optional(),
  endTime: z.string().regex(/^\d{2}:\d{2}$/, 'Invalid time format (HH:MM)').optional(),
  location: z.string().max(255, 'Location too long').optional(),
  category: z.nativeEnum(EventCategory),
  isAllDay: z.boolean().optional(),
  isRecurring: z.boolean().optional(),
  recurrenceRule: z.string().optional(),
  visibility: z.nativeEnum(EventVisibility).optional(),
});

const UpdateEventSchema = z.object({
  title: z.string().min(1, 'Event title is required').max(255, 'Title too long').optional(),
  description: z.string().optional(),
  eventDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)').optional(),
  startTime: z.string().regex(/^\d{2}:\d{2}$/, 'Invalid time format (HH:MM)').optional(),
  endTime: z.string().regex(/^\d{2}:\d{2}$/, 'Invalid time format (HH:MM)').optional(),
  location: z.string().max(255, 'Location too long').optional(),
  category: z.nativeEnum(EventCategory).optional(),
  isAllDay: z.boolean().optional(),
  isRecurring: z.boolean().optional(),
  recurrenceRule: z.string().optional(),
  visibility: z.nativeEnum(EventVisibility).optional(),
  isActive: z.boolean().optional(),
});

const EventFiltersSchema = z.object({
  category: z.nativeEnum(EventCategory).optional(),
  visibility: z.nativeEnum(EventVisibility).optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  search: z.string().optional(),
  limit: z.string().transform(val => parseInt(val, 10)).optional(),
  offset: z.string().transform(val => parseInt(val, 10)).optional(),
});

/**
 * GET /api/events
 * Retrieve events for the congregation
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = EventFiltersSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { 
      category, 
      visibility, 
      startDate, 
      endDate, 
      isActive, 
      search, 
      limit = 50, 
      offset = 0 
    } = validationResult.data;

    // Build filters
    const filters: EventFilters = {};
    if (category) filters.category = category;
    if (visibility) filters.visibility = visibility;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);
    if (isActive !== undefined) filters.isActive = isActive;
    if (search) filters.search = search;

    // Get events
    const events = await EventManagementService.getEvents(
      member.congregationId,
      filters,
      limit,
      offset
    );

    return NextResponse.json({
      success: true,
      events,
      count: events.length,
      filters: {
        category,
        visibility,
        startDate,
        endDate,
        isActive,
        search,
      },
    });

  } catch (error) {
    console.error('Events GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve events',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/events
 * Create a new event
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can create events
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create events' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CreateEventSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid event data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const eventData = validationResult.data;

    // Convert date string to Date object
    const createData: CreateEventData = {
      ...eventData,
      eventDate: new Date(eventData.eventDate),
    };

    // Create event
    const event = await EventManagementService.createEvent(
      member.congregationId,
      createData
    );

    return NextResponse.json({
      success: true,
      event,
      message: 'Event created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Event creation error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to create event',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/events
 * Update an existing event
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can update events
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update events' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const { eventId, ...updateData } = body;

    if (!eventId) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }

    const validationResult = UpdateEventSchema.safeParse(updateData);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid event data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Convert date string to Date object if provided
    const updateEventData: UpdateEventData = {
      ...validatedData,
      ...(validatedData.eventDate && { eventDate: new Date(validatedData.eventDate) }),
    };

    // Update event
    const event = await EventManagementService.updateEvent(
      member.congregationId,
      eventId,
      updateEventData
    );

    return NextResponse.json({
      success: true,
      event,
      message: 'Event updated successfully',
    });

  } catch (error) {
    console.error('Event update error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to update event',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/events
 * Delete an event (soft delete)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can delete events
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to delete events' },
        { status: 403 }
      );
    }

    // Get event ID from query parameters
    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('eventId');

    if (!eventId) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }

    // Delete event
    await EventManagementService.deleteEvent(member.congregationId, eventId);

    return NextResponse.json({
      success: true,
      message: 'Event deleted successfully',
    });

  } catch (error) {
    console.error('Event deletion error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to delete event',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
