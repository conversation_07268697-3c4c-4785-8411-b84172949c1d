const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function populateSettings() {
  try {
    console.log('🔧 Populating congregation settings...');

    // Check if Coral Oeste congregation exists
    const congregation = await prisma.congregation.findFirst({
      where: {
        name: 'Coral Oeste'
      }
    });

    if (!congregation) {
      console.log('❌ Coral Oeste congregation not found');
      return;
    }

    console.log(`✅ Found congregation: ${congregation.name} (ID: ${congregation.id})`);

    // Default settings for Coral Oeste
    const defaultSettings = [
      { key: 'congregation_number', value: '1441' },
      { key: 'congregation_pin', value: '1930' },
      { key: 'circuit_number', value: '23' },
      { key: 'circuit_overseer', value: '<PERSON>' },
      { key: 'address', value: 'Calle Principal 123, Coral Oeste, Puerto Rico 00123' },
      { key: 'midweek_day', value: 'Thursday' },
      { key: 'midweek_time', value: '7:00 PM' },
      { key: 'weekend_day', value: 'Sunday' },
      { key: 'weekend_time', value: '10:00 AM' },
      { key: 'default_congregation_id', value: '1441' },
      { key: 'default_congregation_pin', value: '1930' },
    ];

    // Upsert each setting
    for (const setting of defaultSettings) {
      await prisma.congregationSetting.upsert({
        where: {
          congregationId_settingKey: {
            congregationId: congregation.id,
            settingKey: setting.key,
          },
        },
        update: {
          settingValue: setting.value,
        },
        create: {
          congregationId: congregation.id,
          settingKey: setting.key,
          settingValue: setting.value,
        },
      });

      console.log(`✓ Set ${setting.key} = ${setting.value}`);
    }

    console.log('\n✅ Congregation settings populated successfully!');

  } catch (error) {
    console.error('❌ Error populating congregation settings:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
populateSettings();
