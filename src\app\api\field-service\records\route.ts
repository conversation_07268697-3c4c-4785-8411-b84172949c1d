import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.user;

    // Get field service records for the current user
    const records = await prisma.fieldServiceRecord.findMany({
      where: {
        congregationId: user.congregationId,
        memberId: user.id
      },
      orderBy: {
        serviceMonth: 'desc'
      }
    });

    // Format the records for the frontend
    const formattedRecords = records.map(record => ({
      id: record.id,
      serviceMonth: record.serviceMonth.toISOString().split('T')[0].substring(0, 7), // YYYY-MM format
      hours: record.hours || 0,
      placements: record.placements || 0,
      videoShowings: record.videoShowings || 0,
      returnVisits: record.returnVisits || 0,
      bibleStudies: record.bibleStudies || 0,
      notes: record.notes || '',
      isSubmitted: record.isSubmitted || false,
      submittedAt: record.submittedAt?.toISOString().split('T')[0] || null
    }));

    return NextResponse.json({ records: formattedRecords });
  } catch (error) {
    console.error('Error fetching field service records:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.user;
    const body = await request.json();

    const {
      serviceMonth,
      hours,
      placements,
      videoShowings,
      returnVisits,
      bibleStudies,
      notes
    } = body;

    // Create or update field service record
    const record = await prisma.fieldServiceRecord.upsert({
      where: {
        congregationId_memberId_serviceMonth: {
          congregationId: user.congregationId,
          memberId: user.id,
          serviceMonth: new Date(serviceMonth + '-01')
        }
      },
      update: {
        hours: parseFloat(hours) || 0,
        placements: parseInt(placements) || 0,
        videoShowings: parseInt(videoShowings) || 0,
        returnVisits: parseInt(returnVisits) || 0,
        bibleStudies: parseInt(bibleStudies) || 0,
        notes: notes || '',
        updatedAt: new Date()
      },
      create: {
        congregationId: user.congregationId,
        memberId: user.id,
        serviceMonth: new Date(serviceMonth + '-01'),
        hours: parseFloat(hours) || 0,
        placements: parseInt(placements) || 0,
        videoShowings: parseInt(videoShowings) || 0,
        returnVisits: parseInt(returnVisits) || 0,
        bibleStudies: parseInt(bibleStudies) || 0,
        notes: notes || '',
        isSubmitted: false
      }
    });

    return NextResponse.json({
      success: true,
      record: {
        id: record.id,
        serviceMonth: record.serviceMonth.toISOString().split('T')[0].substring(0, 7),
        hours: record.hours,
        placements: record.placements,
        videoShowings: record.videoShowings,
        returnVisits: record.returnVisits,
        bibleStudies: record.bibleStudies,
        notes: record.notes,
        isSubmitted: record.isSubmitted
      }
    });
  } catch (error) {
    console.error('Error creating/updating field service record:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
