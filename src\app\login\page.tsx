'use client';

/**
 * Login Page for Hermanos App
 *
 * Provides congregation-based authentication with the exact same UI design
 * and workflow as the original system. Spanish-first interface with
 * mobile-friendly responsive design.
 */

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface LoginFormData {
  region: string;
  congregationId: string;
  pin: string;
  rememberMe: boolean;
}

interface LoginResponse {
  success: boolean;
  token?: string;
  user?: {
    id: string;
    name: string;
    role: string;
    congregationId: string;
    congregationName: string;
  };
  congregation?: {
    id: string;
    name: string;
    language: string;
    timezone: string;
  };
  permissions?: {
    canAccessAdmin: boolean;
    canManageSettings: boolean;
    canExtendToken: boolean;
  };
  error?: string;
}

export default function LoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<LoginFormData>({
    region: '',
    congregationId: '',
    pin: '',
    rememberMe: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = 'checked' in e.target ? e.target.checked : false;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));

    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/congregation-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data: LoginResponse = await response.json();

      if (data.success && data.token) {
        // Store token in localStorage (in production, consider more secure storage)
        localStorage.setItem('hermanos_token', data.token);
        localStorage.setItem('hermanos_user', JSON.stringify(data.user));
        localStorage.setItem('hermanos_congregation', JSON.stringify(data.congregation));
        localStorage.setItem('hermanos_permissions', JSON.stringify(data.permissions));

        // Redirect to dashboard
        router.push('/dashboard');
      } else {
        setError(data.error || 'Error de autenticación');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Error de conexión. Por favor, intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 flex flex-col justify-center py-8 px-4 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center mb-8">
          <div className="mb-4">
            <div className="w-16 h-16 bg-white rounded-full mx-auto flex items-center justify-center shadow-lg">
              <span className="text-2xl font-bold text-blue-600">H</span>
            </div>
          </div>
          <h1 className="text-2xl font-bold text-white mb-1 drop-shadow-lg">
            Hermanos
          </h1>
          <h2 className="text-lg text-blue-100 drop-shadow-md">
            Sistema de Congregación
          </h2>
        </div>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-6 shadow-2xl rounded-xl border border-blue-200">
          <form className="space-y-5" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="region" className="block text-sm font-medium text-gray-700">
                Región
              </label>
              <div className="mt-1">
                <select
                  id="region"
                  name="region"
                  value={formData.region}
                  onChange={handleInputChange}
                  className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 hover:bg-white transition-colors"
                >
                  <option value="">Selecciona una región</option>
                  <option value="america-central">América Central</option>
                  <option value="america-del-norte">América del Norte</option>
                  <option value="america-del-sur">América del Sur</option>
                  <option value="caribe">Caribe</option>
                  <option value="europa">Europa</option>
                  <option value="africa">África</option>
                  <option value="asia">Asia</option>
                  <option value="oceania">Oceanía</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="congregationId" className="block text-sm font-medium text-gray-700">
                ID de Congregación
              </label>
              <div className="mt-1">
                <input
                  id="congregationId"
                  name="congregationId"
                  type="text"
                  required
                  maxLength={8}
                  placeholder="Ej: CORALOES"
                  value={formData.congregationId}
                  onChange={handleInputChange}
                  className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 hover:bg-white transition-colors"
                />
              </div>
            </div>

            <div>
              <label htmlFor="pin" className="block text-sm font-medium text-gray-700">
                PIN de Congregación
              </label>
              <div className="mt-1">
                <input
                  id="pin"
                  name="pin"
                  type="password"
                  required
                  placeholder="Ingresa el PIN"
                  value={formData.pin}
                  onChange={handleInputChange}
                  className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-gray-50 hover:bg-white transition-colors"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                id="rememberMe"
                name="rememberMe"
                type="checkbox"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-900">
                Mantener sesión iniciada
              </label>
            </div>

            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">
                  {error}
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-lg text-base font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200"
              >
                {isLoading ? 'Conectando...' : 'Conectar'}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="text-center text-sm text-gray-500">
              <p>¿Necesitas ayuda con el acceso?</p>
              <p className="mt-1">Contacta a los ancianos de tu congregación</p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 text-center text-xs text-blue-200">
        <p>Hermanos v1.0</p>
        <p className="mt-1">Sistema de Gestión de Congregación</p>
      </div>
    </div>
  );
}
