#!/usr/bin/env node

/**
 * Fix Duplicate Emails Script
 * 
 * Fixes duplicate email addresses in the members table by making them unique
 * within each congregation before applying the unique constraint.
 */

const { PrismaClient } = require('@prisma/client');

async function fixDuplicateEmails() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking for duplicate emails...');
    
    // Find duplicate emails within congregations
    const duplicates = await prisma.$queryRaw`
      SELECT congregation_id, email, COUNT(*) as count
      FROM members 
      GROUP BY congregation_id, email 
      HAVING COUNT(*) > 1
    `;
    
    console.log(`Found ${duplicates.length} duplicate email groups`);
    
    for (const duplicate of duplicates) {
      console.log(`\n📧 Fixing duplicates for email: ${duplicate.email} in congregation: ${duplicate.congregation_id}`);
      
      // Get all members with this duplicate email
      const members = await prisma.member.findMany({
        where: {
          congregationId: duplicate.congregation_id,
          email: duplicate.email,
        },
        orderBy: {
          createdAt: 'asc', // Keep the oldest one with original email
        },
      });
      
      // Update all but the first member to have unique emails
      for (let i = 1; i < members.length; i++) {
        const member = members[i];
        const newEmail = `${member.email.split('@')[0]}_${i}@${member.email.split('@')[1]}`;
        
        await prisma.member.update({
          where: { id: member.id },
          data: { email: newEmail },
        });
        
        console.log(`  ✅ Updated ${member.name}: ${member.email} → ${newEmail}`);
      }
    }
    
    console.log('\n✅ All duplicate emails fixed!');
    
  } catch (error) {
    console.error('❌ Error fixing duplicate emails:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  fixDuplicateEmails().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

module.exports = fixDuplicateEmails;
