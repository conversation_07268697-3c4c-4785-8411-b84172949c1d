#!/usr/bin/env node

/**
 * Test Authentication
 * 
 * This script tests the authentication system to see what's failing.
 */

const { Client } = require('pg');

async function testAuthentication() {
    console.log('🔍 TESTING AUTHENTICATION SYSTEM...');
    console.log('');

    let client;

    try {
        // 1. Connect to database
        console.log('🔌 Connecting to database...');
        client = new Client({
            host: 'localhost',
            port: 5432,
            user: 'mywebsites',
            password: 'password',
            database: 'hermanos'
        });

        await client.connect();
        console.log('✅ Database connection successful');

        // 2. Check congregations table
        console.log('📋 Checking congregations...');
        const congregations = await client.query('SELECT id, name, region FROM congregations;');
        console.log(`   Found ${congregations.rows.length} congregations:`);
        congregations.rows.forEach(cong => {
            console.log(`   - ID: ${cong.id}, Name: ${cong.name}, Region: ${cong.region}`);
        });

        // 3. Check members table
        console.log('👥 Checking members...');
        const members = await client.query('SELECT id, name, email, role, is_active FROM members LIMIT 5;');
        console.log(`   Found ${members.rows.length} members (showing first 5):`);
        members.rows.forEach(member => {
            console.log(`   - ID: ${member.id}, Name: ${member.name}, Email: ${member.email}, Role: ${member.role}, Active: ${member.is_active}`);
        });

        // 4. Check roles table
        console.log('🔐 Checking roles...');
        const roles = await client.query('SELECT name, description FROM roles;');
        console.log(`   Found ${roles.rows.length} roles:`);
        roles.rows.forEach(role => {
            console.log(`   - ${role.name}: ${role.description}`);
        });

        // 5. Test specific congregation lookup
        console.log('🔍 Testing congregation lookup for ID 1441...');
        const specificCong = await client.query('SELECT * FROM congregations WHERE id = $1;', ['1441']);
        if (specificCong.rows.length > 0) {
            console.log('   ✅ Congregation 1441 found:');
            console.log(`   - Name: ${specificCong.rows[0].name}`);
            console.log(`   - Region: ${specificCong.rows[0].region}`);
            console.log(`   - Language: ${specificCong.rows[0].language}`);
            console.log(`   - Active: ${specificCong.rows[0].is_active}`);
        } else {
            console.log('   ❌ Congregation 1441 NOT found');
        }

        // 6. Test member lookup
        console.log('👤 Testing member lookup...');
        const testMember = await client.query('SELECT * FROM members WHERE congregation_id = $1 AND is_active = true LIMIT 1;', ['1441']);
        if (testMember.rows.length > 0) {
            console.log('   ✅ Active member found:');
            console.log(`   - Name: ${testMember.rows[0].name}`);
            console.log(`   - Email: ${testMember.rows[0].email}`);
            console.log(`   - Role: ${testMember.rows[0].role}`);
        } else {
            console.log('   ❌ No active members found for congregation 1441');
        }

        // 7. Test API endpoint
        console.log('🌐 Testing API endpoint...');
        try {
            const response = await fetch('http://localhost:3000/api/auth/congregation-login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    congregationId: '1441',
                    pin: '123456' // This might not be the correct PIN
                })
            });

            const result = await response.json();
            console.log(`   Response status: ${response.status}`);
            console.log(`   Response body:`, result);

        } catch (error) {
            console.log(`   ❌ API test failed: ${error.message}`);
        }

        console.log('');
        console.log('🎯 DIAGNOSIS:');
        
        if (congregations.rows.length === 0) {
            console.log('❌ ISSUE: No congregations found in database');
            console.log('💡 SOLUTION: Data restoration failed - need to re-run restoration script');
        } else if (members.rows.length === 0) {
            console.log('❌ ISSUE: No members found in database');
            console.log('💡 SOLUTION: Members table is empty - need to restore member data');
        } else {
            console.log('✅ Database data looks good');
            console.log('💡 ISSUE: Likely authentication logic or PIN mismatch');
            console.log('💡 SOLUTION: Check PIN hashing or authentication middleware');
        }

    } catch (error) {
        console.error('❌ Error during authentication test:', error);
    } finally {
        if (client) {
            await client.end();
        }
    }
}

// Run the test
testAuthentication();
