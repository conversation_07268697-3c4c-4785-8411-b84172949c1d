/**
 * Import Apartment Buildings (Territories 7, 8, 9) - Proper Excel Structure
 * 
 * Parses exactly like Excel: Building address first, then apartment list
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const CONGREGATION_ID = '1441'; // Coral Oeste
const ZIP_CODE = 'Miami, FL 33144';

// Territory configurations
const TERRITORY_CONFIGS = {
  '007': { displayOrder: 7, sheetName: 'Terr 7' },
  '008': { displayOrder: 8, sheetName: 'Terr 8' },
  '009': { displayOrder: 9, sheetName: 'Terr 9' }
};

/**
 * Parse apartment building addresses exactly like Excel structure
 */
function parseApartmentBuilding(excelData, territoryNumber) {
  const addresses = [];
  let currentBuilding = '';
  let buildingFound = false;
  
  console.log(`\n🔍 Parsing Territory ${territoryNumber} apartment structure...`);
  
  for (let i = 0; i < excelData.length; i++) {
    const row = excelData[i];
    
    if (!row || row.length === 0) continue;
    
    // Skip header rows
    if (i < 7) continue;
    
    // Look for building address patterns
    const cellB = row[1];
    if (cellB) {
      const cellValue = cellB.toString().trim();
      
      // Check for building address (contains FLAGLER or building number)
      if (cellValue.includes('FLAGLER') || cellValue.includes('W FLAGLER ST')) {
        currentBuilding = cellValue;
        buildingFound = true;
        
        // Add building address first
        addresses.push({
          address: `${currentBuilding}, ${ZIP_CODE}`,
          notes: null,
          isBuilding: true,
          isBuildingHeader: true
        });
        
        console.log(`🏢 Found building: ${currentBuilding}`);
        continue;
      }
      
      // Check for building number pattern (like "6537" in Territory 7)
      if (/^\d{4}$/.test(cellValue) && !buildingFound) {
        currentBuilding = `${cellValue} W FLAGLER ST`;
        buildingFound = true;
        
        // Add building address first
        addresses.push({
          address: `${currentBuilding}, ${ZIP_CODE}`,
          notes: null,
          isBuilding: true,
          isBuildingHeader: true
        });
        
        console.log(`🏢 Found building: ${currentBuilding}`);
        continue;
      }
    }
    
    // Look for building info in other columns (Territory 7 has "Edif. 6537" in column G)
    if (!buildingFound && row[6]) {
      const cellG = row[6].toString().trim();
      if (cellG.includes('Edif') || cellG.includes('Edificio')) {
        const buildingMatch = cellG.match(/(\d{4})/);
        if (buildingMatch) {
          currentBuilding = `${buildingMatch[1]} W FLAGLER ST`;
          buildingFound = true;
          
          // Add building address first
          addresses.push({
            address: `${currentBuilding}, ${ZIP_CODE}`,
            notes: null,
            isBuilding: true,
            isBuildingHeader: true
          });
          
          console.log(`🏢 Found building: ${currentBuilding}`);
          continue;
        }
      }
    }
    
    // Parse apartment numbers (only after building is found)
    if (buildingFound && cellB) {
      const cellValue = cellB.toString().trim();
      
      // Skip Excel date serials and headers
      if (typeof row[1] === 'number' && row[1] > 40000) continue;
      if (cellValue.includes('Casa') || cellValue.includes('Fechas') || cellValue.includes('Observaciones')) continue;
      
      // Check if this is an apartment number
      if (/^\d+[a-z]?$/.test(cellValue)) {
        const apartmentNumber = cellValue;
        
        // Get notes from column G (index 6) if available
        let notes = null;
        if (row[6] && typeof row[6] === 'string') {
          const noteText = row[6].toString().trim();
          if (noteText && noteText !== 'null' && noteText !== '' && 
              !noteText.includes('Observaciones') && !noteText.includes('Edif') &&
              !noteText.includes('H-23') && !noteText.includes('T-')) {
            notes = noteText;
          }
        }
        
        // Add apartment as "Apt X"
        addresses.push({
          address: `Apt ${apartmentNumber}`,
          notes: notes,
          isBuilding: true,
          isBuildingHeader: false,
          buildingAddress: currentBuilding
        });
        
        console.log(`   🚪 Apt ${apartmentNumber}${notes ? ` (${notes})` : ''}`);
      }
    }
    
    // Also check column H for additional apartments (Territory 7 has two columns)
    if (buildingFound && row[7]) {
      const cellH = row[7].toString().trim();
      if (/^\d+[a-z]?$/.test(cellH)) {
        const apartmentNumber = cellH;
        
        addresses.push({
          address: `Apt ${apartmentNumber}`,
          notes: null,
          isBuilding: true,
          isBuildingHeader: false,
          buildingAddress: currentBuilding
        });
        
        console.log(`   🚪 Apt ${apartmentNumber}`);
      }
    }
  }
  
  return addresses;
}

async function importApartmentBuilding(territoryNumber) {
  try {
    console.log(`\n📂 Importing Territory ${territoryNumber} (Apartment Building)...`);
    
    const config = TERRITORY_CONFIGS[territoryNumber];
    if (!config) {
      console.error(`❌ No configuration found for Territory ${territoryNumber}`);
      return false;
    }

    // Read Excel file
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${territoryNumber}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    const worksheet = workbook.Sheets[config.sheetName];
    
    if (!worksheet) {
      console.error(`❌ Sheet '${config.sheetName}' not found`);
      console.log(`Available sheets: ${workbook.SheetNames.join(', ')}`);
      return false;
    }
    
    console.log(`📊 Using sheet: ${config.sheetName}`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse apartment building structure
    const addresses = parseApartmentBuilding(excelData, territoryNumber);
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    if (addresses.length === 0) {
      console.log(`⚠️  No addresses found to import for Territory ${territoryNumber}`);
      return false;
    }
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: CONGREGATION_ID }
    });

    if (!congregation) {
      console.error(`❌ Congregation ${CONGREGATION_ID} (Coral Oeste) not found`);
      return false;
    }

    console.log(`✅ Found congregation: ${congregation.name}`);
    
    // Clear existing territory
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: congregation.id,
        territory: {
          territoryNumber: territoryNumber
        }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: congregation.id,
        territoryNumber: territoryNumber
      }
    });
    
    console.log(`🗑️  Cleared existing Territory ${territoryNumber}`);
    
    // Create the territory with addresses in Excel order
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: congregation.id,
        territoryNumber: territoryNumber,
        address: allAddresses,
        notes: allNotes || null,
        status: 'available',
        displayOrder: config.displayOrder
      }
    });
    
    console.log(`✅ Created Territory ${territoryNumber} with ${addresses.length} addresses`);
    console.log(`📍 Display Order: ${config.displayOrder}`);
    
    // Display summary
    console.log('\n📋 Address Summary:');
    const buildingHeaders = addresses.filter(addr => addr.isBuildingHeader);
    const apartments = addresses.filter(addr => !addr.isBuildingHeader);
    console.log(`   🏢 Buildings: ${buildingHeaders.length}`);
    console.log(`   🚪 Apartments: ${apartments.length}`);
    console.log(`   📝 With Notes: ${addresses.filter(addr => addr.notes).length}`);
    
    // Show structure
    console.log('\n📋 Structure Preview:');
    addresses.slice(0, 10).forEach((addr, index) => {
      const prefix = addr.isBuildingHeader ? '🏢' : '   🚪';
      console.log(`${prefix} ${addr.address}${addr.notes ? ` (${addr.notes})` : ''}`);
    });
    
    return true;
    
  } catch (error) {
    console.error(`❌ Error importing Territory ${territoryNumber}:`, error.message);
    return false;
  }
}

async function importAllApartmentBuildings() {
  try {
    console.log('🚀 Starting apartment building import for territories 7, 8, 9...');
    
    const territories = ['007', '008', '009'];
    let successCount = 0;
    let failCount = 0;
    
    for (const territory of territories) {
      const success = await importApartmentBuilding(territory);
      if (success) {
        successCount++;
      } else {
        failCount++;
      }
      
      // Small delay between imports
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`\n🎉 Apartment building import completed!`);
    console.log(`✅ Successfully imported: ${successCount} territories`);
    console.log(`❌ Failed to import: ${failCount} territories`);
    
  } catch (error) {
    console.error('❌ Error during import:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  importAllApartmentBuildings();
}

module.exports = { importApartmentBuilding, importAllApartmentBuildings };
