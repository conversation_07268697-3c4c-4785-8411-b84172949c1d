/**
 * Import Territory 003 - Fixed Parsing Script
 * 
 * <PERSON><PERSON>ly handles the Excel structure with correct street/house number identification
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const TERRITORY_NUMBER = '003';
const CONGREGATION_ID = '1441'; // Coral Oeste
const ZIP_CODE = 'Miami, FL 33126';
const DISPLAY_ORDER = 3;

/**
 * Parse addresses from Excel data with proper structure handling
 */
function parseAddresses(excelData) {
  const addresses = [];
  let currentStreet = '';
  
  for (let i = 0; i < excelData.length; i++) {
    const row = excelData[i];
    
    if (!row || row.length === 0) continue;
    
    // Skip header rows and metadata
    if (i < 8) continue; // Skip first 8 rows which are headers
    
    // Column B (index 1) contains both street names and house numbers
    const cellB = row[1];
    if (!cellB) continue;
    
    const cellValue = cellB.toString().trim();
    if (!cellValue) continue;
    
    // Skip Excel date serial numbers (they appear as large decimal numbers)
    if (typeof cellB === 'number' && cellB > 40000) {
      console.log(`⏭️  Skipping Excel date serial: ${cellB}`);
      continue;
    }
    
    // Check if this is a street name (contains letters and common street indicators)
    const isStreetName = /^[A-Z]/.test(cellValue) && 
                        (/AVE|ST|RD|CT|WAY|BLVD|PL|LN|DR|CIR|CANAL/i.test(cellValue) ||
                         cellValue.includes('NW ') || cellValue.includes('SW ') ||
                         cellValue.includes('NE ') || cellValue.includes('SE '));
    
    if (isStreetName) {
      currentStreet = cellValue;
      console.log(`📍 Found street: ${currentStreet}`);
      continue;
    }
    
    // Check if this is a house number (starts with digit and is reasonable)
    if (/^\d/.test(cellValue) && currentStreet) {
      const houseNumber = cellValue;
      
      // Skip unreasonably large house numbers (likely Excel artifacts)
      const numericValue = parseInt(houseNumber);
      if (numericValue > 10000 && !currentStreet.includes('CANAL')) {
        console.log(`⏭️  Skipping large house number: ${houseNumber}`);
        continue;
      }
      
      const fullAddress = `${houseNumber} ${currentStreet}, ${ZIP_CODE}`;
      
      // Get notes from column G (index 6) if available
      let notes = null;
      if (row[6] && typeof row[6] === 'string') {
        const noteText = row[6].toString().trim();
        if (noteText && noteText !== 'null' && noteText !== '') {
          notes = noteText;
        }
      }
      
      addresses.push({
        address: fullAddress,
        notes: notes,
        street: currentStreet,
        houseNumber: houseNumber
      });
      
      console.log(`🏠 Added address: ${fullAddress}${notes ? ` (${notes})` : ''}`);
    }
  }
  
  return addresses;
}

async function importTerritory003Fixed() {
  try {
    console.log(`📂 Importing Territory ${TERRITORY_NUMBER} (Fixed Parsing)...`);

    // Read Excel file
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${TERRITORY_NUMBER}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    // Use the correct sheet name from diagnosis
    const worksheet = workbook.Sheets['Terr 3'];
    
    if (!worksheet) {
      console.error(`❌ Sheet 'Terr 3' not found`);
      console.log(`Available sheets: ${workbook.SheetNames.join(', ')}`);
      process.exit(1);
    }
    
    console.log(`📊 Using sheet: Terr 3`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse addresses with fixed logic
    const addresses = parseAddresses(excelData);
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    if (addresses.length === 0) {
      console.log(`⚠️  No addresses found to import for Territory ${TERRITORY_NUMBER}`);
      return;
    }
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: CONGREGATION_ID }
    });

    if (!congregation) {
      console.error(`❌ Congregation ${CONGREGATION_ID} (Coral Oeste) not found`);
      process.exit(1);
    }

    console.log(`✅ Found congregation: ${congregation.name}`);
    
    // Clear existing territory
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: congregation.id,
        territory: {
          territoryNumber: TERRITORY_NUMBER
        }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: congregation.id,
        territoryNumber: TERRITORY_NUMBER
      }
    });
    
    console.log(`🗑️  Cleared existing Territory ${TERRITORY_NUMBER}`);
    
    // Create the territory with all addresses combined
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: congregation.id,
        territoryNumber: TERRITORY_NUMBER,
        address: allAddresses,
        notes: allNotes || null,
        status: 'available',
        displayOrder: DISPLAY_ORDER
      }
    });
    
    console.log(`✅ Created Territory ${TERRITORY_NUMBER} with ${addresses.length} addresses`);
    console.log(`📍 Display Order: ${DISPLAY_ORDER}`);
    
    // Display summary by street
    console.log('\n📋 Address Summary by Street:');
    const streetGroups = {};
    addresses.forEach(addr => {
      if (!streetGroups[addr.street]) streetGroups[addr.street] = [];
      streetGroups[addr.street].push(addr);
    });
    
    Object.keys(streetGroups).forEach(street => {
      console.log(`   ${street}: ${streetGroups[street].length} addresses`);
    });

  } catch (error) {
    console.error(`❌ Error importing Territory ${TERRITORY_NUMBER}:`, error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  importTerritory003Fixed();
}

module.exports = { importTerritory003Fixed };
