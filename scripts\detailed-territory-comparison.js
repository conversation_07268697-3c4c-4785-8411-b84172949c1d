const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

async function detailedTerritoryComparison() {
  const prisma = new PrismaClient();

  try {
    // Get territories from database
    const dbTerritories = await prisma.territory.findMany({
      where: { congregationId: '1441' },
      select: { territoryNumber: true },
      orderBy: { territoryNumber: 'asc' }
    });

    const dbNumbers = new Set(dbTerritories.map(t => t.territoryNumber));

    // Get Excel files
    const territoriosDir = path.join(process.cwd(), 'Territorios');
    const allFiles = fs.readdirSync(territoriosDir);

    console.log(`📁 Total files in Territorios directory: ${allFiles.length}`);
    console.log(`📋 Territories in database: ${dbTerritories.length}\n`);

    // Filter and analyze Excel files
    const excelFiles = allFiles.filter(file => file.endsWith('.xlsx'));
    console.log(`📊 Excel files found: ${excelFiles.length}`);

    // Extract territory numbers from Excel files
    const excelTerritories = [];
    const duplicates = [];
    const invalidFiles = [];

    for (const file of excelFiles) {
      const match = file.match(/Terr\.\s*(\d+)/i);
      if (match) {
        const number = match[1].padStart(3, '0');

        // Check for duplicates
        if (excelTerritories.some(t => t.number === number)) {
          duplicates.push({ file, number });
        } else {
          excelTerritories.push({ file, number });
        }
      } else {
        invalidFiles.push(file);
      }
    }

    console.log(`\n🔍 Analysis Results:`);
    console.log(`✅ Valid Excel territories: ${excelTerritories.length}`);
    console.log(`🔄 Duplicate files: ${duplicates.length}`);
    console.log(`❌ Invalid file names: ${invalidFiles.length}`);

    if (duplicates.length > 0) {
      console.log(`\n📋 Duplicate Files Found:`);
      duplicates.forEach(dup => {
        console.log(`  - ${dup.file} (Territory ${dup.number})`);
      });
    }

    if (invalidFiles.length > 0) {
      console.log(`\n❌ Invalid File Names:`);
      invalidFiles.forEach(file => {
        console.log(`  - ${file}`);
      });
    }

    // Find missing territories
    const excelNumbers = new Set(excelTerritories.map(t => t.number));
    const missingFromDB = excelTerritories.filter(t => !dbNumbers.has(t.number));
    const extraInDB = dbTerritories.filter(t => !excelNumbers.has(t.territoryNumber));

    console.log(`\n📊 Comparison Results:`);
    console.log(`📁 Unique Excel territories: ${excelNumbers.size}`);
    console.log(`💾 Database territories: ${dbNumbers.size}`);
    console.log(`❌ Missing from database: ${missingFromDB.length}`);
    console.log(`➕ Extra in database: ${extraInDB.length}`);

    if (missingFromDB.length > 0) {
      console.log(`\n❌ Missing from Database:`);
      missingFromDB.forEach(territory => {
        console.log(`  - Territory ${territory.number} (${territory.file})`);
      });
    }

    if (extraInDB.length > 0) {
      console.log(`\n➕ Extra in Database (no Excel file):`);
      extraInDB.forEach(territory => {
        console.log(`  - Territory ${territory.territoryNumber}`);
      });
    }

    // Show all Excel files for verification
    console.log(`\n📋 All Excel Files (${excelFiles.length}):`);
    excelFiles.sort().forEach((file, index) => {
      const match = file.match(/Terr\.\s*(\d+)/i);
      const number = match ? match[1].padStart(3, '0') : 'INVALID';
      const inDB = dbNumbers.has(number);
      const status = inDB ? '✅' : '❌';
      console.log(`  ${(index + 1).toString().padStart(2, ' ')}. ${status} ${file} → Territory ${number}`);
    });

    // Summary
    console.log(`\n📈 Summary:`);
    console.log(`Total Excel files: ${excelFiles.length}`);
    console.log(`Unique territories in Excel: ${excelNumbers.size}`);
    console.log(`Territories in database: ${dbNumbers.size}`);
    console.log(`Success rate: ${((dbNumbers.size / excelNumbers.size) * 100).toFixed(1)}%`);

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

detailedTerritoryComparison().catch(console.error);
