'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ChartBarIcon,
  ClockIcon,
  DocumentTextIcon,
  CalendarDaysIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  PencilIcon,
  ClockIcon as HistoryIcon
} from '@heroicons/react/24/outline';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface ServiceScheduleTime {
  id: string;
  serviceDate: string;
  serviceTime: string;
  location: string;
  address?: string;
  conductor?: {
    id: string;
    name: string;
    role: string;
  };
  notes?: string;
}

interface ServiceSchedule {
  id: string;
  weekStartDate: string;
  weekEndDate: string;
  scheduleTimes: ServiceScheduleTime[];
}

interface FlatServiceSchedule {
  id: string;
  date: string;
  time: string;
  location: string;
  address?: string;
  conductor: string;
}

interface ServiceRecord {
  id?: string;
  serviceMonth: string;
  hours: number | null;
  placements: number | null;
  videoShowings: number | null;
  returnVisits: number | null;
  bibleStudies: number | null;
  notes: string | null;
  isSubmitted: boolean;
  submittedAt: string | null;
}

export default function FieldServicePage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'resumen' | 'pendiente' | 'informes' | 'horarios'>('resumen');
  const [schedules, setSchedules] = useState<FlatServiceSchedule[]>([]);
  const [records, setRecords] = useState<ServiceRecord[]>([]);
  const [selectedMonth, setSelectedMonth] = useState('2025-07');
  const [showHistorical, setShowHistorical] = useState(false);
  const [expandedDates, setExpandedDates] = useState<Set<string>>(new Set());
  const [editingSchedule, setEditingSchedule] = useState<FlatServiceSchedule | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  // Generate dynamic month options
  const generateMonthOptions = () => {
    const options = [];
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();

    // Generate 12 months: 6 months back and 6 months forward
    for (let i = -6; i <= 6; i++) {
      const date = new Date(currentYear, currentMonth + i, 1);
      const year = date.getFullYear();
      const month = date.getMonth();
      const monthNames = [
        'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
        'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
      ];

      const value = `${year}-${(month + 1).toString().padStart(2, '0')}`;
      const label = `${monthNames[month]} ${year}`;

      options.push({ value, label });
    }

    return options;
  };

  const monthOptions = generateMonthOptions();

  useEffect(() => {
    checkAuth();
    loadServiceSchedules();
    loadServiceRecords();
  }, []);

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Auth error:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const loadServiceSchedules = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) return;

      // Use admin API to get all service times
      const response = await fetch('/api/admin/service-schedules', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.schedule && data.schedule.serviceTimes) {
          // Convert API format to component format
          const convertedSchedules = data.schedule.serviceTimes.map((time: any) => ({
            id: time.id,
            date: time.serviceDate.split('T')[0], // Extract date part
            time: formatTimeFromAPI(time.serviceTime),
            location: time.location,
            address: time.address || '',
            conductor: time.conductor?.name || 'Sin asignar'
          }));
          setSchedules(convertedSchedules);
        }
      }
    } catch (error) {
      console.error('Error loading service schedules:', error);
    }
  };

  const formatTimeFromAPI = (timeStr: string) => {
    // Convert "09:30" to "9:30 AM" format
    const [hours, minutes] = timeStr.split(':');
    const hour24 = parseInt(hours);
    const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
    const period = hour24 >= 12 ? 'PM' : 'AM';
    return `${hour12}:${minutes} ${period}`;
  };

  const loadServiceRecords = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/field-service/records', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRecords(data.records || []);
      } else {
        console.error('Failed to load service records:', response.status, response.statusText);
        setRecords([]);
      }
    } catch (error) {
      console.error('Error loading service records:', error);
      setRecords([]);
    }
  };

  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString || dateString === 'undefined' || dateString === 'null' || dateString.trim() === '') {
      return 'Fecha no disponible';
    }

    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Fecha inválida';
    }

    const days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
    const months = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

    const dayName = days[date.getDay()];
    const monthName = months[date.getMonth()];
    const day = date.getDate();

    return `${dayName}, ${day} de ${monthName}`;
  };

  const formatDateRange = (startDate: string, endDate: string): string => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const startDay = start.getDate();
    const endDay = end.getDate();
    const month = start.toLocaleDateString('es-ES', { month: 'long' });
    const year = start.getFullYear();

    return `${startDay}-${endDay} ${month} de ${year}`;
  };

  const groupSchedulesByDate = (schedules: FlatServiceSchedule[]) => {
    const grouped: { [key: string]: FlatServiceSchedule[] } = {};

    schedules.forEach(schedule => {
      const date = schedule.date;
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(schedule);
    });

    // Sort times within each date
    Object.keys(grouped).forEach(date => {
      grouped[date].sort((a, b) => a.time.localeCompare(b.time));
    });

    return grouped;
  };

  const toggleDateExpansion = (date: string) => {
    const newExpanded = new Set(expandedDates);
    if (newExpanded.has(date)) {
      newExpanded.delete(date);
    } else {
      newExpanded.add(date);
    }
    setExpandedDates(newExpanded);
  };

  const getFilteredSchedules = () => {
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    return schedules.filter(schedule => {
      const scheduleDate = new Date(schedule.date);
      scheduleDate.setHours(0, 0, 0, 0);

      if (showHistorical) {
        // Show past schedules (before today)
        return scheduleDate < currentDate;
      } else {
        // Show upcoming schedules (today and future)
        return scheduleDate >= currentDate;
      }
    });
  };

  const isUpcomingSchedule = (date: string) => {
    const scheduleDate = new Date(date);
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    scheduleDate.setHours(0, 0, 0, 0);
    return scheduleDate >= currentDate;
  };

  const canEditSchedules = () => {
    return user && ['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(user.role);
  };

  const handleEditSchedule = (schedule: FlatServiceSchedule) => {
    setEditingSchedule(schedule);
    setShowEditModal(true);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-green-600 text-white p-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.push('/dashboard')}
              className="text-green-200 hover:text-white flex items-center"
            >
              ← Atrás
            </button>
            <h1 className="text-2xl font-bold">Servicio del Campo</h1>
            <div></div> {/* Spacer for centering */}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-1 sm:px-4">
        {/* Month Selection */}
        <div className="bg-white rounded-lg shadow-md p-2 sm:p-4 mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mes de Servicio
              </label>
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(e.target.value)}
                className="w-1/2 sm:w-auto px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
              >
                {monthOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex flex-wrap gap-1 sm:gap-2 mt-3">
              <button
                onClick={() => setActiveTab('resumen')}
                className={`flex flex-col items-center px-2 py-1 sm:px-3 sm:py-2 rounded-lg transition-colors min-w-[60px] sm:min-w-[80px] ${
                  activeTab === 'resumen'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Resumen"
              >
                <ChartBarIcon className="h-4 w-4 sm:h-5 sm:w-5 mb-1" />
                <span className="text-xs">Resumen</span>
              </button>
              <button
                onClick={() => setActiveTab('pendiente')}
                className={`flex flex-col items-center px-2 py-1 sm:px-3 sm:py-2 rounded-lg transition-colors min-w-[60px] sm:min-w-[80px] ${
                  activeTab === 'pendiente'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Pendiente"
              >
                <ClockIcon className="h-4 w-4 sm:h-5 sm:w-5 mb-1" />
                <span className="text-xs">Pendiente</span>
              </button>
              <button
                onClick={() => setActiveTab('informes')}
                className={`flex flex-col items-center px-2 py-1 sm:px-3 sm:py-2 rounded-lg transition-colors min-w-[60px] sm:min-w-[80px] ${
                  activeTab === 'informes'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Informes"
              >
                <DocumentTextIcon className="h-4 w-4 sm:h-5 sm:w-5 mb-1" />
                <span className="text-xs">Informes</span>
              </button>
              <button
                onClick={() => setActiveTab('horarios')}
                className={`flex flex-col items-center px-2 py-1 sm:px-3 sm:py-2 rounded-lg transition-colors min-w-[60px] sm:min-w-[80px] ${
                  activeTab === 'horarios'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Horarios"
              >
                <CalendarDaysIcon className="h-4 w-4 sm:h-5 sm:w-5 mb-1" />
                <span className="text-xs">Horarios</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-md">
          {activeTab === 'resumen' && (
            <div className="p-2 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Resumen del Mes</h3>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {records.reduce((total, record) => total + (record.hours || 0), 0)}
                  </div>
                  <div className="text-sm text-gray-600">Horas</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {records.reduce((total, record) => total + (record.placements || 0), 0)}
                  </div>
                  <div className="text-sm text-gray-600">Publicaciones</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {records.reduce((total, record) => total + (record.returnVisits || 0), 0)}
                  </div>
                  <div className="text-sm text-gray-600">Revisitas</div>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {records.reduce((total, record) => total + (record.bibleStudies || 0), 0)}
                  </div>
                  <div className="text-sm text-gray-600">Estudios</div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'pendiente' && (
            <div className="p-2 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Actividades Pendientes</h3>
              <div className="space-y-3">
                {/* TODO: Load pending activities from API */}
                <div className="text-center py-8 text-gray-500">
                  <p>No hay actividades pendientes</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'informes' && (
            <div className="p-2 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Mis Informes de Servicio</h3>

              <div className="space-y-3 sm:space-y-4">
                {records.map((record) => (
                  <div key={record.id} className="border border-gray-200 rounded-lg p-3 sm:p-4">
                    <h4 className="font-medium text-gray-900 mb-3 text-sm sm:text-base">
                      {new Date(record.serviceMonth).toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })}
                    </h4>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4 text-xs sm:text-sm">
                      <div>
                        <span className="text-gray-600">Horas:</span>
                        <span className="ml-1 sm:ml-2 font-medium">{record.hours}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Publicaciones:</span>
                        <span className="ml-1 sm:ml-2 font-medium">{record.placements}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Videos:</span>
                        <span className="ml-1 sm:ml-2 font-medium">{record.videoShowings}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Revisitas:</span>
                        <span className="ml-1 sm:ml-2 font-medium">{record.returnVisits}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Estudios:</span>
                        <span className="ml-1 sm:ml-2 font-medium">{record.bibleStudies}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'horarios' && (
            <div className="p-2 sm:p-6">
              {/* Header with Historical Toggle */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Horarios de Servicio</h3>
                <button
                  onClick={() => setShowHistorical(!showHistorical)}
                  className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    showHistorical
                      ? 'bg-gray-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <HistoryIcon className="h-4 w-4 mr-2" />
                  {showHistorical ? 'Ver Próximos' : 'Ver Histórico'}
                </button>
              </div>

              {/* Schedule Display */}
              {(() => {
                const filteredSchedules = getFilteredSchedules();
                const groupedSchedule = groupSchedulesByDate(filteredSchedules);
                const sortedDates = Object.keys(groupedSchedule).sort((a, b) =>
                  showHistorical ? b.localeCompare(a) : a.localeCompare(b)
                );

                if (sortedDates.length === 0) {
                  return (
                    <div className="text-center py-12">
                      <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-gray-500">
                        {showHistorical ? 'No hay horarios históricos' : 'No hay horarios programados'}
                      </p>
                    </div>
                  );
                }

                return (
                  <div className="space-y-2">
                    {sortedDates.map((date) => {
                      const times = groupedSchedule[date];
                      const isExpanded = expandedDates.has(date);
                      const canEdit = !showHistorical && isUpcomingSchedule(date);

                      return (
                        <div key={date} className="border border-gray-200 rounded-lg overflow-hidden">
                          {/* Date Header */}
                          <button
                            onClick={() => toggleDateExpansion(date)}
                            className="w-full bg-green-600 text-white p-4 flex items-center justify-between hover:bg-green-700 transition-colors"
                          >
                            <span className="font-medium">
                              {formatDate(date)}
                            </span>
                            <div className="flex items-center space-x-2">
                              {canEdit && (
                                <PencilIcon className="w-4 h-4 text-green-200" />
                              )}
                              {isExpanded ? (
                                <ChevronUpIcon className="w-5 h-5" />
                              ) : (
                                <ChevronDownIcon className="w-5 h-5" />
                              )}
                            </div>
                          </button>

                          {/* Service Times */}
                          {isExpanded && (
                            <div className="bg-white">
                              {times.map((schedule, index) => (
                                <div key={schedule.id} className={`p-4 ${index !== times.length - 1 ? 'border-b border-gray-100' : ''}`}>
                                  <div className="space-y-3">
                                    {/* Time */}
                                    <div className="text-xl font-bold text-gray-900">
                                      {schedule.time}
                                    </div>

                                    {/* Location */}
                                    <div className="text-base text-gray-800">
                                      {schedule.location}
                                    </div>

                                    {/* Address - only show if not Zoom */}
                                    {schedule.address && !schedule.location.toLowerCase().includes('zoom') && (
                                      <div className="text-sm text-gray-600">
                                        {schedule.address}
                                      </div>
                                    )}

                                    {/* Zoom Details - show if it's a Zoom meeting */}
                                    {schedule.location.toLowerCase().includes('zoom') && schedule.address && (
                                      <div className="text-sm text-gray-600">
                                        {schedule.address}
                                      </div>
                                    )}

                                    {/* Conductor */}
                                    <div className="mt-3">
                                      <div className="text-sm font-semibold text-gray-900 mb-1">
                                        Conductor
                                      </div>
                                      <div className="flex items-center justify-between">
                                        <div className="text-base text-gray-800">
                                          {schedule.conductor}
                                        </div>
                                        {canEditSchedules() && !showHistorical && (
                                          <button
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleEditSchedule(schedule);
                                            }}
                                            className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                                            title="Editar horario"
                                          >
                                            <PencilIcon className="w-4 h-4" />
                                          </button>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                );
              })()}
            </div>
          )}
        </div>
      </div>

      {/* Edit Schedule Modal */}
      {showEditModal && editingSchedule && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Editar Horario de Servicio</h3>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fecha
                  </label>
                  <input
                    type="date"
                    value={editingSchedule.date}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    readOnly
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hora
                  </label>
                  <input
                    type="text"
                    value={editingSchedule.time}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="9:30 AM"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ubicación
                  </label>
                  <input
                    type="text"
                    value={editingSchedule.location}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Salón del Reino"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Dirección
                  </label>
                  <textarea
                    value={editingSchedule.address || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    placeholder="Dirección completa o detalles de Zoom"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Conductor
                  </label>
                  <input
                    type="text"
                    value={editingSchedule.conductor}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Nombre del conductor"
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowEditModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={() => {
                    // TODO: Implement save functionality
                    setShowEditModal(false);
                  }}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Guardar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
