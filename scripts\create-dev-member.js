#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function createDevMember() {
  const prisma = new PrismaClient();
  
  try {
    console.log('👨‍💻 Creating developer member...');
    
    // Ensure DEV congregation exists
    const devCong = await prisma.congregation.findUnique({
      where: { id: 'DEV' }
    });
    
    if (!devCong) {
      console.log('❌ DEV congregation not found');
      return;
    }
    
    // Create developer member
    const hashedPin = await bcrypt.hash('dev123', 12);
    
    const devMember = await prisma.member.upsert({
      where: { 
        congregationId_name: {
          congregationId: 'DEV',
          name: 'Developer User'
        }
      },
      update: {
        pin: hashedPin,
        role: 'developer',
        isActive: true
      },
      create: {
        congregationId: 'DEV',
        name: 'Developer User',
        email: '<EMAIL>',
        phone: '+1234567890',
        role: 'developer',
        pin: hashedPin,
        isActive: true
      }
    });
    
    console.log('✅ Created developer member:', devMember.name);
    
    // Test authentication flow
    console.log('\n🧪 Testing complete authentication flow...');
    
    // Test congregation login
    const testCongregations = [
      { id: '1441', pin: '1930', name: 'Coral Oeste (1441)' },
      { id: 'DEV', pin: '5555', name: 'Developer' },
      { id: 'CORALOES', pin: 'coralpin123', name: 'Coral Oeste (CORALOES)' }
    ];
    
    for (const testCong of testCongregations) {
      const congregation = await prisma.congregation.findUnique({
        where: { id: testCong.id }
      });
      
      if (congregation) {
        const isPinValid = await bcrypt.compare(testCong.pin, congregation.pin);
        
        if (isPinValid) {
          const members = await prisma.member.findMany({
            where: {
              congregationId: congregation.id,
              isActive: true
            },
            orderBy: [
              { role: 'desc' },
              { createdAt: 'asc' }
            ]
          });
          
          console.log(`✅ ${testCong.name}: PIN works, ${members.length} members`);
          if (members.length > 0) {
            console.log(`   Default user: ${members[0].name} (${members[0].role})`);
          }
        } else {
          console.log(`❌ ${testCong.name}: PIN failed`);
        }
      }
    }
    
    console.log('\n📋 Login Instructions:');
    console.log('1. Use Congregation ID: 1441 with PIN: 1930');
    console.log('2. Or use Congregation ID: DEV with PIN: 5555');
    console.log('3. Or use Congregation ID: CORALOES with PIN: coralpin123');
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createDevMember();
