@echo off
echo ========================================
echo RESTORING DATABASE FROM BACKUP
echo ========================================
echo.

REM Set database connection variables
set PGHOST=localhost
set PGPORT=5432
set PGUSER=mywebsites
set PGPASSWORD=password
set PGDATABASE=hermanos

echo 📋 Database: %PGUSER%@%PGHOST%:%PGPORT%/%PGDATABASE%
echo.

echo 🗑️  Step 1: Dropping existing database...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -c "DROP DATABASE IF EXISTS %PGDATABASE%;"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to drop database
    pause
    exit /b 1
)
echo ✅ Database dropped successfully
echo.

echo 🆕 Step 2: Creating new database...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -c "CREATE DATABASE %PGDATABASE%;"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to create database
    pause
    exit /b 1
)
echo ✅ Database created successfully
echo.

echo 📥 Step 3: Restoring from backup...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d %PGDATABASE% -f "hermanos-07-25-25E.sql"
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to restore from backup
    pause
    exit /b 1
)
echo ✅ Backup restored successfully
echo.

echo 🎉 DATABASE RESTORATION COMPLETE!
echo.
echo 📋 NEXT STEPS:
echo    1. Restart the development server
echo    2. Test authentication with restored data
echo    3. Check login credentials from backup
echo.
echo 🔍 TO VERIFY RESTORATION:
echo    - Run: npx prisma studio
echo    - Check congregation and member data
echo    - Test login functionality
echo.
pause
