#!/usr/bin/env node

/**
 * Test Territory 001 Boundary Display
 * 
 * This script tests that Territory 001's real boundary data is properly
 * returned by the API endpoints and displays correctly in the map components.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Test database boundary data
 */
async function testDatabaseBoundary() {
  try {
    console.log('🧪 Testing Database Boundary Data');
    console.log('==================================\n');

    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        id: true,
        territoryNumber: true,
        boundaries: true
      }
    });

    if (!territory) {
      console.log('❌ Territory 001 not found');
      return false;
    }

    console.log(`✅ Territory 001 found (ID: ${territory.id})`);
    
    if (!territory.boundaries) {
      console.log('❌ No boundary data in database');
      return false;
    }

    const boundary = territory.boundaries;
    console.log(`✅ Boundary data exists`);
    console.log(`   Type: ${boundary.type}`);
    console.log(`   Coordinates: ${boundary.coordinates[0].length} points`);
    
    // Validate boundary structure
    if (boundary.type !== 'Polygon') {
      console.log('❌ Invalid boundary type');
      return false;
    }
    
    if (!boundary.coordinates || !boundary.coordinates[0] || boundary.coordinates[0].length < 4) {
      console.log('❌ Invalid boundary coordinates');
      return false;
    }
    
    // Check if polygon is closed
    const coords = boundary.coordinates[0];
    const firstPoint = coords[0];
    const lastPoint = coords[coords.length - 1];
    
    if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
      console.log('❌ Polygon is not properly closed');
      return false;
    }
    
    console.log('✅ Boundary structure is valid');
    console.log('✅ Polygon is properly closed');
    
    // Display boundary details
    console.log('\n📍 Boundary Coordinates:');
    coords.forEach((coord, index) => {
      const label = index === 0 ? 'Start/End' : 
                   index === 1 ? 'Northeast' :
                   index === 2 ? 'Southeast' :
                   index === 3 ? 'Southwest' : 'Point';
      console.log(`   ${label}: [${coord[0]}, ${coord[1]}]`);
    });
    
    // Calculate center
    const lats = coords.slice(0, -1).map(c => c[1]); // Exclude duplicate last point
    const lngs = coords.slice(0, -1).map(c => c[0]);
    
    const center = {
      latitude: lats.reduce((a, b) => a + b, 0) / lats.length,
      longitude: lngs.reduce((a, b) => a + b, 0) / lngs.length
    };
    
    console.log('\n📍 Calculated Center:');
    console.log(`   Latitude: ${center.latitude}`);
    console.log(`   Longitude: ${center.longitude}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Database test error:', error);
    return false;
  }
}

/**
 * Test API response format
 */
async function testAPIFormat() {
  try {
    console.log('\n🧪 Testing API Response Format');
    console.log('===============================\n');
    
    // Simulate what the API should return
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      }
    });
    
    if (!territory) {
      console.log('❌ Territory not found');
      return false;
    }
    
    // Test individual territory API format
    const apiResponse = {
      id: territory.id,
      territoryNumber: territory.territoryNumber,
      address: territory.address,
      status: territory.status,
      notes: territory.notes,
      boundaries: territory.boundaries
    };
    
    console.log('✅ Individual Territory API Response:');
    console.log(`   ID: ${apiResponse.id}`);
    console.log(`   Territory Number: ${apiResponse.territoryNumber}`);
    console.log(`   Has Address: ${!!apiResponse.address}`);
    console.log(`   Has Boundaries: ${!!apiResponse.boundaries}`);
    
    if (apiResponse.boundaries) {
      console.log(`   Boundary Type: ${apiResponse.boundaries.type}`);
      console.log(`   Boundary Points: ${apiResponse.boundaries.coordinates[0].length}`);
    }
    
    // Test map data API format
    const mapDataResponse = {
      id: territory.id,
      territoryNumber: territory.territoryNumber,
      address: territory.address,
      status: territory.status,
      boundary: territory.boundaries, // Note: different field name
      coordinates: null // Will be calculated from boundary
    };
    
    // Calculate coordinates from boundary
    if (territory.boundaries && territory.boundaries.coordinates) {
      const coords = territory.boundaries.coordinates[0];
      const lats = coords.map(c => c[1]);
      const lngs = coords.map(c => c[0]);
      
      mapDataResponse.coordinates = {
        latitude: lats.reduce((a, b) => a + b, 0) / lats.length,
        longitude: lngs.reduce((a, b) => a + b, 0) / lngs.length
      };
    }
    
    console.log('\n✅ Map Data API Response:');
    console.log(`   ID: ${mapDataResponse.id}`);
    console.log(`   Territory Number: ${mapDataResponse.territoryNumber}`);
    console.log(`   Has Boundary: ${!!mapDataResponse.boundary}`);
    console.log(`   Has Coordinates: ${!!mapDataResponse.coordinates}`);
    
    if (mapDataResponse.coordinates) {
      console.log(`   Center Lat: ${mapDataResponse.coordinates.latitude}`);
      console.log(`   Center Lng: ${mapDataResponse.coordinates.longitude}`);
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ API format test error:', error);
    return false;
  }
}

/**
 * Test boundary validation
 */
function testBoundaryValidation() {
  console.log('\n🧪 Testing Boundary Validation');
  console.log('===============================\n');
  
  // Test coordinates are in valid ranges for Miami
  const MIAMI_BOUNDS = {
    lat: { min: 25.7, max: 25.8 },
    lng: { min: -80.3, max: -80.2 }
  };
  
  const testCoords = [
    [-80.2775, 25.763],  // Northwest
    [-80.2725, 25.763],  // Northeast
    [-80.2725, 25.758],  // Southeast
    [-80.2775, 25.758]   // Southwest
  ];
  
  console.log('📍 Validating coordinates are within Miami bounds:');
  
  let allValid = true;
  testCoords.forEach((coord, index) => {
    const [lng, lat] = coord;
    const latValid = lat >= MIAMI_BOUNDS.lat.min && lat <= MIAMI_BOUNDS.lat.max;
    const lngValid = lng >= MIAMI_BOUNDS.lng.min && lng <= MIAMI_BOUNDS.lng.max;
    
    const label = ['Northwest', 'Northeast', 'Southeast', 'Southwest'][index];
    const status = latValid && lngValid ? '✅' : '❌';
    
    console.log(`   ${status} ${label}: [${lng}, ${lat}] - Lat: ${latValid ? 'Valid' : 'Invalid'}, Lng: ${lngValid ? 'Valid' : 'Invalid'}`);
    
    if (!latValid || !lngValid) {
      allValid = false;
    }
  });
  
  if (allValid) {
    console.log('\n✅ All coordinates are within valid Miami bounds');
  } else {
    console.log('\n❌ Some coordinates are outside valid Miami bounds');
  }
  
  return allValid;
}

/**
 * Main test function
 */
async function main() {
  console.log('🗺️  Territory 001 Boundary Testing');
  console.log('===================================\n');
  
  const tests = [
    { name: 'Database Boundary', test: testDatabaseBoundary },
    { name: 'API Format', test: testAPIFormat },
    { name: 'Boundary Validation', test: testBoundaryValidation }
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const { name, test } of tests) {
    try {
      const result = await test();
      if (result) {
        passed++;
        console.log(`\n✅ ${name} test: PASSED`);
      } else {
        console.log(`\n❌ ${name} test: FAILED`);
      }
    } catch (error) {
      console.log(`\n❌ ${name} test: ERROR - ${error.message}`);
    }
  }
  
  console.log('\n📊 Test Results:');
  console.log('================');
  console.log(`Passed: ${passed}/${total}`);
  console.log(`Status: ${passed === total ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (passed === total) {
    console.log('\n🎉 Territory 001 boundary is ready for map display!');
    console.log('   - Real boundary data is in the database');
    console.log('   - API responses include boundary information');
    console.log('   - Coordinates are valid for Miami location');
    console.log('   - Boundary should display correctly on maps');
  }
  
  await prisma.$disconnect();
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testDatabaseBoundary,
  testAPIFormat,
  testBoundaryValidation
};
