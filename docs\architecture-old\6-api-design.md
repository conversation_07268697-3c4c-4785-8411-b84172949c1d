# 6. API Design

## API Architecture Principles

The API design for the Hermanos application follows RESTful principles while prioritizing simplicity, consistency, and mobile optimization. The API serves as the bridge between the frontend application and the database, ensuring secure data access with proper congregation isolation.

**Core API Principles:**

- **RESTful Design**: Standard HTTP methods and status codes for predictable behavior
- **Congregation Isolation**: All endpoints automatically filter data by congregation
- **Mobile Optimization**: Efficient data transfer and caching for mobile devices
- **Consistent Response Format**: Standardized response structure across all endpoints
- **Error Handling**: Comprehensive error responses with user-friendly messages
- **Authentication Required**: All endpoints require valid JWT authentication
- **Type Safety**: Full TypeScript integration for request/response validation

## API Response Format

```typescript
// lib/types/api.types.ts - Standardized API response types
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    filters?: Record<string, any>;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
  };
}

// Success response helper
export function createSuccessResponse<T>(data: T, meta?: ApiResponse<T>['meta']): ApiResponse<T> {
  return {
    success: true,
    data,
    meta,
  };
}

// Error response helper
export function createErrorResponse(code: string, message: string, details?: any): ApiResponse {
  return {
    success: false,
    error: {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      requestId: generateRequestId(),
    },
  };
}

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
```

## Member Management API

```typescript
// pages/api/members/index.ts - Member management endpoints
import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { PERMISSIONS } from '@/lib/auth/simpleRBAC';
import { createSuccessResponse, createErrorResponse } from '@/lib/types/api.types';
import { validateMemberData } from '@/lib/validation/simple';
import bcrypt from 'bcryptjs';

export default withAuth(
  async (req, res, context) => {
    const { user, congregation } = context;

    switch (req.method) {
      case 'GET':
        return await getMembers(req, res, congregation.id);
      case 'POST':
        return await createMember(req, res, congregation.id, user.id);
      default:
        return res
          .status(405)
          .json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
    }
  },
  { requirePermission: PERMISSIONS.VIEW_ADMIN }
);

// Get all members for congregation
async function getMembers(req: NextApiRequest, res: NextApiResponse, congregationId: string) {
  try {
    const { page = 1, limit = 50, role, active = 'true' } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const take = Math.min(Number(limit), 100); // Max 100 per page

    const where: any = {
      congregationId,
      ...(role && { role: role as string }),
      ...(active !== 'all' && { isActive: active === 'true' }),
    };

    const [members, total] = await Promise.all([
      prisma.member.findMany({
        where,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          lastLogin: true,
          createdAt: true,
        },
        orderBy: { name: 'asc' },
        skip,
        take,
      }),
      prisma.member.count({ where }),
    ]);

    return res.status(200).json(
      createSuccessResponse(members, {
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit)),
        },
      })
    );
  } catch (error) {
    console.error('Get members error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to retrieve members'));
  }
}

// Create new member
async function createMember(
  req: NextApiRequest,
  res: NextApiResponse,
  congregationId: string,
  createdBy: string
) {
  try {
    // Validate input data
    const validation = validateMemberData(req.body);
    if (validation.length > 0) {
      return res
        .status(400)
        .json(createErrorResponse('VALIDATION_ERROR', 'Invalid member data', validation));
    }

    const { name, email, role, pin } = req.body;

    // Check if member with same email already exists in congregation
    if (email) {
      const existingMember = await prisma.member.findFirst({
        where: {
          congregationId,
          email,
          isActive: true,
        },
      });

      if (existingMember) {
        return res
          .status(409)
          .json(createErrorResponse('DUPLICATE_EMAIL', 'A member with this email already exists'));
      }
    }

    // Hash the PIN
    const hashedPin = await bcrypt.hash(pin, 12);

    // Create member
    const member = await prisma.member.create({
      data: {
        congregationId,
        name,
        email: email || null,
        role,
        pin: hashedPin,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true,
      },
    });

    return res.status(201).json(createSuccessResponse(member));
  } catch (error) {
    console.error('Create member error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to create member'));
  }
}
```

## Meeting Management API

```typescript
// pages/api/meetings/midweek/index.ts - Midweek meeting endpoints
import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { PERMISSIONS } from '@/lib/auth/simpleRBAC';
import { createSuccessResponse, createErrorResponse } from '@/lib/types/api.types';

export default withAuth(async (req, res, context) => {
  const { congregation } = context;

  switch (req.method) {
    case 'GET':
      return await getMidweekMeetings(req, res, congregation.id);
    case 'POST':
      return await createMidweekMeeting(req, res, congregation.id);
    default:
      return res.status(405).json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
  }
});

// Get midweek meetings with parts and assignments
async function getMidweekMeetings(
  req: NextApiRequest,
  res: NextApiResponse,
  congregationId: string
) {
  try {
    const { startDate = new Date().toISOString().split('T')[0], endDate, limit = 10 } = req.query;

    const endDateValue =
      endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 30 days from now

    const meetings = await prisma.midweekMeeting.findMany({
      where: {
        congregationId,
        meetingDate: {
          gte: new Date(startDate as string),
          lte: new Date(endDateValue as string),
        },
      },
      include: {
        chairman: {
          select: { id: true, name: true },
        },
        parts: {
          include: {
            assignedMember: {
              select: { id: true, name: true },
            },
            assistant: {
              select: { id: true, name: true },
            },
          },
          orderBy: { displayOrder: 'asc' },
        },
      },
      orderBy: { meetingDate: 'asc' },
      take: Number(limit),
    });

    return res.status(200).json(createSuccessResponse(meetings));
  } catch (error) {
    console.error('Get midweek meetings error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to retrieve meetings'));
  }
}

// Create new midweek meeting
async function createMidweekMeeting(
  req: NextApiRequest,
  res: NextApiResponse,
  congregationId: string
) {
  try {
    const { meetingDate, theme, chairmanId, location, parts } = req.body;

    // Check if meeting already exists for this date
    const existingMeeting = await prisma.midweekMeeting.findFirst({
      where: {
        congregationId,
        meetingDate: new Date(meetingDate),
      },
    });

    if (existingMeeting) {
      return res
        .status(409)
        .json(createErrorResponse('MEETING_EXISTS', 'A meeting already exists for this date'));
    }

    // Create meeting with parts in a transaction
    const meeting = await prisma.$transaction(async tx => {
      const newMeeting = await tx.midweekMeeting.create({
        data: {
          congregationId,
          meetingDate: new Date(meetingDate),
          theme,
          chairmanId,
          location: location || 'Kingdom Hall',
        },
      });

      // Create meeting parts if provided
      if (parts && Array.isArray(parts)) {
        await tx.midweekMeetingPart.createMany({
          data: parts.map((part: any, index: number) => ({
            meetingId: newMeeting.id,
            partType: part.partType,
            title: part.title,
            durationMinutes: part.durationMinutes,
            assignedMemberId: part.assignedMemberId,
            assistantId: part.assistantId,
            displayOrder: index + 1,
            notes: part.notes,
          })),
        });
      }

      return newMeeting;
    });

    // Fetch the complete meeting with parts
    const completeMeeting = await prisma.midweekMeeting.findUnique({
      where: { id: meeting.id },
      include: {
        chairman: {
          select: { id: true, name: true },
        },
        parts: {
          include: {
            assignedMember: {
              select: { id: true, name: true },
            },
            assistant: {
              select: { id: true, name: true },
            },
          },
          orderBy: { displayOrder: 'asc' },
        },
      },
    });

    return res.status(201).json(createSuccessResponse(completeMeeting));
  } catch (error) {
    console.error('Create midweek meeting error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to create meeting'));
  }
}
```

## Task Management API

```typescript
// pages/api/tasks/index.ts - Task management endpoints
import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { createSuccessResponse, createErrorResponse } from '@/lib/types/api.types';

export default withAuth(async (req, res, context) => {
  const { congregation } = context;

  switch (req.method) {
    case 'GET':
      return await getTasks(req, res, congregation.id);
    case 'POST':
      return await createTask(req, res, congregation.id);
    default:
      return res.status(405).json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
  }
});

// Get tasks with optional filtering
async function getTasks(req: NextApiRequest, res: NextApiResponse, congregationId: string) {
  try {
    const { category, active = 'true', page = 1, limit = 20 } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const take = Math.min(Number(limit), 100);

    const where: any = {
      congregationId,
      ...(category && { category: category as string }),
      ...(active !== 'all' && { isActive: active === 'true' }),
    };

    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        include: {
          createdBy: {
            select: { id: true, name: true },
          },
          assignments: {
            where: {
              status: { in: ['assigned', 'in_progress'] },
            },
            include: {
              assignedMember: {
                select: { id: true, name: true },
              },
            },
            orderBy: { assignedDate: 'desc' },
            take: 5, // Latest 5 assignments
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take,
      }),
      prisma.task.count({ where }),
    ]);

    return res.status(200).json(
      createSuccessResponse(tasks, {
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit)),
        },
      })
    );
  } catch (error) {
    console.error('Get tasks error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to retrieve tasks'));
  }
}

// Create new task
async function createTask(req: NextApiRequest, res: NextApiResponse, congregationId: string) {
  try {
    const {
      title,
      description,
      category,
      estimatedDurationMinutes,
      requiresElder,
      requiresMinisterialServant,
      serviceGroupSpecific,
      isRecurring,
      recurrencePattern,
    } = req.body;

    const task = await prisma.task.create({
      data: {
        congregationId,
        title,
        description,
        category,
        estimatedDurationMinutes,
        requiresElder: requiresElder || false,
        requiresMinisterialServant: requiresMinisterialServant || false,
        serviceGroupSpecific: serviceGroupSpecific || false,
        isRecurring: isRecurring || false,
        recurrencePattern: recurrencePattern || null,
        isActive: true,
      },
      include: {
        createdBy: {
          select: { id: true, name: true },
        },
      },
    });

    return res.status(201).json(createSuccessResponse(task));
  } catch (error) {
    console.error('Create task error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to create task'));
  }
}
```

## File Upload API

```typescript
// pages/api/letters/upload.ts - File upload endpoint
import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { PERMISSIONS } from '@/lib/auth/simpleRBAC';
import { createSuccessResponse, createErrorResponse } from '@/lib/types/api.types';
import formidable from 'formidable';
import fs from 'fs/promises';
import path from 'path';

export const config = {
  api: {
    bodyParser: false, // Disable body parser for file uploads
  },
};

export default withAuth(
  async (req, res, context) => {
    if (req.method !== 'POST') {
      return res.status(405).json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
    }

    return await uploadLetter(req, res, context);
  },
  { requirePermission: PERMISSIONS.UPLOAD_LETTERS }
);

async function uploadLetter(req: NextApiRequest, res: NextApiResponse, context: any) {
  try {
    const { congregation, user } = context;

    // Parse form data
    const form = formidable({
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
      allowEmptyFiles: false,
      filter: ({ mimetype }) => {
        return (
          mimetype === 'application/pdf' || mimetype === 'image/jpeg' || mimetype === 'image/png'
        );
      },
    });

    const [fields, files] = await form.parse(req);

    const title = Array.isArray(fields.title) ? fields.title[0] : fields.title;
    const category = Array.isArray(fields.category) ? fields.category[0] : fields.category;
    const visibility = Array.isArray(fields.visibility) ? fields.visibility[0] : fields.visibility;
    const file = Array.isArray(files.file) ? files.file[0] : files.file;

    if (!file || !title) {
      return res
        .status(400)
        .json(createErrorResponse('MISSING_REQUIRED_FIELDS', 'File and title are required'));
    }

    // Generate unique filename
    const fileExtension = path.extname(file.originalFilename || '');
    const sanitizedTitle = title.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
    const timestamp = Date.now();
    const filename = `${sanitizedTitle}_${timestamp}${fileExtension}`;

    // Create upload directory if it doesn't exist
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'letters', congregation.id);
    await fs.mkdir(uploadDir, { recursive: true });

    // Move file to upload directory
    const filePath = path.join(uploadDir, filename);
    await fs.copyFile(file.filepath, filePath);

    // Clean up temporary file
    await fs.unlink(file.filepath);

    // Save file metadata to database
    const letter = await prisma.letter.create({
      data: {
        congregationId: congregation.id,
        title,
        filename,
        filePath: `/uploads/letters/${congregation.id}/${filename}`,
        fileSize: file.size,
        mimeType: file.mimetype,
        category: category || 'General',
        visibility: visibility || 'ALL_MEMBERS',
        uploadedBy: user.id,
        isActive: true,
      },
      include: {
        uploadedBy: {
          select: { id: true, name: true },
        },
      },
    });

    return res.status(201).json(createSuccessResponse(letter));
  } catch (error) {
    console.error('File upload error:', error);
    return res.status(500).json(createErrorResponse('UPLOAD_FAILED', 'Failed to upload file'));
  }
}
```

## API Client Service

```typescript
// lib/services/apiClient.ts - Frontend API client
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL;
  }

  setToken(token: string) {
    this.token = token;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || 'API request failed');
    }

    return data;
  }

  // Member API methods
  async getMembers(params?: { page?: number; limit?: number; role?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.role) searchParams.set('role', params.role);

    const query = searchParams.toString();
    return this.request(`/members${query ? `?${query}` : ''}`);
  }

  async createMember(memberData: any) {
    return this.request('/members', {
      method: 'POST',
      body: JSON.stringify(memberData),
    });
  }

  // Meeting API methods
  async getMidweekMeetings(params?: { startDate?: string; endDate?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.set('startDate', params.startDate);
    if (params?.endDate) searchParams.set('endDate', params.endDate);

    const query = searchParams.toString();
    return this.request(`/meetings/midweek${query ? `?${query}` : ''}`);
  }

  async createMidweekMeeting(meetingData: any) {
    return this.request('/meetings/midweek', {
      method: 'POST',
      body: JSON.stringify(meetingData),
    });
  }

  // Task API methods
  async getTasks(params?: { category?: string; page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (params?.category) searchParams.set('category', params.category);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.request(`/tasks${query ? `?${query}` : ''}`);
  }

  async createTask(taskData: any) {
    return this.request('/tasks', {
      method: 'POST',
      body: JSON.stringify(taskData),
    });
  }

  // File upload method
  async uploadLetter(formData: FormData) {
    const url = `${this.baseURL}/letters/upload`;

    const headers: HeadersInit = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData, // Don't set Content-Type for FormData
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || 'Upload failed');
    }

    return data;
  }
}

export const apiClient = new ApiClient();
```

This API design provides a robust, secure, and efficient interface for all congregation management operations while maintaining the simplicity and performance that mobile users expect.

---
