/**
 * PIN Settings Management API Endpoint
 *
 * Handles PIN configuration settings for congregations.
 * Only accessible to developers and coordinators.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PinService } from '@/lib/services/pinService';

// Validation schema for PIN settings
const PinSettingsSchema = z.object({
  minLength: z.number().min(3).max(20),
  maxLength: z.number().min(3).max(20),
  requireNumeric: z.boolean(),
  requireAlphanumeric: z.boolean(),
  requireSpecialChars: z.boolean(),
  allowSequential: z.boolean(),
  allowRepeated: z.boolean(),
  expirationDays: z.number().nullable(),
  bcryptRounds: z.number().min(10).max(15),
  maxAttempts: z.number().min(3).max(10),
  lockoutDurationMinutes: z.number().min(5).max(1440), // 5 minutes to 24 hours
  preventReuseCount: z.number().min(0).max(10),
  temporaryPinExpirationHours: z.number().min(1).max(168), // 1 hour to 1 week
}).refine(data => data.maxLength >= data.minLength, {
  message: "maxLength must be greater than or equal to minLength",
  path: ["maxLength"],
});

/**
 * GET - Retrieve PIN settings for congregation
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view PIN settings' },
        { status: 403 }
      );
    }

    // Get PIN settings
    const settings = await PinService.getPinSettings(user.congregationId);

    return NextResponse.json({
      success: true,
      settings,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('PIN settings GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch PIN settings',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT - Update PIN settings for congregation
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has developer access (only developers can change PIN settings)
    if (user.role !== 'developer') {
      return NextResponse.json(
        { error: 'Only developers can modify PIN settings' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = PinSettingsSchema.parse(body);

    // Update PIN settings
    const settings = await PinService.updatePinSettings(
      user.congregationId,
      validatedData,
      user.userId
    );

    return NextResponse.json({
      success: true,
      settings,
      message: 'PIN settings updated successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('PIN settings PUT error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid PIN settings data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to update PIN settings';

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT to update PIN settings.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. PIN settings cannot be deleted.' },
    { status: 405 }
  );
}
