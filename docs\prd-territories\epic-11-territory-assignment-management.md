# Epic 11: Territory Assignment & Management

**Epic Goal:** Enable congregation administrators to assign territories to members (Elders, Ministerial Servants, and approved Publishers) with comprehensive tracking of assignment status, history, and workflow management, delivering the core business value of digital territory coordination.

## Story 11.1: Member Territory Assignment Interface

As a congregation administrator,
I want to assign territories to congregation members,
so that territory assignments can be tracked digitally instead of manually.

### Acceptance Criteria

1. Assignment interface displays available territories and congregation members
2. Dropdown or search functionality allows selection of member for assignment
3. Assignment date is automatically recorded when territory is assigned
4. Territory status changes from "available" to "assigned" upon assignment
5. Assignment confirmation shows territory details and assigned member information
6. Only users with Elder or Ministerial Servant roles can access assignment functionality

## Story 11.2: Territory Assignment History Tracking

As a congregation administrator,
I want to view territory assignment history,
so that I can track who has worked territories and when they were completed.

### Acceptance Criteria

1. Territory detail view displays complete assignment history chronologically
2. History entries show assigned member, assignment date, completion date, and duration
3. Current assignment is clearly distinguished from historical assignments
4. Assignment history is preserved when territories are reassigned
5. History view includes search and filter capabilities by member or date range
6. Assignment statistics show average assignment duration and completion rates

## Story 11.3: Territory Status Management

As a congregation administrator,
I want to manage territory status changes,
so that territory availability and workflow states are accurately tracked.

### Acceptance Criteria

1. Territory status can be updated to: available, assigned, completed, out of service
2. Status change interface provides reason/notes field for documentation
3. Status changes are logged with timestamp and user who made the change
4. Completed territories automatically become available for reassignment
5. Out of service territories are excluded from assignment workflows
6. Status change notifications are sent to relevant administrators

## Story 11.4: Member Territory Assignment View

As a congregation member,
I want to view my assigned territories following the established Field Service UI patterns,
so that I can see what territories I need to work with a familiar interface.

### Acceptance Criteria

1. Member territory interface follows the exact UI patterns from Field Service screenshots (1.jpg through 8.jpg)
2. Territory cards use the same layout, styling, and visual hierarchy as Field Service cards
3. Territory list view matches Field Service list patterns with consistent spacing and typography
4. Territory detail view follows Field Service detail screen patterns
5. Navigation and interaction patterns match existing Field Service workflows
6. Territory cards display territory number, address, and assignment date using Field Service card format
7. Assignment duration indicator follows Field Service time display patterns
8. Member can mark territory as completed using Field Service-style completion interface
9. Interface maintains mobile optimization consistent with Field Service mobile patterns

## Story 11.5: Territory Assignment Notifications

As a congregation member,
I want to receive notifications about territory assignments,
so that I'm informed when territories are assigned to me or need attention.

### Acceptance Criteria

1. Email notifications are sent when territories are assigned to members
2. Notification includes territory details and assignment information
3. Reminder notifications are sent for territories assigned beyond typical duration
4. Members can configure notification preferences (email, in-app, frequency)
5. Administrators receive notifications when territories are marked completed
6. Notification system integrates with existing Coral Oeste App notification patterns

## Story 11.6: Territory Assignment Reports

As a service coordinator,
I want to generate territory assignment reports,
so that I can monitor territory coverage and assignment effectiveness.

### Acceptance Criteria

1. Assignment report shows all territories with current status and assigned members
2. Report includes assignment duration and completion statistics
3. Available territories report helps identify unassigned territories
4. Member workload report shows territory distribution across members
5. Reports can be filtered by date range, member, or territory status
6. Reports are exportable in PDF format for congregation records
