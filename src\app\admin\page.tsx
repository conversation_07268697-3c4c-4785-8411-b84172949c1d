'use client';

/**
 * Admin Page for Hermanos App
 *
 * Administrative interface with role-based access control.
 * Only accessible to users with admin permissions.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminFooter from '@/components/admin/AdminFooter';
import { useTranslation } from '@/contexts/LanguageContext';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface Permissions {
  canAccessAdmin: boolean;
  canManageSettings: boolean;
}

export default function AdminPage() {
  const router = useRouter();
  const { t } = useTranslation();
  const [user, setUser] = useState<User | null>(null);
  const [permissions, setPermissions] = useState<Permissions | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAdminAccess();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkAdminAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      // Verify token and check admin permissions
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();

        // Check if user has admin access
        if (!data.permissions.canAccessAdmin) {
          router.push('/dashboard');
          return;
        }

        setUser(data.user);
        setPermissions(data.permissions);
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Admin access check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verificando permisos de administrador...</p>
        </div>
      </div>
    );
  }

  if (!user || !permissions) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Pixel-perfect match to reference design */}
      <header className="bg-blue-600 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToDashboard}
                className="flex items-center text-white hover:text-blue-100 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span className="text-sm font-medium">Member View</span>
              </button>
            </div>
            <div className="text-center flex-1">
              <h1 className="text-lg font-semibold text-white whitespace-nowrap">
                {t('admin.dashboard')}
              </h1>
            </div>
            <div className="w-24"></div> {/* Spacer for centering */}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
        <div className="px-4 py-2 sm:px-0">
          {/* Welcome Section - Simplified to match reference design */}
          <div className="bg-white overflow-hidden shadow-sm rounded-lg mb-6">
            <div className="px-6 py-4">
              <h2 className="text-xl font-semibold text-gray-900">
                {t('admin.welcome', { congregationName: user?.congregationName || 'Coral Oeste' })}
              </h2>
            </div>
          </div>

          {/* Admin Features Grid - Pixel-perfect match to reference design */}
          <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Member Management */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/members/enhanced'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Member Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.members.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.members.description')}
                </p>
              </div>
            </div>

            {/* Midweek Meeting */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/meetings/midweek'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Midweek Meeting Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.midweek_meeting.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.midweek_meeting.description')}
                </p>
              </div>
            </div>

            {/* Weekend Meeting */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/meetings/weekend'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Weekend Meeting Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.weekend_meeting.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.weekend_meeting.description')}
                </p>
              </div>
            </div>

            {/* Field Service */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/field-service'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Field Service Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.field_service.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.field_service.description')}
                </p>
              </div>
            </div>

            {/* Territories */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/territorios'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Territory Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.territories.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.territories.description')}
                </p>
              </div>
            </div>

            {/* Tasks */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/tasks'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Tasks Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.tasks.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.tasks.description')}
                </p>
              </div>
            </div>

            {/* Assignments */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/assignments'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Assignments Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.assignments.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.assignments.description')}
                </p>
              </div>
            </div>

            {/* Programs */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/programs'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Programs Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.programs.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.programs.description')}
                </p>
              </div>
            </div>

            {/* Events */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/events'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Events Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.events.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.events.description')}
                </p>
              </div>
            </div>
            {/* Letters */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/letters'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Letters Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.letters.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.letters.description')}
                </p>
              </div>
            </div>



            {/* Permissions */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/permissions'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Permissions Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.permissions.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.permissions.description')}
                </p>
              </div>
            </div>

            {/* Database Management */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/database'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Database Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.database.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.database.description')}
                </p>
              </div>
            </div>

            {/* Song Management */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/songs'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Song Management"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.songs.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.songs.description')}
                </p>
              </div>
            </div>

            {/* Congregation Settings */}
            <div
              className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md hover:bg-gray-50 transition-all cursor-pointer"
              onClick={() => window.location.href = '/admin/settings'}
              role="button"
              tabIndex={0}
              aria-label="Navigate to Congregation Settings"
            >
              <div className="p-6 text-center">
                <div className="w-12 h-12 bg-gray-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-1">
                  {t('admin.cards.settings.title')}
                </h3>
                <p className="text-xs text-gray-600 line-clamp-2">
                  {t('admin.cards.settings.description')}
                </p>
              </div>
            </div>
          </div>


        </div>
      </main>

      {/* Admin Footer */}
      <AdminFooter currentSection="inicio" />
    </div>
  );
}
