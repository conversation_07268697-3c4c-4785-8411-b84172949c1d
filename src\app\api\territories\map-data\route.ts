/**
 * Territory Map Data API Endpoint
 *
 * Provides territory data with coordinates for map visualization.
 * Handles geocoding, caching, and territory location management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { geocodingService } from '@/services/territories/GeocodingService';
import type { Territory } from '@/types/territories/map';

// Validation schema for query parameters
const MapDataQuerySchema = z.object({
  status: z.enum(['available', 'assigned', 'completed', 'out_of_service']).nullable().optional(),
  includeCoordinates: z.boolean().optional().default(true),
  forceGeocode: z.boolean().optional().default(false),
  congregationId: z.string().nullable().optional(),
  // Member filtering options
  memberView: z.boolean().optional().default(false), // Show only territories assigned to current member
  memberId: z.string().nullable().optional(), // Show territories assigned to specific member (admin only)
  includeAssignments: z.boolean().optional().default(true) // Include assignment information
});

/**
 * GET /api/territories/map-data - Get territories with coordinates for map display
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Get member details for permission checking
    const member = await prisma.member.findFirst({
      where: {
        congregationId: user.congregationId,
        pin: user.pin
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      status: searchParams.get('status'),
      includeCoordinates: searchParams.get('includeCoordinates') === 'true',
      forceGeocode: searchParams.get('forceGeocode') === 'true',
      congregationId: searchParams.get('congregationId') || user.congregationId,
      memberView: searchParams.get('memberView') === 'true',
      memberId: searchParams.get('memberId'),
      includeAssignments: searchParams.get('includeAssignments') !== 'false'
    };

    // Check permissions based on request type
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    // Member view is allowed for all authenticated users
    // Admin view (all territories) requires admin access
    // Specific member filtering requires admin access
    if (!queryParams.memberView && !hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for full territory map data' },
        { status: 403 }
      );
    }

    if (queryParams.memberId && !hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required to view other member territories' },
        { status: 403 }
      );
    }

    const validatedParams = MapDataQuerySchema.parse(queryParams);

    // Build where clause for territory filtering
    const whereClause: any = {
      congregationId: validatedParams.congregationId
    };

    if (validatedParams.status) {
      whereClause.status = validatedParams.status;
    }

    // Add member filtering if requested
    if (validatedParams.memberView) {
      // Show only territories assigned to current member
      whereClause.assignments = {
        some: {
          memberId: member.id,
          status: 'active'
        }
      };
    } else if (validatedParams.memberId) {
      // Show territories assigned to specific member (admin only)
      whereClause.assignments = {
        some: {
          memberId: validatedParams.memberId,
          status: 'active'
        }
      };
    }

    // Fetch territories from database
    const territories = await prisma.territory.findMany({
      where: whereClause,
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        status: true,
        notes: true,
        boundaries: true,
        displayOrder: true,
        createdAt: true,
        updatedAt: true,
        // Include assignment information if requested
        assignments: validatedParams.includeAssignments ? {
          where: { returnedAt: null },
          select: {
            id: true,
            assignedAt: true,
            dueDate: true,
            member: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          },
          take: 1,
          orderBy: { assignedAt: 'desc' }
        } : false
      },
      orderBy: [
        { displayOrder: 'asc' },
        { territoryNumber: 'asc' }
      ]
    });

    // Process territories and add coordinates if requested
    const processedTerritories: Territory[] = [];
    const geocodingPromises: Promise<void>[] = [];

    for (const territory of territories) {
      const territoryData: Territory = {
        id: territory.id,
        territoryNumber: territory.territoryNumber,
        address: territory.address,
        status: territory.status as Territory['status'],
        notes: territory.notes || undefined,
        bounds: undefined,
        coordinates: undefined
      };

      // Add current assignment info if exists and requested
      if (validatedParams.includeAssignments && territory.assignments && territory.assignments.length > 0) {
        const assignment = territory.assignments[0];
        (territoryData as any).currentAssignment = {
          id: assignment.id,
          memberId: assignment.member.id,
          memberName: `${assignment.member.firstName} ${assignment.member.lastName}`.trim(),
          assignedAt: assignment.assignedAt,
          dueDate: assignment.dueDate
        };
      }

      // Handle coordinates if requested
      if (validatedParams.includeCoordinates) {
        // Check if we have cached coordinates in boundaries field
        let coordinates = null;

        if (territory.boundaries && typeof territory.boundaries === 'object') {
          const boundaries = territory.boundaries as any;
          if (boundaries.coordinates && Array.isArray(boundaries.coordinates)) {
            coordinates = {
              latitude: boundaries.coordinates[1],
              longitude: boundaries.coordinates[0]
            };
          }
        }

        if (!coordinates || validatedParams.forceGeocode) {
          // Need to geocode the address
          const geocodePromise = geocodingService.geocodeAddress(territory.address)
            .then(async (result) => {
              if (result) {
                territoryData.coordinates = {
                  latitude: result.latitude,
                  longitude: result.longitude
                };

                // Cache coordinates in boundaries field for future use
                try {
                  await prisma.territory.update({
                    where: { id: territory.id },
                    data: {
                      boundaries: {
                        type: 'Point',
                        coordinates: [result.longitude, result.latitude]
                      }
                    }
                  });
                } catch (error) {
                  console.warn(`Failed to cache coordinates for territory ${territory.territoryNumber}:`, error);
                }
              }
            })
            .catch((error) => {
              console.warn(`Geocoding failed for territory ${territory.territoryNumber}:`, error);
            });

          geocodingPromises.push(geocodePromise);
        } else {
          territoryData.coordinates = coordinates;
        }
      }

      processedTerritories.push(territoryData);
    }

    // Wait for all geocoding operations to complete
    if (geocodingPromises.length > 0) {
      await Promise.allSettled(geocodingPromises);
    }

    // Calculate congregation bounds if territories have coordinates
    let congregationBounds = null;
    const territoriesWithCoords = processedTerritories.filter(t => t.coordinates);

    if (territoriesWithCoords.length > 0) {
      const latitudes = territoriesWithCoords.map(t => t.coordinates!.latitude);
      const longitudes = territoriesWithCoords.map(t => t.coordinates!.longitude);

      congregationBounds = {
        north: Math.max(...latitudes),
        south: Math.min(...latitudes),
        east: Math.max(...longitudes),
        west: Math.min(...longitudes)
      };

      // Add padding to bounds
      const latPadding = (congregationBounds.north - congregationBounds.south) * 0.1;
      const lngPadding = (congregationBounds.east - congregationBounds.west) * 0.1;

      congregationBounds.north += latPadding;
      congregationBounds.south -= latPadding;
      congregationBounds.east += lngPadding;
      congregationBounds.west -= lngPadding;
    }

    const response = {
      success: true,
      territories: processedTerritories,
      totalCount: processedTerritories.length,
      geocodedCount: processedTerritories.filter(t => t.coordinates).length,
      congregationBounds,
      metadata: {
        includeCoordinates: validatedParams.includeCoordinates,
        forceGeocode: validatedParams.forceGeocode,
        status: validatedParams.status,
        congregationId: validatedParams.congregationId
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Territory map data GET error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch territory map data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve territory map data.' },
    { status: 405 }
  );
}
