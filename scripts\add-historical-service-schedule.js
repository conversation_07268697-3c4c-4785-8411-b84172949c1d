/**
 * <PERSON><PERSON><PERSON> to add historical service schedule data for testing
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addHistoricalServiceSchedule() {
  try {
    console.log('Adding historical service schedule data...');

    // Get the congregation (assuming Coral Oeste with ID 1441)
    const congregation = await prisma.congregation.findFirst({
      where: { id: '1441' }
    });

    if (!congregation) {
      console.error('Congregation not found. Please ensure congregation 1441 exists.');
      return;
    }

    // Get some members to assign as conductors
    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        role: { in: ['elder', 'ministerial_servant'] },
        isActive: true
      },
      take: 3
    });

    if (members.length === 0) {
      console.error('No elders or ministerial servants found to assign as conductors.');
      return;
    }

    // Create a weekly schedule for last week
    const today = new Date();
    const lastWeekStart = new Date(today);
    lastWeekStart.setDate(today.getDate() - today.getDay() - 7); // Last Sunday
    const lastWeekEnd = new Date(lastWeekStart);
    lastWeekEnd.setDate(lastWeekStart.getDate() + 6); // Last Saturday

    console.log(`Creating historical schedule for week: ${lastWeekStart.toISOString().split('T')[0]} to ${lastWeekEnd.toISOString().split('T')[0]}`);

    // Create or update the weekly schedule
    const schedule = await prisma.serviceSchedule.upsert({
      where: {
        congregationId_weekStartDate: {
          congregationId: '1441',
          weekStartDate: lastWeekStart
        }
      },
      update: {
        weekEndDate: lastWeekEnd,
        updatedAt: new Date()
      },
      create: {
        congregationId: '1441',
        weekStartDate: lastWeekStart,
        weekEndDate: lastWeekEnd
      }
    });

    console.log('Historical weekly schedule created:', schedule.id);

    // Add sample service times for last week
    const serviceTimes = [
      {
        serviceDate: new Date(lastWeekStart.getTime() + 4 * 24 * 60 * 60 * 1000), // Thursday
        serviceTime: '18:15',
        location: 'Salón del Reino',
        address: '7790 West 4th Av Hialeah Fl 33014',
        conductorId: members[0]?.id
      },
      {
        serviceDate: new Date(lastWeekStart.getTime() + 4 * 24 * 60 * 60 * 1000), // Thursday
        serviceTime: '19:30',
        location: 'Salón del Reino',
        address: '7790 West 4th Av Hialeah Fl 33014',
        conductorId: members[1]?.id
      },
      {
        serviceDate: new Date(lastWeekStart.getTime() + 6 * 24 * 60 * 60 * 1000), // Saturday
        serviceTime: '09:30',
        location: 'Salón del Reino',
        address: '7790 West 4th Av Hialeah Fl 33014',
        conductorId: members[2]?.id
      }
    ];

    for (const timeData of serviceTimes) {
      const serviceTime = await prisma.serviceScheduleTime.create({
        data: {
          scheduleId: schedule.id,
          ...timeData
        },
        include: {
          conductor: {
            select: {
              id: true,
              name: true,
              role: true
            }
          }
        }
      });

      console.log(`Historical service time created: ${timeData.serviceDate.toISOString().split('T')[0]} at ${timeData.serviceTime} - ${timeData.location} (Conductor: ${serviceTime.conductor?.name || 'Sin asignar'})`);
    }

    console.log('\nHistorical service schedule data added successfully!');
    console.log('You can now test the historical view functionality.');

  } catch (error) {
    console.error('Error adding historical service schedule:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addHistoricalServiceSchedule();
