'use client';

/**
 * Congregation Analytics Dashboard
 *
 * Comprehensive analytics dashboard that provides insights into all
 * congregation activities including field service, tasks, assignments, and member engagement.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface CongregationOverview {
  congregationId: string;
  congregationName: string;
  totalMembers: number;
  activeMembers: number;
  lastUpdated: Date;
  healthScore: number;
  alerts: Alert[];
  quickInsights: QuickInsight[];
}

interface Alert {
  id: string;
  type: 'warning' | 'info' | 'critical';
  category: 'field_service' | 'tasks' | 'assignments' | 'meetings';
  title: string;
  description: string;
  actionRequired: boolean;
  createdAt: Date;
}

interface QuickInsight {
  id: string;
  category: 'field_service' | 'tasks' | 'assignments' | 'meetings' | 'general';
  title: string;
  value: string | number;
  trend: 'up' | 'down' | 'stable';
  description: string;
}

interface ActivityMetrics {
  fieldService: {
    currentMonthHours: number;
    previousMonthHours: number;
    averageHours: number;
    activePublishers: number;
    submissionRate: number;
    trend: 'up' | 'down' | 'stable';
  };
  tasks: {
    totalActiveTasks: number;
    completionRate: number;
    overdueCount: number;
    averageCompletionTime: number;
    memberParticipation: number;
    trend: 'up' | 'down' | 'stable';
  };
  assignments: {
    totalUpcomingAssignments: number;
    unassignedParts: number;
    assignmentRate: number;
    averageAssignmentsPerMember: number;
    conflictCount: number;
    trend: 'up' | 'down' | 'stable';
  };
  meetings: {
    attendanceRate: number;
    participationRate: number;
    assignmentDistribution: number;
    preparationQuality: number;
    trend: 'up' | 'down' | 'stable';
  };
}

interface MemberEngagementProfile {
  memberId: string;
  memberName: string;
  memberRole: string;
  engagementScore: number;
  lastActivity: Date;
  needsAttention: boolean;
  recommendations: string[];
}

export default function AnalyticsPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [overview, setOverview] = useState<CongregationOverview | null>(null);
  const [metrics, setMetrics] = useState<ActivityMetrics | null>(null);
  const [memberProfiles, setMemberProfiles] = useState<MemberEngagementProfile[]>([]);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  useEffect(() => {
    checkAuthentication();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      fetchAnalyticsData();
    }
  }, [user, activeTab]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();

      // Check if user has permission to access analytics
      if (!['elder', 'ministerial_servant'].includes(data.member.role)) {
        router.push('/dashboard');
        return;
      }

      setUser(data.member);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAnalyticsData = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('hermanos_token');

      // Fetch different data based on active tab
      if (activeTab === 'overview') {
        const overviewResponse = await fetch('/api/analytics?type=overview', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        if (overviewResponse.ok) {
          const overviewData = await overviewResponse.json();
          setOverview(overviewData.overview);
        }
      } else if (activeTab === 'metrics') {
        const metricsResponse = await fetch('/api/analytics?type=metrics', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json();
          setMetrics(metricsData.metrics);
        }
      } else if (activeTab === 'members') {
        const membersResponse = await fetch('/api/analytics?type=members&limit=20', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        if (membersResponse.ok) {
          const membersData = await membersResponse.json();
          setMemberProfiles(membersData.memberProfiles);
        }
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    }
  };

  const generateReport = async () => {
    if (!user) return;

    setIsGeneratingReport(true);
    try {
      const token = localStorage.getItem('hermanos_token');

      const today = new Date();
      const startDate = new Date(today.getFullYear(), today.getMonth(), 1);
      const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      const response = await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          reportType: 'monthly_comprehensive',
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          includeMembers: true,
        }),
      });

      if (response.ok) {
        const reportData = await response.json();
        // Here you would typically trigger a download or display the report
        alert('Informe generado exitosamente');
        console.log('Report data:', reportData.exportData);
      } else {
        const errorData = await response.json();
        alert(`Error al generar informe: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Error al generar el informe');
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const getHealthScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAlertTypeColor = (type: string): string => {
    switch (type) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'info': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable'): string => {
    switch (trend) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      case 'stable': return '➡️';
      default: return '➡️';
    }
  };

  const formatDate = (dateString: string | Date): string => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-blue-200 hover:text-white mb-2 flex items-center"
            >
              ← Volver al Panel
            </button>
            <h1 className="text-2xl font-bold">Análisis y Estadísticas</h1>
            <p className="text-blue-200">Información detallada de la actividad congregacional</p>
          </div>
          <div>
            <button
              onClick={generateReport}
              disabled={isGeneratingReport}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                isGeneratingReport
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-white text-blue-600 hover:bg-blue-50'
              }`}
            >
              {isGeneratingReport ? 'Generando...' : 'Generar Informe'}
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'overview', label: 'Resumen General', icon: '📊' },
              { key: 'metrics', label: 'Métricas Detalladas', icon: '📈' },
              { key: 'members', label: 'Participación de Hermanos', icon: '👥' },
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2 ${
                  activeTab === tab.key
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'overview' && overview && (
          <div className="space-y-6">
            {/* Health Score and Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Salud Congregacional</h3>
                <p className={`text-4xl font-bold ${getHealthScoreColor(overview.healthScore)}`}>
                  {overview.healthScore}%
                </p>
                <p className="text-gray-600 text-sm">Puntuación general</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Total de Hermanos</h3>
                <p className="text-4xl font-bold text-blue-600">{overview.totalMembers}</p>
                <p className="text-gray-600 text-sm">{overview.activeMembers} activos</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Alertas Activas</h3>
                <p className="text-4xl font-bold text-red-600">{overview.alerts.length}</p>
                <p className="text-gray-600 text-sm">Requieren atención</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Última Actualización</h3>
                <p className="text-lg font-semibold text-gray-900">{formatDate(overview.lastUpdated)}</p>
                <p className="text-gray-600 text-sm">Datos actualizados</p>
              </div>
            </div>

            {/* Quick Insights */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Información Rápida</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {overview.quickInsights.map((insight) => (
                  <div key={insight.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">{insight.title}</h3>
                      <span className="text-lg">{getTrendIcon(insight.trend)}</span>
                    </div>
                    <p className="text-2xl font-bold text-blue-600">{insight.value}</p>
                    <p className="text-gray-600 text-sm">{insight.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Alerts */}
            {overview.alerts.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Alertas y Notificaciones</h2>
                <div className="space-y-3">
                  {overview.alerts.map((alert) => (
                    <div key={alert.id} className={`border rounded-lg p-4 ${getAlertTypeColor(alert.type)}`}>
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold">{alert.title}</h3>
                          <p className="text-sm mt-1">{alert.description}</p>
                          <p className="text-xs mt-2 opacity-75">
                            {alert.category.replace('_', ' ')} • {formatDate(alert.createdAt)}
                          </p>
                        </div>
                        {alert.actionRequired && (
                          <span className="px-2 py-1 bg-white bg-opacity-50 rounded text-xs font-medium">
                            Acción Requerida
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'metrics' && metrics && (
          <div className="space-y-6">
            {/* Field Service Metrics */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <span>📊</span>
                Servicio del Campo
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{metrics.fieldService.currentMonthHours}</p>
                  <p className="text-gray-600 text-sm">Horas este mes</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{metrics.fieldService.activePublishers}</p>
                  <p className="text-gray-600 text-sm">Publicadores activos</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{Math.round(metrics.fieldService.submissionRate)}%</p>
                  <p className="text-gray-600 text-sm">Tasa de entrega</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">{metrics.fieldService.averageHours}</p>
                  <p className="text-gray-600 text-sm">Promedio por hermano</p>
                </div>
              </div>
            </div>

            {/* Tasks Metrics */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <span>✅</span>
                Tareas
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{metrics.tasks.totalActiveTasks}</p>
                  <p className="text-gray-600 text-sm">Tareas activas</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{Math.round(metrics.tasks.completionRate)}%</p>
                  <p className="text-gray-600 text-sm">Tasa de finalización</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{metrics.tasks.overdueCount}</p>
                  <p className="text-gray-600 text-sm">Tareas vencidas</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{metrics.tasks.memberParticipation}</p>
                  <p className="text-gray-600 text-sm">Hermanos participando</p>
                </div>
              </div>
            </div>

            {/* Assignments Metrics */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <span>📝</span>
                Asignaciones
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{metrics.assignments.totalUpcomingAssignments}</p>
                  <p className="text-gray-600 text-sm">Asignaciones próximas</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{metrics.assignments.unassignedParts}</p>
                  <p className="text-gray-600 text-sm">Partes sin asignar</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{Math.round(metrics.assignments.assignmentRate)}%</p>
                  <p className="text-gray-600 text-sm">Tasa de asignación</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{metrics.assignments.averageAssignmentsPerMember}</p>
                  <p className="text-gray-600 text-sm">Promedio por hermano</p>
                </div>
              </div>
            </div>

            {/* Meetings Metrics */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <span>🏛️</span>
                Reuniones
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{Math.round(metrics.meetings.attendanceRate)}%</p>
                  <p className="text-gray-600 text-sm">Asistencia promedio</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{Math.round(metrics.meetings.participationRate)}%</p>
                  <p className="text-gray-600 text-sm">Participación</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{Math.round(metrics.meetings.assignmentDistribution)}%</p>
                  <p className="text-gray-600 text-sm">Distribución de partes</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">{Math.round(metrics.meetings.preparationQuality)}%</p>
                  <p className="text-gray-600 text-sm">Calidad de preparación</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'members' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Participación de Hermanos</h2>
            {memberProfiles.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No hay datos de participación disponibles.</p>
            ) : (
              <div className="space-y-4">
                {memberProfiles.map((profile) => (
                  <div key={profile.memberId} className={`border rounded-lg p-4 ${
                    profile.needsAttention ? 'border-red-200 bg-red-50' : 'border-gray-200'
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h3 className="font-semibold text-gray-900">{profile.memberName}</h3>
                        <p className="text-sm text-gray-600">{profile.memberRole}</p>
                      </div>
                      <div className="text-right">
                        <p className={`text-2xl font-bold ${
                          profile.engagementScore >= 80 ? 'text-green-600' :
                          profile.engagementScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {profile.engagementScore}%
                        </p>
                        <p className="text-sm text-gray-600">Participación</p>
                      </div>
                    </div>

                    {profile.needsAttention && (
                      <div className="mt-3 p-2 bg-yellow-100 rounded-md">
                        <p className="text-sm font-medium text-yellow-800">Necesita atención</p>
                        <ul className="text-sm text-yellow-700 mt-1">
                          {profile.recommendations.map((rec, index) => (
                            <li key={index}>• {rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
