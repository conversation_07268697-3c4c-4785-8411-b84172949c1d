const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addCoordinatorRole() {
  try {
    console.log('👑 Adding coordinator role to roles table...\n');

    // Check if coordinator role already exists
    const existingRole = await prisma.role.findFirst({
      where: {
        name: 'coordinator',
      },
    });

    if (existingRole) {
      console.log('✅ Coordinator role already exists!');
      console.log(`📋 Role ID: ${existingRole.id}`);
      return existingRole;
    }

    // Add coordinator role
    const coordinatorRole = await prisma.role.create({
      data: {
        name: 'coordinator',
        description: 'Coordinador del Cuerpo de Ancianos',
        permissions: [
          'MANAGE_MEMBERS',
          'MANAGE_MEETINGS',
          'MANAGE_ASSIGNMENTS',
          'MANAGE_FIELD_SERVICE',
          'MANAGE_LETTERS',
          'MANAGE_EVENTS',
          'MANAGE_TASKS',
          'VIEW_ANALYTICS',
          'MANAGE_SETTINGS',
          'DELEGATE_PERMISSIONS',
        ],
        isActive: true,
      },
    });

    console.log('✅ Coordinator role created successfully!');
    console.log(`📋 Role ID: ${coordinatorRole.id}`);
    console.log(`📝 Description: ${coordinatorRole.description}`);
    console.log(`🔐 Permissions: ${coordinatorRole.permissions.length} permissions`);

    return coordinatorRole;

  } catch (error) {
    console.error('❌ Error adding coordinator role:', error);
    throw error;
  }
}

async function createCoordinatorUser() {
  try {
    // First ensure the coordinator role exists
    await addCoordinatorRole();

    console.log('\n👤 Creating coordinator user...');

    const bcrypt = require('bcryptjs');
    const hashedPin = await bcrypt.hash('1234', 10);

    // Create coordinator user
    const coordinator = await prisma.member.create({
      data: {
        congregationId: '1441',
        name: 'Carlos Coordinador',
        email: '<EMAIL>',
        phone: '+****************',
        address: '456 Leadership Ave, Coral Oeste, FL 33101',
        birthDate: new Date('1975-03-20'),
        role: 'coordinator',
        serviceGroup: 'Grupo 1',
        pin: hashedPin,
        isActive: true,
        preferences: {
          language: 'es',
          notifications: true,
        },
        contactPreferences: {
          preferredMethod: 'email',
          allowEmergencyContact: true,
          privacyLevel: 'elders_only',
        },
        qualifications: [
          'Coordinador del Cuerpo de Ancianos',
          'Superintendente de Escuela',
          'Oración',
          'Discurso',
          'Presidente',
          'Lector'
        ],
        notes: 'Coordinador del cuerpo de ancianos. Acceso completo a funciones administrativas.',
      },
    });

    console.log('✅ Coordinator user created successfully!');
    console.log(`📧 Email: ${coordinator.email}`);
    console.log(`👤 Name: ${coordinator.name}`);
    console.log(`🔑 PIN: 1234`);
    console.log(`🏢 Role: ${coordinator.role}`);
    console.log(`🆔 ID: ${coordinator.id}`);

    console.log('\n🧪 Testing Login Credentials:');
    console.log('🌐 URL: http://localhost:3001/login');
    console.log('🏛️ Congregation ID: 1441');
    console.log('🔐 PIN: 1234');
    console.log('📧 User: Carlos Coordinador');

  } catch (error) {
    if (error.code === 'P2002') {
      console.log('⚠️ User already exists with this email.');
    } else {
      console.error('❌ Error creating coordinator user:', error);
    }
  }
}

async function main() {
  try {
    await createCoordinatorUser();
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
