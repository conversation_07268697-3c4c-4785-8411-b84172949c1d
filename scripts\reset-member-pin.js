#!/usr/bin/env node

/**
 * Reset Member PIN
 * 
 * This script resets a specific member's PIN and tests member-specific authentication
 * to verify that the profile dropdown displays correctly for individual member access.
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

class MemberPinResetter {
  constructor() {
    this.prisma = new PrismaClient();
  }

  async run() {
    try {
      console.log('🔑 Resetting Member PIN and Testing Authentication...\n');

      // Step 1: Find and display the member
      await this.findMember();

      // Step 2: Reset the PIN
      await this.resetPin();

      // Step 3: Test member-specific authentication
      await this.testMemberAuthentication();

      // Step 4: Compare with congregation PIN authentication
      await this.compareCongregationPinAuth();

      console.log('\n✅ PIN reset and authentication testing completed!');

    } catch (error) {
      console.error('❌ Error during PIN reset:', error);
      throw error;
    } finally {
      await this.prisma.$disconnect();
    }
  }

  async findMember() {
    console.log('1️⃣ Finding Member...');
    
    const member = await this.prisma.member.findUnique({
      where: { id: '1' },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        congregationId: true,
        isActive: true,
        lastLogin: true
      }
    });

    if (!member) {
      throw new Error('Member with ID "1" not found');
    }

    console.log('   📋 Member Details:');
    console.log(`      ID: ${member.id}`);
    console.log(`      Name: ${member.name}`);
    console.log(`      Email: ${member.email}`);
    console.log(`      Role: ${member.role}`);
    console.log(`      Congregation: ${member.congregationId}`);
    console.log(`      Active: ${member.isActive}`);
    console.log(`      Last Login: ${member.lastLogin || 'Never'}`);

    this.member = member;
  }

  async resetPin() {
    console.log('\n2️⃣ Resetting PIN...');
    
    const newPin = '5455';
    const hashedPin = await bcrypt.hash(newPin, 12);
    
    console.log(`   🔐 New PIN: ${newPin}`);
    console.log(`   🔒 Hashed PIN: ${hashedPin.substring(0, 20)}...`);

    await this.prisma.member.update({
      where: { id: '1' },
      data: { 
        pin: hashedPin,
        updatedAt: new Date()
      }
    });

    console.log('   ✅ PIN updated successfully');

    // Verify the PIN was set correctly
    const updatedMember = await this.prisma.member.findUnique({
      where: { id: '1' },
      select: { pin: true }
    });

    const pinMatches = await bcrypt.compare(newPin, updatedMember.pin);
    if (pinMatches) {
      console.log('   ✅ PIN verification successful');
    } else {
      throw new Error('PIN verification failed');
    }
  }

  async testMemberAuthentication() {
    console.log('\n3️⃣ Testing Member-Specific Authentication...');

    try {
      // Test member login (this would be a different endpoint than congregation login)
      // For now, we'll simulate what member-specific login should look like
      console.log('   🔍 Member-specific login simulation:');
      console.log(`      Member ID: ${this.member.id}`);
      console.log(`      Member Name: ${this.member.name}`);
      console.log(`      Member Role: ${this.member.role}`);
      console.log(`      PIN: 5455 (newly set)`);
      
      // Verify PIN works
      const member = await this.prisma.member.findUnique({
        where: { id: '1' },
        select: { pin: true, name: true, role: true, congregationId: true }
      });

      const pinValid = await bcrypt.compare('5455', member.pin);
      
      if (pinValid) {
        console.log('   ✅ Member PIN authentication would succeed');
        
        // Show what the profile dropdown SHOULD display for member-specific access
        console.log('\n   📋 Expected Profile Dropdown for Member Access:');
        console.log(`      👤 Name: ${member.name}`);
        console.log(`      🏷️  Role: ${this.getRoleDisplayName(member.role)}`);
        console.log(`      🏛️  Congregation: Coral Oeste`);
        console.log(`      🚪 Logout Option: Cerrar Sesión`);
        console.log('   ');
        console.log('   ❌ Should NOT show:');
        console.log('      - "Acceso con PIN de Congregación"');
        console.log('      - Only congregation name without member info');
        
      } else {
        console.log('   ❌ Member PIN authentication would fail');
      }

    } catch (error) {
      console.error('   ❌ Member authentication test failed:', error.message);
    }
  }

  async compareCongregationPinAuth() {
    console.log('\n4️⃣ Comparing with Congregation PIN Authentication...');

    try {
      // Test congregation PIN login
      const response = await fetch('http://localhost:3001/api/auth/congregation-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          congregationId: '1441',
          pin: '1930',
          rememberMe: false,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        
        console.log('   ✅ Congregation PIN authentication successful');
        console.log('\n   📋 Congregation PIN Profile Dropdown (Current):');
        console.log(`      🏛️  Congregation: ${data.congregation.name}`);
        console.log('      🔑 Access Type: Acceso con PIN de Congregación');
        console.log('      🚪 Logout Option: Cerrar Sesión');
        console.log(`      🔍 hasCongregationPinAccess: ${data.user.hasCongregationPinAccess}`);
        
        console.log('\n   🔄 Key Differences:');
        console.log('      Member Access:');
        console.log('        - Shows member name and role');
        console.log('        - Shows congregation name');
        console.log('        - hasCongregationPinAccess: false (or undefined)');
        console.log('      ');
        console.log('      Congregation PIN Access:');
        console.log('        - Shows only congregation name');
        console.log('        - Shows "Acceso con PIN de Congregación"');
        console.log('        - hasCongregationPinAccess: true');

      } else {
        console.log('   ❌ Congregation PIN authentication failed');
      }

    } catch (error) {
      console.log(`   ⚠️  Congregation PIN test skipped (server not running): ${error.message}`);
    }
  }

  getRoleDisplayName(role) {
    const roleNames = {
      'publisher': 'Publicador',
      'ministerial_servant': 'Siervo Ministerial',
      'elder': 'Anciano',
      'coordinator': 'Coordinador',
      'overseer_coordinator': 'Superintendente/Coordinador',
      'developer': 'Desarrollador',
    };
    return roleNames[role] || role;
  }
}

// Additional function to create member-specific login endpoint test
async function testMemberSpecificLogin() {
  console.log('\n5️⃣ Testing Member-Specific Login Endpoint...');
  
  try {
    // This would test a member-specific login endpoint if it exists
    // For now, we'll note what needs to be implemented
    console.log('   📝 Note: Member-specific login endpoint may need to be implemented');
    console.log('   🎯 Required endpoint: POST /api/auth/member-login');
    console.log('   📋 Expected payload:');
    console.log('      {');
    console.log('        "congregationId": "1441",');
    console.log('        "memberId": "1",');
    console.log('        "pin": "5455"');
    console.log('      }');
    console.log('   ');
    console.log('   📋 Expected response:');
    console.log('      {');
    console.log('        "success": true,');
    console.log('        "token": "jwt_token",');
    console.log('        "user": {');
    console.log('          "id": "1",');
    console.log('          "name": "Richard Rubi",');
    console.log('          "role": "overseer_coordinator",');
    console.log('          "congregationId": "1441",');
    console.log('          "congregationName": "Coral Oeste",');
    console.log('          "hasCongregationPinAccess": false');
    console.log('        }');
    console.log('      }');

  } catch (error) {
    console.error('   ❌ Member-specific login test failed:', error.message);
  }
}

// Run the PIN reset and testing
if (require.main === module) {
  const resetter = new MemberPinResetter();
  resetter.run()
    .then(() => testMemberSpecificLogin())
    .catch(console.error);
}

module.exports = MemberPinResetter;
