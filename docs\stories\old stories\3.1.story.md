# Story 3.1: Midweek Meeting Management System

## Status

Draft

## Story

**As a** meeting coordinator and elder,
**I want** to manage midweek meetings with Life and Ministry Meeting Workbook integration,
**so that** I can assign parts, track attendance, and maintain the exact workflow we currently use for congregation meeting coordination.

## Acceptance Criteria

1. **Meeting Creation and Scheduling (UI Reference: Administración-de-Reuniones-Entre-Semana.png)**
   - I can create midweek meetings for specific dates using the exact interface shown in reference images
   - I can set meeting location (Kingdom Hall or Zoom with link) following the location selection pattern
   - I can assign chairman, opening prayer, and closing prayer using dropdown member selection
   - I can add meeting notes and special announcements in the notes section
   - Interface follows the card-based layout with Spanish-first terminology

2. **Life and Ministry Meeting Parts Management (UI Reference: Administración-de-Reuniones-Entre-Semana-Assign.png)**
   - I can add meeting parts following the standard workbook structure shown in assignment interface
   - I can assign members to different parts (student, assistant, conductor) using the assignment modal pattern
   - I can set time allocations for each part following the time display format
   - I can track part completion status during meetings with checkbox/status indicators
   - Assignment interface uses the modal overlay pattern shown in reference images

3. **Member Assignment Interface (UI Reference: Administración-de-Reuniones-configuracion-miembros.png)**
   - I can select from active congregation members using the member selection interface shown
   - I can filter members by role and availability following the filtering pattern
   - I can assign multiple roles per member using the role assignment interface
   - I can view member assignment history in the member management view
   - Interface follows the tabbed layout pattern for member configuration

4. **Meeting Display and Navigation (UI Reference: Reunion-Entre-Semana.png)**
   - I can view meetings in the card-based format shown in member-facing interface
   - I can see upcoming meetings with assigned parts following the meeting card layout
   - I can edit existing meetings using the administrative interface pattern
   - I can mark meetings as published/unpublished with toggle controls
   - Navigation follows the breadcrumb and back button patterns shown

5. **JW.org Integration Preservation (UI Reference: Programas-Salón-Del-Reino.png)**
   - The system maintains existing JW.org data fetching patterns shown in programs interface
   - Song numbers are automatically resolved to titles following the song display format
   - Meeting workbook data is cached and synchronized as shown in current implementation
   - All existing URL patterns and caching mechanisms are preserved
   - Song integration follows the format shown in meeting programs

6. **Mobile-Responsive Interface (UI Reference: Member Area interfaces)**
   - All meeting management functions work on mobile devices following responsive patterns
   - Touch-friendly interface for part assignments using mobile-optimized controls
   - Optimized layout for tablet and phone screens as shown in member area images
   - Quick access to meeting information during actual meetings with simplified mobile view
   - Follows the mobile navigation patterns shown in member area screenshots

7. **Permission-Based Access and Spanish-First Design**
   - Only elders and ministerial servants can manage meetings following admin access patterns
   - Publishers can view their assigned parts in the member-facing interface shown
   - Coordinators have full meeting management access through admin dashboard
   - Proper congregation isolation for multi-tenant security
   - All text, labels, and terminology in Spanish following the reference images exactly

## Tasks

- [ ] Create midweek meeting management database integration (AC: 1, 4)
  - [ ] Implement meeting CRUD operations using existing schema
  - [ ] Create meeting parts management with proper relationships
  - [ ] Add meeting location and Zoom link management
  - [ ] Implement meeting publication status tracking
  - [ ] Add congregation isolation and permission validation
  - [ ] Create meeting date validation and conflict detection

- [ ] Build meeting parts assignment system (AC: 2, 3)
  - [ ] Create part type definitions following workbook structure
  - [ ] Implement member assignment interface with role filtering
  - [ ] Add time allocation and duration tracking
  - [ ] Create part completion status management
  - [ ] Implement assignment history tracking for fair distribution
  - [ ] Add bulk assignment and template functionality

- [ ] Develop meeting management UI components following exact reference designs (AC: 4, 6)
  - [ ] Create meeting calendar view matching the layout shown in Administración-de-Reuniones-Entre-Semana.png
  - [ ] Build meeting creation and editing forms following the card-based design pattern
  - [ ] Implement part assignment interface matching Administración-de-Reuniones-Entre-Semana-Assign.png modal pattern
  - [ ] Create meeting display cards following the format shown in Reunion-Entre-Semana.png
  - [ ] Add mobile-responsive design matching Member Area interface patterns
  - [ ] Implement touch-friendly assignment controls following mobile UI patterns shown

- [ ] Integrate with existing JW.org systems (AC: 5)
  - [ ] Preserve existing song catalog integration
  - [ ] Maintain current JW.org data fetching patterns
  - [ ] Implement workbook data synchronization
  - [ ] Add song number to title resolution
  - [ ] Preserve existing caching mechanisms
  - [ ] Ensure URL pattern compatibility

- [ ] Implement permission and access control (AC: 7)
  - [ ] Add role-based meeting management access
  - [ ] Implement congregation isolation for multi-tenant security
  - [ ] Create member assignment permission validation
  - [ ] Add meeting visibility controls based on user role
  - [ ] Implement audit trail for meeting changes
  - [ ] Add proper authentication middleware for all endpoints

- [ ] Create meeting workflow and navigation (AC: 1, 4)
  - [ ] Build meeting list view with filtering and search
  - [ ] Implement meeting duplication for recurring patterns
  - [ ] Add meeting template system for common structures
  - [ ] Create meeting archive and history functionality
  - [ ] Implement meeting reminder and notification system
  - [ ] Add meeting export and printing capabilities

## UI/UX Compliance Requirements

### Visual Design Compliance
- **Exact Color Scheme**: Match the color palette shown in reference images (blues, greens, grays)
- **Typography**: Use the same font family, sizes, and weights shown in admin interfaces
- **Card Layout**: Implement the exact card-based layout pattern with rounded corners and shadows
- **Button Styles**: Match button designs, colors, and hover states from reference images
- **Form Controls**: Use identical input field styles, dropdowns, and form layouts

### Spanish-First Interface Requirements
- **Terminology**: Use exact Spanish terms shown in reference images:
  - "Administración de Reuniones Entre Semana" for midweek meeting management
  - "Asignaciones" for assignments
  - "Configuración" for settings
  - "Miembros" for members
  - "Reunión Entre Semana" for midweek meeting display
- **Date/Time Format**: Follow Spanish date formatting shown in interfaces
- **Navigation Labels**: Match navigation terminology exactly as shown

### Layout Pattern Compliance
- **Admin Interface**: Follow the tabbed layout pattern shown in Administración-de-Reuniones-Entre-Semana-Configuracion.png
- **Assignment Modal**: Implement the modal overlay pattern from Administración-de-Reuniones-Entre-Semana-Assign.png
- **Member Selection**: Use the member selection interface pattern from configuracion-miembros.png
- **Card Display**: Match the meeting card layout from Reunion-Entre-Semana.png
- **Mobile Layout**: Follow the responsive patterns shown in Member Area images

### Interactive Element Requirements
- **Modal Behavior**: Assignment modals should open/close following the pattern shown
- **Dropdown Menus**: Member selection dropdowns should match the style and behavior
- **Toggle Controls**: Publication status toggles should match existing design
- **Navigation**: Back buttons and breadcrumbs should follow the established pattern

## Technical Requirements

### Database Schema
- Utilize existing `midweek_meetings` and `midweek_meeting_parts` tables
- Maintain proper foreign key relationships with members and congregations
- Implement proper indexing for date-based queries and member lookups
- Add validation constraints for meeting dates and part assignments

### API Design
- RESTful endpoints following existing patterns: `/api/meetings/midweek`
- Proper authentication middleware using existing JWT system
- Congregation-scoped queries for multi-tenant isolation
- Batch operations for multiple part assignments
- Real-time updates for collaborative meeting planning

### Frontend Architecture
- React components following existing design patterns
- Mobile-first responsive design using Tailwind CSS
- State management for meeting data and assignments
- Form validation and error handling
- Integration with existing dashboard navigation

### JW.org Integration
- Preserve existing song catalog fetching mechanisms
- Maintain current caching strategies for workbook data
- Ensure compatibility with existing URL patterns
- Implement graceful fallbacks for offline scenarios

## Definition of Done

- [ ] All meeting CRUD operations work correctly with proper validation
- [ ] Part assignment system allows flexible member assignment and tracking
- [ ] Mobile-responsive interface works on all device sizes
- [ ] JW.org integration maintains existing functionality without changes
- [ ] Permission system properly restricts access based on user roles
- [ ] **UI Compliance**: All components match reference images pixel-perfect
  - [ ] Admin interface matches Administración-de-Reuniones-Entre-Semana.png layout exactly
  - [ ] Assignment modal matches Administración-de-Reuniones-Entre-Semana-Assign.png pattern
  - [ ] Member interface matches Reunion-Entre-Semana.png card layout
  - [ ] Mobile interface matches Member Area responsive patterns
- [ ] **Spanish Localization**: All text matches reference image terminology exactly
- [ ] **Visual Design**: Colors, typography, and spacing match reference images
- [ ] Meeting data is properly isolated by congregation for multi-tenant security
- [ ] Performance is optimized for large numbers of meetings and assignments
- [ ] Comprehensive error handling and user feedback is implemented
- [ ] Integration tests verify meeting workflow from creation to completion
- [ ] **User Acceptance**: Interface feels identical to existing system for users

## Dependencies

- Existing authentication system (Stories 1.3, 2.1)
- Member management system (Story 2.2)
- Database schema (midweek_meetings, midweek_meeting_parts tables)
- JW.org integration patterns (existing song catalog system)
- Admin dashboard framework (Story 1.4)

## Notes

- This story focuses on preserving the exact meeting management workflow currently used
- JW.org integration must work identically to avoid disrupting congregation processes
- The interface should feel familiar to users of the existing system
- Meeting coordination is a weekly critical activity that must be seamless
- Consider the collaborative nature of meeting planning with multiple coordinators

## Dev Agent Record

### Agent Model Used

_To be populated by development agent_

### Debug Log References

_To be populated by development agent_

### Completion Notes List

_To be populated by development agent_

### File List

_To be populated by development agent_

### Change Log

_To be populated by development agent_
