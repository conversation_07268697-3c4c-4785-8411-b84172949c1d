#!/usr/bin/env node

/**
 * Dashboard Test Script for Hermanos App
 *
 * Tests the dashboard API endpoint, role-based access control,
 * and data aggregation functionality.
 *
 * Usage: node scripts/test-dashboard.js
 */

const { PrismaClient } = require('@prisma/client');

class DashboardTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = [];
  }

  addTestResult(testName, success, message) {
    this.testResults.push({
      test: testName,
      success,
      message,
    });

    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  async testDashboardDataAggregation() {
    console.log('\n📊 Testing Dashboard Data Aggregation...');

    try {
      // Get a test congregation and member
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      if (!congregation) {
        this.addTestResult('Congregation Lookup', false, 'No active congregation found');
        return false;
      }

      const member = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
      });

      if (!member) {
        this.addTestResult('Member Lookup', false, 'No active member found');
        return false;
      }

      this.addTestResult('Test Data Setup', true, `Using congregation ${congregation.name} and member ${member.name}`);

      // Test task counting
      const pendingTasks = await this.prisma.taskAssignment.count({
        where: {
          congregationId: congregation.id,
          assignedMemberId: member.id,
          status: 'pending',
        },
      });

      this.addTestResult('Task Counting', true, `Found ${pendingTasks} pending tasks`);

      // Test service record lookup
      const recentServiceReport = await this.prisma.fieldServiceRecord.findFirst({
        where: {
          congregationId: congregation.id,
          memberId: member.id,
        },
        orderBy: {
          serviceMonth: 'desc',
        },
      });

      this.addTestResult('Service Record Lookup', true,
        recentServiceReport ? 'Found recent service record' : 'No service records found');

      // Test meeting counting
      const upcomingMeetings = await this.prisma.midweekMeeting.count({
        where: {
          congregationId: congregation.id,
          meetingDate: {
            gte: new Date(),
            lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          },
          isActive: true,
        },
      });

      this.addTestResult('Meeting Counting', true, `Found ${upcomingMeetings} upcoming meetings`);

      // Test admin data (for non-publisher roles)
      if (member.role !== 'publisher') {
        const totalMembers = await this.prisma.member.count({
          where: {
            congregationId: congregation.id,
            isActive: true,
          },
        });

        const totalTasks = await this.prisma.task.count({
          where: {
            congregationId: congregation.id,
            isActive: true,
          },
        });

        this.addTestResult('Admin Data Access', true,
          `Admin can see ${totalMembers} members and ${totalTasks} tasks`);
      } else {
        this.addTestResult('Publisher Data Restriction', true,
          'Publisher role correctly restricted from admin data');
      }

      return true;
    } catch (error) {
      this.addTestResult('Dashboard Data Aggregation', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testRoleBasedDataAccess() {
    console.log('\n🔐 Testing Role-Based Data Access...');

    try {
      // Get members with different roles
      const members = await this.prisma.member.findMany({
        where: { isActive: true },
        include: {
          congregation: true,
        },
      });

      if (members.length === 0) {
        this.addTestResult('Member Data', false, 'No active members found');
        return false;
      }

      // Test access for different roles
      const roleTests = {};

      for (const member of members) {
        if (!roleTests[member.role]) {
          roleTests[member.role] = {
            canAccessAdmin: ['elder', 'overseer_coordinator', 'developer', 'ministerial_servant'].includes(member.role),
            canManageSettings: ['elder', 'overseer_coordinator', 'developer'].includes(member.role),
            member: member,
          };
        }
      }

      for (const [role, test] of Object.entries(roleTests)) {
        this.addTestResult(`Role ${role} Admin Access`,
          test.canAccessAdmin || role === 'publisher',
          test.canAccessAdmin ? 'Has admin access' : 'No admin access (correct for publisher)');

        const shouldHaveSettings = ['elder', 'overseer_coordinator', 'developer'].includes(role);
        const hasCorrectSettingsAccess = test.canManageSettings === shouldHaveSettings;
        this.addTestResult(`Role ${role} Settings Access`, hasCorrectSettingsAccess,
          test.canManageSettings ? 'Can manage settings' : 'Cannot manage settings (correct)');
      }

      return true;
    } catch (error) {
      this.addTestResult('Role-Based Data Access', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testCongregationIsolation() {
    console.log('\n🏛️ Testing Congregation Isolation...');

    try {
      const congregations = await this.prisma.congregation.findMany({
        where: { isActive: true },
      });

      if (congregations.length === 0) {
        this.addTestResult('Congregation Data', false, 'No active congregations found');
        return false;
      }

      const testCongregation = congregations[0];

      // Test that queries are properly isolated by congregation
      const congregationMembers = await this.prisma.member.findMany({
        where: {
          congregationId: testCongregation.id,
          isActive: true,
        },
      });

      const congregationTasks = await this.prisma.task.findMany({
        where: {
          congregationId: testCongregation.id,
          isActive: true,
        },
      });

      this.addTestResult('Congregation Member Isolation', true,
        `Found ${congregationMembers.length} members for congregation ${testCongregation.id}`);

      this.addTestResult('Congregation Task Isolation', true,
        `Found ${congregationTasks.length} tasks for congregation ${testCongregation.id}`);

      // Verify no cross-congregation data leakage
      const allMembers = await this.prisma.member.count({
        where: { isActive: true },
      });

      const isolationWorking = congregationMembers.length <= allMembers;
      this.addTestResult('Data Isolation Verification', isolationWorking,
        isolationWorking ? 'No data leakage detected' : 'Potential data leakage detected');

      return true;
    } catch (error) {
      this.addTestResult('Congregation Isolation', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testDashboardPerformance() {
    console.log('\n⚡ Testing Dashboard Performance...');

    try {
      const startTime = Date.now();

      // Simulate the dashboard API queries
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      if (!congregation) {
        this.addTestResult('Performance Test Setup', false, 'No congregation found');
        return false;
      }

      const member = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
      });

      if (!member) {
        this.addTestResult('Performance Test Setup', false, 'No member found');
        return false;
      }

      // Run all dashboard queries in parallel (like the API does)
      await Promise.all([
        this.prisma.taskAssignment.count({
          where: {
            congregationId: congregation.id,
            assignedMemberId: member.id,
            status: 'pending',
          },
        }),
        this.prisma.fieldServiceRecord.findFirst({
          where: {
            congregationId: congregation.id,
            memberId: member.id,
          },
          orderBy: {
            serviceMonth: 'desc',
          },
        }),
        this.prisma.midweekMeeting.count({
          where: {
            congregationId: congregation.id,
            meetingDate: {
              gte: new Date(),
              lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            },
            isActive: true,
          },
        }),
      ]);

      const endTime = Date.now();
      const duration = endTime - startTime;

      const isPerformant = duration < 1000; // Should complete in under 1 second
      this.addTestResult('Dashboard Query Performance', isPerformant,
        `Queries completed in ${duration}ms ${isPerformant ? '(good)' : '(slow)'}`);

      return isPerformant;
    } catch (error) {
      this.addTestResult('Dashboard Performance', false, `Error: ${error.message}`);
      return false;
    }
  }

  generateReport() {
    console.log('\n📋 Dashboard Test Report');
    console.log('=========================');

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.test}: ${r.message}`));
    }

    const allPassed = failedTests === 0;
    if (allPassed) {
      console.log('\n🎉 All dashboard tests passed!');
      console.log('\n📝 Dashboard is ready for use:');
      console.log('1. Start the development server: npm run dev');
      console.log('2. Login at http://localhost:3001/login');
      console.log('3. Access dashboard with role-based features');
      console.log('4. Test admin access (for elders/admins only)');
    } else {
      console.log('\n⚠️ Some dashboard tests failed!');
    }

    return allPassed;
  }

  async run() {
    try {
      console.log('🚀 Starting dashboard functionality tests...');

      await this.prisma.$connect();
      console.log('✅ Database connection established');

      // Run all tests
      await this.testDashboardDataAggregation();
      await this.testRoleBasedDataAccess();
      await this.testCongregationIsolation();
      await this.testDashboardPerformance();

      // Generate report
      const success = this.generateReport();

      if (!success) {
        process.exit(1);
      }

    } catch (error) {
      console.error('\n💥 Test execution failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new DashboardTester();
  tester.run().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = DashboardTester;
