#!/usr/bin/env node

/**
 * Check PINs
 * 
 * This script checks the actual PINs stored in the database.
 */

const { Client } = require('pg');
const bcrypt = require('bcryptjs');

async function checkPins() {
    console.log('🔍 CHECKING PINS IN DATABASE...');
    console.log('');

    let client;

    try {
        // 1. Connect to database
        console.log('🔌 Connecting to database...');
        client = new Client({
            host: 'localhost',
            port: 5432,
            user: 'mywebsites',
            password: 'password',
            database: 'hermanos'
        });

        await client.connect();
        console.log('✅ Database connection successful');

        // 2. Check congregation PIN
        console.log('📋 Checking congregation PIN...');
        const congregation = await client.query('SELECT id, name, pin FROM congregations WHERE id = $1;', ['1441']);
        
        if (congregation.rows.length > 0) {
            const congPin = congregation.rows[0].pin;
            console.log(`   Congregation: ${congregation.rows[0].name}`);
            console.log(`   Stored PIN hash: ${congPin.substring(0, 20)}...`);
            
            // Test common PINs
            const testPins = ['123456', '000000', '1441', 'coraleste', 'admin'];
            
            console.log('   Testing common PINs:');
            for (const testPin of testPins) {
                const isMatch = await bcrypt.compare(testPin, congPin);
                console.log(`   - ${testPin}: ${isMatch ? '✅ MATCH' : '❌ No match'}`);
                
                if (isMatch) {
                    console.log(`   🎉 FOUND CONGREGATION PIN: ${testPin}`);
                    break;
                }
            }
        }

        // 3. Check member PINs
        console.log('👥 Checking member PINs...');
        const members = await client.query('SELECT id, name, email, pin FROM members WHERE congregation_id = $1 AND is_active = true LIMIT 3;', ['1441']);
        
        for (const member of members.rows) {
            console.log(`   Member: ${member.name} (${member.email})`);
            console.log(`   Stored PIN hash: ${member.pin.substring(0, 20)}...`);
            
            // Test common PINs
            const testPins = ['123456', '000000', '1234', 'admin'];
            
            for (const testPin of testPins) {
                const isMatch = await bcrypt.compare(testPin, member.pin);
                if (isMatch) {
                    console.log(`   🎉 FOUND MEMBER PIN: ${testPin}`);
                    break;
                }
            }
        }

        // 4. Try to set a known PIN for testing
        console.log('🔧 Setting known PIN for testing...');
        const newPinHash = await bcrypt.hash('123456', 10);
        
        await client.query('UPDATE congregations SET pin = $1 WHERE id = $2;', [newPinHash, '1441']);
        console.log('   ✅ Congregation PIN updated to: 123456');
        
        // Update a member PIN too
        const testMember = members.rows[0];
        if (testMember) {
            await client.query('UPDATE members SET pin = $1 WHERE id = $2;', [newPinHash, testMember.id]);
            console.log(`   ✅ Member PIN updated for ${testMember.name}: 123456`);
        }

        console.log('');
        console.log('🎯 TESTING CREDENTIALS:');
        console.log('   Congregation ID: 1441');
        console.log('   Congregation PIN: 123456');
        if (testMember) {
            console.log(`   Test Member: ${testMember.email}`);
            console.log('   Member PIN: 123456');
        }

        // 5. Test the API again
        console.log('🌐 Testing API with updated PIN...');
        try {
            const response = await fetch('http://localhost:3000/api/auth/congregation-login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    congregationId: '1441',
                    pin: '123456'
                })
            });

            const result = await response.json();
            console.log(`   Response status: ${response.status}`);
            
            if (response.ok) {
                console.log('   ✅ Congregation authentication successful!');
                console.log(`   Token received: ${result.token ? 'Yes' : 'No'}`);
            } else {
                console.log('   ❌ Authentication failed:');
                console.log(`   Error: ${result.error}`);
            }

        } catch (error) {
            console.log(`   ❌ API test failed: ${error.message}`);
        }

    } catch (error) {
        console.error('❌ Error during PIN check:', error);
    } finally {
        if (client) {
            await client.end();
        }
    }
}

// Run the check
checkPins();
