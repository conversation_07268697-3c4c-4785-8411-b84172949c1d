'use client';

/**
 * Admin Events Management Page
 *
 * Administrative interface for managing congregation events and activities.
 * Allows creating, editing, and coordinating events for authorized users.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  EventData,
  EventCategory,
  EventVisibility,
  EVENT_CATEGORY_LABELS,
  EVENT_VISIBILITY_LABELS,
  CreateEventData
} from '@/lib/services/eventManagementService';
import AdminFooter from '@/components/admin/AdminFooter';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface EventSummary {
  totalEvents: number;
  upcomingEvents: number;
  eventsByCategory: Record<EventCategory, number>;
  nextEvent?: EventData;
}

export default function AdminEventsPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [events, setEvents] = useState<EventData[]>([]);
  const [summary, setSummary] = useState<EventSummary | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState<EventData | null>(null);
  const [isLoadingEvents, setIsLoadingEvents] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showStatistics, setShowStatistics] = useState(false);

  // Form state
  const [formData, setFormData] = useState<Partial<CreateEventData>>({
    title: '',
    description: '',
    eventDate: new Date(),
    startTime: '',
    endTime: '',
    location: '',
    category: EventCategory.OTHER,
    isAllDay: false,
    isRecurring: false,
    recurrenceRule: '',
    visibility: EventVisibility.ALL_MEMBERS,
  });

  useEffect(() => {
    checkAdminAccess();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      loadEventsData();
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAdminAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      // Use dashboard API to get user data (same pattern as other working pages)
      const response = await fetch('/api/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token is invalid, redirect to login
          localStorage.removeItem('hermanos_token');
          localStorage.removeItem('hermanos_user');
          localStorage.removeItem('hermanos_congregation');
          localStorage.removeItem('hermanos_permissions');
          router.push('/login');
          return;
        }
        throw new Error('Failed to load user data');
      }

      const data = await response.json();

      // Check if user has permission to access admin events
      // Use the permissions system from dashboard API which already handles all admin roles
      if (!data.permissions?.canAccessAdmin) {
        router.push('/dashboard');
        return;
      }

      // Set user data from dashboard response
      setUser({
        id: data.user.id,
        name: data.user.name,
        role: data.user.role,
        congregationId: data.user.congregationId,
        congregationName: data.congregation.name,
      });
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const loadEventsData = async () => {
    if (!user) return;

    setIsLoadingEvents(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        setError('No hay token de autenticación');
        return;
      }

      // Load summary and events
      const [summaryResponse, eventsResponse] = await Promise.all([
        fetch('/api/events/summary', {
          headers: { 'Authorization': `Bearer ${token}` },
        }),
        fetch('/api/events?limit=50', {
          headers: { 'Authorization': `Bearer ${token}` },
        }),
      ]);

      // Handle summary response
      if (summaryResponse.ok) {
        const summaryData = await summaryResponse.json();
        setSummary(summaryData.summary);
      } else if (summaryResponse.status === 401) {
        // Only redirect to login on 401, not on other errors
        localStorage.removeItem('hermanos_token');
        localStorage.removeItem('hermanos_user');
        localStorage.removeItem('hermanos_congregation');
        localStorage.removeItem('hermanos_permissions');
        router.push('/login');
        return;
      }

      // Handle events response
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json();
        setEvents(eventsData.events || []);
      } else if (eventsResponse.status === 401) {
        // Only redirect to login on 401, not on other errors
        localStorage.removeItem('hermanos_token');
        localStorage.removeItem('hermanos_user');
        localStorage.removeItem('hermanos_congregation');
        localStorage.removeItem('hermanos_permissions');
        router.push('/login');
        return;
      } else {
        // For non-401 errors, just show error message
        try {
          const errorData = await eventsResponse.json();
          setError(errorData.error || 'Error al cargar eventos');
        } catch {
          setError('Error al cargar eventos');
        }
      }
    } catch (error) {
      console.error('Error loading events data:', error);
      setError('Error al cargar los datos de eventos');
    } finally {
      setIsLoadingEvents(false);
    }
  };

  const handleCreateEvent = async () => {
    if (!formData.title || !formData.eventDate || !formData.category) {
      setError('Por favor complete todos los campos requeridos');
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');

      const eventData = {
        ...formData,
        eventDate: new Date(formData.eventDate).toISOString().split('T')[0],
      };

      const response = await fetch('/api/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(eventData),
      });

      if (response.ok) {
        setSuccessMessage('Evento creado exitosamente');
        setShowCreateModal(false);
        resetForm();
        loadEventsData();
      } else if (response.status === 401) {
        // Only redirect to login on 401, not on other errors
        localStorage.removeItem('hermanos_token');
        localStorage.removeItem('hermanos_user');
        localStorage.removeItem('hermanos_congregation');
        localStorage.removeItem('hermanos_permissions');
        router.push('/login');
        return;
      } else {
        try {
          const errorData = await response.json();
          setError(errorData.error || 'Error al crear evento');
        } catch {
          setError('Error al crear evento');
        }
      }
    } catch (error) {
      console.error('Error creating event:', error);
      setError('Error al crear el evento');
    }
  };

  const handleUpdateEvent = async () => {
    if (!editingEvent || !formData.title || !formData.eventDate || !formData.category) {
      setError('Por favor complete todos los campos requeridos');
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');

      const updateData = {
        eventId: editingEvent.id,
        ...formData,
        eventDate: new Date(formData.eventDate).toISOString().split('T')[0],
      };

      const response = await fetch('/api/events', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        setSuccessMessage('Evento actualizado exitosamente');
        setEditingEvent(null);
        resetForm();
        loadEventsData();
      } else if (response.status === 401) {
        // Only redirect to login on 401, not on other errors
        localStorage.removeItem('hermanos_token');
        localStorage.removeItem('hermanos_user');
        localStorage.removeItem('hermanos_congregation');
        localStorage.removeItem('hermanos_permissions');
        router.push('/login');
        return;
      } else {
        try {
          const errorData = await response.json();
          setError(errorData.error || 'Error al actualizar evento');
        } catch {
          setError('Error al actualizar evento');
        }
      }
    } catch (error) {
      console.error('Error updating event:', error);
      setError('Error al actualizar el evento');
    }
  };

  const handleDeleteEvent = async (eventId: string) => {
    if (!confirm('¿Está seguro de que desea eliminar este evento?')) {
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch(`/api/events?eventId=${eventId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setSuccessMessage('Evento eliminado exitosamente');
        loadEventsData();
      } else if (response.status === 401) {
        // Only redirect to login on 401, not on other errors
        localStorage.removeItem('hermanos_token');
        localStorage.removeItem('hermanos_user');
        localStorage.removeItem('hermanos_congregation');
        localStorage.removeItem('hermanos_permissions');
        router.push('/login');
        return;
      } else {
        try {
          const errorData = await response.json();
          setError(errorData.error || 'Error al eliminar evento');
        } catch {
          setError('Error al eliminar evento');
        }
      }
    } catch (error) {
      console.error('Error deleting event:', error);
      setError('Error al eliminar el evento');
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      eventDate: new Date(),
      startTime: '',
      endTime: '',
      location: '',
      category: EventCategory.OTHER,
      isAllDay: false,
      isRecurring: false,
      recurrenceRule: '',
      visibility: EventVisibility.ALL_MEMBERS,
    });
  };

  const openEditModal = (event: EventData) => {
    setEditingEvent(event);
    setFormData({
      title: event.title,
      description: event.description || '',
      eventDate: event.eventDate,
      startTime: event.startTime || '',
      endTime: event.endTime || '',
      location: event.location || '',
      category: event.category,
      isAllDay: event.isAllDay,
      isRecurring: event.isRecurring,
      recurrenceRule: event.recurrenceRule || '',
      visibility: event.visibility,
    });
    setShowCreateModal(true);
  };

  const closeModal = () => {
    setShowCreateModal(false);
    setEditingEvent(null);
    resetForm();
    setError(null);
  };

  const formatEventDate = (date: Date): string => {
    return new Date(date).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getEventCategoryColor = (category: EventCategory): string => {
    const colors: Record<EventCategory, string> = {
      [EventCategory.ASSEMBLY]: 'bg-blue-100 text-blue-800 border-blue-200',
      [EventCategory.CONVENTION]: 'bg-purple-100 text-purple-800 border-purple-200',
      [EventCategory.SPECIAL_MEETING]: 'bg-green-100 text-green-800 border-green-200',
      [EventCategory.SERVICE_ACTIVITY]: 'bg-orange-100 text-orange-800 border-orange-200',
      [EventCategory.SOCIAL_ACTIVITY]: 'bg-pink-100 text-pink-800 border-pink-200',
      [EventCategory.TRAINING]: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      [EventCategory.OTHER]: 'bg-gray-100 text-gray-800 border-gray-200',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="bg-gray-50 relative">
      {/* Header */}
      <div className="bg-pink-600 text-white p-4">
        <div className="max-w-7xl mx-auto">
          {/* Single Row with Back Button, Title, and Action */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin')}
                className="text-pink-200 hover:text-white flex items-center"
              >
                ← Back
              </button>
              <h1 className="text-2xl font-bold">
                <span className="hidden md:inline">Events Management</span>
                <span className="md:hidden">Events</span>
              </h1>
            </div>
            {/* Desktop Button */}
            <button
              onClick={() => setShowCreateModal(true)}
              className="hidden md:flex bg-white text-pink-600 px-4 py-2 rounded-md font-medium hover:bg-pink-50 transition-colors"
            >
              Add Event
            </button>
            {/* Mobile Icon Button */}
            <button
              onClick={() => setShowCreateModal(true)}
              className="md:hidden bg-white text-pink-600 p-2 rounded-md hover:bg-pink-50 transition-colors"
              title="Add Event"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 pb-20">
        {/* Success/Error Messages */}
        {successMessage && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <p className="text-green-800">{successMessage}</p>
            <button
              onClick={() => setSuccessMessage(null)}
              className="text-green-600 hover:text-green-800 text-sm mt-2"
            >
              Cerrar
            </button>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <p className="text-red-800">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800 text-sm mt-2"
            >
              Cerrar
            </button>
          </div>
        )}

        {/* Summary Cards */}
        {summary && (
          <>
            {/* Desktop - Always visible */}
            <div className="hidden md:grid grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Events</h3>
                <p className="text-3xl font-bold text-pink-600">{summary.totalEvents}</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Upcoming Events</h3>
                <p className="text-3xl font-bold text-blue-600">{summary.upcomingEvents}</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Assemblies</h3>
                <p className="text-3xl font-bold text-purple-600">{summary.eventsByCategory[EventCategory.ASSEMBLY]}</p>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Activities</h3>
                <p className="text-3xl font-bold text-orange-600">
                  {summary.eventsByCategory[EventCategory.SERVICE_ACTIVITY] + summary.eventsByCategory[EventCategory.SOCIAL_ACTIVITY]}
                </p>
              </div>
            </div>

            {/* Mobile - Collapsible */}
            <div className="md:hidden mb-6">
              {/* Toggle Button */}
              <button
                onClick={() => setShowStatistics(!showStatistics)}
                className="w-full bg-white rounded-lg shadow-md p-3 flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <span className="text-sm font-medium text-gray-700">Statistics</span>
                <svg
                  className={`w-5 h-5 text-gray-500 transition-transform ${showStatistics ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Collapsible Content */}
              {showStatistics && (
                <div className="mt-3 grid grid-cols-2 gap-3">
                  <div className="bg-white rounded-lg shadow-md p-3">
                    <h3 className="text-xs font-semibold text-gray-900 mb-1">Total</h3>
                    <p className="text-xl font-bold text-pink-600">{summary.totalEvents}</p>
                  </div>

                  <div className="bg-white rounded-lg shadow-md p-3">
                    <h3 className="text-xs font-semibold text-gray-900 mb-1">Upcoming</h3>
                    <p className="text-xl font-bold text-blue-600">{summary.upcomingEvents}</p>
                  </div>

                  <div className="bg-white rounded-lg shadow-md p-3">
                    <h3 className="text-xs font-semibold text-gray-900 mb-1">Assemblies</h3>
                    <p className="text-xl font-bold text-purple-600">{summary.eventsByCategory[EventCategory.ASSEMBLY]}</p>
                  </div>

                  <div className="bg-white rounded-lg shadow-md p-3">
                    <h3 className="text-xs font-semibold text-gray-900 mb-1">Activities</h3>
                    <p className="text-xl font-bold text-orange-600">
                      {summary.eventsByCategory[EventCategory.SERVICE_ACTIVITY] + summary.eventsByCategory[EventCategory.SOCIAL_ACTIVITY]}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {/* Events List */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Congregation Events</h2>

          {isLoadingEvents ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading events...</p>
            </div>
          ) : events.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No events available</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="mt-4 bg-pink-600 text-white px-4 py-2 rounded-md hover:bg-pink-700 transition-colors"
              >
                Create First Event
              </button>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden md:block overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Event Title
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Time
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {events.map((event) => (
                      <tr key={event.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => openEditModal(event)}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{event.title}</div>
                              <div className="flex items-center gap-2 mt-1">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getEventCategoryColor(event.category)}`}>
                                  {EVENT_CATEGORY_LABELS[event.category]}
                                </span>
                                {!event.isActive && (
                                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-600 border border-red-200">
                                    Inactive
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatEventDate(event.eventDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {event.isAllDay ? 'All Day' :
                           event.startTime && event.endTime ? `${event.startTime} - ${event.endTime}` :
                           event.startTime || 'TBD'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
                            <button
                              onClick={() => openEditModal(event)}
                              className="text-blue-600 hover:text-blue-800 px-3 py-1 rounded border border-blue-300 hover:bg-blue-50 transition-colors"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDeleteEvent(event.id)}
                              className="text-red-600 hover:text-red-800 px-3 py-1 rounded border border-red-300 hover:bg-red-50 transition-colors"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Mobile Card View */}
              <div className="md:hidden space-y-4">
                {events.map((event) => (
                  <div key={event.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">{event.title}</h3>
                        <div className="flex items-center gap-2 mb-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getEventCategoryColor(event.category)}`}>
                            {EVENT_CATEGORY_LABELS[event.category]}
                          </span>
                          {!event.isActive && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-600 border border-red-200">
                              Inactive
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-1">
                          <span className="font-medium">Date:</span> {formatEventDate(event.eventDate)}
                        </p>
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Time:</span> {event.isAllDay ? 'All Day' :
                           event.startTime && event.endTime ? `${event.startTime} - ${event.endTime}` :
                           event.startTime || 'TBD'}
                        </p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => openEditModal(event)}
                          className="text-blue-600 hover:text-blue-800 p-2 rounded-full hover:bg-blue-50 transition-colors"
                          title="Edit Event"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDeleteEvent(event.id)}
                          className="text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-red-50 transition-colors"
                          title="Delete Event"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Create/Edit Event Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">
                  {editingEvent ? 'Edit Event' : 'Add Event'}
                </h2>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={(e) => {
                e.preventDefault();
                if (editingEvent) {
                  handleUpdateEvent();
                } else {
                  handleCreateEvent();
                }
              }}>
                <div className="space-y-4">
                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Title *
                    </label>
                    <input
                      type="text"
                      value={formData.title || ''}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                      required
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={formData.description || ''}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                    />
                  </div>

                  {/* Date and Time */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date *
                      </label>
                      <input
                        type="date"
                        value={formData.eventDate ? new Date(formData.eventDate).toISOString().split('T')[0] : ''}
                        onChange={(e) => setFormData({ ...formData, eventDate: new Date(e.target.value) })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Start Time
                      </label>
                      <input
                        type="time"
                        value={formData.startTime || ''}
                        onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                        disabled={formData.isAllDay}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        End Time
                      </label>
                      <input
                        type="time"
                        value={formData.endTime || ''}
                        onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                        disabled={formData.isAllDay}
                      />
                    </div>
                  </div>

                  {/* All Day Checkbox */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isAllDay"
                      checked={formData.isAllDay || false}
                      onChange={(e) => setFormData({
                        ...formData,
                        isAllDay: e.target.checked,
                        ...(e.target.checked && { startTime: '', endTime: '' })
                      })}
                      className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isAllDay" className="ml-2 block text-sm text-gray-700">
                      All Day Event
                    </label>
                  </div>

                  {/* Location */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Location
                    </label>
                    <input
                      type="text"
                      value={formData.location || ''}
                      onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                      placeholder="e.g. Kingdom Hall, Regional Assembly, etc."
                    />
                  </div>

                  {/* Category and Visibility */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Category *
                      </label>
                      <select
                        value={formData.category || EventCategory.OTHER}
                        onChange={(e) => setFormData({ ...formData, category: e.target.value as EventCategory })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                        required
                      >
                        {Object.values(EventCategory).map(category => (
                          <option key={category} value={category}>
                            {EVENT_CATEGORY_LABELS[category]}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Visibility
                      </label>
                      <select
                        value={formData.visibility || EventVisibility.ALL_MEMBERS}
                        onChange={(e) => setFormData({ ...formData, visibility: e.target.value as EventVisibility })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                      >
                        {Object.values(EventVisibility).map(visibility => (
                          <option key={visibility} value={visibility}>
                            {EVENT_VISIBILITY_LABELS[visibility]}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Recurring Event */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isRecurring"
                      checked={formData.isRecurring || false}
                      onChange={(e) => setFormData({ ...formData, isRecurring: e.target.checked })}
                      className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isRecurring" className="ml-2 block text-sm text-gray-700">
                      Recurring Event
                    </label>
                  </div>

                  {formData.isRecurring && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Recurrence Rule
                      </label>
                      <input
                        type="text"
                        value={formData.recurrenceRule || ''}
                        onChange={(e) => setFormData({ ...formData, recurrenceRule: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                        placeholder="e.g. Every Sunday, Monthly, etc."
                      />
                    </div>
                  )}
                </div>

                {/* Modal Actions */}
                <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 transition-colors"
                  >
                    {editingEvent ? 'Update Event' : 'Add Event'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Admin Footer */}
      <AdminFooter currentSection="events" />
    </div>
  );
}
