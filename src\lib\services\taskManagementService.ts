/**
 * Task Management Service
 *
 * Comprehensive service for managing congregation tasks, assignments,
 * and tracking with proper validation and congregation isolation.
 */

import { prisma } from '@/lib/prisma';

export interface Task {
  id: string;
  congregationId: string;
  title: string;
  description: string | null;
  category: string;
  frequency: string;
  estimatedTime: number | null;
  instructions: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  assignments?: TaskAssignment[];
}

export interface TaskAssignment {
  id: string;
  congregationId: string;
  taskId: string;
  assignedMemberId: string | null;
  assignedDate: Date;
  dueDate: Date | null;
  status: string;
  notes: string | null;
  completedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  task?: Task;
  assignedMember?: {
    id: string;
    name: string;
    role: string;
  };
}

export interface TaskInput {
  title: string;
  description?: string;
  category: string;
  frequency: string;
  estimatedTime?: number;
  instructions?: string;
}

export interface TaskAssignmentInput {
  taskId: string;
  assignedMemberId?: string;
  assignedDate: string; // YYYY-MM-DD format
  dueDate?: string; // YYYY-MM-DD format
  notes?: string;
}

export interface TaskStatistics {
  totalTasks: number;
  activeTasks: number;
  pendingAssignments: number;
  inProgressAssignments: number;
  completedAssignments: number;
  overdueTasks: number;
  tasksByCategory: Record<string, number>;
  memberWorkload: Array<{
    memberId: string;
    memberName: string;
    pendingTasks: number;
    inProgressTasks: number;
    completedTasks: number;
  }>;
}

export class TaskManagementService {
  /**
   * Get all tasks for a congregation with optional filtering
   */
  static async getTasks(
    congregationId: string,
    options: {
      category?: string;
      isActive?: boolean;
      includeAssignments?: boolean;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{ tasks: Task[]; total: number }> {
    try {
      const {
        category,
        isActive = true,
        includeAssignments = false,
        page = 1,
        limit = 20
      } = options;

      const skip = (page - 1) * limit;
      const take = Math.min(limit, 100);

      const where = {
        congregationId,
        ...(category && { category }),
        ...(isActive !== undefined && { isActive }),
      };

      const include = includeAssignments ? {
        assignments: {
          include: {
            assignedMember: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
          },
          orderBy: { assignedDate: 'desc' as const },
          take: 10, // Latest 10 assignments per task
        },
      } : undefined;

      const [tasks, total] = await Promise.all([
        prisma.task.findMany({
          where,
          include,
          orderBy: { createdAt: 'desc' },
          skip,
          take,
        }),
        prisma.task.count({ where }),
      ]);

      return { tasks, total };
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw new Error('Failed to fetch tasks');
    }
  }

  /**
   * Get a specific task by ID
   */
  static async getTaskById(
    congregationId: string,
    taskId: string,
    includeAssignments: boolean = true
  ): Promise<Task | null> {
    try {
      const task = await prisma.task.findFirst({
        where: {
          id: taskId,
          congregationId,
        },
        include: includeAssignments ? {
          assignments: {
            include: {
              assignedMember: {
                select: {
                  id: true,
                  name: true,
                  role: true,
                },
              },
            },
            orderBy: { assignedDate: 'desc' },
          },
        } : undefined,
      });

      return task;
    } catch (error) {
      console.error('Error fetching task:', error);
      throw new Error('Failed to fetch task');
    }
  }

  /**
   * Create a new task
   */
  static async createTask(
    congregationId: string,
    taskData: TaskInput
  ): Promise<Task> {
    try {
      // Validate task data
      const validation = this.validateTaskData(taskData);
      if (!validation.isValid) {
        throw new Error(`Invalid task data: ${validation.errors.join(', ')}`);
      }

      const task = await prisma.task.create({
        data: {
          congregationId,
          title: taskData.title,
          description: taskData.description || null,
          category: taskData.category,
          frequency: taskData.frequency,
          estimatedTime: taskData.estimatedTime || null,
          instructions: taskData.instructions || null,
          isActive: true,
        },
      });

      return task;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  }

  /**
   * Update an existing task
   */
  static async updateTask(
    congregationId: string,
    taskId: string,
    taskData: Partial<TaskInput>
  ): Promise<Task> {
    try {
      // Verify task exists and belongs to congregation
      const existingTask = await prisma.task.findFirst({
        where: {
          id: taskId,
          congregationId,
        },
      });

      if (!existingTask) {
        throw new Error('Task not found or does not belong to congregation');
      }

      // Validate updated data
      if (Object.keys(taskData).length > 0) {
        const validation = this.validateTaskData(taskData as TaskInput, true);
        if (!validation.isValid) {
          throw new Error(`Invalid task data: ${validation.errors.join(', ')}`);
        }
      }

      const updatedTask = await prisma.task.update({
        where: { id: taskId },
        data: {
          ...(taskData.title && { title: taskData.title }),
          ...(taskData.description !== undefined && { description: taskData.description }),
          ...(taskData.category && { category: taskData.category }),
          ...(taskData.frequency && { frequency: taskData.frequency }),
          ...(taskData.estimatedTime !== undefined && { estimatedTime: taskData.estimatedTime }),
          ...(taskData.instructions !== undefined && { instructions: taskData.instructions }),
          updatedAt: new Date(),
        },
      });

      return updatedTask;
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  }

  /**
   * Toggle task active status
   */
  static async toggleTaskStatus(
    congregationId: string,
    taskId: string,
    isActive: boolean
  ): Promise<Task> {
    try {
      // Verify task exists and belongs to congregation
      const existingTask = await prisma.task.findFirst({
        where: {
          id: taskId,
          congregationId,
        },
      });

      if (!existingTask) {
        throw new Error('Task not found or does not belong to congregation');
      }

      const updatedTask = await prisma.task.update({
        where: { id: taskId },
        data: {
          isActive,
          updatedAt: new Date(),
        },
      });

      return updatedTask;
    } catch (error) {
      console.error('Error toggling task status:', error);
      throw error;
    }
  }

  /**
   * Delete a task (only if no assignments exist)
   */
  static async deleteTask(
    congregationId: string,
    taskId: string
  ): Promise<void> {
    try {
      // Verify task exists and belongs to congregation
      const existingTask = await prisma.task.findFirst({
        where: {
          id: taskId,
          congregationId,
        },
        include: {
          assignments: true,
        },
      });

      if (!existingTask) {
        throw new Error('Task not found or does not belong to congregation');
      }

      // Check if task has assignments
      if (existingTask.assignments.length > 0) {
        throw new Error('Cannot delete task with existing assignments. Deactivate instead.');
      }

      await prisma.task.delete({
        where: { id: taskId },
      });
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  }

  /**
   * Validate task data
   */
  static validateTaskData(data: Partial<TaskInput>, isUpdate: boolean = false): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!isUpdate || data.title !== undefined) {
      if (!data.title || data.title.trim().length === 0) {
        errors.push('Title is required');
      } else if (data.title.length > 255) {
        errors.push('Title cannot exceed 255 characters');
      }
    }

    if (!isUpdate || data.category !== undefined) {
      if (!data.category || data.category.trim().length === 0) {
        errors.push('Category is required');
      } else if (data.category.length > 100) {
        errors.push('Category cannot exceed 100 characters');
      }
    }

    if (!isUpdate || data.frequency !== undefined) {
      if (!data.frequency || data.frequency.trim().length === 0) {
        errors.push('Frequency is required');
      } else if (!['weekly', 'monthly', 'quarterly', 'yearly', 'one-time'].includes(data.frequency)) {
        errors.push('Invalid frequency. Must be: weekly, monthly, quarterly, yearly, or one-time');
      }
    }

    if (data.description && data.description.length > 2000) {
      errors.push('Description cannot exceed 2000 characters');
    }

    if (data.instructions && data.instructions.length > 2000) {
      errors.push('Instructions cannot exceed 2000 characters');
    }

    if (data.estimatedTime !== undefined && data.estimatedTime !== null) {
      if (data.estimatedTime < 0 || data.estimatedTime > 480) {
        errors.push('Estimated time must be between 0 and 480 minutes');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get task assignments for a congregation with optional filtering
   */
  static async getTaskAssignments(
    congregationId: string,
    options: {
      memberId?: string;
      taskId?: string;
      status?: string;
      dateFrom?: string;
      dateTo?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{ assignments: TaskAssignment[]; total: number }> {
    try {
      const {
        memberId,
        taskId,
        status,
        dateFrom,
        dateTo,
        page = 1,
        limit = 20
      } = options;

      const skip = (page - 1) * limit;
      const take = Math.min(limit, 100);

      const where = {
        congregationId,
        ...(memberId && { assignedMemberId: memberId }),
        ...(taskId && { taskId }),
        ...(status && { status }),
        ...(dateFrom && dateTo && {
          assignedDate: {
            gte: new Date(dateFrom),
            lte: new Date(dateTo),
          },
        }),
      };

      const [assignments, total] = await Promise.all([
        prisma.taskAssignment.findMany({
          where,
          include: {
            task: {
              select: {
                id: true,
                title: true,
                category: true,
                frequency: true,
                estimatedTime: true,
              },
            },
            assignedMember: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
          },
          orderBy: { assignedDate: 'desc' },
          skip,
          take,
        }),
        prisma.taskAssignment.count({ where }),
      ]);

      return { assignments, total };
    } catch (error) {
      console.error('Error fetching task assignments:', error);
      throw new Error('Failed to fetch task assignments');
    }
  }

  /**
   * Create a new task assignment
   */
  static async createTaskAssignment(
    congregationId: string,
    assignmentData: TaskAssignmentInput
  ): Promise<TaskAssignment> {
    try {
      // Validate assignment data
      const validation = this.validateAssignmentData(assignmentData);
      if (!validation.isValid) {
        throw new Error(`Invalid assignment data: ${validation.errors.join(', ')}`);
      }

      // Verify task exists and belongs to congregation
      const task = await prisma.task.findFirst({
        where: {
          id: assignmentData.taskId,
          congregationId,
          isActive: true,
        },
      });

      if (!task) {
        throw new Error('Task not found or is not active');
      }

      // Verify member exists and belongs to congregation (if assigned)
      if (assignmentData.assignedMemberId) {
        const member = await prisma.member.findFirst({
          where: {
            id: assignmentData.assignedMemberId,
            congregationId,
            isActive: true,
          },
        });

        if (!member) {
          throw new Error('Member not found or is not active');
        }
      }

      // Check for existing assignment on the same date
      const existingAssignment = await prisma.taskAssignment.findFirst({
        where: {
          congregationId,
          taskId: assignmentData.taskId,
          assignedDate: new Date(assignmentData.assignedDate),
        },
      });

      if (existingAssignment) {
        throw new Error('Task already assigned for this date');
      }

      const assignment = await prisma.taskAssignment.create({
        data: {
          congregationId,
          taskId: assignmentData.taskId,
          assignedMemberId: assignmentData.assignedMemberId || null,
          assignedDate: new Date(assignmentData.assignedDate),
          dueDate: assignmentData.dueDate ? new Date(assignmentData.dueDate) : null,
          notes: assignmentData.notes || null,
          status: 'pending',
        },
        include: {
          task: {
            select: {
              id: true,
              title: true,
              category: true,
              frequency: true,
              estimatedTime: true,
            },
          },
          assignedMember: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return assignment;
    } catch (error) {
      console.error('Error creating task assignment:', error);
      throw error;
    }
  }

  /**
   * Update task assignment status
   */
  static async updateAssignmentStatus(
    congregationId: string,
    assignmentId: string,
    status: string,
    notes?: string,
    memberId?: string
  ): Promise<TaskAssignment> {
    try {
      // Validate status
      if (!['pending', 'in_progress', 'completed', 'cancelled'].includes(status)) {
        throw new Error('Invalid status. Must be: pending, in_progress, completed, or cancelled');
      }

      // Verify assignment exists and belongs to congregation
      const existingAssignment = await prisma.taskAssignment.findFirst({
        where: {
          id: assignmentId,
          congregationId,
        },
      });

      if (!existingAssignment) {
        throw new Error('Assignment not found or does not belong to congregation');
      }

      // If member is specified, verify they can update this assignment
      if (memberId && existingAssignment.assignedMemberId !== memberId) {
        throw new Error('You can only update your own assignments');
      }

      const updateData = {
        status,
        updatedAt: new Date(),
        ...(notes !== undefined && { notes }),
        ...(status === 'completed' && { completedAt: new Date() }),
        ...((status === 'pending' || status === 'in_progress') && { completedAt: null }),
      };



      const updatedAssignment = await prisma.taskAssignment.update({
        where: { id: assignmentId },
        data: updateData,
        include: {
          task: {
            select: {
              id: true,
              title: true,
              category: true,
              frequency: true,
              estimatedTime: true,
            },
          },
          assignedMember: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return updatedAssignment;
    } catch (error) {
      console.error('Error updating assignment status:', error);
      throw error;
    }
  }

  /**
   * Reassign a task to a different member
   */
  static async reassignTask(
    congregationId: string,
    assignmentId: string,
    newMemberId: string | null,
    notes?: string
  ): Promise<TaskAssignment> {
    try {
      // Verify assignment exists and belongs to congregation
      const existingAssignment = await prisma.taskAssignment.findFirst({
        where: {
          id: assignmentId,
          congregationId,
        },
      });

      if (!existingAssignment) {
        throw new Error('Assignment not found or does not belong to congregation');
      }

      // Verify new member exists and belongs to congregation (if specified)
      if (newMemberId) {
        const member = await prisma.member.findFirst({
          where: {
            id: newMemberId,
            congregationId,
            isActive: true,
          },
        });

        if (!member) {
          throw new Error('New member not found or is not active');
        }
      }

      const updatedAssignment = await prisma.taskAssignment.update({
        where: { id: assignmentId },
        data: {
          assignedMemberId: newMemberId,
          status: 'pending', // Reset status when reassigning
          notes: notes || null,
          completedAt: null,
          updatedAt: new Date(),
        },
        include: {
          task: {
            select: {
              id: true,
              title: true,
              category: true,
              frequency: true,
              estimatedTime: true,
            },
          },
          assignedMember: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return updatedAssignment;
    } catch (error) {
      console.error('Error reassigning task:', error);
      throw error;
    }
  }

  /**
   * Validate assignment data
   */
  static validateAssignmentData(data: TaskAssignmentInput): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.taskId || data.taskId.trim().length === 0) {
      errors.push('Task ID is required');
    }

    if (!data.assignedDate || !/^\d{4}-\d{2}-\d{2}$/.test(data.assignedDate)) {
      errors.push('Assigned date is required and must be in YYYY-MM-DD format');
    }

    if (data.dueDate && !/^\d{4}-\d{2}-\d{2}$/.test(data.dueDate)) {
      errors.push('Due date must be in YYYY-MM-DD format');
    }

    if (data.assignedDate && data.dueDate) {
      const assignedDate = new Date(data.assignedDate);
      const dueDate = new Date(data.dueDate);

      if (dueDate < assignedDate) {
        errors.push('Due date cannot be before assigned date');
      }
    }

    if (data.notes && data.notes.length > 1000) {
      errors.push('Notes cannot exceed 1000 characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get task statistics for a congregation
   */
  static async getTaskStatistics(congregationId: string): Promise<TaskStatistics> {
    try {
      const [
        totalTasks,
        activeTasks,
        assignments,
        tasksByCategory,
        memberWorkload
      ] = await Promise.all([
        // Total tasks
        prisma.task.count({
          where: { congregationId },
        }),

        // Active tasks
        prisma.task.count({
          where: { congregationId, isActive: true },
        }),

        // All assignments
        prisma.taskAssignment.findMany({
          where: { congregationId },
          include: {
            assignedMember: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        }),

        // Tasks by category
        prisma.task.groupBy({
          by: ['category'],
          where: { congregationId, isActive: true },
          _count: { id: true },
        }),

        // Member workload
        prisma.taskAssignment.groupBy({
          by: ['assignedMemberId'],
          where: {
            congregationId,
            assignedMemberId: { not: null },
          },
          _count: { id: true },
        }),
      ]);

      // Calculate assignment statistics
      const pendingAssignments = assignments.filter(a => a.status === 'pending').length;
      const inProgressAssignments = assignments.filter(a => a.status === 'in_progress').length;
      const completedAssignments = assignments.filter(a => a.status === 'completed').length;

      // Calculate overdue tasks
      const today = new Date();
      const overdueTasks = assignments.filter(a =>
        a.dueDate &&
        new Date(a.dueDate) < today &&
        a.status !== 'completed' &&
        a.status !== 'cancelled'
      ).length;

      // Format tasks by category
      const tasksByCategoryFormatted: Record<string, number> = {};
      tasksByCategory.forEach(item => {
        tasksByCategoryFormatted[item.category] = item._count.id;
      });

      // Get member details for workload
      const memberIds = memberWorkload.map(m => m.assignedMemberId).filter(Boolean) as string[];
      const members = await prisma.member.findMany({
        where: {
          id: { in: memberIds },
          congregationId,
        },
        select: {
          id: true,
          name: true,
        },
      });

      const memberMap = new Map(members.map(m => [m.id, m.name]));

      // Calculate detailed member workload
      const memberWorkloadDetailed = await Promise.all(
        memberIds.map(async (memberId) => {
          const memberAssignments = assignments.filter(a => a.assignedMemberId === memberId);

          return {
            memberId,
            memberName: memberMap.get(memberId) || 'Unknown',
            pendingTasks: memberAssignments.filter(a => a.status === 'pending').length,
            inProgressTasks: memberAssignments.filter(a => a.status === 'in_progress').length,
            completedTasks: memberAssignments.filter(a => a.status === 'completed').length,
          };
        })
      );

      return {
        totalTasks,
        activeTasks,
        pendingAssignments,
        inProgressAssignments,
        completedAssignments,
        overdueTasks,
        tasksByCategory: tasksByCategoryFormatted,
        memberWorkload: memberWorkloadDetailed,
      };
    } catch (error) {
      console.error('Error calculating task statistics:', error);
      throw new Error('Failed to calculate task statistics');
    }
  }

  /**
   * Get member's personal task assignments
   */
  static async getMemberTasks(
    congregationId: string,
    memberId: string,
    options: {
      status?: string;
      limit?: number;
    } = {}
  ): Promise<TaskAssignment[]> {
    try {
      const { status, limit = 50 } = options;

      const where = {
        congregationId,
        assignedMemberId: memberId,
        ...(status && { status }),
      };

      const assignments = await prisma.taskAssignment.findMany({
        where,
        include: {
          task: {
            select: {
              id: true,
              title: true,
              description: true,
              category: true,
              frequency: true,
              estimatedTime: true,
              instructions: true,
            },
          },
        },
        orderBy: [
          { status: 'asc' }, // pending first, then in_progress, completed, cancelled
          { assignedDate: 'desc' },
        ],
        take: limit,
      });

      return assignments;
    } catch (error) {
      console.error('Error fetching member tasks:', error);
      throw new Error('Failed to fetch member tasks');
    }
  }

  /**
   * Get task categories for a congregation
   */
  static async getTaskCategories(congregationId: string): Promise<string[]> {
    try {
      const categories = await prisma.task.findMany({
        where: { congregationId, isActive: true },
        select: { category: true },
        distinct: ['category'],
        orderBy: { category: 'asc' },
      });

      return categories.map(c => c.category);
    } catch (error) {
      console.error('Error fetching task categories:', error);
      throw new Error('Failed to fetch task categories');
    }
  }

  /**
   * Get upcoming task assignments (next 30 days)
   */
  static async getUpcomingAssignments(
    congregationId: string,
    days: number = 30
  ): Promise<TaskAssignment[]> {
    try {
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);

      const assignments = await prisma.taskAssignment.findMany({
        where: {
          congregationId,
          assignedDate: {
            gte: today,
            lte: futureDate,
          },
          status: { in: ['pending', 'in_progress'] },
        },
        include: {
          task: {
            select: {
              id: true,
              title: true,
              category: true,
              estimatedTime: true,
            },
          },
          assignedMember: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
        orderBy: { assignedDate: 'asc' },
      });

      return assignments;
    } catch (error) {
      console.error('Error fetching upcoming assignments:', error);
      throw new Error('Failed to fetch upcoming assignments');
    }
  }

  /**
   * Get overdue task assignments
   */
  static async getOverdueAssignments(congregationId: string): Promise<TaskAssignment[]> {
    try {
      const today = new Date();

      const assignments = await prisma.taskAssignment.findMany({
        where: {
          congregationId,
          dueDate: {
            lt: today,
          },
          status: { in: ['pending', 'in_progress'] },
        },
        include: {
          task: {
            select: {
              id: true,
              title: true,
              category: true,
              estimatedTime: true,
            },
          },
          assignedMember: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
        orderBy: { dueDate: 'asc' },
      });

      return assignments;
    } catch (error) {
      console.error('Error fetching overdue assignments:', error);
      throw new Error('Failed to fetch overdue assignments');
    }
  }

  /**
   * Format date for display
   */
  static formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  /**
   * Format estimated time for display
   */
  static formatEstimatedTime(minutes: number | null): string {
    if (!minutes) return 'No especificado';

    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
    }
  }

  /**
   * Get status display text in Spanish
   */
  static getStatusDisplayText(status: string): string {
    const statusMap: Record<string, string> = {
      'pending': 'Pendiente',
      'in_progress': 'En Progreso',
      'completed': 'Completado',
      'cancelled': 'Cancelado',
    };

    return statusMap[status] || status;
  }

  /**
   * Get frequency display text in Spanish
   */
  static getFrequencyDisplayText(frequency: string): string {
    const frequencyMap: Record<string, string> = {
      'weekly': 'Semanal',
      'monthly': 'Mensual',
      'quarterly': 'Trimestral',
      'yearly': 'Anual',
      'one-time': 'Una vez',
    };

    return frequencyMap[frequency] || frequency;
  }
}
