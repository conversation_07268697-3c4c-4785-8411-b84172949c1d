// Data Cleaning Service Tests
// Tests for territory data cleaning and standardization

import { describe, it, expect, beforeEach } from '@jest/globals';
import { DataCleaningService } from '@/services/territories/DataCleaningService';

describe('DataCleaningService', () => {
  beforeEach(() => {
    // Reset to default configurations before each test
    DataCleaningService.setTerritoryNumberConfig({});
    DataCleaningService.setAddressConfig({});
  });

  describe('cleanTerritoryNumber', () => {
    it('should clean territory number with whitespace', () => {
      const result = DataCleaningService.cleanTerritoryNumber('  A-001  ');

      expect(result.cleaned).toBe('A-001');
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0].type).toBe('spacing');
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    it('should convert to uppercase when case insensitive', () => {
      const result = DataCleaningService.cleanTerritoryNumber('a-001');

      expect(result.cleaned).toBe('A-001');
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0].type).toBe('capitalization');
    });

    it('should remove invalid characters', () => {
      const result = DataCleaningService.cleanTerritoryNumber('A-001@#$');

      expect(result.cleaned).toBe('A-001');
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0].type).toBe('format');
    });

    it('should handle empty input', () => {
      const result = DataCleaningService.cleanTerritoryNumber('');

      expect(result.cleaned).toBe('');
      expect(result.changes).toHaveLength(0);
      expect(result.confidence).toBe(1);
    });

    it('should handle multiple changes', () => {
      const result = DataCleaningService.cleanTerritoryNumber('  a-001@#$  ');

      expect(result.cleaned).toBe('A-001');
      expect(result.changes.length).toBeGreaterThan(1);
      expect(result.confidence).toBeLessThan(1);
    });
  });

  describe('cleanAddress', () => {
    it('should standardize capitalization', () => {
      const result = DataCleaningService.cleanAddress('123 main street');

      expect(result.cleaned).toBe('123 Main St');
      expect(result.changes.some(c => c.type === 'capitalization')).toBe(true);
      expect(result.changes.some(c => c.type === 'abbreviation')).toBe(true);
    });

    it('should remove extra spaces', () => {
      const result = DataCleaningService.cleanAddress('123   Main    Street');

      expect(result.cleaned).toBe('123 Main St');
      expect(result.changes.some(c => c.type === 'spacing')).toBe(true);
    });

    it('should standardize punctuation', () => {
      const result = DataCleaningService.cleanAddress('123 Main St,Apt 2');

      expect(result.cleaned).toBe('123 Main St, Apt 2');
      expect(result.changes.some(c => c.type === 'punctuation')).toBe(true);
    });

    it('should apply abbreviations', () => {
      const result = DataCleaningService.cleanAddress('123 Main Street');

      expect(result.cleaned).toBe('123 Main St');
      expect(result.changes.some(c => c.type === 'abbreviation')).toBe(true);
    });

    it('should remove forbidden characters', () => {
      const result = DataCleaningService.cleanAddress('123 Main St <script>');

      expect(result.cleaned).toBe('123 Main St ');
      expect(result.changes.some(c => c.type === 'format')).toBe(true);
    });

    it('should handle empty input', () => {
      const result = DataCleaningService.cleanAddress('');

      expect(result.cleaned).toBe('');
      expect(result.changes).toHaveLength(0);
      expect(result.confidence).toBe(1);
    });

    it('should preserve proper case for exceptions', () => {
      const result = DataCleaningService.cleanAddress('123 main street of the americas');

      expect(result.cleaned).toBe('123 Main St of the Americas');
      expect(result.changes.some(c => c.type === 'capitalization')).toBe(true);
    });
  });

  describe('cleanNotes', () => {
    it('should trim whitespace', () => {
      const result = DataCleaningService.cleanNotes('  Test notes  ');

      expect(result.cleaned).toBe('Test notes');
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0].type).toBe('spacing');
    });

    it('should clean up spacing and line breaks', () => {
      const result = DataCleaningService.cleanNotes('Test   notes\n\n\nwith   breaks');

      expect(result.cleaned).toBe('Test notes\nwith breaks');
      expect(result.changes.some(c => c.type === 'spacing')).toBe(true);
    });

    it('should remove forbidden characters', () => {
      const result = DataCleaningService.cleanNotes('Test notes <script>alert("xss")</script>');

      expect(result.cleaned).toBe('Test notes alert("xss")');
      expect(result.changes.some(c => c.type === 'format')).toBe(true);
    });

    it('should handle empty input', () => {
      const result = DataCleaningService.cleanNotes('');

      expect(result.cleaned).toBe('');
      expect(result.changes).toHaveLength(0);
      expect(result.confidence).toBe(1);
    });
  });

  describe('cleanTerritoryData', () => {
    it('should clean all fields', () => {
      const data = {
        territoryNumber: '  a-001  ',
        address: '123 main street',
        notes: '  test notes  '
      };

      const result = DataCleaningService.cleanTerritoryData(data);

      expect(result.territoryNumber.cleaned).toBe('A-001');
      expect(result.address.cleaned).toBe('123 Main St');
      expect(result.notes?.cleaned).toBe('test notes');
    });

    it('should handle missing notes', () => {
      const data = {
        territoryNumber: 'A-001',
        address: '123 Main Street'
      };

      const result = DataCleaningService.cleanTerritoryData(data);

      expect(result.territoryNumber.cleaned).toBe('A-001');
      expect(result.address.cleaned).toBe('123 Main St');
      expect(result.notes).toBeUndefined();
    });
  });

  describe('getCleaningStatistics', () => {
    it('should calculate statistics correctly', () => {
      const results = [
        {
          original: 'A-001',
          cleaned: 'A-001',
          changes: [],
          confidence: 1
        },
        {
          original: '  a-002  ',
          cleaned: 'A-002',
          changes: [
            { type: 'spacing', description: 'Removed whitespace', before: '  a-002  ', after: 'a-002' },
            { type: 'capitalization', description: 'Converted to uppercase', before: 'a-002', after: 'A-002' }
          ],
          confidence: 0.6
        },
        {
          original: '123 main street',
          cleaned: '123 Main St',
          changes: [
            { type: 'capitalization', description: 'Standardized capitalization', before: '123 main street', after: '123 Main Street' },
            { type: 'abbreviation', description: 'Applied abbreviation', before: 'Street', after: 'St' }
          ],
          confidence: 0.7
        }
      ];

      const stats = DataCleaningService.getCleaningStatistics(results);

      expect(stats.totalProcessed).toBe(3);
      expect(stats.totalChanges).toBe(4);
      expect(stats.itemsChanged).toBe(2);
      expect(stats.changesByType.spacing).toBe(1);
      expect(stats.changesByType.capitalization).toBe(2);
      expect(stats.changesByType.abbreviation).toBe(1);
      expect(stats.averageConfidence).toBeCloseTo(0.77, 1);
    });

    it('should handle empty results', () => {
      const stats = DataCleaningService.getCleaningStatistics([]);

      expect(stats.totalProcessed).toBe(0);
      expect(stats.totalChanges).toBe(0);
      expect(stats.itemsChanged).toBe(0);
      expect(stats.averageConfidence).toBe(0);
    });
  });

  describe('configuration', () => {
    it('should respect custom territory number configuration', () => {
      DataCleaningService.setTerritoryNumberConfig({
        caseSensitive: true
      });

      const result = DataCleaningService.cleanTerritoryNumber('a-001');

      expect(result.cleaned).toBe('a-001'); // Should not convert to uppercase
      expect(result.changes.some(c => c.type === 'capitalization')).toBe(false);
    });

    it('should respect custom address configuration', () => {
      DataCleaningService.setAddressConfig({
        standardizeCapitalization: false
      });

      const result = DataCleaningService.cleanAddress('123 main street');

      expect(result.cleaned).toBe('123 main st'); // Should not capitalize
      expect(result.changes.some(c => c.type === 'capitalization')).toBe(false);
    });

    it('should use custom abbreviations', () => {
      DataCleaningService.setAddressConfig({
        abbreviations: {
          'Street': 'Str',
          'Avenue': 'Av'
        }
      });

      const result = DataCleaningService.cleanAddress('123 Main Street');

      expect(result.cleaned).toBe('123 Main Str');
      expect(result.changes.some(c => c.type === 'abbreviation')).toBe(true);
    });
  });

  describe('edge cases', () => {
    it('should handle null and undefined inputs', () => {
      const result1 = DataCleaningService.cleanTerritoryNumber(null as any);
      const result2 = DataCleaningService.cleanAddress(undefined as any);

      expect(result1.cleaned).toBe('');
      expect(result2.cleaned).toBe('');
    });

    it('should handle very long inputs', () => {
      const longInput = 'A'.repeat(1000);
      const result = DataCleaningService.cleanTerritoryNumber(longInput);

      expect(result.cleaned).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
    });

    it('should handle special characters in addresses', () => {
      const result = DataCleaningService.cleanAddress('123 Main St. Apt #5, Unit B');

      expect(result.cleaned).toBeDefined();
      expect(result.changes.some(c => c.type === 'punctuation')).toBe(true);
    });
  });
});
