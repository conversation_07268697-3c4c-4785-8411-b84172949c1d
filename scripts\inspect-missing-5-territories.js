const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

async function inspectMissing5Territories() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  
  // The 5 specific missing territories
  const missingTerritories = ['014', '051', '053', '068', '069'];
  
  console.log(`🔍 Deep inspection of 5 missing territories...\n`);
  
  for (const territoryNum of missingTerritories) {
    const fileName = `Terr. ${territoryNum}.xlsx`;
    const filePath = path.join(territoriosDir, fileName);
    
    console.log(`${'='.repeat(60)}`);
    console.log(`📋 TERRITORY ${territoryNum} - ${fileName}`);
    console.log(`${'='.repeat(60)}`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ File not found: ${fileName}\n`);
      continue;
    }
    
    try {
      const workbook = XLSX.readFile(filePath);
      const sheetNames = workbook.SheetNames;
      console.log(`📄 Sheets: ${sheetNames.join(', ')}\n`);
      
      // Analyze each sheet
      for (const sheetName of sheetNames) {
        if (sheetName.toLowerCase().includes('mapa')) {
          console.log(`🗺️  Skipping map sheet: ${sheetName}\n`);
          continue;
        }
        
        console.log(`📊 SHEET: ${sheetName}`);
        console.log(`${'-'.repeat(40)}`);
        
        const sheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
        
        console.log(`Total rows: ${data.length}`);
        
        // Show first 20 rows with detailed analysis
        console.log(`\nFirst 20 rows with content:`);
        let rowCount = 0;
        for (let i = 0; i < Math.min(data.length, 30) && rowCount < 20; i++) {
          const row = data[i];
          if (row && row.length > 0 && row.some(cell => cell && cell.toString().trim())) {
            console.log(`\nRow ${i + 1}:`);
            for (let j = 0; j < Math.min(row.length, 8); j++) {
              const cell = row[j];
              if (cell !== undefined && cell !== null && cell !== '') {
                const cellStr = cell.toString().trim();
                console.log(`  Col ${j + 1}: "${cellStr}" (${typeof cell})`);
                
                // Analyze cell content
                if (/\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(cellStr)) {
                  console.log(`    🛣️  STREET PATTERN DETECTED`);
                }
                if (/^\d+[A-Z]?$/i.test(cellStr) && cellStr.length < 6) {
                  console.log(`    🏠 HOUSE NUMBER PATTERN DETECTED`);
                }
                if (/^\d+\s+[A-Z]/.test(cellStr)) {
                  console.log(`    🏘️  FULL ADDRESS PATTERN DETECTED`);
                }
                if (/perro|rejas|no.*tocar|no.*visitar|apartamento|apt/i.test(cellStr)) {
                  console.log(`    📝 NOTE PATTERN DETECTED`);
                }
              }
            }
            rowCount++;
          }
        }
        
        // Pattern summary for this sheet
        console.log(`\n📈 PATTERN SUMMARY FOR ${sheetName}:`);
        let streetCount = 0;
        let numberCount = 0;
        let addressCount = 0;
        let noteCount = 0;
        
        for (let i = 0; i < data.length; i++) {
          const row = data[i];
          if (!row || row.length === 0) continue;
          
          for (let j = 0; j < row.length; j++) {
            const cell = row[j];
            if (!cell) continue;
            
            const cellStr = cell.toString().trim();
            
            if (/\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(cellStr)) {
              streetCount++;
            }
            if (/^\d+[A-Z]?$/i.test(cellStr) && cellStr.length < 6) {
              numberCount++;
            }
            if (/^\d+\s+[A-Z]/.test(cellStr)) {
              addressCount++;
            }
            if (/perro|rejas|no.*tocar|no.*visitar|apartamento|apt/i.test(cellStr)) {
              noteCount++;
            }
          }
        }
        
        console.log(`  🛣️  Street patterns: ${streetCount}`);
        console.log(`  🏠 Number patterns: ${numberCount}`);
        console.log(`  🏘️  Full address patterns: ${addressCount}`);
        console.log(`  📝 Note patterns: ${noteCount}`);
        
        // Determine potential parsing strategy
        console.log(`\n💡 PARSING STRATEGY RECOMMENDATION:`);
        if (streetCount === 0 && numberCount === 0 && addressCount === 0) {
          console.log(`  ❌ NO RECOGNIZABLE PATTERNS - File may be empty or corrupted`);
        } else if (addressCount > 0) {
          console.log(`  ✅ Full addresses found - Use direct address extraction`);
        } else if (streetCount > 0 && numberCount > 0) {
          console.log(`  ✅ Street + Number patterns found - Use combination parsing`);
          console.log(`  📍 Street count: ${streetCount}, Number count: ${numberCount}`);
        } else if (numberCount > 0) {
          console.log(`  ⚠️  Numbers found but no streets - May need manual street identification`);
        } else {
          console.log(`  ⚠️  Unusual pattern - Needs custom parser`);
        }
        
        console.log(`\n`);
      }
      
    } catch (error) {
      console.log(`❌ Error reading file: ${error.message}\n`);
    }
  }
  
  console.log(`${'='.repeat(60)}`);
  console.log(`🎯 ANALYSIS COMPLETE`);
  console.log(`${'='.repeat(60)}`);
}

inspectMissing5Territories().catch(console.error);
