import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import { EnhancedDocumentService } from '@/lib/services/enhancedDocumentService';
import { extractAndVerifyToken } from '@/lib/middleware/auth';

/**
 * POST /api/documents/upload
 * Upload a document file and create document record
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Check if user has permission to upload documents
    if (!['elder', 'ministerial_servant'].includes(user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const category = formData.get('category') as string;
    const subcategory = formData.get('subcategory') as string;
    const visibility = formData.get('visibility') as string;
    const priority = formData.get('priority') as string;
    const tags = formData.get('tags') as string;
    const expirationDate = formData.get('expirationDate') as string;
    const publishDate = formData.get('publishDate') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!title) {
      return NextResponse.json({ error: 'Title is required' }, { status: 400 });
    }

    // Validate file type and size
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'File type not allowed' }, { status: 400 });
    }

    // 10MB limit
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: 'File size too large (max 10MB)' }, { status: 400 });
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'documents');
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const originalName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filename = `${timestamp}_${originalName}`;
    const filePath = join(uploadDir, filename);
    const publicPath = `/uploads/documents/${filename}`;

    // Save file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Create document record
    const documentData = {
      title,
      description: description || undefined,
      filename: originalName,
      filePath: publicPath,
      fileSize: file.size,
      mimeType: file.type,
      category: category || undefined,
      subcategory: subcategory || undefined,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      visibility: visibility || 'ALL_MEMBERS',
      priority: priority || 'NORMAL',
      expirationDate: expirationDate ? new Date(expirationDate) : undefined,
      publishDate: publishDate ? new Date(publishDate) : undefined,
      congregationId: user.congregationId,
      uploadedById: user.userId,
    };

    const document = await EnhancedDocumentService.createDocument(documentData);

    return NextResponse.json({
      document,
      message: 'Document uploaded successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error uploading document:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to upload document' },
      { status: 500 }
    );
  }
}
