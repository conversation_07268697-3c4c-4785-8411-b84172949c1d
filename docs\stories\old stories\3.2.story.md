# Story 3.2: Weekend Meeting Management System

## Status

Draft

## Story

**As a** meeting coordinator and elder,
**I want** to manage weekend meetings with public talk and Watchtower study coordination,
**so that** I can coordinate speakers, assign conductors/readers, track visiting speakers, and maintain the exact workflow we currently use for weekend meeting management.

## Acceptance Criteria

1. **Weekend Meeting Creation and Scheduling (UI Reference: Reunión-Fin-de-Semana.png)**
   - I can create weekend meetings for specific dates using interface matching weekend meeting layout
   - I can set meeting location (Kingdom Hall or Zoom with link) following location selection pattern
   - I can assign chairman, opening prayer, and closing prayer using dropdown member selection
   - I can add meeting notes and special announcements in the notes section
   - Interface follows the card-based layout with Spanish-first terminology ("Reunión Fin de Semana")

2. **Public Talk Management (UI Reference: Weekend meeting admin interfaces)**
   - I can assign public talk speakers (local or visiting) using speaker selection interface
   - I can enter public talk titles and themes following the text input patterns shown
   - I can manage visiting speaker information (name, congregation) with dedicated fields
   - I can track speaker history and avoid repetition through speaker management interface
   - Speaker assignment follows the modal pattern similar to midweek meeting assignments

3. **Watchtower Study Coordination (UI Reference: Weekend meeting coordination patterns)**
   - I can assign Watchtower study conductors using member dropdown selection
   - I can assign Watchtower readers following the same member selection pattern
   - I can link to current Watchtower study articles with article reference fields
   - I can track conductor/reader rotation for fair distribution through assignment history
   - Interface uses the same tabbed layout pattern for different assignment types

4. **Visiting Speaker Management (UI Reference: Member management patterns)**
   - I can add visiting speakers from other congregations using add member form pattern
   - I can track speaker congregation and contact information in dedicated fields
   - I can manage speaker availability and scheduling through calendar interface
   - I can maintain speaker database for future reference with search and filter capabilities
   - Speaker profiles follow the member card layout pattern

5. **Meeting Display and Navigation (UI Reference: Reunión-Fin-de-Semana.png)**
   - I can view weekend meetings in the card-based format shown in member-facing interface
   - I can see upcoming meetings with speaker assignments following the meeting card layout
   - I can edit existing meetings using the administrative interface pattern
   - I can mark meetings as published/unpublished with toggle controls
   - Navigation follows the breadcrumb and back button patterns established

6. **Integration with Song Management (UI Reference: Programas-Salón-Del-Reino.png)**
   - Opening and closing songs are automatically resolved to titles following song display format
   - Song selection integrates with existing song catalog using established patterns
   - Song numbers display with proper titles in Spanish/English as shown in programs
   - Maintains existing JW.org song integration patterns without modification
   - Song interface follows the format shown in meeting programs

7. **Permission-Based Access and Spanish-First Design**
   - Only elders and ministerial servants can manage meetings following admin access patterns
   - Publishers can view their assigned parts in the member-facing interface shown
   - Mobile-responsive interface for all meeting functions matching Member Area patterns
   - Proper congregation isolation for multi-tenant security
   - All text, labels, and terminology in Spanish following the reference images exactly

## Tasks

- [ ] Create weekend meeting management database integration (AC: 1, 5)
  - [ ] Implement weekend meeting CRUD operations using existing schema
  - [ ] Create meeting location and Zoom link management
  - [ ] Add meeting publication status tracking
  - [ ] Implement meeting date validation and conflict detection
  - [ ] Add congregation isolation and permission validation
  - [ ] Create meeting duplication and template functionality

- [ ] Build public talk assignment system (AC: 2, 4)
  - [ ] Create public talk speaker assignment interface
  - [ ] Implement local vs visiting speaker selection
  - [ ] Add public talk title and theme management
  - [ ] Create visiting speaker database and management
  - [ ] Implement speaker history tracking to avoid repetition
  - [ ] Add speaker congregation and contact information

- [ ] Develop Watchtower study coordination (AC: 3)
  - [ ] Create Watchtower conductor assignment interface
  - [ ] Implement Watchtower reader assignment system
  - [ ] Add conductor/reader rotation tracking
  - [ ] Create assignment history for fair distribution
  - [ ] Implement Watchtower article linking and references
  - [ ] Add study preparation notes and materials

- [ ] Create weekend meeting UI components following exact reference designs (AC: 5, 7)
  - [ ] Build weekend meeting calendar view matching the layout shown in weekend meeting interfaces
  - [ ] Create meeting creation and editing forms following the card-based design pattern
  - [ ] Implement speaker assignment interface matching the modal pattern from midweek assignments
  - [ ] Create meeting display cards following the format shown in Reunión-Fin-de-Semana.png
  - [ ] Add mobile-responsive design matching Member Area interface patterns
  - [ ] Implement touch-friendly assignment controls following mobile UI patterns shown

- [ ] Integrate with existing song management (AC: 6)
  - [ ] Connect with existing song catalog system
  - [ ] Implement song number to title resolution
  - [ ] Add song selection interface for opening/closing songs
  - [ ] Preserve existing JW.org song integration patterns
  - [ ] Maintain current caching and fetching mechanisms
  - [ ] Ensure multilingual song title support

- [ ] Implement visiting speaker management (AC: 4)
  - [ ] Create visiting speaker database and profiles
  - [ ] Add speaker congregation and contact management
  - [ ] Implement speaker availability and scheduling system
  - [ ] Create speaker invitation and confirmation workflow
  - [ ] Add speaker travel and accommodation notes
  - [ ] Implement speaker feedback and rating system

- [ ] Build meeting workflow and administration (AC: 1, 7)
  - [ ] Add role-based weekend meeting management access
  - [ ] Implement congregation isolation for multi-tenant security
  - [ ] Create meeting assignment permission validation
  - [ ] Add meeting visibility controls based on user role
  - [ ] Implement audit trail for meeting changes
  - [ ] Add meeting export and printing capabilities

## UI/UX Compliance Requirements

### Visual Design Compliance
- **Consistent Design Language**: Match the visual design established in midweek meeting interfaces
- **Weekend Meeting Styling**: Follow the specific color scheme and layout for weekend meetings
- **Card Layout**: Implement the exact card-based layout pattern with consistent spacing
- **Form Controls**: Use identical input field styles, dropdowns, and form layouts from reference images
- **Speaker Management**: Follow the member management UI patterns for visiting speaker interfaces

### Spanish-First Interface Requirements
- **Weekend Terminology**: Use exact Spanish terms for weekend meetings:
  - "Reunión Fin de Semana" for weekend meeting display
  - "Discurso Público" for public talk
  - "Estudio de La Atalaya" for Watchtower study
  - "Orador Visitante" for visiting speaker
  - "Conductor" and "Lector" for study assignments
- **Speaker Labels**: Follow Spanish terminology for speaker roles and assignments
- **Navigation Labels**: Match navigation terminology exactly as shown in reference images

### Layout Pattern Compliance
- **Weekend Meeting Interface**: Follow the layout pattern shown in Reunión-Fin-de-Semana.png
- **Speaker Assignment**: Use the modal overlay pattern consistent with midweek assignments
- **Visiting Speaker Management**: Follow the member management interface patterns
- **Calendar Integration**: Match the calendar display format from existing interfaces
- **Mobile Layout**: Follow the responsive patterns shown in Member Area images

### Interactive Element Requirements
- **Speaker Selection**: Dropdown menus should match the style and behavior of member selection
- **Visiting Speaker Modal**: Speaker management modals should follow established patterns
- **Assignment Workflow**: Speaker assignment workflow should match midweek meeting patterns
- **Publication Controls**: Toggle controls should match existing design patterns

## Technical Requirements

### Database Schema
- Utilize existing `weekend_meetings` and `weekend_meeting_parts` tables
- Maintain proper foreign key relationships with members and congregations
- Implement proper indexing for date-based queries and speaker lookups
- Add validation constraints for meeting dates and speaker assignments
- Create visiting speaker database with congregation references

### API Design
- RESTful endpoints following existing patterns: `/api/meetings/weekend`
- Proper authentication middleware using existing JWT system
- Congregation-scoped queries for multi-tenant isolation
- Batch operations for multiple speaker assignments
- Real-time updates for collaborative meeting planning
- Integration with existing member management APIs

### Frontend Architecture
- React components following existing design patterns
- Mobile-first responsive design using Tailwind CSS
- State management for meeting data and speaker assignments
- Form validation and error handling
- Integration with existing dashboard navigation
- Reusable components shared with midweek meeting system

### Song Integration
- Preserve existing song catalog fetching mechanisms
- Maintain current caching strategies for song data
- Ensure compatibility with existing URL patterns
- Implement graceful fallbacks for offline scenarios
- Support for multilingual song titles (Spanish/English)

## Definition of Done

- [ ] All weekend meeting CRUD operations work correctly with proper validation
- [ ] Public talk speaker assignment system allows local and visiting speaker management
- [ ] Watchtower study coordination enables conductor and reader assignment
- [ ] Visiting speaker database maintains comprehensive speaker information
- [ ] Mobile-responsive interface works on all device sizes
- [ ] Song integration maintains existing functionality without changes
- [ ] Permission system properly restricts access based on user roles
- [ ] **UI Compliance**: All components match reference images pixel-perfect
  - [ ] Weekend meeting interface matches Reunión-Fin-de-Semana.png layout exactly
  - [ ] Speaker assignment modal follows established modal patterns
  - [ ] Visiting speaker management matches member management UI patterns
  - [ ] Mobile interface matches Member Area responsive patterns
- [ ] **Spanish Localization**: All text matches weekend meeting terminology exactly
  - [ ] "Reunión Fin de Semana", "Discurso Público", "Estudio de La Atalaya" used correctly
  - [ ] Speaker roles use proper Spanish terminology ("Orador Visitante", "Conductor", "Lector")
- [ ] **Visual Design**: Colors, typography, and spacing match reference images
- [ ] Meeting data is properly isolated by congregation for multi-tenant security
- [ ] Performance is optimized for large numbers of meetings and speakers
- [ ] Comprehensive error handling and user feedback is implemented
- [ ] Integration tests verify weekend meeting workflow from creation to completion
- [ ] **User Acceptance**: Interface feels identical to existing system for users
- [ ] **Cross-Meeting Consistency**: Weekend meeting interface feels consistent with midweek meetings

## Dependencies

- Existing authentication system (Stories 1.3, 2.1)
- Member management system (Story 2.2)
- Database schema (weekend_meetings, weekend_meeting_parts tables)
- Song catalog system (existing JW.org integration)
- Admin dashboard framework (Story 1.4)
- Midweek meeting management patterns (Story 3.1)

## Notes

- This story builds on patterns established in Story 3.1 (Midweek Meeting Management)
- Weekend meetings have a simpler structure than midweek meetings but require speaker coordination
- Visiting speaker management is a unique aspect that requires careful database design
- Song integration must work identically to avoid disrupting congregation processes
- The interface should feel familiar to users of the existing system
- Consider the coordination required between congregations for visiting speakers

## Dev Agent Record

### Agent Model Used

_To be populated by development agent_

### Debug Log References

_To be populated by development agent_

### Completion Notes List

_To be populated by development agent_

### File List

_To be populated by development agent_

### Change Log

_To be populated by development agent_
