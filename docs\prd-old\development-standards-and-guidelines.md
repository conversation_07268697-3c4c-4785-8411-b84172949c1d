# Development Standards and Guidelines

## **Critical Development Requirements**

**Environment Configuration Standards:**

- All configuration variables must be defined in `.env` file as single source of truth
- Never hardcode localhost, ports, URLs, or any environment-specific values in codebase files
- Use default ports specified in `.env` file for all development and production environments
- Create standardized `npm run dev` script that initializes servers using environment variables only

**Package and Dependency Management:**

- Always download and use latest stable versions of all packages and dependencies
- Regular dependency updates for security patches and performance improvements
- Maintain up-to-date package.json with latest compatible versions
- Use exact version pinning only when necessary for stability

**Code Quality and Reuse Standards:**

- Use `codebase-retrieval` tool before implementing new components to prevent duplication
- Extend existing implementations rather than creating duplicate functionality
- Maintain project simplicity focusing on core congregation management functionality
- No mock implementations - all functionality must be fully implemented with real data connections

**UI and Performance Standards:**

- Design functionality to fit within single viewport without scrolling (except data listings)
- Prioritize compact, efficient layouts maximizing screen real estate
- Modern, attractive UI design while preserving exact existing visual identity
- Mobile-first responsive design with touch optimization

**Implementation Priority Order:**

1. Core congregation management functionality preservation
2. Exact UI/UX preservation and modern responsive design
3. Performance optimization for mobile devices
4. Multi-congregation architecture features
