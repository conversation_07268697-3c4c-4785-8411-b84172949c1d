#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function testAuth() {
  const prisma = new PrismaClient();
  
  try {
    // Test congregation 1441 specifically
    const cong1441 = await prisma.congregation.findUnique({
      where: { id: '1441' },
      select: { id: true, name: true, pin: true }
    });
    
    if (cong1441) {
      console.log(`Testing congregation 1441: ${cong1441.name}`);
      console.log(`Stored PIN hash: ${cong1441.pin.substring(0, 20)}...`);
      
      // Test the PIN that should work for 1441
      const testPin = '1930';
      try {
        const isValid = await bcrypt.compare(testPin, cong1441.pin);
        console.log(`PIN '${testPin}' for 1441: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
      } catch (e) {
        console.log(`Error testing PIN: ${e.message}`);
        // Try plain text
        if (testPin === cong1441.pin) {
          console.log(`PIN '${testPin}' works as plain text`);
        }
      }
    }
    
    // Test CORALOES
    const coraloes = await prisma.congregation.findUnique({
      where: { id: 'CORALOES' },
      select: { id: true, name: true, pin: true }
    });
    
    if (coraloes) {
      console.log(`\nTesting congregation CORALOES: ${coraloes.name}`);
      const testPin = 'coralpin123';
      const isValid = await bcrypt.compare(testPin, coraloes.pin);
      console.log(`PIN '${testPin}' for CORALOES: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testAuth();
