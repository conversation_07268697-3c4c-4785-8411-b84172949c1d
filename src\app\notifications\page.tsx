'use client';

/**
 * Notifications Page
 *
 * Member interface for viewing and managing notifications and communications.
 * Displays notifications, allows marking as read, and provides filtering options.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  NotificationData,
  NotificationCategory,
  NotificationPriority,
  NotificationSummary,
  NotificationStatus,
  NOTIFICATION_CATEGORY_LABELS,
  NOTIFICATION_PRIORITY_LABELS
} from '@/lib/services/communicationService';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

export default function NotificationsPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [summary, setSummary] = useState<NotificationSummary | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<NotificationCategory | 'ALL'>('ALL');
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAuthentication();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      loadNotificationsData();
    }
  }, [user, selectedCategory, showUnreadOnly]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();
      setUser(data.member);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const loadNotificationsData = async () => {
    if (!user) return;

    setIsLoadingNotifications(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');

      // Load summary and notifications
      const [summaryResponse, notificationsResponse] = await Promise.all([
        fetch('/api/notifications/summary', {
          headers: { 'Authorization': `Bearer ${token}` },
        }),
        fetch(`/api/notifications?${new URLSearchParams({
          ...(selectedCategory !== 'ALL' && { category: selectedCategory }),
          ...(showUnreadOnly && { isRead: 'false' }),
          limit: '20',
        })}`, {
          headers: { 'Authorization': `Bearer ${token}` },
        }),
      ]);

      if (summaryResponse.ok) {
        const summaryData = await summaryResponse.json();
        setSummary(summaryData.summary);
      }

      if (notificationsResponse.ok) {
        const notificationsData = await notificationsResponse.json();
        setNotifications(notificationsData.notifications);
      } else {
        const errorData = await notificationsResponse.json();
        setError(errorData.error || 'Error al cargar notificaciones');
      }
    } catch (error) {
      console.error('Error loading notifications data:', error);
      setError('Error al cargar los datos de notificaciones');
    } finally {
      setIsLoadingNotifications(false);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ notificationId }),
      });

      if (response.ok) {
        // Update the notification in the local state
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === notificationId
              ? { ...notification, readAt: new Date(), status: NotificationStatus.READ }
              : notification
          )
        );

        // Reload summary to update unread count
        loadNotificationsData();
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Error al marcar como leída');
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      setError('Error al marcar la notificación como leída');
    }
  };

  const getNotificationCategoryColor = (category: NotificationCategory): string => {
    const colors: Record<NotificationCategory, string> = {
      [NotificationCategory.URGENT]: 'bg-red-100 text-red-800 border-red-200',
      [NotificationCategory.GENERAL]: 'bg-blue-100 text-blue-800 border-blue-200',
      [NotificationCategory.EVENTS]: 'bg-purple-100 text-purple-800 border-purple-200',
      [NotificationCategory.ASSIGNMENTS]: 'bg-green-100 text-green-800 border-green-200',
      [NotificationCategory.TASKS]: 'bg-orange-100 text-orange-800 border-orange-200',
      [NotificationCategory.MEETINGS]: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      [NotificationCategory.SERVICE]: 'bg-teal-100 text-teal-800 border-teal-200',
      [NotificationCategory.ANNOUNCEMENTS]: 'bg-pink-100 text-pink-800 border-pink-200',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getNotificationPriorityColor = (priority: NotificationPriority): string => {
    const colors: Record<NotificationPriority, string> = {
      [NotificationPriority.LOW]: 'text-gray-600',
      [NotificationPriority.NORMAL]: 'text-blue-600',
      [NotificationPriority.HIGH]: 'text-orange-600',
      [NotificationPriority.URGENT]: 'text-red-600',
    };
    return colors[priority] || 'text-gray-600';
  };

  const formatNotificationDate = (date: Date | string): string => {
    const notificationDate = typeof date === 'string' ? new Date(date) : date;
    return notificationDate.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isNotificationUnread = (notification: NotificationData): boolean => {
    return !notification.readAt;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-blue-200 hover:text-white mb-2 flex items-center"
            >
              ← Volver al Panel
            </button>
            <h1 className="text-2xl font-bold">Notificaciones</h1>
            <p className="text-blue-200">Comunicaciones y avisos de la congregación</p>
          </div>
          <button
            onClick={() => router.push('/notifications/preferences')}
            className="bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors"
          >
            Preferencias
          </button>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Total de Notificaciones</h3>
              <p className="text-3xl font-bold text-blue-600">{summary.totalNotifications}</p>
              <p className="text-gray-600 text-sm">Notificaciones recibidas</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Sin Leer</h3>
              <p className="text-3xl font-bold text-red-600">{summary.unreadNotifications}</p>
              <p className="text-gray-600 text-sm">Requieren atención</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Más Reciente</h3>
              {summary.recentNotifications.length > 0 ? (
                <div>
                  <p className="text-lg font-semibold text-gray-900">{summary.recentNotifications[0].title}</p>
                  <p className="text-gray-600 text-sm">
                    {formatNotificationDate(summary.recentNotifications[0].createdAt)}
                  </p>
                </div>
              ) : (
                <p className="text-gray-500">No hay notificaciones</p>
              )}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Filtros</h2>

          {/* Category Filter */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Categoría</label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory('ALL')}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  selectedCategory === 'ALL'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Todas
              </button>
              {Object.values(NotificationCategory).map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-md font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {NOTIFICATION_CATEGORY_LABELS[category]}
                </button>
              ))}
            </div>
          </div>

          {/* Read Status Filter */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="showUnreadOnly"
              checked={showUnreadOnly}
              onChange={(e) => setShowUnreadOnly(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="showUnreadOnly" className="ml-2 block text-sm text-gray-700">
              Mostrar solo no leídas
            </label>
          </div>
        </div>

        {/* Notifications List */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            {selectedCategory === 'ALL' ? 'Todas las Notificaciones' : NOTIFICATION_CATEGORY_LABELS[selectedCategory as NotificationCategory]}
          </h2>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {isLoadingNotifications ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Cargando notificaciones...</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No hay notificaciones disponibles</p>
            </div>
          ) : (
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`border-2 rounded-lg p-6 transition-all duration-200 hover:shadow-md ${
                    isNotificationUnread(notification)
                      ? 'border-blue-200 bg-blue-50'
                      : 'border-gray-200 bg-white'
                  }`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{notification.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getNotificationCategoryColor(notification.category)}`}>
                          {NOTIFICATION_CATEGORY_LABELS[notification.category]}
                        </span>
                        <span className={`text-xs font-medium ${getNotificationPriorityColor(notification.priority)}`}>
                          {NOTIFICATION_PRIORITY_LABELS[notification.priority]}
                        </span>
                        {isNotificationUnread(notification) && (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-600 border border-red-200">
                            Nueva
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 mb-3">{notification.message}</p>
                    </div>
                    {isNotificationUnread(notification) && (
                      <button
                        onClick={() => markAsRead(notification.id)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Marcar como leída
                      </button>
                    )}
                  </div>

                  <div className="text-sm text-gray-500">
                    <p>Recibida: {formatNotificationDate(notification.createdAt)}</p>
                    {notification.readAt && (
                      <p>Leída: {formatNotificationDate(notification.readAt)}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
