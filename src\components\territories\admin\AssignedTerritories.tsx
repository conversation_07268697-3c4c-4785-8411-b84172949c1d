'use client';

import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  MapPinIcon,
  CalendarDaysIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

interface AssignedTerritory {
  id: string;
  territoryNumber: string;
  address: string;
  assignedAt: string;
  daysAssigned: number;
}

interface MemberWithAssignments {
  id: string;
  name: string;
  role: string;
  assignments: AssignedTerritory[];
  totalAssignments: number;
}

export default function AssignedTerritories() {
  const [membersWithAssignments, setMembersWithAssignments] = useState<MemberWithAssignments[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [unassigningTerritory, setUnassigningTerritory] = useState<string | null>(null);

  useEffect(() => {
    loadAssignedTerritories();
  }, []);

  const loadAssignedTerritories = async () => {
    try {
      setIsLoading(true);
      const rawToken = localStorage.getItem('hermanos_token');
      if (!rawToken) {
        throw new Error('No authentication token found');
      }

      let token = rawToken.trim();
      if (token.toLowerCase().startsWith('bearer ')) {
        token = token.substring(7);
      }

      const response = await fetch('/api/territories/assignments?type=members', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Error al cargar asignaciones');
      }

      const result = await response.json();
      setMembersWithAssignments(result.data?.membersWithAssignments || []);
    } catch (err) {
      console.error('Error loading assigned territories:', err);
      setError(err instanceof Error ? err.message : 'Error al cargar asignaciones');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredMembers = membersWithAssignments;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const handleUnassignTerritory = async (assignmentId: string, territoryNumber: string, memberName: string) => {
    if (!confirm(`¿Estás seguro de que quieres desasignar el territorio ${territoryNumber} de ${memberName}?`)) {
      return;
    }

    try {
      setUnassigningTerritory(assignmentId);
      const rawToken = localStorage.getItem('hermanos_token');
      if (!rawToken) {
        throw new Error('No authentication token found');
      }

      let token = rawToken.trim();
      if (token.toLowerCase().startsWith('bearer ')) {
        token = token.substring(7);
      }

      const response = await fetch(`/api/territories/assignments/${assignmentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Error al desasignar territorio');
      }

      // Refresh the assignments list
      await loadAssignedTerritories();

    } catch (err) {
      alert(err instanceof Error ? err.message : 'Error al desasignar territorio');
    } finally {
      setUnassigningTerritory(null);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando asignaciones...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 lg:p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Territorios Asignados</h2>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      {/* Members with Assignments - Compact View */}
      <div className="bg-white border border-gray-200 rounded-lg">
        {filteredMembers.length === 0 ? (
          <div className="text-center py-12">
            <UserIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              No hay miembros con territorios asignados
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredMembers.map((member) => (
              <div key={member.id} className="p-4">
                {/* Member with Territory Numbers */}
                <div className="flex items-center flex-wrap gap-2">
                  <span className="font-medium text-gray-900 mr-2">{member.name}:</span>
                  <div className="flex flex-wrap gap-2">
                    {member.assignments.map((territory) => (
                      <div
                        key={territory.id}
                        className="group relative inline-flex items-center"
                      >
                        <span
                          className="bg-blue-600 text-white px-3 py-2 rounded-md text-base font-bold min-w-[3rem] text-center shadow-sm border border-blue-700"
                          style={{
                            color: '#ffffff !important',
                            backgroundColor: '#2563eb !important',
                            fontSize: '16px !important',
                            fontWeight: '700 !important',
                            display: 'inline-block !important'
                          }}
                          title={`Territory: ${territory.territoryNumber || 'undefined'} - ID: ${territory.id}`}
                        >
                          {territory.territoryNumber || 'N/A'}
                        </span>
                        <button
                          onClick={() => handleUnassignTerritory(territory.id, territory.territoryNumber, member.name)}
                          disabled={unassigningTerritory === territory.id}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600 disabled:opacity-50"
                          title={`Desasignar territorio ${territory.territoryNumber}`}
                        >
                          {unassigningTerritory === territory.id ? (
                            <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                          ) : (
                            '×'
                          )}
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
