/**
 * PIN Settings Form Component
 * 
 * Form for configuring PIN requirements and security settings
 * for congregations. Only accessible to developers.
 */

import React, { useState, useEffect } from 'react';
import { PinSettings } from '@/lib/services/pinService';

interface PinSettingsFormProps {
  settings?: PinSettings | null;
  onSubmit: (data: PinSettingsFormData) => void;
  onCancel: () => void;
  isLoading: boolean;
  canEdit: boolean;
}

export interface PinSettingsFormData {
  minLength: number;
  maxLength: number;
  requireNumeric: boolean;
  requireAlphanumeric: boolean;
  requireSpecialChars: boolean;
  allowSequential: boolean;
  allowRepeated: boolean;
  expirationDays: number | null;
  bcryptRounds: number;
}

export default function PinSettingsForm({
  settings,
  onSubmit,
  onCancel,
  isLoading,
  canEdit,
}: PinSettingsFormProps) {
  const [formData, setFormData] = useState<PinSettingsFormData>({
    minLength: 4,
    maxLength: 8,
    requireNumeric: true,
    requireAlphanumeric: false,
    requireSpecialChars: false,
    allowSequential: true,
    allowRepeated: true,
    expirationDays: null,
    bcryptRounds: 12,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (settings) {
      setFormData({
        minLength: settings.minLength,
        maxLength: settings.maxLength,
        requireNumeric: settings.requireNumeric,
        requireAlphanumeric: settings.requireAlphanumeric,
        requireSpecialChars: settings.requireSpecialChars,
        allowSequential: settings.allowSequential,
        allowRepeated: settings.allowRepeated,
        expirationDays: settings.expirationDays,
        bcryptRounds: settings.bcryptRounds,
      });
    }
  }, [settings]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (formData.minLength < 3 || formData.minLength > 20) {
      newErrors.minLength = 'La longitud mínima debe estar entre 3 y 20';
    }

    if (formData.maxLength < 3 || formData.maxLength > 20) {
      newErrors.maxLength = 'La longitud máxima debe estar entre 3 y 20';
    }

    if (formData.maxLength < formData.minLength) {
      newErrors.maxLength = 'La longitud máxima debe ser mayor o igual a la mínima';
    }

    if (formData.bcryptRounds < 10 || formData.bcryptRounds > 15) {
      newErrors.bcryptRounds = 'Las rondas de bcrypt deben estar entre 10 y 15';
    }

    if (formData.expirationDays !== null && formData.expirationDays < 1) {
      newErrors.expirationDays = 'Los días de expiración deben ser mayor a 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSubmit(formData);
  };

  const handleInputChange = (field: keyof PinSettingsFormData, value: string | boolean | number | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        Configuración de PINs
      </h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Length Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Longitud Mínima *
            </label>
            <input
              type="number"
              min="3"
              max="20"
              value={formData.minLength}
              onChange={(e) => handleInputChange('minLength', parseInt(e.target.value, 10))}
              className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.minLength ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={!canEdit}
            />
            {errors.minLength && (
              <p className="text-red-600 text-sm mt-1">{errors.minLength}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Longitud Máxima *
            </label>
            <input
              type="number"
              min="3"
              max="20"
              value={formData.maxLength}
              onChange={(e) => handleInputChange('maxLength', parseInt(e.target.value, 10))}
              className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.maxLength ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={!canEdit}
            />
            {errors.maxLength && (
              <p className="text-red-600 text-sm mt-1">{errors.maxLength}</p>
            )}
          </div>
        </div>

        {/* Character Requirements */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Requisitos de Caracteres</h3>
          <div className="space-y-3">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.requireNumeric}
                onChange={(e) => handleInputChange('requireNumeric', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                disabled={!canEdit}
              />
              <span className="text-sm text-gray-700">Requiere números</span>
            </label>

            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.requireAlphanumeric}
                onChange={(e) => handleInputChange('requireAlphanumeric', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                disabled={!canEdit}
              />
              <span className="text-sm text-gray-700">Requiere letras y números</span>
            </label>

            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.requireSpecialChars}
                onChange={(e) => handleInputChange('requireSpecialChars', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                disabled={!canEdit}
              />
              <span className="text-sm text-gray-700">Requiere caracteres especiales (!@#$%^&*)</span>
            </label>
          </div>
        </div>

        {/* Restrictions */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Restricciones</h3>
          <div className="space-y-3">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.allowSequential}
                onChange={(e) => handleInputChange('allowSequential', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                disabled={!canEdit}
              />
              <span className="text-sm text-gray-700">Permitir caracteres secuenciales (123, abc)</span>
            </label>

            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.allowRepeated}
                onChange={(e) => handleInputChange('allowRepeated', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                disabled={!canEdit}
              />
              <span className="text-sm text-gray-700">Permitir caracteres repetidos consecutivos (11, aa)</span>
            </label>
          </div>
        </div>

        {/* Security Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Días de Expiración (opcional)
            </label>
            <input
              type="number"
              min="1"
              value={formData.expirationDays || ''}
              onChange={(e) => handleInputChange('expirationDays', e.target.value ? parseInt(e.target.value, 10) : null)}
              className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.expirationDays ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Sin expiración"
              disabled={!canEdit}
            />
            {errors.expirationDays && (
              <p className="text-red-600 text-sm mt-1">{errors.expirationDays}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Dejar vacío para PINs sin expiración
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Rondas de Bcrypt *
            </label>
            <input
              type="number"
              min="10"
              max="15"
              value={formData.bcryptRounds}
              onChange={(e) => handleInputChange('bcryptRounds', parseInt(e.target.value, 10))}
              className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.bcryptRounds ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={!canEdit}
            />
            {errors.bcryptRounds && (
              <p className="text-red-600 text-sm mt-1">{errors.bcryptRounds}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Mayor número = más seguro pero más lento
            </p>
          </div>
        </div>

        {/* Actions */}
        {canEdit && (
          <div className="flex justify-end space-x-4 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              disabled={isLoading}
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
              disabled={isLoading}
            >
              {isLoading ? 'Guardando...' : 'Guardar Configuración'}
            </button>
          </div>
        )}

        {!canEdit && (
          <div className="pt-4 border-t">
            <p className="text-sm text-gray-600 text-center">
              Solo los desarrolladores pueden modificar la configuración de PINs
            </p>
          </div>
        )}
      </form>
    </div>
  );
}
