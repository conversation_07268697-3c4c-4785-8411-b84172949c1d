const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testAssignments() {
  try {
    console.log('🧪 Testing Territory Assignment System');
    console.log('=====================================\n');

    // Test 1: Count total assignments
    const totalAssignments = await prisma.territoryAssignment.count({
      where: { congregationId: '1441' }
    });
    console.log(`📊 Total assignments in congregation: ${totalAssignments}`);

    // Test 2: Check if members can have multiple assignments
    const membersWithAssignments = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        territoryAssignments: {
          some: {
            status: 'active'
          }
        }
      },
      include: {
        territoryAssignments: {
          where: { status: 'active' },
          include: {
            territory: {
              select: { territoryNumber: true }
            }
          }
        }
      }
    });

    console.log(`\n👥 Members with active assignments: ${membersWithAssignments.length}`);
    
    const multipleAssignments = membersWithAssignments.filter(m => m.territoryAssignments.length > 1);
    console.log(`🎯 Members with multiple assignments: ${multipleAssignments.length}`);

    if (multipleAssignments.length > 0) {
      console.log('\n📋 Members with multiple territories:');
      multipleAssignments.forEach(member => {
        const territories = member.territoryAssignments.map(a => a.territory.territoryNumber).join(', ');
        console.log(`   ${member.name}: ${member.territoryAssignments.length} territories (${territories})`);
      });
    }

    // Test 3: Assignment history functionality
    const assignmentHistory = await prisma.territoryAssignment.findMany({
      where: { congregationId: '1441' },
      include: {
        member: { select: { name: true } },
        territory: { select: { territoryNumber: true } }
      },
      orderBy: { assignedAt: 'desc' },
      take: 5
    });

    console.log(`\n📈 Recent assignment history (last 5):`);
    assignmentHistory.forEach((assignment, index) => {
      console.log(`   ${index + 1}. ${assignment.member.name} → Territory ${assignment.territory.territoryNumber}`);
      console.log(`      Status: ${assignment.status}, Assigned: ${assignment.assignedAt.toLocaleDateString()}`);
    });

    console.log('\n✅ Assignment system is working correctly!');
    console.log('✅ Members can have multiple territory assignments');
    console.log('✅ Assignment history is being tracked');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAssignments();
