#!/usr/bin/env node

/**
 * Enhanced Migration Validation Script for Hermanos App
 * 
 * Validates the MySQL to PostgreSQL migration by comparing data integrity,
 * relationships, and business logic constraints.
 */

const mysql = require('mysql2/promise');
const { Pool } = require('pg');
const { PrismaClient } = require('@prisma/client');

class MigrationValidator {
  constructor() {
    this.mysqlConnection = null;
    this.pgPool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });
    this.prisma = new PrismaClient();
    
    this.validationResults = {
      tableComparisons: [],
      relationshipChecks: [],
      dataIntegrityChecks: [],
      businessLogicChecks: [],
      errors: [],
      warnings: [],
    };
  }

  async initialize() {
    try {
      // Initialize MySQL connection
      this.mysqlConnection = await mysql.createConnection({
        host: process.env.MYSQL_HOST || 'localhost',
        user: process.env.MYSQL_USER || 'root',
        password: process.env.MYSQL_PASSWORD || '',
        database: process.env.MYSQL_DATABASE || 'coral_oeste_db',
        charset: 'utf8mb4',
      });

      console.log('✅ MySQL connection established');

      // Test PostgreSQL connection
      await this.pgPool.query('SELECT 1');
      console.log('✅ PostgreSQL connection established');

      // Test Prisma connection
      await this.prisma.$connect();
      console.log('✅ Prisma client connected');

    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async validateTableCounts() {
    console.log('\n📊 Validating table record counts...');

    const tableMappings = [
      { mysql: 'congregations', postgres: 'congregations', model: 'congregation' },
      { mysql: 'congregation_settings', postgres: 'congregation_settings', model: 'congregationSetting' },
      { mysql: 'roles', postgres: 'roles', model: 'role' },
      { mysql: 'members', postgres: 'members', model: 'member' },
      { mysql: 'elder_permissions', postgres: 'elder_permissions', model: 'elderPermission' },
      { mysql: 'service_groups', postgres: 'service_groups', model: 'serviceGroup' },
      { mysql: 'territories', postgres: 'territories', model: 'territory' },
      { mysql: 'tasks', postgres: 'tasks', model: 'task' },
      { mysql: 'task_assignments', postgres: 'task_assignments', model: 'taskAssignment' },
      { mysql: 'field_service_records', postgres: 'field_service_records', model: 'fieldServiceRecord' },
      { mysql: 'letters', postgres: 'letters', model: 'letter' },
      { mysql: 'midweek_meetings', postgres: 'midweek_meetings', model: 'midweekMeeting' },
      { mysql: 'songs', postgres: 'songs', model: 'song' },
      { mysql: 'special_songs', postgres: 'special_songs', model: 'specialSong' },
    ];

    for (const mapping of tableMappings) {
      try {
        // Get MySQL count
        const [mysqlResult] = await this.mysqlConnection.execute(
          `SELECT COUNT(*) as count FROM ${mapping.mysql}`
        );
        const mysqlCount = mysqlResult[0].count;

        // Get PostgreSQL count using Prisma
        const postgresCount = await this.prisma[mapping.model].count();

        const isValid = mysqlCount === postgresCount;
        
        this.validationResults.tableComparisons.push({
          table: mapping.mysql,
          mysqlCount,
          postgresCount,
          isValid,
          difference: postgresCount - mysqlCount,
        });

        const status = isValid ? '✅' : '❌';
        console.log(`${status} ${mapping.mysql}: MySQL(${mysqlCount}) → PostgreSQL(${postgresCount})`);

        if (!isValid) {
          this.validationResults.errors.push(
            `Table ${mapping.mysql}: Count mismatch - MySQL: ${mysqlCount}, PostgreSQL: ${postgresCount}`
          );
        }

      } catch (error) {
        console.error(`❌ Error validating ${mapping.mysql}:`, error.message);
        this.validationResults.errors.push(`Table ${mapping.mysql}: ${error.message}`);
      }
    }
  }

  async validateDataIntegrity() {
    console.log('\n🔍 Validating data integrity...');

    try {
      // Validate congregation data integrity
      const congregations = await this.prisma.congregation.findMany();
      for (const congregation of congregations) {
        if (!congregation.name || !congregation.id) {
          this.validationResults.errors.push(
            `Congregation ${congregation.id}: Missing required fields`
          );
        }
      }

      // Validate member-congregation relationships
      const members = await this.prisma.member.findMany({
        include: { congregation: true }
      });
      
      for (const member of members) {
        if (!member.congregation) {
          this.validationResults.errors.push(
            `Member ${member.id}: Missing congregation relationship`
          );
        }
      }

      // Validate task assignments
      const taskAssignments = await this.prisma.taskAssignment.findMany({
        include: { task: true, congregation: true }
      });

      for (const assignment of taskAssignments) {
        if (!assignment.task || !assignment.congregation) {
          this.validationResults.errors.push(
            `Task assignment ${assignment.id}: Missing required relationships`
          );
        }
      }

      console.log('✅ Data integrity validation completed');

    } catch (error) {
      console.error('❌ Error during data integrity validation:', error.message);
      this.validationResults.errors.push(`Data integrity: ${error.message}`);
    }
  }

  async validateBusinessLogic() {
    console.log('\n🏢 Validating business logic constraints...');

    try {
      // Validate congregation isolation
      const congregations = await this.prisma.congregation.findMany({
        include: {
          members: true,
          tasks: true,
          letters: true,
        }
      });

      for (const congregation of congregations) {
        // Check that all members belong to the correct congregation
        const invalidMembers = congregation.members.filter(
          member => member.congregationId !== congregation.id
        );
        
        if (invalidMembers.length > 0) {
          this.validationResults.errors.push(
            `Congregation ${congregation.id}: ${invalidMembers.length} members with incorrect congregation_id`
          );
        }

        // Check that all tasks belong to the correct congregation
        const invalidTasks = congregation.tasks.filter(
          task => task.congregationId !== congregation.id
        );
        
        if (invalidTasks.length > 0) {
          this.validationResults.errors.push(
            `Congregation ${congregation.id}: ${invalidTasks.length} tasks with incorrect congregation_id`
          );
        }
      }

      console.log('✅ Business logic validation completed');

    } catch (error) {
      console.error('❌ Error during business logic validation:', error.message);
      this.validationResults.errors.push(`Business logic: ${error.message}`);
    }
  }

  async generateValidationReport() {
    console.log('\n📋 Generating validation report...');

    const totalTables = this.validationResults.tableComparisons.length;
    const validTables = this.validationResults.tableComparisons.filter(t => t.isValid).length;
    const totalErrors = this.validationResults.errors.length;
    const totalWarnings = this.validationResults.warnings.length;

    console.log('\n' + '='.repeat(60));
    console.log('📊 MIGRATION VALIDATION REPORT');
    console.log('='.repeat(60));
    console.log(`📋 Tables validated: ${validTables}/${totalTables}`);
    console.log(`❌ Errors: ${totalErrors}`);
    console.log(`⚠️  Warnings: ${totalWarnings}`);

    if (totalErrors > 0) {
      console.log('\n🚨 ERRORS:');
      this.validationResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (totalWarnings > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.validationResults.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning}`);
      });
    }

    const isValid = totalErrors === 0;
    console.log('\n' + (isValid ? '✅ Migration validation PASSED!' : '❌ Migration validation FAILED!'));
    console.log('='.repeat(60));

    return isValid;
  }

  async cleanup() {
    try {
      if (this.mysqlConnection) {
        await this.mysqlConnection.end();
      }
      if (this.pgPool) {
        await this.pgPool.end();
      }
      if (this.prisma) {
        await this.prisma.$disconnect();
      }
    } catch (error) {
      console.error('⚠️ Error during cleanup:', error.message);
    }
  }

  async runFullValidation() {
    console.log('🔍 Starting migration validation...\n');

    try {
      await this.initialize();
      await this.validateTableCounts();
      await this.validateDataIntegrity();
      await this.validateBusinessLogic();
      
      const isValid = await this.generateValidationReport();
      
      if (!isValid) {
        process.exit(1);
      }

    } catch (error) {
      console.error('\n❌ Validation failed:', error.message);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// Main execution
async function main() {
  const validator = new MigrationValidator();
  await validator.runFullValidation();
}

// Run validation if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = MigrationValidator;
