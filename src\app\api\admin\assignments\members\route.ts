/**
 * Eligible Members API Endpoint
 * 
 * Provides list of members eligible for section assignments.
 * Only accessible to coordinator elders.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { getSectionsByRole } from '@/lib/constants/administrativeSections';

/**
 * GET - Retrieve eligible members for section assignments
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user is a coordinator elder
    if (user.role !== 'overseer_coordinator') {
      return NextResponse.json(
        { error: 'Only coordinator elders can view eligible members' },
        { status: 403 }
      );
    }

    // Get all elders and ministerial servants from the congregation
    const eligibleMembers = await prisma.member.findMany({
      where: {
        congregationId: user.congregationId,
        isActive: true,
        role: {
          in: ['elder', 'ministerial_servant'],
        },
      },
      select: {
        id: true,
        name: true,
        role: true,
        createdAt: true,
      },
      orderBy: [
        { role: 'desc' }, // elders first
        { name: 'asc' },
      ],
    });

    // Get current assignments for these members
    const currentAssignments = await prisma.sectionAssignment.findMany({
      where: {
        congregationId: user.congregationId,
        isActive: true,
        memberId: {
          in: eligibleMembers.map(m => m.id),
        },
      },
      select: {
        memberId: true,
        sectionType: true,
      },
    });

    // Create a map of current assignments by member
    const assignmentsByMember = currentAssignments.reduce((acc, assignment) => {
      if (!acc[assignment.memberId]) {
        acc[assignment.memberId] = [];
      }
      acc[assignment.memberId].push(assignment.sectionType);
      return acc;
    }, {} as Record<string, string[]>);

    // Enhance member data with assignment info and eligible sections
    const membersWithAssignments = eligibleMembers.map(member => {
      const currentAssignments = assignmentsByMember[member.id] || [];
      const eligibleSections = getSectionsByRole(member.role);
      
      return {
        id: member.id,
        name: member.name,
        role: member.role,
        createdAt: member.createdAt,
        currentAssignments,
        eligibleSections: eligibleSections.map(section => ({
          id: section.id,
          name: section.name,
          description: section.description,
          color: section.color,
          isAssigned: currentAssignments.includes(section.id),
        })),
        assignmentCount: currentAssignments.length,
      };
    });

    // Get role display names
    const getRoleDisplayName = (role: string): string => {
      const roleNames = {
        'elder': 'Anciano',
        'ministerial_servant': 'Siervo Ministerial',
      };
      return roleNames[role as keyof typeof roleNames] || role;
    };

    const membersWithDisplayNames = membersWithAssignments.map(member => ({
      ...member,
      roleDisplayName: getRoleDisplayName(member.role),
    }));

    return NextResponse.json({
      success: true,
      members: membersWithDisplayNames,
      count: membersWithDisplayNames.length,
      summary: {
        totalEligible: membersWithDisplayNames.length,
        elders: membersWithDisplayNames.filter(m => m.role === 'elder').length,
        ministerialServants: membersWithDisplayNames.filter(m => m.role === 'ministerial_servant').length,
        totalAssignments: currentAssignments.length,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Eligible members GET error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch eligible members',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
