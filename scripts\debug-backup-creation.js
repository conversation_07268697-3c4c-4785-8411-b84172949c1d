/**
 * Debug Backup Creation
 * 
 * Tests the backup creation step by step to identify issues
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs').promises;
const path = require('path');

const prisma = new PrismaClient();

async function debugBackupCreation() {
  try {
    console.log('🔍 Debugging Backup Creation...\n');

    // Test 1: Database connection
    console.log('1. Testing database connection...');
    try {
      await prisma.$connect();
      console.log('✅ Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return;
    }

    // Test 2: Test each table individually
    console.log('\n2. Testing individual table access...');
    const tables = [
      { name: 'congregations', model: 'congregation' },
      { name: 'members', model: 'member' },
      { name: 'roles', model: 'role' },
      { name: 'elder_permissions', model: 'elderPermission' },
      { name: 'songs', model: 'song' },
      { name: 'letters', model: 'letter' },
      { name: 'tasks', model: 'task' },
      { name: 'task_assignments', model: 'taskAssignment' },
      { name: 'field_service_records', model: 'fieldServiceRecord' },
      { name: 'congregation_settings', model: 'congregationSetting' }
    ];

    const tableResults = {};

    for (const table of tables) {
      try {
        let tableData = [];
        
        switch (table.model) {
          case 'congregation':
            tableData = await prisma.congregation.findMany();
            break;
          case 'member':
            tableData = await prisma.member.findMany();
            break;
          case 'role':
            tableData = await prisma.role.findMany();
            break;
          case 'elderPermission':
            tableData = await prisma.elderPermission.findMany();
            break;
          case 'song':
            tableData = await prisma.song.findMany();
            break;
          case 'letter':
            tableData = await prisma.letter.findMany();
            break;
          case 'task':
            tableData = await prisma.task.findMany();
            break;
          case 'taskAssignment':
            tableData = await prisma.taskAssignment.findMany();
            break;
          case 'fieldServiceRecord':
            tableData = await prisma.fieldServiceRecord.findMany();
            break;
          case 'congregationSetting':
            tableData = await prisma.congregationSetting.findMany();
            break;
        }

        tableResults[table.name] = {
          success: true,
          count: tableData.length,
          data: tableData
        };
        console.log(`✅ ${table.name}: ${tableData.length} records`);

      } catch (error) {
        tableResults[table.name] = {
          success: false,
          error: error.message
        };
        console.log(`❌ ${table.name}: ${error.message}`);
      }
    }

    // Test 3: Generate SQL content for successful tables
    console.log('\n3. Testing SQL content generation...');
    let sqlContent = `-- Database Backup Created: ${new Date().toISOString()}\n`;
    sqlContent += `-- Coral Oeste Congregation Database\n\n`;

    for (const table of tables) {
      const result = tableResults[table.name];
      
      if (result.success && result.data.length > 0) {
        try {
          console.log(`  Processing ${table.name}...`);
          
          const tableData = result.data;
          const columns = Object.keys(tableData[0]);
          const columnList = columns.map(col => `"${col}"`).join(', ');
          
          sqlContent += `-- Table: ${table.name} (${tableData.length} records)\n`;
          sqlContent += `INSERT INTO "${table.name}" (${columnList}) VALUES\n`;
          
          // Process only first record for testing
          const firstRow = tableData[0];
          const rowValues = columns.map(col => {
            const value = firstRow[col];
            if (value === null || value === undefined) return 'NULL';
            if (typeof value === 'string') {
              const escaped = value.replace(/'/g, "''").replace(/\\/g, '\\\\');
              return `'${escaped}'`;
            }
            if (value instanceof Date) return `'${value.toISOString()}'`;
            if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
            if (typeof value === 'object') {
              return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
            }
            return String(value);
          });
          
          sqlContent += `(${rowValues.join(', ')});\n\n`;
          console.log(`    ✅ SQL generated for ${table.name}`);
          
        } catch (sqlError) {
          console.log(`    ❌ SQL generation failed for ${table.name}: ${sqlError.message}`);
          sqlContent += `-- Error generating SQL for ${table.name}: ${sqlError.message}\n\n`;
        }
      } else if (result.success) {
        sqlContent += `-- No data in table ${table.name}\n\n`;
      } else {
        sqlContent += `-- Error accessing table ${table.name}: ${result.error}\n\n`;
      }
    }

    sqlContent += `-- Backup completed: ${new Date().toISOString()}\n`;

    // Test 4: File writing
    console.log('\n4. Testing file writing...');
    const backupDir = path.join(process.cwd(), 'backups');
    await fs.mkdir(backupDir, { recursive: true });
    
    const testFilename = `debug_backup_${Date.now()}.sql`;
    const testFilePath = path.join(backupDir, testFilename);
    
    await fs.writeFile(testFilePath, sqlContent, 'utf8');
    const stats = await fs.stat(testFilePath);
    
    console.log(`✅ Test backup file created: ${testFilename}`);
    console.log(`✅ File size: ${formatFileSize(stats.size)}`);
    
    // Show first few lines
    console.log('\n5. Sample backup content:');
    const lines = sqlContent.split('\n').slice(0, 15);
    console.log(lines.join('\n') + '...');

    // Clean up
    await fs.unlink(testFilePath);
    console.log('\n🧹 Test file cleaned up');

    console.log('\n🎉 Backup creation debug completed successfully!');
    console.log('If this test passes, the backup API should work.');

  } catch (error) {
    console.error('❌ Debug test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Helper function to format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run the debug test
debugBackupCreation().catch(console.error);
