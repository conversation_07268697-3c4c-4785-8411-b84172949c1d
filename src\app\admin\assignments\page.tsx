'use client';

/**
 * Administrative Assignments Page
 *
 * Interface for coordinator elders to manage section assignments.
 * Allows assigning, transferring, and removing administrative responsibilities.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AssignmentCard from '@/components/admin/assignments/AssignmentCard';
import AssignmentForm from '@/components/admin/assignments/AssignmentForm';
import { SectionAssignmentData } from '@/lib/services/sectionAssignmentService';
import { ADMINISTRATIVE_SECTIONS, ScopeDefinition } from '@/lib/constants/administrativeSections';
import AdminFooter from '@/components/admin/AdminFooter';

interface EligibleMember {
  id: string;
  name: string;
  role: string;
  roleDisplayName: string;
  currentAssignments: string[];
  eligibleSections: Array<{
    id: string;
    name: string;
    description: string;
    color: string;
    isAssigned: boolean;
  }>;
  assignmentCount: number;
}

export default function AssignmentsPage() {
  const router = useRouter();
  const [assignments, setAssignments] = useState<SectionAssignmentData[]>([]);
  const [members, setMembers] = useState<EligibleMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [userRole, setUserRole] = useState<string>('');

  useEffect(() => {
    checkAccess();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      // Verify token and check permissions
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserRole(data.user.role);

        // Check if user has admin access
        if (!data.permissions.canAccessAdmin) {
          router.push('/dashboard');
          return;
        }

        // Load assignments and members
        await Promise.all([
          loadAssignments(),
          loadMembers(),
        ]);
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Access check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const loadAssignments = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/assignments', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAssignments(data.assignments);
      } else {
        throw new Error('Failed to load assignments');
      }
    } catch (error) {
      console.error('Error loading assignments:', error);
      setError('Error al cargar las asignaciones');
    }
  };

  const loadMembers = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/assignments/members', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMembers(data.members);
      } else {
        throw new Error('Failed to load members');
      }
    } catch (error) {
      console.error('Error loading members:', error);
      setError('Error al cargar los miembros');
    }
  };

  const handleCreateAssignment = async (data: {
    memberId: string;
    sectionType: ADMINISTRATIVE_SECTIONS;
    scopeDefinition: ScopeDefinition;
    reason?: string;
  }) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/assignments', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        await Promise.all([
          loadAssignments(),
          loadMembers(),
        ]);
        setShowForm(false);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create assignment');
      }
    } catch (error) {
      console.error('Error creating assignment:', error);
      setError(error instanceof Error ? error.message : 'Error al crear la asignación');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveAssignment = async (assignmentId: string) => {
    if (!confirm('¿Estás seguro de que quieres remover esta asignación?')) {
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/assignments', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          assignmentId,
          reason: 'Removido por el coordinador',
        }),
      });

      if (response.ok) {
        await Promise.all([
          loadAssignments(),
          loadMembers(),
        ]);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove assignment');
      }
    } catch (error) {
      console.error('Error removing assignment:', error);
      setError(error instanceof Error ? error.message : 'Error al remover la asignación');
    }
  };

  const handleTransferAssignment = () => {
    // TODO: Implement transfer functionality
    alert('Funcionalidad de transferencia próximamente');
  };

  const handleBackToDashboard = () => {
    router.push('/admin');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando asignaciones administrativas...</p>
        </div>
      </div>
    );
  }

  const canManage = userRole === 'overseer_coordinator';

  return (
    <div className="bg-gray-50 relative">
      {/* Header */}
      <header className="bg-yellow-600 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-white">
                Asignaciones Administrativas
              </h1>
              <p className="text-yellow-100">
                Delegación de responsabilidades administrativas
              </p>
            </div>
            <button
              onClick={handleBackToDashboard}
              className="bg-yellow-700 hover:bg-yellow-800 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Volver al Admin
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 pb-20">
        <div className="px-4 py-6 sm:px-0">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800">{error}</p>
              <button
                onClick={() => setError(null)}
                className="text-red-600 hover:text-red-800 text-sm mt-2"
              >
                Cerrar
              </button>
            </div>
          )}

          {/* Actions */}
          {canManage && (
            <div className="mb-6">
              <button
                onClick={() => setShowForm(!showForm)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
              >
                {showForm ? 'Cancelar' : 'Nueva Asignación'}
              </button>
            </div>
          )}

          {/* Assignment Form */}
          {showForm && canManage && (
            <div className="mb-6">
              <AssignmentForm
                members={members}
                onSubmit={handleCreateAssignment}
                onCancel={() => setShowForm(false)}
                isLoading={isSubmitting}
              />
            </div>
          )}

          {/* Assignments Grid */}
          <div className="space-y-6">
            <h2 className="text-xl font-bold text-gray-900">
              Asignaciones Actuales ({assignments.length})
            </h2>

            {assignments.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">
                  No hay asignaciones administrativas activas
                </p>
                {canManage && (
                  <p className="text-gray-400 mt-2">
                    Haz clic en &quot;Nueva Asignación&quot; para comenzar
                  </p>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {assignments.map(assignment => (
                  <AssignmentCard
                    key={assignment.id}
                    assignment={assignment}
                    onRemove={handleRemoveAssignment}
                    onTransfer={handleTransferAssignment}
                    canManage={canManage}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Admin Footer */}
      <AdminFooter currentSection="assignments" />
    </div>
  );
}
