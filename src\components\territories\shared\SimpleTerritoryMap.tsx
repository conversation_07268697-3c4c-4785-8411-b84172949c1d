'use client';

import React, { useEffect, useRef, useState } from 'react';
import type { Territory } from '@/types/territories/map';

// Helper function to calculate bounds for territories
function calculateTerritoryBounds(territories: Territory[]): [[number, number], [number, number]] | null {
  const territoriesWithBounds = territories.filter(t => t.boundary && t.boundary.coordinates);

  if (territoriesWithBounds.length === 0) {
    return null;
  }

  let minLng = Infinity;
  let maxLng = -Infinity;
  let minLat = Infinity;
  let maxLat = -Infinity;

  territoriesWithBounds.forEach(territory => {
    if (territory.boundary && territory.boundary.coordinates) {
      const coords = territory.boundary.coordinates[0]; // Get the outer ring
      coords.forEach(([lng, lat]) => {
        minLng = Math.min(minLng, lng);
        maxLng = Math.max(maxLng, lng);
        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
      });
    }
  });

  if (minLng === Infinity || maxLng === -Infinity || minLat === Infinity || maxLat === -Infinity) {
    return null;
  }

  return [[minLng, minLat], [maxLng, maxLat]];
}

// Global type declaration for MapLibre GL
declare global {
  interface Window {
    maplibregl: any;
  }
}

interface SimpleTerritoryMapProps {
  territories: Territory[];
  height?: string;
  width?: string;
  className?: string;
}

export default function SimpleTerritoryMap({
  territories,
  height = '400px',
  width = '100%',
  className = ''
}: SimpleTerritoryMapProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState('Inicializando mapa...');
  const [isInitialized, setIsInitialized] = useState(false);

  // Debug logging
  useEffect(() => {
    console.log('🗺️ SimpleTerritoryMap received props:', {
      territoriesCount: territories?.length || 0,
      territories: territories?.map(t => ({
        id: t.id,
        number: t.territoryNumber,
        hasBoundary: !!t.boundary,
        hasCoordinates: !!t.coordinates
      })),
      height,
      width,
      className
    });
  }, [territories, height, width, className]);

  useEffect(() => {
    let mounted = true;

    const initializeMap = async () => {
      if (!mapContainerRef.current || isInitialized) return;

      // Ensure container has proper dimensions
      const container = mapContainerRef.current;
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        console.warn('⚠️ Map container has zero dimensions, retrying...');
        setTimeout(() => {
          if (mounted) initializeMap();
        }, 100);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        setStatus('Cargando librerías del mapa...');

        // Load MapLibre CSS (using stable version)
        if (!document.querySelector('link[href*="maplibre-gl"]')) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = 'https://unpkg.com/maplibre-gl@3.6.2/dist/maplibre-gl.css';
          document.head.appendChild(link);

          await new Promise<void>((resolve) => {
            link.onload = () => resolve();
            link.onerror = () => resolve();
            setTimeout(() => resolve(), 2000);
          });
        }

        if (!mounted) return;

        // Load MapLibre GL from CDN for better compatibility
        setStatus('Inicializando mapa...');

        // Load MapLibre GL script if not already loaded
        if (!window.maplibregl) {
          await new Promise<void>((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/maplibre-gl@3.6.2/dist/maplibre-gl.js';
            script.onload = () => resolve();
            script.onerror = () => reject(new Error('Failed to load MapLibre GL'));
            document.head.appendChild(script);
          });
        }

        const maplibregl = window.maplibregl;

        if (!mounted) return;

        // Cleanup existing map
        if (mapRef.current) {
          mapRef.current.remove();
          mapRef.current = null;
        }

        console.log('🗺️ Creating map instance...');

        // Create map
        const map = new maplibregl.Map({
          container: mapContainerRef.current,
          style: {
            version: 8,
            sources: {
              'osm-tiles': {
                type: 'raster',
                tiles: [
                  'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                  'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
                  'https://b.tile.openstreetmap.org/{z}/{x}/{y}.png',
                  'https://c.tile.openstreetmap.org/{z}/{x}/{y}.png'
                ],
                tileSize: 256,
                attribution: '© OpenStreetMap contributors'
              }
            },
            layers: [
              {
                id: 'osm-tiles',
                type: 'raster',
                source: 'osm-tiles'
              }
            ]
          },
          center: territories.length > 0 && territories[0].coordinates
            ? [territories[0].coordinates.longitude, territories[0].coordinates.latitude]
            : [-80.2715, 25.7620], // Default to Miami
          zoom: 14, // Increased zoom for better boundary visibility
          attributionControl: true,
          crossSourceCollisions: false
        });

        mapRef.current = map;
        console.log('✅ Map instance created');

        // Wait for map to load
        map.on('load', async () => {
          console.log('🎯 Map loaded successfully');
          setStatus('Mapa cargado');
          setIsLoading(false);
          setIsInitialized(true);

          // Force a resize to ensure proper rendering
          setTimeout(() => {
            if (map && mounted) {
              map.resize();
              console.log('🔄 Map resized after load');
            }
          }, 100);

          // Add territories to map
          if (territories.length > 0) {
            console.log('🏠 Adding territories to map...');
            try {
              await addTerritoriesToMap(map, territories, maplibregl);

              // Fit map to territory boundaries if they exist
              if (territories.some(t => t.boundary)) {
                try {
                  const bounds = calculateTerritoryBounds(territories);
                  if (bounds) {
                    console.log('🎯 Fitting map to territory bounds:', bounds);
                    map.fitBounds(bounds, {
                      padding: 50,
                      maxZoom: 16
                    });
                  }
                } catch (boundsError) {
                  console.warn('⚠️ Could not calculate territory bounds:', boundsError);
                }
              }

              // Force another resize after adding territories
              setTimeout(() => {
                if (map && mounted) {
                  map.resize();
                  console.log('🔄 Map resized after adding territories');
                }
              }, 200);
            } catch (error) {
              console.error('❌ Failed to add territories:', error);
            }
          }
        });

        // Error handling
        map.on('error', (e) => {
          console.error('❌ Map error:', e);
          if (mounted) {
            setError(`Error del mapa: ${e.error?.message || 'Error desconocido'}`);
            setStatus('Error en el mapa');
          }
        });

      } catch (error) {
        console.error('❌ Map initialization error:', error);
        if (mounted) {
          setError(error instanceof Error ? error.message : 'Error al inicializar el mapa');
          setStatus('Error al cargar el mapa');
          setIsLoading(false);
        }
      }
    };

    initializeMap();

    return () => {
      mounted = false;
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
      setIsInitialized(false);
    };
  }, [territories]);

  // Function to add territories to map
  const addTerritoriesToMap = async (map: any, territories: Territory[], maplibregl: any) => {
    let territoriesWithBoundaries = 0;
    let territoriesWithoutBoundaries = 0;

    for (const territory of territories) {
      // Only add boundary if it exists (real boundary data)
      if (territory.boundary) {
        territoriesWithBoundaries++;
        try {
          const sourceId = `territory-boundary-${territory.id}`;
          const layerId = `territory-boundary-layer-${territory.id}`;

          console.log(`🗺️ Processing real boundary for territory ${territory.territoryNumber}:`, {
            boundaryType: territory.boundary.type,
            coordinatesLength: territory.boundary.coordinates?.[0]?.length,
            firstCoordinate: territory.boundary.coordinates?.[0]?.[0]
          });

          // Validate boundary data
          if (!territory.boundary.coordinates || !Array.isArray(territory.boundary.coordinates)) {
            throw new Error('Invalid boundary coordinates structure');
          }

          if (!territory.boundary.coordinates[0] || !Array.isArray(territory.boundary.coordinates[0])) {
            throw new Error('Invalid polygon coordinates');
          }

          // Get status colors
          const getStatusColor = (status: string) => {
            switch (status) {
              case 'available': return { color: '#10B981', fillColor: '#10B981' }; // Green
              case 'assigned': return { color: '#3B82F6', fillColor: '#3B82F6' }; // Blue
              case 'completed': return { color: '#F59E0B', fillColor: '#F59E0B' }; // Orange
              case 'out_of_service': return { color: '#EF4444', fillColor: '#EF4444' }; // Red
              default: return { color: '#6B7280', fillColor: '#6B7280' }; // Gray
            }
          };

          const colors = getStatusColor(territory.status);
          console.log(`🎨 Using colors for territory ${territory.territoryNumber}:`, colors);

          // Create GeoJSON source with validation
          const geoJsonData = {
            type: 'Feature',
            id: territory.id,
            properties: {
              territoryId: territory.id,
              territoryNumber: territory.territoryNumber,
              status: territory.status
            },
            geometry: {
              type: territory.boundary.type,
              coordinates: territory.boundary.coordinates
            }
          };

          console.log(`📍 GeoJSON data for territory ${territory.territoryNumber}:`, JSON.stringify(geoJsonData, null, 2));

          // Check if source already exists
          if (map.getSource(sourceId)) {
            console.log(`🔄 Updating existing source: ${sourceId}`);
            map.getSource(sourceId).setData(geoJsonData);
          } else {
            console.log(`➕ Adding new source: ${sourceId}`);
            // Add source to map
            map.addSource(sourceId, {
              type: 'geojson',
              data: geoJsonData
            });
          }

          // Check if layers already exist
          if (!map.getLayer(layerId)) {
            console.log(`➕ Adding fill layer: ${layerId}`);
            // Add fill layer
            map.addLayer({
              id: layerId,
              type: 'fill',
              source: sourceId,
              paint: {
                'fill-color': colors.fillColor,
                'fill-opacity': 0.4
              }
            });
          }

          if (!map.getLayer(`${layerId}-stroke`)) {
            console.log(`➕ Adding stroke layer: ${layerId}-stroke`);
            // Add stroke layer
            map.addLayer({
              id: `${layerId}-stroke`,
              type: 'line',
              source: sourceId,
              paint: {
                'line-color': colors.color,
                'line-width': 4,
                'line-opacity': 1.0
              }
            });
          }

          console.log(`✅ Successfully added boundary for territory ${territory.territoryNumber}`);

          // Add a visual confirmation that boundary was rendered
          console.log(`🎨 Boundary rendered with colors:`, {
            fillColor: colors.fillColor,
            strokeColor: colors.color,
            fillOpacity: 0.4,
            strokeWidth: 4
          });

        } catch (boundaryError) {
          console.error(`❌ Failed to add boundary for territory ${territory.territoryNumber}:`, boundaryError);
          console.error('Boundary data:', territory.boundary);
        }
      } else {
        territoriesWithoutBoundaries++;
        console.log(`⚠️ Territory ${territory.territoryNumber} has no boundary data - skipping boundary display`);
      }

      // Add marker if coordinates exist
      if (territory.coordinates) {
        try {
          // Get status color for marker
          const getMarkerColor = (status: string) => {
            switch (status) {
              case 'available': return '#10B981'; // Green
              case 'assigned': return '#3B82F6'; // Blue
              case 'completed': return '#F59E0B'; // Orange
              case 'out_of_service': return '#EF4444'; // Red
              default: return '#6B7280'; // Gray
            }
          };

          const markerColor = getMarkerColor(territory.status);

          // Create marker element
          const markerElement = document.createElement('div');
          markerElement.className = 'territory-marker';
          markerElement.style.cssText = `
            width: 40px;
            height: 40px;
            background-color: ${markerColor};
            border: 3px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            color: white;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          `;
          markerElement.textContent = territory.territoryNumber;

          // Create marker
          const marker = new maplibregl.Marker({ element: markerElement })
            .setLngLat([territory.coordinates.longitude, territory.coordinates.latitude])
            .addTo(map);

          // Add popup
          const popup = new maplibregl.default.Popup({ offset: 25 })
            .setHTML(`
              <div style="padding: 8px;">
                <h3 style="margin: 0 0 4px 0; font-size: 16px;">Territorio ${territory.territoryNumber}</h3>
                <p style="margin: 0; font-size: 14px; color: #666;">Estado: ${territory.status}</p>
                ${!territory.boundary ? '<p style="margin: 4px 0 0 0; font-size: 12px; color: #f59e0b;">⚠️ Sin datos de límites</p>' : ''}
              </div>
            `);

          markerElement.addEventListener('click', () => {
            popup.setLngLat([territory.coordinates!.longitude, territory.coordinates!.latitude])
                  .addTo(map);
          });

          console.log(`✅ Added marker for territory ${territory.territoryNumber}`);
        } catch (markerError) {
          console.warn(`⚠️ Failed to add marker for territory ${territory.territoryNumber}:`, markerError);
        }
      }
    }

    // Log summary
    console.log(`📊 Territory boundary summary: ${territoriesWithBoundaries} with boundaries, ${territoriesWithoutBoundaries} without boundaries`);
    if (territoriesWithoutBoundaries > 0) {
      console.log(`💡 ${territoriesWithoutBoundaries} territories need real boundary data to display territory outlines`);
    }

    // Fit bounds based on territories - THIS IS THE KEY ZOOM FEATURE!
    if (territories.length === 1) {
      // Single territory - zoom to its boundary or coordinates
      const territory = territories[0];

      if (territory.boundary && territory.boundary.coordinates) {
        // Calculate bounds from boundary coordinates
        const bounds = new maplibregl.LngLatBounds();

        const addCoordinatesToBounds = (coords: any) => {
          if (Array.isArray(coords[0])) {
            if (Array.isArray(coords[0][0])) {
              // Polygon coordinates
              coords[0].forEach((coord: [number, number]) => {
                bounds.extend(coord);
              });
            } else {
              // LineString coordinates
              coords.forEach((coord: [number, number]) => {
                bounds.extend(coord);
              });
            }
          }
        };

        addCoordinatesToBounds(territory.boundary.coordinates);
        map.fitBounds(bounds, { padding: 100 });
        console.log(`🎯 Map zoomed to territory ${territory.territoryNumber} boundary`);
      } else if (territory.coordinates) {
        // Fallback to center coordinates with fixed zoom
        map.flyTo({
          center: [territory.coordinates.longitude, territory.coordinates.latitude],
          zoom: 16,
          duration: 1000
        });
        console.log(`🎯 Map centered on territory ${territory.territoryNumber} coordinates`);
      }
    } else if (territories.length > 1) {
      // Multiple territories - fit all coordinates
      const coordinates = territories
        .filter(t => t.coordinates)
        .map(t => [t.coordinates!.longitude, t.coordinates!.latitude]);

      if (coordinates.length > 0) {
        const bounds = new maplibregl.LngLatBounds();
        coordinates.forEach(coord => bounds.extend(coord as [number, number]));
        map.fitBounds(bounds, { padding: 50 });
        console.log('🎯 Map bounds fitted to all territories');
      }
    }
  };

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`} style={{ height, width }}>
        <div className="text-red-800 font-medium">Error del Mapa</div>
        <div className="text-red-600 text-sm mt-1">{error}</div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} style={{ height, width }}>
      <div ref={mapContainerRef} className="w-full h-full rounded-lg overflow-hidden" />

      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <div className="text-sm text-gray-600">{status}</div>
          </div>
        </div>
      )}

      {!isLoading && territories.length > 0 && (
        <div className="absolute bottom-2 right-2 z-10 bg-white rounded-lg shadow-md px-3 py-1">
          <span className="text-xs font-medium">{territories.length} territorios</span>
        </div>
      )}
    </div>
  );
}
