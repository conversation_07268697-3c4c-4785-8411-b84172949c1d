const { PrismaClient } = require('@prisma/client');

async function verifyTerritory014() {
  const prisma = new PrismaClient();
  
  try {
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '014'
      }
    });
    
    if (!territory) {
      console.log('❌ Territory 014 not found');
      return;
    }
    
    console.log(`✅ Territory 014 found`);
    console.log(`📊 Total addresses: ${territory.address.split('\n').length}`);
    console.log(`📝 Notes: ${territory.notes ? territory.notes.split('\n').length : 0}`);
    
    console.log(`\n🏢 Sample addresses (first 15):`);
    const addresses = territory.address.split('\n');
    addresses.slice(0, 15).forEach((addr, idx) => {
      console.log(`  ${idx + 1}. ${addr}`);
    });
    
    // Count buildings
    const buildings = new Set();
    addresses.forEach(addr => {
      const match = addr.match(/(\d{4}) FLAGLER ST/);
      if (match) {
        buildings.add(match[1]);
      }
    });
    
    console.log(`\n🏢 Buildings found: ${buildings.size}`);
    console.log(`Buildings: ${Array.from(buildings).sort().join(', ')}`);
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

verifyTerritory014().catch(console.error);
