const { PrismaClient } = require('@prisma/client');

async function checkTerritorySchema() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking territory table schema...\n');
    
    // Get table structure
    const columns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'territories' 
      ORDER BY ordinal_position;
    `;
    
    console.log('📋 Current territories table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    console.log('\n🎯 Expected columns for our app:');
    console.log('  - territory_number: varchar(50)');
    console.log('  - address: text');
    console.log('  - notes: text (nullable)');
    
    // Check if expected columns exist
    const expectedColumns = ['territory_number', 'address', 'notes'];
    const existingColumns = columns.map(col => col.column_name);
    
    console.log('\n✅ Missing columns:');
    expectedColumns.forEach(col => {
      if (!existingColumns.includes(col)) {
        console.log(`  ❌ ${col} - MISSING`);
      } else {
        console.log(`  ✅ ${col} - EXISTS`);
      }
    });
    
    // Get sample data
    console.log('\n📊 Sample territory data:');
    const sampleData = await prisma.$queryRaw`SELECT * FROM territories LIMIT 3`;
    console.log(JSON.stringify(sampleData, null, 2));
    
  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkTerritorySchema();
