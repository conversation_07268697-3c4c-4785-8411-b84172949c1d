#!/usr/bin/env node

/**
 * Permission Delegation System Test
 *
 * Tests the administrative delegation system implementation for story 2.1
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testPermissionDelegationSystem() {
  console.log('🧪 TESTING PERMISSION DELEGATION SYSTEM');
  console.log('='.repeat(50));

  try {
    // Test 1: Verify database schema
    console.log('\n📋 TEST 1: Verify Database Schema');
    await testDatabaseSchema();

    // Test 2: Test permission assignment
    console.log('\n🔐 TEST 2: Test Permission Assignment');
    await testPermissionAssignment();

    // Test 3: Test permission revocation
    console.log('\n❌ TEST 3: Test Permission Revocation');
    await testPermissionRevocation();

    // Test 4: Test audit logging
    console.log('\n📝 TEST 4: Test Audit Logging');
    await testAuditLogging();

    // Test 5: Test permission expiration
    console.log('\n⏰ TEST 5: Test Permission Expiration');
    await testPermissionExpiration();

    console.log('\n✅ ALL TESTS COMPLETED SUCCESSFULLY');

  } catch (error) {
    console.error('\n❌ TEST FAILED:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

async function testDatabaseSchema() {
  // Test ElderPermission table structure
  const elderPermissionCount = await prisma.elderPermission.count();
  console.log(`   ElderPermission table accessible: ${elderPermissionCount >= 0 ? '✅' : '❌'}`);

  // Test PermissionAuditLog table structure
  const auditLogCount = await prisma.permissionAuditLog.count();
  console.log(`   PermissionAuditLog table accessible: ${auditLogCount >= 0 ? '✅' : '❌'}`);

  // Test that we can query with new fields
  const testPermission = await prisma.elderPermission.findFirst({
    select: {
      id: true,
      sectionId: true,
      permissions: true,
      assignedBy: true,
      assignedAt: true,
      expirationDate: true,
      isActive: true,
      notes: true,
    },
  });
  console.log(`   New schema fields accessible: ✅`);
}

async function testPermissionAssignment() {
  // Get test congregation and members
  const congregation = await prisma.congregation.findFirst({
    where: { id: '1441' },
  });

  if (!congregation) {
    throw new Error('Test congregation not found');
  }

  const coordinator = await prisma.member.findFirst({
    where: {
      congregationId: '1441',
      role: 'coordinator',
    },
  });

  const elder = await prisma.member.findFirst({
    where: {
      congregationId: '1441',
      role: 'elder',
    },
  });

  if (!coordinator || !elder) {
    console.log('   ⚠️ Skipping test - no coordinator or elder found');
    return;
  }

  // Test permission assignment
  const testPermission = {
    congregationId: '1441',
    memberId: elder.id,
    sectionId: 'meetings',
    permissions: ['view', 'edit', 'assign'],
    assignedBy: coordinator.id,
    assignedAt: new Date(),
    isActive: true,
    notes: 'Test permission assignment',
  };

  const createdPermission = await prisma.elderPermission.create({
    data: testPermission,
  });

  console.log(`   Permission assigned successfully: ✅`);
  console.log(`   Permission ID: ${createdPermission.id}`);

  // Clean up
  await prisma.elderPermission.delete({
    where: { id: createdPermission.id },
  });

  console.log(`   Test permission cleaned up: ✅`);
}

async function testPermissionRevocation() {
  // Get test members
  const coordinator = await prisma.member.findFirst({
    where: {
      congregationId: '1441',
      role: 'coordinator',
    },
  });

  const elder = await prisma.member.findFirst({
    where: {
      congregationId: '1441',
      role: 'elder',
    },
  });

  if (!coordinator || !elder) {
    console.log('   ⚠️ Skipping test - no coordinator or elder found');
    return;
  }

  // Create test permission
  const testPermission = await prisma.elderPermission.create({
    data: {
      congregationId: '1441',
      memberId: elder.id,
      sectionId: 'tasks',
      permissions: ['view', 'edit', 'create'],
      assignedBy: coordinator.id,
      assignedAt: new Date(),
      isActive: true,
      notes: 'Test permission for revocation',
    },
  });

  console.log(`   Test permission created: ✅`);

  // Test revocation by deactivating
  await prisma.elderPermission.update({
    where: { id: testPermission.id },
    data: { isActive: false },
  });

  const revokedPermission = await prisma.elderPermission.findUnique({
    where: { id: testPermission.id },
  });

  console.log(`   Permission revoked (isActive: ${revokedPermission?.isActive}): ${!revokedPermission?.isActive ? '✅' : '❌'}`);

  // Clean up
  await prisma.elderPermission.delete({
    where: { id: testPermission.id },
  });

  console.log(`   Test permission cleaned up: ✅`);
}

async function testAuditLogging() {
  // Get test members
  const coordinator = await prisma.member.findFirst({
    where: {
      congregationId: '1441',
      role: 'coordinator',
    },
  });

  const elder = await prisma.member.findFirst({
    where: {
      congregationId: '1441',
      role: 'elder',
    },
  });

  if (!coordinator || !elder) {
    console.log('   ⚠️ Skipping test - no coordinator or elder found');
    return;
  }

  // Create test audit log entry
  const testAuditLog = await prisma.permissionAuditLog.create({
    data: {
      congregationId: '1441',
      userId: elder.id,
      action: 'assign',
      sectionId: 'letters',
      permissions: ['view', 'upload'],
      performedBy: coordinator.id,
      reason: 'Test audit log entry',
      ipAddress: '127.0.0.1',
      userAgent: 'Test Agent',
      timestamp: new Date(),
    },
  });

  console.log(`   Audit log entry created: ✅`);
  console.log(`   Audit log ID: ${testAuditLog.id}`);

  // Test querying audit logs
  const auditLogs = await prisma.permissionAuditLog.findMany({
    where: {
      congregationId: '1441',
      userId: elder.id,
    },
    include: {
      user: { select: { name: true } },
      performedByMember: { select: { name: true } },
    },
    orderBy: { timestamp: 'desc' },
    take: 5,
  });

  console.log(`   Audit logs queried successfully: ✅`);
  console.log(`   Found ${auditLogs.length} audit log entries`);

  // Clean up
  await prisma.permissionAuditLog.delete({
    where: { id: testAuditLog.id },
  });

  console.log(`   Test audit log cleaned up: ✅`);
}

async function testPermissionExpiration() {
  // Get test members
  const coordinator = await prisma.member.findFirst({
    where: {
      congregationId: '1441',
      role: 'coordinator',
    },
  });

  const elder = await prisma.member.findFirst({
    where: {
      congregationId: '1441',
      role: 'elder',
    },
  });

  if (!coordinator || !elder) {
    console.log('   ⚠️ Skipping test - no coordinator or elder found');
    return;
  }

  // Create permission with past expiration date
  const pastDate = new Date();
  pastDate.setDate(pastDate.getDate() - 1); // Yesterday

  const expiredPermission = await prisma.elderPermission.create({
    data: {
      congregationId: '1441',
      memberId: elder.id,
      sectionId: 'events',
      permissions: ['view', 'create'],
      assignedBy: coordinator.id,
      assignedAt: new Date(),
      expirationDate: pastDate,
      isActive: true,
      notes: 'Test expired permission',
    },
  });

  console.log(`   Expired permission created: ✅`);

  // Test finding expired permissions
  const expiredPermissions = await prisma.elderPermission.findMany({
    where: {
      congregationId: '1441',
      isActive: true,
      expirationDate: {
        lte: new Date(),
      },
    },
  });

  console.log(`   Found ${expiredPermissions.length} expired permissions: ✅`);

  // Test expiration logic
  if (expiredPermissions.length > 0) {
    await prisma.elderPermission.updateMany({
      where: {
        id: {
          in: expiredPermissions.map(p => p.id),
        },
      },
      data: {
        isActive: false,
      },
    });

    console.log(`   Expired permissions deactivated: ✅`);
  }

  // Clean up
  await prisma.elderPermission.delete({
    where: { id: expiredPermission.id },
  });

  console.log(`   Test expired permission cleaned up: ✅`);
}

// Run the test
testPermissionDelegationSystem();
