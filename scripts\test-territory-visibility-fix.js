#!/usr/bin/env node

/**
 * Test Territory Visibility Fix
 * 
 * This script tests the fixed API endpoint to ensure territory numbers
 * are properly returned in the expected format.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Test the fixed API endpoint
 */
async function testFixedAPI() {
  try {
    console.log('🧪 Testing Fixed Territory Visibility API');
    console.log('=========================================\n');

    // Test the AssignmentService directly
    const { AssignmentService } = require('../src/services/territories/AssignmentService.ts');
    
    console.log('📋 Testing AssignmentService.getMembersWithAssignments:');
    const membersWithAssignments = await AssignmentService.getMembersWithAssignments('1441');
    
    console.log(`   Members returned: ${membersWithAssignments.length}`);
    
    if (membersWithAssignments.length > 0) {
      console.log('\n🔍 Data Structure Analysis:');
      membersWithAssignments.forEach((member, index) => {
        if (member.assignments && member.assignments.length > 0) {
          console.log(`\n   Member ${index + 1}: ${member.name}`);
          console.log(`   Role: ${member.role}`);
          console.log(`   Active assignments: ${member.activeAssignments}`);
          console.log(`   Assignments array length: ${member.assignments.length}`);
          
          console.log('   Territory details:');
          member.assignments.forEach((territory, tIndex) => {
            console.log(`     ${tIndex + 1}. ID: ${territory.id}`);
            console.log(`        Territory Number: "${territory.territoryNumber}" (type: ${typeof territory.territoryNumber})`);
            console.log(`        Address: "${territory.address?.substring(0, 50)}..."`);
            console.log(`        Assigned At: ${territory.assignedAt}`);
            console.log(`        Days Assigned: ${territory.daysAssigned}`);
          });
        }
      });

      // Check for potential issues
      console.log('\n✅ Territory Number Visibility Check:');
      let allNumbersVisible = true;
      let totalTerritories = 0;

      membersWithAssignments.forEach(member => {
        if (member.assignments) {
          member.assignments.forEach(territory => {
            totalTerritories++;
            if (!territory.territoryNumber || territory.territoryNumber === '' || territory.territoryNumber === undefined || territory.territoryNumber === null) {
              allNumbersVisible = false;
              console.log(`   ❌ Missing territory number for assignment ID: ${territory.id}`);
            }
          });
        }
      });

      console.log(`   Total territories checked: ${totalTerritories}`);
      console.log(`   All territory numbers visible: ${allNumbersVisible ? '✅ YES' : '❌ NO'}`);

      if (allNumbersVisible && totalTerritories > 0) {
        console.log('\n🎉 Territory visibility fix successful!');
        console.log('✅ All territory numbers are properly accessible');
        console.log('✅ Data structure matches component expectations');
        console.log('✅ Territory badges should now display numbers correctly');
      }
    }

    return true;

  } catch (error) {
    console.error('❌ Error testing fixed API:', error);
    return false;
  }
}

/**
 * Test the API endpoint via HTTP
 */
async function testAPIEndpoint() {
  try {
    console.log('\n🧪 Testing API Endpoint via HTTP');
    console.log('=================================\n');

    // Get authentication token
    const loginResponse = await fetch('http://localhost:3001/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: 'coral2024'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Failed to get authentication token');
      console.log('Response status:', loginResponse.status);
      return false;
    }

    const { token } = await loginResponse.json();
    console.log('✅ Authentication token obtained');

    // Test the assignments endpoint
    console.log('\n📋 Testing /api/territories/assignments?type=members:');
    const assignmentsResponse = await fetch('http://localhost:3001/api/territories/assignments?type=members', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!assignmentsResponse.ok) {
      console.log('❌ Assignments API failed');
      console.log('Response status:', assignmentsResponse.status);
      return false;
    }

    const assignmentsData = await assignmentsResponse.json();
    console.log('✅ Assignments API working');
    
    if (assignmentsData.success && assignmentsData.data && assignmentsData.data.membersWithAssignments) {
      const members = assignmentsData.data.membersWithAssignments;
      console.log(`   Members with assignments: ${members.length}`);

      if (members.length > 0) {
        console.log('\n🔍 API Response Data Structure:');
        members.forEach((member, index) => {
          if (member.assignments && member.assignments.length > 0) {
            console.log(`\n   Member ${index + 1}: ${member.name}`);
            console.log(`   Assignments: ${member.assignments.length}`);
            
            member.assignments.slice(0, 3).forEach((territory, tIndex) => {
              console.log(`     ${tIndex + 1}. Territory Number: "${territory.territoryNumber}"`);
              console.log(`        Address: "${territory.address?.substring(0, 40)}..."`);
              console.log(`        Days Assigned: ${territory.daysAssigned}`);
            });
          }
        });

        console.log('\n✅ API endpoint returning correct data structure');
        console.log('✅ Territory numbers are accessible in the response');
      }
    }

    return true;

  } catch (error) {
    console.error('❌ Error testing API endpoint:', error);
    return false;
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🧪 Territory Visibility Fix Test');
  console.log('================================\n');

  try {
    const tests = [
      { name: 'Fixed AssignmentService', test: testFixedAPI },
      { name: 'API Endpoint via HTTP', test: testAPIEndpoint }
    ];

    let passed = 0;
    let total = tests.length;

    for (const { name, test } of tests) {
      try {
        const result = await test();
        if (result) {
          passed++;
          console.log(`\n✅ ${name} test: PASSED`);
        } else {
          console.log(`\n❌ ${name} test: FAILED`);
        }
      } catch (error) {
        console.log(`\n❌ ${name} test: ERROR - ${error.message}`);
      }
    }

    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`Passed: ${passed}/${total}`);
    console.log(`Status: ${passed === total ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    if (passed === total) {
      console.log('\n🎉 Territory visibility fix verified!');
      console.log('✅ AssignmentService returns correct data format');
      console.log('✅ API endpoint provides territory numbers');
      console.log('✅ Component should now display territory numbers correctly');
      
      console.log('\n📱 Next Steps:');
      console.log('1. Refresh the browser to see the territory numbers');
      console.log('2. Navigate to Territorios > Asignados tab');
      console.log('3. Territory badges should now show numbers clearly');
    }

  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testFixedAPI,
  testAPIEndpoint
};
