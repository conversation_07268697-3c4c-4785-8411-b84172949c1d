const XLSX = require('xlsx');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Improved Parser for Territory 014 - Match format of 007/008
function parseTerritory014LikeBuildings(data) {
  const addresses = [];
  const notes = [];
  const buildings = new Map(); // Map building number to apartments
  
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    if (!row || row.length === 0) continue;
    
    // Look for building numbers (4-digit numbers that are building addresses)
    for (let j = 0; j < row.length; j++) {
      const cell = row[j];
      if (!cell) continue;
      
      const cellStr = cell.toString().trim();
      
      // Detect building numbers (4-digit numbers)
      if (/^\d{4}$/.test(cellStr)) {
        const buildingNum = cellStr;
        
        // Check if this is a building address by looking for apartment numbers in subsequent rows
        let isBuilding = false;
        let apartmentNumbers = [];
        
        // Look ahead for apartment numbers
        for (let k = i + 1; k < Math.min(i + 20, data.length); k++) {
          const nextRow = data[k];
          if (nextRow && nextRow[j]) {
            const nextCell = nextRow[j].toString().trim();
            if (/^\d{1,3}$/.test(nextCell) && nextCell.length <= 3) {
              apartmentNumbers.push(nextCell);
              isBuilding = true;
            } else if (/^\d{4}$/.test(nextCell)) {
              // Found another building, stop here
              break;
            }
          }
        }
        
        if (isBuilding && apartmentNumbers.length > 0) {
          buildings.set(buildingNum, apartmentNumbers);
          console.log(`Found building ${buildingNum} with ${apartmentNumbers.length} apartments`);
        }
      }
    }
  }
  
  // Convert buildings to the format like territories 007/008
  for (const [buildingNum, apartments] of buildings) {
    // Add building address first
    addresses.push(`${buildingNum} FLAGLER ST, Miami, FL 33144`);
    
    // Add apartment numbers (just "Apt X" format)
    apartments.forEach(aptNum => {
      addresses.push(`Apt ${aptNum}`);
    });
  }
  
  // Parse notes separately
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    if (!row || row.length === 0) continue;
    
    // Look for observations in column 7 (index 6)
    if (row[6] && row[6].toString().trim()) {
      const obs = row[6].toString().trim();
      if (obs !== 'Observaciones' && obs !== 'Edificio' && obs.length > 2 && 
          !obs.includes('H-') && !obs.includes('T-') && !obs.includes('Hoja')) {
        
        // Try to associate with apartment if possible
        let aptContext = '';
        if (row[1] && /^\d{1,3}$/.test(row[1].toString().trim())) {
          aptContext = `Apt ${row[1].toString().trim()}`;
        }
        
        if (aptContext) {
          notes.push(`${aptContext}: ${obs}`);
        } else {
          notes.push(obs);
        }
      }
    }
  }
  
  return { addresses, notes };
}

async function reformatTerritory014() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  
  try {
    console.log(`🔧 Reformatting Territory 014 to match 007/008 style...`);
    
    // Delete existing Territory 014
    await prisma.territory.deleteMany({
      where: {
        congregationId: '1441',
        territoryNumber: '014'
      }
    });
    console.log(`🗑️  Deleted existing Territory 014`);
    
    // Read and parse the Excel file
    const fileName = 'Terr. 014.xlsx';
    const filePath = path.join(territoriosDir, fileName);
    
    const workbook = XLSX.readFile(filePath);
    const sheet = workbook.Sheets['Terr 14'];
    const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    
    // Parse with building-first format
    const result = parseTerritory014LikeBuildings(data);
    
    if (result.addresses.length === 0) {
      console.log(`❌ No addresses found, aborting...`);
      return;
    }
    
    // Create new Territory 014 with proper format
    const territory = await prisma.territory.create({
      data: {
        congregationId: '1441',
        territoryNumber: '014',
        address: result.addresses.join('\n'),
        notes: result.notes.length > 0 ? result.notes.join('\n') : null,
        status: 'available',
        displayOrder: 14
      }
    });
    
    console.log(`✅ Recreated Territory 014 with ${result.addresses.length} addresses (007/008 style)`);
    if (result.notes.length > 0) {
      console.log(`📝 ${result.notes.length} notes imported`);
    }
    
    // Show sample addresses
    console.log(`\n📋 Sample addresses (first 20):`);
    result.addresses.slice(0, 20).forEach((addr, idx) => {
      console.log(`  ${idx + 1}. ${addr}`);
    });
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  } finally {
    await prisma.$disconnect();
  }
}

reformatTerritory014().catch(console.error);
