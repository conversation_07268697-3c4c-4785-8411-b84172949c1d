/**
 * Replace "Candado" with "Perros/Rejas" in Territory Notes
 *
 * Updates all territories to replace "Candado" notes with "Perros/Rejas"
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Configuration
const CONGREGATION_ID = '1441'; // Coral Oeste

async function replaceCandadoWithPerrosRejas() {
  try {
    console.log('🔄 Replacing "Candado" with "Perros/Rejas" in all territories...');

    // Get all territories for the congregation
    const territories = await prisma.territory.findMany({
      where: {
        congregationId: CONGREGATION_ID
      }
    });

    console.log(`📊 Found ${territories.length} territories to check`);

    let updatedCount = 0;

    for (const territory of territories) {
      let needsUpdate = false;
      let updatedNotes = territory.notes;

      // Check and update notes field
      if (territory.notes) {
        const originalNotes = territory.notes;
        updatedNotes = originalNotes
          .replace(/\bCandado\b/gi, 'Perros/Rejas')
          .replace(/\bCANDADO\b/gi, 'Perros/Rejas')
          .replace(/\bcandado\b/gi, 'Perros/Rejas')
          .replace(/Candado -/gi, 'Perros/Rejas -')
          .replace(/CANDADO/gi, 'Perros/Rejas');

        if (updatedNotes !== originalNotes) {
          needsUpdate = true;
          console.log(`📝 Territory ${territory.territoryNumber}: Updated notes`);
        }
      }

      // Check and update address field (in case notes are embedded in addresses)
      let updatedAddress = territory.address;
      if (territory.address) {
        const originalAddress = territory.address;
        updatedAddress = originalAddress
          .replace(/\(Candado\)/gi, '(Perros/Rejas)')
          .replace(/\(CANDADO\)/gi, '(Perros/Rejas)')
          .replace(/\(candado\)/gi, '(Perros/Rejas)')
          .replace(/Candado/gi, 'Perros/Rejas')
          .replace(/CANDADO/gi, 'Perros/Rejas')
          .replace(/candado/gi, 'Perros/Rejas');

        if (updatedAddress !== originalAddress) {
          needsUpdate = true;
          console.log(`🏠 Territory ${territory.territoryNumber}: Updated addresses`);
        }
      }

      // Update the territory if changes were made
      if (needsUpdate) {
        await prisma.territory.update({
          where: { id: territory.id },
          data: {
            notes: updatedNotes,
            address: updatedAddress
          }
        });

        updatedCount++;
        console.log(`✅ Updated Territory ${territory.territoryNumber}`);
      }
    }

    console.log(`\n🎉 Replacement completed!`);
    console.log(`✅ Updated ${updatedCount} territories`);
    console.log(`📊 Checked ${territories.length} total territories`);

    // Show summary of changes
    if (updatedCount > 0) {
      console.log('\n📋 Summary of changes:');
      console.log('   - "Candado" → "Perros/Rejas"');
      console.log('   - "CANDADO" → "Perros/Rejas"');
      console.log('   - "candado" → "Perros/Rejas"');
      console.log('   - "(Candado)" → "(Perros/Rejas)"');
    }

  } catch (error) {
    console.error('❌ Error replacing Candado with Perros/Rejas:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  replaceCandadoWithPerrosRejas();
}

module.exports = { replaceCandadoWithPerrosRejas };
