/**
 * Account Lockout Management API Endpoint
 *
 * Handles account lockout and unlock operations for enhanced security.
 * Only accessible to elders, coordinators, and developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PinService } from '@/lib/services/pinService';

// Validation schema for account unlock
const UnlockAccountSchema = z.object({
  memberId: z.string().min(1, 'Member ID is required'),
  reason: z.string().optional(),
});

/**
 * POST - Unlock a member account
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to unlock accounts' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = UnlockAccountSchema.parse(body);

    // Extract client information for audit trail
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Check if account is actually locked
    const isLocked = await PinService.isAccountLocked(user.congregationId, validatedData.memberId);
    if (!isLocked) {
      return NextResponse.json(
        { error: 'Account is not currently locked' },
        { status: 400 }
      );
    }

    // Unlock the account
    await PinService.unlockAccount(
      user.congregationId,
      validatedData.memberId,
      user.userId,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: true,
      message: 'Account unlocked successfully',
      unlockedBy: user.userId,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Account unlock error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid unlock data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to unlock account';
    
    return NextResponse.json(
      { 
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET - Get account lockout status
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view lockout status' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('memberId');

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    // Check lockout status
    const isLocked = await PinService.isAccountLocked(user.congregationId, memberId);

    // Get lockout history
    const lockoutEvents = await PinService.getSecurityAuditEvents(
      user.congregationId,
      memberId,
      'lockout',
      10
    );

    return NextResponse.json({
      success: true,
      isLocked,
      lockoutHistory: lockoutEvents,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Lockout status error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get lockout status',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
