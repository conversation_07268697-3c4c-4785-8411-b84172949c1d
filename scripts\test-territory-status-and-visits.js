#!/usr/bin/env node

/**
 * Test Territory Status and Visit Functionality
 * 
 * This script tests the new territory status management and visit logging
 * functionality to ensure partial completion tracking works correctly.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Test territory status enum values
 */
async function testTerritoryStatusEnum() {
  try {
    console.log('🧪 Testing Territory Status Enum');
    console.log('=================================\n');

    // Test that we can create territories with all status values
    const statusValues = ['available', 'assigned', 'completed', 'unavailable'];
    
    console.log('📊 Testing status enum values:');
    for (const status of statusValues) {
      try {
        // Try to find a territory with this status or create a test one
        const existingTerritory = await prisma.territory.findFirst({
          where: { 
            congregationId: '1441',
            status: status
          }
        });

        if (existingTerritory) {
          console.log(`   ✅ ${status}: Found existing territory ${existingTerritory.territoryNumber}`);
        } else {
          console.log(`   ✅ ${status}: Enum value is valid (no existing territories)`);
        }
      } catch (error) {
        console.log(`   ❌ ${status}: Error - ${error.message}`);
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Error testing territory status enum:', error);
    return false;
  }
}

/**
 * Test partial completion fields
 */
async function testPartialCompletionFields() {
  try {
    console.log('\n🧪 Testing Partial Completion Fields');
    console.log('====================================\n');

    // Get an assignment to test with
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        congregationId: '1441'
      },
      include: {
        territory: {
          select: {
            territoryNumber: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      }
    });

    if (!assignment) {
      console.log('⚠️  No assignments found to test with');
      return true;
    }

    console.log(`📋 Testing with assignment: ${assignment.member.name} → Territory ${assignment.territory.territoryNumber}`);

    // Test that the new fields exist and have default values
    console.log('\n📊 Assignment partial completion fields:');
    console.log(`   Visit Count: ${assignment.visitCount}`);
    console.log(`   Is Partially Completed: ${assignment.isPartiallyCompleted}`);
    console.log(`   Partial Completion Notes: ${assignment.partialCompletionNotes || 'None'}`);

    // Test updating partial completion fields
    const updatedAssignment = await prisma.territoryAssignment.update({
      where: { id: assignment.id },
      data: {
        visitCount: 2,
        isPartiallyCompleted: true,
        partialCompletionNotes: 'Test partial completion - worked streets 1-10'
      }
    });

    console.log('\n✅ Successfully updated partial completion fields:');
    console.log(`   Visit Count: ${updatedAssignment.visitCount}`);
    console.log(`   Is Partially Completed: ${updatedAssignment.isPartiallyCompleted}`);
    console.log(`   Notes: ${updatedAssignment.partialCompletionNotes}`);

    // Reset the test data
    await prisma.territoryAssignment.update({
      where: { id: assignment.id },
      data: {
        visitCount: assignment.visitCount,
        isPartiallyCompleted: assignment.isPartiallyCompleted,
        partialCompletionNotes: assignment.partialCompletionNotes
      }
    });

    console.log('✅ Reset test data to original values');

    return true;
  } catch (error) {
    console.error('❌ Error testing partial completion fields:', error);
    return false;
  }
}

/**
 * Test TerritoryVisit model
 */
async function testTerritoryVisitModel() {
  try {
    console.log('\n🧪 Testing TerritoryVisit Model');
    console.log('===============================\n');

    // Get an assignment to test with
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        congregationId: '1441'
      },
      include: {
        territory: {
          select: {
            territoryNumber: true
          }
        }
      }
    });

    if (!assignment) {
      console.log('⚠️  No assignments found to test with');
      return true;
    }

    console.log(`📋 Testing visit logging for Territory ${assignment.territory.territoryNumber}`);

    // Create a test visit
    const testVisit = await prisma.territoryVisit.create({
      data: {
        assignmentId: assignment.id,
        visitDate: new Date(),
        isCompleted: false,
        notes: 'Test visit - worked morning hours',
        addressesWorked: 'Streets 1-5, Avenue A 100-150',
        congregationId: '1441'
      }
    });

    console.log('✅ Successfully created test visit:');
    console.log(`   Visit ID: ${testVisit.id}`);
    console.log(`   Visit Date: ${testVisit.visitDate.toLocaleDateString()}`);
    console.log(`   Is Completed: ${testVisit.isCompleted}`);
    console.log(`   Addresses Worked: ${testVisit.addressesWorked}`);
    console.log(`   Notes: ${testVisit.notes}`);

    // Test retrieving visits for the assignment
    const visits = await prisma.territoryVisit.findMany({
      where: {
        assignmentId: assignment.id,
        congregationId: '1441'
      },
      orderBy: {
        visitDate: 'desc'
      }
    });

    console.log(`\n📊 Total visits for this assignment: ${visits.length}`);

    // Clean up test data
    await prisma.territoryVisit.delete({
      where: { id: testVisit.id }
    });

    console.log('✅ Cleaned up test visit data');

    return true;
  } catch (error) {
    console.error('❌ Error testing TerritoryVisit model:', error);
    return false;
  }
}

/**
 * Test multiple visits workflow
 */
async function testMultipleVisitsWorkflow() {
  try {
    console.log('\n🧪 Testing Multiple Visits Workflow');
    console.log('===================================\n');

    // Get an assignment
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        congregationId: '1441',
        status: 'active'
      },
      include: {
        territory: {
          select: {
            territoryNumber: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      }
    });

    if (!assignment) {
      console.log('⚠️  No active assignments found to test with');
      return true;
    }

    console.log(`📋 Testing multiple visits workflow:`);
    console.log(`   Assignment: ${assignment.member.name} → Territory ${assignment.territory.territoryNumber}`);

    // Simulate multiple visits
    const visits = [
      {
        visitDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
        isCompleted: false,
        notes: 'First visit - worked morning, will continue next week',
        addressesWorked: 'Streets 1-10'
      },
      {
        visitDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        isCompleted: false,
        notes: 'Second visit - continued from street 11',
        addressesWorked: 'Streets 11-20'
      },
      {
        visitDate: new Date(), // Today
        isCompleted: true,
        notes: 'Final visit - completed remaining addresses',
        addressesWorked: 'Streets 21-25, completed territory'
      }
    ];

    const createdVisits = [];
    for (let i = 0; i < visits.length; i++) {
      const visit = await prisma.territoryVisit.create({
        data: {
          assignmentId: assignment.id,
          ...visits[i],
          congregationId: '1441'
        }
      });
      createdVisits.push(visit);
      
      console.log(`   ✅ Visit ${i + 1}: ${visit.isCompleted ? 'Completed' : 'Partial'} - ${visit.addressesWorked}`);
    }

    // Update assignment visit count
    await prisma.territoryAssignment.update({
      where: { id: assignment.id },
      data: {
        visitCount: visits.length,
        isPartiallyCompleted: visits.some(v => !v.isCompleted)
      }
    });

    console.log(`\n📊 Assignment updated:`);
    console.log(`   Total visits: ${visits.length}`);
    console.log(`   Had partial completions: ${visits.some(v => !v.isCompleted)}`);
    console.log(`   Final status: ${visits[visits.length - 1].isCompleted ? 'Completed' : 'In Progress'}`);

    // Clean up test data
    for (const visit of createdVisits) {
      await prisma.territoryVisit.delete({
        where: { id: visit.id }
      });
    }

    // Reset assignment
    await prisma.territoryAssignment.update({
      where: { id: assignment.id },
      data: {
        visitCount: 0,
        isPartiallyCompleted: false
      }
    });

    console.log('✅ Cleaned up test data');

    return true;
  } catch (error) {
    console.error('❌ Error testing multiple visits workflow:', error);
    return false;
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🧪 Territory Status and Visit Functionality Test');
  console.log('================================================\n');

  try {
    const tests = [
      { name: 'Territory Status Enum', test: testTerritoryStatusEnum },
      { name: 'Partial Completion Fields', test: testPartialCompletionFields },
      { name: 'TerritoryVisit Model', test: testTerritoryVisitModel },
      { name: 'Multiple Visits Workflow', test: testMultipleVisitsWorkflow }
    ];

    let passed = 0;
    let total = tests.length;

    for (const { name, test } of tests) {
      try {
        const result = await test();
        if (result) {
          passed++;
          console.log(`\n✅ ${name} test: PASSED`);
        } else {
          console.log(`\n❌ ${name} test: FAILED`);
        }
      } catch (error) {
        console.log(`\n❌ ${name} test: ERROR - ${error.message}`);
      }
    }

    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`Passed: ${passed}/${total}`);
    console.log(`Status: ${passed === total ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    if (passed === total) {
      console.log('\n🎉 Territory status and visit functionality is working correctly!');
      console.log('✅ Territory status enum supports "unavailable" instead of "out_of_service"');
      console.log('✅ Partial completion tracking is implemented');
      console.log('✅ Multiple visits per assignment are supported');
      console.log('✅ Visit logging and history tracking works');
      console.log('✅ Database schema supports real-world territory workflows');
    }

  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testTerritoryStatusEnum,
  testPartialCompletionFields,
  testTerritoryVisitModel,
  testMultipleVisitsWorkflow
};
