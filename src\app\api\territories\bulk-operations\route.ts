/**
 * Territory Bulk Operations API Endpoint
 *
 * Handles bulk operations on multiple territories including bulk assignments,
 * bulk status updates, and bulk unassignments.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { AssignmentService } from '@/services/territories/AssignmentService';
import { prisma } from '@/lib/prisma';

// Validation schema for bulk assignment
const BulkAssignmentSchema = z.object({
  territoryIds: z.array(z.string()).min(1, 'At least one territory required'),
  memberId: z.string().min(1, 'Member ID is required'),
  notes: z.string().optional()
});

// Validation schema for bulk status update
const BulkStatusUpdateSchema = z.object({
  territoryIds: z.array(z.string()).min(1, 'At least one territory required'),
  status: z.enum(['available', 'assigned', 'completed', 'out_of_service']),
  reason: z.string().optional()
});

// Validation schema for bulk unassignment
const BulkUnassignmentSchema = z.object({
  territoryIds: z.array(z.string()).min(1, 'At least one territory required'),
  reason: z.string().optional()
});

export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information to check role-based permissions
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: {
        role: true,
        congregationId: true
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions for bulk operations
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for bulk operations' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const operation = body.operation;

    switch (operation) {
      case 'bulk_assign':
        const assignmentData = BulkAssignmentSchema.parse(body);
        
        console.log(`Processing bulk assignment: ${assignmentData.territoryIds.length} territories to member ${assignmentData.memberId}`);

        const assignmentResults = [];
        
        for (const territoryId of assignmentData.territoryIds) {
          try {
            const result = await AssignmentService.assignTerritory(
              {
                territoryId,
                memberId: assignmentData.memberId,
                notes: assignmentData.notes
              },
              user.userId,
              member.congregationId
            );

            assignmentResults.push({
              territoryId,
              success: result.success,
              error: result.error
            });
          } catch (error) {
            assignmentResults.push({
              territoryId,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        const successfulAssignments = assignmentResults.filter(r => r.success).length;
        const failedAssignments = assignmentResults.filter(r => !r.success).length;

        console.log(`Bulk assignment completed: ${successfulAssignments} successful, ${failedAssignments} failed`);

        return NextResponse.json({
          success: true,
          operation: 'bulk_assign',
          results: assignmentResults,
          summary: {
            total: assignmentData.territoryIds.length,
            successful: successfulAssignments,
            failed: failedAssignments
          }
        });

      case 'bulk_status_update':
        const statusData = BulkStatusUpdateSchema.parse(body);
        
        console.log(`Processing bulk status update: ${statusData.territoryIds.length} territories to status ${statusData.status}`);

        const statusResults = [];
        
        for (const territoryId of statusData.territoryIds) {
          try {
            await prisma.territory.update({
              where: {
                id: territoryId,
                congregationId: member.congregationId
              },
              data: {
                status: statusData.status,
                updatedAt: new Date()
              }
            });

            statusResults.push({
              territoryId,
              success: true
            });
          } catch (error) {
            statusResults.push({
              territoryId,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        const successfulStatusUpdates = statusResults.filter(r => r.success).length;
        const failedStatusUpdates = statusResults.filter(r => !r.success).length;

        console.log(`Bulk status update completed: ${successfulStatusUpdates} successful, ${failedStatusUpdates} failed`);

        return NextResponse.json({
          success: true,
          operation: 'bulk_status_update',
          results: statusResults,
          summary: {
            total: statusData.territoryIds.length,
            successful: successfulStatusUpdates,
            failed: failedStatusUpdates
          }
        });

      case 'bulk_unassign':
        const unassignmentData = BulkUnassignmentSchema.parse(body);
        
        console.log(`Processing bulk unassignment: ${unassignmentData.territoryIds.length} territories`);

        const unassignmentResults = [];
        
        for (const territoryId of unassignmentData.territoryIds) {
          try {
            // Find active assignment for this territory
            const activeAssignment = await prisma.territoryAssignment.findFirst({
              where: {
                territoryId,
                status: 'active',
                congregationId: member.congregationId
              }
            });

            if (activeAssignment) {
              const result = await AssignmentService.returnTerritory(
                activeAssignment.id,
                user.userId,
                member.congregationId,
                unassignmentData.reason
              );

              unassignmentResults.push({
                territoryId,
                success: result.success,
                error: result.error
              });
            } else {
              unassignmentResults.push({
                territoryId,
                success: false,
                error: 'No active assignment found'
              });
            }
          } catch (error) {
            unassignmentResults.push({
              territoryId,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        const successfulUnassignments = unassignmentResults.filter(r => r.success).length;
        const failedUnassignments = unassignmentResults.filter(r => !r.success).length;

        console.log(`Bulk unassignment completed: ${successfulUnassignments} successful, ${failedUnassignments} failed`);

        return NextResponse.json({
          success: true,
          operation: 'bulk_unassign',
          results: unassignmentResults,
          summary: {
            total: unassignmentData.territoryIds.length,
            successful: successfulUnassignments,
            failed: failedUnassignments
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid operation. Supported operations: bulk_assign, bulk_status_update, bulk_unassign' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Bulk operations error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to process bulk operation',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST for bulk operations.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST for bulk operations.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST for bulk operations.' },
    { status: 405 }
  );
}
