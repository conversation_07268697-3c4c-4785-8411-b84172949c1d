/**
 * Document Management Types
 * 
 * Centralized type definitions for document management functionality
 * including letters, files, and document-related operations.
 */

// Document interfaces
export interface DocumentData {
  id: string;
  congregationId: string;
  title: string;
  description?: string;
  filename: string;
  filePath: string;
  fileSize?: number;
  mimeType?: string;
  category?: string;
  subcategory?: string;
  tags: string[];
  visibility: DocumentVisibility;
  priority: DocumentPriority;
  status: DocumentStatus;
  version: number;
  parentId?: string;
  folderId?: string;
  expirationDate?: Date;
  publishDate?: Date;
  uploadDate: Date;
  uploadedById?: string;
  approvedById?: string;
  approvedAt?: Date;
  downloadCount: number;
  viewCount: number;
  searchableText?: string;
  thumbnailPath?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateDocumentData {
  title: string;
  description?: string;
  filename: string;
  filePath: string;
  fileSize?: number;
  mimeType?: string;
  category?: string;
  subcategory?: string;
  tags?: string[];
  visibility?: DocumentVisibility;
  priority?: DocumentPriority;
  folderId?: string;
  expirationDate?: Date;
  publishDate?: Date;
  searchableText?: string;
  thumbnailPath?: string;
}

export interface UpdateDocumentData {
  title?: string;
  description?: string;
  category?: string;
  subcategory?: string;
  tags?: string[];
  visibility?: DocumentVisibility;
  priority?: DocumentPriority;
  status?: DocumentStatus;
  folderId?: string;
  expirationDate?: Date;
  publishDate?: Date;
  searchableText?: string;
  thumbnailPath?: string;
  isActive?: boolean;
}

export interface DocumentFilters {
  category?: string;
  subcategory?: string;
  tags?: string[];
  visibility?: DocumentVisibility;
  priority?: DocumentPriority;
  status?: DocumentStatus;
  folderId?: string;
  uploadedById?: string;
  startDate?: Date;
  endDate?: Date;
  search?: string;
  isExpired?: boolean;
}

export interface DocumentFolder {
  id: string;
  congregationId: string;
  name: string;
  description?: string;
  parentId?: string;
  path: string;
  color?: string;
  icon?: string;
  visibility: DocumentVisibility;
  sortOrder: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  children?: DocumentFolder[];
  documentCount?: number;
}

export interface DocumentAccessLog {
  id: string;
  documentId: string;
  memberId: string;
  accessType: DocumentAccessType;
  ipAddress?: string;
  userAgent?: string;
  accessedAt: Date;
}

export interface DocumentComment {
  id: string;
  documentId: string;
  memberId: string;
  parentId?: string;
  content: string;
  isInternal: boolean;
  isResolved: boolean;
  createdAt: Date;
  updatedAt: Date;
  member?: {
    id: string;
    name: string;
  };
  replies?: DocumentComment[];
}

export interface DocumentWorkflow {
  id: string;
  documentId: string;
  workflowType: DocumentWorkflowType;
  status: DocumentWorkflowStatus;
  assignedToId?: string;
  assignedById: string;
  priority: DocumentPriority;
  dueDate?: Date;
  comments?: string;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface DocumentAnalytics {
  totalDocuments: number;
  documentsByCategory: Record<string, number>;
  documentsByStatus: Record<DocumentStatus, number>;
  recentUploads: DocumentData[];
  popularDocuments: DocumentData[];
  expiringDocuments: DocumentData[];
  accessStats: {
    totalViews: number;
    totalDownloads: number;
    uniqueUsers: number;
  };
}

// Document enums
export enum DocumentVisibility {
  ALL_MEMBERS = 'ALL_MEMBERS',
  ELDERS_ONLY = 'ELDERS_ONLY',
  MINISTERIAL_SERVANTS_PLUS = 'MINISTERIAL_SERVANTS_PLUS',
  COORDINATORS_ONLY = 'COORDINATORS_ONLY',
  CUSTOM = 'CUSTOM'
}

export enum DocumentPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum DocumentStatus {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  EXPIRED = 'EXPIRED'
}

export enum DocumentAccessType {
  VIEW = 'VIEW',
  DOWNLOAD = 'DOWNLOAD',
  SEARCH = 'SEARCH'
}

export enum DocumentWorkflowType {
  APPROVAL = 'APPROVAL',
  REVIEW = 'REVIEW',
  PUBLICATION = 'PUBLICATION'
}

export enum DocumentWorkflowStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED'
}

// Legacy Letter interface for backward compatibility
export interface Letter {
  id: string;
  congregationId: string;
  title: string;
  filename: string;
  category: 'announcement' | 'instruction' | 'form' | 'schedule' | 'other';
  visibility: 'public' | 'elders_only' | 'ms_and_elders';
  uploadedBy: string;
  uploadDate: Date;
  createdAt: Date;
  updatedAt: Date;
}
