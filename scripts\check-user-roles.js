/**
 * Check User Roles Script
 * 
 * Checks what roles exist in the database to help debug the admin access issue.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUserRoles() {
  try {
    console.log('🔍 Checking user roles in database...');

    // Get all members with their roles
    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441', // Coral Oeste congregation
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        role: true,
        email: true,
      },
      orderBy: {
        role: 'asc',
      },
    });

    console.log('\n📊 Members and their roles:');
    console.log('================================');
    
    const roleCount = {};
    
    members.forEach(member => {
      console.log(`${member.name}: ${member.role} (${member.email})`);
      roleCount[member.role] = (roleCount[member.role] || 0) + 1;
    });

    console.log('\n📈 Role distribution:');
    console.log('=====================');
    Object.entries(roleCount).forEach(([role, count]) => {
      console.log(`${role}: ${count} members`);
    });

    console.log('\n🔑 Admin access roles should include:');
    console.log('====================================');
    console.log('- elder');
    console.log('- ministerial_servant');
    console.log('- overseer_coordinator');
    console.log('- coordinator');
    console.log('- developer');

    console.log('\n✅ Check complete!');

  } catch (error) {
    console.error('❌ Error checking user roles:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  checkUserRoles()
    .then(() => {
      console.log('✨ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { checkUserRoles };
