/**
 * Midweek Meetings API Endpoint
 * 
 * Handles CRUD operations for midweek meetings including
 * fetching meetings with parts and creating new meetings.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

// Validation schema for creating meetings
const CreateMeetingSchema = z.object({
  meetingDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  theme: z.string().optional(),
  chairmanId: z.string().optional(),
  location: z.string().default('Kingdom Hall'),
  zoomLink: z.string().url().optional(),
  notes: z.string().optional(),
  parts: z.array(z.object({
    partType: z.string(),
    title: z.string(),
    timeAllocation: z.number().optional(),
    bibleReading: z.string().optional(),
    studyPoints: z.string().optional(),
    notes: z.string().optional(),
    displayOrder: z.number(),
  })).optional(),
});

export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { member } = authResult;
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const startDate = searchParams.get('startDate') || new Date().toISOString().split('T')[0];
    const endDate = searchParams.get('endDate') || 
      new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 30 days from now
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50);
    const includeInactive = searchParams.get('includeInactive') === 'true';

    // Fetch meetings with congregation isolation
    const meetings = await prisma.midweekMeeting.findMany({
      where: {
        congregationId: member.congregationId,
        meetingDate: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
        ...(includeInactive ? {} : { isActive: true }),
      },
      include: {
        parts: {
          orderBy: { displayOrder: 'asc' },
        },
      },
      orderBy: { meetingDate: 'asc' },
      take: limit,
    });

    return NextResponse.json({
      success: true,
      meetings,
      count: meetings.length,
      dateRange: { startDate, endDate },
    });

  } catch (error) {
    console.error('Get midweek meetings error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve meetings',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { member } = authResult;

    // Check if user has permission to create meetings
    if (!['elder', 'overseer_coordinator', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create meetings' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CreateMeetingSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { meetingDate, theme, chairmanId, location, zoomLink, notes, parts } = validationResult.data;

    // Check if meeting already exists for this date
    const existingMeeting = await prisma.midweekMeeting.findFirst({
      where: {
        congregationId: member.congregationId,
        meetingDate: new Date(meetingDate),
      },
    });

    if (existingMeeting) {
      return NextResponse.json(
        { error: 'A meeting already exists for this date' },
        { status: 409 }
      );
    }

    // Validate chairman if provided
    if (chairmanId) {
      const chairman = await prisma.member.findFirst({
        where: {
          id: chairmanId,
          congregationId: member.congregationId,
          isActive: true,
        },
      });

      if (!chairman) {
        return NextResponse.json(
          { error: 'Invalid chairman ID or chairman not found' },
          { status: 400 }
        );
      }
    }

    // Create meeting with parts in a transaction
    const meeting = await prisma.$transaction(async (tx) => {
      // Create the meeting
      const newMeeting = await tx.midweekMeeting.create({
        data: {
          congregationId: member.congregationId,
          meetingDate: new Date(meetingDate),
          theme,
          chairman: chairmanId,
          location,
          zoomLink,
          notes,
          isActive: true,
        },
      });

      // Create meeting parts if provided
      if (parts && parts.length > 0) {
        await tx.midweekMeetingPart.createMany({
          data: parts.map(part => ({
            meetingId: newMeeting.id,
            partType: part.partType,
            title: part.title,
            timeAllocation: part.timeAllocation,
            bibleReading: part.bibleReading,
            studyPoints: part.studyPoints,
            notes: part.notes,
            displayOrder: part.displayOrder,
          })),
        });
      }

      return newMeeting;
    });

    // Fetch the complete meeting with parts
    const completeMeeting = await prisma.midweekMeeting.findUnique({
      where: { id: meeting.id },
      include: {
        parts: {
          orderBy: { displayOrder: 'asc' },
        },
      },
    });

    return NextResponse.json({
      success: true,
      meeting: completeMeeting,
      message: 'Meeting created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Create midweek meeting error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to create meeting',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create meetings.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use DELETE on specific meeting ID.' },
    { status: 405 }
  );
}
