// Territory Card Component Tests
// Tests for the TerritoryCard component functionality

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, jest } from '@jest/globals';
import TerritoryCard from '@/components/territories/shared/TerritoryCard';
import { Territory } from '@/types/territories/territory';

// Mock territory data
const mockTerritory: Territory & {
  assignedMember?: {
    id: string;
    name: string;
    assignedAt: Date;
    assignedBy: string;
  } | null;
} = {
  id: 'territory-1',
  congregationId: '1441',
  territoryNumber: 'T001',
  address: '123 Main Street, Anytown',
  status: 'available',
  notes: 'Test territory notes',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-15'),
  assignedMember: null
};

const mockAssignedTerritory: Territory & {
  assignedMember?: {
    id: string;
    name: string;
    assignedAt: Date;
    assignedBy: string;
  } | null;
} = {
  ...mockTerritory,
  id: 'territory-2',
  territoryNumber: 'T002',
  status: 'assigned',
  assignedMember: {
    id: 'member-1',
    name: '<PERSON>',
    assignedAt: new Date('2024-01-10'),
    assignedBy: 'elder-1'
  }
};

describe('TerritoryCard', () => {
  it('renders territory information correctly', () => {
    render(<TerritoryCard territory={mockTerritory} />);

    // Check territory number
    expect(screen.getByText('Territorio T001')).toBeInTheDocument();
    expect(screen.getByText('T001')).toBeInTheDocument();

    // Check address
    expect(screen.getByText('123 Main Street, Anytown')).toBeInTheDocument();

    // Check status
    expect(screen.getByText('Disponible')).toBeInTheDocument();

    // Check notes
    expect(screen.getByText('Test territory notes')).toBeInTheDocument();

    // Check updated date
    expect(screen.getByText(/Actualizado el/)).toBeInTheDocument();
  });

  it('displays assigned member information when territory is assigned', () => {
    render(<TerritoryCard territory={mockAssignedTerritory} />);

    // Check assigned status
    expect(screen.getByText('Asignado')).toBeInTheDocument();

    // Check assigned member name
    expect(screen.getByText('Juan Pérez')).toBeInTheDocument();

    // Check assignment date
    expect(screen.getByText(/Asignado el/)).toBeInTheDocument();
  });

  it('does not display assigned member section when territory is available', () => {
    render(<TerritoryCard territory={mockTerritory} />);

    // Should not show assigned member information
    expect(screen.queryByText('Juan Pérez')).not.toBeInTheDocument();
    expect(screen.queryByText(/Asignado el/)).not.toBeInTheDocument();
  });

  it('calls onClick handler when card is clicked', () => {
    const mockOnClick = jest.fn();
    render(<TerritoryCard territory={mockTerritory} onClick={mockOnClick} />);

    const card = screen.getByRole('button');
    fireEvent.click(card);

    expect(mockOnClick).toHaveBeenCalledWith(mockTerritory);
  });

  it('displays correct status colors and icons', () => {
    const { rerender } = render(<TerritoryCard territory={mockTerritory} />);

    // Available status
    expect(screen.getByText('✓')).toBeInTheDocument();
    expect(screen.getByText('Disponible')).toBeInTheDocument();

    // Assigned status
    rerender(<TerritoryCard territory={mockAssignedTerritory} />);
    expect(screen.getByText('👤')).toBeInTheDocument();
    expect(screen.getByText('Asignado')).toBeInTheDocument();

    // Completed status
    const completedTerritory = { ...mockTerritory, status: 'completed' as const };
    rerender(<TerritoryCard territory={completedTerritory} />);
    expect(screen.getByText('✅')).toBeInTheDocument();
    expect(screen.getByText('Completado')).toBeInTheDocument();

    // Out of service status
    const outOfServiceTerritory = { ...mockTerritory, status: 'out_of_service' as const };
    rerender(<TerritoryCard territory={outOfServiceTerritory} />);
    expect(screen.getByText('⚠️')).toBeInTheDocument();
    expect(screen.getByText('Fuera de Servicio')).toBeInTheDocument();
  });

  it('handles territory without notes', () => {
    const territoryWithoutNotes = { ...mockTerritory, notes: undefined };
    render(<TerritoryCard territory={territoryWithoutNotes} />);

    // Should not display notes section
    expect(screen.queryByText('Test territory notes')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <TerritoryCard territory={mockTerritory} className="custom-class" />
    );

    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass('custom-class');
  });

  it('has proper accessibility attributes', () => {
    render(<TerritoryCard territory={mockTerritory} />);

    const card = screen.getByRole('button');
    expect(card).toHaveAttribute('tabIndex', '0');
    expect(card).toHaveAttribute('aria-label', 'Territorio T001 - 123 Main Street, Anytown');
  });

  it('formats dates correctly', () => {
    render(<TerritoryCard territory={mockAssignedTerritory} />);

    // Check that dates are formatted in Spanish locale
    expect(screen.getByText(/09\/01\/2024/)).toBeInTheDocument(); // Assignment date
    expect(screen.getByText(/14\/01\/2024/)).toBeInTheDocument(); // Updated date
  });
});
