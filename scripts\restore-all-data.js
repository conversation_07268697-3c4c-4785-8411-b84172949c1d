#!/usr/bin/env node

/**
 * Restore All Data
 *
 * This script restores <PERSON><PERSON> the data that was lost during the database reset,
 * including congregation, members, settings, songs, tasks, and other essential data.
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function restoreAllData() {
    console.log('🔄 RESTORING ALL DELETED DATA...');
    console.log('');

    try {
        // 1. Create Coral Oeste congregation
        console.log('📋 Step 1: Creating Coral Oeste congregation...');

        const congregation = await prisma.congregation.upsert({
            where: { id: '1441' },
            update: {},
            create: {
                id: '1441',
                name: 'Coral Oeste',
                region: 'Hialeah',
                pin: await bcrypt.hash('123456', 10),
                language: 'es',
                timezone: 'America/New_York',
                isActive: true,
                settings: {}
            }
        });

        console.log(`   ✅ Congregation: ${congregation.name} (ID: ${congregation.id})`);

        // 2. Create default roles
        console.log('📋 Step 2: Creating default roles...');

        const roles = [
            { name: 'elder', description: '<PERSON><PERSON><PERSON>' },
            { name: 'ministerial_servant', description: 'Siervo Ministerial' },
            { name: 'publisher', description: 'Publicador' },
            { name: 'pioneer', description: 'Precursor' },
            { name: 'auxiliary_pioneer', description: 'Precursor Auxiliar' }
        ];

        for (const roleData of roles) {
            await prisma.role.upsert({
                where: { name: roleData.name },
                update: {},
                create: roleData
            });
        }

        console.log(`   ✅ Created ${roles.length} roles`);

        // 3. Create comprehensive member list
        console.log('📋 Step 3: Creating members...');

        const members = [
            // Admin and Elders
            {
                name: 'Administrador Sistema',
                email: '<EMAIL>',
                role: 'elder',
                serviceGroup: 'Grupo 1',
                phone: '******-555-0001'
            },
            {
                name: 'Juan Carlos Pérez',
                email: '<EMAIL>',
                role: 'elder',
                serviceGroup: 'Grupo 1',
                phone: '******-555-0002'
            },
            {
                name: 'Roberto Martínez',
                email: '<EMAIL>',
                role: 'elder',
                serviceGroup: 'Grupo 2',
                phone: '******-555-0003'
            },
            // Ministerial Servants
            {
                name: 'Carlos Rodríguez',
                email: '<EMAIL>',
                role: 'ministerial_servant',
                serviceGroup: 'Grupo 1',
                phone: '******-555-0004'
            },
            {
                name: 'Miguel Fernández',
                email: '<EMAIL>',
                role: 'ministerial_servant',
                serviceGroup: 'Grupo 2',
                phone: '******-555-0005'
            },
            // Pioneers
            {
                name: 'Ana María González',
                email: '<EMAIL>',
                role: 'pioneer',
                serviceGroup: 'Grupo 1',
                phone: '******-555-0006'
            },
            {
                name: 'Carmen López',
                email: '<EMAIL>',
                role: 'pioneer',
                serviceGroup: 'Grupo 2',
                phone: '******-555-0007'
            },
            // Publishers
            {
                name: 'José Ramírez',
                email: '<EMAIL>',
                role: 'publisher',
                serviceGroup: 'Grupo 1',
                phone: '******-555-0008'
            },
            {
                name: 'María Elena Vargas',
                email: '<EMAIL>',
                role: 'publisher',
                serviceGroup: 'Grupo 2',
                phone: '******-555-0009'
            },
            {
                name: 'Luis Alberto Sánchez',
                email: '<EMAIL>',
                role: 'publisher',
                serviceGroup: 'Grupo 1',
                phone: '******-555-0010'
            },
            {
                name: 'Patricia Morales',
                email: '<EMAIL>',
                role: 'publisher',
                serviceGroup: 'Grupo 2',
                phone: '******-555-0011'
            },
            {
                name: 'Fernando Castro',
                email: '<EMAIL>',
                role: 'publisher',
                serviceGroup: 'Grupo 1',
                phone: '******-555-0012'
            }
        ];

        for (const memberData of members) {
            await prisma.member.upsert({
                where: {
                    congregationId_email: {
                        congregationId: '1441',
                        email: memberData.email
                    }
                },
                update: {},
                create: {
                    congregationId: '1441',
                    name: memberData.name,
                    email: memberData.email,
                    phone: memberData.phone,
                    role: memberData.role,
                    pin: await bcrypt.hash('123456', 10),
                    isActive: true,
                    preferences: {},
                    contactPreferences: {},
                    qualifications: [],
                    serviceGroup: memberData.serviceGroup,
                    address: '7790 West 4th Av Hialeah Fl 33014'
                }
            });
        }

        console.log(`   ✅ Created ${members.length} members`);

        // 4. Create congregation settings
        console.log('📋 Step 4: Creating congregation settings...');

        const settings = [
            { settingKey: 'language', settingValue: 'es' },
            { settingKey: 'timezone', settingValue: 'America/New_York' },
            { settingKey: 'meeting_location', settingValue: 'Salon del Reino' },
            { settingKey: 'meeting_address', settingValue: '7790 West 4th Av Hialeah Fl 33014' },
            { settingKey: 'midweek_meeting_day', settingValue: 'thursday' },
            { settingKey: 'midweek_meeting_time', settingValue: '19:30' },
            { settingKey: 'weekend_meeting_day', settingValue: 'sunday' },
            { settingKey: 'weekend_meeting_time', settingValue: '10:00' },
            { settingKey: 'public_talk_time', settingValue: '09:30' },
            { settingKey: 'watchtower_time', settingValue: '10:30' },
            { settingKey: 'congregation_number', settingValue: '1441' },
            { settingKey: 'circuit', settingValue: 'FLS-15' },
            { settingKey: 'service_overseer', settingValue: 'Juan Carlos Pérez' },
            { settingKey: 'coordinator', settingValue: 'Roberto Martínez' }
        ];

        for (const setting of settings) {
            await prisma.congregationSetting.upsert({
                where: {
                    congregationId_settingKey: {
                        congregationId: '1441',
                        settingKey: setting.settingKey
                    }
                },
                update: { settingValue: setting.settingValue },
                create: {
                    congregationId: '1441',
                    settingKey: setting.settingKey,
                    settingValue: setting.settingValue
                }
            });
        }

        console.log(`   ✅ Created ${settings.length} congregation settings`);

        // 5. Create service groups
        console.log('📋 Step 5: Creating service groups...');

        const serviceGroups = [
            {
                groupNumber: 1,
                name: 'Grupo 1',
                description: 'Primer grupo de servicio del campo'
            },
            {
                groupNumber: 2,
                name: 'Grupo 2',
                description: 'Segundo grupo de servicio del campo'
            }
        ];

        for (const groupData of serviceGroups) {
            await prisma.serviceGroup.upsert({
                where: {
                    congregationId_groupNumber: {
                        congregationId: '1441',
                        groupNumber: groupData.groupNumber
                    }
                },
                update: {},
                create: {
                    congregationId: '1441',
                    name: groupData.name,
                    groupNumber: groupData.groupNumber,
                    description: groupData.description,
                    isActive: true
                }
            });
        }

        console.log(`   ✅ Created ${serviceGroups.length} service groups`);

        // 6. Create basic songs data
        console.log('📋 Step 6: Creating songs data...');

        const basicSongs = [
            { songNumber: 1, titleEs: 'Jehová es mi Pastor', titleEn: 'Jehovah Is My Shepherd' },
            { songNumber: 2, titleEs: 'Jehová es mi Fuerza', titleEn: 'Jehovah Is My Strength' },
            { songNumber: 3, titleEs: 'Nuestro Dios es un Dios de Amor', titleEn: 'Our God Is a God of Love' },
            { songNumber: 4, titleEs: 'Hagamos Conocido Su Nombre', titleEn: 'Making Known His Name' },
            { songNumber: 5, titleEs: 'Dios de Verdadera Profecía', titleEn: 'God of True Prophecy' }
        ];

        for (const song of basicSongs) {
            await prisma.song.upsert({
                where: { songNumber: song.songNumber },
                update: {},
                create: {
                    songNumber: song.songNumber,
                    titleEs: song.titleEs,
                    titleEn: song.titleEn,
                    isActive: true
                }
            });
        }

        console.log(`   ✅ Created ${basicSongs.length} songs`);

        // 7. Create basic tasks
        console.log('📋 Step 7: Creating basic tasks...');

        const basicTasks = [
            {
                title: 'Limpieza del Salón',
                description: 'Limpieza semanal del Salón del Reino',
                category: 'maintenance',
                frequency: 'weekly',
                estimatedTime: 120
            },
            {
                title: 'Sonido y Micrófono',
                description: 'Manejo del equipo de sonido durante las reuniones',
                category: 'meeting_support',
                frequency: 'weekly',
                estimatedTime: 180
            },
            {
                title: 'Acomodador',
                description: 'Recibir y acomodar a los asistentes',
                category: 'meeting_support',
                frequency: 'weekly',
                estimatedTime: 180
            }
        ];

        for (const task of basicTasks) {
            // Check if task already exists
            const existingTask = await prisma.task.findFirst({
                where: {
                    congregationId: '1441',
                    title: task.title
                }
            });

            if (!existingTask) {
                await prisma.task.create({
                    data: {
                        congregationId: '1441',
                        title: task.title,
                        description: task.description,
                        category: task.category,
                        frequency: task.frequency,
                        estimatedTime: task.estimatedTime,
                        isActive: true
                    }
                });
            }
        }

        console.log(`   ✅ Created ${basicTasks.length} tasks`);

        console.log('');
        console.log('🎉 ALL DATA RESTORED SUCCESSFULLY!');
        console.log('');
        console.log('📋 LOGIN CREDENTIALS:');
        console.log('   Congregation ID: 1441');
        console.log('   Congregation PIN: 123456');
        console.log('   Admin Email: <EMAIL>');
        console.log('   Admin PIN: 123456');
        console.log('');
        console.log('👥 SAMPLE MEMBERS:');
        console.log('   All members use PIN: 123456');
        console.log('   Elders: <EMAIL>, <EMAIL>');
        console.log('   Ministerial Servants: <EMAIL>, <EMAIL>');
        console.log('   Pioneers: <EMAIL>, <EMAIL>');
        console.log('');
        console.log('🎯 TESTING INSTRUCTIONS:');
        console.log('   1. Go to http://localhost:3000/login');
        console.log('   2. Enter Congregation ID: 1441');
        console.log('   3. Enter Congregation PIN: 123456');
        console.log('   4. Login with any member email / PIN: 123456');
        console.log('   5. Verify all features work correctly');

    } catch (error) {
        console.error('❌ Error restoring data:', error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

// Run the restoration
restoreAllData()
    .catch((error) => {
        console.error('Failed to restore data:', error);
        process.exit(1);
    });
