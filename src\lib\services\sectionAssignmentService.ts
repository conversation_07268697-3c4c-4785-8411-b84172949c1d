/**
 * Section Assignment Service
 *
 * Handles administrative section assignments, transfers, and history tracking
 * for the delegation system.
 */

import { prisma } from '@/lib/prisma';
import { ADMINISTRATIVE_SECTIONS, ScopeDefinition, canAssignSection } from '@/lib/constants/administrativeSections';

export interface SectionAssignmentData {
  id: string;
  memberId: string;
  memberName: string;
  memberRole: string;
  sectionType: ADMINISTRATIVE_SECTIONS;
  scopeDefinition: ScopeDefinition;
  assignedBy: string | null;
  assignedByName: string | null;
  assignedAt: Date;
  isActive: boolean;
}

export interface AssignmentHistoryData {
  id: string;
  memberId: string;
  memberName: string;
  sectionType: ADMINISTRATIVE_SECTIONS;
  action: 'assigned' | 'removed' | 'transferred';
  assignedBy: string | null;
  assignedByName: string | null;
  assignedTo: string | null;
  assignedToName: string | null;
  reason: string | null;
  createdAt: Date;
}

export class SectionAssignmentService {
  /**
   * Get all section assignments for a congregation
   */
  static async getAssignments(congregationId: string): Promise<SectionAssignmentData[]> {
    const assignments = await prisma.sectionAssignment.findMany({
      where: {
        congregationId,
        isActive: true,
      },
      include: {
        member: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        assignedByMember: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [
        { sectionType: 'asc' },
        { assignedAt: 'desc' },
      ],
    });

    return assignments.map(assignment => ({
      id: assignment.id,
      memberId: assignment.memberId,
      memberName: assignment.member.name,
      memberRole: assignment.member.role,
      sectionType: assignment.sectionType as ADMINISTRATIVE_SECTIONS,
      scopeDefinition: assignment.scopeDefinition as ScopeDefinition,
      assignedBy: assignment.assignedBy,
      assignedByName: assignment.assignedByMember?.name || null,
      assignedAt: assignment.assignedAt,
      isActive: assignment.isActive,
    }));
  }

  /**
   * Get assignments for a specific member
   */
  static async getMemberAssignments(congregationId: string, memberId: string): Promise<SectionAssignmentData[]> {
    const assignments = await prisma.sectionAssignment.findMany({
      where: {
        congregationId,
        memberId,
        isActive: true,
      },
      include: {
        member: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        assignedByMember: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        assignedAt: 'desc',
      },
    });

    return assignments.map(assignment => ({
      id: assignment.id,
      memberId: assignment.memberId,
      memberName: assignment.member.name,
      memberRole: assignment.member.role,
      sectionType: assignment.sectionType as ADMINISTRATIVE_SECTIONS,
      scopeDefinition: assignment.scopeDefinition as ScopeDefinition,
      assignedBy: assignment.assignedBy,
      assignedByName: assignment.assignedByMember?.name || null,
      assignedAt: assignment.assignedAt,
      isActive: assignment.isActive,
    }));
  }

  /**
   * Assign a section to a member
   */
  static async assignSection(
    congregationId: string,
    memberId: string,
    sectionType: ADMINISTRATIVE_SECTIONS,
    scopeDefinition: ScopeDefinition,
    assignedBy: string,
    reason?: string
  ): Promise<SectionAssignmentData> {
    // Validate assignment permissions
    const [assignerMember, targetMember] = await Promise.all([
      prisma.member.findUnique({
        where: { id: assignedBy },
        select: { role: true, name: true },
      }),
      prisma.member.findUnique({
        where: { id: memberId },
        select: { role: true, name: true },
      }),
    ]);

    if (!assignerMember || !targetMember) {
      throw new Error('Assigner or target member not found');
    }

    if (!canAssignSection(assignerMember.role, targetMember.role, sectionType)) {
      throw new Error('Insufficient permissions to assign this section');
    }

    // Check if assignment already exists
    const existingAssignment = await prisma.sectionAssignment.findUnique({
      where: {
        memberId_sectionType: {
          memberId,
          sectionType,
        },
      },
    });

    if (existingAssignment && existingAssignment.isActive) {
      throw new Error('Member already has an active assignment for this section');
    }

    // Create assignment and history record in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create or reactivate assignment
      const assignment = await tx.sectionAssignment.upsert({
        where: {
          memberId_sectionType: {
            memberId,
            sectionType,
          },
        },
        update: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          scopeDefinition: scopeDefinition as any,
          assignedBy,
          assignedAt: new Date(),
          isActive: true,
          updatedAt: new Date(),
        },
        create: {
          congregationId,
          memberId,
          sectionType,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          scopeDefinition: scopeDefinition as any,
          assignedBy,
        },
        include: {
          member: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
          assignedByMember: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Create history record
      await tx.assignmentHistory.create({
        data: {
          congregationId,
          memberId,
          sectionType,
          action: 'assigned',
          assignedBy,
          reason: reason || `Asignado a ${sectionType} por ${assignerMember.name}`,
        },
      });

      return assignment;
    });

    return {
      id: result.id,
      memberId: result.memberId,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      memberName: (result as any).member.name,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      memberRole: (result as any).member.role,
      sectionType: result.sectionType as ADMINISTRATIVE_SECTIONS,
      scopeDefinition: result.scopeDefinition as ScopeDefinition,
      assignedBy: result.assignedBy,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      assignedByName: (result as any).assignedByMember?.name || null,
      assignedAt: result.assignedAt,
      isActive: result.isActive,
    };
  }

  /**
   * Remove a section assignment
   */
  static async removeAssignment(
    congregationId: string,
    assignmentId: string,
    removedBy: string,
    reason?: string
  ): Promise<void> {
    const assignment = await prisma.sectionAssignment.findUnique({
      where: { id: assignmentId },
      include: {
        member: {
          select: { name: true },
        },
      },
    });

    if (!assignment || assignment.congregationId !== congregationId) {
      throw new Error('Assignment not found');
    }

    await prisma.$transaction(async (tx) => {
      // Deactivate assignment
      await tx.sectionAssignment.update({
        where: { id: assignmentId },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
      });

      // Create history record
      await tx.assignmentHistory.create({
        data: {
          congregationId,
          memberId: assignment.memberId,
          sectionType: assignment.sectionType,
          action: 'removed',
          assignedBy: removedBy,
          reason: reason || `Removido de ${assignment.sectionType}`,
        },
      });
    });
  }

  /**
   * Transfer a section assignment from one member to another
   */
  static async transferAssignment(
    congregationId: string,
    fromMemberId: string,
    toMemberId: string,
    sectionType: ADMINISTRATIVE_SECTIONS,
    scopeDefinition: ScopeDefinition,
    transferredBy: string,
    reason?: string
  ): Promise<SectionAssignmentData> {
    // Remove old assignment and create new one
    await prisma.$transaction(async (tx) => {
      // Deactivate old assignment
      await tx.sectionAssignment.updateMany({
        where: {
          congregationId,
          memberId: fromMemberId,
          sectionType,
          isActive: true,
        },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
      });

      // Create history record for transfer
      await tx.assignmentHistory.create({
        data: {
          congregationId,
          memberId: fromMemberId,
          sectionType,
          action: 'transferred',
          assignedBy: transferredBy,
          assignedTo: toMemberId,
          reason: reason || `Transferido de ${fromMemberId} a ${toMemberId}`,
        },
      });
    });

    // Create new assignment
    return await this.assignSection(
      congregationId,
      toMemberId,
      sectionType,
      scopeDefinition,
      transferredBy,
      reason
    );
  }

  /**
   * Get assignment history for a congregation
   */
  static async getAssignmentHistory(
    congregationId: string,
    limit: number = 50
  ): Promise<AssignmentHistoryData[]> {
    const history = await prisma.assignmentHistory.findMany({
      where: { congregationId },
      include: {
        member: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedByMember: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedToMember: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });

    return history.map(record => ({
      id: record.id,
      memberId: record.memberId,
      memberName: record.member.name,
      sectionType: record.sectionType as ADMINISTRATIVE_SECTIONS,
      action: record.action as 'assigned' | 'removed' | 'transferred',
      assignedBy: record.assignedBy,
      assignedByName: record.assignedByMember?.name || null,
      assignedTo: record.assignedTo,
      assignedToName: record.assignedToMember?.name || null,
      reason: record.reason,
      createdAt: record.createdAt,
    }));
  }
}
