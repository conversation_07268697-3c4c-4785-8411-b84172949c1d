/**
 * Test Songs Server Action
 * 
 * Tests the new server action for fetching songs without authentication
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSongsServerAction() {
  try {
    console.log('🎵 Testing Songs Server Action...\n');

    // Test basic song fetching
    console.log('📋 Testing basic song fetching...');
    const songs = await prisma.song.findMany({
      orderBy: { songNumber: 'asc' },
      take: 10,
      select: {
        id: true,
        songNumber: true,
        titleEs: true,
        titleEn: true,
        category: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log(`✅ Found ${songs.length} songs`);
    if (songs.length > 0) {
      console.log('First song:', {
        number: songs[0].songNumber,
        titleEs: songs[0].titleEs,
        titleEn: songs[0].titleEn
      });
    }

    // Test language filtering
    console.log('\n🌐 Testing Spanish language filtering...');
    const spanishSongs = await prisma.song.findMany({
      where: {
        titleEs: { not: null, not: '' }
      },
      orderBy: { songNumber: 'asc' },
      take: 5,
    });
    console.log(`✅ Found ${spanishSongs.length} songs with Spanish titles`);

    console.log('\n🌐 Testing English language filtering...');
    const englishSongs = await prisma.song.findMany({
      where: {
        titleEn: { not: null, not: '' }
      },
      orderBy: { songNumber: 'asc' },
      take: 5,
    });
    console.log(`✅ Found ${englishSongs.length} songs with English titles`);

    // Test pagination
    console.log('\n📄 Testing pagination...');
    const page1 = await prisma.song.findMany({
      orderBy: { songNumber: 'asc' },
      skip: 0,
      take: 10,
    });
    
    const page2 = await prisma.song.findMany({
      orderBy: { songNumber: 'asc' },
      skip: 10,
      take: 10,
    });

    console.log(`✅ Page 1: ${page1.length} songs`);
    console.log(`✅ Page 2: ${page2.length} songs`);

    // Test total count
    console.log('\n📊 Testing total count...');
    const totalCount = await prisma.song.count();
    console.log(`✅ Total songs in database: ${totalCount}`);

    console.log('\n🎉 All server action tests passed!');
    console.log('The Song Management interface should now work without authentication errors.');

  } catch (error) {
    console.error('❌ Server action test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSongsServerAction().catch(console.error);
