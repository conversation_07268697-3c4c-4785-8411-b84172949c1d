'use client';

/**
 * Enhanced Member Management Page
 *
 * Comprehensive member management interface with permission delegation integration,
 * advanced search, filtering, and role-based access control from Story 2.1.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Edit, RotateCcw, UserX, Trash2, Search } from 'lucide-react';
import EnhancedMemberForm from '@/components/admin/members/EnhancedMemberForm';
import ResetPinModal from '@/components/admin/members/ResetPinModal';
import ConfirmationModal from '@/components/admin/members/ConfirmationModal';
import AdminFooter from '@/components/admin/AdminFooter';
import { MemberProfile } from '@/lib/services/memberManagementService';

interface MemberSearchFilters {
  roles: string[];
  serviceGroups: string[];
  status: string[];
  qualifications: string[];
}

interface SearchState {
  query: string;
  filters: MemberSearchFilters;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  page: number;
  limit: number;
}

export default function EnhancedMemberManagementPage() {
  const router = useRouter();
  const [members, setMembers] = useState<MemberProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingMember, setEditingMember] = useState<MemberProfile | null>(null);
  const [canManage, setCanManage] = useState(false);
  const [totalMembers, setTotalMembers] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Modal states
  const [showResetPinModal, setShowResetPinModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<MemberProfile | null>(null);
  const [confirmationConfig, setConfirmationConfig] = useState<{
    title: string;
    message: string;
    confirmText: string;
    confirmVariant: 'danger' | 'warning' | 'success' | 'primary';
    icon: 'warning' | 'info';
    action: () => Promise<void>;
  } | null>(null);

  // Data for dropdowns
  const [serviceGroups, setServiceGroups] = useState<string[]>([]);
  const [roles, setRoles] = useState<{value: string, label: string}[]>([]);

  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    filters: {
      roles: [],
      serviceGroups: [],
      status: [],
      qualifications: [],
    },
    sortBy: 'name',
    sortOrder: 'asc',
    page: 1,
    limit: 20,
  });

  const roleOptions = [
    { value: 'publisher', label: 'Publicador' },
    { value: 'ministerial_servant', label: 'Siervo Ministerial' },
    { value: 'elder', label: 'Anciano' },
  ];

  const statusOptions = [
    { value: 'active', label: 'Activo' },
    { value: 'inactive', label: 'Inactivo' },
  ];

  useEffect(() => {
    loadMembers();
    loadServiceGroups();
    loadRoles();
  }, [searchState]);

  const loadServiceGroups = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/service-groups', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const groupNames = data.groups?.map((group: any) => `Grupo ${group.groupNumber}`) || [];
        setServiceGroups(groupNames);
      }
    } catch (error) {
      console.error('Error loading service groups:', error);
    }
  };

  const loadRoles = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/roles', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const roleOptions = data.roles?.map((role: any) => ({
          value: role.name,
          label: role.description || role.name
        })) || [];
        setRoles(roleOptions);
      } else {
        // Fallback to hardcoded roles if API fails
        setRoles([
          { value: 'publisher', label: 'Publicador' },
          { value: 'ministerial_servant', label: 'Siervo Ministerial' },
          { value: 'elder', label: 'Anciano' },
          { value: 'coordinator', label: 'Coordinador' },
          { value: 'developer', label: 'Desarrollador' },
        ]);
      }
    } catch (error) {
      console.error('Error loading roles:', error);
      // Fallback to hardcoded roles
      setRoles([
        { value: 'publisher', label: 'Publicador' },
        { value: 'ministerial_servant', label: 'Siervo Ministerial' },
        { value: 'elder', label: 'Anciano' },
        { value: 'coordinator', label: 'Coordinador' },
        { value: 'developer', label: 'Desarrollador' },
      ]);
    }
  };

  const loadMembers = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();

      if (searchState.query) {
        params.append('query', searchState.query);
      }

      if (searchState.filters.roles.length > 0) {
        params.append('roles', searchState.filters.roles.join(','));
      }

      if (searchState.filters.serviceGroups.length > 0) {
        params.append('serviceGroups', searchState.filters.serviceGroups.join(','));
      }

      if (searchState.filters.status.length > 0) {
        params.append('status', searchState.filters.status.join(','));
      }

      if (searchState.filters.qualifications.length > 0) {
        params.append('qualifications', searchState.filters.qualifications.join(','));
      }

      params.append('page', searchState.page.toString());
      params.append('limit', searchState.limit.toString());
      params.append('sortBy', searchState.sortBy);
      params.append('sortOrder', searchState.sortOrder);

      const response = await fetch(`/api/admin/members/enhanced?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hermanos_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load members');
      }

      const data = await response.json();

      if (data.success) {
        setMembers(data.members);
        setTotalMembers(data.total);
        setTotalPages(data.pages);
        setCanManage(true); // If we can load, we can manage
      } else {
        throw new Error(data.error || 'Failed to load members');
      }

    } catch (error) {
      console.error('Error loading members:', error);
      setError(error instanceof Error ? error.message : 'Failed to load members');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchState(prev => ({
      ...prev,
      query,
      page: 1, // Reset to first page
    }));
  };

  const handleFilterChange = (filterType: keyof MemberSearchFilters, values: string[]) => {
    setSearchState(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [filterType]: values,
      },
      page: 1, // Reset to first page
    }));
  };

  const handleSort = (sortBy: string) => {
    setSearchState(prev => ({
      ...prev,
      sortBy,
      sortOrder: prev.sortBy === sortBy && prev.sortOrder === 'asc' ? 'desc' : 'asc',
      page: 1, // Reset to first page
    }));
  };

  const handlePageChange = (page: number) => {
    setSearchState(prev => ({
      ...prev,
      page,
    }));
  };

  const handleCreateMember = async (memberData: any) => {
    try {
      const response = await fetch('/api/admin/members/enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('hermanos_token')}`,
        },
        body: JSON.stringify(memberData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create member');
      }

      const data = await response.json();

      if (data.success) {
        setShowForm(false);
        await loadMembers(); // Reload the list
      } else {
        throw new Error(data.error || 'Failed to create member');
      }

    } catch (error) {
      console.error('Error creating member:', error);
      throw error; // Re-throw to be handled by the form
    }
  };

  const handleUpdateMember = async (memberData: any) => {
    if (!editingMember) return;

    try {
      const response = await fetch('/api/admin/members/enhanced', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('hermanos_token')}`,
        },
        body: JSON.stringify({
          memberId: editingMember.id,
          ...memberData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update member');
      }

      const data = await response.json();

      if (data.success) {
        setEditingMember(null);
        setShowForm(false);
        await loadMembers(); // Reload the list
      } else {
        throw new Error(data.error || 'Failed to update member');
      }

    } catch (error) {
      console.error('Error updating member:', error);
      throw error; // Re-throw to be handled by the form
    }
  };

  const handleEditMember = (member: MemberProfile) => {
    setEditingMember(member);
    setShowForm(true);
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingMember(null);
  };

  const getRoleDisplayName = (role: string): string => {
    const roleOption = roleOptions.find(option => option.value === role);
    return roleOption ? roleOption.label : role;
  };

  const getRoleColor = (role: string): string => {
    const roleColors = {
      'publisher': 'bg-blue-100 text-blue-800',
      'ministerial_servant': 'bg-green-100 text-green-800',
      'elder': 'bg-purple-100 text-purple-800',
      'coordinator': 'bg-orange-100 text-orange-800',
    };
    return roleColors[role as keyof typeof roleColors] || 'bg-gray-100 text-gray-800';
  };

  const handleResetPin = (member: MemberProfile) => {
    setSelectedMember(member);
    setShowResetPinModal(true);
  };

  const handleResetPinSuccess = (newPin?: string) => {
    if (newPin) {
      alert(`PIN reseteado exitosamente. Nuevo PIN: ${newPin}`);
    } else {
      alert('PIN reseteado exitosamente.');
    }
    // Optionally reload members to reflect any changes
    loadMembers();
  };

  const handleSuspendMember = (member: MemberProfile) => {
    const action = member.isActive ? 'suspender' : 'reactivar';
    const actionText = member.isActive ? 'Suspend' : 'Reactivate';

    setSelectedMember(member);
    setConfirmationConfig({
      title: `${actionText} ${member.name}`,
      message: member.isActive
        ? `Are you sure you want to suspend ${member.name}?`
        : `Are you sure you want to reactivate ${member.name}?`,
      confirmText: actionText,
      confirmVariant: member.isActive ? 'warning' : 'success',
      icon: member.isActive ? 'warning' : 'info',
      action: async () => {
        const response = await fetch('/api/admin/members/enhanced', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('hermanos_token')}`,
          },
          body: JSON.stringify({
            memberId: member.id,
            isActive: !member.isActive,
            reason: `Member ${action} from member management interface`
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update member status');
        }

        const data = await response.json();
        if (data.success) {
          await loadMembers(); // Reload the list
        } else {
          throw new Error(data.error || 'Failed to update member status');
        }
      }
    });
    setShowConfirmationModal(true);
  };

  const handleRemoveMember = (member: MemberProfile) => {
    setSelectedMember(member);
    setConfirmationConfig({
      title: `Remove ${member.name}`,
      message: `Are you sure you want to permanently remove ${member.name}? This action cannot be undone.`,
      confirmText: 'Remove',
      confirmVariant: 'danger',
      icon: 'warning',
      action: async () => {
        const response = await fetch('/api/admin/members/enhanced', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('hermanos_token')}`,
          },
          body: JSON.stringify({ memberId: member.id }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to remove member');
        }

        const data = await response.json();
        if (data.success) {
          await loadMembers(); // Reload the list
        } else {
          throw new Error(data.error || 'Failed to remove member');
        }
      }
    });
    setShowConfirmationModal(true);
  };



  if (loading && members.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando miembros...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => loadMembers()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  if (showForm) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <EnhancedMemberForm
            member={editingMember || undefined}
            onSubmit={editingMember ? handleUpdateMember : handleCreateMember}
            onCancel={handleCancelForm}
            canManageRoles={canManage}
            availableServiceGroups={serviceGroups}
            availableRoles={roles}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Pixel-perfect match to reference design */}
      <header className="bg-blue-600 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin')}
                className="flex items-center text-white hover:text-blue-100 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span className="text-sm font-medium">Back</span>
              </button>
            </div>
            <div className="text-center flex-1">
              <h1 className="text-xl font-semibold text-white">
                Member Management
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              {canManage && (
                <button
                  onClick={() => setShowForm(true)}
                  className="px-4 py-2 bg-blue-700 hover:bg-blue-800 text-white rounded-md text-sm font-medium transition-colors"
                >
                  Add Member
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Search and Filters - Single Row Layout */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 pb-20">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex gap-2 items-center">
            {/* Search Input */}
            <div className="flex-1">
              <input
                type="text"
                value={searchState.query}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="Search members..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Search Icon Button */}
            <button
              type="button"
              className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 flex-shrink-0"
              title="Search"
            >
              <Search size={20} />
            </button>
          </div>

          {/* Filter Pills Row */}
          <div className="flex flex-wrap gap-2 mt-4">
            {/* Role Filter Pills */}
            {roleOptions.map(option => (
              <button
                key={option.value}
                onClick={() => {
                  const isSelected = searchState.filters.roles.includes(option.value);
                  handleFilterChange('roles',
                    isSelected
                      ? searchState.filters.roles.filter(r => r !== option.value)
                      : [...searchState.filters.roles, option.value]
                  );
                }}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  searchState.filters.roles.includes(option.value)
                    ? 'bg-blue-100 text-blue-800 border border-blue-300'
                    : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Members List - Table Layout */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Table Header - Hidden on mobile */}
          <div className="hidden md:block px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div className="grid grid-cols-11 gap-4 items-center text-sm font-medium text-gray-700">
              <div className="col-span-3">Name</div>
              <div className="col-span-2">Phone</div>
              <div className="col-span-2">Role</div>
              <div className="col-span-1">Status</div>
              <div className="col-span-3">Actions</div>
            </div>
          </div>

          {/* Table Body */}
          {members.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No se encontraron miembros</p>
              {canManage && (
                <p className="text-gray-400 mt-2">
                  Haz clic en &quot;Add Member&quot; para comenzar
                </p>
              )}
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {members.map(member => (
                <div key={member.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                  {/* Desktop Layout */}
                  <div className="hidden md:grid grid-cols-11 gap-4 items-center text-sm">
                    {/* Name */}
                    <div className="col-span-3">
                      <div className="font-medium text-gray-900">{member.name}</div>
                    </div>

                    {/* Phone */}
                    <div className="col-span-2 text-gray-600">
                      {member.phone || 'N/A'}
                    </div>

                    {/* Role */}
                    <div className="col-span-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleColor(member.role)}`}>
                        {getRoleDisplayName(member.role)}
                      </span>
                    </div>

                    {/* Status */}
                    <div className="col-span-1">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        member.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {member.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="col-span-3">
                      {canManage && (
                        <div className="flex flex-wrap gap-1">
                          <button
                            onClick={() => handleEditMember(member)}
                            className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                            title="Edit"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleResetPin(member)}
                            className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                            title="Reset PIN"
                          >
                            Reset PIN
                          </button>
                          <button
                            onClick={() => handleSuspendMember(member)}
                            className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                            title="Suspend"
                          >
                            Suspend
                          </button>
                          <button
                            onClick={() => handleRemoveMember(member)}
                            className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                            title="Remove"
                          >
                            Remove
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Mobile Layout */}
                  <div className="md:hidden">
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{member.name}</div>
                        <div className="text-sm text-gray-600">{member.phone || 'No phone'}</div>
                      </div>

                      {canManage && (
                        <div className="flex items-center space-x-1 ml-4">
                          <button
                            onClick={() => handleEditMember(member)}
                            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                            title="Edit"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleResetPin(member)}
                            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                            title="Reset PIN"
                          >
                            <RotateCcw size={16} />
                          </button>
                          <button
                            onClick={() => handleSuspendMember(member)}
                            className="p-2 text-gray-600 hover:bg-gray-50 rounded-full transition-colors"
                            title="Suspend"
                          >
                            <UserX size={16} />
                          </button>
                          <button
                            onClick={() => handleRemoveMember(member)}
                            className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                            title="Remove"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">
                  Page {searchState.page} of {totalPages}
                </p>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePageChange(searchState.page - 1)}
                    disabled={searchState.page === 1}
                    className="px-4 py-2 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(searchState.page + 1)}
                    disabled={searchState.page === totalPages}
                    className="px-4 py-2 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <ResetPinModal
        isOpen={showResetPinModal}
        onClose={() => setShowResetPinModal(false)}
        member={selectedMember}
        onSuccess={handleResetPinSuccess}
      />

      <ConfirmationModal
        isOpen={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        onConfirm={confirmationConfig?.action || (() => Promise.resolve())}
        title={confirmationConfig?.title || ''}
        message={confirmationConfig?.message || ''}
        confirmText={confirmationConfig?.confirmText || 'Confirm'}
        confirmVariant={confirmationConfig?.confirmVariant || 'primary'}
        icon={confirmationConfig?.icon || 'warning'}
      />

      {/* Admin Footer */}
      <AdminFooter currentSection="members" />
    </div>
  );
}
