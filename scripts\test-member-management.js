#!/usr/bin/env node

/**
 * Member Management Test Script for Hermanos App
 *
 * Tests the member profile management system including CRUD operations,
 * audit trail tracking, and permission validation.
 *
 * Usage: node scripts/test-member-management.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

class MemberManagementTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = [];
  }

  addTestResult(testName, success, message) {
    this.testResults.push({
      test: testName,
      success,
      message,
    });

    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  async testDatabaseSchema() {
    console.log('\n🗄️ Testing Database Schema...');

    try {
      // Test member_change_history table
      const historyCount = await this.prisma.memberChangeHistory.count();
      this.addTestResult('Member Change History Table', true, `Table exists with ${historyCount} records`);

      // Test members table relationships
      const memberCount = await this.prisma.member.count();
      this.addTestResult('Members Table', true, `Table exists with ${memberCount} records`);

      return true;
    } catch (error) {
      this.addTestResult('Database Schema', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testMemberCRUDOperations() {
    console.log('\n👥 Testing Member CRUD Operations...');

    try {
      // Get test congregation
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      if (!congregation) {
        this.addTestResult('Congregation Setup', false, 'No active congregation found');
        return false;
      }

      // Get admin user for operations
      const adminUser = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          role: { in: ['elder', 'overseer_coordinator', 'developer'] },
          isActive: true,
        },
      });

      if (!adminUser) {
        this.addTestResult('Admin User Setup', false, 'No admin user found');
        return false;
      }

      // Test member creation
      const timestamp = Date.now();
      const testMemberData = {
        name: 'Test Member',
        email: `test.member.${timestamp}@example.com`,
        role: 'publisher',
        pin: await bcrypt.hash('testpin123', 10),
      };

      const newMember = await this.prisma.member.create({
        data: {
          congregationId: congregation.id,
          ...testMemberData,
        },
      });

      this.addTestResult('Member Creation', !!newMember.id, `Created member: ${newMember.name}`);

      // Create history record for creation
      await this.prisma.memberChangeHistory.create({
        data: {
          congregationId: congregation.id,
          memberId: newMember.id,
          changedBy: adminUser.id,
          changeType: 'created',
          reason: 'Test member creation',
        },
      });

      this.addTestResult('Creation History Record', true, 'Created history record for member creation');

      // Test member update
      const updatedMember = await this.prisma.member.update({
        where: { id: newMember.id },
        data: {
          name: 'Updated Test Member',
          role: 'ministerial_servant',
        },
      });

      this.addTestResult('Member Update', updatedMember.name === 'Updated Test Member',
        `Updated member name to: ${updatedMember.name}`);

      // Create history records for updates
      await this.prisma.memberChangeHistory.createMany({
        data: [
          {
            congregationId: congregation.id,
            memberId: newMember.id,
            changedBy: adminUser.id,
            changeType: 'updated',
            fieldName: 'name',
            oldValue: testMemberData.name,
            newValue: 'Updated Test Member',
            reason: 'Test name update',
          },
          {
            congregationId: congregation.id,
            memberId: newMember.id,
            changedBy: adminUser.id,
            changeType: 'role_changed',
            fieldName: 'role',
            oldValue: 'publisher',
            newValue: 'ministerial_servant',
            reason: 'Test role change',
          },
        ],
      });

      this.addTestResult('Update History Records', true, 'Created history records for member updates');

      // Test member deactivation
      await this.prisma.member.update({
        where: { id: newMember.id },
        data: { isActive: false },
      });

      await this.prisma.memberChangeHistory.create({
        data: {
          congregationId: congregation.id,
          memberId: newMember.id,
          changedBy: adminUser.id,
          changeType: 'deactivated',
          fieldName: 'isActive',
          oldValue: 'true',
          newValue: 'false',
          reason: 'Test member deactivation',
        },
      });

      this.addTestResult('Member Deactivation', true, 'Successfully deactivated member');

      return newMember.id;
    } catch (error) {
      this.addTestResult('Member CRUD Operations', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testMemberQueries() {
    console.log('\n🔍 Testing Member Queries...');

    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Test getting all members with pagination
      const allMembers = await this.prisma.member.findMany({
        where: {
          congregationId: congregation.id,
        },
        include: {
          congregation: {
            select: {
              name: true,
            },
          },
        },
        orderBy: [
          { isActive: 'desc' },
          { role: 'desc' },
          { name: 'asc' },
        ],
        take: 10,
      });

      this.addTestResult('Member Query with Pagination', true,
        `Retrieved ${allMembers.length} members with congregation data`);

      // Test filtering by role
      const elders = await this.prisma.member.findMany({
        where: {
          congregationId: congregation.id,
          role: 'elder',
          isActive: true,
        },
      });

      this.addTestResult('Role-based Filtering', true,
        `Found ${elders.length} active elders`);

      // Test search functionality
      const searchResults = await this.prisma.member.findMany({
        where: {
          congregationId: congregation.id,
          OR: [
            { name: { contains: 'Juan', mode: 'insensitive' } },
            { email: { contains: 'juan', mode: 'insensitive' } },
          ],
        },
      });

      this.addTestResult('Search Functionality', true,
        `Search for 'Juan' returned ${searchResults.length} results`);

      return true;
    } catch (error) {
      this.addTestResult('Member Queries', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testMemberHistory() {
    console.log('\n📋 Testing Member History...');

    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Test getting member history
      const history = await this.prisma.memberChangeHistory.findMany({
        where: {
          congregationId: congregation.id,
        },
        include: {
          member: {
            select: {
              name: true,
            },
          },
          changedByMember: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 20,
      });

      this.addTestResult('Member History Query', true,
        `Retrieved ${history.length} history records`);

      // Test history filtering by change type
      const creationRecords = await this.prisma.memberChangeHistory.findMany({
        where: {
          congregationId: congregation.id,
          changeType: 'created',
        },
      });

      this.addTestResult('History Filtering by Type', true,
        `Found ${creationRecords.length} creation records`);

      // Test history for specific member
      if (history.length > 0) {
        const memberHistory = await this.prisma.memberChangeHistory.findMany({
          where: {
            congregationId: congregation.id,
            memberId: history[0].memberId,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });

        this.addTestResult('Member-specific History', true,
          `Found ${memberHistory.length} records for specific member`);
      }

      return true;
    } catch (error) {
      this.addTestResult('Member History', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testDataIntegrity() {
    console.log('\n🔒 Testing Data Integrity...');

    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Test email uniqueness within congregation
      const existingMember = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
      });

      if (existingMember) {
        try {
          await this.prisma.member.create({
            data: {
              congregationId: congregation.id,
              name: 'Duplicate Email Test',
              email: existingMember.email, // Same email
              role: 'publisher',
              pin: await bcrypt.hash('testpin', 10),
            },
          });
          this.addTestResult('Email Uniqueness', false, 'Should have failed due to duplicate email');
        } catch (error) {
          this.addTestResult('Email Uniqueness', true, 'Correctly prevented duplicate email');
        }
      }

      // Test congregation isolation
      const memberCount = await this.prisma.member.count({
        where: {
          congregationId: congregation.id,
        },
      });

      const totalMembers = await this.prisma.member.count();

      const isolationWorking = memberCount <= totalMembers;
      this.addTestResult('Congregation Isolation', isolationWorking,
        isolationWorking ? 'Congregation isolation working' : 'Potential isolation issue');

      // Test foreign key constraints
      const historyWithValidReferences = await this.prisma.memberChangeHistory.findMany({
        where: {
          congregationId: congregation.id,
        },
        include: {
          member: true,
          changedByMember: true,
        },
        take: 5,
      });

      const allReferencesValid = historyWithValidReferences.every(record =>
        record.member && record.changedByMember
      );

      this.addTestResult('Foreign Key Integrity', allReferencesValid,
        allReferencesValid ? 'All foreign key references valid' : 'Some invalid references found');

      return true;
    } catch (error) {
      this.addTestResult('Data Integrity', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testPerformance() {
    console.log('\n⚡ Testing Member Management Performance...');

    try {
      const startTime = Date.now();

      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Run complex query with joins and filtering
      await this.prisma.member.findMany({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
        include: {
          congregation: {
            select: {
              name: true,
            },
          },
        },
        orderBy: [
          { isActive: 'desc' },
          { role: 'desc' },
          { name: 'asc' },
        ],
        take: 20,
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      const isPerformant = duration < 500; // Should complete in under 500ms
      this.addTestResult('Member Query Performance', isPerformant,
        `Complex query completed in ${duration}ms ${isPerformant ? '(good)' : '(slow)'}`);

      return isPerformant;
    } catch (error) {
      this.addTestResult('Member Management Performance', false, `Error: ${error.message}`);
      return false;
    }
  }

  generateReport() {
    console.log('\n📋 Member Management Test Report');
    console.log('=================================');

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.test}: ${r.message}`));
    }

    const allPassed = failedTests === 0;
    if (allPassed) {
      console.log('\n🎉 All member management tests passed!');
      console.log('\n📝 Member management system is ready:');
      console.log('1. Start the development server: npm run dev');
      console.log('2. Login as an elder or coordinator');
      console.log('3. Go to Admin → Gestión de Miembros');
      console.log('4. Create, edit, and manage member profiles');
    } else {
      console.log('\n⚠️ Some member management tests failed!');
    }

    return allPassed;
  }

  async run() {
    try {
      console.log('🚀 Starting member management tests...');

      await this.prisma.$connect();
      console.log('✅ Database connection established');

      // Run all tests
      await this.testDatabaseSchema();
      await this.testMemberCRUDOperations();
      await this.testMemberQueries();
      await this.testMemberHistory();
      await this.testDataIntegrity();
      await this.testPerformance();

      // Generate report
      const success = this.generateReport();

      if (!success) {
        process.exit(1);
      }

    } catch (error) {
      console.error('\n💥 Test execution failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new MemberManagementTester();
  tester.run().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = MemberManagementTester;
