import { validateEnvironmentVariables, resetValidation } from '../env-validation'

describe('Environment Validation', () => {
  const originalEnv = process.env

  beforeEach(() => {
    jest.resetModules()
    process.env = { ...originalEnv }
    resetValidation()
  })

  afterAll(() => {
    process.env = originalEnv
  })

  it('should validate valid environment variables', () => {
    process.env = {
      ...originalEnv,
      DATABASE_URL: 'postgresql://user:pass@localhost:5432/hermanos',
      NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
      NEXT_PUBLIC_API_URL: 'http://localhost:3000/api',
      JWT_SECRET: 'this-is-a-very-long-secret-key-for-testing-purposes',
      NODE_ENV: 'test'
    }

    expect(() => validateEnvironmentVariables()).not.toThrow()
  })

  it('should throw error for missing required variables', () => {
    process.env = {
      ...originalEnv,
      NODE_ENV: 'test'
    }
    delete process.env.DATABASE_URL
    delete process.env.NEXT_PUBLIC_APP_URL
    delete process.env.JWT_SECRET

    expect(() => validateEnvironmentVariables()).toThrow()
  })

  it('should throw error for invalid JWT_SECRET', () => {
    process.env = {
      ...originalEnv,
      DATABASE_URL: 'postgresql://user:pass@localhost:5432/hermanos',
      NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
      NEXT_PUBLIC_API_URL: 'http://localhost:3000/api',
      JWT_SECRET: 'short', // Too short
      NODE_ENV: 'test'
    }

    expect(() => validateEnvironmentVariables()).toThrow('JWT_SECRET must be at least 32 characters')
  })

  it('should throw error for invalid URL format', () => {
    process.env = {
      ...originalEnv,
      DATABASE_URL: 'invalid-url',
      NEXT_PUBLIC_APP_URL: 'not-a-url',
      NEXT_PUBLIC_API_URL: 'http://localhost:3000/api',
      JWT_SECRET: 'this-is-a-very-long-secret-key-for-testing-purposes',
      NODE_ENV: 'test'
    }

    expect(() => validateEnvironmentVariables()).toThrow()
  })
})
