/**
 * Meeting Import Service
 * 
 * Comprehensive service for importing and validating meeting data
 * with proper error handling and data integrity checks.
 */

import { prisma } from '@/lib/prisma';
import { SongValidationService } from './songValidationService';

export interface ImportValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  validMeetings: ValidatedMeeting[];
  invalidMeetings: InvalidMeeting[];
  duplicateMeetings: DuplicateMeeting[];
  totalMeetings: number;
  validCount: number;
  invalidCount: number;
  duplicateCount: number;
}

export interface ValidatedMeeting {
  date: string;
  theme?: string;
  parts: ValidatedPart[];
  songs: number[];
  isValid: boolean;
  warnings: string[];
}

export interface ValidatedPart {
  partNumber: number;
  partType: string;
  title: string;
  timeAllocation?: number;
  bibleReading?: string;
  studyPoints?: string[];
  notes?: string;
  isValid: boolean;
  warnings: string[];
}

export interface InvalidMeeting {
  date: string;
  errors: string[];
  originalData: any;
}

export interface DuplicateMeeting {
  date: string;
  existingMeetingId: string;
  action: 'skip' | 'overwrite';
}

export interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  failed: number;
  errors: string[];
  warnings: string[];
  importedMeetings: ImportedMeeting[];
  skippedMeetings: SkippedMeeting[];
  failedMeetings: FailedMeeting[];
}

export interface ImportedMeeting {
  meetingId: string;
  date: string;
  partsCreated: number;
}

export interface SkippedMeeting {
  date: string;
  reason: string;
}

export interface FailedMeeting {
  date: string;
  error: string;
  originalData: any;
}

export class MeetingImportService {
  /**
   * Validate meeting data before import
   */
  static async validateMeetingData(
    meetings: any[],
    congregationId: string,
    overwriteExisting: boolean = false
  ): Promise<ImportValidationResult> {
    const result: ImportValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      validMeetings: [],
      invalidMeetings: [],
      duplicateMeetings: [],
      totalMeetings: meetings.length,
      validCount: 0,
      invalidCount: 0,
      duplicateCount: 0,
    };

    if (meetings.length === 0) {
      result.warnings.push('No meetings provided for validation');
      return result;
    }

    // Check for existing meetings
    const meetingDates = meetings.map(m => new Date(m.date));
    const existingMeetings = await prisma.midweekMeeting.findMany({
      where: {
        congregationId,
        meetingDate: { in: meetingDates },
      },
      select: { id: true, meetingDate: true },
    });

    const existingDatesMap = new Map(
      existingMeetings.map(m => [m.meetingDate.toISOString().split('T')[0], m.id])
    );

    // Validate each meeting
    for (const meeting of meetings) {
      try {
        const meetingDate = meeting.date;
        
        // Check for duplicates
        if (existingDatesMap.has(meetingDate)) {
          result.duplicateMeetings.push({
            date: meetingDate,
            existingMeetingId: existingDatesMap.get(meetingDate)!,
            action: overwriteExisting ? 'overwrite' : 'skip',
          });
          result.duplicateCount++;
          
          if (!overwriteExisting) {
            continue; // Skip validation for meetings that will be skipped
          }
        }

        // Validate meeting structure
        const validationResult = this.validateSingleMeeting(meeting);
        
        if (validationResult.isValid) {
          result.validMeetings.push(validationResult);
          result.validCount++;
        } else {
          result.invalidMeetings.push({
            date: meetingDate,
            errors: validationResult.warnings, // warnings contain validation errors
            originalData: meeting,
          });
          result.invalidCount++;
          result.isValid = false;
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        result.invalidMeetings.push({
          date: meeting.date || 'Unknown',
          errors: [`Validation failed: ${errorMessage}`],
          originalData: meeting,
        });
        result.invalidCount++;
        result.isValid = false;
      }
    }

    // Add summary warnings
    if (result.duplicateCount > 0) {
      result.warnings.push(
        `${result.duplicateCount} meetings already exist. ${overwriteExisting ? 'Will overwrite.' : 'Will skip.'}`
      );
    }

    if (result.invalidCount > 0) {
      result.errors.push(`${result.invalidCount} meetings failed validation`);
    }

    return result;
  }

  /**
   * Validate a single meeting
   */
  private static validateSingleMeeting(meeting: any): ValidatedMeeting {
    const result: ValidatedMeeting = {
      date: meeting.date,
      theme: meeting.theme,
      parts: [],
      songs: meeting.songs || [],
      isValid: true,
      warnings: [],
    };

    // Validate date format
    if (!meeting.date || !/^\d{4}-\d{2}-\d{2}$/.test(meeting.date)) {
      result.warnings.push('Invalid or missing date format');
      result.isValid = false;
    }

    // Validate date is not in the past (more than 1 year ago)
    const meetingDate = new Date(meeting.date);
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    
    if (meetingDate < oneYearAgo) {
      result.warnings.push('Meeting date is more than 1 year in the past');
    }

    // Validate parts
    if (!meeting.parts || !Array.isArray(meeting.parts)) {
      result.warnings.push('Missing or invalid parts array');
      result.isValid = false;
    } else {
      for (let i = 0; i < meeting.parts.length; i++) {
        const part = meeting.parts[i];
        const validatedPart = this.validateMeetingPart(part, i + 1);
        result.parts.push(validatedPart);
        
        if (!validatedPart.isValid) {
          result.isValid = false;
        }
        
        result.warnings.push(...validatedPart.warnings);
      }

      if (meeting.parts.length === 0) {
        result.warnings.push('No meeting parts found');
      }
    }

    // Validate songs
    if (meeting.songs && Array.isArray(meeting.songs)) {
      const { valid, invalid } = SongValidationService.validateSongNumberRanges(meeting.songs);
      
      if (invalid.length > 0) {
        result.warnings.push(`Invalid song numbers: ${invalid.join(', ')}`);
      }
      
      result.songs = valid;
    }

    return result;
  }

  /**
   * Validate a single meeting part
   */
  private static validateMeetingPart(part: any, partNumber: number): ValidatedPart {
    const result: ValidatedPart = {
      partNumber,
      partType: part.partType || 'general',
      title: part.title || '',
      timeAllocation: part.timeAllocation,
      bibleReading: part.bibleReading,
      studyPoints: part.studyPoints,
      notes: part.notes,
      isValid: true,
      warnings: [],
    };

    // Validate required fields
    if (!part.title || part.title.trim().length === 0) {
      result.warnings.push(`Part ${partNumber}: Missing or empty title`);
      result.isValid = false;
    }

    if (part.title && part.title.length > 255) {
      result.warnings.push(`Part ${partNumber}: Title too long (max 255 characters)`);
      result.isValid = false;
    }

    // Validate time allocation
    if (part.timeAllocation !== undefined) {
      if (!Number.isInteger(part.timeAllocation) || part.timeAllocation < 1 || part.timeAllocation > 120) {
        result.warnings.push(`Part ${partNumber}: Invalid time allocation (must be 1-120 minutes)`);
      }
    }

    // Validate part type
    const validPartTypes = ['treasures', 'digging', 'living', 'ministry', 'prayer', 'song', 'general'];
    if (!validPartTypes.includes(part.partType)) {
      result.warnings.push(`Part ${partNumber}: Unknown part type '${part.partType}'`);
    }

    // Validate study points
    if (part.studyPoints && Array.isArray(part.studyPoints)) {
      if (part.studyPoints.length > 10) {
        result.warnings.push(`Part ${partNumber}: Too many study points (max 10)`);
      }
    }

    return result;
  }

  /**
   * Import validated meetings into the database
   */
  static async importMeetings(
    validatedMeetings: ValidatedMeeting[],
    congregationId: string,
    overwriteExisting: boolean = false
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      imported: 0,
      skipped: 0,
      failed: 0,
      errors: [],
      warnings: [],
      importedMeetings: [],
      skippedMeetings: [],
      failedMeetings: [],
    };

    for (const meeting of validatedMeetings) {
      try {
        const meetingDate = new Date(meeting.date);

        // Check if meeting exists
        const existingMeeting = await prisma.midweekMeeting.findFirst({
          where: {
            congregationId,
            meetingDate,
          },
        });

        if (existingMeeting && !overwriteExisting) {
          result.skippedMeetings.push({
            date: meeting.date,
            reason: 'Meeting already exists',
          });
          result.skipped++;
          continue;
        }

        // Delete existing meeting if overwriting
        if (existingMeeting && overwriteExisting) {
          await prisma.$transaction(async (tx) => {
            await tx.midweekMeetingPart.deleteMany({
              where: { meetingId: existingMeeting.id },
            });
            await tx.midweekMeeting.delete({
              where: { id: existingMeeting.id },
            });
          });
        }

        // Import meeting in transaction
        const importedMeeting = await prisma.$transaction(async (tx) => {
          // Create meeting
          const newMeeting = await tx.midweekMeeting.create({
            data: {
              congregationId,
              meetingDate,
              theme: meeting.theme || null,
              location: 'Kingdom Hall',
              isActive: true,
            },
          });

          // Create parts
          let partsCreated = 0;
          for (let i = 0; i < meeting.parts.length; i++) {
            const part = meeting.parts[i];
            
            await tx.midweekMeetingPart.create({
              data: {
                meetingId: newMeeting.id,
                partType: part.partType,
                title: part.title,
                timeAllocation: part.timeAllocation || null,
                bibleReading: part.bibleReading || null,
                studyPoints: part.studyPoints ? part.studyPoints.join('; ') : null,
                notes: part.notes || null,
                displayOrder: i + 1,
              },
            });
            partsCreated++;
          }

          return { meetingId: newMeeting.id, partsCreated };
        });

        result.importedMeetings.push({
          meetingId: importedMeeting.meetingId,
          date: meeting.date,
          partsCreated: importedMeeting.partsCreated,
        });
        result.imported++;

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        result.failedMeetings.push({
          date: meeting.date,
          error: errorMessage,
          originalData: meeting,
        });
        result.failed++;
        result.errors.push(`Failed to import meeting ${meeting.date}: ${errorMessage}`);
      }
    }

    result.success = result.failed === 0;
    
    if (result.imported === 0 && result.skipped === 0 && result.failed === 0) {
      result.warnings.push('No meetings were processed');
    }

    return result;
  }
}
