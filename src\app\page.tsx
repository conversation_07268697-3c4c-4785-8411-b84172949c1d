'use client';

/**
 * Home Page for Hermanos App
 *
 * This page handles the initial routing logic:
 * - If user is authenticated, redirect to dashboard
 * - If user is not authenticated, redirect to login
 * - Shows a loading state while checking authentication
 */

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      try {
        // Check if user has a stored token
        const token = localStorage.getItem('hermanos_token');
        const user = localStorage.getItem('hermanos_user');

        if (token && user) {
          // Verify token is still valid by making a test API call with timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

          try {
            const response = await fetch('/api/auth/verify', {
              headers: {
                'Authorization': `Bear<PERSON> ${token}`,
              },
              signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (response.ok) {
              const data = await response.json();
              if (data.success) {
                // Token is valid, redirect to dashboard
                router.push('/dashboard');
                return;
              }
            }
          } catch (fetchError) {
            clearTimeout(timeoutId);
            console.error('Auth verification fetch error:', fetchError);
          }

          // Token is invalid or verification failed, clear storage
          localStorage.removeItem('hermanos_token');
          localStorage.removeItem('hermanos_user');
          localStorage.removeItem('hermanos_congregation');
          localStorage.removeItem('hermanos_permissions');
        }

        // No valid authentication, redirect to login
        router.push('/login');
      } catch (error) {
        console.error('Auth check error:', error);
        // On error, redirect to login
        router.push('/login');
      } finally {
        setIsLoading(false);
      }
    };

    // Add a small delay to prevent flash
    const timer = setTimeout(checkAuthAndRedirect, 100);

    return () => clearTimeout(timer);
  }, [router]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Hermanos App
            </h1>
            <p className="text-xl text-gray-600">
              Sistema de Gestión de Congregación
            </p>
          </div>

          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-500">Verificando autenticación...</p>
          </div>

          {/* Fallback link in case JavaScript fails */}
          <div className="mt-8">
            <a
              href="/login"
              className="text-blue-600 hover:text-blue-800 underline text-sm"
            >
              Ir al inicio de sesión
            </a>
          </div>
        </div>

        <div className="mt-16 text-center text-sm text-gray-400">
          <p>Hermanos App v1.0</p>
          <p>Para uso exclusivo de congregaciones de los Testigos de Jehová</p>
        </div>
      </div>
    );
  }

  // This should not be reached as we redirect in useEffect
  return null;
}
