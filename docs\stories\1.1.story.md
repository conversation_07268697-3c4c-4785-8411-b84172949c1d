# Story 1.1: Next.js Project Setup and Development Environment

**Epic:** Epic 1: Foundation & Database Migration  
**Story Points:** 8  
**Priority:** High  
**Status:** Draft  

## Story

As a developer,
I want to set up a Next.js fullstack project with PostgreSQL and Prisma,
so that I have a modern development environment for building the Hermanos app with exact UI preservation.

## Acceptance Criteria

1. **Next.js 14+ project created with latest versions of TypeScript, App Router, and Tailwind CSS**
   - Next.js 14+ project initialized with TypeScript in strict mode and App Router architecture
   - Tailwind CSS integrated with custom configuration for Hermanos app design system
   - Project structure organized with feature-based architecture and proper separation of concerns
   - Development server configured with hot reloading and fast refresh capabilities

2. **PostgreSQL database connection established with latest Prisma ORM version**
   - PostgreSQL database connection configured with connection pooling and optimization
   - Prisma ORM integrated with schema definition and migration capabilities
   - Database client generation and type safety validation implemented
   - Connection testing and health check endpoints established

3. **Development environment includes latest ESLint, Prettier for code quality**
   - ESLint configuration with Next.js, TypeScript, and React best practices
   - Prettier integration with consistent code formatting and style enforcement
   - Pre-commit hooks with <PERSON><PERSON> for automated code quality validation
   - VS Code configuration with recommended extensions and settings

4. **Complete `.env` file configuration with all ports, URLs, and environment variables**
   - Comprehensive environment variable configuration with development and production settings
   - Database connection strings with proper security and access control
   - API endpoints and service URLs configured with environment-specific values
   - Authentication secrets and JWT configuration with secure key management

5. **Never hardcode localhost, ports, or URLs in any codebase files**
   - All URLs and endpoints configured through environment variables
   - Dynamic port configuration with fallback defaults and validation
   - Base URL configuration for API calls and asset loading
   - Environment-aware configuration with development, staging, and production support

6. **Single `npm run dev` script initializes both frontend and backend using .env variables**
   - Unified development script with concurrent frontend and backend startup
   - Environment variable loading and validation during startup
   - Database migration and seeding integration with development workflow
   - Error handling and graceful startup with dependency checking

7. **Health check API endpoint using environment configuration**
   - Comprehensive health check endpoint with database connectivity validation
   - System status monitoring with environment information and dependency checking
   - API versioning and documentation with OpenAPI/Swagger integration
   - Performance monitoring and logging configuration for development and production

## Dev Notes

### Technical Architecture

**Frontend Setup:**
- Next.js 14+ with App Router and TypeScript strict mode
- Tailwind CSS with custom design system configuration
- Component library structure with UI primitives and compound components
- State management with Zustand for client-side state
- Form handling with React Hook Form and Zod validation

**Backend Setup:**
- Next.js API routes with tRPC for type-safe API development
- Prisma ORM with PostgreSQL database connection
- Authentication middleware with JWT token management
- File upload handling with local storage configuration
- Logging and monitoring with structured logging

**Database Configuration:**
- PostgreSQL with connection pooling and optimization
- Prisma schema with multi-tenant architecture support
- Migration system with version control and rollback capabilities
- Seeding system with development and test data

### Environment Configuration

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/hermanos"
DATABASE_POOL_SIZE=20

# Application URLs
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"

# Authentication
JWT_SECRET="your-secure-jwt-secret-key"
JWT_EXPIRES_IN="60d"
BCRYPT_ROUNDS=12

# File Storage
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE=10485760

# Development
NODE_ENV="development"
LOG_LEVEL="debug"

# External Services
JW_ORG_BASE_URL="https://wol.jw.org"
```

### API Endpoints (tRPC)

```typescript
// Health check and system status
system: router({
  health: publicProcedure
    .query(async ({ ctx }) => {
      const dbStatus = await checkDatabaseConnection();
      const envStatus = validateEnvironmentVariables();
      
      return {
        status: 'healthy',
        timestamp: new Date(),
        database: dbStatus,
        environment: envStatus,
        version: process.env.npm_package_version
      };
    }),

  info: publicProcedure
    .query(async () => {
      return {
        name: 'Hermanos App',
        version: process.env.npm_package_version,
        environment: process.env.NODE_ENV,
        features: ['multi-congregation', 'jw-org-integration', 'offline-support']
      };
    })
})
```

### Critical Implementation Requirements

1. **Environment-First Configuration**: All configuration through environment variables with validation
2. **Type Safety Enforcement**: TypeScript strict mode with comprehensive type checking
3. **Database-First Testing**: Real PostgreSQL database with proper connection pooling
4. **Local Infrastructure Only**: Local PostgreSQL and file storage, no cloud dependencies
5. **Performance Optimization**: Connection pooling, caching, and optimization from the start
6. **Security Foundation**: Secure defaults with proper authentication and authorization setup

### Testing Requirements

**Unit Tests:**
- Environment variable validation and loading
- Database connection and health check functionality
- API endpoint response validation
- Configuration parsing and validation

**Integration Tests:**
- Complete development environment startup
- Database connectivity and migration execution
- API endpoint accessibility and response validation
- File upload and storage functionality

**E2E Tests:**
- Full development workflow from startup to API access
- Health check endpoint validation
- Database connectivity through application layer
- Environment configuration validation

## Testing

### Test Data Requirements

- Sample environment configurations for different deployment scenarios
- Test database with basic schema and connection validation
- Mock external service responses for development environment
- Sample file upload scenarios for storage validation

### Validation Scenarios

- Test environment startup with missing or invalid environment variables
- Validate database connection with various connection string formats
- Test API endpoint accessibility and response formatting
- Verify file upload and storage functionality with various file types

## Definition of Done

- [ ] Next.js 14+ project created with TypeScript and App Router
- [ ] PostgreSQL database connection established with Prisma ORM
- [ ] Development environment includes ESLint and Prettier configuration
- [ ] Complete environment variable configuration implemented
- [ ] No hardcoded URLs or ports in codebase
- [ ] Single development script initializes frontend and backend
- [ ] Health check API endpoint functional with environment validation
- [ ] All unit tests pass with real database connection
- [ ] Integration tests validate complete development environment
- [ ] E2E tests confirm full project setup functionality
- [ ] Code review completed and approved
- [ ] Documentation updated with setup and configuration instructions

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: BMad Master Task Executor
- Date: 2025-01-24

### Debug Log References
- None yet

### Completion Notes
- Story recreated with comprehensive technical architecture
- Environment-first configuration with validation and security
- Complete project structure with feature-based organization
- Full API specification with tRPC procedures for system health
- Testing requirements defined with real database validation

### File List
- docs/stories/1.1.story.md (recreated)

### Change Log
- 2025-01-24: Story recreated with comprehensive technical specification
