/**
 * Token Verification API Endpoint
 *
 * Verifies JWT tokens and returns user information for authenticated sessions.
 */

import { NextRequest, NextResponse } from 'next/server';
import { SimpleJWTManager } from '@/lib/auth/simpleJWT';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Extract and verify token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user still exists and is active
    const member = await prisma.member.findUnique({
      where: {
        id: user.userId,
        isActive: true,
      },
      include: {
        congregation: {
          select: {
            id: true,
            name: true,
            language: true,
            timezone: true,
            isActive: true,
          },
        },
      },
    });

    if (!member || !member.congregation?.isActive) {
      return NextResponse.json(
        { error: 'User or congregation no longer active' },
        { status: 401 }
      );
    }

    // Determine permissions based on role and congregation PIN access
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    const hasSettingsAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(member.role);

    // Return user information
    return NextResponse.json({
      success: true,
      user: {
        id: member.id,
        name: member.name,
        role: member.role,
        congregationId: member.congregationId,
        congregationName: member.congregation.name,
        lastLogin: member.lastLogin,
        hasCongregationPinAccess: user.hasCongregationPinAccess || false,
      },
      congregation: {
        id: member.congregation.id,
        name: member.congregation.name,
        language: member.congregation.language,
        timezone: member.congregation.timezone,
      },
      permissions: {
        canAccessAdmin: hasAdminAccess,
        canManageSettings: hasSettingsAccess,
        hasCongregationPinAccess: user.hasCongregationPinAccess || false,
      },
      tokenInfo: {
        issuedAt: user.iat ? new Date(user.iat * 1000).toISOString() : null,
        expiresAt: user.exp ? new Date(user.exp * 1000).toISOString() : null,
      },
    });

  } catch (error) {
    console.error('Token verification error:', error);

    return NextResponse.json(
      {
        error: 'Token verification failed',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Verify the provided token
    const user = SimpleJWTManager.verifyToken(token);

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check if token needs refresh
    const refreshedToken = SimpleJWTManager.refreshTokenIfNeeded(token);

    return NextResponse.json({
      success: true,
      valid: true,
      user: {
        id: user.userId,
        name: user.name,
        role: user.role,
        congregationId: user.congregationId,
      },
      refreshedToken: refreshedToken || null,
      tokenInfo: {
        issuedAt: user.iat ? new Date(user.iat * 1000).toISOString() : null,
        expiresAt: user.exp ? new Date(user.exp * 1000).toISOString() : null,
        needsRefresh: !!refreshedToken,
      },
    });

  } catch (error) {
    console.error('Token verification error:', error);

    return NextResponse.json(
      {
        error: 'Token verification failed',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
