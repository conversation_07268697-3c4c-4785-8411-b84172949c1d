#!/usr/bin/env node

/**
 * Test Actual Coordinates
 *
 * This script will help us understand what coordinates are actually being
 * used and if there's a mismatch between expected and actual values.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Get the exact boundary data as stored in database
 */
async function getExactBoundaryData() {
  try {
    console.log('🔍 Retrieving Exact Boundary Data from Database');
    console.log('===============================================\n');

    const result = await prisma.$queryRaw`
      SELECT
        territory_number,
        boundaries
      FROM territories
      WHERE congregation_id = '1441'
      AND territory_number = '001'
    `;

    if (result.length === 0) {
      console.log('❌ No territory found');
      return null;
    }

    const territory = result[0];
    console.log('📋 Raw Database Result:');
    console.log(`   Territory Number: ${territory.territory_number}`);
    console.log(`   Boundaries Type: ${typeof territory.boundaries}`);

    if (territory.boundaries) {
      console.log('📍 Raw Boundary Data:');
      console.log(JSON.stringify(territory.boundaries, null, 2));

      // Parse and validate
      const boundary = territory.boundaries;
      if (boundary.coordinates && boundary.coordinates[0]) {
        console.log('\n📐 Parsed Coordinates:');
        boundary.coordinates[0].forEach((coord, index) => {
          const labels = ['Point 1 (NW)', 'Point 2 (NE)', 'Point 3 (SE)', 'Point 4 (SW)', 'Point 5 (Close)'];
          console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}] (lng, lat)`);
        });
      }
    }

    return territory;
  } catch (error) {
    console.error('❌ Database query error:', error);
    return null;
  }
}

/**
 * Compare with expected coordinates
 */
function compareWithExpected(actualBoundary) {
  console.log('\n🔍 Comparing with Expected Coordinates');
  console.log('======================================\n');

  const expectedCoordinates = [
    [-80.2775, 25.763],  // Northwest: NW 67 AVE & Tamiami Canal Rd
    [-80.2725, 25.763],  // Northeast: NW 65 AVE & Tamiami Canal Rd
    [-80.2725, 25.758],  // Southeast: NW 65 AVE & Low addresses
    [-80.2775, 25.758],  // Southwest: NW 67 AVE & Low addresses
    [-80.2775, 25.763]   // Close polygon
  ];

  console.log('📍 Expected Coordinates:');
  expectedCoordinates.forEach((coord, index) => {
    const labels = ['Point 1 (NW)', 'Point 2 (NE)', 'Point 3 (SE)', 'Point 4 (SW)', 'Point 5 (Close)'];
    console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}] (lng, lat)`);
  });

  if (actualBoundary && actualBoundary.coordinates && actualBoundary.coordinates[0]) {
    console.log('\n📍 Actual Coordinates:');
    const actualCoords = actualBoundary.coordinates[0];
    actualCoords.forEach((coord, index) => {
      const labels = ['Point 1 (NW)', 'Point 2 (NE)', 'Point 3 (SE)', 'Point 4 (SW)', 'Point 5 (Close)'];
      console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}] (lng, lat)`);
    });

    console.log('\n🔍 Differences:');
    for (let i = 0; i < Math.min(expectedCoordinates.length, actualCoords.length); i++) {
      const expected = expectedCoordinates[i];
      const actual = actualCoords[i];
      const lngDiff = actual[0] - expected[0];
      const latDiff = actual[1] - expected[1];

      const labels = ['Point 1 (NW)', 'Point 2 (NE)', 'Point 3 (SE)', 'Point 4 (SW)', 'Point 5 (Close)'];

      if (Math.abs(lngDiff) > 0.000001 || Math.abs(latDiff) > 0.000001) {
        console.log(`   ${labels[i]}: Lng diff: ${lngDiff.toFixed(6)}, Lat diff: ${latDiff.toFixed(6)} ❌`);
      } else {
        console.log(`   ${labels[i]}: No difference ✅`);
      }
    }
  }
}

/**
 * Create a corrected boundary if needed
 */
function createCorrectedBoundary() {
  console.log('\n🛠️  Creating Corrected Boundary');
  console.log('===============================\n');

  // Based on the user's feedback that the map coordinates are different,
  // let me create a boundary that might be more accurate for the actual territory

  // Let's try coordinates that are slightly adjusted based on real Miami geography
  const correctedBoundary = {
    type: 'Polygon',
    coordinates: [[
      [-80.2780, 25.7635],  // Northwest: Slightly more west and north
      [-80.2720, 25.7635],  // Northeast: Slightly more east and north
      [-80.2720, 25.7575],  // Southeast: Slightly more east and south
      [-80.2780, 25.7575],  // Southwest: Slightly more west and south
      [-80.2780, 25.7635]   // Close polygon
    ]]
  };

  console.log('📍 Corrected Boundary Coordinates:');
  correctedBoundary.coordinates[0].forEach((coord, index) => {
    const labels = ['Point 1 (NW)', 'Point 2 (NE)', 'Point 3 (SE)', 'Point 4 (SW)', 'Point 5 (Close)'];
    console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}] (lng, lat)`);
  });

  // Calculate center
  const coords = correctedBoundary.coordinates[0].slice(0, -1); // Exclude duplicate last point
  const centerLat = coords.reduce((sum, coord) => sum + coord[1], 0) / coords.length;
  const centerLng = coords.reduce((sum, coord) => sum + coord[0], 0) / coords.length;

  console.log('\n📍 Corrected Boundary Center:');
  console.log(`   Latitude: ${centerLat}`);
  console.log(`   Longitude: ${centerLng}`);

  // Calculate dimensions
  const lats = coords.map(c => c[1]);
  const lngs = coords.map(c => c[0]);
  const latSpan = Math.max(...lats) - Math.min(...lats);
  const lngSpan = Math.max(...lngs) - Math.min(...lngs);

  console.log('\n📏 Corrected Boundary Dimensions:');
  console.log(`   Latitude span: ${latSpan.toFixed(6)} degrees (~${(latSpan * 69).toFixed(2)} miles)`);
  console.log(`   Longitude span: ${lngSpan.toFixed(6)} degrees (~${(lngSpan * 54.6).toFixed(2)} miles)`);

  return correctedBoundary;
}

/**
 * Update boundary in database
 */
async function updateBoundaryInDatabase(newBoundary) {
  try {
    console.log('\n💾 Updating Boundary in Database');
    console.log('=================================\n');

    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      }
    });

    if (!territory) {
      console.log('❌ Territory 001 not found');
      return false;
    }

    await prisma.territory.update({
      where: { id: territory.id },
      data: {
        boundaries: newBoundary
      }
    });

    console.log('✅ Boundary updated successfully');
    console.log('📍 New boundary coordinates saved to database');

    return true;
  } catch (error) {
    console.error('❌ Update error:', error);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  const command = process.argv[2];

  console.log('🔧 Territory 001 Coordinate Testing & Correction');
  console.log('================================================\n');

  try {
    // Get exact boundary data
    const actualBoundary = await getExactBoundaryData();
    if (!actualBoundary) return;

    // Compare with expected
    compareWithExpected(actualBoundary.boundaries);

    if (command === 'correct') {
      // Create corrected boundary
      const correctedBoundary = createCorrectedBoundary();

      // Ask for confirmation (in real scenario)
      console.log('\n❓ Would you like to update the boundary with corrected coordinates?');
      console.log('   Run: node scripts/test-actual-coordinates.js update');

    } else if (command === 'update') {
      const correctedBoundary = createCorrectedBoundary();
      const success = await updateBoundaryInDatabase(correctedBoundary);

      if (success) {
        console.log('\n🎉 Boundary correction completed!');
        console.log('   Please test the map display to verify the boundary is now accurate.');
      }
    } else {
      console.log('\n🎯 Next Steps:');
      console.log('==============');
      console.log('1. Review the coordinate comparison above');
      console.log('2. If coordinates match but map display is wrong, check map rendering');
      console.log('3. If coordinates need correction, run: node scripts/test-actual-coordinates.js correct');
      console.log('4. To apply corrections, run: node scripts/test-actual-coordinates.js update');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  getExactBoundaryData,
  compareWithExpected,
  createCorrectedBoundary,
  updateBoundaryInDatabase
};
