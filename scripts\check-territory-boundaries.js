#!/usr/bin/env node

/**
 * Check Territory Boundaries Script
 * 
 * This script checks what boundary data exists in the territories table
 * and helps diagnose why boundaries aren't displaying on maps.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTerritoryBoundaries() {
  try {
    console.log('🔍 Checking territory boundary data...\n');

    // Get all territories with their boundary data
    const territories = await prisma.territory.findMany({
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        boundaries: true,
        status: true
      },
      orderBy: {
        territoryNumber: 'asc'
      },
      take: 10 // Limit to first 10 for testing
    });

    console.log(`📊 Found ${territories.length} territories\n`);

    territories.forEach((territory, index) => {
      console.log(`${index + 1}. Territory ${territory.territoryNumber}`);
      console.log(`   ID: ${territory.id}`);
      console.log(`   Status: ${territory.status}`);
      console.log(`   Address: ${territory.address.substring(0, 50)}...`);
      
      if (territory.boundaries) {
        console.log(`   ✅ Has boundary data: ${typeof territory.boundaries}`);
        
        // Try to parse the boundary data
        try {
          const boundaryData = typeof territory.boundaries === 'string' 
            ? JSON.parse(territory.boundaries) 
            : territory.boundaries;
          
          console.log(`   📐 Boundary type: ${boundaryData.type || 'Unknown'}`);
          
          if (boundaryData.coordinates) {
            console.log(`   📍 Coordinates: ${Array.isArray(boundaryData.coordinates) ? 'Array' : typeof boundaryData.coordinates}`);
            if (Array.isArray(boundaryData.coordinates) && boundaryData.coordinates.length > 0) {
              console.log(`   📏 Coordinate points: ${boundaryData.coordinates[0]?.length || 'Unknown'}`);
            }
          } else {
            console.log(`   ❌ No coordinates in boundary data`);
          }
        } catch (parseError) {
          console.log(`   ❌ Error parsing boundary data: ${parseError.message}`);
        }
      } else {
        console.log(`   ❌ No boundary data`);
      }
      
      console.log(''); // Empty line for readability
    });

    // Check if any territories have boundary data
    const territoriesWithBoundaries = territories.filter(t => t.boundaries);
    console.log(`\n📈 Summary:`);
    console.log(`   Total territories: ${territories.length}`);
    console.log(`   With boundaries: ${territoriesWithBoundaries.length}`);
    console.log(`   Without boundaries: ${territories.length - territoriesWithBoundaries.length}`);

    if (territoriesWithBoundaries.length > 0) {
      console.log(`\n🎯 Sample boundary data from Territory ${territoriesWithBoundaries[0].territoryNumber}:`);
      try {
        const sampleBoundary = typeof territoriesWithBoundaries[0].boundaries === 'string'
          ? JSON.parse(territoriesWithBoundaries[0].boundaries)
          : territoriesWithBoundaries[0].boundaries;
        console.log(JSON.stringify(sampleBoundary, null, 2));
      } catch (error) {
        console.log(`   ❌ Error displaying sample: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Error checking territory boundaries:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkTerritoryBoundaries();
