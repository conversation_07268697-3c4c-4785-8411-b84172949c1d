import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyToken } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { congregationId } = authResult;

    // Get active territory assignments
    const assignments = await prisma.territoryAssignment.findMany({
      where: {
        congregationId: congregationId,
        status: 'active'
      },
      include: {
        territory: {
          select: {
            territoryNumber: true,
            address: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      },
      orderBy: [
        { territory: { territoryNumber: 'asc' } }
      ]
    });

    // Format the data for the report
    const reportData = assignments.map(assignment => {
      const assignedDate = new Date(assignment.assignedAt);
      const today = new Date();
      const daysAssigned = Math.floor((today.getTime() - assignedDate.getTime()) / (1000 * 60 * 60 * 24));

      return {
        territoryNumber: assignment.territory.territoryNumber,
        address: assignment.territory.address,
        assignedTo: assignment.member.name,
        assignedDate: assignment.assignedAt.toISOString(),
        daysAssigned: daysAssigned,
        status: assignment.status
      };
    });

    return NextResponse.json(reportData);

  } catch (error) {
    console.error('Error fetching assignment report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
