// next.config.js - Optimized for congregation management
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost'], // Local image optimization
    formats: ['image/webp', 'image/avif'], // Modern image formats
  },
  compress: true, // Enable gzip compression
  poweredByHeader: false, // Remove X-Powered-By header for security
  reactStrictMode: true, // Enable React strict mode
  swcMinify: true, // Use SWC for faster builds
};

module.exports = nextConfig;
