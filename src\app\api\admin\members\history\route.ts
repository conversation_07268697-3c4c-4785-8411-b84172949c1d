/**
 * Member History API Endpoint
 * 
 * Provides access to member change history and audit trail.
 * Only accessible to elders and coordinators with proper permissions.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { MemberManagementService } from '@/lib/services/memberManagementService';

/**
 * GET - Retrieve member change history
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view member history' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('memberId') || undefined;
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : 50;

    // Validate limit
    if (isNaN(limit) || limit < 1 || limit > 200) {
      return NextResponse.json(
        { error: 'Invalid limit parameter. Must be between 1 and 200.' },
        { status: 400 }
      );
    }

    // Get member history
    const history = await MemberManagementService.getMemberHistory(
      user.congregationId,
      memberId,
      limit
    );

    return NextResponse.json({
      success: true,
      history,
      count: history.length,
      limit,
      memberId: memberId || null,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Member history GET error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch member history',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
