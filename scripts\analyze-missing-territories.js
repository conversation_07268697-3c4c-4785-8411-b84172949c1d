const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

async function analyzeMissingTerritories() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  
  // Missing territories that couldn't be imported
  const missingTerritories = ['014', '027', '032', '038', '040', '045', '046', '047', '051', '053', '055', '058', '065', '068', '069', '074', '075', '082', '086', '096', '098', '102', '103'];
  
  console.log(`🔍 Analyzing ${missingTerritories.length} missing territories...\n`);
  
  for (const territoryNum of missingTerritories.slice(0, 5)) { // Analyze first 5
    const fileName = `Terr. ${territoryNum}.xlsx`;
    const filePath = path.join(territoriosDir, fileName);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ File not found: ${fileName}`);
      continue;
    }
    
    try {
      console.log(`📋 Territory ${territoryNum} (${fileName}):`);
      
      const workbook = XLSX.readFile(filePath);
      const sheetNames = workbook.SheetNames;
      console.log(`  Sheets: ${sheetNames.join(', ')}`);
      
      // Analyze each sheet
      for (const sheetName of sheetNames) {
        if (sheetName.toLowerCase().includes('mapa')) continue;
        
        console.log(`\n  📄 Sheet: ${sheetName}`);
        const sheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
        
        console.log(`    Rows: ${data.length}`);
        
        // Show first 10 non-empty rows
        let rowCount = 0;
        for (let i = 0; i < Math.min(data.length, 20) && rowCount < 10; i++) {
          const row = data[i];
          if (row && row.length > 0 && row.some(cell => cell && cell.toString().trim())) {
            const rowContent = row.slice(0, 6).map(cell => cell ? `"${cell}"` : '""').join(', ');
            console.log(`    Row ${i + 1}: [${rowContent}]`);
            rowCount++;
          }
        }
        
        // Look for potential patterns
        console.log(`\n    Pattern Analysis:`);
        
        // Check for different address patterns
        let streetPatterns = 0;
        let numberPatterns = 0;
        let addressPatterns = 0;
        
        for (let i = 0; i < data.length; i++) {
          const row = data[i];
          if (!row || row.length === 0) continue;
          
          for (let j = 0; j < row.length; j++) {
            const cell = row[j];
            if (!cell) continue;
            
            const cellStr = cell.toString().trim();
            
            // Street patterns
            if (/\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(cellStr)) {
              streetPatterns++;
            }
            
            // Number patterns
            if (/^\d+[A-Z]?$/i.test(cellStr) && cellStr.length < 6) {
              numberPatterns++;
            }
            
            // Full address patterns
            if (/^\d+\s+[A-Z]/.test(cellStr) || /\d+.*Miami.*FL/i.test(cellStr)) {
              addressPatterns++;
            }
          }
        }
        
        console.log(`    - Street patterns found: ${streetPatterns}`);
        console.log(`    - Number patterns found: ${numberPatterns}`);
        console.log(`    - Full address patterns found: ${addressPatterns}`);
        
        if (streetPatterns === 0 && numberPatterns === 0 && addressPatterns === 0) {
          console.log(`    ⚠️  No recognizable address patterns found`);
        }
      }
      
      console.log('\n' + '='.repeat(50) + '\n');
      
    } catch (error) {
      console.log(`  ❌ Error reading file: ${error.message}\n`);
    }
  }
}

analyzeMissingTerritories().catch(console.error);
