#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

async function cleanupCongregations() {
  const prisma = new PrismaClient();

  try {
    console.log('🧹 Cleaning up congregations - keeping only Coral Oeste (1441)...');

    // Keep only congregation 1441 (Coral Oeste)
    const keepCongregation = '1441';

    // Get all congregations
    const allCongregations = await prisma.congregation.findMany();
    console.log('📋 Current congregations:');
    allCongregations.forEach(cong => {
      console.log(`  - ${cong.id}: ${cong.name} ${cong.id === keepCongregation ? '✅ KEEP' : '❌ DELETE'}`);
    });

    // Get congregations to delete
    const congregationsToDelete = allCongregations.filter(c => c.id !== keepCongregation);

    if (congregationsToDelete.length === 0) {
      console.log('✅ Database already clean - only Coral Oeste (1441) exists');

      // Verify the correct congregation
      const coralOeste = await prisma.congregation.findFirst({
        where: { id: keepCongregation }
      });

      if (coralOeste) {
        console.log(`✅ Coral Oeste found: ${coralOeste.name} (ID: ${coralOeste.id})`);

        const memberCount = await prisma.member.count({
          where: { congregationId: keepCongregation, isActive: true }
        });
        console.log(`👥 Active members: ${memberCount}`);

        // Check admin users
        const adminRoles = ['elder', 'overseer_coordinator', 'developer', 'ministerial_servant'];
        const adminCount = await prisma.member.count({
          where: {
            congregationId: keepCongregation,
            isActive: true,
            role: { in: adminRoles }
          }
        });
        console.log(`👑 Admin users: ${adminCount}`);
      }

      return;
    }

    console.log(`\n🗑️ Deleting ${congregationsToDelete.length} incorrect congregations:`);

    // Delete members from other congregations first
    for (const cong of congregationsToDelete) {
      const memberCount = await prisma.member.count({
        where: { congregationId: cong.id }
      });

      if (memberCount > 0) {
        console.log(`  📤 Deleting ${memberCount} members from ${cong.id} (${cong.name})`);
        await prisma.member.deleteMany({
          where: { congregationId: cong.id }
        });
      }
    }

    // Delete the incorrect congregations
    const deleteResult = await prisma.congregation.deleteMany({
      where: {
        id: {
          not: keepCongregation
        }
      }
    });

    console.log(`✅ Deleted ${deleteResult.count} incorrect congregations`);

    // Verify final state
    const finalCongregations = await prisma.congregation.findMany();
    console.log('\n📊 Final database state:');

    if (finalCongregations.length === 1 && finalCongregations[0].id === keepCongregation) {
      const cong = finalCongregations[0];
      console.log(`  ✅ ${cong.id}: ${cong.name} (CORRECT)`);

      const memberCount = await prisma.member.count({
        where: { congregationId: keepCongregation, isActive: true }
      });
      console.log(`  👥 Active members: ${memberCount}`);

      // Check admin users
      const adminRoles = ['elder', 'overseer_coordinator', 'developer', 'ministerial_servant'];
      const adminCount = await prisma.member.count({
        where: {
          congregationId: keepCongregation,
          isActive: true,
          role: { in: adminRoles }
        }
      });
      console.log(`  👑 Admin users: ${adminCount}`);

    } else {
      console.log('❌ ERROR: Database state is incorrect after cleanup');
    }

    console.log('\n🎉 Database cleanup completed!');

  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupCongregations();
