/**
 * Permission Checker Utility
 *
 * Integrates delegated permissions with the existing RBAC system.
 * Checks both role-based permissions and delegated section permissions.
 */

import { prisma } from '@/lib/prisma';
import { ROLES, PERMISSIONS, hasPermission as hasRolePermission } from './simpleRBAC';
import { ADMINISTRATIVE_SECTIONS } from '@/lib/constants/administrativeSections';

export interface UserPermissionContext {
  userId: string;
  role: ROLES;
  congregationId: string;
  hasCongregationPinAccess?: boolean;
}

export interface DelegatedPermission {
  sectionId: string;
  permissions: string[];
  isActive: boolean;
  expirationDate?: Date;
}

export class PermissionChecker {
  /**
   * Check if user has a specific permission (role-based, delegated, or congregation PIN access)
   */
  static async hasPermission(
    context: UserPermissionContext,
    permission: PERMISSIONS,
    sectionId?: string
  ): Promise<boolean> {
    // If user has congregation PIN access, they have all permissions
    if (context.hasCongregationPinAccess) {
      return true;
    }

    // Check role-based permissions
    if (hasRolePermission(context.role, permission)) {
      return true;
    }

    // If no section specified, only role-based permissions apply
    if (!sectionId) {
      return false;
    }

    // Check delegated permissions for the specific section
    const delegatedPermissions = await this.getDelegatedPermissions(
      context.userId,
      context.congregationId
    );

    const sectionPermissions = delegatedPermissions.find(
      dp => dp.sectionId === sectionId && dp.isActive
    );

    if (!sectionPermissions) {
      return false;
    }

    // Map PERMISSIONS enum to delegated permission strings
    const permissionMapping = this.getPermissionMapping();
    const delegatedPermissionName = permissionMapping[permission];

    if (!delegatedPermissionName) {
      return false;
    }

    return sectionPermissions.permissions.includes(delegatedPermissionName);
  }

  /**
   * Check if user can access admin panel
   */
  static async canAccessAdmin(context: UserPermissionContext): Promise<boolean> {
    // If user has congregation PIN access, they have full admin access
    if (context.hasCongregationPinAccess) {
      return true;
    }

    // Check role-based admin access
    if (hasRolePermission(context.role, PERMISSIONS.VIEW_ADMIN)) {
      return true;
    }

    // Check if user has any delegated permissions
    const delegatedPermissions = await this.getDelegatedPermissions(
      context.userId,
      context.congregationId
    );

    return delegatedPermissions.some(dp => dp.isActive && dp.permissions.length > 0);
  }

  /**
   * Get all sections user can access (role-based + delegated + congregation PIN)
   */
  static async getAccessibleSections(
    context: UserPermissionContext
  ): Promise<string[]> {
    const accessibleSections = new Set<string>();

    // If user has congregation PIN access, they can access all sections
    if (context.hasCongregationPinAccess) {
      Object.values(ADMINISTRATIVE_SECTIONS).forEach(section => {
        accessibleSections.add(section);
      });
      return Array.from(accessibleSections);
    }

    // Add sections based on role permissions
    if (hasRolePermission(context.role, PERMISSIONS.VIEW_ADMIN)) {
      // Full admin access - can access all sections
      Object.values(ADMINISTRATIVE_SECTIONS).forEach(section => {
        accessibleSections.add(section);
      });
    } else {
      // Check specific role permissions
      if (hasRolePermission(context.role, PERMISSIONS.MANAGE_MEETINGS)) {
        accessibleSections.add(ADMINISTRATIVE_SECTIONS.MEETINGS);
      }
      if (hasRolePermission(context.role, PERMISSIONS.MANAGE_TASKS)) {
        accessibleSections.add(ADMINISTRATIVE_SECTIONS.TASKS);
      }
      if (hasRolePermission(context.role, PERMISSIONS.MANAGE_LETTERS)) {
        accessibleSections.add(ADMINISTRATIVE_SECTIONS.LETTERS);
      }
      if (hasRolePermission(context.role, PERMISSIONS.MANAGE_EVENTS)) {
        accessibleSections.add(ADMINISTRATIVE_SECTIONS.EVENTS);
      }
      if (hasRolePermission(context.role, PERMISSIONS.MANAGE_FIELD_SERVICE_RECORDS)) {
        accessibleSections.add(ADMINISTRATIVE_SECTIONS.FIELD_SERVICE);
      }
    }

    // Add sections from delegated permissions
    const delegatedPermissions = await this.getDelegatedPermissions(
      context.userId,
      context.congregationId
    );

    delegatedPermissions
      .filter(dp => dp.isActive)
      .forEach(dp => {
        accessibleSections.add(dp.sectionId);
      });

    return Array.from(accessibleSections);
  }

  /**
   * Get user's permissions for a specific section
   */
  static async getSectionPermissions(
    context: UserPermissionContext,
    sectionId: string
  ): Promise<string[]> {
    const permissions = new Set<string>();

    // Add role-based permissions
    const rolePermissions = this.getRolePermissionsForSection(context.role, sectionId);
    rolePermissions.forEach(p => permissions.add(p));

    // Add delegated permissions
    const delegatedPermissions = await this.getDelegatedPermissions(
      context.userId,
      context.congregationId
    );

    const sectionPermissions = delegatedPermissions.find(
      dp => dp.sectionId === sectionId && dp.isActive
    );

    if (sectionPermissions) {
      sectionPermissions.permissions.forEach(p => permissions.add(p));
    }

    return Array.from(permissions);
  }

  /**
   * Check if user can delegate permissions (coordinators or congregation PIN holders)
   */
  static canDelegatePermissions(context: UserPermissionContext): boolean {
    return context.role === ROLES.COORDINATOR || context.hasCongregationPinAccess === true;
  }

  /**
   * Get delegated permissions from database
   */
  private static async getDelegatedPermissions(
    userId: string,
    congregationId: string
  ): Promise<DelegatedPermission[]> {
    const permissions = await prisma.elderPermission.findMany({
      where: {
        memberId: userId,
        congregationId,
        isActive: true,
        OR: [
          { expirationDate: null },
          { expirationDate: { gt: new Date() } },
        ],
      },
      select: {
        sectionId: true,
        permissions: true,
        isActive: true,
        expirationDate: true,
      },
    });

    return permissions.map(p => ({
      sectionId: p.sectionId,
      permissions: p.permissions as string[],
      isActive: p.isActive,
      expirationDate: p.expirationDate || undefined,
    }));
  }

  /**
   * Get role-based permissions for a specific section
   */
  private static getRolePermissionsForSection(role: ROLES, sectionId: string): string[] {
    const permissions: string[] = [];

    // Map role permissions to section-specific permissions
    switch (sectionId) {
      case ADMINISTRATIVE_SECTIONS.MEETINGS:
        if (hasRolePermission(role, PERMISSIONS.MANAGE_MEETINGS)) {
          permissions.push('view', 'edit', 'assign', 'schedule');
        }
        break;

      case ADMINISTRATIVE_SECTIONS.TASKS:
        if (hasRolePermission(role, PERMISSIONS.MANAGE_TASKS)) {
          permissions.push('view', 'edit', 'assign', 'create');
        }
        break;

      case ADMINISTRATIVE_SECTIONS.LETTERS:
        if (hasRolePermission(role, PERMISSIONS.MANAGE_LETTERS)) {
          permissions.push('view', 'edit', 'upload', 'approve');
        }
        break;

      case ADMINISTRATIVE_SECTIONS.EVENTS:
        if (hasRolePermission(role, PERMISSIONS.MANAGE_EVENTS)) {
          permissions.push('view', 'edit', 'create', 'cancel');
        }
        break;

      case ADMINISTRATIVE_SECTIONS.FIELD_SERVICE:
        if (hasRolePermission(role, PERMISSIONS.MANAGE_FIELD_SERVICE_RECORDS)) {
          permissions.push('view', 'edit', 'coordinate', 'assign_territories');
        }
        break;
    }

    return permissions;
  }

  /**
   * Map PERMISSIONS enum to delegated permission strings
   */
  private static getPermissionMapping(): Record<PERMISSIONS, string> {
    return {
      [PERMISSIONS.VIEW_DASHBOARD]: 'view',
      [PERMISSIONS.VIEW_PROFILE]: 'view_profile',
      [PERMISSIONS.EDIT_PROFILE]: 'edit_profile',
      [PERMISSIONS.VIEW_ADMIN]: 'view_admin',
      [PERMISSIONS.MANAGE_MEMBERS]: 'manage_members',
      [PERMISSIONS.MANAGE_TASKS]: 'manage_tasks',
      [PERMISSIONS.MANAGE_MEETINGS]: 'manage_meetings',
      [PERMISSIONS.MANAGE_LETTERS]: 'manage_letters',
      [PERMISSIONS.MANAGE_EVENTS]: 'manage_events',
      [PERMISSIONS.MANAGE_CONGREGATION_SETTINGS]: 'manage_congregation_settings',
      [PERMISSIONS.MANAGE_PERMISSIONS]: 'manage_permissions',
      [PERMISSIONS.VIEW_FIELD_SERVICE_RECORDS]: 'view_field_service_records',
      [PERMISSIONS.MANAGE_FIELD_SERVICE_RECORDS]: 'manage_field_service_records',
      [PERMISSIONS.MANAGE_DATABASE]: 'manage_database',
      [PERMISSIONS.VIEW_SYSTEM_LOGS]: 'view_system_logs',
      [PERMISSIONS.MANAGE_SYSTEM_SETTINGS]: 'manage_system_settings',
    };
  }
}
