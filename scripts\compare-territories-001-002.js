const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function compareTerritories() {
  try {
    console.log('🔍 Comparing Territories 001 and 002');
    console.log('====================================\n');

    const territories = await prisma.territory.findMany({
      where: {
        congregationId: '1441',
        territoryNumber: { in: ['001', '002'] }
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        boundaries: true
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });

    territories.forEach(territory => {
      console.log(`📍 Territory ${territory.territoryNumber} (ID: ${territory.id})`);
      
      // Address info
      const addresses = territory.address.split('\n').filter(addr => addr.trim());
      console.log(`   📋 Addresses: ${addresses.length}`);
      console.log(`   📍 First address: ${addresses[0]}`);

      // Boundary info
      if (territory.boundaries) {
        console.log('   ✅ Has boundary data');
        console.log(`   📐 Type: ${territory.boundaries.type}`);
        console.log(`   📍 Points: ${territory.boundaries.coordinates[0].length}`);
        
        const coords = territory.boundaries.coordinates[0];
        console.log('   📍 Coordinates:');
        console.log(`      Northwest: [${coords[0][0]}, ${coords[0][1]}]`);
        console.log(`      Northeast: [${coords[1][0]}, ${coords[1][1]}]`);
        console.log(`      Southeast: [${coords[2][0]}, ${coords[2][1]}]`);
        console.log(`      Southwest: [${coords[3][0]}, ${coords[3][1]}]`);
        
        // Calculate center
        const centerCoords = coords.slice(0, -1);
        const centerLat = centerCoords.reduce((sum, c) => sum + c[1], 0) / centerCoords.length;
        const centerLng = centerCoords.reduce((sum, c) => sum + c[0], 0) / centerCoords.length;
        
        console.log(`   📍 Center: [${centerLng}, ${centerLat}]`);
        
        // Calculate dimensions
        const lats = centerCoords.map(c => c[1]);
        const lngs = centerCoords.map(c => c[0]);
        const latSpan = Math.max(...lats) - Math.min(...lats);
        const lngSpan = Math.max(...lngs) - Math.min(...lngs);
        
        console.log(`   📏 Dimensions: ${lngSpan.toFixed(4)} lng × ${latSpan.toFixed(4)} lat`);
        
      } else {
        console.log('   ❌ No boundary data');
      }
      console.log('');
    });

    // Check for potential racing logic issues
    console.log('🔍 Potential Racing Logic Issues:');
    console.log('==================================');
    
    const territory001 = territories.find(t => t.territoryNumber === '001');
    const territory002 = territories.find(t => t.territoryNumber === '002');
    
    if (territory001?.boundaries && territory002?.boundaries) {
      const coords001 = territory001.boundaries.coordinates[0];
      const coords002 = territory002.boundaries.coordinates[0];
      
      console.log('\n📊 Coordinate Comparison:');
      console.log('Territory 001 vs Territory 002');
      console.log('------------------------------');
      
      for (let i = 0; i < 4; i++) {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest'];
        const coord001 = coords001[i];
        const coord002 = coords002[i];
        
        console.log(`${labels[i]}:`);
        console.log(`  T001: [${coord001[0]}, ${coord001[1]}]`);
        console.log(`  T002: [${coord002[0]}, ${coord002[1]}]`);
        
        const lngDiff = Math.abs(coord001[0] - coord002[0]);
        const latDiff = Math.abs(coord001[1] - coord002[1]);
        
        if (lngDiff < 0.001 && latDiff < 0.001) {
          console.log(`  ⚠️  Very similar coordinates - potential duplicate logic`);
        }
        console.log('');
      }
      
      // Check if boundaries are identical
      const identical = JSON.stringify(coords001) === JSON.stringify(coords002);
      if (identical) {
        console.log('🚨 IDENTICAL BOUNDARIES DETECTED!');
        console.log('   This indicates a racing logic issue where both territories');
        console.log('   are getting the same boundary data.');
      } else {
        console.log('✅ Boundaries are different - no racing logic detected');
      }
      
    } else {
      console.log('❌ Cannot compare - one or both territories missing boundary data');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

compareTerritories();
