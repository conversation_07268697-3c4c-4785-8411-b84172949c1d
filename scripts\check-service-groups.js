const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkServiceGroups() {
  try {
    console.log('Checking service groups in database...');

    // Check if service_groups table exists and get all groups for congregation 1441
    const serviceGroups = await prisma.serviceGroup.findMany({
      where: {
        congregationId: '1441'
      },
      include: {
        overseer: {
          select: {
            id: true,
            name: true,
            role: true
          }
        },
        assistant: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      },
      orderBy: {
        groupNumber: 'asc'
      }
    });

    console.log(`\nFound ${serviceGroups.length} service groups in database for congregation 1441:`);

    if (serviceGroups.length === 0) {
      console.log('No service groups found in database!');
      console.log('This explains why only 2 hardcoded groups are showing.');

      console.log('\nCreating sample service groups...');

      // Get some members to assign as conductors
      const members = await prisma.member.findMany({
        where: {
          congregationId: '1441',
          isActive: true,
          role: {
            in: ['elder', 'ministerial_servant']
          }
        },
        take: 7
      });

      console.log(`Found ${members.length} potential conductors`);

      // Create 7 service groups
      const groupsToCreate = [];
      for (let i = 1; i <= 7; i++) {
        const overseer = members[i - 1]; // Assign available members as overseers
        groupsToCreate.push({
          congregationId: '1441',
          name: `Grupo ${i}`,
          groupNumber: i,
          overseerId: overseer?.id || null,
          assistantId: null, // Can be assigned later
          address: `Dirección del Grupo ${i}`,
          isActive: true
        });
      }

      // Create the groups
      for (const group of groupsToCreate) {
        const created = await prisma.serviceGroup.create({
          data: group,
          include: {
            overseer: {
              select: { name: true, role: true }
            }
          }
        });
        console.log(`✅ Created Group ${created.groupNumber} with overseer: ${created.overseer?.name || 'Sin asignar'}`);
      }

      console.log('\n✅ Service groups created successfully!');
    } else {
      console.log('✅ Service groups found:');
      serviceGroups.forEach(group => {
        console.log(`- Group ${group.groupNumber}: Overseer: ${group.overseer?.name || 'Sin asignar'}, Assistant: ${group.assistant?.name || 'Sin asignar'}, Address: ${group.address}`);
      });
    }

    // Also check if the API is fetching correctly
    console.log('\n--- Checking API endpoint ---');
    console.log('The frontend should be calling: GET /api/admin/service-groups');
    console.log('Make sure this endpoint exists and is working properly.');

  } catch (error) {
    console.error('❌ Error checking service groups:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkServiceGroups();
