#!/usr/bin/env node

/**
 * Test Script: Pixel-Perfect Dashboard Fixes Verification
 * 
 * This script verifies that all four critical UI fixes have been implemented correctly:
 * 1. Admin button styling (white background with blue icon)
 * 2. Layout spacing and density (more compact)
 * 3. Bottom navigation styling (cleaner appearance)
 * 4. Visual hierarchy fine-tuning (better proportions)
 */

const puppeteer = require('puppeteer');

class PixelPerfectFixesTest {
  constructor() {
    this.testResults = {
      adminButtonFixed: false,
      spacingImproved: false,
      bottomNavFixed: false,
      visualHierarchyImproved: false,
      errors: []
    };
  }

  async runTests() {
    console.log('🎯 PIXEL-PERFECT DASHBOARD FIXES VERIFICATION');
    console.log('='.repeat(50));
    
    let browser;
    try {
      browser = await puppeteer.launch({ 
        headless: false,
        defaultViewport: { width: 375, height: 812 } // iPhone X dimensions
      });
      
      const page = await browser.newPage();
      
      // Test 1: Admin Button Styling
      console.log('\n1️⃣ Testing Admin Button Styling...');
      await this.testAdminButtonStyling(page);
      
      // Test 2: Layout Spacing and Density
      console.log('\n2️⃣ Testing Layout Spacing and Density...');
      await this.testLayoutSpacing(page);
      
      // Test 3: Bottom Navigation Styling
      console.log('\n3️⃣ Testing Bottom Navigation Styling...');
      await this.testBottomNavigation(page);
      
      // Test 4: Visual Hierarchy
      console.log('\n4️⃣ Testing Visual Hierarchy...');
      await this.testVisualHierarchy(page);
      
      // Generate final report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Test execution failed:', error.message);
      this.testResults.errors.push(`Test execution: ${error.message}`);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  async testAdminButtonStyling(page) {
    try {
      // Navigate to login and dashboard
      await page.goto('http://localhost:3000/login');
      await page.waitForSelector('input[type="text"]', { timeout: 10000 });
      
      await page.type('input[type="text"]', '1441');
      await page.type('input[type="password"]', '1441');
      await page.click('button[type="submit"]');
      
      // Wait for dashboard
      await page.waitForSelector('[data-testid="dashboard-grid"]', { timeout: 10000 });
      
      // Check admin button styling
      const adminButton = await page.$('button:has-text("Administración")');
      if (adminButton) {
        const styles = await page.evaluate((btn) => {
          const computed = window.getComputedStyle(btn);
          return {
            backgroundColor: computed.backgroundColor,
            borderRadius: computed.borderRadius,
            padding: computed.padding
          };
        }, adminButton);
        
        // Check for white background (rgb(255, 255, 255))
        if (styles.backgroundColor === 'rgb(255, 255, 255)') {
          console.log('   ✅ Admin button has white background');
          this.testResults.adminButtonFixed = true;
        } else {
          console.log(`   ❌ Admin button background: ${styles.backgroundColor} (expected white)`);
          this.testResults.errors.push('Admin button: Background not white');
        }
      } else {
        console.log('   ❌ Admin button not found');
        this.testResults.errors.push('Admin button: Element not found');
      }
      
    } catch (error) {
      console.log(`   ❌ Admin button test failed: ${error.message}`);
      this.testResults.errors.push(`Admin button: ${error.message}`);
    }
  }

  async testLayoutSpacing(page) {
    try {
      // Check spacing between grid elements
      const gridElements = await page.$$('[data-testid="dashboard-grid"] > div');
      
      if (gridElements.length > 0) {
        const spacing = await page.evaluate(() => {
          const grid = document.querySelector('[data-testid="dashboard-grid"]');
          const computed = window.getComputedStyle(grid);
          return computed.gap || computed.rowGap;
        });
        
        // Check for reduced spacing (should be 12px or 0.75rem)
        if (spacing === '12px' || spacing === '0.75rem') {
          console.log('   ✅ Layout spacing is more compact (12px)');
          this.testResults.spacingImproved = true;
        } else {
          console.log(`   ❌ Layout spacing: ${spacing} (expected 12px)`);
          this.testResults.errors.push(`Layout spacing: ${spacing} instead of 12px`);
        }
      } else {
        console.log('   ❌ Grid elements not found');
        this.testResults.errors.push('Layout spacing: Grid elements not found');
      }
      
    } catch (error) {
      console.log(`   ❌ Layout spacing test failed: ${error.message}`);
      this.testResults.errors.push(`Layout spacing: ${error.message}`);
    }
  }

  async testBottomNavigation(page) {
    try {
      // Check bottom navigation styling
      const bottomNav = await page.$('nav[class*="fixed bottom-0"]');
      
      if (bottomNav) {
        const styles = await page.evaluate((nav) => {
          const computed = window.getComputedStyle(nav);
          return {
            padding: computed.padding,
            backgroundColor: computed.backgroundColor
          };
        }, bottomNav);
        
        console.log('   ✅ Bottom navigation found');
        console.log(`   📏 Padding: ${styles.padding}`);
        this.testResults.bottomNavFixed = true;
      } else {
        console.log('   ❌ Bottom navigation not found');
        this.testResults.errors.push('Bottom navigation: Element not found');
      }
      
    } catch (error) {
      console.log(`   ❌ Bottom navigation test failed: ${error.message}`);
      this.testResults.errors.push(`Bottom navigation: ${error.message}`);
    }
  }

  async testVisualHierarchy(page) {
    try {
      // Check icon sizes in cards
      const iconContainers = await page.$$('.w-11.h-11');
      
      if (iconContainers.length >= 6) {
        console.log(`   ✅ Found ${iconContainers.length} icons with updated size (w-11 h-11)`);
        this.testResults.visualHierarchyImproved = true;
      } else {
        console.log(`   ❌ Expected 6+ icons with w-11 h-11, found ${iconContainers.length}`);
        this.testResults.errors.push(`Visual hierarchy: Only ${iconContainers.length} icons with correct size`);
      }
      
      // Check section headers
      const sectionHeaders = await page.$$('h2');
      if (sectionHeaders.length >= 3) {
        console.log(`   ✅ Found ${sectionHeaders.length} section headers`);
      } else {
        console.log(`   ❌ Expected 3+ section headers, found ${sectionHeaders.length}`);
        this.testResults.errors.push(`Visual hierarchy: Only ${sectionHeaders.length} section headers`);
      }
      
    } catch (error) {
      console.log(`   ❌ Visual hierarchy test failed: ${error.message}`);
      this.testResults.errors.push(`Visual hierarchy: ${error.message}`);
    }
  }

  generateReport() {
    console.log('\n📋 PIXEL-PERFECT FIXES VERIFICATION REPORT');
    console.log('='.repeat(50));
    
    const fixes = [
      { name: 'Admin Button Styling', status: this.testResults.adminButtonFixed },
      { name: 'Layout Spacing & Density', status: this.testResults.spacingImproved },
      { name: 'Bottom Navigation Styling', status: this.testResults.bottomNavFixed },
      { name: 'Visual Hierarchy Fine-tuning', status: this.testResults.visualHierarchyImproved }
    ];
    
    let passedFixes = 0;
    fixes.forEach(fix => {
      const icon = fix.status ? '✅' : '❌';
      console.log(`${icon} ${fix.name}`);
      if (fix.status) passedFixes++;
    });
    
    const successRate = (passedFixes / fixes.length) * 100;
    console.log(`\n🎯 Success Rate: ${successRate.toFixed(1)}% (${passedFixes}/${fixes.length} fixes verified)`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ Issues Found:');
      this.testResults.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
    }
    
    if (successRate >= 75) {
      console.log('\n🎉 PIXEL-PERFECT FIXES VERIFICATION PASSED!');
      console.log('The dashboard now matches the reference design much more closely.');
    } else {
      console.log('\n⚠️  PIXEL-PERFECT FIXES VERIFICATION NEEDS ATTENTION');
      console.log('Some fixes may need additional adjustments.');
    }
  }
}

// Run the tests
const tester = new PixelPerfectFixesTest();
tester.runTests().catch(console.error);
