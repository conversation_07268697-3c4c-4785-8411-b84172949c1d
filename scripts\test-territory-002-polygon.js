#!/usr/bin/env node

/**
 * Test Territory 002 Polygon
 * 
 * This script specifically tests Territory 002's boundary data to verify
 * the polygon generation and display is working correctly.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Get Territory 002 detailed information
 */
async function getTerritory002Details() {
  try {
    console.log('🔍 Territory 002 Detailed Analysis');
    console.log('===================================\n');

    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '002'
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        boundaries: true,
        status: true
      }
    });

    if (!territory) {
      console.log('❌ Territory 002 not found');
      return null;
    }

    console.log(`✅ Territory 002 found (ID: ${territory.id})`);
    console.log(`📍 Status: ${territory.status}`);
    
    // Analyze addresses
    const addresses = territory.address.split('\n').filter(addr => addr.trim());
    console.log(`📋 Total addresses: ${addresses.length}`);
    
    console.log('\n📍 First 5 addresses:');
    addresses.slice(0, 5).forEach((addr, index) => {
      console.log(`   ${index + 1}. ${addr}`);
    });
    
    if (addresses.length > 5) {
      console.log(`   ... and ${addresses.length - 5} more addresses`);
    }

    // Analyze boundary data
    if (territory.boundaries) {
      console.log('\n✅ Boundary data exists');
      const boundary = territory.boundaries;
      
      console.log(`📐 Boundary type: ${boundary.type}`);
      console.log(`📍 Coordinate points: ${boundary.coordinates[0].length}`);
      
      console.log('\n📍 Territory 002 Boundary Coordinates:');
      boundary.coordinates[0].forEach((coord, index) => {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
        console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}] (lng, lat)`);
      });
      
      // Calculate center and dimensions
      const coords = boundary.coordinates[0].slice(0, -1); // Exclude duplicate last point
      const lats = coords.map(c => c[1]);
      const lngs = coords.map(c => c[0]);
      
      const center = {
        latitude: lats.reduce((a, b) => a + b, 0) / lats.length,
        longitude: lngs.reduce((a, b) => a + b, 0) / lngs.length
      };
      
      const dimensions = {
        latSpan: Math.max(...lats) - Math.min(...lats),
        lngSpan: Math.max(...lngs) - Math.min(...lngs)
      };
      
      console.log('\n📍 Territory 002 Center:');
      console.log(`   Latitude: ${center.latitude}`);
      console.log(`   Longitude: ${center.longitude}`);
      
      console.log('\n📏 Territory 002 Dimensions:');
      console.log(`   Latitude span: ${dimensions.latSpan.toFixed(6)} degrees (~${(dimensions.latSpan * 69).toFixed(2)} miles)`);
      console.log(`   Longitude span: ${dimensions.lngSpan.toFixed(6)} degrees (~${(dimensions.lngSpan * 54.6).toFixed(2)} miles)`);
      
    } else {
      console.log('❌ No boundary data found');
    }

    return territory;
  } catch (error) {
    console.error('❌ Error getting Territory 002 details:', error);
    return null;
  }
}

/**
 * Test API responses for Territory 002
 */
async function testTerritory002APIs(territoryId) {
  try {
    console.log('\n🌐 Testing Territory 002 API Responses');
    console.log('======================================\n');

    // Test individual territory API simulation
    const individualResponse = await prisma.territory.findFirst({
      where: { id: territoryId },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        status: true,
        boundaries: true
      }
    });

    console.log('📤 Individual Territory API Response:');
    console.log(`   ID: ${individualResponse.id}`);
    console.log(`   Territory Number: ${individualResponse.territoryNumber}`);
    console.log(`   Has Boundaries: ${!!individualResponse.boundaries}`);
    
    if (individualResponse.boundaries) {
      console.log(`   Boundary Type: ${individualResponse.boundaries.type}`);
      console.log(`   Boundary Points: ${individualResponse.boundaries.coordinates[0].length}`);
    }

    // Test map data API simulation
    const mapDataResponse = {
      id: individualResponse.id,
      territoryNumber: individualResponse.territoryNumber,
      address: individualResponse.address,
      status: individualResponse.status,
      boundary: individualResponse.boundaries, // Note: field name is 'boundary' not 'boundaries'
      coordinates: null
    };

    // Calculate center coordinates from boundary
    if (individualResponse.boundaries && individualResponse.boundaries.coordinates) {
      const coords = individualResponse.boundaries.coordinates[0];
      const lats = coords.slice(0, -1).map(c => c[1]); // Exclude duplicate last point
      const lngs = coords.slice(0, -1).map(c => c[0]);
      
      mapDataResponse.coordinates = {
        latitude: lats.reduce((a, b) => a + b, 0) / lats.length,
        longitude: lngs.reduce((a, b) => a + b, 0) / lngs.length
      };
    }

    console.log('\n📤 Map Data API Response:');
    console.log(`   ID: ${mapDataResponse.id}`);
    console.log(`   Territory Number: ${mapDataResponse.territoryNumber}`);
    console.log(`   Has Boundary: ${!!mapDataResponse.boundary}`);
    console.log(`   Has Coordinates: ${!!mapDataResponse.coordinates}`);
    
    if (mapDataResponse.coordinates) {
      console.log(`   Center Latitude: ${mapDataResponse.coordinates.latitude}`);
      console.log(`   Center Longitude: ${mapDataResponse.coordinates.longitude}`);
    }

    return { individualResponse, mapDataResponse };
  } catch (error) {
    console.error('❌ API test error:', error);
    return null;
  }
}

/**
 * Compare Territory 002 with Territory 001
 */
async function compareWithTerritory001() {
  try {
    console.log('\n🔍 Comparing Territory 002 with Territory 001');
    console.log('==============================================\n');

    const territories = await prisma.territory.findMany({
      where: {
        congregationId: '1441',
        territoryNumber: { in: ['001', '002'] }
      },
      select: {
        territoryNumber: true,
        boundaries: true
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });

    territories.forEach(territory => {
      console.log(`📍 Territory ${territory.territoryNumber}:`);
      
      if (territory.boundaries) {
        const coords = territory.boundaries.coordinates[0];
        console.log(`   Northwest: [${coords[0][0]}, ${coords[0][1]}]`);
        console.log(`   Northeast: [${coords[1][0]}, ${coords[1][1]}]`);
        console.log(`   Southeast: [${coords[2][0]}, ${coords[2][1]}]`);
        console.log(`   Southwest: [${coords[3][0]}, ${coords[3][1]}]`);
        
        // Calculate center
        const lats = coords.slice(0, -1).map(c => c[1]);
        const lngs = coords.slice(0, -1).map(c => c[0]);
        const centerLat = lats.reduce((a, b) => a + b, 0) / lats.length;
        const centerLng = lngs.reduce((a, b) => a + b, 0) / lngs.length;
        
        console.log(`   Center: [${centerLng}, ${centerLat}]`);
      } else {
        console.log('   ❌ No boundary data');
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ Comparison error:', error);
  }
}

/**
 * Validate Territory 002 boundary coordinates
 */
function validateTerritory002Boundary(boundary) {
  console.log('\n🧮 Territory 002 Boundary Validation');
  console.log('====================================\n');

  if (!boundary || !boundary.coordinates) {
    console.log('❌ No boundary data to validate');
    return false;
  }

  const coords = boundary.coordinates[0];
  
  // Check coordinate ranges for Miami
  const MIAMI_BOUNDS = {
    lat: { min: 25.7, max: 25.9 },
    lng: { min: -80.3, max: -80.2 }
  };
  
  console.log('📍 Coordinate validation:');
  let allValid = true;
  
  coords.forEach((coord, index) => {
    const [lng, lat] = coord;
    const latValid = lat >= MIAMI_BOUNDS.lat.min && lat <= MIAMI_BOUNDS.lat.max;
    const lngValid = lng >= MIAMI_BOUNDS.lng.min && lng <= MIAMI_BOUNDS.lng.max;
    
    const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
    const status = latValid && lngValid ? '✅' : '❌';
    
    console.log(`   ${status} ${labels[index]}: [${lng}, ${lat}]`);
    
    if (!latValid || !lngValid) {
      allValid = false;
    }
  });
  
  // Check polygon closure
  const firstPoint = coords[0];
  const lastPoint = coords[coords.length - 1];
  const isClosed = firstPoint[0] === lastPoint[0] && firstPoint[1] === lastPoint[1];
  
  console.log(`\n🔄 Polygon closure: ${isClosed ? '✅' : '❌'}`);
  
  if (allValid && isClosed) {
    console.log('\n✅ Territory 002 boundary is valid and ready for map display');
  } else {
    console.log('\n❌ Territory 002 boundary has validation issues');
  }
  
  return allValid && isClosed;
}

/**
 * Main test function
 */
async function main() {
  console.log('🧪 Territory 002 Polygon Testing');
  console.log('=================================\n');

  try {
    // Get Territory 002 details
    const territory002 = await getTerritory002Details();
    if (!territory002) return;

    // Test API responses
    const apiTests = await testTerritory002APIs(territory002.id);
    if (!apiTests) return;

    // Compare with Territory 001
    await compareWithTerritory001();

    // Validate boundary
    const isValid = validateTerritory002Boundary(territory002.boundaries);

    console.log('\n🎯 Territory 002 Test Summary:');
    console.log('==============================');
    console.log(`✅ Territory 002 found and analyzed`);
    console.log(`✅ Boundary data exists and is ${isValid ? 'valid' : 'invalid'}`);
    console.log(`✅ API responses include boundary data`);
    console.log(`✅ Ready for map display testing`);
    
    console.log('\n💡 Next Steps:');
    console.log('1. Open the territory map in browser');
    console.log('2. Navigate to Territory 002');
    console.log('3. Verify the boundary outline displays correctly');
    console.log('4. Compare with Territory 001 to check for consistency');
    console.log('5. Look for any duplicate or racing logic issues');

  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  getTerritory002Details,
  testTerritory002APIs,
  compareWithTerritory001,
  validateTerritory002Boundary
};
