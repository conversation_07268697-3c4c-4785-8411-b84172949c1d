#!/usr/bin/env node

/**
 * Set Test PIN
 * 
 * This script sets a known PIN for testing authentication.
 */

const { Client } = require('pg');
const bcrypt = require('bcryptjs');

async function setTestPin() {
    console.log('🔧 SETTING TEST PIN...');
    console.log('');

    let client;

    try {
        // 1. Connect to database
        console.log('🔌 Connecting to database...');
        client = new Client({
            host: 'localhost',
            port: 5432,
            user: 'mywebsites',
            password: 'password',
            database: 'hermanos'
        });

        await client.connect();
        console.log('✅ Database connection successful');

        // 2. Set congregation PIN to 123456
        console.log('🔐 Setting congregation PIN to 123456...');
        const newPinHash = await bcrypt.hash('123456', 12);
        
        await client.query('UPDATE congregations SET pin = $1 WHERE id = $2;', [newPinHash, '1441']);
        console.log('✅ Congregation PIN updated');

        // 3. Set member PINs to 123456 for testing
        console.log('👥 Setting member PINs to 123456...');
        
        const members = await client.query('SELECT id, name, email FROM members WHERE congregation_id = $1 AND is_active = true LIMIT 5;', ['1441']);
        
        for (const member of members.rows) {
            await client.query('UPDATE members SET pin = $1 WHERE id = $2;', [newPinHash, member.id]);
            console.log(`   ✅ Updated PIN for ${member.name} (${member.email})`);
        }

        // 4. Test the authentication
        console.log('🌐 Testing authentication...');
        try {
            const response = await fetch('http://localhost:3000/api/auth/congregation-login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    congregationId: '1441',
                    pin: '123456'
                })
            });

            const result = await response.json();
            console.log(`   Response status: ${response.status}`);
            
            if (response.ok) {
                console.log('   ✅ Congregation authentication successful!');
                console.log(`   Token received: ${result.token ? 'Yes' : 'No'}`);
            } else {
                console.log('   ❌ Authentication failed:');
                console.log(`   Error: ${result.error}`);
            }

        } catch (error) {
            console.log(`   ❌ API test failed: ${error.message}`);
        }

        console.log('');
        console.log('🎉 TEST CREDENTIALS SET SUCCESSFULLY!');
        console.log('');
        console.log('🎯 LOGIN CREDENTIALS:');
        console.log('   Congregation ID: 1441');
        console.log('   Congregation PIN: 123456');
        console.log('');
        console.log('👥 SAMPLE MEMBERS (all use PIN: 123456):');
        members.rows.forEach(member => {
            console.log(`   - ${member.name}: ${member.email}`);
        });
        console.log('');
        console.log('📋 TESTING INSTRUCTIONS:');
        console.log('   1. Go to http://localhost:3000/login');
        console.log('   2. Enter Congregation ID: 1441');
        console.log('   3. Enter Congregation PIN: 123456');
        console.log('   4. Login with any member email and PIN: 123456');
        console.log('   5. All features should now work correctly!');

    } catch (error) {
        console.error('❌ Error setting test PIN:', error);
    } finally {
        if (client) {
            await client.end();
        }
    }
}

// Run the script
setTestPin();
