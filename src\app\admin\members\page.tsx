'use client';

/**
 * Members Management Page
 *
 * Comprehensive member profile management interface for congregation
 * administrators with CRUD operations and audit trail.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MemberCard from '@/components/admin/members/MemberCard';
import MemberForm, { MemberFormData } from '@/components/admin/members/MemberForm';
import MemberHistory from '@/components/admin/members/MemberHistory';
import { MemberProfile, MemberChangeRecord } from '@/lib/services/memberManagementService';
import AdminFooter from '@/components/admin/AdminFooter';

export default function MembersPage() {
  const router = useRouter();
  const [members, setMembers] = useState<MemberProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string>('');

  // UI State
  const [showForm, setShowForm] = useState(false);
  const [editingMember, setEditingMember] = useState<MemberProfile | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [historyData, setHistoryData] = useState<MemberChangeRecord[]>([]);
  const [historyMemberName, setHistoryMemberName] = useState<string>('');

  // Filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalMembers, setTotalMembers] = useState(0);

  useEffect(() => {
    checkAccess();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (userRole) {
      loadMembers();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userRole, currentPage, searchTerm, roleFilter, activeFilter]);

  const checkAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      // Verify token and check permissions
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserRole(data.user.role);

        // Check if user has admin access
        if (!data.permissions.canAccessAdmin) {
          router.push('/dashboard');
          return;
        }
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Access check failed:', error);
      router.push('/login');
    }
  };

  const loadMembers = async () => {
    try {
      setError(null);
      const token = localStorage.getItem('hermanos_token');

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
      });

      if (searchTerm) params.append('search', searchTerm);
      if (roleFilter) params.append('role', roleFilter);
      if (activeFilter !== 'all') params.append('active', activeFilter);

      const response = await fetch(`/api/admin/members?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMembers(data.members);
        setTotalPages(data.pages);
        setTotalMembers(data.total);
      } else {
        throw new Error('Failed to load members');
      }
    } catch (error) {
      console.error('Error loading members:', error);
      setError('Error al cargar los miembros');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateMember = async (formData: MemberFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/members', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        await loadMembers();
        setShowForm(false);
        setCurrentPage(1); // Go to first page to see new member
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create member');
      }
    } catch (error) {
      console.error('Error creating member:', error);
      setError(error instanceof Error ? error.message : 'Error al crear el miembro');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateMember = async (formData: MemberFormData) => {
    if (!editingMember) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/members', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memberId: editingMember.id,
          ...formData,
        }),
      });

      if (response.ok) {
        await loadMembers();
        setShowForm(false);
        setEditingMember(null);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update member');
      }
    } catch (error) {
      console.error('Error updating member:', error);
      setError(error instanceof Error ? error.message : 'Error al actualizar el miembro');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewHistory = async (memberId: string) => {
    try {
      setShowHistory(true);
      setHistoryData([]);

      const token = localStorage.getItem('hermanos_token');
      const response = await fetch(`/api/admin/members/history?memberId=${memberId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setHistoryData(data.history);

        // Find member name
        const member = members.find(m => m.id === memberId);
        setHistoryMemberName(member?.name || 'Miembro');
      } else {
        throw new Error('Failed to load member history');
      }
    } catch (error) {
      console.error('Error loading member history:', error);
      setError('Error al cargar el historial del miembro');
    }
  };

  const handleEditMember = (member: MemberProfile) => {
    setEditingMember(member);
    setShowForm(true);
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingMember(null);
  };

  const handleBackToDashboard = () => {
    router.push('/admin');
  };

  const canManage = ['elder', 'overseer_coordinator', 'developer'].includes(userRole);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando gestión de miembros...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 relative">
      {/* Header */}
      <header className="bg-blue-600 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-white">
                Gestión de Miembros
              </h1>
              <p className="text-blue-100">
                Administración de perfiles de la congregación
              </p>
            </div>
            <button
              onClick={handleBackToDashboard}
              className="bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Volver al Admin
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 pb-20 min-h-screen">
        <div className="px-4 py-6 sm:px-0">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800">{error}</p>
              <button
                onClick={() => setError(null)}
                className="text-red-600 hover:text-red-800 text-sm mt-2"
              >
                Cerrar
              </button>
            </div>
          )}

          {/* Actions and Filters */}
          <div className="mb-6 space-y-4">
            {/* Action Buttons */}
            {canManage && (
              <div>
                <button
                  onClick={() => setShowForm(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                >
                  Nuevo Miembro
                </button>
              </div>
            )}

            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <input
                type="text"
                placeholder="Buscar por nombre o email..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />

              <select
                value={roleFilter}
                onChange={(e) => {
                  setRoleFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos los roles</option>
                <option value="publisher">Publicador</option>
                <option value="ministerial_servant">Siervo Ministerial</option>
                <option value="elder">Anciano</option>
                <option value="overseer_coordinator">Superintendente/Coordinador</option>
              </select>

              <select
                value={activeFilter}
                onChange={(e) => {
                  setActiveFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Todos los estados</option>
                <option value="true">Solo activos</option>
                <option value="false">Solo inactivos</option>
              </select>

              <div className="text-sm text-gray-600 flex items-center">
                Total: {totalMembers} miembros
              </div>
            </div>
          </div>

          {/* Member Form */}
          {showForm && (
            <div className="mb-6">
              <MemberForm
                member={editingMember}
                onSubmit={editingMember ? handleUpdateMember : handleCreateMember}
                onCancel={handleCancelForm}
                isLoading={isSubmitting}
              />
            </div>
          )}

          {/* Members Grid */}
          <div className="space-y-6">
            {members.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">
                  No se encontraron miembros
                </p>
                {canManage && !showForm && (
                  <p className="text-gray-400 mt-2">
                    Haz clic en &quot;Nuevo Miembro&quot; para comenzar
                  </p>
                )}
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {members.map(member => (
                    <MemberCard
                      key={member.id}
                      member={member}
                      onEdit={handleEditMember}
                      onViewHistory={handleViewHistory}
                      canManage={canManage}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center space-x-4 mt-8">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md disabled:opacity-50 hover:bg-gray-300"
                    >
                      Anterior
                    </button>

                    <span className="text-gray-600">
                      Página {currentPage} de {totalPages}
                    </span>

                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md disabled:opacity-50 hover:bg-gray-300"
                    >
                      Siguiente
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </main>

      {/* Member History Modal */}
      {showHistory && (
        <MemberHistory
          history={historyData}
          memberName={historyMemberName}
          isLoading={false}
          onClose={() => setShowHistory(false)}
        />
      )}

      {/* Admin Footer */}
      <AdminFooter currentSection="members" />
    </div>
  );
}
