// Territory Import Service
// Service for importing territory data from Excel files

import * as XLSX from 'xlsx';
import { prisma } from '@/lib/prisma';
import {
  TerritoryImportData,
  ImportValidationError,
  ImportResult,
  ExcelParseResult,
  ExcelColumnMapping,
  DEFAULT_COLUMN_MAPPINGS,
  TerritoryValidationRules,
  DEFAULT_VALIDATION_RULES
} from '@/types/territories/import';

export class ExcelImportService {
  private static validationRules: TerritoryValidationRules = DEFAULT_VALIDATION_RULES;

  /**
   * Parse Excel file and extract territory data
   */
  static async parseExcelFile(
    fileBuffer: Buffer,
    columnMapping?: ExcelColumnMapping
  ): Promise<ExcelParseResult> {
    try {
      const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];

      if (!sheetName) {
        throw new Error('No sheets found in Excel file');
      }

      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // Try different column mappings if none provided
      const mappings = columnMapping ? [columnMapping] : DEFAULT_COLUMN_MAPPINGS;

      for (const mapping of mappings) {
        const result = this.extractDataWithMapping(jsonData, mapping);
        if (result.data.length > 0 || result.errors.some(e => e.field !== 'file')) {
          return result;
        }
      }

      // If no mapping worked, return empty result with error
      return {
        data: [],
        errors: [{
          rowNumber: 1,
          field: 'file',
          value: 'Excel file',
          error: 'Could not detect territory data columns. Expected columns: Territory Number, Address'
        }],
        totalRows: jsonData.length
      };

    } catch (error) {
      return {
        data: [],
        errors: [{
          rowNumber: 1,
          field: 'file',
          value: 'Excel file',
          error: `Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`
        }],
        totalRows: 0
      };
    }
  }

  /**
   * Extract data using specific column mapping
   */
  private static extractDataWithMapping(
    jsonData: any[][],
    mapping: ExcelColumnMapping
  ): ExcelParseResult {
    const data: TerritoryImportData[] = [];
    const errors: ImportValidationError[] = [];
    const startRow = mapping.startRow || 1;
    const headerRow = startRow > 1 ? jsonData[0] : undefined;

    for (let i = startRow - 1; i < jsonData.length; i++) {
      const row = jsonData[i];
      const rowNumber = i + 1;

      if (!row || row.length === 0) continue;

      try {
        const territoryNumber = this.getCellValue(row, mapping.territoryNumberColumn, headerRow);
        const address = this.getCellValue(row, mapping.addressColumn, headerRow);
        const notes = mapping.notesColumn ? this.getCellValue(row, mapping.notesColumn, headerRow) : undefined;

        // Skip empty rows
        if (!territoryNumber && !address) continue;

        const territoryData: TerritoryImportData = {
          territoryNumber: territoryNumber?.toString().trim() || '',
          address: address?.toString().trim() || '',
          notes: notes?.toString().trim() || undefined,
          rowNumber
        };

        // Validate the extracted data
        const validationErrors = this.validateTerritoryData(territoryData);
        if (validationErrors.length > 0) {
          errors.push(...validationErrors);
        } else {
          data.push(territoryData);
        }

      } catch (error) {
        errors.push({
          rowNumber,
          field: 'row',
          value: row.join(', '),
          error: `Failed to process row: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
    }

    return {
      data,
      errors,
      totalRows: jsonData.length
    };
  }

  /**
   * Get cell value by column identifier (letter or index)
   */
  private static getCellValue(row: any[], column: string | number, headerRow?: any[]): any {
    if (typeof column === 'number') {
      return row[column];
    }

    // Handle column letters (A, B, C, etc.)
    if (typeof column === 'string' && column.length === 1) {
      const columnIndex = column.toUpperCase().charCodeAt(0) - 65; // A=0, B=1, etc.
      return row[columnIndex];
    }

    // Handle column names (search in header row)
    if (headerRow && typeof column === 'string') {
      const columnIndex = headerRow.findIndex(header =>
        header && header.toString().toLowerCase().trim() === column.toLowerCase().trim()
      );
      return columnIndex >= 0 ? row[columnIndex] : undefined;
    }

    return undefined;
  }

  /**
   * Validate territory data according to business rules
   */
  private static validateTerritoryData(data: TerritoryImportData): ImportValidationError[] {
    const errors: ImportValidationError[] = [];

    // Territory number validation
    if (this.validationRules.territoryNumberRequired && !data.territoryNumber) {
      errors.push({
        rowNumber: data.rowNumber,
        field: 'territoryNumber',
        value: data.territoryNumber,
        error: 'Territory number is required'
      });
    }

    if (data.territoryNumber && data.territoryNumber.length > this.validationRules.territoryNumberMaxLength) {
      errors.push({
        rowNumber: data.rowNumber,
        field: 'territoryNumber',
        value: data.territoryNumber,
        error: `Territory number exceeds maximum length of ${this.validationRules.territoryNumberMaxLength} characters`
      });
    }

    if (data.territoryNumber && this.validationRules.territoryNumberPattern) {
      if (!this.validationRules.territoryNumberPattern.test(data.territoryNumber)) {
        errors.push({
          rowNumber: data.rowNumber,
          field: 'territoryNumber',
          value: data.territoryNumber,
          error: 'Territory number contains invalid characters. Only letters, numbers, hyphens, and underscores are allowed'
        });
      }
    }

    // Address validation
    if (this.validationRules.addressRequired && !data.address) {
      errors.push({
        rowNumber: data.rowNumber,
        field: 'address',
        value: data.address,
        error: 'Address is required'
      });
    }

    if (data.address && data.address.length > this.validationRules.addressMaxLength) {
      errors.push({
        rowNumber: data.rowNumber,
        field: 'address',
        value: data.address,
        error: `Address exceeds maximum length of ${this.validationRules.addressMaxLength} characters`
      });
    }

    return errors;
  }

  /**
   * Import territories to database
   */
  static async importTerritories(
    territories: TerritoryImportData[],
    congregationId: string
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      totalRows: territories.length,
      successfulImports: 0,
      failedImports: 0,
      duplicatesFound: 0,
      errors: [],
      duplicates: [],
      importedTerritories: [],
      summary: ''
    };

    try {
      // Check for duplicates within the import data
      const duplicatesInFile = this.findDuplicatesInData(territories);
      result.duplicates.push(...duplicatesInFile);

      // Check for existing territories in database
      const existingTerritories = await this.checkExistingTerritories(territories, congregationId);

      // Filter out duplicates if not allowing them
      const seenNumbers = new Set<string>();
      const territoriesToImport = territories.filter(territory => {
        const isDuplicateInFile = seenNumbers.has(territory.territoryNumber);
        const existsInDatabase = existingTerritories.some(existing =>
          existing.territoryNumber === territory.territoryNumber
        );

        if (isDuplicateInFile || existsInDatabase) {
          result.duplicatesFound++;
          return false;
        }

        seenNumbers.add(territory.territoryNumber);
        return true;
      });

      // Import territories in transaction
      await prisma.$transaction(async (tx) => {
        for (const territory of territoriesToImport) {
          try {
            const created = await tx.territory.create({
              data: {
                congregationId,
                territoryNumber: territory.territoryNumber,
                address: territory.address,
                notes: territory.notes,
                status: 'available'
              }
            });

            result.importedTerritories.push(created.id);
            result.successfulImports++;

          } catch (error) {
            result.errors.push({
              rowNumber: territory.rowNumber,
              field: 'database',
              value: territory.territoryNumber,
              error: `Failed to create territory: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
            result.failedImports++;
          }
        }
      });

      result.success = result.successfulImports > 0;
      result.summary = this.generateImportSummary(result);

    } catch (error) {
      result.errors.push({
        rowNumber: 0,
        field: 'import',
        value: 'batch',
        error: `Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
      result.failedImports = territories.length;
    }

    return result;
  }

  /**
   * Find duplicate territory numbers within the import data
   */
  private static findDuplicatesInData(territories: TerritoryImportData[]): TerritoryImportData[] {
    const seen = new Set<string>();
    const duplicates: TerritoryImportData[] = [];

    for (const territory of territories) {
      if (seen.has(territory.territoryNumber)) {
        duplicates.push(territory);
      } else {
        seen.add(territory.territoryNumber);
      }
    }

    return duplicates;
  }

  /**
   * Check for existing territories in database
   */
  private static async checkExistingTerritories(
    territories: TerritoryImportData[],
    congregationId: string
  ) {
    const territoryNumbers = territories.map(t => t.territoryNumber);

    return await prisma.territory.findMany({
      where: {
        congregationId,
        territoryNumber: {
          in: territoryNumbers
        }
      },
      select: {
        territoryNumber: true
      }
    });
  }

  /**
   * Generate import summary message
   */
  private static generateImportSummary(result: ImportResult): string {
    const parts = [];

    if (result.successfulImports > 0) {
      parts.push(`${result.successfulImports} territories imported successfully`);
    }

    if (result.failedImports > 0) {
      parts.push(`${result.failedImports} territories failed to import`);
    }

    if (result.duplicatesFound > 0) {
      parts.push(`${result.duplicatesFound} duplicates found and skipped`);
    }

    return parts.join(', ') || 'No territories processed';
  }
}
