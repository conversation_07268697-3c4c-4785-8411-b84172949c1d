'use client';

import React, { useState } from 'react';
import TerritoryDetail from '@/components/territories/member/TerritoryDetail';

// Mock territory ID for testing
const MOCK_TERRITORY_ID = 'test-territory-007';

export default function TestTerritoryDetailPage() {
  const [showDetail, setShowDetail] = useState(false);

  // Mock API response for testing
  React.useEffect(() => {
    // Mock the API endpoint for testing
    const originalFetch = window.fetch;
    
    window.fetch = async (url: string | URL | Request, options?: RequestInit) => {
      const urlString = url.toString();
      
      if (urlString.includes(`/api/territories/${MOCK_TERRITORY_ID}`)) {
        // Mock territory data
        return new Response(JSON.stringify({
          id: MOCK_TERRITORY_ID,
          territoryNumber: '007',
          address: '6580 FLAGLER ST, Miami, FL 33144\n6500 FLAGLER ST, Miami, FL 33144\n6520 FLAGLER ST, Miami, FL 33144\n6540 FLAGLER ST, Miami, FL 33144\n6560 FLAGLER ST, Miami, FL 33144',
          status: 'assigned',
          notes: 'Territory notes here',
          coordinates: {
            latitude: 25.7620,
            longitude: -80.2715
          },
          boundary: {
            type: 'Polygon',
            coordinates: [[
              [-80.2725, 25.7630],
              [-80.2705, 25.7630],
              [-80.2705, 25.7610],
              [-80.2725, 25.7610],
              [-80.2725, 25.7630]
            ]]
          },
          assignedMember: {
            id: 'member-1',
            name: 'Juan Pérez',
            assignedAt: new Date().toISOString(),
            assignedBy: 'Elder Smith'
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      if (urlString.includes('/api/auth/verify')) {
        // Mock auth verification
        return new Response(JSON.stringify({
          user: {
            id: 'user-1',
            name: 'Juan Pérez',
            role: 'publisher',
            congregationId: '1441',
            congregationName: 'Coral Oeste'
          }
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // For all other requests, use the original fetch
      return originalFetch(url, options);
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  if (showDetail) {
    return (
      <TerritoryDetail
        territoryId={MOCK_TERRITORY_ID}
        onBack={() => setShowDetail(false)}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold mb-6">Territory Detail Integration Test</h1>
          
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-900 mb-2">🎯 Integration Complete!</h3>
              <p className="text-blue-800 text-sm">
                The territory detail view now includes:
              </p>
              <ul className="text-blue-800 text-sm mt-2 space-y-1">
                <li>• ✅ Interactive map with territory boundaries</li>
                <li>• ✅ Automatic zoom to territory area</li>
                <li>• ✅ Address list with field service action buttons</li>
                <li>• ✅ Mobile-optimized interface</li>
                <li>• ✅ Toggle map visibility</li>
                <li>• ✅ Field Service UI patterns</li>
              </ul>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-medium text-green-900 mb-2">📱 Mobile Interface Features</h3>
              <ul className="text-green-800 text-sm space-y-1">
                <li>• Territory map shows boundaries and markers</li>
                <li>• Tap addresses to expand action buttons</li>
                <li>• Action buttons: At Home, Not Home, Do Not Call, Testigo, Perros/Rejas, No Trespassing</li>
                <li>• Map automatically zooms to territory boundary</li>
                <li>• Toggle map visibility with header button</li>
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="font-medium text-yellow-900 mb-2">🗺️ Map Features</h3>
              <ul className="text-yellow-800 text-sm space-y-1">
                <li>• Territory boundaries displayed as colored polygons</li>
                <li>• Territory markers with numbers</li>
                <li>• Status-based color coding</li>
                <li>• Automatic zoom to fit territory</li>
                <li>• Interactive popups with territory info</li>
              </ul>
            </div>

            <button
              onClick={() => setShowDetail(true)}
              className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              🚀 Test Territory Detail with Map Integration
            </button>

            <div className="text-center text-sm text-gray-500 mt-4">
              <p>This will show Territory 007 with mock data including:</p>
              <p>• Map with territory boundary in Miami</p>
              <p>• 5 sample addresses with action buttons</p>
              <p>• Mobile-optimized interface</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
