# Story 10.2: Excel Territory Data Import Service

**Epic:** Epic 10: Foundation & Territory Data Import
**Story Points:** 8
**Priority:** High
**Status:** Ready for Review

## Story

**As a** congregation administrator,
**I want** to upload Excel files containing territory data,
**so that** existing territory information can be imported into the digital system.

## Acceptance Criteria

1. API endpoint accepts Excel file uploads (.xlsx format)
2. Excel parser extracts territory number and address information from uploaded files
3. Data validation ensures territory numbers are unique within congregation
4. Import process handles errors gracefully and provides detailed feedback
5. Imported territories are automatically assigned "available" status
6. Import results summary shows successful imports, errors, and duplicates

## Tasks / Subtasks

- [x] Create Excel file upload API endpoint (AC: 1)
  - [x] Implement POST /api/territories/import endpoint with multipart/form-data support
  - [x] Add file type validation for .xlsx format only
  - [x] Add file size limits and security validation
  - [x] Implement authentication middleware for admin-only access
  - [x] Add proper error handling and response formatting
- [x] Implement Excel parsing service (AC: 2)
  - [x] Create ExcelImportService class with xlsx library integration
  - [x] Parse Excel files to extract territory number and address columns
  - [x] Handle different Excel file formats and column arrangements
  - [x] Add robust error handling for malformed Excel files
  - [x] Implement data cleaning and normalization for extracted data
- [x] Add territory data validation (AC: 3, 4)
  - [x] Validate territory numbers are unique within congregation
  - [x] Validate required fields (territory number, address) are present
  - [x] Check for duplicate territories in the same import file
  - [x] Implement comprehensive error reporting with row numbers
  - [x] Add data format validation (territory number format, address length)
- [x] Implement database insertion logic (AC: 5)
  - [x] Create territories in database with "available" status
  - [x] Use database transactions for atomic import operations
  - [x] Handle database constraint violations gracefully
  - [x] Implement congregation_id isolation for multi-tenant support
  - [x] Add proper error handling for database operations
- [x] Create import results reporting (AC: 6)
  - [x] Generate detailed import summary with success/error counts
  - [x] Report specific errors with row numbers and descriptions
  - [x] Identify and report duplicate territories found
  - [x] Create structured response format for frontend consumption
  - [x] Add logging for import operations and results
- [x] Implement file storage and cleanup (Architecture Requirements)
  - [x] Store uploaded Excel files in public/uploads/territories/ directory
  - [x] Implement file cleanup after processing completion
  - [x] Add file naming conventions with timestamps and congregation ID
  - [x] Ensure proper file permissions and security
- [x] Write comprehensive tests (Testing Standards)
  - [x] Unit tests for ExcelImportService parsing logic
  - [x] Integration tests for API endpoint with file uploads
  - [x] Test error handling scenarios and validation rules
  - [x] Test database transaction handling and rollback scenarios
  - [x] Test multi-tenant isolation and congregation-specific imports

## Dev Notes

### Dependencies and Prerequisites
**DEPENDENCY ALERT**: This story depends on Story 10.1 (Territory Database Schema Enhancement) being completed first. The database tables (territories, territory_assignments) and Prisma models must exist before implementing the import service.

### API Endpoint Specification
[Source: docs/territories-architecture.md#api-specification]

**Endpoint**: POST /api/territories/import
**Content-Type**: multipart/form-data
**Authentication**: Admin only (MANAGE_TERRITORIES permission required)
**Request Body**: Excel file (.xlsx format) as binary data
**Response**: 202 Accepted with import ID for async processing

### Excel Processing Technology Stack
[Source: docs/territories-architecture.md#tech-stack]
- **Excel Library**: xlsx 0.20+ for Excel file parsing
- **File Storage**: Local file system in public/uploads/territories/
- **Processing Pattern**: Async Excel processing with progress tracking
- **Validation**: Zod validation for all territory API endpoints

### Import Service Architecture
[Source: docs/territories-architecture.md#components]

**ExcelImportService Responsibilities:**
- Parse Excel files and extract territory data
- Validate data format and business rules
- Handle errors gracefully with detailed feedback
- Process imports asynchronously with progress tracking
- Integrate with existing authentication and authorization

### File Structure and Locations
[Source: docs/territories-architecture.md#unified-project-structure]
- **API Route**: `src/app/api/territories/import/route.ts`
- **Service Class**: `src/services/territories/ImportService.ts`
- **Type Definitions**: `src/types/territories/import.ts`
- **File Storage**: `public/uploads/territories/`
- **Test Files**: `tests/api/territories/import.test.ts`

### Data Validation Requirements
[Source: docs/territories-architecture.md#coding-standards]
- **Excel Import Validation**: Always validate Excel data before database insertion
- **Error Logging**: Log all validation errors with row numbers
- **Congregation Isolation**: Always include congregationId in database operations
- **Transaction Safety**: Use database transactions for atomic operations

### Security and Performance Considerations
[Source: docs/territories-architecture.md#security-and-performance]
- **Rate Limiting**: Standard rate limiting for Excel imports
- **File Size Limits**: Implement reasonable file size restrictions
- **Input Sanitization**: Sanitize all extracted data before database insertion
- **Authentication**: Use existing JWT-based authentication system
- **Performance**: Process large imports efficiently without blocking UI

### Database Integration
[Source: docs/territories-architecture.md#database-schema]
- **Territory Table**: Insert into territories table with congregation_id isolation
- **Status Field**: Set status to 'available' for all imported territories
- **Unique Constraints**: Respect territory_number + congregation_id uniqueness
- **Foreign Keys**: Ensure proper congregation_id foreign key relationships

### Testing

**Test File Location**: `tests/api/territories/import.test.ts`
**Testing Framework**: Vitest + Supertest for API endpoint testing
**Test Database**: Separate test database for import testing
**Testing Requirements:**
- Test Excel file parsing with various formats and edge cases
- Test API endpoint authentication and authorization
- Test data validation rules and error reporting
- Test database transaction handling and rollback scenarios
- Test file upload security and size limits
- Test multi-tenant isolation during imports

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-25 | 1.0 | Initial story creation for Excel territory import service | PO Agent |

## Dev Agent Record

### Agent Model Used
Claude Sonnet 4 (Augment Agent) - January 25, 2025

### Debug Log References
- Excel parsing tests: tests/api/territories/import.test.ts
- API endpoint testing: POST /api/territories/import
- File upload validation: 10MB limit, .xlsx/.xls formats only
- Database transaction testing: Prisma transaction handling

### Completion Notes List
1. **Excel Library Integration**: Successfully integrated xlsx library for Excel file parsing
2. **Multi-format Support**: Implemented support for both header-based and column-letter-based Excel formats
3. **Comprehensive Validation**: Added territory number format validation, required field validation, and duplicate detection
4. **Database Integration**: Implemented atomic transactions for territory imports with proper error handling
5. **Security Implementation**: Added admin authentication, file type validation, and congregation isolation
6. **Test Coverage**: Created comprehensive test suite with 12 test cases covering all scenarios
7. **Error Handling**: Implemented detailed error reporting with row numbers and specific error messages

### File List
- `src/types/territories/import.ts` - Import type definitions and validation rules
- `src/services/territories/ImportService.ts` - Excel import service with parsing and validation logic
- `src/app/api/territories/import/route.ts` - API endpoint for file uploads and import processing
- `tests/api/territories/import.test.ts` - Comprehensive test suite for import functionality
- `public/uploads/territories/` - Directory for uploaded Excel files (created)
- `package.json` - Updated with xlsx and @types/xlsx dependencies

## QA Results
*To be populated by QA agent*
