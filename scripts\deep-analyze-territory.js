const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

async function deepAnalyzeTerritory() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  
  // Let's examine Territory 025 in detail
  const fileName = 'Terr. 025.xlsx';
  const filePath = path.join(territoriosDir, fileName);
  
  console.log(`🔍 Deep analysis of ${fileName}...\n`);
  
  try {
    const workbook = XLSX.readFile(filePath);
    const sheetNames = workbook.SheetNames;
    
    for (const sheetName of sheetNames) {
      console.log(`📋 Sheet: ${sheetName}`);
      const sheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      
      console.log(`  Total rows: ${data.length}`);
      
      // Show all rows with content
      console.log(`  All rows with content:`);
      for (let i = 0; i < Math.min(data.length, 30); i++) {
        const row = data[i];
        if (row && row.length > 0 && row.some(cell => cell && cell.toString().trim())) {
          const rowContent = row.map(cell => cell ? `"${cell}"` : '""').join(', ');
          console.log(`    Row ${i + 1}: [${rowContent}]`);
        }
      }
      
      // Look for address patterns more broadly
      console.log(`\n  Looking for address patterns:`);
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (!row || row.length === 0) continue;
        
        for (let j = 0; j < row.length; j++) {
          const cell = row[j];
          if (!cell) continue;
          
          const cellStr = cell.toString().trim();
          // Look for various address patterns
          if (
            /^\d+\s+[A-Z]/.test(cellStr) || 
            /^\d+\s+(NW|SW|NE|SE|W|E|N|S)/.test(cellStr) ||
            /\d+.*Miami.*FL/.test(cellStr) ||
            /^\d{3,5}\s/.test(cellStr)
          ) {
            console.log(`    Found potential address at Row ${i + 1}, Col ${j + 1}: "${cellStr}"`);
          }
        }
      }
      
      console.log('\n');
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

deepAnalyzeTerritory();
