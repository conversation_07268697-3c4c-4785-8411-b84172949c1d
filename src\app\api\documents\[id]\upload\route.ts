import { NextRequest, NextResponse } from 'next/server';
import { EnhancedDocumentService } from '@/lib/services/enhancedDocumentService';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { writeFile, unlink } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * PUT /api/documents/[id]/upload
 * Replace the file for an existing document
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Check if user has permission to update documents
    if (!['elder', 'ministerial_servant', 'coordinator'].includes(user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = params;

    // Get the existing document
    const existingDocument = await EnhancedDocumentService.getDocumentById(
      id,
      user.congregationId,
      user.role
    );

    if (!existingDocument) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const title = formData.get('title') as string;
    const category = formData.get('category') as string;
    const visibility = formData.get('visibility') as string;
    const uploadDate = formData.get('uploadDate') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    if (file.type !== 'application/pdf') {
      return NextResponse.json({ error: 'Only PDF files are allowed' }, { status: 400 });
    }

    // Generate new filename
    const fileExtension = '.pdf';
    const timestamp = Date.now();
    const uniqueId = uuidv4().slice(0, 8);
    const sanitizedTitle = title.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .slice(0, 50);
    const newFilename = `${timestamp}-${uniqueId}-${sanitizedTitle}${fileExtension}`;

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'letters');
    
    // Save new file
    const newFilePath = join(uploadDir, newFilename);
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(newFilePath, buffer);

    // Delete old file if it exists
    try {
      if (existingDocument.filePath && existingDocument.filePath.startsWith('/uploads/')) {
        const oldFilePath = join(process.cwd(), 'public', existingDocument.filePath);
        await unlink(oldFilePath);
      }
    } catch (error) {
      console.warn('Failed to delete old file:', error);
      // Continue even if old file deletion fails
    }

    // Update document in database
    const updateData = {
      title,
      category,
      visibility,
      filename: newFilename,
      filePath: `/uploads/letters/${newFilename}`,
      fileSize: file.size,
      mimeType: file.type,
      uploadDate: uploadDate ? new Date(uploadDate) : undefined,
    };

    const updatedDocument = await EnhancedDocumentService.updateDocument(
      id,
      updateData,
      user.congregationId,
      user.userId
    );

    return NextResponse.json({ 
      document: updatedDocument,
      message: 'Document updated successfully'
    });

  } catch (error) {
    console.error('Error updating document file:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update document file' },
      { status: 500 }
    );
  }
}
