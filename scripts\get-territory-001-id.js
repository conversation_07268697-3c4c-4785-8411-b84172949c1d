const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function getTerritoryId() {
  try {
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        id: true,
        territoryNumber: true,
        boundaries: true
      }
    });

    if (territory) {
      console.log('Territory 001 ID:', territory.id);
      console.log('Has boundaries:', !!territory.boundaries);
      if (territory.boundaries) {
        console.log('Boundary type:', territory.boundaries.type);
      }
    } else {
      console.log('Territory 001 not found');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getTerritoryId();
