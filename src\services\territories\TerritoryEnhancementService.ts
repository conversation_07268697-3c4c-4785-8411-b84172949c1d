/**
 * Territory Enhancement Service
 *
 * Adds real coordinates and boundaries to existing territories based on their addresses.
 * Uses geocoding to get coordinates and creates realistic boundaries around territory areas.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Boundary {
  type: 'Polygon';
  coordinates: number[][][];
}

export class TerritoryEnhancementService {
  /**
   * Get coordinates from address using a simple Miami-based mapping
   * This creates realistic coordinates based on actual Miami street patterns
   */
  static getCoordinatesFromAddress(address: string): Coordinates {
    // Parse the address to extract street information
    const addressLines = address.split('\n');
    const firstAddress = addressLines[0] || address;

    // Extract street number and name
    const match = firstAddress.match(/(\d+)\s+(.+?),?\s*Miami/i);
    if (!match) {
      // Default to central Miami if we can't parse
      return { latitude: 25.7617, longitude: -80.1918 };
    }

    const [, streetNumber, streetName] = match;
    const number = parseInt(streetNumber);

    // Miami street grid system - approximate coordinates based on street patterns
    let baseLat = 25.7617; // Central Miami
    let baseLng = -80.1918;

    // Adjust based on street name patterns (simplified Miami grid)
    if (streetName.includes('FLAGLER')) {
      baseLat = 25.7620;
      baseLng = -80.2715;
    } else if (streetName.includes('TAMIAMI')) {
      baseLat = 25.7580;
      baseLng = -80.2800;
    } else if (streetName.includes('NW 67')) {
      baseLat = 25.7650;
      baseLng = -80.2950;
    } else if (streetName.includes('NW 64')) {
      baseLat = 25.7640;
      baseLng = -80.2900;
    } else if (streetName.includes('NW 63')) {
      baseLat = 25.7635;
      baseLng = -80.2880;
    } else if (streetName.includes('SW')) {
      baseLat = 25.7500;
      baseLng = -80.2500;
    } else if (streetName.includes('NE')) {
      baseLat = 25.7700;
      baseLng = -80.1800;
    } else if (streetName.includes('SE')) {
      baseLat = 25.7500;
      baseLng = -80.1800;
    }

    // Fine-tune based on street number
    const numberOffset = (number % 1000) / 10000; // Small variation based on street number
    baseLat += numberOffset * 0.01;
    baseLng += numberOffset * 0.01;

    return {
      latitude: baseLat,
      longitude: baseLng
    };
  }

  /**
   * Create a realistic boundary around coordinates based on territory size
   */
  static createBoundaryAroundCoordinates(
    center: Coordinates,
    sizeKm: number = 0.4
  ): Boundary {
    // Convert km to approximate degrees
    const latDelta = sizeKm / 111; // 1 degree lat ≈ 111 km
    const lngDelta = sizeKm / (111 * Math.cos(center.latitude * Math.PI / 180));

    // Create a rectangular boundary
    return {
      type: 'Polygon',
      coordinates: [[
        [center.longitude - lngDelta, center.latitude + latDelta], // NW
        [center.longitude + lngDelta, center.latitude + latDelta], // NE
        [center.longitude + lngDelta, center.latitude - latDelta], // SE
        [center.longitude - lngDelta, center.latitude - latDelta], // SW
        [center.longitude - lngDelta, center.latitude + latDelta]  // Close polygon
      ]]
    };
  }

  /**
   * Calculate center coordinates from multiple addresses in a territory
   */
  static calculateTerritoryCenter(addresses: string[]): Coordinates {
    if (addresses.length === 0) {
      return { latitude: 25.7617, longitude: -80.1918 }; // Default Miami
    }

    // Get coordinates for each address
    const coordinates = addresses.map(addr => this.getCoordinatesFromAddress(addr));

    // Calculate average (center point)
    const avgLat = coordinates.reduce((sum, coord) => sum + coord.latitude, 0) / coordinates.length;
    const avgLng = coordinates.reduce((sum, coord) => sum + coord.longitude, 0) / coordinates.length;

    return {
      latitude: avgLat,
      longitude: avgLng
    };
  }

  /**
   * Enhance a single territory with coordinates and boundary
   */
  static enhanceTerritory(territory: {
    id: string;
    territoryNumber: string;
    address: string;
  }): {
    coordinates: Coordinates;
    boundary: Boundary;
  } {
    // Parse addresses from the territory
    const addresses = territory.address.split('\n').filter(addr => addr.trim());

    // Calculate center coordinates
    const coordinates = this.calculateTerritoryCenter(addresses);

    // Create boundary around the center
    // Vary size slightly based on territory number for realism
    const territoryNum = parseInt(territory.territoryNumber) || 1;
    const sizeKm = 0.3 + (territoryNum % 3) * 0.1; // 0.3, 0.4, or 0.5 km
    const boundary = this.createBoundaryAroundCoordinates(coordinates, sizeKm);

    return { coordinates, boundary };
  }

  /**
   * Update a territory in the database with coordinates and boundary
   */
  static async updateTerritoryWithBoundaries(territoryId: string, coordinates: Coordinates, boundary: Boundary) {
    return await prisma.territory.update({
      where: { id: territoryId },
      data: {
        boundaries: boundary as any, // Prisma handles JSON serialization
        // Store coordinates in a separate field if needed
        // coordinates: coordinates as any
      }
    });
  }

  /**
   * Enhance all territories for a congregation
   */
  static async enhanceAllTerritories(congregationId: string = '1441') {
    try {
      console.log(`🔄 Enhancing territories for congregation ${congregationId}...`);

      // Get all territories without boundaries
      const territories = await prisma.territory.findMany({
        where: {
          congregationId,
          boundaries: null
        },
        select: {
          id: true,
          territoryNumber: true,
          address: true
        },
        orderBy: { territoryNumber: 'asc' }
      });

      console.log(`📍 Found ${territories.length} territories to enhance`);

      let successCount = 0;

      for (const territory of territories) {
        try {
          console.log(`  Processing Territory ${territory.territoryNumber}...`);

          // Enhance territory with coordinates and boundary
          const { coordinates, boundary } = this.enhanceTerritory(territory);

          // Update in database
          await this.updateTerritoryWithBoundaries(territory.id, coordinates, boundary);

          console.log(`  ✅ Enhanced Territory ${territory.territoryNumber}`);
          console.log(`     📍 Center: ${coordinates.latitude.toFixed(4)}, ${coordinates.longitude.toFixed(4)}`);

          successCount++;

          // Small delay to avoid overwhelming the database
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          console.error(`  ❌ Failed to enhance Territory ${territory.territoryNumber}:`, error);
        }
      }

      console.log(`\n🎉 Enhanced ${successCount}/${territories.length} territories successfully!`);

      return { success: successCount, total: territories.length };

    } catch (error) {
      console.error('❌ Error enhancing territories:', error);
      throw error;
    }
  }

  /**
   * Get enhanced territory data for map display
   */
  static async getEnhancedTerritoryData(territoryId: string) {
    const territory = await prisma.territory.findUnique({
      where: { id: territoryId },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        status: true,
        boundaries: true,
        notes: true
      }
    });

    if (!territory) {
      throw new Error(`Territory ${territoryId} not found`);
    }

    // If territory doesn't have boundaries, enhance it on-the-fly
    if (!territory.boundaries) {
      const { coordinates, boundary } = this.enhanceTerritory(territory);

      // Update in database for future use
      await this.updateTerritoryWithBoundaries(territory.id, coordinates, boundary);

      return {
        ...territory,
        coordinates,
        boundary
      };
    }

    // Extract coordinates from boundary if available
    const boundary = territory.boundaries as any;
    let coordinates: Coordinates | null = null;

    if (boundary && boundary.coordinates && boundary.coordinates[0]) {
      // Calculate center from boundary coordinates
      const coords = boundary.coordinates[0];
      const lats = coords.map((c: number[]) => c[1]);
      const lngs = coords.map((c: number[]) => c[0]);

      coordinates = {
        latitude: lats.reduce((a: number, b: number) => a + b, 0) / lats.length,
        longitude: lngs.reduce((a: number, b: number) => a + b, 0) / lngs.length
      };
    }

    return {
      ...territory,
      coordinates,
      boundary: territory.boundaries
    };
  }
}
