/**
 * Member Form Component
 *
 * Form for creating and editing member profiles with validation,
 * role selection, and proper error handling.
 */

import React, { useState, useEffect } from 'react';
import { MemberProfile } from '@/lib/services/memberManagementService';

interface MemberFormProps {
  member?: MemberProfile | null;
  onSubmit: (data: MemberFormData) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export interface MemberFormData {
  name: string;
  email: string;
  role: string;
  pin?: string;
  isActive?: boolean;
  reason?: string;
}

const ROLES = [
  { value: 'publisher', label: 'Publicador' },
  { value: 'ministerial_servant', label: 'Siervo Ministerial' },
  { value: 'elder', label: '<PERSON><PERSON><PERSON>' },
  { value: 'overseer_coordinator', label: 'Superintendente/Coordinador' },
];

export default function MemberForm({
  member,
  onSubmit,
  onCancel,
  isLoading,
}: MemberFormProps) {
  const [formData, setFormData] = useState<MemberFormData>({
    name: '',
    email: '',
    role: 'publisher',
    pin: '',
    isActive: true,
    reason: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPin, setShowPin] = useState(false);

  const isEditing = !!member;

  useEffect(() => {
    if (member) {
      setFormData({
        name: member.name,
        email: member.email,
        role: member.role,
        pin: '',
        isActive: member.isActive,
        reason: '',
      });
    }
  }, [member]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'El nombre es requerido';
    } else if (formData.name.length > 100) {
      newErrors.name = 'El nombre es demasiado largo';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'El email es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Formato de email inválido';
    }

    if (!formData.role) {
      newErrors.role = 'El rol es requerido';
    }

    if (!isEditing && !formData.pin) {
      newErrors.pin = 'El PIN es requerido para nuevos miembros';
    } else if (formData.pin && formData.pin.length < 4) {
      newErrors.pin = 'El PIN debe tener al menos 4 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const submitData: MemberFormData = {
      name: formData.name.trim(),
      email: formData.email.trim(),
      role: formData.role,
      reason: formData.reason?.trim() || undefined,
    };

    if (isEditing) {
      submitData.isActive = formData.isActive;
      if (formData.pin) {
        submitData.pin = formData.pin;
      }
    } else {
      submitData.pin = formData.pin;
    }

    onSubmit(submitData);
  };

  const handleInputChange = (field: keyof MemberFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {isEditing ? `Editar Miembro: ${member?.name}` : 'Nuevo Miembro'}
      </h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nombre Completo *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Ingresa el nombre completo"
            maxLength={100}
          />
          {errors.name && (
            <p className="text-red-600 text-sm mt-1">{errors.name}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Correo Electrónico *
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.email ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="<EMAIL>"
            maxLength={255}
          />
          {errors.email && (
            <p className="text-red-600 text-sm mt-1">{errors.email}</p>
          )}
        </div>

        {/* Role */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Rol en la Congregación *
          </label>
          <select
            value={formData.role}
            onChange={(e) => handleInputChange('role', e.target.value)}
            className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.role ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            {ROLES.map(role => (
              <option key={role.value} value={role.value}>
                {role.label}
              </option>
            ))}
          </select>
          {errors.role && (
            <p className="text-red-600 text-sm mt-1">{errors.role}</p>
          )}
        </div>

        {/* PIN */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            PIN {isEditing ? '(dejar vacío para mantener actual)' : '*'}
          </label>
          <div className="relative">
            <input
              type={showPin ? 'text' : 'password'}
              value={formData.pin}
              onChange={(e) => handleInputChange('pin', e.target.value)}
              className={`w-full border rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.pin ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder={isEditing ? 'Nuevo PIN (opcional)' : 'PIN de acceso'}
              maxLength={50}
            />
            <button
              type="button"
              onClick={() => setShowPin(!showPin)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
            >
              {showPin ? '👁️' : '👁️‍🗨️'}
            </button>
          </div>
          {errors.pin && (
            <p className="text-red-600 text-sm mt-1">{errors.pin}</p>
          )}
        </div>

        {/* Active Status (only for editing) */}
        {isEditing && (
          <div>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.isActive}
                onChange={(e) => handleInputChange('isActive', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">
                Miembro activo
              </span>
            </label>
            <p className="text-xs text-gray-500 mt-1">
              Los miembros inactivos no pueden acceder al sistema
            </p>
          </div>
        )}

        {/* Reason */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Razón del Cambio (Opcional)
          </label>
          <textarea
            value={formData.reason}
            onChange={(e) => handleInputChange('reason', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
            placeholder="Describe la razón de este cambio..."
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            Esta información se guardará en el historial de cambios
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            disabled={isLoading}
          >
            {isLoading ? 'Guardando...' : (isEditing ? 'Actualizar' : 'Crear Miembro')}
          </button>
        </div>
      </form>
    </div>
  );
}
