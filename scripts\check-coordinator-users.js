const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCoordinatorUsers() {
  try {
    console.log('🔍 Checking for coordinator-level users...\n');

    // Check for coordinator users
    const coordinators = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        role: 'coordinator',
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    console.log('👑 Coordinator Users:');
    if (coordinators.length > 0) {
      coordinators.forEach(user => {
        console.log(`  - ${user.name} (${user.email}) - Role: ${user.role}`);
      });
    } else {
      console.log('  ❌ No coordinator users found');
    }

    // Check for elder users (who also have admin access)
    const elders = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        role: 'elder',
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
      take: 3, // Just show first 3
    });

    console.log('\n👨‍💼 Elder Users (also have admin access):');
    if (elders.length > 0) {
      elders.forEach(user => {
        console.log(`  - ${user.name} (${user.email}) - Role: ${user.role}`);
      });
    } else {
      console.log('  ❌ No elder users found');
    }

    // Check for developer users (highest access)
    const developers = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        role: 'developer',
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    console.log('\n🛠️ Developer Users (highest access):');
    if (developers.length > 0) {
      developers.forEach(user => {
        console.log(`  - ${user.name} (${user.email}) - Role: ${user.role}`);
      });
    } else {
      console.log('  ❌ No developer users found');
    }

    console.log('\n📝 Note: All these users should have PIN "1234" for testing');
    console.log('🔐 Congregation ID: 1441');
    console.log('🌐 Access the app at: http://localhost:3001');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCoordinatorUsers();
