/**
 * Songs API Endpoint
 *
 * Handles CRUD operations for song management including:
 * - Getting song catalog with search and filtering
 * - Updating song titles in multiple languages
 * - Managing song categories and status
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

// Validation schemas
const getSongsSchema = z.object({
  search: z.string().optional(),
  language: z.enum(['es', 'en']).optional(),
  category: z.string().optional(),
  isActive: z.boolean().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(50),
});

const updateSongSchema = z.object({
  songNumber: z.number().int().positive(),
  titleEs: z.string().min(1).max(255).optional(),
  titleEn: z.string().min(1).max(255).optional(),
  category: z.string().max(100).optional(),
  isActive: z.boolean().optional(),
});

/**
 * GET /api/songs
 * Get songs with optional search and filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Debug: Log the authorization header
    const authHeader = request.headers.get('authorization');
    console.log('Songs API - Authorization header:', authHeader ? 'Present' : 'Missing');

    // Verify authentication
    const authResult = await extractAndVerifyToken(request);
    console.log('Songs API - Auth result:', {
      success: authResult.success,
      error: authResult.error,
      statusCode: authResult.statusCode
    });

    if (!authResult.success || !authResult.user) {
      console.log('Songs API - Authentication failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.statusCode || 401 }
      );
    }

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());

    const validationResult = getSongsSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { search, language, category, isActive, page, limit } = validationResult.data;
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (category) {
      where.category = category;
    }

    if (search) {
      const searchConditions = [];

      // Search by song number
      const songNumber = parseInt(search);
      if (!isNaN(songNumber)) {
        searchConditions.push({ songNumber });
      }

      // Search by title based on language preference
      if (!language || language === 'es') {
        searchConditions.push({
          titleEs: {
            contains: search,
            mode: 'insensitive' as const,
          },
        });
      }

      if (!language || language === 'en') {
        searchConditions.push({
          titleEn: {
            contains: search,
            mode: 'insensitive' as const,
          },
        });
      }

      where.OR = searchConditions;
    }

    // Get songs with pagination
    const [songs, total] = await Promise.all([
      prisma.song.findMany({
        where,
        orderBy: { songNumber: 'asc' },
        skip,
        take: limit,
        select: {
          id: true,
          songNumber: true,
          titleEs: true,
          titleEn: true,
          category: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      }),
      prisma.song.count({ where }),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      songs,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    });

  } catch (error) {
    console.error('Error fetching songs:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/songs
 * Update a song's information
 */
export async function PUT(request: NextRequest) {
  try {
    // Verify authentication and admin permissions
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!['overseer', 'elder', 'ministerial_servant'].includes(authResult.user.role)) {
      return NextResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const validationResult = updateSongSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { songNumber, titleEs, titleEn, category, isActive } = validationResult.data;

    // Check if song exists
    const existingSong = await prisma.song.findUnique({
      where: { songNumber },
    });

    if (!existingSong) {
      return NextResponse.json(
        { error: 'Song not found' },
        { status: 404 }
      );
    }

    // Update song
    const updatedSong = await prisma.song.update({
      where: { songNumber },
      data: {
        ...(titleEs !== undefined && { titleEs }),
        ...(titleEn !== undefined && { titleEn }),
        ...(category !== undefined && { category }),
        ...(isActive !== undefined && { isActive }),
      },
      select: {
        id: true,
        songNumber: true,
        titleEs: true,
        titleEn: true,
        category: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({
      song: updatedSong,
      message: 'Song updated successfully',
    });

  } catch (error) {
    console.error('Error updating song:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
