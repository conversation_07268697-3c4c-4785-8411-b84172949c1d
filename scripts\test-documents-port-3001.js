/**
 * Test the documents API on port 3001 with proper authentication
 */

const BASE_URL = 'http://localhost:3001';

async function authenticateAndTestDocuments() {
  console.log('🚀 Testing Documents API on port 3001...\n');
  
  try {
    // Step 1: Authenticate
    console.log('🔐 Step 1: Authenticating...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/congregation-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1234'
      }),
    });

    console.log(`📡 Login response status: ${loginResponse.status}`);
    
    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      console.log(`❌ Login failed: ${errorText}`);
      return false;
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log(`✅ Authentication successful, token received: ${token ? 'Yes' : 'No'}`);
    
    if (!token) {
      console.log('❌ No token received from login');
      return false;
    }

    // Step 2: Test documents API with authentication
    console.log('\n📄 Step 2: Testing Documents API...');
    const documentsResponse = await fetch(`${BASE_URL}/api/documents?category=letters`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    console.log(`📡 Documents API response status: ${documentsResponse.status}`);
    
    if (!documentsResponse.ok) {
      const errorText = await documentsResponse.text();
      console.log(`❌ Documents API failed: ${errorText}`);
      return false;
    }

    const documentsData = await documentsResponse.json();
    console.log(`✅ Documents API successful`);
    console.log(`📊 Response data:`, JSON.stringify(documentsData, null, 2));
    
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

async function runTest() {
  const success = await authenticateAndTestDocuments();
  
  console.log('\n📋 Test Summary:');
  if (success) {
    console.log('🎉 All tests passed! The documents API is working correctly on port 3001.');
    console.log('💡 The frontend needs to be updated to use port 3001 or the server needs to run on port 3000.');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
  }
}

runTest().catch(console.error);
