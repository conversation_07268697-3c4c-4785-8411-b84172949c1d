/**
 * Field Service Records API Endpoint
 * 
 * Handles CRUD operations for field service records including
 * record entry, submission, and history retrieval.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { FieldServiceManagementService, ServiceRecordInput } from '@/lib/services/fieldServiceManagementService';

// Validation schema for service record creation/update
const ServiceRecordSchema = z.object({
  serviceMonth: z.string().regex(/^\d{4}-\d{2}$/, 'Service month must be in YYYY-MM format'),
  hours: z.number().min(0).max(999).optional(),
  placements: z.number().int().min(0).max(9999).optional(),
  videoShowings: z.number().int().min(0).max(9999).optional(),
  returnVisits: z.number().int().min(0).max(9999).optional(),
  bibleStudies: z.number().int().min(0).max(999).optional(),
  notes: z.string().max(1000).optional(),
});

// Validation schema for GET requests
const GetServiceRecordSchema = z.object({
  serviceMonth: z.string().regex(/^\d{4}-\d{2}$/, 'Service month must be in YYYY-MM format').optional(),
  memberId: z.string().optional(),
  history: z.string().transform(val => val === 'true').optional(),
  limit: z.string().transform(val => parseInt(val, 10)).optional(),
});

/**
 * GET /api/field-service
 * Retrieve service records for the authenticated member
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = GetServiceRecordSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { serviceMonth, memberId, history, limit } = validationResult.data;

    // Determine target member (self or specified member for coordinators)
    let targetMemberId = member.id;
    if (memberId) {
      // Only elders and ministerial servants can view other members' records
      if (!['elder', 'ministerial_servant'].includes(member.role)) {
        return NextResponse.json(
          { error: 'Insufficient permissions to view other members\' records' },
          { status: 403 }
        );
      }
      targetMemberId = memberId;
    }

    if (history) {
      // Get service history
      const records = await FieldServiceManagementService.getMemberServiceHistory(
        member.congregationId,
        targetMemberId,
        limit || 12
      );

      return NextResponse.json({
        success: true,
        records,
        count: records.length,
        memberId: targetMemberId,
      });
    } else if (serviceMonth) {
      // Get specific month record
      const record = await FieldServiceManagementService.getServiceRecord(
        member.congregationId,
        targetMemberId,
        serviceMonth
      );

      return NextResponse.json({
        success: true,
        record,
        serviceMonth,
        memberId: targetMemberId,
      });
    } else {
      // Get current month record by default
      const currentMonth = FieldServiceManagementService.getCurrentServiceMonth();
      const record = await FieldServiceManagementService.getServiceRecord(
        member.congregationId,
        targetMemberId,
        currentMonth
      );

      return NextResponse.json({
        success: true,
        record,
        serviceMonth: currentMonth,
        memberId: targetMemberId,
      });
    }

  } catch (error) {
    console.error('Field service GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve service records',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/field-service
 * Create or update a service record
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = ServiceRecordSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const recordData: ServiceRecordInput = validationResult.data;

    // Additional validation using service
    const serviceValidation = FieldServiceManagementService.validateServiceRecord(recordData);
    if (!serviceValidation.isValid) {
      return NextResponse.json(
        {
          error: 'Invalid service record data',
          details: serviceValidation.errors,
        },
        { status: 400 }
      );
    }

    // Create or update the service record
    const record = await FieldServiceManagementService.upsertServiceRecord(
      member.congregationId,
      member.id,
      recordData
    );

    return NextResponse.json({
      success: true,
      record,
      message: 'Service record saved successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Field service POST error:', error);
    
    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('Cannot modify submitted')) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
    }
    
    return NextResponse.json(
      {
        error: 'Failed to save service record',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/field-service
 * Submit a service record (mark as submitted)
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { serviceMonth } = body;

    if (!serviceMonth || !/^\d{4}-\d{2}$/.test(serviceMonth)) {
      return NextResponse.json(
        { error: 'Valid service month (YYYY-MM) is required' },
        { status: 400 }
      );
    }

    // Submit the service record
    const record = await FieldServiceManagementService.submitServiceRecord(
      member.congregationId,
      member.id,
      serviceMonth
    );

    return NextResponse.json({
      success: true,
      record,
      message: 'Service record submitted successfully',
    });

  } catch (error) {
    console.error('Field service PUT error:', error);
    
    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('already submitted')) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
    }
    
    return NextResponse.json(
      {
        error: 'Failed to submit service record',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT to submit records.' },
    { status: 405 }
  );
}
