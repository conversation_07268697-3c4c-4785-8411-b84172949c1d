#!/usr/bin/env node

/**
 * Complete Data Replacement Script
 * Replaces development data with real MySQL data
 * Includes: members, roles, role_permissions, midweek parts, sections, settings
 */

const fs = require('fs').promises;
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class DataReplacementMigrator {
  constructor() {
    this.stats = {
      tablesProcessed: 0,
      recordsMigrated: 0,
      recordsDeleted: 0,
      errors: [],
      startTime: null,
      endTime: null,
    };
  }

  cleanValue(value) {
    if (!value) return null;
    if (value === 'NULL' || value === null) return null;
    return value.replace(/^'|'$/g, '').replace(/''/g, "'");
  }

  async parseSQLDump() {
    console.log('📖 Reading MySQL dump file...');

    try {
      const sqlContent = await fs.readFile('mysqldb.sql', 'utf8');

      // Extract INSERT statements for specific tables
      const insertRegex = /INSERT INTO `(\w+)` \([^)]+\) VALUES\s*([^;]+);/g;
      const tableData = {};

      let match;
      while ((match = insertRegex.exec(sqlContent)) !== null) {
        const tableName = match[1];
        const valuesString = match[2];

        if (!tableData[tableName]) {
          tableData[tableName] = [];
        }

        // Parse values - simplified parser
        const valueMatches = valuesString.match(/\([^)]+\)/g);
        if (valueMatches) {
          valueMatches.forEach(valueMatch => {
            const values = valueMatch.slice(1, -1).split(',').map(v => v.trim());
            tableData[tableName].push(values);
          });
        }
      }

      console.log(`✅ Found data for ${Object.keys(tableData).length} tables`);
      return tableData;

    } catch (error) {
      console.error('❌ Error reading SQL dump:', error.message);
      throw error;
    }
  }

  async cleanExistingData() {
    console.log('\n🧹 Cleaning existing development data...');

    try {
      // Delete in order to respect foreign key constraints
      console.log('Deleting member change history...');
      await prisma.memberChangeHistory.deleteMany({});

      console.log('Deleting pin change history...');
      await prisma.pinChangeHistory.deleteMany({});

      console.log('Deleting elder permissions...');
      await prisma.elderPermission.deleteMany({});

      console.log('Deleting task assignments...');
      await prisma.taskAssignment.deleteMany({});

      console.log('Deleting tasks...');
      await prisma.task.deleteMany({});

      console.log('Deleting letters...');
      await prisma.letter.deleteMany({});

      console.log('Deleting service groups...');
      await prisma.serviceGroup.deleteMany({});

      console.log('Deleting members (keeping congregation)...');
      // Keep congregations but delete members
      const membersDeleted = await prisma.member.deleteMany({
        where: {
          congregationId: {
            in: ['CORALOES', 'TESTCONG'] // Keep these but delete their members
          }
        }
      });

      console.log('Deleting roles...');
      await prisma.role.deleteMany({});

      this.stats.recordsDeleted += membersDeleted.count;
      console.log(`✅ Cleaned existing data (${membersDeleted.count} members deleted)`);

    } catch (error) {
      console.error('❌ Error cleaning existing data:', error.message);
      this.stats.errors.push({ table: 'cleanup', error: error.message });
    }
  }

  async migrateRoles(data) {
    console.log('\n📋 Migrating roles...');

    if (!data.roles) {
      console.log('⚠️ No role data found');
      return;
    }

    try {
      for (const row of data.roles) {
        // Format: (id, name, description, created_at, updated_at)
        const [id, name, description, createdAt, updatedAt] = row;

        const cleanName = this.cleanValue(name);
        const cleanDescription = this.cleanValue(description);

        if (!cleanName) {
          console.warn(`⚠️ Skipping role with missing name`);
          continue;
        }

        await prisma.role.upsert({
          where: { name: cleanName },
          update: {
            description: cleanDescription,
            updatedAt: new Date(),
          },
          create: {
            name: cleanName,
            description: cleanDescription,
            permissions: [],
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        this.stats.recordsMigrated++;
        console.log(`✅ Migrated role: ${cleanName}`);
      }

      console.log(`✅ Migrated ${data.roles.length} roles`);
      this.stats.tablesProcessed++;

    } catch (error) {
      console.error('❌ Error migrating roles:', error.message);
      this.stats.errors.push({ table: 'roles', error: error.message });
    }
  }

  async migrateMembers(data) {
    console.log('\n👥 Migrating real members...');

    if (!data.members) {
      console.log('⚠️ No member data found');
      return;
    }

    try {
      // Get the congregation for members
      const congregation = await prisma.congregation.findFirst({
        where: { id: '1441' }
      });

      if (!congregation) {
        console.error('❌ No congregation found for members');
        return;
      }

      for (const row of data.members) {
        // Format: (id, username, password, name, email, phone, role_id, is_active, last_login, created_at, updated_at, congregation_id)
        const [id, username, password, name, email, phone, roleId, isActive, lastLogin, createdAt, updatedAt, congregationId] = row;

        const cleanId = this.cleanValue(id);
        const cleanUsername = this.cleanValue(username);
        const cleanName = this.cleanValue(name);
        const cleanEmail = this.cleanValue(email);
        const cleanPassword = this.cleanValue(password);
        const cleanRoleId = this.cleanValue(roleId);

        if (!cleanName || !cleanPassword) {
          console.warn(`⚠️ Skipping member with missing data: ${cleanName}`);
          continue;
        }

        // Map role_id to role name
        let roleName = 'publisher'; // default
        if (cleanRoleId === '1') roleName = 'overseer_coordinator';
        else if (cleanRoleId === '2') roleName = 'elder';
        else if (cleanRoleId === '3') roleName = 'ministerial_servant';
        else if (cleanRoleId === '4') roleName = 'publisher';
        else if (cleanRoleId === '5') roleName = 'developer';

        await prisma.member.create({
          data: {
            id: `1441_${cleanUsername}`, // Create consistent ID
            congregationId: congregation.id,
            name: cleanName,
            email: cleanEmail ? `${cleanUsername}@coraleste.local` : null, // Make email unique
            role: roleName,
            pin: cleanPassword, // Using password as PIN
            isActive: this.cleanValue(isActive) === '1',
            preferences: {},
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        this.stats.recordsMigrated++;
        console.log(`✅ Migrated member: ${cleanName} (${roleName})`);
      }

      console.log(`✅ Migrated ${data.members.length} real members`);
      this.stats.tablesProcessed++;

    } catch (error) {
      console.error('❌ Error migrating members:', error.message);
      this.stats.errors.push({ table: 'members', error: error.message });
    }
  }

  async migrateCongregationSettings(data) {
    console.log('\n⚙️ Migrating congregation settings...');

    if (!data.congregation_settings) {
      console.log('⚠️ No congregation settings data found');
      return;
    }

    try {
      const congregation = await prisma.congregation.findFirst({
        where: { id: '1441' }
      });

      if (!congregation) {
        console.error('❌ No congregation found for settings');
        return;
      }

      for (const row of data.congregation_settings) {
        // Format: (id, congregation_id, setting_key, setting_value, created_at, updated_at)
        const [id, congregationId, settingKey, settingValue, createdAt, updatedAt] = row;

        const cleanKey = this.cleanValue(settingKey);
        const cleanValue = this.cleanValue(settingValue);

        if (!cleanKey) {
          console.warn(`⚠️ Skipping setting with missing key`);
          continue;
        }

        await prisma.congregationSetting.upsert({
          where: {
            congregationId_settingKey: {
              congregationId: congregation.id,
              settingKey: cleanKey
            }
          },
          update: {
            settingValue: cleanValue,
            updatedAt: new Date(),
          },
          create: {
            congregationId: congregation.id,
            settingKey: cleanKey,
            settingValue: cleanValue,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        this.stats.recordsMigrated++;
        console.log(`✅ Migrated setting: ${cleanKey} = ${cleanValue}`);
      }

      console.log(`✅ Migrated ${data.congregation_settings.length} congregation settings`);
      this.stats.tablesProcessed++;

    } catch (error) {
      console.error('❌ Error migrating congregation settings:', error.message);
      this.stats.errors.push({ table: 'congregation_settings', error: error.message });
    }
  }

  async migrateServiceGroups(data) {
    console.log('\n👨‍👩‍👧‍👦 Re-migrating service groups...');

    if (!data.service_groups) {
      console.log('⚠️ No service group data found');
      return;
    }

    try {
      const congregation = await prisma.congregation.findFirst({
        where: { id: '1441' }
      });

      if (!congregation) {
        console.error('❌ No congregation found for service groups');
        return;
      }

      for (const row of data.service_groups) {
        const [id, name, groupNumber, createdAt, updatedAt] = row;

        const cleanName = this.cleanValue(name);
        const cleanGroupNumber = parseInt(this.cleanValue(groupNumber));

        if (!cleanName || !cleanGroupNumber) {
          console.warn(`⚠️ Skipping service group with missing data: ${cleanName}`);
          continue;
        }

        await prisma.serviceGroup.upsert({
          where: {
            congregationId_groupNumber: {
              congregationId: congregation.id,
              groupNumber: cleanGroupNumber
            }
          },
          update: {
            name: cleanName,
            updatedAt: new Date(),
          },
          create: {
            congregationId: congregation.id,
            name: cleanName,
            groupNumber: cleanGroupNumber,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        this.stats.recordsMigrated++;
      }

      console.log(`✅ Re-migrated ${data.service_groups.length} service groups`);
      this.stats.tablesProcessed++;

    } catch (error) {
      console.error('❌ Error migrating service groups:', error.message);
      this.stats.errors.push({ table: 'service_groups', error: error.message });
    }
  }

  async migrateTasks(data) {
    console.log('\n📋 Re-migrating tasks...');

    if (!data.tasks) {
      console.log('⚠️ No task data found');
      return;
    }

    try {
      const congregation = await prisma.congregation.findFirst({
        where: { id: '1441' }
      });

      if (!congregation) {
        console.error('❌ No congregation found for tasks');
        return;
      }

      for (const row of data.tasks) {
        const [id, name, categoryId, description, status, createdAt, updatedAt, displayOrder] = row;

        const cleanName = this.cleanValue(name);
        const cleanDescription = this.cleanValue(description);
        const cleanStatus = this.cleanValue(status);

        if (!cleanName) {
          console.warn(`⚠️ Skipping task with missing data: ${cleanName}`);
          continue;
        }

        await prisma.task.create({
          data: {
            congregationId: congregation.id,
            title: cleanName,
            description: cleanDescription,
            category: 'general',
            frequency: 'one-time',
            isActive: cleanStatus !== 'completed',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        this.stats.recordsMigrated++;
      }

      console.log(`✅ Re-migrated ${data.tasks.length} tasks`);
      this.stats.tablesProcessed++;

    } catch (error) {
      console.error('❌ Error migrating tasks:', error.message);
      this.stats.errors.push({ table: 'tasks', error: error.message });
    }
  }

  async migrateLetters(data) {
    console.log('\n📄 Re-migrating letters...');

    if (!data.letters) {
      console.log('⚠️ No letter data found');
      return;
    }

    try {
      const congregation = await prisma.congregation.findFirst({
        where: { id: '1441' }
      });

      if (!congregation) {
        console.error('❌ No congregation found for letters');
        return;
      }

      for (const row of data.letters) {
        const [id, filename, title, date, category, visibility, createdAt, updatedAt] = row;

        const cleanFilename = this.cleanValue(filename);
        const cleanTitle = this.cleanValue(title);
        const cleanDate = this.cleanValue(date);
        const cleanCategory = this.cleanValue(category);
        const cleanVisibility = this.cleanValue(visibility);

        if (!cleanFilename || !cleanTitle) {
          console.warn(`⚠️ Skipping letter with missing data: ${cleanTitle}`);
          continue;
        }

        await prisma.letter.create({
          data: {
            congregationId: congregation.id,
            title: cleanTitle,
            filename: cleanFilename,
            filePath: `/uploads/letters/${cleanFilename}`,
            category: cleanCategory,
            visibility: cleanVisibility === 'Todos los Miembros' ? 'ALL_MEMBERS' : 'ELDERS_ONLY',
            uploadDate: cleanDate ? new Date(cleanDate) : new Date(),
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        });

        this.stats.recordsMigrated++;
      }

      console.log(`✅ Re-migrated ${data.letters.length} letters`);
      this.stats.tablesProcessed++;

    } catch (error) {
      console.error('❌ Error migrating letters:', error.message);
      this.stats.errors.push({ table: 'letters', error: error.message });
    }
  }

  async executeFullReplacement() {
    console.log('🚀 Starting complete data replacement with MySQL data...\n');
    this.stats.startTime = new Date();

    try {
      const tableData = await this.parseSQLDump();

      // Step 1: Clean existing fake data
      await this.cleanExistingData();

      // Step 2: Migrate real data
      await this.migrateRoles(tableData);
      await this.migrateMembers(tableData);
      await this.migrateCongregationSettings(tableData);

      // Re-migrate other data that was cleaned
      await this.migrateServiceGroups(tableData);
      await this.migrateTasks(tableData);
      await this.migrateLetters(tableData);

      this.stats.endTime = new Date();
      this.printMigrationSummary();

    } catch (error) {
      console.error('❌ Data replacement failed:', error.message);
      this.stats.errors.push({ table: 'general', error: error.message });
    } finally {
      await prisma.$disconnect();
    }
  }

  printMigrationSummary() {
    const duration = this.stats.endTime - this.stats.startTime;
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);

    console.log('\n' + '='.repeat(60));
    console.log('📊 DATA REPLACEMENT SUMMARY');
    console.log('='.repeat(60));
    console.log(`⏱️  Duration: ${minutes}m ${seconds}s`);
    console.log(`📋 Tables processed: ${this.stats.tablesProcessed}`);
    console.log(`📝 Records migrated: ${this.stats.recordsMigrated}`);
    console.log(`🗑️  Records deleted: ${this.stats.recordsDeleted}`);
    console.log(`❌ Errors: ${this.stats.errors.length}`);

    if (this.stats.errors.length > 0) {
      console.log('\n🚨 ERRORS:');
      this.stats.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.table}: ${error.error}`);
      });
    }

    console.log('\n' + (this.stats.errors.length === 0 ? '✅ Data replacement completed successfully!' : '❌ Data replacement completed with errors'));
    console.log('='.repeat(60));
  }
}

// Main execution
async function main() {
  const migrator = new DataReplacementMigrator();
  await migrator.executeFullReplacement();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = DataReplacementMigrator;
