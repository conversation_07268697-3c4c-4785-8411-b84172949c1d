/**
 * API Route: /api/territories/[id]
 *
 * Handles individual territory operations (GET, PUT, DELETE)
 */

import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { extractAndVerifyToken } from '@/lib/middleware/auth';

const prisma = new PrismaClient();

/**
 * GET /api/territories/[id] - Get a specific territory
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id } = params;

    // Get the territory
    const territory = await prisma.territory.findUnique({
      where: {
        id: id,
        congregationId: authResult.user.congregationId
      },
      include: {
        assignments: {
          include: {
            member: true,
            assignedByMember: true
          },
          orderBy: {
            assignedAt: 'desc'
          }
        }
      }
    });

    if (!territory) {
      return NextResponse.json(
        { error: 'Territory not found' },
        { status: 404 }
      );
    }

    // Get current assignment
    const currentAssignment = territory.assignments.find(
      assignment => assignment.returnedAt === null
    );

    // Parse boundary data and extract coordinates from boundary
    let coordinates = null;
    let boundary = null;

    try {
      // Parse boundary if it exists
      if (territory.boundaries) {
        boundary = typeof territory.boundaries === 'string'
          ? JSON.parse(territory.boundaries)
          : territory.boundaries;

        // Extract center coordinates from boundary for map centering
        if (boundary && boundary.coordinates && boundary.coordinates[0]) {
          const polygonCoords = boundary.coordinates[0];
          if (polygonCoords.length > 0) {
            // Calculate center point of the polygon
            const lats = polygonCoords.map((coord: number[]) => coord[1]);
            const lngs = polygonCoords.map((coord: number[]) => coord[0]);

            const centerLat = lats.reduce((sum: number, lat: number) => sum + lat, 0) / lats.length;
            const centerLng = lngs.reduce((sum: number, lng: number) => sum + lng, 0) / lngs.length;

            coordinates = {
              latitude: centerLat,
              longitude: centerLng
            };
          }
        }
      }
    } catch (parseError) {
      console.warn('Error parsing territory boundary:', parseError);
    }

    const response = {
      id: territory.id,
      territoryNumber: territory.territoryNumber,
      address: territory.address,
      status: territory.status,
      notes: territory.notes,
      displayOrder: territory.displayOrder,
      coordinates,
      boundary,
      assignedMember: currentAssignment ? {
        id: currentAssignment.member.id,
        name: currentAssignment.member.name,
        assignedAt: currentAssignment.assignedAt,
        assignedBy: currentAssignment.assignedByMember.name
      } : null,
      createdAt: territory.createdAt,
      updatedAt: territory.updatedAt
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching territory:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/territories/[id] - Update a territory
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has permission to edit territories
    const userRole = authResult.user.role;
    if (!['elder', 'ministerial_servant', 'coordinator', 'overseer_coordinator', 'developer'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id } = params;
    const body = await request.json();

    console.log('PUT /api/territories/[id] - Request body:', body);

    // Validate the territory exists and belongs to the user's congregation
    const existingTerritory = await prisma.territory.findUnique({
      where: {
        id: id,
        congregationId: authResult.user.congregationId
      }
    });

    if (!existingTerritory) {
      return NextResponse.json(
        { error: 'Territory not found' },
        { status: 404 }
      );
    }

    console.log('Existing territory notes:', existingTerritory.notes);
    console.log('New notes from request:', body.notes);

    // Update the territory
    const updatedTerritory = await prisma.territory.update({
      where: { id: id },
      data: {
        address: body.address || existingTerritory.address,
        notes: body.notes !== undefined ? body.notes : existingTerritory.notes,
        status: body.status || existingTerritory.status,
        updatedAt: new Date()
      }
    });

    console.log('Updated territory notes:', updatedTerritory.notes);

    return NextResponse.json({
      id: updatedTerritory.id,
      territoryNumber: updatedTerritory.territoryNumber,
      address: updatedTerritory.address,
      status: updatedTerritory.status,
      notes: updatedTerritory.notes,
      displayOrder: updatedTerritory.displayOrder,
      createdAt: updatedTerritory.createdAt,
      updatedAt: updatedTerritory.updatedAt
    });

  } catch (error) {
    console.error('Error updating territory:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/territories/[id] - Delete a territory
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has permission to delete territories (only elders and above)
    const userRole = authResult.user.role;
    if (!['elder', 'coordinator', 'overseer_coordinator', 'developer'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id } = params;

    // Validate the territory exists and belongs to the user's congregation
    const existingTerritory = await prisma.territory.findUnique({
      where: {
        id: id,
        congregationId: authResult.user.congregationId
      }
    });

    if (!existingTerritory) {
      return NextResponse.json(
        { error: 'Territory not found' },
        { status: 404 }
      );
    }

    // Delete related assignments first
    await prisma.territoryAssignment.deleteMany({
      where: { territoryId: id }
    });

    // Delete the territory
    await prisma.territory.delete({
      where: { id: id }
    });

    return NextResponse.json({
      message: 'Territory deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting territory:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
