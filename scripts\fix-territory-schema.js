const { PrismaClient } = require('@prisma/client');

async function fixTerritorySchema() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 Starting territory schema fix...\n');
    
    // Step 1: Add missing columns
    console.log('📝 Step 1: Adding missing columns...');
    await prisma.$executeRaw`
      ALTER TABLE territories 
      ADD COLUMN IF NOT EXISTS territory_number VARCHAR(50),
      ADD COLUMN IF NOT EXISTS address TEXT,
      ADD COLUMN IF NOT EXISTS notes TEXT,
      ADD COLUMN IF NOT EXISTS display_order INTEGER
    `;
    console.log('✅ Columns added successfully');
    
    // Step 2: Migrate data from old columns to new columns
    console.log('📝 Step 2: Migrating data...');
    await prisma.$executeRaw`
      UPDATE territories 
      SET 
        territory_number = COALESCE(name, 'Unknown'),
        address = COALESCE(description, 'No address provided')
      WHERE territory_number IS NULL OR address IS NULL
    `;
    console.log('✅ Data migrated successfully');
    
    // Step 3: Make required columns NOT NULL
    console.log('📝 Step 3: Setting NOT NULL constraints...');
    await prisma.$executeRaw`
      ALTER TABLE territories 
      ALTER COLUMN territory_number SET NOT NULL,
      ALTER COLUMN address SET NOT NULL
    `;
    console.log('✅ Constraints added successfully');
    
    // Step 4: Handle duplicates and add unique constraint
    console.log('📝 Step 4: Handling duplicates...');
    
    // First check for duplicates
    const duplicates = await prisma.$queryRaw`
      SELECT territory_number, congregation_id, COUNT(*) as count
      FROM territories 
      GROUP BY territory_number, congregation_id 
      HAVING COUNT(*) > 1
    `;
    
    if (duplicates.length > 0) {
      console.log('⚠️  Found duplicates, fixing...');
      // Fix duplicates by adding suffix
      await prisma.$executeRaw`
        WITH numbered_territories AS (
          SELECT id, territory_number, congregation_id,
                 ROW_NUMBER() OVER (PARTITION BY territory_number, congregation_id ORDER BY created_at) as rn
          FROM territories
        )
        UPDATE territories 
        SET territory_number = territory_number || '-' || (
          SELECT rn::text 
          FROM numbered_territories 
          WHERE numbered_territories.id = territories.id
        )
        WHERE id IN (
          SELECT id 
          FROM numbered_territories 
          WHERE rn > 1
        )
      `;
      console.log('✅ Duplicates fixed');
    }
    
    // Add unique constraint
    try {
      await prisma.$executeRaw`
        ALTER TABLE territories 
        ADD CONSTRAINT territories_territory_number_congregation_id_key 
        UNIQUE (territory_number, congregation_id)
      `;
      console.log('✅ Unique constraint added');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  Unique constraint already exists');
      } else {
        throw error;
      }
    }
    
    // Step 5: Add indexes
    console.log('📝 Step 5: Adding indexes...');
    try {
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS territories_congregation_id_idx ON territories(congregation_id)`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS territories_congregation_id_status_idx ON territories(congregation_id, status)`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS territories_display_order_idx ON territories(display_order)`;
      console.log('✅ Indexes added successfully');
    } catch (error) {
      console.log('ℹ️  Some indexes may already exist');
    }
    
    // Verification
    console.log('\n🔍 Verification:');
    const columns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'territories' 
      ORDER BY ordinal_position
    `;
    
    console.log('📋 Current territories table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    // Check sample data
    const sampleData = await prisma.$queryRaw`
      SELECT id, territory_number, address, notes, status 
      FROM territories 
      LIMIT 3
    `;
    
    console.log('\n📊 Sample data after migration:');
    sampleData.forEach((row, index) => {
      console.log(`  ${index + 1}. Territory ${row.territory_number}: ${row.address?.substring(0, 50)}...`);
      if (row.notes) {
        console.log(`     Notes: ${row.notes.substring(0, 50)}...`);
      }
    });
    
    console.log('\n🎉 Territory schema fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing schema:', error.message);
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixTerritorySchema();
