#!/usr/bin/env node

/**
 * Verify Documentation Updates
 * 
 * Checks that all documentation has been updated to reflect the new permission architecture
 */

const fs = require('fs');
const path = require('path');

async function verifyDocumentationUpdates() {
  console.log('📚 VERIFYING DOCUMENTATION UPDATES');
  console.log('='.repeat(40));
  
  let allGood = true;
  
  try {
    // Test 1: Check key documentation files
    console.log('\n📋 TEST 1: Key Documentation Files');
    allGood &= await checkKeyDocumentationFiles();
    
    // Test 2: Check story files
    console.log('\n📖 TEST 2: Story Files');
    allGood &= await checkStoryFiles();
    
    // Test 3: Check PRD files
    console.log('\n📄 TEST 3: PRD Files');
    allGood &= await checkPRDFiles();
    
    // Test 4: Check for remaining old role references
    console.log('\n🔍 TEST 4: Old Role References');
    allGood &= await checkForOldRoleReferences();
    
    if (allGood) {
      console.log('\n✅ ALL DOCUMENTATION UPDATES VERIFIED');
    } else {
      console.log('\n⚠️ SOME DOCUMENTATION ISSUES FOUND');
    }
    
  } catch (error) {
    console.error('\n❌ VERIFICATION FAILED:', error.message);
  }
}

async function checkKeyDocumentationFiles() {
  let success = true;
  
  // Check main architecture file
  const archPath = path.join(__dirname, '../docs/architecture.md');
  if (fs.existsSync(archPath)) {
    const content = fs.readFileSync(archPath, 'utf8');
    
    const hasCoordinatorRole = content.includes('COORDINATOR');
    const hasNewArchSection = content.includes('New Permission Architecture (2025)');
    const noDeveloperRole = !content.includes('DEVELOPER = \'developer\'');
    const noOverseerRole = !content.includes('OVERSEER_COORDINATOR');
    
    console.log(`   Architecture has COORDINATOR role: ${hasCoordinatorRole ? '✅' : '❌'}`);
    console.log(`   Architecture has new section: ${hasNewArchSection ? '✅' : '❌'}`);
    console.log(`   Architecture removed DEVELOPER: ${noDeveloperRole ? '✅' : '❌'}`);
    console.log(`   Architecture removed OVERSEER_COORDINATOR: ${noOverseerRole ? '✅' : '❌'}`);
    
    success &= hasCoordinatorRole && hasNewArchSection && noDeveloperRole && noOverseerRole;
  } else {
    console.log('   Architecture file missing: ❌');
    success = false;
  }
  
  // Check permission changes summary
  const changesPath = path.join(__dirname, '../docs/permission-architecture-changes.md');
  if (fs.existsSync(changesPath)) {
    console.log('   Permission changes summary exists: ✅');
  } else {
    console.log('   Permission changes summary missing: ❌');
    success = false;
  }
  
  return success;
}

async function checkStoryFiles() {
  let success = true;
  
  // Check Story 2.1 (main delegation story)
  const story21Path = path.join(__dirname, '../docs/stories/2.1.story.md');
  if (fs.existsSync(story21Path)) {
    const content = fs.readFileSync(story21Path, 'utf8');
    
    const hasArchChanges = content.includes('Architectural Changes');
    const hasCoordinatorRef = content.includes('coordinator');
    const hasRemovedDevRole = content.includes('REMOVED DEVELOPER ROLE');
    const noDeveloperRole = !content.includes('developer.*role') || content.includes('Removed Developer Role');
    
    console.log(`   Story 2.1 has architectural changes: ${hasArchChanges ? '✅' : '❌'}`);
    console.log(`   Story 2.1 mentions coordinator: ${hasCoordinatorRef ? '✅' : '❌'}`);
    console.log(`   Story 2.1 documents dev role removal: ${hasRemovedDevRole ? '✅' : '❌'}`);
    
    success &= hasArchChanges && hasCoordinatorRef && hasRemovedDevRole;
  } else {
    console.log('   Story 2.1 missing: ❌');
    success = false;
  }
  
  // Check Story 1.2 (database migration)
  const story12Path = path.join(__dirname, '../docs/stories/1.2.story.md');
  if (fs.existsSync(story12Path)) {
    const content = fs.readFileSync(story12Path, 'utf8');
    
    const hasCoordinatorEnum = content.includes('COORDINATOR');
    const noDeveloperEnum = !content.includes('DEVELOPER') || !content.includes('OVERSEER_COORDINATOR');
    
    console.log(`   Story 1.2 has COORDINATOR enum: ${hasCoordinatorEnum ? '✅' : '❌'}`);
    console.log(`   Story 1.2 removed old roles: ${noDeveloperEnum ? '✅' : '❌'}`);
    
    success &= hasCoordinatorEnum && noDeveloperEnum;
  }
  
  return success;
}

async function checkPRDFiles() {
  let success = true;
  
  const prdFiles = [
    'docs/prd/requirements.md',
    'docs/prd-old/requirements.md',
    'docs/prd-new/requirements.md'
  ];
  
  for (const prdFile of prdFiles) {
    const prdPath = path.join(__dirname, '..', prdFile);
    if (fs.existsSync(prdPath)) {
      const content = fs.readFileSync(prdPath, 'utf8');
      
      const hasCoordinatorRef = content.includes('Coordinator');
      const hasPinAccess = content.includes('congregation PIN') || content.includes('PIN access');
      const noDeveloperRef = !content.includes('Developer,') && !content.includes('(Developer,');
      const noOverseerRef = !content.includes('Overseer/Coordinator');
      
      console.log(`   ${prdFile} updated: ${hasCoordinatorRef && hasPinAccess && noDeveloperRef && noOverseerRef ? '✅' : '❌'}`);
      
      success &= hasCoordinatorRef && hasPinAccess && noDeveloperRef && noOverseerRef;
    }
  }
  
  return success;
}

async function checkForOldRoleReferences() {
  let success = true;
  
  // Check for problematic patterns in key files
  const filesToCheck = [
    'docs/architecture.md',
    'docs/stories/2.1.story.md',
    'docs/prd/requirements.md',
    'docs/prd/epic-details.md'
  ];
  
  const problematicPatterns = [
    /DEVELOPER\s*=\s*['"]developer['"]/,
    /OVERSEER_COORDINATOR\s*=\s*['"]overseer_coordinator['"]/,
    /role.*===.*['"]developer['"]/,
    /role.*===.*['"]overseer_coordinator['"]/
  ];
  
  for (const filePath of filesToCheck) {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      let hasProblems = false;
      for (const pattern of problematicPatterns) {
        if (pattern.test(content)) {
          hasProblems = true;
          break;
        }
      }
      
      console.log(`   ${filePath} clean of old roles: ${!hasProblems ? '✅' : '❌'}`);
      success &= !hasProblems;
    }
  }
  
  return success;
}

// Run the verification
verifyDocumentationUpdates();
