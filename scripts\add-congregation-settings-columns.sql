-- Migration script to add congregation settings columns
-- This script adds the necessary columns to support the congregation settings functionality

-- Add missing columns to congregations table
ALTER TABLE congregations 
ADD COLUMN IF NOT EXISTS circuit_number VARCHAR(10) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS circuit_overseer VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS midweek_day VARCHAR(20) DEFAULT 'Thursday',
ADD COLUMN IF NOT EXISTS midweek_time VARCHAR(10) DEFAULT '7:00 PM',
ADD COLUMN IF NOT EXISTS weekend_day VARCHAR(20) DEFAULT 'Sunday',
ADD COLUMN IF NOT EXISTS weekend_time VARCHAR(10) DEFAULT '10:00 AM',
ADD COLUMN IF NOT EXISTS default_congregation_id VARCHAR(20) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS default_congregation_pin VARCHAR(20) DEFAULT NULL;

-- Update the existing Coral Oeste congregation with default values
UPDATE congregations 
SET 
    circuit_number = '23',
    circuit_overseer = '<PERSON>',
    midweek_day = 'Thursday',
    midweek_time = '7:00 PM',
    weekend_day = 'Sunday',
    weekend_time = '10:00 AM',
    default_congregation_id = '1441',
    default_congregation_pin = '1930'
WHERE id = 1;

-- Create audit_logs table if it doesn't exist (for PIN reset logging)
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(255) NOT NULL,
    details JSON DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action ON audit_logs(user_id, action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
