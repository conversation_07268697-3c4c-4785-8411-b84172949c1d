/**
 * Communication Preferences API Endpoint
 * 
 * Handles member communication preferences for notifications and messaging.
 * Allows users to configure their notification delivery preferences.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { 
  CommunicationService, 
  NotificationCategory
} from '@/lib/services/communicationService';

// Validation schema
const UpdatePreferencesSchema = z.object({
  emailNotifications: z.boolean().optional(),
  smsNotifications: z.boolean().optional(),
  inAppNotifications: z.boolean().optional(),
  quietHoursStart: z.string().regex(/^\d{2}:\d{2}$/).optional(),
  quietHoursEnd: z.string().regex(/^\d{2}:\d{2}$/).optional(),
  categoryPreferences: z.record(z.nativeEnum(NotificationCategory), z.boolean()).optional(),
});

/**
 * GET /api/communication/preferences
 * Get communication preferences for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get communication preferences
    const preferences = await CommunicationService.getCommunicationPreferences(
      member.congregationId,
      member.id
    );

    return NextResponse.json({
      success: true,
      preferences,
    });

  } catch (error) {
    console.error('Communication preferences GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve communication preferences',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/communication/preferences
 * Update communication preferences for the authenticated user
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = UpdatePreferencesSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid preferences data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const preferencesData = validationResult.data;

    // Update communication preferences
    const preferences = await CommunicationService.updateCommunicationPreferences(
      member.congregationId,
      member.id,
      preferencesData
    );

    return NextResponse.json({
      success: true,
      preferences,
      message: 'Communication preferences updated successfully',
    });

  } catch (error) {
    console.error('Communication preferences update error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to update communication preferences',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
