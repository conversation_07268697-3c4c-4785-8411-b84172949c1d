/**
 * Test API on port 3000
 */

async function testAPI() {
  try {
    console.log('🔍 Testing API on port 3000...');
    
    // Test authentication
    const loginResponse = await fetch('http://localhost:3000/api/auth/congregation-login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1234',
        memberId: '1' // <PERSON> - coordinator
      }),
    });
    
    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ Login successful, role:', loginData.user.role);
    
    // Test documents API
    const response = await fetch('http://localhost:3000/api/documents', {
      headers: { 'Authorization': `Bearer ${loginData.token}` },
    });
    
    if (!response.ok) {
      throw new Error(`API failed: ${response.status}`);
    }
    
    const data = await response.json();
    console.log(`📊 Documents returned: ${data.documents?.length || 0}`);
    
    if (data.documents && data.documents.length > 0) {
      console.log('📄 Sample document title:', data.documents[0].title);
      console.log('📄 Sample document category:', data.documents[0].category);
    }
    
    console.log('🎉 All tests passed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testAPI();
