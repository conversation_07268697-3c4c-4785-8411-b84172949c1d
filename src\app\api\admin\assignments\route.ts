/**
 * Section Assignments API Endpoint
 * 
 * Handles CRUD operations for administrative section assignments.
 * Only accessible to coordinator elders with proper permissions.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { SectionAssignmentService } from '@/lib/services/sectionAssignmentService';
import { ADMINISTRATIVE_SECTIONS, getDefaultScopeDefinition } from '@/lib/constants/administrativeSections';

// Validation schemas
const AssignSectionSchema = z.object({
  memberId: z.string().min(1, 'Member ID is required'),
  sectionType: z.nativeEnum(ADMINISTRATIVE_SECTIONS),
  scopeDefinition: z.object({
    description: z.string().optional(),
    limitations: z.array(z.string()).optional(),
    specificAreas: z.array(z.string()).optional(),
    expirationDate: z.string().optional(),
    notes: z.string().optional(),
  }).optional(),
  reason: z.string().optional(),
});

const RemoveAssignmentSchema = z.object({
  assignmentId: z.string().min(1, 'Assignment ID is required'),
  reason: z.string().optional(),
});

const TransferAssignmentSchema = z.object({
  fromMemberId: z.string().min(1, 'From member ID is required'),
  toMemberId: z.string().min(1, 'To member ID is required'),
  sectionType: z.nativeEnum(ADMINISTRATIVE_SECTIONS),
  scopeDefinition: z.object({
    description: z.string().optional(),
    limitations: z.array(z.string()).optional(),
    specificAreas: z.array(z.string()).optional(),
    expirationDate: z.string().optional(),
    notes: z.string().optional(),
  }).optional(),
  reason: z.string().optional(),
});

/**
 * GET - Retrieve all section assignments for the congregation
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view assignments' },
        { status: 403 }
      );
    }

    // Get assignments for the congregation
    const assignments = await SectionAssignmentService.getAssignments(user.congregationId);

    return NextResponse.json({
      success: true,
      assignments,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Assignments GET error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch assignments',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Create a new section assignment
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user is a coordinator elder
    if (user.role !== 'overseer_coordinator') {
      return NextResponse.json(
        { error: 'Only coordinator elders can assign sections' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = AssignSectionSchema.parse(body);

    // Use default scope definition if not provided
    const scopeDefinition = validatedData.scopeDefinition || 
      getDefaultScopeDefinition(validatedData.sectionType);

    // Create the assignment
    const assignment = await SectionAssignmentService.assignSection(
      user.congregationId,
      validatedData.memberId,
      validatedData.sectionType,
      scopeDefinition,
      user.userId,
      validatedData.reason
    );

    return NextResponse.json({
      success: true,
      assignment,
      message: 'Section assigned successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Assignment POST error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to create assignment';
    
    return NextResponse.json(
      { 
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE - Remove a section assignment
 */
export async function DELETE(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user is a coordinator elder
    if (user.role !== 'overseer_coordinator') {
      return NextResponse.json(
        { error: 'Only coordinator elders can remove assignments' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = RemoveAssignmentSchema.parse(body);

    // Remove the assignment
    await SectionAssignmentService.removeAssignment(
      user.congregationId,
      validatedData.assignmentId,
      user.userId,
      validatedData.reason
    );

    return NextResponse.json({
      success: true,
      message: 'Assignment removed successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Assignment DELETE error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to remove assignment';
    
    return NextResponse.json(
      { 
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT - Transfer a section assignment
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user is a coordinator elder
    if (user.role !== 'overseer_coordinator') {
      return NextResponse.json(
        { error: 'Only coordinator elders can transfer assignments' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = TransferAssignmentSchema.parse(body);

    // Use default scope definition if not provided
    const scopeDefinition = validatedData.scopeDefinition || 
      getDefaultScopeDefinition(validatedData.sectionType);

    // Transfer the assignment
    const assignment = await SectionAssignmentService.transferAssignment(
      user.congregationId,
      validatedData.fromMemberId,
      validatedData.toMemberId,
      validatedData.sectionType,
      scopeDefinition,
      user.userId,
      validatedData.reason
    );

    return NextResponse.json({
      success: true,
      assignment,
      message: 'Assignment transferred successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Assignment PUT error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to transfer assignment';
    
    return NextResponse.json(
      { 
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
