'use client';

/**
 * Admin Communications Management Page
 *
 * Administrative interface for managing congregation communications and notifications.
 * Allows creating announcements, managing templates, and sending targeted messages.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  NotificationCategory,
  NotificationPriority,
  DeliveryMethod,
  NOTIFICATION_CATEGORY_LABELS,
  NOTIFICATION_PRIORITY_LABELS,
  DELIVERY_METHOD_LABELS,
  CreateNotificationData
} from '@/lib/services/communicationService';
import AdminFooter from '@/components/admin/AdminFooter';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface Member {
  id: string;
  name: string;
  role: string;
  isActive: boolean;
}

export default function AdminCommunicationsPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [members, setMembers] = useState<Member[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<Partial<CreateNotificationData>>({
    title: '',
    message: '',
    category: NotificationCategory.GENERAL,
    priority: NotificationPriority.NORMAL,
    deliveryMethod: [DeliveryMethod.IN_APP],
    recipientIds: [],
  });

  const [selectedRecipients, setSelectedRecipients] = useState<string[]>([]);
  const [recipientType, setRecipientType] = useState<'all' | 'specific'>('all');

  useEffect(() => {
    checkAdminAccess();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      loadMembers();
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAdminAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();

      // Check if user has permission to access admin communications
      if (!['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(data.user.role)) {
        router.push('/dashboard');
        return;
      }

      setUser(data.user);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const loadMembers = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/members', {
        headers: { 'Authorization': `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        setMembers(data.members || []);
      }
    } catch (error) {
      console.error('Error loading members:', error);
    }
  };

  const handleCreateNotification = async () => {
    if (!formData.title || !formData.message || !formData.category) {
      setError('Por favor complete todos los campos requeridos');
      return;
    }

    setIsCreating(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');

      const notificationData = {
        ...formData,
        ...(recipientType === 'specific' && selectedRecipients.length > 0 && {
          recipientIds: selectedRecipients
        }),
      };

      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(notificationData),
      });

      if (response.ok) {
        const data = await response.json();
        setSuccessMessage(`Notificación enviada exitosamente a ${data.count} hermanos`);
        setShowCreateModal(false);
        resetForm();
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Error al enviar notificación');
      }
    } catch (error) {
      console.error('Error creating notification:', error);
      setError('Error al enviar la notificación');
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      message: '',
      category: NotificationCategory.GENERAL,
      priority: NotificationPriority.NORMAL,
      deliveryMethod: [DeliveryMethod.IN_APP],
      recipientIds: [],
    });
    setSelectedRecipients([]);
    setRecipientType('all');
  };

  const closeModal = () => {
    setShowCreateModal(false);
    resetForm();
    setError(null);
  };

  const toggleRecipient = (memberId: string) => {
    setSelectedRecipients(prev =>
      prev.includes(memberId)
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  const selectAllMembers = () => {
    setSelectedRecipients(members.filter(m => m.isActive).map(m => m.id));
  };

  const clearAllMembers = () => {
    setSelectedRecipients([]);
  };

  const toggleDeliveryMethod = (method: DeliveryMethod) => {
    const currentMethods = formData.deliveryMethod || [DeliveryMethod.IN_APP];
    const newMethods = currentMethods.includes(method)
      ? currentMethods.filter(m => m !== method)
      : [...currentMethods, method];

    // Ensure at least one method is selected
    if (newMethods.length === 0) {
      return;
    }

    setFormData({ ...formData, deliveryMethod: newMethods });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <button
              onClick={() => router.push('/admin')}
              className="text-blue-200 hover:text-white mb-2 flex items-center"
            >
              ← Volver a Administración
            </button>
            <h1 className="text-2xl font-bold">Gestión de Comunicaciones</h1>
            <p className="text-blue-200">Envío de notificaciones y comunicaciones congregacionales</p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors"
          >
            Enviar Notificación
          </button>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 pb-20">
        {/* Success/Error Messages */}
        {successMessage && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <p className="text-green-800">{successMessage}</p>
            <button
              onClick={() => setSuccessMessage(null)}
              className="text-green-600 hover:text-green-800 text-sm mt-2"
            >
              Cerrar
            </button>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <p className="text-red-800">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800 text-sm mt-2"
            >
              Cerrar
            </button>
          </div>
        )}

        {/* Communication Overview */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Centro de Comunicaciones</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">📢</span>
              </div>
              <h3 className="font-semibold text-gray-900">Anuncios</h3>
              <p className="text-gray-600 text-sm">Comunicaciones generales para toda la congregación</p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">📅</span>
              </div>
              <h3 className="font-semibold text-gray-900">Recordatorios</h3>
              <p className="text-gray-600 text-sm">Recordatorios de eventos y actividades importantes</p>
            </div>

            <div className="text-center">
              <div className="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="font-semibold text-gray-900">Urgentes</h3>
              <p className="text-gray-600 text-sm">Comunicaciones urgentes que requieren atención inmediata</p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Acciones Rápidas</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={() => {
                setFormData({
                  ...formData,
                  category: NotificationCategory.ANNOUNCEMENTS,
                  priority: NotificationPriority.NORMAL,
                  title: 'Anuncio Congregacional',
                });
                setShowCreateModal(true);
              }}
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
            >
              <div className="text-center">
                <span className="text-2xl mb-2 block">📢</span>
                <h3 className="font-medium text-gray-900">Anuncio General</h3>
              </div>
            </button>

            <button
              onClick={() => {
                setFormData({
                  ...formData,
                  category: NotificationCategory.EVENTS,
                  priority: NotificationPriority.NORMAL,
                  title: 'Recordatorio de Evento',
                });
                setShowCreateModal(true);
              }}
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
            >
              <div className="text-center">
                <span className="text-2xl mb-2 block">📅</span>
                <h3 className="font-medium text-gray-900">Recordatorio</h3>
              </div>
            </button>

            <button
              onClick={() => {
                setFormData({
                  ...formData,
                  category: NotificationCategory.URGENT,
                  priority: NotificationPriority.URGENT,
                  title: 'Comunicación Urgente',
                  deliveryMethod: [DeliveryMethod.IN_APP, DeliveryMethod.EMAIL],
                });
                setShowCreateModal(true);
              }}
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-red-300 hover:bg-red-50 transition-colors"
            >
              <div className="text-center">
                <span className="text-2xl mb-2 block">⚡</span>
                <h3 className="font-medium text-gray-900">Urgente</h3>
              </div>
            </button>

            <button
              onClick={() => {
                setFormData({
                  ...formData,
                  category: NotificationCategory.GENERAL,
                  priority: NotificationPriority.NORMAL,
                  title: '',
                  message: '',
                });
                setShowCreateModal(true);
              }}
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
            >
              <div className="text-center">
                <span className="text-2xl mb-2 block">✉️</span>
                <h3 className="font-medium text-gray-900">Personalizado</h3>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Create Notification Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Enviar Notificación</h2>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={(e) => {
                e.preventDefault();
                handleCreateNotification();
              }}>
                <div className="space-y-6">
                  {/* Title and Message */}
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Título *
                      </label>
                      <input
                        type="text"
                        value={formData.title || ''}
                        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Mensaje *
                      </label>
                      <textarea
                        value={formData.message || ''}
                        onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>
                  </div>

                  {/* Category and Priority */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Categoría *
                      </label>
                      <select
                        value={formData.category || NotificationCategory.GENERAL}
                        onChange={(e) => setFormData({ ...formData, category: e.target.value as NotificationCategory })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        {Object.values(NotificationCategory).map(category => (
                          <option key={category} value={category}>
                            {NOTIFICATION_CATEGORY_LABELS[category]}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Prioridad
                      </label>
                      <select
                        value={formData.priority || NotificationPriority.NORMAL}
                        onChange={(e) => setFormData({ ...formData, priority: e.target.value as NotificationPriority })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {Object.values(NotificationPriority).map(priority => (
                          <option key={priority} value={priority}>
                            {NOTIFICATION_PRIORITY_LABELS[priority]}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Delivery Methods */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Métodos de Entrega
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {Object.values(DeliveryMethod).map(method => (
                        <label key={method} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={(formData.deliveryMethod || []).includes(method)}
                            onChange={() => toggleDeliveryMethod(method)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {DELIVERY_METHOD_LABELS[method]}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Recipients */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Destinatarios
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="recipientType"
                            value="all"
                            checked={recipientType === 'all'}
                            onChange={(e) => setRecipientType(e.target.value as 'all' | 'specific')}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">Todos los hermanos activos</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="recipientType"
                            value="specific"
                            checked={recipientType === 'specific'}
                            onChange={(e) => setRecipientType(e.target.value as 'all' | 'specific')}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">Hermanos específicos</span>
                        </label>
                      </div>

                      {recipientType === 'specific' && (
                        <div className="border border-gray-300 rounded-md p-4 max-h-60 overflow-y-auto">
                          <div className="flex justify-between items-center mb-3">
                            <span className="text-sm font-medium text-gray-700">
                              Seleccionar hermanos ({selectedRecipients.length} seleccionados)
                            </span>
                            <div className="space-x-2">
                              <button
                                type="button"
                                onClick={selectAllMembers}
                                className="text-xs text-blue-600 hover:text-blue-800"
                              >
                                Seleccionar todos
                              </button>
                              <button
                                type="button"
                                onClick={clearAllMembers}
                                className="text-xs text-gray-600 hover:text-gray-800"
                              >
                                Limpiar
                              </button>
                            </div>
                          </div>
                          <div className="space-y-2">
                            {members.filter(m => m.isActive).map(member => (
                              <label key={member.id} className="flex items-center">
                                <input
                                  type="checkbox"
                                  checked={selectedRecipients.includes(member.id)}
                                  onChange={() => toggleRecipient(member.id)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  {member.name} ({member.role})
                                </span>
                              </label>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Modal Actions */}
                <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={isCreating}
                    className={`px-4 py-2 rounded-md transition-colors ${
                      isCreating
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    } text-white`}
                  >
                    {isCreating ? 'Enviando...' : 'Enviar Notificación'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Admin Footer */}
      <AdminFooter currentSection="communications" />
    </div>
  );
}
