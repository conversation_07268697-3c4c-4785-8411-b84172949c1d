const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createCoordinatorUser() {
  try {
    console.log('👑 Creating coordinator user for Story 2.2 testing...\n');

    // Hash the PIN
    const hashedPin = await bcrypt.hash('1234', 10);

    // Create coordinator user
    const coordinator = await prisma.member.create({
      data: {
        congregationId: '1441',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        address: '456 Leadership Ave, Coral Oeste, FL 33101',
        birthDate: new Date('1975-03-20'),
        role: 'coordinator',
        serviceGroup: 'Grupo 1',
        pin: hashedPin,
        isActive: true,
        preferences: {
          language: 'es',
          notifications: true,
        },
        contactPreferences: {
          preferredMethod: 'email',
          allowEmergencyContact: true,
          privacyLevel: 'elders_only',
        },
        qualifications: [
          '<PERSON>ordinador del Cuerpo de Ancianos',
          '<PERSON><PERSON> de Escuela',
          'Oración',
          '<PERSON><PERSON><PERSON>',
          'President<PERSON>',
          '<PERSON><PERSON>'
        ],
        notes: 'Coordinador del cuerpo de ancianos. Acceso completo a funciones administrativas.',
      },
    });

    console.log('✅ Coordinator user created successfully!');
    console.log(`📧 Email: ${coordinator.email}`);
    console.log(`👤 Name: ${coordinator.name}`);
    console.log(`🔑 PIN: 1234`);
    console.log(`🏢 Role: ${coordinator.role}`);
    console.log(`🆔 ID: ${coordinator.id}`);

    console.log('\n🧪 Testing Login Credentials:');
    console.log('🌐 URL: http://localhost:3001/login');
    console.log('🏛️ Congregation ID: 1441');
    console.log('🔐 PIN: 1234');
    console.log('📧 User: Carlos Coordinador');

    console.log('\n📋 This user has access to:');
    console.log('  ✅ Enhanced Member Management (Story 2.2)');
    console.log('  ✅ Permission Delegation System (Story 2.1)');
    console.log('  ✅ All administrative functions');
    console.log('  ✅ Member profile creation and editing');
    console.log('  ✅ Role assignment and service group management');

  } catch (error) {
    if (error.code === 'P2002') {
      console.log('⚠️ User already exists with this email. Checking existing user...');
      
      const existingUser = await prisma.member.findFirst({
        where: {
          email: '<EMAIL>',
          congregationId: '1441',
        },
      });

      if (existingUser) {
        console.log('✅ Existing coordinator user found:');
        console.log(`📧 Email: ${existingUser.email}`);
        console.log(`👤 Name: ${existingUser.name}`);
        console.log(`🏢 Role: ${existingUser.role}`);
        console.log(`🔑 PIN: 1234 (if not changed)`);
      }
    } else {
      console.error('❌ Error creating coordinator user:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createCoordinatorUser();
