# Story 12.6: Map-Based Territory Assignment

**Epic:** Epic 12: Territory Visualization & Member Interface  
**Story Points:** 8  
**Priority:** High  
**Status:** Draft

## Story

**As a** congregation administrator,  
**I want** to assign territories to members directly from the map interface,  
**so that** I can make assignment decisions based on geographic proximity and visual territory distribution.

## Acceptance Criteria

1. Territory assignment can be initiated by clicking territory markers on the map
2. Member selection dropdown appears with geographic proximity suggestions
3. Assignment workflow integrates with existing territory assignment system
4. Map updates in real-time to reflect new territory assignments
5. Geographic assignment suggestions consider member location and current workload
6. Bulk territory assignment allows selecting multiple territories for batch assignment

## Tasks / Subtasks

- [ ] Create map-based assignment interface (AC: 1, 2)
  - [ ] Add assignment action to territory marker click/tap functionality
  - [ ] Create MapAssignmentModal component for territory assignment from map
  - [ ] Implement member selection dropdown with search and filtering
  - [ ] Add geographic proximity suggestions for member selection
  - [ ] Create assignment confirmation workflow from map interface
- [ ] Integrate with existing assignment system (AC: 3, 4)
  - [ ] Connect map assignment with existing TerritoryAssignmentService
  - [ ] Implement real-time map updates after territory assignments
  - [ ] Add assignment validation and error handling for map workflow
  - [ ] Integrate with territory status management system
  - [ ] Connect with assignment notification system
- [ ] Implement geographic assignment suggestions (AC: 5)
  - [ ] Create GeoAssignmentService for proximity-based member suggestions
  - [ ] Calculate member-to-territory distances for assignment recommendations
  - [ ] Consider member current workload in assignment suggestions
  - [ ] Add member location data and proximity calculations
  - [ ] Implement assignment optimization algorithms
- [ ] Add bulk territory assignment (AC: 6)
  - [ ] Implement multi-territory selection on map interface
  - [ ] Create bulk assignment modal with member selection
  - [ ] Add bulk assignment validation and conflict detection
  - [ ] Implement batch assignment processing with progress tracking
  - [ ] Create bulk assignment confirmation and results display
- [ ] Create assignment analytics integration (Analytics)
  - [ ] Add assignment pattern analysis based on geographic data
  - [ ] Create territory distribution visualization for assignment planning
  - [ ] Implement assignment workload balance analysis
  - [ ] Add assignment efficiency metrics and recommendations
  - [ ] Create assignment history visualization on map
- [ ] Implement assignment API endpoints (Backend Integration)
  - [ ] Create POST /api/territories/map-assignment for map-based assignments
  - [ ] Add GET /api/territories/assignment-suggestions for proximity recommendations
  - [ ] Implement POST /api/territories/bulk-assignment for batch assignments
  - [ ] Add assignment validation API with geographic considerations
  - [ ] Create assignment analytics API for map-based insights
- [ ] Integrate with territory dashboard (Dashboard Integration)
  - [ ] Add map assignment functionality to territory admin dashboard
  - [ ] Create assignment mode toggle for map interface
  - [ ] Implement assignment workflow synchronization between map and list views
  - [ ] Add assignment statistics and analytics to dashboard
  - [ ] Create assignment history visualization integration
- [ ] Write comprehensive tests (Testing Standards)
  - [ ] Unit tests for map assignment workflow and validation
  - [ ] Integration tests for assignment system integration
  - [ ] Test geographic proximity calculations and suggestions
  - [ ] Test bulk assignment functionality and performance
  - [ ] E2E tests for complete map-based assignment workflow

## Dev Notes

### Dependencies and Prerequisites
**DEPENDENCY**: This story depends on:
- Story 12.3 (Interactive Territory Map Features) - Map interaction functionality required
- Story 11.1 (Member Territory Assignment Interface) - Assignment system integration
- Story 11.3 (Territory Status Management) - Status management integration

### Map Assignment Architecture
[Source: docs/territories-architecture.md#territory-assignment-workflow]

**Assignment Integration**: Map-based assignment must integrate with existing TerritoryAssignmentService
**Real-time Updates**: Map interface must update immediately after assignments
**Workflow Consistency**: Assignment workflow must match existing assignment patterns

### Geographic Assignment Suggestions
**Proximity-Based Recommendations:**
```typescript
interface GeoAssignmentService {
  calculateMemberProximity(territoryId: string, members: Member[]): ProximityResult[];
  suggestOptimalAssignments(territories: Territory[], members: Member[]): AssignmentSuggestion[];
  analyzeWorkloadBalance(assignments: Assignment[]): WorkloadAnalysis;
  optimizeAssignmentDistribution(congregationId: string): OptimizationResult;
}
```

### Technology Stack
[Source: docs/territories-architecture.md#tech-stack]
- **Map Library**: MapLibre GL JS for assignment interface and territory selection
- **Assignment Logic**: Integration with existing TerritoryAssignmentService
- **Geographic Calculations**: Haversine formula for distance calculations
- **Real-time Updates**: React Query for immediate map updates after assignments

### Map Assignment Interface
**Assignment Modal Implementation:**
```typescript
const MapAssignmentModal = ({ territory, onAssign, onCancel }) => {
  const { members, loading } = useMembers();
  const proximityMembers = useMemo(() => 
    calculateProximityRanking(territory, members), [territory, members]
  );
  
  return (
    <Modal>
      <TerritoryInfo territory={territory} />
      <MemberSelector 
        members={proximityMembers}
        onSelect={handleMemberSelect}
      />
      <AssignmentActions onConfirm={onAssign} onCancel={onCancel} />
    </Modal>
  );
};
```

### Bulk Assignment Implementation
**Multi-Territory Selection:**
- Map interface for selecting multiple territories
- Visual feedback for selected territories
- Bulk assignment modal with member selection
- Batch processing with progress tracking
- Conflict detection and resolution

### File Structure and Locations
[Source: docs/territories-architecture.md#unified-project-structure]
- **Map Assignment**: `src/components/territories/admin/MapAssignment.tsx`
- **Assignment Modal**: `src/components/territories/admin/MapAssignmentModal.tsx`
- **Geo Service**: `src/services/territories/GeoAssignmentService.ts`
- **API Routes**: `src/app/api/territories/map-assignment/route.ts`
- **Bulk Assignment**: `src/components/territories/admin/BulkMapAssignment.tsx`

### API Specification
**Map Assignment API Endpoints:**
- `POST /api/territories/map-assignment` - Create assignment from map interface
- `GET /api/territories/assignment-suggestions` - Get proximity-based member suggestions
- `POST /api/territories/bulk-assignment` - Batch territory assignments
- `GET /api/territories/assignment-analytics` - Assignment pattern analysis

### Geographic Proximity Calculations
**Distance-Based Member Suggestions:**
```typescript
const calculateProximity = (territory: Territory, member: Member): number => {
  if (!member.address || !territory.coordinates) return Infinity;
  
  const memberCoords = geocodeAddress(member.address);
  const territoryCoords = [territory.longitude, territory.latitude];
  
  return calculateDistance(memberCoords, territoryCoords);
};

const suggestMembers = (territory: Territory, members: Member[]): MemberSuggestion[] => {
  return members
    .map(member => ({
      member,
      distance: calculateProximity(territory, member),
      currentWorkload: member.assignedTerritories?.length || 0
    }))
    .sort((a, b) => a.distance - b.distance)
    .slice(0, 5); // Top 5 suggestions
};
```

### Assignment Workflow Integration
[Source: Story 11.1 - Member Territory Assignment Interface]

**Existing Assignment Integration:**
- Use existing TerritoryAssignmentService for assignment creation
- Integrate with territory status management system
- Connect with assignment notification system
- Maintain assignment audit trail and history

### Real-Time Map Updates
**Assignment Feedback:**
- Immediate territory marker color change after assignment
- Real-time territory status updates on map
- Assignment confirmation with visual feedback
- Map refresh with updated territory information

### Security and Authorization
**Map Assignment Security:**
- Admin role verification for assignment functionality
- Congregation isolation for all assignment operations
- Assignment validation and conflict detection
- Proper authentication for map assignment endpoints

### Performance Considerations
**Assignment Performance:**
- Efficient proximity calculations for member suggestions
- Optimized bulk assignment processing
- Real-time map updates without full page refresh
- Cached member location data for proximity calculations

### Assignment Analytics
**Geographic Assignment Insights:**
- Assignment pattern analysis based on geographic distribution
- Territory workload balance visualization
- Assignment efficiency metrics and recommendations
- Historical assignment pattern tracking

### Testing Requirements
[Source: docs/territories-architecture.md#testing-strategy]
- **Assignment Tests**: Test map-based assignment workflow and validation
- **Proximity Tests**: Verify geographic proximity calculations accuracy
- **Bulk Tests**: Test bulk assignment functionality and performance
- **Integration Tests**: Verify assignment system integration
- **Real-time Tests**: Test map updates and real-time functionality

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-25 | 1.0 | Initial story creation for map-based territory assignment | PO Agent |

## Dev Agent Record

### Agent Model Used
*To be populated by development agent*

### Debug Log References
*To be populated by development agent*

### Completion Notes List
*To be populated by development agent*

### File List
*To be populated by development agent*

## QA Results
*To be populated by QA agent*
