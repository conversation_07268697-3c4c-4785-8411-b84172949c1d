# Hermanos Fullstack Architecture Document

## Table of Contents

- [<PERSON><PERSON>ck Architecture Document](#table-of-contents)
  - [Table of Contents](./table-of-contents.md)
  - [1. Project Overview](./1-project-overview.md)
    - [Vision Statement](./1-project-overview.md#vision-statement)
    - [Core Objectives](./1-project-overview.md#core-objectives)
    - [Target Users](./1-project-overview.md#target-users)
    - [Success Criteria](./1-project-overview.md#success-criteria)
    - [Project Scope](./1-project-overview.md#project-scope)
    - [Key Constraints](./1-project-overview.md#key-constraints)
  - [2. Current System Analysis](./2-current-system-analysis.md)
    - [Existing Architecture Overview](./2-current-system-analysis.md#existing-architecture-overview)
    - [Database Schema Analysis](./2-current-system-analysis.md#database-schema-analysis)
    - [Current User Interface Analysis](./2-current-system-analysis.md#current-user-interface-analysis)
    - [Feature Analysis](./2-current-system-analysis.md#feature-analysis)
    - [Performance Characteristics](./2-current-system-analysis.md#performance-characteristics)
    - [User Experience Assessment](./2-current-system-analysis.md#user-experience-assessment)
    - [Technical Debt and Limitations](./2-current-system-analysis.md#technical-debt-and-limitations)
    - [Migration Considerations](./2-current-system-analysis.md#migration-considerations)
    - [Success Factors from Current System](./2-current-system-analysis.md#success-factors-from-current-system)
  - [3. Technology Stack Selection](./3-technology-stack-selection.md)
    - [Technology Selection Criteria](./3-technology-stack-selection.md#technology-selection-criteria)
    - [Selected Technology Stack](./3-technology-stack-selection.md#selected-technology-stack)
      - [Frontend Framework: Next.js 14](./3-technology-stack-selection.md#frontend-framework-nextjs-14)
      - [Database: PostgreSQL 15](./3-technology-stack-selection.md#database-postgresql-15)
      - [ORM: Prisma](./3-technology-stack-selection.md#orm-prisma)
      - [Styling: Tailwind CSS](./3-technology-stack-selection.md#styling-tailwind-css)
      - [State Management: Zustand](./3-technology-stack-selection.md#state-management-zustand)
    - [Development Tools and Utilities](./3-technology-stack-selection.md#development-tools-and-utilities)
      - [TypeScript Configuration](./3-technology-stack-selection.md#typescript-configuration)
      - [Package.json Dependencies](./3-technology-stack-selection.md#packagejson-dependencies)
    - [Technology Stack Benefits](./3-technology-stack-selection.md#technology-stack-benefits)
  - [4. Database Architecture](./4-database-architecture.md)
    - [Database Migration Strategy](./4-database-architecture.md#database-migration-strategy)
    - [Core Database Schema](./4-database-architecture.md#core-database-schema)
      - [Congregation Management Tables](./4-database-architecture.md#congregation-management-tables)
      - [Meeting Management Schema](./4-database-architecture.md#meeting-management-schema)
      - [Task and Assignment Management](./4-database-architecture.md#task-and-assignment-management)
      - [Communication and Document Management](./4-database-architecture.md#communication-and-document-management)
    - [Data Migration Scripts](./4-database-architecture.md#data-migration-scripts)
      - [MySQL to PostgreSQL Migration](./4-database-architecture.md#mysql-to-postgresql-migration)
    - [Database Performance Optimization](./4-database-architecture.md#database-performance-optimization)
      - [Indexing Strategy](./4-database-architecture.md#indexing-strategy)
      - [Query Optimization Examples](./4-database-architecture.md#query-optimization-examples)
  - [5. Authentication and Authorization](./5-authentication-and-authorization.md)
    - [Authentication Strategy](./5-authentication-and-authorization.md#authentication-strategy)
    - [Simple JWT Implementation](./5-authentication-and-authorization.md#simple-jwt-implementation)
    - [Role-Based Access Control](./5-authentication-and-authorization.md#role-based-access-control)
    - [Authentication Middleware](./5-authentication-and-authorization.md#authentication-middleware)
    - [Login API Implementation](./5-authentication-and-authorization.md#login-api-implementation)
    - [Frontend Authentication Hook](./5-authentication-and-authorization.md#frontend-authentication-hook)
    - [Login Page Implementation](./5-authentication-and-authorization.md#login-page-implementation)
  - [6. API Design](./6-api-design.md)
    - [API Architecture Principles](./6-api-design.md#api-architecture-principles)
    - [API Response Format](./6-api-design.md#api-response-format)
    - [Member Management API](./6-api-design.md#member-management-api)
    - [Meeting Management API](./6-api-design.md#meeting-management-api)
    - [Task Management API](./6-api-design.md#task-management-api)
    - [File Upload API](./6-api-design.md#file-upload-api)
    - [API Client Service](./6-api-design.md#api-client-service)
