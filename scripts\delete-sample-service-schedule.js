/**
 * <PERSON><PERSON>t to delete sample service schedule data for testing
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function deleteSampleServiceSchedule() {
  try {
    console.log('Deleting sample service schedule data...');

    // Get the current week schedule
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday

    console.log(`Looking for schedule starting: ${startOfWeek.toISOString().split('T')[0]}`);

    // Find the schedule for this week
    const schedule = await prisma.serviceSchedule.findFirst({
      where: {
        congregationId: '1441',
        weekStartDate: startOfWeek
      },
      include: {
        scheduleTimes: true
      }
    });

    if (!schedule) {
      console.log('No sample schedule found for this week.');
      return;
    }

    console.log(`Found schedule: ${schedule.id} with ${schedule.scheduleTimes.length} service times`);

    // Delete all service times for this schedule
    const deletedTimes = await prisma.serviceScheduleTime.deleteMany({
      where: {
        scheduleId: schedule.id
      }
    });

    console.log(`Deleted ${deletedTimes.count} service times`);

    // Delete the schedule itself
    await prisma.serviceSchedule.delete({
      where: {
        id: schedule.id
      }
    });

    console.log('Schedule deleted successfully');
    console.log('\nSample service schedule data deleted successfully!');

  } catch (error) {
    console.error('Error deleting sample service schedule:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
deleteSampleServiceSchedule();
