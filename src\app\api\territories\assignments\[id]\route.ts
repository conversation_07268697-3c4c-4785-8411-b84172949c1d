/**
 * Territory Assignment Management API Endpoint
 * 
 * Handles individual territory assignment operations including unassignment.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

/**
 * DELETE /api/territories/assignments/[id]
 * Unassign a territory from a member
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get admin member information
    const adminMember = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { 
        id: true,
        name: true,
        role: true,
        congregationId: true 
      }
    });

    if (!adminMember) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions for territory unassignment
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(adminMember.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for territory unassignment' },
        { status: 403 }
      );
    }

    const assignmentId = params.id;

    // Get the assignment to verify it exists and belongs to same congregation
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        id: assignmentId,
        congregationId: adminMember.congregationId
      },
      include: {
        territory: {
          select: {
            id: true,
            territoryNumber: true,
            address: true
          }
        },
        member: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!assignment) {
      return NextResponse.json(
        { error: 'Assignment not found or not in same congregation' },
        { status: 404 }
      );
    }

    // Unassign territory in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update assignment status to completed
      const updatedAssignment = await tx.territoryAssignment.update({
        where: { id: assignmentId },
        data: {
          status: 'completed',
          completedAt: new Date(),
          updatedAt: new Date()
        }
      });

      // Update territory status back to available
      await tx.territory.update({
        where: { id: assignment.territory.id },
        data: {
          status: 'available',
          updatedAt: new Date()
        }
      });

      return updatedAssignment;
    });

    return NextResponse.json({
      success: true,
      data: {
        assignment: result,
        territory: {
          id: assignment.territory.id,
          territoryNumber: assignment.territory.territoryNumber,
          address: assignment.territory.address
        },
        member: {
          id: assignment.member.id,
          name: assignment.member.name
        },
        unassignedBy: {
          id: adminMember.id,
          name: adminMember.name,
          role: adminMember.role
        },
        message: `Territorio ${assignment.territory.territoryNumber} desasignado exitosamente de ${assignment.member.name}`
      }
    });

  } catch (error) {
    console.error('Territory unassignment error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/territories/assignments/[id]
 * Get specific assignment details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get admin member information
    const adminMember = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { 
        congregationId: true 
      }
    });

    if (!adminMember) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    const assignmentId = params.id;

    // Get the assignment details
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        id: assignmentId,
        congregationId: adminMember.congregationId
      },
      include: {
        territory: {
          select: {
            id: true,
            territoryNumber: true,
            address: true,
            status: true
          }
        },
        member: {
          select: {
            id: true,
            name: true,
            role: true
          }
        },
        assignedByMember: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!assignment) {
      return NextResponse.json(
        { error: 'Assignment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        assignment: {
          id: assignment.id,
          status: assignment.status,
          assignedAt: assignment.assignedAt,
          completedAt: assignment.completedAt,
          visitCount: assignment.visitCount,
          isPartiallyCompleted: assignment.isPartiallyCompleted,
          territory: assignment.territory,
          member: assignment.member,
          assignedBy: assignment.assignedByMember
        }
      }
    });

  } catch (error) {
    console.error('Assignment get error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
