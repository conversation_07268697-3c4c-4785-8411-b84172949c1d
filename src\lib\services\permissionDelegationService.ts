/**
 * Permission Delegation Service
 *
 * Handles administrative delegation system with granular permissions for elders
 * and ministerial servants. Implements story 2.1 requirements.
 */

import { prisma } from '@/lib/prisma';
import { ADMINISTRATIVE_SECTIONS } from '@/lib/constants/administrativeSections';

export interface PermissionAssignment {
  id: string;
  userId: string;
  userName: string;
  userRole: string;
  sectionId: string;
  sectionName: string;
  permissions: string[];
  assignedBy: string;
  assignedByName: string;
  assignedAt: Date;
  expirationDate?: Date;
  isActive: boolean;
  notes?: string;
}

export interface PermissionAuditEntry {
  id: string;
  userId: string;
  userName: string;
  action: 'assign' | 'revoke' | 'modify' | 'expire';
  sectionId: string;
  sectionName: string;
  permissions: string[];
  performedBy: string;
  performedByName: string;
  reason?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface AssignPermissionsRequest {
  userId: string;
  sectionId: string;
  permissions: string[];
  expirationDate?: Date;
  notes?: string;
  reason?: string;
}

export interface RevokePermissionsRequest {
  userId: string;
  sectionId: string;
  permissions?: string[]; // If not provided, revoke all permissions for the section
  reason?: string;
}

export interface DelegationContext {
  performedBy: string;
  hasCongregationPinAccess?: boolean;
  ipAddress?: string;
  userAgent?: string;
}

export class PermissionDelegationService {
  /**
   * Assign permissions to a user for a specific section
   */
  static async assignPermissions(
    congregationId: string,
    request: AssignPermissionsRequest,
    context: DelegationContext
  ): Promise<PermissionAssignment> {
    // Validate that the performer has authority to delegate
    await this.validateDelegationAuthority(
      context.performedBy,
      request.sectionId,
      congregationId,
      context.hasCongregationPinAccess
    );

    // Validate that the target user is eligible for the section
    const targetUser = await prisma.member.findUnique({
      where: { id: request.userId },
      select: { id: true, name: true, role: true, congregationId: true },
    });

    if (!targetUser || targetUser.congregationId !== congregationId) {
      throw new Error('Target user not found or not in the same congregation');
    }

    // Validate section and permissions
    this.validateSectionPermissions(request.sectionId, request.permissions);

    // Check if user is eligible for this section based on role
    this.validateUserEligibility(targetUser.role, request.sectionId);

    // Create or update permission assignment
    const existingPermission = await prisma.elderPermission.findUnique({
      where: {
        memberId_sectionId: {
          memberId: request.userId,
          sectionId: request.sectionId,
        },
      },
    });

    let permissionAssignment;

    if (existingPermission) {
      // Update existing permission
      permissionAssignment = await prisma.elderPermission.update({
        where: { id: existingPermission.id },
        data: {
          permissions: request.permissions,
          assignedBy: context.performedBy,
          assignedAt: new Date(),
          expirationDate: request.expirationDate,
          isActive: true,
          notes: request.notes,
        },
        include: {
          member: { select: { name: true, role: true } },
          assignedByMember: { select: { name: true } },
        },
      });
    } else {
      // Create new permission
      permissionAssignment = await prisma.elderPermission.create({
        data: {
          congregationId,
          memberId: request.userId,
          sectionId: request.sectionId,
          permissions: request.permissions,
          assignedBy: context.performedBy,
          assignedAt: new Date(),
          expirationDate: request.expirationDate,
          isActive: true,
          notes: request.notes,
        },
        include: {
          member: { select: { name: true, role: true } },
          assignedByMember: { select: { name: true } },
        },
      });
    }

    // Log the permission change
    await this.logPermissionChange(
      congregationId,
      request.userId,
      existingPermission ? 'modify' : 'assign',
      request.sectionId,
      request.permissions,
      context.performedBy,
      request.reason,
      context.ipAddress,
      context.userAgent
    );

    return this.mapToPermissionAssignment(permissionAssignment);
  }

  /**
   * Revoke all permissions for a user across all sections
   */
  static async revokeAllPermissions(
    congregationId: string,
    userId: string,
    context: DelegationContext,
    reason?: string
  ): Promise<void> {
    // Get all active permissions for the user
    const existingPermissions = await prisma.elderPermission.findMany({
      where: {
        congregationId,
        memberId: userId,
        isActive: true,
      },
    });

    if (existingPermissions.length === 0) {
      return; // No permissions to revoke
    }

    // Deactivate all permissions
    await prisma.elderPermission.updateMany({
      where: {
        congregationId,
        memberId: userId,
        isActive: true,
      },
      data: {
        isActive: false,
      },
    });

    // Log the permission changes
    for (const permission of existingPermissions) {
      await this.logPermissionChange(
        congregationId,
        userId,
        'revoke',
        permission.sectionId,
        permission.permissions as string[],
        context.performedBy,
        reason || 'All permissions revoked',
        context.ipAddress,
        context.userAgent
      );
    }
  }

  /**
   * Revoke permissions from a user for a specific section
   */
  static async revokePermissions(
    congregationId: string,
    request: RevokePermissionsRequest,
    context: DelegationContext
  ): Promise<void> {
    // Validate that the performer has authority to delegate
    await this.validateDelegationAuthority(
      context.performedBy,
      request.sectionId,
      congregationId,
      context.hasCongregationPinAccess
    );

    const existingPermission = await prisma.elderPermission.findUnique({
      where: {
        memberId_sectionId: {
          memberId: request.userId,
          sectionId: request.sectionId,
        },
      },
    });

    if (!existingPermission) {
      throw new Error('Permission assignment not found');
    }

    if (request.permissions && request.permissions.length > 0) {
      // Revoke specific permissions
      const currentPermissions = existingPermission.permissions as string[];
      const remainingPermissions = currentPermissions.filter(
        p => !request.permissions!.includes(p)
      );

      if (remainingPermissions.length === 0) {
        // No permissions left, deactivate the assignment
        await prisma.elderPermission.update({
          where: { id: existingPermission.id },
          data: { isActive: false },
        });
      } else {
        // Update with remaining permissions
        await prisma.elderPermission.update({
          where: { id: existingPermission.id },
          data: { permissions: remainingPermissions },
        });
      }

      // Log the permission change
      await this.logPermissionChange(
        congregationId,
        request.userId,
        'revoke',
        request.sectionId,
        request.permissions,
        context.performedBy,
        request.reason,
        context.ipAddress,
        context.userAgent
      );
    } else {
      // Revoke all permissions for the section
      await prisma.elderPermission.update({
        where: { id: existingPermission.id },
        data: { isActive: false },
      });

      // Log the permission change
      await this.logPermissionChange(
        congregationId,
        request.userId,
        'revoke',
        request.sectionId,
        existingPermission.permissions as string[],
        context.performedBy,
        request.reason,
        context.ipAddress,
        context.userAgent
      );
    }
  }

  /**
   * Get user permissions for a specific user
   */
  static async getUserPermissions(
    congregationId: string,
    userId: string
  ): Promise<PermissionAssignment[]> {
    const permissions = await prisma.elderPermission.findMany({
      where: {
        congregationId,
        memberId: userId,
        isActive: true,
        OR: [
          { expirationDate: null },
          { expirationDate: { gt: new Date() } },
        ],
      },
      include: {
        member: { select: { name: true, role: true } },
        assignedByMember: { select: { name: true } },
      },
      orderBy: { assignedAt: 'desc' },
    });

    return permissions.map(this.mapToPermissionAssignment);
  }

  /**
   * Get all permission assignments for the congregation
   */
  static async getAllPermissionAssignments(
    congregationId: string
  ): Promise<PermissionAssignment[]> {
    const permissions = await prisma.elderPermission.findMany({
      where: {
        congregationId,
        isActive: true,
        OR: [
          { expirationDate: null },
          { expirationDate: { gt: new Date() } },
        ],
      },
      include: {
        member: { select: { name: true, role: true } },
        assignedByMember: { select: { name: true } },
      },
      orderBy: [
        { member: { name: 'asc' } },
        { sectionId: 'asc' },
      ],
    });

    return permissions.map(this.mapToPermissionAssignment);
  }

  /**
   * Expire permissions that have passed their expiration date
   */
  static async expirePermissions(congregationId: string): Promise<void> {
    const expiredPermissions = await prisma.elderPermission.findMany({
      where: {
        congregationId,
        isActive: true,
        expirationDate: {
          lte: new Date(),
        },
      },
    });

    if (expiredPermissions.length > 0) {
      // Deactivate expired permissions
      await prisma.elderPermission.updateMany({
        where: {
          id: {
            in: expiredPermissions.map(p => p.id),
          },
        },
        data: {
          isActive: false,
        },
      });

      // Log expiration for each permission
      for (const permission of expiredPermissions) {
        await this.logPermissionChange(
          congregationId,
          permission.memberId,
          'expire',
          permission.sectionId,
          permission.permissions as string[],
          'system', // System-initiated expiration
          'Permission expired automatically'
        );
      }
    }
  }

  /**
   * Get permission audit log
   */
  static async getPermissionAuditLog(
    congregationId: string,
    userId?: string,
    sectionId?: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<PermissionAuditEntry[]> {
    const where: any = { congregationId };

    if (userId) where.userId = userId;
    if (sectionId) where.sectionId = sectionId;
    if (dateRange) {
      where.timestamp = {
        gte: dateRange.start,
        lte: dateRange.end,
      };
    }

    const auditLogs = await prisma.permissionAuditLog.findMany({
      where,
      include: {
        user: { select: { name: true } },
        performedByMember: { select: { name: true } },
      },
      orderBy: { timestamp: 'desc' },
      take: 100, // Limit to last 100 entries
    });

    return auditLogs.map(log => ({
      id: log.id,
      userId: log.userId,
      userName: log.user.name,
      action: log.action as 'assign' | 'revoke' | 'modify' | 'expire',
      sectionId: log.sectionId,
      sectionName: this.getSectionName(log.sectionId),
      permissions: log.permissions as string[],
      performedBy: log.performedBy,
      performedByName: log.performedByMember.name,
      reason: log.reason || undefined,
      timestamp: log.timestamp,
      ipAddress: log.ipAddress || undefined,
      userAgent: log.userAgent || undefined,
    }));
  }

  /**
   * Validate that the performer has authority to delegate permissions
   * Authority can come from:
   * 1. Being a coordinator (full access by default)
   * 2. Using congregation PIN (full overseer access)
   */
  private static async validateDelegationAuthority(
    performerId: string,
    sectionId: string,
    congregationId: string,
    hasCongregationPinAccess?: boolean
  ): Promise<void> {
    // If user has congregation PIN access, they have full authority
    if (hasCongregationPinAccess) {
      return;
    }

    const performer = await prisma.member.findUnique({
      where: { id: performerId },
      select: { role: true, congregationId: true },
    });

    if (!performer || performer.congregationId !== congregationId) {
      throw new Error('Performer not found or not in the same congregation');
    }

    // Only coordinators can delegate permissions (unless using congregation PIN)
    if (performer.role !== 'coordinator') {
      throw new Error('Insufficient authority to delegate permissions. Only coordinators or congregation PIN holders can assign permissions.');
    }
  }

  /**
   * Validate section and permissions
   */
  private static validateSectionPermissions(sectionId: string, permissions: string[]): void {
    // Validate that the section exists
    if (!Object.values(ADMINISTRATIVE_SECTIONS).includes(sectionId as ADMINISTRATIVE_SECTIONS)) {
      throw new Error(`Invalid section: ${sectionId}`);
    }

    // Validate that permissions are not empty
    if (!permissions || permissions.length === 0) {
      throw new Error('At least one permission must be specified');
    }

    // Additional validation for specific permissions can be added here
  }

  /**
   * Validate user eligibility for section based on role
   */
  private static validateUserEligibility(userRole: string, sectionId: string): void {
    // Publishers cannot be assigned administrative permissions
    if (userRole === 'publisher') {
      throw new Error('Publishers cannot be assigned administrative permissions');
    }

    // Additional role-based validation can be added here
  }

  /**
   * Log permission changes for audit trail
   */
  private static async logPermissionChange(
    congregationId: string,
    userId: string,
    action: 'assign' | 'revoke' | 'modify' | 'expire',
    sectionId: string,
    permissions: string[],
    performedBy: string,
    reason?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await prisma.permissionAuditLog.create({
      data: {
        congregationId,
        userId,
        action,
        sectionId,
        permissions,
        performedBy,
        reason,
        ipAddress,
        userAgent,
        timestamp: new Date(),
      },
    });
  }

  /**
   * Map database result to PermissionAssignment interface
   */
  private static mapToPermissionAssignment(permission: any): PermissionAssignment {
    return {
      id: permission.id,
      userId: permission.memberId,
      userName: permission.member.name,
      userRole: permission.member.role,
      sectionId: permission.sectionId,
      sectionName: this.getSectionName(permission.sectionId),
      permissions: permission.permissions as string[],
      assignedBy: permission.assignedBy,
      assignedByName: permission.assignedByMember.name,
      assignedAt: permission.assignedAt,
      expirationDate: permission.expirationDate || undefined,
      isActive: permission.isActive,
      notes: permission.notes || undefined,
    };
  }

  /**
   * Get section name from section ID
   */
  private static getSectionName(sectionId: string): string {
    const sectionMap: Record<string, string> = {
      [ADMINISTRATIVE_SECTIONS.FIELD_SERVICE]: 'Servicio del Campo',
      [ADMINISTRATIVE_SECTIONS.MEETINGS]: 'Reuniones',
      [ADMINISTRATIVE_SECTIONS.TASKS]: 'Tareas',
      [ADMINISTRATIVE_SECTIONS.LETTERS]: 'Cartas',
      [ADMINISTRATIVE_SECTIONS.EVENTS]: 'Eventos',
    };

    return sectionMap[sectionId] || sectionId;
  }
}
