'use client';

import React, { useState } from 'react';

interface TerritoryStatusManagerProps {
  territoryId: string;
  territoryNumber: string;
  currentStatus: 'available' | 'assigned' | 'completed' | 'unavailable';
  onStatusChange?: (newStatus: string) => void;
  onClose?: () => void;
}

export default function TerritoryStatusManager({
  territoryId,
  territoryNumber,
  currentStatus,
  onStatusChange,
  onClose
}: TerritoryStatusManagerProps) {
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);
  const [reason, setReason] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const statusOptions = [
    { value: 'available', label: 'Disponible', color: 'bg-green-100 text-green-800', description: 'Territorio disponible para asignación' },
    { value: 'assigned', label: 'Asignado', color: 'bg-blue-100 text-blue-800', description: 'Territorio asignado a un hermano' },
    { value: 'completed', label: 'Completado', color: 'bg-purple-100 text-purple-800', description: 'Territorio completado, listo para reasignación' },
    { value: 'unavailable', label: 'No Disponible', color: 'bg-red-100 text-red-800', description: 'Territorio temporalmente no disponible' }
  ];

  const handleStatusUpdate = async () => {
    if (selectedStatus === currentStatus) {
      onClose?.();
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/territories/${territoryId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          status: selectedStatus,
          reason: reason.trim() || undefined,
          notes: notes.trim() || undefined
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al actualizar el estado');
      }

      const result = await response.json();
      
      // Notify parent component
      onStatusChange?.(selectedStatus);
      
      // Show success message
      alert(`Estado del territorio ${territoryNumber} actualizado a: ${statusOptions.find(s => s.value === selectedStatus)?.label}`);
      
      onClose?.();

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = statusOptions.find(s => s.value === status);
    if (!statusConfig) return null;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.color}`}>
        {statusConfig.label}
      </span>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Cambiar Estado
            </h2>
            <p className="text-sm text-gray-600">
              Territorio {territoryNumber}
            </p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={loading}
            >
              ✕
            </button>
          )}
        </div>

        {/* Current Status */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Estado Actual
          </label>
          <div className="flex items-center">
            {getStatusBadge(currentStatus)}
          </div>
        </div>

        {/* New Status Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Nuevo Estado
          </label>
          <div className="space-y-3">
            {statusOptions.map((option) => (
              <label
                key={option.value}
                className={`flex items-start p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedStatus === option.value
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <input
                  type="radio"
                  name="status"
                  value={option.value}
                  checked={selectedStatus === option.value}
                  onChange={(e) => setSelectedStatus(e.target.value as any)}
                  className="mt-1 mr-3"
                  disabled={loading}
                />
                <div className="flex-1">
                  <div className="flex items-center mb-1">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${option.color}`}>
                      {option.label}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    {option.description}
                  </p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Reason Field */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Motivo (Opcional)
          </label>
          <input
            type="text"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Ej: Territorio completado por Juan Pérez"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading}
          />
        </div>

        {/* Notes Field */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notas Adicionales (Opcional)
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Notas adicionales sobre el cambio de estado..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading}
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="text-red-600 text-sm">
              ⚠️ {error}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleStatusUpdate}
            disabled={loading || selectedStatus === currentStatus}
            className={`flex-1 py-2 px-4 rounded-md font-medium transition-colors ${
              loading || selectedStatus === currentStatus
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Actualizando...
              </div>
            ) : selectedStatus === currentStatus ? (
              'Sin Cambios'
            ) : (
              'Actualizar Estado'
            )}
          </button>
          
          {onClose && (
            <button
              onClick={onClose}
              disabled={loading}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            >
              Cancelar
            </button>
          )}
        </div>

        {/* Status Change Info */}
        {selectedStatus !== currentStatus && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="text-blue-800 text-sm">
              <strong>Cambio:</strong> {statusOptions.find(s => s.value === currentStatus)?.label} → {statusOptions.find(s => s.value === selectedStatus)?.label}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
