import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import path from 'path';
import fs from 'fs/promises';

// GET - List all backups
export async function GET(request: NextRequest) {
  try {
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin permissions
    if (!['coordinator', 'elder', 'ministerial_servant'].includes(authResult.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get backup directory
    const backupDir = path.join(process.cwd(), 'backups');

    try {
      await fs.access(backupDir);
    } catch {
      // Create backup directory if it doesn't exist
      await fs.mkdir(backupDir, { recursive: true });
      return NextResponse.json({ backups: [] });
    }

    // Read backup files
    const files = await fs.readdir(backupDir);
    const sqlFiles = files.filter(file => file.endsWith('.sql'));

    const backups = await Promise.all(
      sqlFiles.map(async (filename) => {
        const filePath = path.join(backupDir, filename);
        const stats = await fs.stat(filePath);

        return {
          id: filename,
          filename,
          fileSize: formatFileSize(stats.size),
          createdAt: stats.birthtime.toLocaleString(),
          status: 'completed' as const
        };
      })
    );

    // Sort by creation date (newest first)
    backups.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({ backups });

  } catch (error) {
    console.error('Error fetching backups:', error);
    return NextResponse.json(
      { error: 'Failed to fetch backups' },
      { status: 500 }
    );
  }
}

// POST - Create new backup
export async function POST(request: NextRequest) {
  try {
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin permissions
    if (!['coordinator', 'elder', 'ministerial_servant'].includes(authResult.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { description } = body;

    // Create backup directory if it doesn't exist
    const backupDir = path.join(process.cwd(), 'backups');
    await fs.mkdir(backupDir, { recursive: true });

    // Generate backup filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `coraldb01_${timestamp}.sql`;
    const filePath = path.join(backupDir, filename);

    try {
      console.log('Starting backup creation...');

      // Create backup using Prisma-based approach
      let backupContent;
      try {
        backupContent = await createDatabaseBackup();
        console.log(`Backup content generated: ${backupContent.length} characters`);
      } catch (backupGenError) {
        console.warn('Full backup failed, creating minimal backup:', backupGenError.message);
        backupContent = await createMinimalBackup();
        console.log(`Minimal backup content generated: ${backupContent.length} characters`);
      }

      // Write backup to file
      await fs.writeFile(filePath, backupContent, 'utf8');
      console.log(`Backup file written: ${filePath}`);

      // Verify backup file was created
      const stats = await fs.stat(filePath);

      // Log backup creation
      console.log(`Backup created successfully: ${filename} (${formatFileSize(stats.size)})`);

      return NextResponse.json({
        success: true,
        backup: {
          id: filename,
          filename,
          fileSize: formatFileSize(stats.size),
          createdAt: new Date().toLocaleString(), // Use current time instead of birthtime
          status: 'completed' as const
        }
      });

    } catch (backupError) {
      console.error('Backup creation failed:', backupError);
      console.error('Error stack:', backupError?.stack);

      // Clean up failed backup file if it exists
      try {
        await fs.unlink(filePath);
        console.log('Cleaned up failed backup file');
      } catch (cleanupError) {
        console.warn('Could not clean up failed backup file:', cleanupError);
      }

      return NextResponse.json(
        {
          error: `Failed to create database backup: ${backupError instanceof Error ? backupError.message : 'Unknown error'}`,
          details: backupError instanceof Error ? backupError.stack : String(backupError)
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error creating backup:', error);
    return NextResponse.json(
      { error: 'Failed to create backup' },
      { status: 500 }
    );
  }
}

// Create database backup using Prisma
async function createDatabaseBackup(): Promise<string> {
  const timestamp = new Date().toISOString();
  let sqlContent = `-- Database Backup Created: ${timestamp}\n`;
  sqlContent += `-- Coral Oeste Congregation Database\n\n`;

  try {
    // Dynamically discover all tables in the database
    console.log('Discovering all tables in database...');
    const allTables = await prisma.$queryRaw<Array<{ table_name: string }>>`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
      AND table_name != '_prisma_migrations'
      ORDER BY table_name;
    `;

    console.log(`Found ${allTables.length} tables to backup:`, allTables.map(t => t.table_name).join(', '));

    let totalRecordsProcessed = 0;

    for (const table of allTables) {
      try {
        const tableName = table.table_name;
        console.log(`Starting backup for table: ${tableName}`);
        sqlContent += `-- Table: ${tableName}\n`;

        // Use raw SQL to get all data from the table
        const tableData = await prisma.$queryRawUnsafe(`SELECT * FROM "${tableName}"`);

        console.log(`Table ${tableName}: Found ${tableData.length} records`);

        if (Array.isArray(tableData) && tableData.length > 0) {
          // Get column names from first row
          const columns = Object.keys(tableData[0]);
          const columnList = columns.map(col => `"${col}"`).join(', ');

          sqlContent += `INSERT INTO "${tableName}" (${columnList}) VALUES\n`;

          // Process ALL records, not just the first one
          const values = tableData.map(row => {
            const rowValues = columns.map(col => {
              const value = row[col];
              if (value === null || value === undefined) return 'NULL';
              if (typeof value === 'string') {
                // Escape single quotes and handle special characters
                const escaped = value.replace(/'/g, "''").replace(/\\/g, '\\\\');
                return `'${escaped}'`;
              }
              if (value instanceof Date) return `'${value.toISOString()}'`;
              if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
              if (typeof value === 'object') {
                // Handle JSON objects
                return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
              }
              return String(value);
            });
            return `(${rowValues.join(', ')})`;
          });

          sqlContent += values.join(',\n') + ';\n\n';
          totalRecordsProcessed += tableData.length;
          console.log(`Backup: Processed ${tableData.length} records from ${tableName}`);
        } else {
          sqlContent += `-- No data in table ${tableName}\n\n`;
        }
      } catch (tableError) {
        console.warn(`Warning: Could not backup table ${tableName}:`, tableError);
        sqlContent += `-- Warning: Could not backup table ${tableName}: ${tableError.message}\n\n`;
      }
    }

    sqlContent += `-- Backup completed: ${new Date().toISOString()}\n`;
    sqlContent += `-- Total records processed: ${totalRecordsProcessed}\n`;

    console.log(`Full database backup completed: ${totalRecordsProcessed} total records`);
    return sqlContent;

  } catch (error) {
    console.error('Error creating database backup:', error);
    throw new Error(`Failed to create database backup: ${error.message}`);
  }
}

// Create minimal backup as fallback
async function createMinimalBackup(): Promise<string> {
  const timestamp = new Date().toISOString();
  let sqlContent = `-- Minimal Database Backup Created: ${timestamp}\n`;
  sqlContent += `-- Coral Oeste Congregation Database\n\n`;

  try {
    // Get basic counts from key tables
    const congregationCount = await prisma.congregation.count();
    const memberCount = await prisma.member.count();
    const songCount = await prisma.song.count();

    sqlContent += `-- Database Statistics\n`;
    sqlContent += `-- Congregations: ${congregationCount}\n`;
    sqlContent += `-- Members: ${memberCount}\n`;
    sqlContent += `-- Songs: ${songCount}\n\n`;

    // Export only essential data (congregations and basic settings)
    const congregations = await prisma.congregation.findMany();
    if (congregations.length > 0) {
      sqlContent += `-- Essential Data: Congregations\n`;
      for (const cong of congregations) {
        sqlContent += `INSERT INTO "congregations" ("id", "name", "region", "language") VALUES `;
        sqlContent += `('${cong.id}', '${cong.name}', '${cong.region}', '${cong.language}');\n`;
      }
    }

    sqlContent += `\n-- Minimal backup completed: ${new Date().toISOString()}\n`;
    return sqlContent;

  } catch (error) {
    console.error('Error creating minimal backup:', error);
    // Return basic backup with timestamp
    return `-- Emergency Backup Created: ${timestamp}\n-- Database backup failed, but backup file created for reference\n`;
  }
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
