// Territory API Tests
// Tests for territory management API endpoints

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';

// Mock Prisma for testing
jest.mock('@/lib/prisma', () => ({
  prisma: {
    territory: {
      findMany: jest.fn(),
    },
  },
}));

// Mock auth middleware
jest.mock('@/lib/middleware/auth', () => ({
  extractAndVerifyToken: jest.fn(),
}));

describe('Territory API', () => {
  const mockPrisma = require('@/lib/prisma').prisma;
  const mockAuth = require('@/lib/middleware/auth');

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/territories', () => {
    const mockUser = {
      userId: 'user-1',
      congregationId: '1441',
      hasCongregationPinAccess: true
    };

    const mockTerritories = [
      {
        id: 'territory-1',
        congregationId: '1441',
        territoryNumber: 'T001',
        address: '123 Main St',
        status: 'available',
        notes: 'Test territory',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-15'),
        currentAssignment: null
      },
      {
        id: 'territory-2',
        congregationId: '1441',
        territoryNumber: 'T002',
        address: '456 Oak Ave',
        status: 'assigned',
        notes: null,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-10'),
        currentAssignment: {
          member: {
            id: 'member-1',
            firstName: 'Juan',
            lastName: 'Pérez'
          },
          assignedAt: new Date('2024-01-05'),
          assignedBy: 'elder-1'
        }
      }
    ];

    it('should return territories for authenticated admin user', async () => {
      mockAuth.extractAndVerifyToken.mockResolvedValue(mockUser);
      mockPrisma.territory.findMany.mockResolvedValue(mockTerritories);

      // Mock the API route
      const { GET } = require('@/app/api/territories/route');
      const mockRequest = {
        url: 'http://localhost:3000/api/territories',
        headers: new Map([['authorization', 'Bearer valid-token']])
      } as any;

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.territories).toHaveLength(2);
      expect(data.summary.total).toBe(2);
      expect(data.summary.available).toBe(1);
      expect(data.summary.assigned).toBe(1);
    });

    it('should filter territories by status', async () => {
      mockAuth.extractAndVerifyToken.mockResolvedValue(mockUser);
      const availableTerritories = mockTerritories.filter(t => t.status === 'available');
      mockPrisma.territory.findMany.mockResolvedValue(availableTerritories);

      const { GET } = require('@/app/api/territories/route');
      const mockRequest = {
        url: 'http://localhost:3000/api/territories?status=available',
        headers: new Map([['authorization', 'Bearer valid-token']])
      } as any;

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.territories).toHaveLength(1);
      expect(data.territories[0].status).toBe('available');
    });

    it('should filter territories by search term', async () => {
      mockAuth.extractAndVerifyToken.mockResolvedValue(mockUser);
      const searchResults = mockTerritories.filter(t => t.address.includes('Main'));
      mockPrisma.territory.findMany.mockResolvedValue(searchResults);

      const { GET } = require('@/app/api/territories/route');
      const mockRequest = {
        url: 'http://localhost:3000/api/territories?search=Main',
        headers: new Map([['authorization', 'Bearer valid-token']])
      } as any;

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.territories).toHaveLength(1);
      expect(data.territories[0].address).toContain('Main');
    });

    it('should return 401 for unauthenticated requests', async () => {
      mockAuth.extractAndVerifyToken.mockResolvedValue(null);

      const { GET } = require('@/app/api/territories/route');
      const mockRequest = {
        url: 'http://localhost:3000/api/territories',
        headers: new Map()
      } as any;

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Authentication required');
    });

    it('should return 403 for non-admin users', async () => {
      const nonAdminUser = { ...mockUser, hasCongregationPinAccess: false };
      mockAuth.extractAndVerifyToken.mockResolvedValue(nonAdminUser);

      const { GET } = require('@/app/api/territories/route');
      const mockRequest = {
        url: 'http://localhost:3000/api/territories',
        headers: new Map([['authorization', 'Bearer valid-token']])
      } as any;

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Admin access required for territory management');
    });

    it('should enforce congregation isolation', async () => {
      mockAuth.extractAndVerifyToken.mockResolvedValue(mockUser);

      const { GET } = require('@/app/api/territories/route');
      const mockRequest = {
        url: 'http://localhost:3000/api/territories?congregationId=different-congregation',
        headers: new Map([['authorization', 'Bearer valid-token']])
      } as any;

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Cannot access territories for different congregation');
    });

    it('should handle database errors gracefully', async () => {
      mockAuth.extractAndVerifyToken.mockResolvedValue(mockUser);
      mockPrisma.territory.findMany.mockRejectedValue(new Error('Database connection failed'));

      const { GET } = require('@/app/api/territories/route');
      const mockRequest = {
        url: 'http://localhost:3000/api/territories',
        headers: new Map([['authorization', 'Bearer valid-token']])
      } as any;

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to fetch territories');
    });

    it('should transform territory data correctly', async () => {
      mockAuth.extractAndVerifyToken.mockResolvedValue(mockUser);
      mockPrisma.territory.findMany.mockResolvedValue(mockTerritories);

      const { GET } = require('@/app/api/territories/route');
      const mockRequest = {
        url: 'http://localhost:3000/api/territories',
        headers: new Map([['authorization', 'Bearer valid-token']])
      } as any;

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(200);
      
      // Check transformed data structure
      const territory = data.territories[1]; // Assigned territory
      expect(territory).toHaveProperty('id');
      expect(territory).toHaveProperty('territoryNumber');
      expect(territory).toHaveProperty('address');
      expect(territory).toHaveProperty('status');
      expect(territory.assignedMember).toEqual({
        id: 'member-1',
        name: 'Juan Pérez',
        assignedAt: expect.any(String),
        assignedBy: 'elder-1'
      });
    });
  });

  describe('Unsupported methods', () => {
    it('should return 405 for POST requests', async () => {
      const { POST } = require('@/app/api/territories/route');
      const response = await POST();
      const data = await response.json();

      expect(response.status).toBe(405);
      expect(data.error).toContain('Method not allowed');
    });

    it('should return 405 for PUT requests', async () => {
      const { PUT } = require('@/app/api/territories/route');
      const response = await PUT();
      const data = await response.json();

      expect(response.status).toBe(405);
      expect(data.error).toContain('Method not allowed');
    });

    it('should return 405 for DELETE requests', async () => {
      const { DELETE } = require('@/app/api/territories/route');
      const response = await DELETE();
      const data = await response.json();

      expect(response.status).toBe(405);
      expect(data.error).toContain('Method not allowed');
    });
  });
});
