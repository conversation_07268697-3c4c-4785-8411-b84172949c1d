import type { Map as MapLibreMap, LngLatLike, MapOptions } from 'maplibre-gl';

// Map configuration types
export interface MapConfig {
  center: LngLatLike;
  zoom: number;
  minZoom?: number;
  maxZoom?: number;
  style: MapStyleConfig;
  attribution?: string;
}

export interface MapStyleConfig {
  version: number;
  sources: Record<string, TileSource>;
  layers: MapLayer[];
}

export interface TileSource {
  type: 'raster' | 'vector';
  tiles: string[];
  tileSize: number;
  attribution?: string;
  maxzoom?: number;
  minzoom?: number;
}

export interface MapLayer {
  id: string;
  type: 'raster' | 'fill' | 'line' | 'symbol' | 'circle';
  source: string;
  paint?: Record<string, any>;
  layout?: Record<string, any>;
}

// Territory map specific types
export interface TerritoryMapProps {
  territories?: Territory[];
  selectedTerritory?: Territory | null;
  onTerritorySelect?: (territory: Territory) => void;
  className?: string;
  height?: string | number;
  width?: string | number;
  showControls?: boolean;
  interactive?: boolean;
}

export interface Territory {
  id: string;
  territoryNumber: string;
  address: string;
  coordinates?: Coordinates;
  bounds?: MapBounds;
  boundary?: TerritoryBoundary;
  status: 'available' | 'assigned' | 'completed' | 'out_of_service';
}

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

// Territory boundary types
export interface TerritoryBoundary {
  type: 'Polygon' | 'MultiPolygon';
  coordinates: number[][][] | number[][][][]; // GeoJSON format
  properties?: {
    color?: string;
    fillColor?: string;
    fillOpacity?: number;
    strokeWidth?: number;
    strokeOpacity?: number;
  };
}

export interface BoundaryPoint {
  latitude: number;
  longitude: number;
  order: number; // For maintaining polygon point order
}

// Map service types
export interface MapService {
  map: MapLibreMap | null;
  initialize: (container: string | HTMLElement, config: MapConfig) => Promise<MapLibreMap>;
  destroy: () => void;
  setCenter: (coordinates: LngLatLike) => void;
  setZoom: (zoom: number) => void;
  fitBounds: (bounds: MapBounds) => void;
  addTerritory: (territory: Territory) => void;
  removeTerritory: (territoryId: string) => void;
  highlightTerritory: (territoryId: string) => void;
}

// Geocoding types
export interface GeocodingResult {
  latitude: number;
  longitude: number;
  display_name: string;
  address: {
    house_number?: string;
    road?: string;
    city?: string;
    state?: string;
    postcode?: string;
    country?: string;
  };
  boundingbox: [string, string, string, string]; // [south, north, west, east]
}

export interface GeocodingService {
  geocodeAddress: (address: string) => Promise<GeocodingResult | null>;
  geocodeAddresses: (addresses: string[]) => Promise<(GeocodingResult | null)[]>;
  reverseGeocode: (coordinates: Coordinates) => Promise<GeocodingResult | null>;
}

// Error types
export interface MapError {
  type: 'tile_loading' | 'network' | 'geocoding' | 'initialization' | 'performance';
  message: string;
  details?: any;
  timestamp: Date;
}

// Map event types
export interface MapEventHandlers {
  onLoad?: () => void;
  onError?: (error: MapError) => void;
  onClick?: (coordinates: Coordinates) => void;
  onTerritoryClick?: (territory: Territory) => void;
  onZoomChange?: (zoom: number) => void;
  onMoveEnd?: (center: Coordinates) => void;
}

// Configuration constants
export const DEFAULT_MAP_CONFIG: MapConfig = {
  center: [-80.2711, 25.7617], // Miami, FL coordinates
  zoom: 12,
  minZoom: 8,
  maxZoom: 18,
  style: {
    version: 8,
    sources: {
      'osm': {
        type: 'raster',
        tiles: ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
        tileSize: 256,
        attribution: '© OpenStreetMap contributors'
      }
    },
    layers: [{
      id: 'osm',
      type: 'raster',
      source: 'osm'
    }]
  }
};

export const TILE_SOURCES = {
  openstreetmap: {
    primary: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
    fallbacks: [
      'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
      'https://b.tile.openstreetmap.org/{z}/{x}/{y}.png',
      'https://c.tile.openstreetmap.org/{z}/{x}/{y}.png'
    ]
  }
} as const;
