'use client';

import React from 'react';
import TerritoryDetail from '@/components/territories/member/TerritoryDetail';

interface TerritoryDetailPageProps {
  params: {
    id: string;
  };
}

export default function TerritoryDetailPage({ params }: TerritoryDetailPageProps) {
  const handleBack = () => {
    // Navigate back to territories list
    window.history.back();
  };

  return (
    <TerritoryDetail
      territoryId={params.id}
      onBack={handleBack}
    />
  );
}
