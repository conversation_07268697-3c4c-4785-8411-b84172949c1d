'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  MapPinIcon,
  CalendarDaysIcon,
  ClockIcon,
  CheckCircleIcon,
  PlusIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import VisitLogger from '@/components/territories/member/VisitLogger';

interface Territory {
  id: string;
  territoryNumber: string;
  address: string;
  status: string;
  notes?: string;
}

interface Assignment {
  id: string;
  territory: Territory;
  assignedAt: string;
  assignedBy: {
    name: string;
    role: string;
  };
  daysAssigned: number;
  visitCount: number;
  isPartiallyCompleted: boolean;
  partialCompletionNotes?: string;
  status: string;
}

interface Visit {
  id: string;
  visitDate: string;
  isCompleted: boolean;
  notes?: string;
  addressesWorked?: string;
}

interface AssignmentStatistics {
  totalVisits: number;
  completedVisits: number;
  durationDays: number;
  isPartiallyCompleted: boolean;
  lastVisitDate?: string;
  averageVisitsPerWeek: string;
}

export default function TerritoryDetailPage() {
  const router = useRouter();
  const params = useParams();
  const territoryId = params.id as string;

  const [isLoading, setIsLoading] = useState(true);
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [visits, setVisits] = useState<Visit[]>([]);
  const [statistics, setStatistics] = useState<AssignmentStatistics | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showVisitLogger, setShowVisitLogger] = useState(false);

  useEffect(() => {
    if (territoryId) {
      loadTerritoryDetails();
    }
  }, [territoryId]);

  const loadTerritoryDetails = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');

      // Get assignment details with visits
      const response = await fetch(`/api/territories/assignments/${territoryId}/visits`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al cargar detalles del territorio');
      }

      const result = await response.json();
      setAssignment(result.data.assignment);
      setVisits(result.data.visits);
      setStatistics(result.data.statistics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCompleteTerritory = async () => {
    try {
      const token = localStorage.getItem('token');

      const response = await fetch(`/api/territories/${territoryId}/complete`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          notes: 'Territorio completado desde la interfaz de miembro',
          completionDate: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error('Error al completar territorio');
      }

      const result = await response.json();
      alert(result.data.message);
      router.push('/territorios');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Error al completar territorio');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando territorio...</p>
        </div>
      </div>
    );
  }

  if (error || !assignment) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <DocumentTextIcon className="w-16 h-16 mx-auto mb-2" />
            <p>{error || 'Territorio no encontrado'}</p>
          </div>
          <button
            onClick={() => router.push('/territorios')}
            className="text-green-600 hover:text-green-800"
          >
            ← Volver a Territorios
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Following Field Service pattern */}
      <div className="bg-green-600 text-white p-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.push('/territorios')}
              className="text-green-200 hover:text-white flex items-center"
            >
              ← Atrás
            </button>
            <h1 className="text-2xl font-bold">Territorio {assignment.territory.territoryNumber}</h1>
            <div></div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-1 sm:px-4">
        {/* Territory Info Card */}
        <div className="bg-white rounded-lg shadow-md p-2 sm:p-6 mb-4 sm:mb-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-start">
              <div className="bg-green-100 p-3 rounded-lg mr-4">
                <MapPinIcon className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Territorio {assignment.territory.territoryNumber}
                </h2>
                <p className="text-gray-600 mt-1">{assignment.territory.address}</p>
                <div className="mt-3 text-sm text-gray-500">
                  Asignado por: {assignment.assignedBy.name} el {formatDate(assignment.assignedAt)}
                </div>
              </div>
            </div>
          </div>

          {/* Statistics */}
          {statistics && (
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{statistics.totalVisits}</div>
                <div className="text-sm text-gray-600">Visitas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{statistics.durationDays}</div>
                <div className="text-sm text-gray-600">Días</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{statistics.completedVisits}</div>
                <div className="text-sm text-gray-600">Completadas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{statistics.averageVisitsPerWeek}</div>
                <div className="text-sm text-gray-600">Por Semana</div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={() => setShowVisitLogger(true)}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Registrar Visita
            </button>
            <button
              onClick={handleCompleteTerritory}
              className="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center justify-center"
            >
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Completar Territorio
            </button>
          </div>
        </div>

        {/* Visit History */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="p-2 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Historial de Visitas</h3>

            {visits.length === 0 ? (
              <div className="text-center py-8">
                <ClockIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No hay visitas registradas</p>
                <button
                  onClick={() => setShowVisitLogger(true)}
                  className="mt-4 text-blue-600 hover:text-blue-800"
                >
                  Registrar primera visita
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {visits.map((visit) => (
                  <div key={visit.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-3 ${
                          visit.isCompleted ? 'bg-green-500' : 'bg-yellow-500'
                        }`}></div>
                        <span className="font-medium">
                          {formatDateTime(visit.visitDate)}
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        visit.isCompleted
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {visit.isCompleted ? 'Completada' : 'Parcial'}
                      </span>
                    </div>

                    {visit.addressesWorked && (
                      <div className="mb-2">
                        <span className="text-sm font-medium text-gray-700">Direcciones trabajadas:</span>
                        <p className="text-sm text-gray-600">{visit.addressesWorked}</p>
                      </div>
                    )}

                    {visit.notes && (
                      <div>
                        <span className="text-sm font-medium text-gray-700">Notas:</span>
                        <p className="text-sm text-gray-600">{visit.notes}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Visit Logger Modal */}
      {showVisitLogger && (
        <VisitLogger
          assignmentId={assignment.id}
          territoryNumber={assignment.territory.territoryNumber}
          onVisitLogged={() => {
            setShowVisitLogger(false);
            loadTerritoryDetails();
          }}
          onClose={() => setShowVisitLogger(false)}
        />
      )}
    </div>
  );
}
