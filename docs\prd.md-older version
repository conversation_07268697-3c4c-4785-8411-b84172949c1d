# Hermanos App Product Requirements Document (PRD)

## Goals and Background Context

### Goals
- Create comprehensive PRD and architecture documentation to build "Hermanos" app from scratch in a new directory
- Implement multi-congregation support leveraging existing database structure for scalable congregation management
- Expand multilingual support beyond Spanish/English to accommodate diverse congregation languages
- Preserve existing theme system for section color customization and visual identity management
- Replicate all current functionality based on app screenshots without adding new features
- Maintain existing security model without over-complication beyond current implementation
- Preserve and document the critical JW.org data fetching logic and URL patterns for meeting content integration
- Use Coral Oeste App as reference architecture for building the new "Hermanos" multi-congregation system
- Preserve exact UI structure with member view side for all users and administrative section for leadership roles

### Background Context

The "Hermanos" app represents an evolution from the successful Coral Oeste App, expanding from single-congregation management to a multi-congregation platform. The existing Coral Oeste system has proven its value in managing congregation activities, and the rebuild aims to scale this proven functionality across multiple congregations while modernizing the technology stack. The critical JW.org integration logic, which required significant development effort, must be preserved and documented to ensure seamless meeting content synchronization. This rebuild focuses on architectural modernization and multi-tenancy rather than feature expansion, ensuring the proven workflows remain intact while enabling broader deployment. The first congregation will be Coral Oeste Spanish with Spanish as the default language.

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-06 | 1.0 | Initial PRD creation for "Hermanos" multi-congregation app | Product Manager |

## Requirements

### Functional Requirements

**FR1**: Authentication system supporting congregation-based login with congregation ID (4-8 digits, default 4), member PIN (4-5 digits, default 4), and role-based access control (Developer, Overseer/Coordinator, Elder, Ministerial Servant, Publisher)

**FR2**: Dual interface system with member view side accessible to all users and administrative section restricted to elders, overseers, and ministerial servants

**FR3**: Administrative delegation system where coordinator elders assign administrative sections to elders and ministerial servants who then manage members within their designated areas

**FR4**: Member management system allowing administrators to create, edit, and manage member profiles with role assignments and PIN management

**FR5**: Enhanced meeting assignment system that resolves existing assignment logic issues with improved member qualification validation, availability tracking, and conflict detection

**FR6**: Field service management enabling members to track service time and administrators to manage territories and service groups within delegated authority

**FR7**: Midweek meeting management supporting Life and Ministry Meeting Workbook integration with preserved JW.org data fetching logic and improved assignment workflows

**FR8**: Weekend meeting management for public talks, Watchtower study assignments, and visiting speaker coordination with enhanced assignment capabilities

**FR9**: Task management system allowing delegated administrators to create and assign congregation tasks with proper authorization validation

**FR10**: Assignment management for tracking personal and congregation assignments with completion status and improved assignment logic

**FR11**: Letters management system supporting PDF upload, categorization, and controlled access with administrative delegation

**FR12**: Events management for creating and managing congregation events with delegated administrative control

**FR13**: Song management with multilingual support and preserved JW.org integration for dynamic song title retrieval

**FR14**: Multi-congregation support with Coral Oeste Spanish as the first congregation and Spanish as default language

**FR15**: Multilingual support expandable beyond Spanish and English to accommodate diverse congregation languages

**FR16**: Theme and customization system for section color management and visual identity control

**FR17**: Database backup and restore functionality for data protection and recovery

**FR18**: Responsive web design supporting desktop, tablet, and mobile devices with preserved UI structure

**FR19**: Integration with JW.org data sources for meeting content and song catalog updates with preserved URL patterns and logic

**FR20**: Web-app administrative access for elders, ministerial servants, and overseers to perform leadership responsibilities

### Non-Functional Requirements

**NFR1**: Application must support concurrent access by multiple congregations (estimated 50-100 users per congregation) without performance degradation

**NFR2**: Database operations must complete within 2 seconds for standard queries and 10 seconds for complex reports

**NFR3**: System must maintain 99.5% uptime during congregation meeting times and field service periods

**NFR4**: All user data must be encrypted in transit and at rest, with secure authentication token management

**NFR5**: Application must be fully responsive and functional on mobile devices with preserved touch-optimized interfaces

**NFR6**: System must support data export and import capabilities for congregation data portability

**NFR7**: Application must gracefully handle offline scenarios with appropriate user feedback and retry mechanisms

**NFR8**: Code must maintain TypeScript strict mode compliance for type safety and maintainability

**NFR9**: Database schema must support easy migration from existing MySQL data structure to PostgreSQL

**NFR10**: System must log all administrative actions for audit trail and troubleshooting purposes

**NFR11**: JW.org integration must preserve existing caching mechanisms and fallback strategies for reliability

**NFR12**: Administrative delegation must maintain proper authorization validation and audit trails

## User Interface Design Goals

### Overall UX Vision

The Hermanos App will maintain the exact UI structure and design from the current Coral Oeste App while supporting multi-congregation functionality. The interface will preserve the distinctive card-based dashboard layout with Spanish-first design, ensuring familiarity for current users while providing enhanced multi-congregation capabilities. The design will maintain two distinct interfaces: the member view side accessible to all users and the administrative section for leadership roles, with consistent visual identity and navigation patterns.

### Key Interaction Paradigms

- **Dual Interface Architecture**: Separate member view and administrative interfaces with role-based access
- **Card-based Dashboard Navigation**: Preserved card layout for main navigation with consistent visual design
- **Administrative Delegation Workflow**: Interface adapts based on delegated administrative responsibilities
- **Mobile-first Responsive Design**: Touch-optimized interactions maintaining current mobile interface design
- **Modal-based Administrative Forms**: Administrative forms in overlay modals consistent with current design
- **Contextual Administrative Actions**: Actions presented based on delegated administrative authority
- **Consistent Spanish-first Interface**: Maintained Spanish terminology and interface elements

### Core Screens and Views

- **Login Screen**: Congregation connection interface with region, ID, and PIN authentication (preserved design)
- **Member Dashboard**: Card-based navigation hub with Servicio del Campo, Reuniones, Asignaciones, Tareas, Cartas, Eventos (exact current layout)
- **Administrative Dashboard**: Separate admin interface for Member Management, Meeting Administration, Task Management (preserved admin design)
- **Meeting Management**: Separate interfaces for midweek and weekend meeting coordination (current design preserved)
- **Field Service Interface**: Service time tracking and territory management (current layout maintained)
- **Assignment Management**: Personal assignment tracking with improved assignment logic (preserved UI)
- **Letters & Events**: Communication and event management with current file upload interface
- **Administrative Settings**: Congregation settings and delegation management (preserved admin interface)

### Accessibility: WCAG AA

The application will comply with WCAG AA standards while maintaining the current visual design, including proper color contrast ratios, keyboard navigation support, screen reader compatibility, and alternative text for images.

### Branding

Preserve the exact visual design from current screenshots including card layouts, color schemes, typography, and Spanish interface elements. Maintain the theme system for section color customization while ensuring visual consistency across the multi-congregation platform.

### Target Device and Platforms: Web Responsive

Fully responsive web application optimized for desktop, tablet, and mobile devices, maintaining the current mobile-first design approach with enhanced desktop administrative interfaces.

## Technical Assumptions

### Repository Structure: Monorepo
Single repository containing both frontend (NextJS) and backend (NodeJS) with shared types and utilities, enabling coordinated development and deployment.

### Service Architecture
Monolithic architecture with clear separation between frontend and backend services. NextJS application handling UI and API routes, with dedicated NodeJS backend for complex business logic and preserved JW.org integration services. Multi-congregation tenancy through database-level isolation.

### Testing Requirements
Unit testing for critical business logic, integration testing for API endpoints, and manual testing convenience methods for JW.org data fetching validation. Focus on testing the preserved meeting content synchronization logic and improved assignment algorithms.

### Additional Technical Assumptions and Requests

- **Database**: PostgreSQL with Prisma ORM for type-safe database operations and easy schema migrations from existing MySQL structure
- **Frontend Framework**: NextJS 14+ with App Router for modern React development and API routes
- **Backend Runtime**: NodeJS with Express for API services and preserved JW.org integration logic
- **Authentication**: JWT-based authentication preserving existing congregation ID/PIN model with enhanced multi-congregation support
- **Multi-tenancy**: Database-level tenant isolation using congregation_id foreign keys with proper data separation
- **JW.org Integration**: Preserve existing URL patterns, caching mechanisms, and data fetching logic exactly as implemented
- **Styling**: Tailwind CSS for utility-first styling with preserved theme system for section colors and visual identity
- **Language Support**: i18next for internationalization supporting multiple languages beyond Spanish/English
- **File Storage**: Local filesystem storage for PDFs and documents, maintaining existing upload patterns and file management
- **Development Tools**: TypeScript for type safety, ESLint/Prettier for code quality, maintaining current development standards
- **Deployment**: Docker containerization for consistent deployment across environments
- **PIN Configuration**: Congregation PIN 4-8 digits (default 4), Member PIN 4-5 digits (default 4), configurable by developer role
- **Administrative Delegation**: Proper authorization validation and audit trails for delegated administrative responsibilities

## Epic List

### Epic Overview

**Epic 1: Foundation & Multi-Congregation Infrastructure**
Establish NextJS/PostgreSQL project setup, authentication system, and multi-congregation database architecture with Coral Oeste Spanish as the default congregation (Spanish language).

**Epic 2: Core Member & Authentication System**
Implement comprehensive member management, role-based permissions, congregation-specific authentication workflows, and administrative delegation system with Coral Oeste Spanish congregation data.

**Epic 3: Meeting Management & JW.org Integration**
Build midweek and weekend meeting management with preserved JW.org data fetching logic, meeting content synchronization, and significantly improved assignment system that resolves congregation member assignment issues.

**Epic 4: Field Service & Task Management**
Implement field service tracking, territory management, and congregation task assignment systems with proper administrative delegation hierarchy where coordinator elders assign sections to administrators.

**Epic 5: Communication & Content Management**
Build letters management, events system, and song management with multilingual support, theme customization, and proper administrative delegation for content management.

## Epic Details

### Epic 1: Foundation & Multi-Congregation Infrastructure

**Epic Goal:** Establish the foundational NextJS/PostgreSQL application with multi-congregation architecture, authentication system, and basic congregation management, with Coral Oeste Spanish as the first congregation. This epic delivers a deployable application with working authentication, congregation switching capabilities, and preserved UI structure.

#### Story 1.1: Project Setup and Development Environment
As a developer,
I want to set up a NextJS project with PostgreSQL and Prisma,
so that I have a modern development environment for building the Hermanos app.

**Acceptance Criteria:**
1. NextJS 14+ project created with TypeScript and App Router configuration
2. PostgreSQL database connection established with Prisma ORM integration
3. Development environment includes ESLint, Prettier, and Tailwind CSS
4. Docker configuration for consistent development and deployment
5. Basic project structure with separate frontend and backend concerns
6. Environment variable configuration for database and application settings
7. Health check endpoint returns successful response

#### Story 1.2: Multi-Congregation Database Schema
As a system administrator,
I want a database schema that supports multiple congregations,
so that the Hermanos app can serve different congregations independently.

**Acceptance Criteria:**
1. Prisma schema defines congregations table with name, region, and language settings
2. All feature tables include congregation_id foreign key for tenant isolation
3. Database migration creates initial schema with proper relationships
4. Coral Oeste Spanish congregation record created as default
5. Database indexes optimized for multi-congregation queries
6. Schema supports existing MySQL data structure for migration compatibility
7. All tables include proper timestamps and audit fields

#### Story 1.3: Enhanced Authentication System
As a congregation member,
I want to authenticate using congregation ID and PIN with proper validation,
so that I can access my congregation's features securely.

**Acceptance Criteria:**
1. Login page accepts congregation ID (4-8 digits), PIN (4-5 digits), and region selection
2. JWT authentication system validates congregation credentials with proper length validation
3. Role-based access control supports Developer, Overseer, Elder, Ministerial Servant, Publisher roles
4. Authentication middleware protects API routes with proper authorization
5. Session management with proper token expiration and renewal
6. Coral Oeste Spanish congregation authentication works with existing credentials
7. PIN length configuration by developer role per congregation

#### Story 1.4: Dual Interface Dashboard System
As a congregation member,
I want access to the appropriate interface based on my role,
so that I can navigate to the tools I need with preserved UI design.

**Acceptance Criteria:**
1. Member view dashboard displays card-based navigation for all users (preserved current design)
2. Administrative section accessible only to elders, overseers, and ministerial servants
3. Theme system allows section color customization with preserved color schemes
4. Mobile-first responsive design maintains current interface layouts
5. Spanish-first interface with preserved terminology and navigation elements
6. Logout functionality clears session and redirects to login
7. Dashboard shows congregation-specific welcome message and branding

### Epic 2: Core Member & Authentication System

**Epic Goal:** Implement comprehensive member management, role-based permissions, congregation-specific authentication workflows, and administrative delegation system with full CRUD operations for members. This epic delivers complete user management capabilities with proper security, multi-congregation support, and administrative delegation hierarchy.

#### Story 2.1: Administrative Delegation System
As a coordinator elder,
I want to assign specific administrative sections to elders and ministerial servants,
so that I can delegate administrative responsibilities while maintaining oversight and control.

**Acceptance Criteria:**
1. Administrative section assignment interface for coordinator elders to delegate responsibilities
2. Section-based permission system (Field Service, Meetings, Tasks, Letters, Events, etc.)
3. Elder and ministerial servant assignment to multiple sections with defined scope
4. Assignment history tracking and modification capabilities
5. Section responsibility transfer workflow with proper handover procedures
6. Administrative assignment notifications and confirmation system
7. Coordinator oversight dashboard showing all section assignments and activities

#### Story 2.2: Enhanced Member Profile Management
As a congregation administrator,
I want to create and manage member profiles with roles and permissions,
so that I can maintain accurate congregation membership records with proper delegation authority.

**Acceptance Criteria:**
1. Admin interface for creating new members with name, contact information, and role assignment
2. Member profile editing with validation for required fields and delegation authority validation
3. Role assignment supports all congregation roles with proper authorization checking
4. Member profiles are congregation-specific and isolated by congregation_id
5. Member search and filtering functionality by name, role, and status
6. Member deactivation/reactivation without data deletion
7. Audit trail for member profile changes with timestamp and admin identification

#### Story 2.3: Enhanced PIN Management and Security
As a congregation administrator,
I want to manage member PINs with configurable length requirements,
so that members can securely access their congregation features with proper security controls.

**Acceptance Criteria:**
1. PIN generation and assignment for new members with length validation
2. PIN reset functionality for existing members with proper authorization
3. PIN validation ensures uniqueness within congregation and length constraints
4. Secure PIN storage with proper hashing for both congregation and member PINs
5. PIN change history tracking for security audit
6. Developer role can configure PIN length requirements per congregation
7. PIN complexity requirements configurable per congregation with default settings

