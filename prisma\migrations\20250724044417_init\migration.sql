-- AlterTable
ALTER TABLE "letters" ADD COLUMN     "approved_at" TIMESTAMPTZ,
ADD COLUMN     "approved_by_id" TEXT,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "download_count" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "expiration_date" DATE,
ADD COLUMN     "folder_id" TEXT,
ADD COLUMN     "parent_id" TEXT,
ADD COLUMN     "priority" VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
ADD COLUMN     "publish_date" DATE,
ADD COLUMN     "searchable_text" TEXT,
ADD COLUMN     "status" VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
ADD COLUMN     "subcategory" VARCHAR(100),
ADD COLUMN     "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "thumbnail_path" TEXT,
ADD COLUMN     "version" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "view_count" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "document_folders" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "parent_id" TEXT,
    "path" TEXT NOT NULL,
    "color" VARCHAR(20),
    "icon" VARCHAR(50),
    "visibility" VARCHAR(50) NOT NULL DEFAULT 'ALL_MEMBERS',
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "document_folders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "document_access_logs" (
    "id" TEXT NOT NULL,
    "document_id" TEXT NOT NULL,
    "member_id" TEXT NOT NULL,
    "accessType" VARCHAR(20) NOT NULL,
    "ip_address" VARCHAR(45),
    "user_agent" TEXT,
    "accessed_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "document_access_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "document_comments" (
    "id" TEXT NOT NULL,
    "document_id" TEXT NOT NULL,
    "member_id" TEXT NOT NULL,
    "parent_id" TEXT,
    "content" TEXT NOT NULL,
    "is_internal" BOOLEAN NOT NULL DEFAULT false,
    "is_resolved" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "document_comments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "document_workflows" (
    "id" TEXT NOT NULL,
    "document_id" TEXT NOT NULL,
    "workflowType" VARCHAR(50) NOT NULL,
    "status" VARCHAR(20) NOT NULL,
    "assigned_to_id" TEXT,
    "assigned_by_id" TEXT NOT NULL,
    "priority" VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
    "due_date" DATE,
    "comments" TEXT,
    "completed_at" TIMESTAMPTZ,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "document_workflows_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notifications" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "recipient_id" TEXT NOT NULL,
    "sender_id" TEXT,
    "title" VARCHAR(255) NOT NULL,
    "message" TEXT NOT NULL,
    "category" VARCHAR(50) NOT NULL,
    "priority" VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
    "delivery_method" TEXT[],
    "status" VARCHAR(20) NOT NULL DEFAULT 'DELIVERED',
    "scheduled_for" TIMESTAMPTZ,
    "delivered_at" TIMESTAMPTZ,
    "read_at" TIMESTAMPTZ,
    "metadata" JSON,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "communication_preferences" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "email_notifications" BOOLEAN NOT NULL DEFAULT true,
    "sms_notifications" BOOLEAN NOT NULL DEFAULT false,
    "in_app_notifications" BOOLEAN NOT NULL DEFAULT true,
    "quiet_hours_start" VARCHAR(10),
    "quiet_hours_end" VARCHAR(10),
    "category_preferences" JSON NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "communication_preferences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "communication_templates" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "message" TEXT NOT NULL,
    "category" VARCHAR(50) NOT NULL,
    "variables" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "communication_templates_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "document_folders_congregation_id_parent_id_idx" ON "document_folders"("congregation_id", "parent_id");

-- CreateIndex
CREATE INDEX "document_folders_congregation_id_path_idx" ON "document_folders"("congregation_id", "path");

-- CreateIndex
CREATE INDEX "document_access_logs_document_id_accessed_at_idx" ON "document_access_logs"("document_id", "accessed_at");

-- CreateIndex
CREATE INDEX "document_access_logs_member_id_accessed_at_idx" ON "document_access_logs"("member_id", "accessed_at");

-- CreateIndex
CREATE INDEX "document_comments_document_id_created_at_idx" ON "document_comments"("document_id", "created_at");

-- CreateIndex
CREATE INDEX "document_comments_member_id_created_at_idx" ON "document_comments"("member_id", "created_at");

-- CreateIndex
CREATE INDEX "document_workflows_document_id_status_idx" ON "document_workflows"("document_id", "status");

-- CreateIndex
CREATE INDEX "document_workflows_assigned_to_id_status_idx" ON "document_workflows"("assigned_to_id", "status");

-- CreateIndex
CREATE INDEX "document_workflows_due_date_idx" ON "document_workflows"("due_date");

-- CreateIndex
CREATE INDEX "notifications_congregation_id_recipient_id_read_at_idx" ON "notifications"("congregation_id", "recipient_id", "read_at");

-- CreateIndex
CREATE INDEX "notifications_congregation_id_category_idx" ON "notifications"("congregation_id", "category");

-- CreateIndex
CREATE INDEX "notifications_congregation_id_status_idx" ON "notifications"("congregation_id", "status");

-- CreateIndex
CREATE INDEX "notifications_congregation_id_scheduled_for_idx" ON "notifications"("congregation_id", "scheduled_for");

-- CreateIndex
CREATE INDEX "communication_preferences_congregation_id_member_id_idx" ON "communication_preferences"("congregation_id", "member_id");

-- CreateIndex
CREATE UNIQUE INDEX "communication_preferences_congregation_id_member_id_key" ON "communication_preferences"("congregation_id", "member_id");

-- CreateIndex
CREATE INDEX "communication_templates_congregation_id_category_idx" ON "communication_templates"("congregation_id", "category");

-- CreateIndex
CREATE INDEX "communication_templates_congregation_id_name_idx" ON "communication_templates"("congregation_id", "name");

-- CreateIndex
CREATE INDEX "letters_congregation_id_status_idx" ON "letters"("congregation_id", "status");

-- CreateIndex
CREATE INDEX "letters_congregation_id_folder_id_idx" ON "letters"("congregation_id", "folder_id");

-- CreateIndex
CREATE INDEX "letters_congregation_id_upload_date_idx" ON "letters"("congregation_id", "upload_date");

-- CreateIndex
CREATE INDEX "letters_congregation_id_expiration_date_idx" ON "letters"("congregation_id", "expiration_date");

-- CreateIndex
CREATE INDEX "letters_title_description_searchable_text_idx" ON "letters"("title", "description", "searchable_text");

-- AddForeignKey
ALTER TABLE "letters" ADD CONSTRAINT "letters_approved_by_id_fkey" FOREIGN KEY ("approved_by_id") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "letters" ADD CONSTRAINT "letters_folder_id_fkey" FOREIGN KEY ("folder_id") REFERENCES "document_folders"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "letters" ADD CONSTRAINT "letters_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "letters"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_folders" ADD CONSTRAINT "document_folders_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_folders" ADD CONSTRAINT "document_folders_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "document_folders"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_access_logs" ADD CONSTRAINT "document_access_logs_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "letters"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_access_logs" ADD CONSTRAINT "document_access_logs_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_comments" ADD CONSTRAINT "document_comments_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "letters"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_comments" ADD CONSTRAINT "document_comments_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_comments" ADD CONSTRAINT "document_comments_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "document_comments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_workflows" ADD CONSTRAINT "document_workflows_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "letters"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_workflows" ADD CONSTRAINT "document_workflows_assigned_to_id_fkey" FOREIGN KEY ("assigned_to_id") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "document_workflows" ADD CONSTRAINT "document_workflows_assigned_by_id_fkey" FOREIGN KEY ("assigned_by_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_recipient_id_fkey" FOREIGN KEY ("recipient_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_sender_id_fkey" FOREIGN KEY ("sender_id") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "communication_preferences" ADD CONSTRAINT "communication_preferences_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "communication_preferences" ADD CONSTRAINT "communication_preferences_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "communication_templates" ADD CONSTRAINT "communication_templates_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
