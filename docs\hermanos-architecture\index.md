# Hermanos App Full-Stack Architecture Document

## Table of Contents

- [Introduction](./introduction.md)
  - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
  - [Change Log](./introduction.md#change-log)
- [High Level Architecture](./high-level-architecture.md)
  - [Technical Summary](./high-level-architecture.md#technical-summary)
  - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
  - [Repository Structure](./high-level-architecture.md#repository-structure)
  - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
- [Tech Stack](./tech-stack.md)
  - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Additional Key Dependencies](./tech-stack.md#additional-key-dependencies)
- [Data Models](./data-models.md)
  - [Congregation](./data-models.md#congregation)
  - [Member](./data-models.md#member)
  - [Meeting](./data-models.md#meeting)
  - [MeetingPart](./data-models.md#meetingpart)
  - [Task](./data-models.md#task)
  - [FieldServiceRecord](./data-models.md#fieldservicerecord)
  - [Letter](./data-models.md#letter)
- [API Specification](./api-specification.md)
  - [tRPC Router Definitions](./api-specification.md#trpc-router-definitions)
- [External APIs](./external-apis.md)
  - [JW.org Data Scraping](./external-apis.md#jworg-data-scraping)
- [Database Schema](./database-schema.md)
  - [PostgreSQL Schema with Prisma ORM](./database-schema.md#postgresql-schema-with-prisma-orm)
- [Unified Project Structure](./unified-project-structure.md)
  - [Complete Project Structure](./unified-project-structure.md#complete-project-structure)
- [Security and Performance](./security-and-performance.md)
  - [Security Requirements](./security-and-performance.md#security-requirements)
  - [Performance Optimization](./security-and-performance.md#performance-optimization)
- [Coding Standards](./coding-standards.md)
  - [Critical Fullstack Rules](./coding-standards.md#critical-fullstack-rules)
  - [Naming Conventions](./coding-standards.md#naming-conventions)
- [Monitoring and Logging](./monitoring-and-logging.md)
  - [MoSCoW Prioritization](./monitoring-and-logging.md#moscow-prioritization)
  - [Authentication Requirements](./monitoring-and-logging.md#authentication-requirements-must-have)
- [Conclusion and Next Steps](./conclusion-and-next-steps.md)
  - [Architecture Summary](./conclusion-and-next-steps.md#architecture-summary)
  - [Implementation Roadmap](./conclusion-and-next-steps.md#implementation-roadmap)
  - [Critical Success Factors](./conclusion-and-next-steps.md#critical-success-factors)
  - [Risk Mitigation](./conclusion-and-next-steps.md#risk-mitigation)
  - [Immediate Next Steps](./conclusion-and-next-steps.md#immediate-next-steps)
