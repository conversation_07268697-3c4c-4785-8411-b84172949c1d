/**
 * Member History Component
 * 
 * Displays the change history and audit trail for member profiles
 * with detailed information about who made changes and when.
 */

import React from 'react';
import { MemberChangeRecord } from '@/lib/services/memberManagementService';

interface MemberHistoryProps {
  history: MemberChangeRecord[];
  memberName?: string;
  isLoading: boolean;
  onClose: () => void;
}

const getChangeTypeDisplayName = (changeType: string): string => {
  const changeTypes = {
    'created': 'Creado',
    'updated': 'Actualizado',
    'deactivated': 'Desactivado',
    'reactivated': 'Reactivado',
    'role_changed': 'Rol Cambiado',
  };
  return changeTypes[changeType as keyof typeof changeTypes] || changeType;
};

const getChangeTypeColor = (changeType: string): string => {
  const changeColors = {
    'created': 'bg-green-100 text-green-800',
    'updated': 'bg-blue-100 text-blue-800',
    'deactivated': 'bg-red-100 text-red-800',
    'reactivated': 'bg-green-100 text-green-800',
    'role_changed': 'bg-purple-100 text-purple-800',
  };
  return changeColors[changeType as keyof typeof changeColors] || 'bg-gray-100 text-gray-800';
};

const getFieldDisplayName = (fieldName: string | null): string => {
  if (!fieldName) return '';
  
  const fieldNames = {
    'name': 'Nombre',
    'email': 'Email',
    'role': 'Rol',
    'pin': 'PIN',
    'isActive': 'Estado',
  };
  return fieldNames[fieldName as keyof typeof fieldNames] || fieldName;
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
};

const formatValue = (value: string | null): string => {
  if (!value) return 'N/A';
  if (value === '[HIDDEN]') return '••••••••';
  if (value === 'true') return 'Activo';
  if (value === 'false') return 'Inactivo';
  return value;
};

export default function MemberHistory({
  history,
  memberName,
  isLoading,
  onClose,
}: MemberHistoryProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              Historial de Cambios
              {memberName && (
                <span className="text-gray-600 font-normal"> - {memberName}</span>
              )}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Cargando historial...</span>
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No hay cambios registrados</p>
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((record, index) => (
                <div
                  key={record.id}
                  className="bg-gray-50 rounded-lg p-4 border border-gray-200"
                >
                  {/* Change Header */}
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getChangeTypeColor(record.changeType)}`}>
                        {getChangeTypeDisplayName(record.changeType)}
                      </span>
                      {record.fieldName && (
                        <span className="text-sm text-gray-600">
                          Campo: <span className="font-medium">{getFieldDisplayName(record.fieldName)}</span>
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-gray-500">
                      #{index + 1}
                    </span>
                  </div>

                  {/* Change Details */}
                  {(record.oldValue || record.newValue) && (
                    <div className="mb-3 space-y-2">
                      {record.oldValue && (
                        <div className="text-sm">
                          <span className="text-gray-600">Valor anterior:</span>
                          <span className="ml-2 font-mono bg-red-50 text-red-800 px-2 py-1 rounded">
                            {formatValue(record.oldValue)}
                          </span>
                        </div>
                      )}
                      {record.newValue && (
                        <div className="text-sm">
                          <span className="text-gray-600">Nuevo valor:</span>
                          <span className="ml-2 font-mono bg-green-50 text-green-800 px-2 py-1 rounded">
                            {formatValue(record.newValue)}
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Reason */}
                  {record.reason && (
                    <div className="mb-3">
                      <span className="text-sm text-gray-600">Razón:</span>
                      <p className="text-sm text-gray-800 mt-1 bg-white p-2 rounded border">
                        {record.reason}
                      </p>
                    </div>
                  )}

                  {/* Footer */}
                  <div className="flex justify-between items-center text-xs text-gray-500 pt-2 border-t border-gray-200">
                    <span>
                      Realizado por: <span className="font-medium text-gray-700">{record.changedByName}</span>
                    </span>
                    <span>
                      {formatDate(record.createdAt)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">
              {history.length > 0 && (
                <>Mostrando {history.length} cambio{history.length !== 1 ? 's' : ''}</>
              )}
            </p>
            <button
              onClick={onClose}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Cerrar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
