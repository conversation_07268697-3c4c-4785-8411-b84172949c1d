// Bulk Territory Import Service
// Service for handling multiple Excel file imports with progress tracking

import { v4 as uuidv4 } from 'uuid';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import { ImportQueue } from './ImportQueue';
import {
  BulkImportFile,
  BulkImportBatch,
  BulkImportResult,
  BulkImportProgress,
  BulkProcessingConfig,
  DEFAULT_BULK_CONFIG
} from '@/types/territories/import';

export class BulkImportService {
  private static queue: ImportQueue;
  private static config: BulkProcessingConfig = DEFAULT_BULK_CONFIG;

  /**
   * Initialize the bulk import service
   */
  static initialize(config?: Partial<BulkProcessingConfig>): void {
    if (config) {
      this.config = { ...DEFAULT_BULK_CONFIG, ...config };
    }
    this.queue = ImportQueue.getInstance(this.config);
  }

  /**
   * Get the import queue instance
   */
  static getQueue(): ImportQueue {
    if (!this.queue) {
      this.initialize();
    }
    return this.queue;
  }

  /**
   * Validate files before processing
   */
  static validateFiles(files: File[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check file count
    if (files.length === 0) {
      errors.push('No files provided for import');
    }

    if (files.length > this.config.maxTotalFiles) {
      errors.push(`Too many files. Maximum allowed: ${this.config.maxTotalFiles}`);
    }

    // Check individual files
    for (const file of files) {
      // Check file size
      if (file.size > this.config.maxFileSize) {
        errors.push(`File "${file.name}" exceeds maximum size of ${this.config.maxFileSize / 1024 / 1024}MB`);
      }

      // Check file type
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel' // .xls
      ];

      if (!allowedTypes.includes(file.type)) {
        errors.push(`File "${file.name}" is not a valid Excel file`);
      }

      // Check file name
      if (!file.name || file.name.trim().length === 0) {
        errors.push('File name cannot be empty');
      }
    }

    // Check for duplicate file names
    const fileNames = files.map(f => f.name.toLowerCase());
    const duplicates = fileNames.filter((name, index) => fileNames.indexOf(name) !== index);
    if (duplicates.length > 0) {
      errors.push(`Duplicate file names found: ${duplicates.join(', ')}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Start bulk import process
   */
  static async startBulkImport(
    files: File[],
    congregationId: string,
    overwriteDuplicates: boolean = false
  ): Promise<{ success: boolean; batchId?: string; errors?: string[] }> {
    try {
      // Validate files
      const validation = this.validateFiles(files);
      if (!validation.valid) {
        return {
          success: false,
          errors: validation.errors
        };
      }

      // Create upload directory if it doesn't exist
      const uploadDir = join(process.cwd(), 'public', 'uploads', 'territories', 'bulk');
      if (!existsSync(uploadDir)) {
        await mkdir(uploadDir, { recursive: true });
      }

      // Create batch ID and prepare files
      const batchId = uuidv4();
      const bulkFiles: BulkImportFile[] = [];

      // Save files and create bulk import file objects
      for (const file of files) {
        const fileId = uuidv4();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${batchId}-${fileId}-${file.name}`;
        const filepath = join(uploadDir, filename);

        // Save file to disk
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filepath, buffer);

        const bulkFile: BulkImportFile = {
          id: fileId,
          file,
          name: file.name,
          size: file.size,
          status: 'pending',
          progress: 0
        };

        bulkFiles.push(bulkFile);

        // Set file path in queue
        this.getQueue().setJobFilePath(batchId, fileId, filepath);
      }

      // Add batch to queue
      await this.getQueue().addBatch(bulkFiles, congregationId, batchId);

      return {
        success: true,
        batchId
      };

    } catch (error) {
      console.error('Bulk import start error:', error);
      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Get bulk import progress
   */
  static getBulkImportProgress(batchId: string): BulkImportProgress | null {
    return this.getQueue().getBatchProgress(batchId);
  }

  /**
   * Get bulk import results
   */
  static async getBulkImportResults(batchId: string): Promise<BulkImportResult | null> {
    const progress = this.getQueue().getBatchProgress(batchId);
    if (!progress) return null;

    // Get batch details from queue
    const batch = (this.getQueue() as any).batches.get(batchId) as BulkImportBatch;
    if (!batch) return null;

    // Get all jobs for this batch
    const jobs = Array.from((this.getQueue() as any).jobs.values())
      .filter((job: any) => job.batchId === batchId);

    const fileResults = jobs.map((job: any) => ({
      fileName: job.fileName,
      success: job.status === 'completed',
      result: job.result,
      error: job.error
    }));

    const successfulFiles = jobs.filter((job: any) => job.status === 'completed').length;
    const failedFiles = jobs.filter((job: any) => job.status === 'failed').length;

    const totalTerritories = jobs.reduce((sum: number, job: any) => {
      return sum + (job.result?.totalRows || 0);
    }, 0);

    const successfulImports = jobs.reduce((sum: number, job: any) => {
      return sum + (job.result?.successfulImports || 0);
    }, 0);

    const failedImports = jobs.reduce((sum: number, job: any) => {
      return sum + (job.result?.failedImports || 0);
    }, 0);

    const duplicatesFound = jobs.reduce((sum: number, job: any) => {
      return sum + (job.result?.duplicatesFound || 0);
    }, 0);

    const processingTime = batch.endTime && batch.startTime 
      ? batch.endTime.getTime() - batch.startTime.getTime()
      : 0;

    const summary = this.generateBulkImportSummary({
      totalFiles: batch.totalFiles,
      successfulFiles,
      failedFiles,
      successfulImports,
      failedImports,
      duplicatesFound
    });

    return {
      batchId,
      success: progress.status === 'completed',
      totalFiles: batch.totalFiles,
      successfulFiles,
      failedFiles,
      totalTerritories,
      successfulImports,
      failedImports,
      duplicatesFound,
      fileResults,
      summary,
      processingTime
    };
  }

  /**
   * Retry failed imports in a batch
   */
  static async retryBulkImport(batchId: string): Promise<boolean> {
    return this.getQueue().retryBatch(batchId);
  }

  /**
   * Cancel a bulk import batch
   */
  static async cancelBulkImport(batchId: string): Promise<boolean> {
    return this.getQueue().cancelBatch(batchId);
  }

  /**
   * Get queue status
   */
  static getQueueStatus() {
    return this.getQueue().getQueueStatus();
  }

  /**
   * Generate bulk import summary message
   */
  private static generateBulkImportSummary(data: {
    totalFiles: number;
    successfulFiles: number;
    failedFiles: number;
    successfulImports: number;
    failedImports: number;
    duplicatesFound: number;
  }): string {
    const parts = [];

    if (data.successfulFiles > 0) {
      parts.push(`${data.successfulFiles} archivos procesados exitosamente`);
    }

    if (data.failedFiles > 0) {
      parts.push(`${data.failedFiles} archivos fallaron`);
    }

    if (data.successfulImports > 0) {
      parts.push(`${data.successfulImports} territorios importados`);
    }

    if (data.failedImports > 0) {
      parts.push(`${data.failedImports} territorios fallaron`);
    }

    if (data.duplicatesFound > 0) {
      parts.push(`${data.duplicatesFound} duplicados encontrados`);
    }

    return parts.join(', ') || 'No se procesaron archivos';
  }

  /**
   * Clean up old batch files
   */
  static async cleanupOldFiles(): Promise<void> {
    try {
      const fs = require('fs').promises;
      const path = require('path');
      const uploadDir = join(process.cwd(), 'public', 'uploads', 'territories', 'bulk');

      if (!existsSync(uploadDir)) return;

      const files = await fs.readdir(uploadDir);
      const cutoffTime = new Date(Date.now() - (this.config.cleanupAfterHours * 60 * 60 * 1000));

      for (const file of files) {
        const filePath = join(uploadDir, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime < cutoffTime) {
          await fs.unlink(filePath);
          console.log(`Cleaned up old bulk import file: ${file}`);
        }
      }
    } catch (error) {
      console.error('Error cleaning up old bulk import files:', error);
    }
  }
}
