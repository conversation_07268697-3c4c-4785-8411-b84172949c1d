/**
 * Simple test to check Territory 001 API response
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testTerritory001() {
  try {
    console.log('🧪 Testing Territory 001 data directly from database...\n');

    // Get Territory 001 directly from database
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      }
    });

    if (!territory) {
      console.log('❌ Territory 001 not found in database');
      return;
    }

    console.log('📊 Territory 001 Database Data:');
    console.log(`  ID: ${territory.id}`);
    console.log(`  Territory Number: ${territory.territoryNumber}`);
    console.log(`  Address: ${territory.address?.split('\n')[0] || 'No address'}`);
    console.log(`  Has Boundaries: ${territory.boundaries ? 'YES' : 'NO'}`);
    
    if (territory.boundaries) {
      console.log(`  Boundary Type: ${territory.boundaries.type}`);
      console.log(`  Boundary Coordinates: ${territory.boundaries.coordinates?.[0]?.length || 0} points`);
    }

    // Test what the API route logic would return
    console.log('\n🔄 Simulating API route logic...');
    
    let coordinates = null;
    let boundary = null;

    try {
      // Try to parse coordinates if they exist
      if (territory.coordinates) {
        coordinates = typeof territory.coordinates === 'string'
          ? JSON.parse(territory.coordinates)
          : territory.coordinates;
      }

      // Try to parse boundary if it exists (using the FIXED logic)
      if (territory.boundaries) {
        boundary = typeof territory.boundaries === 'string'
          ? JSON.parse(territory.boundaries)
          : territory.boundaries;
      }
    } catch (parseError) {
      console.warn('Error parsing territory coordinates/boundary:', parseError);
    }

    console.log('📤 API Response would be:');
    console.log(`  Has Coordinates: ${coordinates ? 'YES' : 'NO'}`);
    console.log(`  Has Boundary: ${boundary ? 'YES' : 'NO'}`);
    
    if (boundary) {
      console.log(`  Boundary Type: ${boundary.type}`);
      console.log(`  Boundary Coordinates: ${boundary.coordinates?.[0]?.length || 0} points`);
      console.log('\n🎉 SUCCESS: Territory 001 has boundary data!');
      console.log('   The map should display territory boundaries properly.');
    } else {
      console.log('\n❌ ISSUE: Territory 001 boundary data not being processed correctly');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testTerritory001();
