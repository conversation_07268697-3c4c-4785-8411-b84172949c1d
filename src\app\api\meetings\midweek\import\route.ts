/**
 * Midweek Meeting Workbook Import API Endpoint
 *
 * Handles importing meeting data from JW.org workbook into the congregation system.
 * Supports date range fetching and automatic meeting structure creation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { JWOrgWorkbookService, WorkbookFetchOptions } from '@/lib/services/jwOrgWorkbookService';
import { SongValidationService } from '@/lib/services/songValidationService';
import { MeetingImportService } from '@/lib/services/meetingImportService';
import { ImportAuditService } from '@/lib/services/importAuditService';

// Validation schema for import request
const ImportRequestSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  language: z.enum(['es', 'en']).default('es'),
  overwriteExisting: z.boolean().default(false),
  retryAttempts: z.number().min(1).max(5).default(3),
  timeout: z.number().min(5000).max(60000).default(30000),
});

export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { member } = authResult;

    // Check if user has permission to import meetings
    if (!['elder', 'overseer_coordinator', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to import meeting data' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = ImportRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { startDate, endDate, language, overwriteExisting, retryAttempts, timeout } = validationResult.data;

    // Start audit logging
    const auditId = await ImportAuditService.startImportOperation(
      member.congregationId,
      member.id,
      'workbook_import',
      validationResult.data
    );

    // Validate date range
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start > end) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    // Check for reasonable date range (max 3 months)
    const maxRange = 90 * 24 * 60 * 60 * 1000; // 90 days in milliseconds
    if (end.getTime() - start.getTime() > maxRange) {
      return NextResponse.json(
        { error: 'Date range cannot exceed 90 days' },
        { status: 400 }
      );
    }

    // Fetch workbook data from JW.org
    const fetchOptions: WorkbookFetchOptions = {
      startDate: start,
      endDate: end,
      language,
      retryAttempts,
      timeout,
    };

    const fetchResult = await JWOrgWorkbookService.fetchWorkbookData(fetchOptions);

    if (!fetchResult.success) {
      // Log connectivity issues
      for (const error of fetchResult.errors) {
        await ImportAuditService.logConnectivityIssue(member.congregationId, {
          timestamp: new Date(),
          url: fetchResult.fetchedFrom,
          error,
          retryAttempt: retryAttempts,
        });
      }

      // Update audit log with failure
      await ImportAuditService.updateImportOperation(
        auditId,
        'failed',
        { fetchResult },
        fetchResult.errors,
        fetchResult.warnings,
        { step: 'fetch_workbook_data' }
      );

      return NextResponse.json(
        {
          error: 'Failed to fetch workbook data',
          details: fetchResult.errors,
          warnings: fetchResult.warnings,
          fetchedFrom: fetchResult.fetchedFrom,
          auditId,
        },
        { status: 500 }
      );
    }

    // Validate fetched data
    const workbookValidationResult = JWOrgWorkbookService.validateWorkbookData(fetchResult.meetings);
    if (!workbookValidationResult.isValid) {
      return NextResponse.json(
        {
          error: 'Invalid workbook data received',
          details: workbookValidationResult.errors,
          fetchedData: fetchResult.meetings,
        },
        { status: 422 }
      );
    }

    // Extract and validate songs
    const songExtractionResult = SongValidationService.extractSongsFromMeetings(fetchResult.meetings);
    const songValidationResult = await SongValidationService.validateSongs(songExtractionResult.extractedSongs);

    // Generate missing songs report if needed
    let missingSongsReport = '';
    if (songValidationResult.missingCount > 0) {
      missingSongsReport = SongValidationService.generateMissingSongsReport(songValidationResult);
    }

    // Validate meetings before import
    const meetingValidationResult = await MeetingImportService.validateMeetingData(
      fetchResult.meetings,
      member.congregationId,
      overwriteExisting
    );

    // Import validated meetings into database
    const importResult = await MeetingImportService.importMeetings(
      meetingValidationResult.validMeetings,
      member.congregationId,
      overwriteExisting
    );

    // Prepare final results
    const finalResult = {
      success: importResult.success,
      message: importResult.success ? 'Workbook data imported successfully' : 'Import completed with errors',
      imported: importResult.imported,
      skipped: importResult.skipped,
      failed: importResult.failed,
      errors: [...importResult.errors, ...meetingValidationResult.errors],
      warnings: [...fetchResult.warnings, ...songValidationResult.warnings, ...importResult.warnings, ...meetingValidationResult.warnings],
      fetchedFrom: fetchResult.fetchedFrom,
      fetchedAt: fetchResult.fetchedAt,
      totalMeetings: fetchResult.meetings.length,
      validation: {
        totalMeetings: meetingValidationResult.totalMeetings,
        validMeetings: meetingValidationResult.validCount,
        invalidMeetings: meetingValidationResult.invalidCount,
        duplicateMeetings: meetingValidationResult.duplicateCount,
        isValid: meetingValidationResult.isValid,
        invalidMeetingsList: meetingValidationResult.invalidMeetings,
        duplicateMeetingsList: meetingValidationResult.duplicateMeetings,
      },
      songValidation: {
        totalSongs: songValidationResult.totalSongs,
        validSongs: songValidationResult.validCount,
        missingSongs: songValidationResult.missingCount,
        isValid: songValidationResult.isValid,
        missingSongsList: songValidationResult.missingSongs,
        missingSongsReport: missingSongsReport,
      },
      importDetails: {
        importedMeetings: importResult.importedMeetings,
        skippedMeetings: importResult.skippedMeetings,
        failedMeetings: importResult.failedMeetings,
      },
      auditId,
    };

    // Update audit log with completion
    await ImportAuditService.updateImportOperation(
      auditId,
      importResult.success ? 'completed' : 'failed',
      finalResult,
      finalResult.errors,
      finalResult.warnings,
      {
        fetchResult,
        validationResult,
        songValidationResult,
        importResult,
      }
    );

    return NextResponse.json(finalResult);

  } catch (error) {
    console.error('Workbook import error:', error);

    // Try to update audit log if we have an auditId
    try {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      // Note: auditId might not be defined if error occurred before audit start
      if (typeof auditId !== 'undefined') {
        await ImportAuditService.updateImportOperation(
          auditId,
          'failed',
          {},
          [errorMessage],
          [],
          { step: 'unexpected_error', error: errorMessage }
        );
      }
    } catch (auditError) {
      console.error('Failed to update audit log:', auditError);
    }

    return NextResponse.json(
      {
        error: 'Internal server error during import',
        message: error instanceof Error ? error.message : 'Unknown error',
        auditId: typeof auditId !== 'undefined' ? auditId : null,
      },
      { status: 500 }
    );
  }
}



// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to import workbook data.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to import workbook data.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to import workbook data.' },
    { status: 405 }
  );
}
