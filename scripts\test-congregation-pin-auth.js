#!/usr/bin/env node

/**
 * Test Congregation PIN Authentication
 *
 * This script tests the congregation PIN authentication flow to verify
 * that the hasCongregationPinAccess flag is properly set and returned.
 */

// Using built-in fetch API (Node.js 18+)

async function testCongregationPinAuth() {
  console.log('🧪 Testing Congregation PIN Authentication...\n');

  try {
    // Test congregation PIN login
    console.log('1️⃣ Testing congregation PIN login...');

    const loginResponse = await fetch('http://localhost:3001/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1930',
        rememberMe: false,
      }),
    });

    if (!loginResponse.ok) {
      const errorData = await loginResponse.json();
      console.error('❌ Login failed:', errorData);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful!');

    // Check the user object structure
    console.log('\n2️⃣ Checking user object structure...');
    console.log('📋 User object:');
    console.log(`   ID: ${loginData.user.id}`);
    console.log(`   Name: ${loginData.user.name}`);
    console.log(`   Role: ${loginData.user.role}`);
    console.log(`   Congregation ID: ${loginData.user.congregationId}`);
    console.log(`   Congregation Name: ${loginData.user.congregationName}`);
    console.log(`   Has Congregation PIN Access: ${loginData.user.hasCongregationPinAccess}`);

    // Check permissions
    console.log('\n📋 Permissions object:');
    console.log(`   Can Access Admin: ${loginData.permissions.canAccessAdmin}`);
    console.log(`   Can Manage Settings: ${loginData.permissions.canManageSettings}`);
    console.log(`   Has Congregation PIN Access: ${loginData.permissions.hasCongregationPinAccess}`);

    // Verify the flag is set correctly
    if (loginData.user.hasCongregationPinAccess === true) {
      console.log('\n✅ hasCongregationPinAccess flag is correctly set to true');
    } else {
      console.log('\n❌ hasCongregationPinAccess flag is NOT set correctly');
      console.log(`   Expected: true`);
      console.log(`   Actual: ${loginData.user.hasCongregationPinAccess}`);
    }

    // Test dashboard API with the token
    console.log('\n3️⃣ Testing dashboard API with congregation PIN token...');

    const dashboardResponse = await fetch('http://localhost:3001/api/dashboard', {
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
      },
    });

    if (dashboardResponse.ok) {
      const dashboardData = await dashboardResponse.json();
      console.log('✅ Dashboard API call successful!');
      console.log(`   User name: ${dashboardData.user.name}`);
      console.log(`   Has Congregation PIN Access: ${dashboardData.user.hasCongregationPinAccess}`);

      if (dashboardData.user.hasCongregationPinAccess === true) {
        console.log('✅ Dashboard API correctly preserves hasCongregationPinAccess flag');
      } else {
        console.log('❌ Dashboard API does NOT preserve hasCongregationPinAccess flag');
      }
    } else {
      console.log('❌ Dashboard API call failed');
      const errorData = await dashboardResponse.json();
      console.error('   Error:', errorData);
    }

    console.log('\n🎯 Expected Profile Dropdown Behavior:');
    console.log('   When hasCongregationPinAccess is true, the profile dropdown should show:');
    console.log('   - Coral Oeste (congregation name)');
    console.log('   - Acceso con PIN de Congregación');
    console.log('   - Cerrar Sesión');
    console.log('   ');
    console.log('   It should NOT show:');
    console.log('   - Carlos Coordinador (member name)');
    console.log('   - Coordinador (role name)');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testCongregationPinAuth();
