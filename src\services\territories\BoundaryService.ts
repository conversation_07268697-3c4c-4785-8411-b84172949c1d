import type { TerritoryBoundary, BoundaryPoint, Coordinates } from '@/types/territories/map';

/**
 * Territory Boundary Service
 * 
 * Handles territory boundary creation, editing, and management
 */
export class BoundaryService {
  
  /**
   * Create a polygon boundary from an array of points
   */
  static createPolygonBoundary(
    points: BoundaryPoint[],
    properties?: TerritoryBoundary['properties']
  ): TerritoryBoundary {
    // Sort points by order to ensure correct polygon shape
    const sortedPoints = points.sort((a, b) => a.order - b.order);
    
    // Convert to GeoJSON coordinate format [longitude, latitude]
    const coordinates = sortedPoints.map(point => [point.longitude, point.latitude]);
    
    // Close the polygon by adding the first point at the end if not already closed
    if (coordinates.length > 0) {
      const firstPoint = coordinates[0];
      const lastPoint = coordinates[coordinates.length - 1];
      if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
        coordinates.push(firstPoint);
      }
    }
    
    return {
      type: 'Polygon',
      coordinates: [coordinates], // Polygon requires array of linear rings
      properties: {
        color: '#10B981', // Default green color
        fillColor: '#10B981',
        fillOpacity: 0.2,
        strokeWidth: 2,
        strokeOpacity: 0.8,
        ...properties
      }
    };
  }
  
  /**
   * Create a rectangular boundary from two corner points
   */
  static createRectangularBoundary(
    northEast: Coordinates,
    southWest: Coordinates,
    properties?: TerritoryBoundary['properties']
  ): TerritoryBoundary {
    const coordinates = [
      [southWest.longitude, northEast.latitude], // NW
      [northEast.longitude, northEast.latitude], // NE
      [northEast.longitude, southWest.latitude], // SE
      [southWest.longitude, southWest.latitude], // SW
      [southWest.longitude, northEast.latitude]  // Close polygon
    ];
    
    return {
      type: 'Polygon',
      coordinates: [coordinates],
      properties: {
        color: '#10B981',
        fillColor: '#10B981',
        fillOpacity: 0.2,
        strokeWidth: 2,
        strokeOpacity: 0.8,
        ...properties
      }
    };
  }
  
  /**
   * Get boundary color based on territory status
   */
  static getBoundaryColorByStatus(status: string): { color: string; fillColor: string } {
    switch (status) {
      case 'available':
        return { color: '#10B981', fillColor: '#10B981' }; // Green
      case 'assigned':
        return { color: '#3B82F6', fillColor: '#3B82F6' }; // Blue
      case 'completed':
        return { color: '#F59E0B', fillColor: '#F59E0B' }; // Orange
      case 'out_of_service':
        return { color: '#EF4444', fillColor: '#EF4444' }; // Red
      default:
        return { color: '#6B7280', fillColor: '#6B7280' }; // Gray
    }
  }
  
  /**
   * Update boundary properties based on territory status
   */
  static updateBoundaryForStatus(
    boundary: TerritoryBoundary,
    status: string
  ): TerritoryBoundary {
    const colors = this.getBoundaryColorByStatus(status);
    
    return {
      ...boundary,
      properties: {
        ...boundary.properties,
        ...colors
      }
    };
  }
  
  /**
   * Calculate the center point of a boundary
   */
  static getBoundaryCenter(boundary: TerritoryBoundary): Coordinates | null {
    if (boundary.type !== 'Polygon' || !boundary.coordinates[0]) {
      return null;
    }
    
    const coordinates = boundary.coordinates[0] as number[][];
    let totalLat = 0;
    let totalLng = 0;
    let pointCount = 0;
    
    // Calculate average of all points (excluding the closing point)
    for (let i = 0; i < coordinates.length - 1; i++) {
      totalLng += coordinates[i][0];
      totalLat += coordinates[i][1];
      pointCount++;
    }
    
    if (pointCount === 0) return null;
    
    return {
      latitude: totalLat / pointCount,
      longitude: totalLng / pointCount
    };
  }
  
  /**
   * Check if a point is inside a territory boundary
   */
  static isPointInBoundary(point: Coordinates, boundary: TerritoryBoundary): boolean {
    if (boundary.type !== 'Polygon' || !boundary.coordinates[0]) {
      return false;
    }
    
    const coordinates = boundary.coordinates[0] as number[][];
    const x = point.longitude;
    const y = point.latitude;
    
    let inside = false;
    for (let i = 0, j = coordinates.length - 1; i < coordinates.length; j = i++) {
      const xi = coordinates[i][0];
      const yi = coordinates[i][1];
      const xj = coordinates[j][0];
      const yj = coordinates[j][1];
      
      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }
    
    return inside;
  }
  
  /**
   * Convert boundary to MapLibre source format
   */
  static boundaryToGeoJSON(territoryId: string, boundary: TerritoryBoundary) {
    return {
      type: 'Feature',
      id: territoryId,
      properties: {
        territoryId,
        ...boundary.properties
      },
      geometry: {
        type: boundary.type,
        coordinates: boundary.coordinates
      }
    };
  }
  
  /**
   * Create a sample boundary for testing (around Miami area)
   */
  static createSampleBoundary(centerLat: number, centerLng: number, radiusKm: number = 0.5): TerritoryBoundary {
    // Create a rough circle approximation using 8 points
    const points: BoundaryPoint[] = [];
    const earthRadius = 6371; // km
    
    for (let i = 0; i < 8; i++) {
      const angle = (i * 2 * Math.PI) / 8;
      const deltaLat = (radiusKm / earthRadius) * (180 / Math.PI);
      const deltaLng = (radiusKm / earthRadius) * (180 / Math.PI) / Math.cos(centerLat * Math.PI / 180);
      
      const lat = centerLat + deltaLat * Math.cos(angle);
      const lng = centerLng + deltaLng * Math.sin(angle);
      
      points.push({
        latitude: lat,
        longitude: lng,
        order: i
      });
    }
    
    return this.createPolygonBoundary(points);
  }
}

export default BoundaryService;
