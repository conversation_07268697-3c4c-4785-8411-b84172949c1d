#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function checkAuth() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 Checking congregations...');
    const congregations = await prisma.congregation.findMany({
      where: { isActive: true },
      select: { id: true, name: true, pin: true }
    });

    console.log(`Found ${congregations.length} congregations:`);
    for (const cong of congregations) {
      console.log(`- ID: ${cong.id}, Name: ${cong.name}`);

      // Test different PINs
      const testPins = ['coralpin123', '1930', '5555', 'dev123'];
      for (const pin of testPins) {
        try {
          const isValid = await bcrypt.compare(pin, cong.pin);
          if (isValid) {
            console.log(`  ✅ PIN '${pin}' works for ${cong.id}`);
          }
        } catch (e) {
          // Try plain text comparison for legacy
          if (pin === cong.pin) {
            console.log(`  ✅ PIN '${pin}' works for ${cong.id} (plain text)`);
          }
        }
      }
    }

    console.log('\n🔍 Checking members...');
    const members = await prisma.member.findMany({
      where: { isActive: true },
      select: { id: true, name: true, role: true, congregationId: true }
    });

    console.log(`Found ${members.length} active members`);

    // Group by role
    const roleGroups = {};
    members.forEach(member => {
      if (!roleGroups[member.role]) {
        roleGroups[member.role] = [];
      }
      roleGroups[member.role].push(member);
    });

    console.log('\n📊 Members by role:');
    Object.keys(roleGroups).forEach(role => {
      console.log(`\n${role.toUpperCase()}: ${roleGroups[role].length}`);
      roleGroups[role].forEach(member => {
        console.log(`  - ${member.name} (${member.congregationId})`);
      });
    });

    // Check admin access
    const adminRoles = ['elder', 'overseer_coordinator', 'developer', 'ministerial_servant'];
    const adminMembers = members.filter(m => adminRoles.includes(m.role));
    console.log(`\n👑 Members with admin access: ${adminMembers.length}`);
    adminMembers.forEach(member => {
      console.log(`  - ${member.name} (${member.role}) - ${member.congregationId}`);
    });

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkAuth();
