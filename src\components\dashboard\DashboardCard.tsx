/**
 * Dashboard Card Component
 * 
 * Reusable card component for dashboard navigation with consistent styling,
 * touch-friendly design, and Spanish-first interface.
 */

import React from 'react';

interface DashboardCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'yellow' | 'indigo' | 'pink';
  onClick: () => void;
  badge?: string | number;
  disabled?: boolean;
  className?: string;
}

const colorClasses = {
  blue: {
    bg: 'bg-blue-50',
    border: 'border-blue-200',
    icon: 'text-blue-600',
    title: 'text-blue-900',
    description: 'text-blue-700',
    hover: 'hover:bg-blue-100',
    badge: 'bg-blue-500 text-white',
  },
  green: {
    bg: 'bg-green-50',
    border: 'border-green-200',
    icon: 'text-green-600',
    title: 'text-green-900',
    description: 'text-green-700',
    hover: 'hover:bg-green-100',
    badge: 'bg-green-500 text-white',
  },
  purple: {
    bg: 'bg-purple-50',
    border: 'border-purple-200',
    icon: 'text-purple-600',
    title: 'text-purple-900',
    description: 'text-purple-700',
    hover: 'hover:bg-purple-100',
    badge: 'bg-purple-500 text-white',
  },
  orange: {
    bg: 'bg-orange-50',
    border: 'border-orange-200',
    icon: 'text-orange-600',
    title: 'text-orange-900',
    description: 'text-orange-700',
    hover: 'hover:bg-orange-100',
    badge: 'bg-orange-500 text-white',
  },
  red: {
    bg: 'bg-red-50',
    border: 'border-red-200',
    icon: 'text-red-600',
    title: 'text-red-900',
    description: 'text-red-700',
    hover: 'hover:bg-red-100',
    badge: 'bg-red-500 text-white',
  },
  yellow: {
    bg: 'bg-yellow-50',
    border: 'border-yellow-200',
    icon: 'text-yellow-600',
    title: 'text-yellow-900',
    description: 'text-yellow-700',
    hover: 'hover:bg-yellow-100',
    badge: 'bg-yellow-500 text-white',
  },
  indigo: {
    bg: 'bg-indigo-50',
    border: 'border-indigo-200',
    icon: 'text-indigo-600',
    title: 'text-indigo-900',
    description: 'text-indigo-700',
    hover: 'hover:bg-indigo-100',
    badge: 'bg-indigo-500 text-white',
  },
  pink: {
    bg: 'bg-pink-50',
    border: 'border-pink-200',
    icon: 'text-pink-600',
    title: 'text-pink-900',
    description: 'text-pink-700',
    hover: 'hover:bg-pink-100',
    badge: 'bg-pink-500 text-white',
  },
};

export default function DashboardCard({
  title,
  description,
  icon,
  color,
  onClick,
  badge,
  disabled = false,
  className = '',
}: DashboardCardProps) {
  const colors = colorClasses[color];

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        relative w-full p-6 rounded-xl border-2 transition-all duration-200
        min-h-[120px] text-left
        ${colors.bg} ${colors.border} ${colors.hover}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer active:scale-95'}
        focus:outline-none focus:ring-4 focus:ring-blue-200
        ${className}
      `}
    >
      {/* Badge */}
      {badge && (
        <div className={`
          absolute -top-2 -right-2 px-2 py-1 rounded-full text-xs font-bold
          ${colors.badge}
          min-w-[24px] h-6 flex items-center justify-center
        `}>
          {badge}
        </div>
      )}

      {/* Icon */}
      <div className={`mb-3 ${colors.icon}`}>
        <div className="w-8 h-8">
          {icon}
        </div>
      </div>

      {/* Content */}
      <div>
        <h3 className={`text-lg font-semibold mb-2 ${colors.title}`}>
          {title}
        </h3>
        <p className={`text-sm ${colors.description} leading-relaxed`}>
          {description}
        </p>
      </div>
    </button>
  );
}
