#!/usr/bin/env node

/**
 * Authentication Data Setup Script for Hermanos App
 *
 * Creates sample congregation and member data with properly hashed PINs
 * for testing the authentication system.
 *
 * Usage: node scripts/setup-auth-data.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

class AuthDataSetup {
  constructor() {
    this.prisma = new PrismaClient();
  }

  async hashPin(pin) {
    const saltRounds = 12;
    return await bcrypt.hash(pin, saltRounds);
  }

  async setupCongregationData() {
    console.log('🔐 Setting up authentication data...');

    try {
      // Create Coral Oeste congregation with hashed PIN
      const congregationPin = 'coralpin123'; // Default PIN for testing
      const hashedCongregationPin = await this.hashPin(congregationPin);

      const congregation = await this.prisma.congregation.upsert({
        where: { id: 'CORALOES' },
        update: {
          pin: hashedCongregationPin,
          name: 'Coral Oeste',
          region: 'Mexico',
          language: 'es',
          timezone: 'America/Mexico_City',
          settings: {
            meetingDay: 'Thursday',
            meetingTime: '19:00',
            kingdomHallAddress: 'Salón del Reino Coral Oeste'
          },
          isActive: true,
        },
        create: {
          id: 'CORALOES',
          name: 'Coral Oeste',
          region: 'Mexico',
          pin: hashedCongregationPin,
          language: 'es',
          timezone: 'America/Mexico_City',
          settings: {
            meetingDay: 'Thursday',
            meetingTime: '19:00',
            kingdomHallAddress: 'Salón del Reino Coral Oeste'
          },
          isActive: true,
        },
      });

      console.log(`✅ Created congregation: ${congregation.name}`);
      console.log(`📋 Congregation ID: ${congregation.id}`);
      console.log(`🔑 Congregation PIN: ${congregationPin} (for testing)`);

      // Create roles
      const roles = [
        {
          name: 'publisher',
          description: 'Publicador',
          permissions: ['view_dashboard', 'view_profile', 'edit_profile']
        },
        {
          name: 'ministerial_servant',
          description: 'Siervo Ministerial',
          permissions: ['view_dashboard', 'view_profile', 'edit_profile', 'view_admin', 'manage_tasks']
        },
        {
          name: 'elder',
          description: 'Anciano',
          permissions: ['view_dashboard', 'view_profile', 'edit_profile', 'view_admin', 'manage_members', 'manage_tasks', 'manage_meetings', 'manage_letters']
        },
        {
          name: 'overseer_coordinator',
          description: 'Superintendente/Coordinador',
          permissions: ['view_dashboard', 'view_profile', 'edit_profile', 'view_admin', 'manage_members', 'manage_tasks', 'manage_meetings', 'manage_letters', 'manage_congregation_settings']
        },
        {
          name: 'developer',
          description: 'Desarrollador',
          permissions: ['all']
        },
      ];

      for (const roleData of roles) {
        await this.prisma.role.upsert({
          where: { name: roleData.name },
          update: {
            description: roleData.description,
            permissions: roleData.permissions,
          },
          create: roleData,
        });
      }

      console.log(`✅ Created ${roles.length} roles`);

      // Create sample members with hashed PINs
      const members = [
        {
          name: 'Juan Pérez',
          email: '<EMAIL>',
          role: 'overseer_coordinator',
          pin: 'coordinator123',
        },
        {
          name: 'María González',
          email: '<EMAIL>',
          role: 'publisher',
          pin: 'maria123',
        },
        {
          name: 'Carlos Rodríguez',
          email: '<EMAIL>',
          role: 'ministerial_servant',
          pin: 'carlos123',
        },
        {
          name: 'Ana Martínez',
          email: '<EMAIL>',
          role: 'elder',
          pin: 'ana123',
        },
        {
          name: 'Pedro López',
          email: '<EMAIL>',
          role: 'elder',
          pin: 'pedro123',
        },
        {
          name: 'Developer Admin',
          email: '<EMAIL>',
          role: 'developer',
          pin: 'dev123',
        },
      ];

      console.log('\n👥 Creating members...');

      for (const memberData of members) {
        const hashedPin = await this.hashPin(memberData.pin);

        const member = await this.prisma.member.upsert({
          where: {
            id: `${congregation.id}_${memberData.name.replace(/\s+/g, '_').toLowerCase()}`
          },
          update: {
            congregationId: congregation.id,
            name: memberData.name,
            email: memberData.email,
            role: memberData.role,
            pin: hashedPin,
            isActive: true,
            preferences: {
              language: 'es',
              notifications: true,
            },
          },
          create: {
            id: `${congregation.id}_${memberData.name.replace(/\s+/g, '_').toLowerCase()}`,
            congregationId: congregation.id,
            name: memberData.name,
            email: memberData.email,
            role: memberData.role,
            pin: hashedPin,
            isActive: true,
            preferences: {
              language: 'es',
              notifications: true,
            },
          },
        });

        console.log(`  ✅ ${member.name} (${member.role}) - PIN: ${memberData.pin}`);
      }

      console.log('\n🎉 Authentication data setup completed successfully!');
      console.log('\n📝 Login Instructions:');
      console.log('1. Go to http://localhost:3000/login');
      console.log(`2. Enter Congregation ID: ${congregation.id}`);
      console.log(`3. Enter Congregation PIN: ${congregationPin}`);
      console.log('4. The system will automatically log you in as the first elder');

      return {
        congregation,
        memberCount: members.length,
        roleCount: roles.length,
      };

    } catch (error) {
      console.error('❌ Error setting up authentication data:', error);
      throw error;
    }
  }

  async validateSetup() {
    console.log('\n🔍 Validating authentication setup...');

    try {
      const congregationCount = await this.prisma.congregation.count({
        where: { isActive: true }
      });

      const memberCount = await this.prisma.member.count({
        where: { isActive: true }
      });

      const roleCount = await this.prisma.role.count({
        where: { isActive: true }
      });

      console.log(`✅ Active congregations: ${congregationCount}`);
      console.log(`✅ Active members: ${memberCount}`);
      console.log(`✅ Active roles: ${roleCount}`);

      // Test PIN hashing
      const testCongregation = await this.prisma.congregation.findFirst({
        where: { isActive: true }
      });

      if (testCongregation) {
        const pinTest = await bcrypt.compare('coralpin123', testCongregation.pin);
        console.log(`✅ PIN hashing test: ${pinTest ? 'PASS' : 'FAIL'}`);
      }

      return congregationCount > 0 && memberCount > 0 && roleCount > 0;

    } catch (error) {
      console.error('❌ Validation failed:', error);
      return false;
    }
  }

  async run() {
    try {
      await this.prisma.$connect();
      console.log('✅ Database connection established');

      const result = await this.setupCongregationData();
      const isValid = await this.validateSetup();

      if (isValid) {
        console.log('\n🎉 Authentication system is ready for testing!');
      } else {
        console.log('\n❌ Authentication setup validation failed!');
        process.exit(1);
      }

    } catch (error) {
      console.error('\n💥 Setup failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new AuthDataSetup();
  setup.run().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

module.exports = AuthDataSetup;
