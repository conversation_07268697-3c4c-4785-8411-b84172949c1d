-- CreateTable
CREATE TABLE "health_checks" (
    "id" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ok',
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "health_checks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "congregations" (
    "id" VARCHAR(8) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "region" VARCHAR(50),
    "pin" VARCHAR(255) NOT NULL,
    "language" VARCHAR(5) NOT NULL DEFAULT 'es',
    "timezone" VARCHAR(50) NOT NULL DEFAULT 'America/Mexico_City',
    "settings" JSONB NOT NULL DEFAULT '{}',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "congregations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "roles" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "permissions" JSONB NOT NULL DEFAULT '[]',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "members" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255),
    "role" VARCHAR(50) NOT NULL DEFAULT 'publisher',
    "pin" VARCHAR(255) NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "last_login" TIMESTAMPTZ,
    "preferences" JSONB NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "members_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "elder_permissions" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "section" VARCHAR(100) NOT NULL,
    "can_access" BOOLEAN NOT NULL DEFAULT false,
    "can_edit" BOOLEAN NOT NULL DEFAULT false,
    "can_delete" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "elder_permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "section_assignments" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "section_type" VARCHAR(100) NOT NULL,
    "scope_definition" JSONB NOT NULL DEFAULT '{}',
    "assigned_by" TEXT,
    "assigned_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "section_assignments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assignment_history" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "section_type" VARCHAR(100) NOT NULL,
    "action" VARCHAR(50) NOT NULL,
    "assigned_by" TEXT,
    "assigned_to" TEXT,
    "reason" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "assignment_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "midweek_meetings" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "meeting_date" DATE NOT NULL,
    "chairman" VARCHAR(255),
    "opening_prayer" VARCHAR(255),
    "closing_prayer" VARCHAR(255),
    "location" VARCHAR(100) NOT NULL DEFAULT 'Kingdom Hall',
    "zoom_link" TEXT,
    "notes" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "midweek_meetings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "midweek_meeting_parts" (
    "id" TEXT NOT NULL,
    "meeting_id" TEXT NOT NULL,
    "part_number" INTEGER NOT NULL,
    "part_type" VARCHAR(100) NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "assigned_member" VARCHAR(255),
    "assistant" VARCHAR(255),
    "time_allocation" INTEGER,
    "notes" TEXT,
    "is_completed" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "midweek_meeting_parts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "weekend_meetings" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "meeting_date" DATE NOT NULL,
    "public_talk_title" VARCHAR(255),
    "public_talk_speaker" VARCHAR(255),
    "speaker_congregation" VARCHAR(255),
    "watchtower_conductor" VARCHAR(255),
    "watchtower_reader" VARCHAR(255),
    "chairman" VARCHAR(255),
    "opening_prayer" VARCHAR(255),
    "closing_prayer" VARCHAR(255),
    "location" VARCHAR(100) NOT NULL DEFAULT 'Kingdom Hall',
    "zoom_link" TEXT,
    "notes" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "weekend_meetings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "weekend_meeting_parts" (
    "id" TEXT NOT NULL,
    "meeting_id" TEXT NOT NULL,
    "part_type" VARCHAR(100) NOT NULL,
    "assigned_member" VARCHAR(255),
    "notes" TEXT,
    "is_completed" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "weekend_meeting_parts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tasks" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "category" VARCHAR(100) NOT NULL,
    "frequency" VARCHAR(50) NOT NULL,
    "estimated_time" INTEGER,
    "instructions" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "tasks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "task_assignments" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "task_id" TEXT NOT NULL,
    "assigned_member_id" TEXT,
    "assigned_date" DATE NOT NULL,
    "due_date" DATE,
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "notes" TEXT,
    "completed_at" TIMESTAMPTZ,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "task_assignments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "field_service_records" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "service_month" DATE NOT NULL,
    "hours" DECIMAL(5,2),
    "placements" INTEGER DEFAULT 0,
    "video_showings" INTEGER DEFAULT 0,
    "return_visits" INTEGER DEFAULT 0,
    "bible_studies" INTEGER DEFAULT 0,
    "notes" TEXT,
    "is_submitted" BOOLEAN NOT NULL DEFAULT false,
    "submitted_at" TIMESTAMPTZ,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "field_service_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "letters" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "filename" VARCHAR(255) NOT NULL,
    "file_path" TEXT NOT NULL,
    "file_size" INTEGER,
    "mime_type" VARCHAR(100),
    "category" VARCHAR(100),
    "visibility" VARCHAR(50) NOT NULL DEFAULT 'ALL_MEMBERS',
    "upload_date" DATE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "uploaded_by_id" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "letters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "events" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "event_date" DATE NOT NULL,
    "start_time" VARCHAR(10),
    "end_time" VARCHAR(10),
    "location" VARCHAR(255),
    "category" VARCHAR(100) NOT NULL,
    "is_all_day" BOOLEAN NOT NULL DEFAULT false,
    "is_recurring" BOOLEAN NOT NULL DEFAULT false,
    "recurrence_rule" TEXT,
    "visibility" VARCHAR(50) NOT NULL DEFAULT 'ALL_MEMBERS',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "songs" (
    "id" TEXT NOT NULL,
    "song_number" INTEGER NOT NULL,
    "title_es" VARCHAR(255),
    "title_en" VARCHAR(255),
    "category" VARCHAR(100),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "songs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "roles_name_key" ON "roles"("name");

-- CreateIndex
CREATE INDEX "members_congregation_id_role_idx" ON "members"("congregation_id", "role");

-- CreateIndex
CREATE INDEX "members_congregation_id_is_active_idx" ON "members"("congregation_id", "is_active");

-- CreateIndex
CREATE INDEX "elder_permissions_congregation_id_section_idx" ON "elder_permissions"("congregation_id", "section");

-- CreateIndex
CREATE UNIQUE INDEX "elder_permissions_member_id_section_key" ON "elder_permissions"("member_id", "section");

-- CreateIndex
CREATE INDEX "section_assignments_congregation_id_section_type_idx" ON "section_assignments"("congregation_id", "section_type");

-- CreateIndex
CREATE INDEX "section_assignments_congregation_id_is_active_idx" ON "section_assignments"("congregation_id", "is_active");

-- CreateIndex
CREATE UNIQUE INDEX "section_assignments_member_id_section_type_key" ON "section_assignments"("member_id", "section_type");

-- CreateIndex
CREATE INDEX "assignment_history_congregation_id_member_id_idx" ON "assignment_history"("congregation_id", "member_id");

-- CreateIndex
CREATE INDEX "assignment_history_congregation_id_section_type_idx" ON "assignment_history"("congregation_id", "section_type");

-- CreateIndex
CREATE INDEX "assignment_history_created_at_idx" ON "assignment_history"("created_at");

-- CreateIndex
CREATE INDEX "midweek_meetings_congregation_id_meeting_date_idx" ON "midweek_meetings"("congregation_id", "meeting_date");

-- CreateIndex
CREATE INDEX "midweek_meeting_parts_meeting_id_part_number_idx" ON "midweek_meeting_parts"("meeting_id", "part_number");

-- CreateIndex
CREATE INDEX "weekend_meetings_congregation_id_meeting_date_idx" ON "weekend_meetings"("congregation_id", "meeting_date");

-- CreateIndex
CREATE INDEX "weekend_meeting_parts_meeting_id_part_type_idx" ON "weekend_meeting_parts"("meeting_id", "part_type");

-- CreateIndex
CREATE INDEX "tasks_congregation_id_category_idx" ON "tasks"("congregation_id", "category");

-- CreateIndex
CREATE INDEX "tasks_congregation_id_is_active_idx" ON "tasks"("congregation_id", "is_active");

-- CreateIndex
CREATE INDEX "task_assignments_congregation_id_assigned_date_idx" ON "task_assignments"("congregation_id", "assigned_date");

-- CreateIndex
CREATE INDEX "task_assignments_assigned_member_id_status_idx" ON "task_assignments"("assigned_member_id", "status");

-- CreateIndex
CREATE INDEX "field_service_records_congregation_id_service_month_idx" ON "field_service_records"("congregation_id", "service_month");

-- CreateIndex
CREATE UNIQUE INDEX "field_service_records_member_id_service_month_key" ON "field_service_records"("member_id", "service_month");

-- CreateIndex
CREATE INDEX "letters_congregation_id_visibility_idx" ON "letters"("congregation_id", "visibility");

-- CreateIndex
CREATE INDEX "letters_congregation_id_category_idx" ON "letters"("congregation_id", "category");

-- CreateIndex
CREATE INDEX "events_congregation_id_event_date_idx" ON "events"("congregation_id", "event_date");

-- CreateIndex
CREATE INDEX "events_congregation_id_category_idx" ON "events"("congregation_id", "category");

-- CreateIndex
CREATE UNIQUE INDEX "songs_song_number_key" ON "songs"("song_number");

-- AddForeignKey
ALTER TABLE "members" ADD CONSTRAINT "members_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "members" ADD CONSTRAINT "members_role_fkey" FOREIGN KEY ("role") REFERENCES "roles"("name") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "elder_permissions" ADD CONSTRAINT "elder_permissions_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "elder_permissions" ADD CONSTRAINT "elder_permissions_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "section_assignments" ADD CONSTRAINT "section_assignments_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "section_assignments" ADD CONSTRAINT "section_assignments_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "section_assignments" ADD CONSTRAINT "section_assignments_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assignment_history" ADD CONSTRAINT "assignment_history_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assignment_history" ADD CONSTRAINT "assignment_history_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assignment_history" ADD CONSTRAINT "assignment_history_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assignment_history" ADD CONSTRAINT "assignment_history_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "midweek_meetings" ADD CONSTRAINT "midweek_meetings_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "midweek_meeting_parts" ADD CONSTRAINT "midweek_meeting_parts_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "midweek_meetings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "weekend_meetings" ADD CONSTRAINT "weekend_meetings_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "weekend_meeting_parts" ADD CONSTRAINT "weekend_meeting_parts_meeting_id_fkey" FOREIGN KEY ("meeting_id") REFERENCES "weekend_meetings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_assignments" ADD CONSTRAINT "task_assignments_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_assignments" ADD CONSTRAINT "task_assignments_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_assignments" ADD CONSTRAINT "task_assignments_assigned_member_id_fkey" FOREIGN KEY ("assigned_member_id") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "field_service_records" ADD CONSTRAINT "field_service_records_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "field_service_records" ADD CONSTRAINT "field_service_records_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "letters" ADD CONSTRAINT "letters_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "letters" ADD CONSTRAINT "letters_uploaded_by_id_fkey" FOREIGN KEY ("uploaded_by_id") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "events" ADD CONSTRAINT "events_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
