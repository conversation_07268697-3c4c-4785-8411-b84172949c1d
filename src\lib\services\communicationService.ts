/**
 * Communication and Notification Service
 *
 * Comprehensive service for managing congregation communications, notifications, and announcements.
 * Handles multi-channel delivery (email, SMS, in-app), notification preferences, and communication tracking.
 */

import { prisma } from '@/lib/prisma';

// Communication interfaces
export interface NotificationData {
  id: string;
  congregationId: string;
  recipientId: string;
  senderId?: string;
  title: string;
  message: string;
  category: NotificationCategory;
  priority: NotificationPriority;
  deliveryMethod: DeliveryMethod[];
  status: NotificationStatus;
  scheduledFor?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  metadata?: Record<string, unknown>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateNotificationData {
  recipientId?: string; // If not provided, sends to all members
  recipientIds?: string[]; // For targeted group notifications
  senderId?: string;
  title: string;
  message: string;
  category: NotificationCategory;
  priority?: NotificationPriority;
  deliveryMethod?: DeliveryMethod[];
  scheduledFor?: Date;
  metadata?: Record<string, unknown>;
}

export interface UpdateNotificationData {
  title?: string;
  message?: string;
  category?: NotificationCategory;
  priority?: NotificationPriority;
  deliveryMethod?: DeliveryMethod[];
  scheduledFor?: Date;
  status?: NotificationStatus;
  metadata?: Record<string, unknown>;
  isActive?: boolean;
}

export interface NotificationFilters {
  category?: NotificationCategory;
  priority?: NotificationPriority;
  status?: NotificationStatus;
  recipientId?: string;
  senderId?: string;
  startDate?: Date;
  endDate?: Date;
  isRead?: boolean;
  search?: string;
}

export interface CommunicationPreferences {
  id: string;
  congregationId: string;
  memberId: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  inAppNotifications: boolean;
  quietHoursStart?: string;
  quietHoursEnd?: string;
  categoryPreferences: Record<NotificationCategory, boolean>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CommunicationTemplate {
  id: string;
  congregationId: string;
  name: string;
  title: string;
  message: string;
  category: NotificationCategory;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationSummary {
  totalNotifications: number;
  unreadNotifications: number;
  notificationsByCategory: Record<NotificationCategory, number>;
  notificationsByStatus: Record<NotificationStatus, number>;
  recentNotifications: NotificationData[];
}

// Notification enums
export enum NotificationCategory {
  URGENT = 'URGENT',
  GENERAL = 'GENERAL',
  EVENTS = 'EVENTS',
  ASSIGNMENTS = 'ASSIGNMENTS',
  TASKS = 'TASKS',
  MEETINGS = 'MEETINGS',
  SERVICE = 'SERVICE',
  ANNOUNCEMENTS = 'ANNOUNCEMENTS'
}

export enum NotificationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum NotificationStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENDING = 'SENDING',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
  FAILED = 'FAILED'
}

export enum DeliveryMethod {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  IN_APP = 'IN_APP',
  PUSH = 'PUSH'
}

// Category labels in Spanish
export const NOTIFICATION_CATEGORY_LABELS: Record<NotificationCategory, string> = {
  [NotificationCategory.URGENT]: 'Urgente',
  [NotificationCategory.GENERAL]: 'General',
  [NotificationCategory.EVENTS]: 'Eventos',
  [NotificationCategory.ASSIGNMENTS]: 'Asignaciones',
  [NotificationCategory.TASKS]: 'Tareas',
  [NotificationCategory.MEETINGS]: 'Reuniones',
  [NotificationCategory.SERVICE]: 'Servicio',
  [NotificationCategory.ANNOUNCEMENTS]: 'Anuncios'
};

export const NOTIFICATION_PRIORITY_LABELS: Record<NotificationPriority, string> = {
  [NotificationPriority.LOW]: 'Baja',
  [NotificationPriority.NORMAL]: 'Normal',
  [NotificationPriority.HIGH]: 'Alta',
  [NotificationPriority.URGENT]: 'Urgente'
};

export const NOTIFICATION_STATUS_LABELS: Record<NotificationStatus, string> = {
  [NotificationStatus.DRAFT]: 'Borrador',
  [NotificationStatus.SCHEDULED]: 'Programado',
  [NotificationStatus.SENDING]: 'Enviando',
  [NotificationStatus.DELIVERED]: 'Entregado',
  [NotificationStatus.READ]: 'Leído',
  [NotificationStatus.FAILED]: 'Fallido'
};

export const DELIVERY_METHOD_LABELS: Record<DeliveryMethod, string> = {
  [DeliveryMethod.EMAIL]: 'Correo Electrónico',
  [DeliveryMethod.SMS]: 'SMS',
  [DeliveryMethod.IN_APP]: 'En la Aplicación',
  [DeliveryMethod.PUSH]: 'Notificación Push'
};

export class CommunicationService {
  /**
   * Create a new notification
   */
  static async createNotification(
    congregationId: string,
    notificationData: CreateNotificationData
  ): Promise<NotificationData[]> {
    try {
      // Validate notification data
      this.validateNotificationData(notificationData);

      // Determine recipients
      let recipientIds: string[] = [];

      if (notificationData.recipientId) {
        recipientIds = [notificationData.recipientId];
      } else if (notificationData.recipientIds) {
        recipientIds = notificationData.recipientIds;
      } else {
        // Send to all active members
        const members = await prisma.member.findMany({
          where: {
            congregationId,
            isActive: true,
          },
          select: { id: true },
        });
        recipientIds = members.map(m => m.id);
      }

      // Create notifications for each recipient
      const notifications = await Promise.all(
        recipientIds.map(async (recipientId) => {
          // Get recipient's communication preferences
          const preferences = await this.getCommunicationPreferences(congregationId, recipientId);

          // Determine delivery methods based on preferences and priority
          const deliveryMethods = this.determineDeliveryMethods(
            notificationData.deliveryMethod || [DeliveryMethod.IN_APP],
            preferences,
            notificationData.priority || NotificationPriority.NORMAL
          );

          const notification = await prisma.notification.create({
            data: {
              congregationId,
              recipientId,
              senderId: notificationData.senderId,
              title: notificationData.title,
              message: notificationData.message,
              category: notificationData.category,
              priority: notificationData.priority || NotificationPriority.NORMAL,
              deliveryMethod: deliveryMethods,
              status: notificationData.scheduledFor ? NotificationStatus.SCHEDULED : NotificationStatus.DELIVERED,
              scheduledFor: notificationData.scheduledFor,
              metadata: notificationData.metadata || {},
              deliveredAt: notificationData.scheduledFor ? null : new Date(),
            },
          });

          return this.mapNotificationToNotificationData(notification);
        })
      );

      // If not scheduled, trigger immediate delivery
      if (!notificationData.scheduledFor) {
        await this.processNotificationDelivery(notifications);
      }

      return notifications;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw new Error('Failed to create notification');
    }
  }

  /**
   * Get notifications for a user
   */
  static async getNotifications(
    congregationId: string,
    recipientId: string,
    filters: NotificationFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<NotificationData[]> {
    try {
      const where: {
        congregationId: string;
        recipientId: string;
        isActive: boolean;
        category?: NotificationCategory;
        priority?: NotificationPriority;
        status?: NotificationStatus;
        senderId?: string;
        createdAt?: { gte?: Date; lte?: Date };
        readAt?: { not?: null } | null;
        OR?: Array<{
          title?: { contains: string; mode: 'insensitive' };
          message?: { contains: string; mode: 'insensitive' };
        }>;
      } = {
        congregationId,
        recipientId,
        isActive: true,
      };

      // Apply filters
      if (filters.category) {
        where.category = filters.category;
      }

      if (filters.priority) {
        where.priority = filters.priority;
      }

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.senderId) {
        where.senderId = filters.senderId;
      }

      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
          where.createdAt.gte = filters.startDate;
        }
        if (filters.endDate) {
          where.createdAt.lte = filters.endDate;
        }
      }

      if (filters.isRead !== undefined) {
        where.readAt = filters.isRead ? { not: null } : null;
      }

      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { message: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      const notifications = await prisma.notification.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      });

      return notifications.map(this.mapNotificationToNotificationData);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw new Error('Failed to fetch notifications');
    }
  }

  /**
   * Mark notification as read
   */
  static async markNotificationAsRead(
    congregationId: string,
    notificationId: string,
    recipientId: string
  ): Promise<NotificationData> {
    try {
      const notification = await prisma.notification.update({
        where: {
          id: notificationId,
          congregationId,
          recipientId,
        },
        data: {
          status: NotificationStatus.READ,
          readAt: new Date(),
        },
      });

      return this.mapNotificationToNotificationData(notification);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw new Error('Failed to mark notification as read');
    }
  }

  /**
   * Get notification summary for dashboard
   */
  static async getNotificationSummary(
    congregationId: string,
    recipientId: string
  ): Promise<NotificationSummary> {
    try {
      // Get total notifications
      const totalNotifications = await prisma.notification.count({
        where: {
          congregationId,
          recipientId,
          isActive: true,
        },
      });

      // Get unread notifications count
      const unreadNotifications = await prisma.notification.count({
        where: {
          congregationId,
          recipientId,
          isActive: true,
          readAt: null,
        },
      });

      // Get notifications by category
      const notificationsByCategory: Record<NotificationCategory, number> = {
        [NotificationCategory.URGENT]: 0,
        [NotificationCategory.GENERAL]: 0,
        [NotificationCategory.EVENTS]: 0,
        [NotificationCategory.ASSIGNMENTS]: 0,
        [NotificationCategory.TASKS]: 0,
        [NotificationCategory.MEETINGS]: 0,
        [NotificationCategory.SERVICE]: 0,
        [NotificationCategory.ANNOUNCEMENTS]: 0,
      };

      const categoryStats = await prisma.notification.groupBy({
        by: ['category'],
        where: {
          congregationId,
          recipientId,
          isActive: true,
        },
        _count: {
          category: true,
        },
      });

      categoryStats.forEach(stat => {
        if (stat.category in notificationsByCategory) {
          notificationsByCategory[stat.category as NotificationCategory] = stat._count.category;
        }
      });

      // Get notifications by status
      const notificationsByStatus: Record<NotificationStatus, number> = {
        [NotificationStatus.DRAFT]: 0,
        [NotificationStatus.SCHEDULED]: 0,
        [NotificationStatus.SENDING]: 0,
        [NotificationStatus.DELIVERED]: 0,
        [NotificationStatus.READ]: 0,
        [NotificationStatus.FAILED]: 0,
      };

      const statusStats = await prisma.notification.groupBy({
        by: ['status'],
        where: {
          congregationId,
          recipientId,
          isActive: true,
        },
        _count: {
          status: true,
        },
      });

      statusStats.forEach(stat => {
        if (stat.status in notificationsByStatus) {
          notificationsByStatus[stat.status as NotificationStatus] = stat._count.status;
        }
      });

      // Get recent notifications
      const recentNotificationRecords = await prisma.notification.findMany({
        where: {
          congregationId,
          recipientId,
          isActive: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
      });

      const recentNotifications = recentNotificationRecords.map(this.mapNotificationToNotificationData);

      return {
        totalNotifications,
        unreadNotifications,
        notificationsByCategory,
        notificationsByStatus,
        recentNotifications,
      };
    } catch (error) {
      console.error('Error getting notification summary:', error);
      throw new Error('Failed to get notification summary');
    }
  }

  /**
   * Get communication preferences for a member
   */
  static async getCommunicationPreferences(
    congregationId: string,
    memberId: string
  ): Promise<CommunicationPreferences> {
    try {
      let preferences = await prisma.communicationPreference.findFirst({
        where: {
          congregationId,
          memberId,
        },
      });

      // Create default preferences if none exist
      if (!preferences) {
        preferences = await prisma.communicationPreference.create({
          data: {
            congregationId,
            memberId,
            emailNotifications: true,
            smsNotifications: false,
            inAppNotifications: true,
            categoryPreferences: {
              [NotificationCategory.URGENT]: true,
              [NotificationCategory.GENERAL]: true,
              [NotificationCategory.EVENTS]: true,
              [NotificationCategory.ASSIGNMENTS]: true,
              [NotificationCategory.TASKS]: true,
              [NotificationCategory.MEETINGS]: true,
              [NotificationCategory.SERVICE]: true,
              [NotificationCategory.ANNOUNCEMENTS]: true,
            },
          },
        });
      }

      return this.mapPreferencesToCommunicationPreferences(preferences);
    } catch (error) {
      console.error('Error getting communication preferences:', error);
      throw new Error('Failed to get communication preferences');
    }
  }

  /**
   * Update communication preferences for a member
   */
  static async updateCommunicationPreferences(
    congregationId: string,
    memberId: string,
    preferences: Partial<CommunicationPreferences>
  ): Promise<CommunicationPreferences> {
    try {
      const updatedPreferences = await prisma.communicationPreference.upsert({
        where: {
          congregationId_memberId: {
            congregationId,
            memberId,
          },
        },
        update: {
          emailNotifications: preferences.emailNotifications,
          smsNotifications: preferences.smsNotifications,
          inAppNotifications: preferences.inAppNotifications,
          quietHoursStart: preferences.quietHoursStart,
          quietHoursEnd: preferences.quietHoursEnd,
          categoryPreferences: preferences.categoryPreferences,
        },
        create: {
          congregationId,
          memberId,
          emailNotifications: preferences.emailNotifications ?? true,
          smsNotifications: preferences.smsNotifications ?? false,
          inAppNotifications: preferences.inAppNotifications ?? true,
          quietHoursStart: preferences.quietHoursStart,
          quietHoursEnd: preferences.quietHoursEnd,
          categoryPreferences: preferences.categoryPreferences ?? {
            [NotificationCategory.URGENT]: true,
            [NotificationCategory.GENERAL]: true,
            [NotificationCategory.EVENTS]: true,
            [NotificationCategory.ASSIGNMENTS]: true,
            [NotificationCategory.TASKS]: true,
            [NotificationCategory.MEETINGS]: true,
            [NotificationCategory.SERVICE]: true,
            [NotificationCategory.ANNOUNCEMENTS]: true,
          },
        },
      });

      return this.mapPreferencesToCommunicationPreferences(updatedPreferences);
    } catch (error) {
      console.error('Error updating communication preferences:', error);
      throw new Error('Failed to update communication preferences');
    }
  }

  /**
   * Create communication template
   */
  static async createCommunicationTemplate(
    congregationId: string,
    templateData: {
      name: string;
      title: string;
      message: string;
      category: NotificationCategory;
      variables?: string[];
    }
  ): Promise<CommunicationTemplate> {
    try {
      const template = await prisma.communicationTemplate.create({
        data: {
          congregationId,
          name: templateData.name,
          title: templateData.title,
          message: templateData.message,
          category: templateData.category,
          variables: templateData.variables || [],
        },
      });

      return this.mapTemplateToCommunicationTemplate(template);
    } catch (error) {
      console.error('Error creating communication template:', error);
      throw new Error('Failed to create communication template');
    }
  }

  /**
   * Get communication templates
   */
  static async getCommunicationTemplates(
    congregationId: string,
    category?: NotificationCategory
  ): Promise<CommunicationTemplate[]> {
    try {
      const where: {
        congregationId: string;
        isActive: boolean;
        category?: NotificationCategory;
      } = {
        congregationId,
        isActive: true,
      };

      if (category) {
        where.category = category;
      }

      const templates = await prisma.communicationTemplate.findMany({
        where,
        orderBy: { name: 'asc' },
      });

      return templates.map(this.mapTemplateToCommunicationTemplate);
    } catch (error) {
      console.error('Error getting communication templates:', error);
      throw new Error('Failed to get communication templates');
    }
  }

  /**
   * Send automatic notification for events
   */
  static async sendEventNotification(
    congregationId: string,
    eventId: string,
    type: 'created' | 'updated' | 'cancelled' | 'reminder'
  ): Promise<void> {
    try {
      // Get event details
      const event = await prisma.event.findFirst({
        where: { id: eventId, congregationId },
      });

      if (!event) {
        throw new Error('Event not found');
      }

      let title: string;
      let message: string;
      let priority: NotificationPriority;

      switch (type) {
        case 'created':
          title = 'Nuevo Evento Programado';
          message = `Se ha programado un nuevo evento: ${event.title} para el ${event.eventDate.toLocaleDateString('es-ES')}.`;
          priority = NotificationPriority.NORMAL;
          break;
        case 'updated':
          title = 'Evento Actualizado';
          message = `El evento "${event.title}" ha sido actualizado. Revisa los detalles.`;
          priority = NotificationPriority.NORMAL;
          break;
        case 'cancelled':
          title = 'Evento Cancelado';
          message = `El evento "${event.title}" programado para el ${event.eventDate.toLocaleDateString('es-ES')} ha sido cancelado.`;
          priority = NotificationPriority.HIGH;
          break;
        case 'reminder':
          title = 'Recordatorio de Evento';
          message = `Recordatorio: El evento "${event.title}" es mañana, ${event.eventDate.toLocaleDateString('es-ES')}.`;
          priority = NotificationPriority.NORMAL;
          break;
        default:
          throw new Error('Invalid notification type');
      }

      await this.createNotification(congregationId, {
        title,
        message,
        category: NotificationCategory.EVENTS,
        priority,
        deliveryMethod: [DeliveryMethod.IN_APP, DeliveryMethod.EMAIL],
        metadata: {
          eventId,
          eventTitle: event.title,
          eventDate: event.eventDate,
          notificationType: type,
        },
      });
    } catch (error) {
      console.error('Error sending event notification:', error);
      throw new Error('Failed to send event notification');
    }
  }

  /**
   * Send automatic notification for task assignments
   */
  static async sendTaskNotification(
    congregationId: string,
    taskAssignmentId: string,
    type: 'assigned' | 'due_soon' | 'overdue'
  ): Promise<void> {
    try {
      // Get task assignment details
      const taskAssignment = await prisma.taskAssignment.findFirst({
        where: { id: taskAssignmentId, congregationId },
        include: {
          task: true,
          assignedMember: true,
        },
      });

      if (!taskAssignment || !taskAssignment.assignedMemberId) {
        return;
      }

      let title: string;
      let message: string;
      let priority: NotificationPriority;

      switch (type) {
        case 'assigned':
          title = 'Nueva Tarea Asignada';
          message = `Se te ha asignado la tarea: ${taskAssignment.task.title}. Fecha límite: ${taskAssignment.dueDate?.toLocaleDateString('es-ES') || 'No especificada'}.`;
          priority = NotificationPriority.NORMAL;
          break;
        case 'due_soon':
          title = 'Tarea Próxima a Vencer';
          message = `La tarea "${taskAssignment.task.title}" vence pronto. Fecha límite: ${taskAssignment.dueDate?.toLocaleDateString('es-ES')}.`;
          priority = NotificationPriority.HIGH;
          break;
        case 'overdue':
          title = 'Tarea Vencida';
          message = `La tarea "${taskAssignment.task.title}" está vencida. Fecha límite: ${taskAssignment.dueDate?.toLocaleDateString('es-ES')}.`;
          priority = NotificationPriority.URGENT;
          break;
        default:
          throw new Error('Invalid notification type');
      }

      await this.createNotification(congregationId, {
        recipientId: taskAssignment.assignedMemberId,
        title,
        message,
        category: NotificationCategory.TASKS,
        priority,
        deliveryMethod: [DeliveryMethod.IN_APP, DeliveryMethod.EMAIL],
        metadata: {
          taskAssignmentId,
          taskId: taskAssignment.taskId,
          taskTitle: taskAssignment.task.title,
          dueDate: taskAssignment.dueDate,
          notificationType: type,
        },
      });
    } catch (error) {
      console.error('Error sending task notification:', error);
      throw new Error('Failed to send task notification');
    }
  }

  /**
   * Validate notification data
   */
  private static validateNotificationData(notificationData: CreateNotificationData): void {
    if (!notificationData.title || notificationData.title.trim().length === 0) {
      throw new Error('Notification title is required');
    }

    if (!notificationData.message || notificationData.message.trim().length === 0) {
      throw new Error('Notification message is required');
    }

    if (notificationData.scheduledFor && notificationData.scheduledFor < new Date()) {
      throw new Error('Scheduled time cannot be in the past');
    }
  }

  /**
   * Determine delivery methods based on preferences and priority
   */
  private static determineDeliveryMethods(
    requestedMethods: DeliveryMethod[],
    preferences: CommunicationPreferences,
    priority: NotificationPriority
  ): DeliveryMethod[] {
    const deliveryMethods: DeliveryMethod[] = [];

    // Always include in-app notifications
    if (preferences.inAppNotifications) {
      deliveryMethods.push(DeliveryMethod.IN_APP);
    }

    // Add email if enabled and requested
    if (preferences.emailNotifications && requestedMethods.includes(DeliveryMethod.EMAIL)) {
      deliveryMethods.push(DeliveryMethod.EMAIL);
    }

    // Add SMS if enabled and requested
    if (preferences.smsNotifications && requestedMethods.includes(DeliveryMethod.SMS)) {
      deliveryMethods.push(DeliveryMethod.SMS);
    }

    // For urgent notifications, override preferences
    if (priority === NotificationPriority.URGENT) {
      if (!deliveryMethods.includes(DeliveryMethod.EMAIL) && requestedMethods.includes(DeliveryMethod.EMAIL)) {
        deliveryMethods.push(DeliveryMethod.EMAIL);
      }
      if (!deliveryMethods.includes(DeliveryMethod.SMS) && requestedMethods.includes(DeliveryMethod.SMS)) {
        deliveryMethods.push(DeliveryMethod.SMS);
      }
    }

    return deliveryMethods.length > 0 ? deliveryMethods : [DeliveryMethod.IN_APP];
  }

  /**
   * Process notification delivery (placeholder for actual delivery implementation)
   */
  private static async processNotificationDelivery(notifications: NotificationData[]): Promise<void> {
    // This would integrate with actual email/SMS services
    // For now, we'll just log the delivery
    for (const notification of notifications) {
      console.log(`Processing delivery for notification ${notification.id} via ${notification.deliveryMethod.join(', ')}`);

      // Update delivery status
      await prisma.notification.update({
        where: { id: notification.id },
        data: {
          status: NotificationStatus.DELIVERED,
          deliveredAt: new Date(),
        },
      });
    }
  }

  /**
   * Map Prisma Notification to NotificationData
   */
  private static mapNotificationToNotificationData(notification: {
    id: string;
    congregationId: string;
    recipientId: string;
    senderId?: string | null;
    title: string;
    message: string;
    category: string;
    priority: string;
    deliveryMethod: string[];
    status: string;
    scheduledFor?: Date | null;
    deliveredAt?: Date | null;
    readAt?: Date | null;
    metadata?: unknown;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
  }): NotificationData {
    return {
      id: notification.id,
      congregationId: notification.congregationId,
      recipientId: notification.recipientId,
      senderId: notification.senderId || undefined,
      title: notification.title,
      message: notification.message,
      category: notification.category as NotificationCategory,
      priority: notification.priority as NotificationPriority,
      deliveryMethod: notification.deliveryMethod as DeliveryMethod[],
      status: notification.status as NotificationStatus,
      scheduledFor: notification.scheduledFor || undefined,
      deliveredAt: notification.deliveredAt || undefined,
      readAt: notification.readAt || undefined,
      metadata: notification.metadata || {},
      isActive: notification.isActive,
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt,
    };
  }

  /**
   * Map Prisma CommunicationPreference to CommunicationPreferences
   */
  private static mapPreferencesToCommunicationPreferences(preferences: {
    id: string;
    congregationId: string;
    memberId: string;
    emailNotifications: boolean;
    smsNotifications: boolean;
    inAppNotifications: boolean;
    quietHoursStart?: string | null;
    quietHoursEnd?: string | null;
    categoryPreferences?: unknown;
    createdAt: Date;
    updatedAt: Date;
  }): CommunicationPreferences {
    return {
      id: preferences.id,
      congregationId: preferences.congregationId,
      memberId: preferences.memberId,
      emailNotifications: preferences.emailNotifications,
      smsNotifications: preferences.smsNotifications,
      inAppNotifications: preferences.inAppNotifications,
      quietHoursStart: preferences.quietHoursStart || undefined,
      quietHoursEnd: preferences.quietHoursEnd || undefined,
      categoryPreferences: preferences.categoryPreferences || {},
      createdAt: preferences.createdAt,
      updatedAt: preferences.updatedAt,
    };
  }

  /**
   * Map Prisma CommunicationTemplate to CommunicationTemplate
   */
  private static mapTemplateToCommunicationTemplate(template: {
    id: string;
    congregationId: string;
    name: string;
    title: string;
    message: string;
    category: string;
    variables?: string[] | null;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
  }): CommunicationTemplate {
    return {
      id: template.id,
      congregationId: template.congregationId,
      name: template.name,
      title: template.title,
      message: template.message,
      category: template.category as NotificationCategory,
      variables: template.variables || [],
      isActive: template.isActive,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    };
  }

  /**
   * Format notification date for display
   */
  static formatNotificationDate(date: Date): string {
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  /**
   * Get notification category color
   */
  static getNotificationCategoryColor(category: NotificationCategory): string {
    const colors: Record<NotificationCategory, string> = {
      [NotificationCategory.URGENT]: 'red',
      [NotificationCategory.GENERAL]: 'blue',
      [NotificationCategory.EVENTS]: 'purple',
      [NotificationCategory.ASSIGNMENTS]: 'green',
      [NotificationCategory.TASKS]: 'orange',
      [NotificationCategory.MEETINGS]: 'indigo',
      [NotificationCategory.SERVICE]: 'teal',
      [NotificationCategory.ANNOUNCEMENTS]: 'pink',
    };
    return colors[category] || 'gray';
  }

  /**
   * Get notification priority color
   */
  static getNotificationPriorityColor(priority: NotificationPriority): string {
    const colors: Record<NotificationPriority, string> = {
      [NotificationPriority.LOW]: 'gray',
      [NotificationPriority.NORMAL]: 'blue',
      [NotificationPriority.HIGH]: 'orange',
      [NotificationPriority.URGENT]: 'red',
    };
    return colors[priority] || 'gray';
  }
}
