/**
 * Dashboard Layout Component
 *
 * Main layout component for the dashboard with header, navigation,
 * and content area. Preserves exact UI design with Spanish-first interface.
 */

import React, { useState, useRef, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface Congregation {
  id: string;
  name: string;
  language: string;
  timezone: string;
}

interface DashboardLayoutProps {
  user: User;
  congregation: Congregation;
  children: React.ReactNode;
  onLogout: () => void;
}

const getRoleDisplayName = (role: string): string => {
  const roleNames = {
    'publisher': 'Publicador',
    'ministerial_servant': 'Siervo Ministerial',
    'elder': '<PERSON><PERSON><PERSON>',
    'coordinator': 'Coordinador',
    'overseer_coordinator': 'Superintendente/Coordinador',
    'developer': 'Desarrollador',
  };
  return roleNames[role as keyof typeof roleNames] || role;
};

export default function DashboardLayout({
  user,
  congregation,
  children,
  onLogout,
}: DashboardLayoutProps) {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const profileMenuRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {
        setIsProfileMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    setIsProfileMenuOpen(false);
    onLogout();
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile-first container - exact match to reference design */}
      <div className="max-w-lg mx-auto min-h-screen bg-gray-100 relative">
        {/* Header - Exact match to reference design */}
        <header className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="px-4 py-4">
            <div className="flex justify-between items-center">
              {/* Left side - App title and congregation */}
              <div>
                <h1 className="text-xl font-bold">
                  Salón Del Reino - {congregation.name}
                </h1>
              </div>

              {/* Right side - User Profile Menu */}
              <div className="relative" ref={profileMenuRef}>
                <button
                  onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                  className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-colors"
                >
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                </button>

                {/* Profile Dropdown Menu */}
                {isProfileMenuOpen && (
                  <div className="absolute right-0 top-12 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                    {/* User Info Section */}
                    <div className="px-4 py-3 border-b border-gray-100">
                      {user.hasCongregationPinAccess ? (
                        // Congregation PIN Access - Show only congregation info
                        <>
                          <p className="text-sm font-medium text-gray-900">{congregation.name}</p>
                          <p className="text-xs text-blue-600">Acceso con PIN de Congregación</p>
                        </>
                      ) : (
                        // Member-specific access - Show member info
                        <>
                          <p className="text-sm font-medium text-gray-900">{user.name}</p>
                          <p className="text-xs text-gray-500">{getRoleDisplayName(user.role)}</p>
                          <p className="text-xs text-gray-500">{congregation.name}</p>
                        </>
                      )}
                    </div>

                    {/* Menu Items */}
                    <div className="py-1">
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        <span>Cerrar Sesión</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Ocean Background Section - Exact match to reference */}
        <div className="relative h-32 overflow-hidden">
          {/* Ocean image matching reference design */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1505228395891-9a51e7e86bf6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3433&q=80')`
            }}
          />
          {/* Blue overlay matching reference */}
          <div className="absolute inset-0 bg-gradient-to-b from-blue-400/30 to-blue-600/40"></div>
          {/* Smooth white transition at bottom */}
          <div className="absolute bottom-0 w-full h-6 bg-gradient-to-t from-gray-100 via-gray-100/80 to-transparent"></div>
        </div>

        {/* Main Content */}
        <main className="relative -mt-6 z-10 pb-20">
          {children}
        </main>

        {/* Bottom Navigation - Exact match to reference design */}
        <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-1 py-1.5 shadow-lg z-50">
          <div className="max-w-lg mx-auto">
            <div className="flex justify-around items-center">
              <button className="flex flex-col items-center py-1.5 px-2 text-blue-600">
                <svg className="w-4 h-4 mb-0.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <span className="text-xs font-medium">Inicio</span>
              </button>
              <button className="flex flex-col items-center py-1.5 px-2 text-gray-400">
                <svg className="w-4 h-4 mb-0.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z"/>
                </svg>
                <span className="text-xs">Servicio</span>
              </button>
              <button className="flex flex-col items-center py-1.5 px-2 text-gray-400">
                <svg className="w-4 h-4 mb-0.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2 1l-3 4v2h3v7h6z"/>
                </svg>
                <span className="text-xs">Entre Semana</span>
              </button>
              <button className="flex flex-col items-center py-1.5 px-2 text-gray-400">
                <svg className="w-4 h-4 mb-0.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"/>
                </svg>
                <span className="text-xs">Fin Semana</span>
              </button>
              <button className="flex flex-col items-center py-1.5 px-2 text-gray-400">
                <svg className="w-4 h-4 mb-0.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                </svg>
                <span className="text-xs">Asignaciones</span>
              </button>
            </div>
          </div>
        </nav>
      </div>
    </div>
  );
}
