import { NextRequest, NextResponse } from 'next/server';
import { EnhancedDocumentService } from '@/lib/services/enhancedDocumentService';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { unlink } from 'fs/promises';
import { join } from 'path';

/**
 * GET /api/documents/[id]
 * Get a specific document by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    const { id } = params;

    // Get document
    const document = await EnhancedDocumentService.getDocumentById(
      id,
      user.congregationId,
      user.role
    );

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    return NextResponse.json({ document });
  } catch (error) {
    console.error('Error fetching document:', error);
    return NextResponse.json(
      { error: 'Failed to fetch document' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/documents/[id]
 * Update a specific document
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Check if user has permission to update documents
    if (!['elder', 'ministerial_servant', 'coordinator'].includes(user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = params;
    const updateData = await request.json();

    // Update document
    const document = await EnhancedDocumentService.updateDocument(
      id,
      updateData,
      user.congregationId,
      user.userId
    );

    return NextResponse.json({ document });
  } catch (error) {
    console.error('Error updating document:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update document' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/documents/[id]
 * Delete a specific document
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify token
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Check if user has permission to delete documents (only elders and coordinators)
    if (!['elder', 'coordinator'].includes(user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = params;

    // Get document first to check ownership and get file path
    const document = await EnhancedDocumentService.getDocumentById(
      id,
      user.congregationId,
      user.role
    );

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Delete the physical file
    try {
      if (document.filePath && document.filePath.startsWith('/uploads/')) {
        const filePath = join(process.cwd(), 'public', document.filePath);
        await unlink(filePath);
      }
    } catch (fileError) {
      console.warn('Failed to delete physical file:', fileError);
      // Continue with database deletion even if file deletion fails
    }

    // Delete document from database
    await EnhancedDocumentService.deleteDocument(id, user.congregationId);

    return NextResponse.json({
      message: 'Document deleted successfully',
      id
    });
  } catch (error) {
    console.error('Error deleting document:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete document' },
      { status: 500 }
    );
  }
}
