# Story 6.5: Congregation Language Settings & Multilingual Support

**Epic:** Epic 6: Enhanced Meeting Management & JW.org Integration
**Story Points:** 10
**Priority:** High
**Status:** In Progress

## Story

As a congregation administrator and member,
I want comprehensive multilingual support with congregation-level language settings,
so that I can configure the application language for my congregation and all members can use the application in their preferred language (Spanish/English).

## Acceptance Criteria

1. **Language Settings Management in Congregation Settings**
   - Language selection dropdown in congregation settings with Spanish and English options
   - Default language setting for "Coral Oeste" congregation set to Spanish
   - Language preference storage in congregation_settings table with proper persistence
   - Language setting validation and proper fallback to Spanish if not specified
   - Admin interface for changing congregation language with immediate effect across the application

2. **Comprehensive Multilingual Support for Admin Areas**
   - Complete Spanish translation for all administrative interfaces and components
   - Dynamic language switching that affects all admin pages, forms, and messages
   - Consistent terminology and professional Spanish translations throughout
   - Language-specific date, time, and number formatting
   - Error messages and validation feedback in selected language
   - Admin dashboard, member management, songs, letters, events, and settings all translated

3. **Comprehensive Multilingual Support for Member Areas**
   - Complete Spanish translation for all member-facing interfaces and dashboards
   - Dynamic language switching that affects member dashboard, navigation, and content
   - Consistent user experience with language preference applied across all sections
   - Language-specific content formatting and cultural considerations
   - Member dashboard, field service, meetings, assignments, tasks, letters, and events all translated

4. **Language Persistence and User Experience**
   - Language preference persistence across browser sessions and page refreshes
   - Immediate language switching without page reload where possible
   - Consistent language application across all application components
   - Language preference inheritance from congregation settings for new users
   - Proper language fallback mechanisms for missing translations

5. **JW.org Integration Language Support**
   - Language-aware JW.org data fetching (Spanish vs English content)
   - Meeting workbook data fetched in congregation's selected language
   - Song titles and meeting content displayed in appropriate language
   - Fallback mechanisms for content not available in selected language

6. **Translation Infrastructure and Management**
   - Organized translation key structure with namespaced categories
   - Translation management system for easy updates and additions
   - Professional Spanish translations with proper terminology
   - English translations as fallback and alternative option
   - Translation validation and completeness checking

## Dev Notes

### Technical Architecture

**Language Management:**
- Congregation-level language settings stored in congregation_settings table
- Language preference cascading: congregation default → user preference → Spanish fallback
- Real-time language switching with context preservation
- Translation key management with organized namespace structure

**Translation Infrastructure:**
- React Context for language state management
- Custom hook for translation access in components
- Translation files organized by feature/section
- Dynamic translation loading and caching

### Database Schema Enhancement

```sql
-- Add language field to congregation_settings table
ALTER TABLE congregation_settings
ADD COLUMN language VARCHAR(2) DEFAULT 'es' CHECK (language IN ('es', 'en'));

-- Set default language for Coral Oeste
UPDATE congregation_settings
SET language = 'es'
WHERE congregation_id = 1441;
```

### API Endpoints

```typescript
// Language and settings management
GET /api/admin/settings/congregation - Fetch congregation settings including language
PUT /api/admin/settings/congregation - Update congregation settings including language
GET /api/i18n/translations/:language - Fetch translation resources for specified language

// Enhanced settings structure
interface CongregationSettings {
  id: string;
  name: string;
  number: string;
  pin: string;
  language: 'es' | 'en'; // New language field
  circuitNumber: string;
  circuitOverseer: string;
  address: string;
  midweekDay: string;
  midweekTime: string;
  weekendDay: string;
  weekendTime: string;
}
```

### Translation Structure

```typescript
// Spanish translations (es.json)
{
  "common": {
    "save": "Guardar",
    "cancel": "Cancelar",
    "edit": "Editar",
    "delete": "Eliminar",
    "search": "Buscar",
    "loading": "Cargando...",
    "error": "Error",
    "success": "Éxito"
  },
  "admin": {
    "dashboard": "Panel de Administración",
    "members": {
      "title": "Gestión de Miembros",
      "add_member": "Agregar Miembro",
      "edit_member": "Editar Miembro",
      "member_name": "Nombre del Miembro",
      "member_role": "Función"
    },
    "songs": {
      "title": "Administración de Canciones",
      "song_number": "Número",
      "song_title": "Título",
      "sync_songs": "Sincronizar Canciones"
    },
    "letters": {
      "title": "Gestión de Cartas",
      "upload_letter": "Subir Nueva Carta",
      "letter_title": "Título de la Carta"
    },
    "settings": {
      "title": "Configuración de la Congregación",
      "language": "Idioma de la Congregación",
      "language_spanish": "Español",
      "language_english": "Inglés"
    }
  },
  "member": {
    "dashboard": "Panel de Miembros",
    "field_service": "Servicio del Campo",
    "meetings": "Reuniones",
    "assignments": "Asignaciones",
    "tasks": "Tareas",
    "letters": "Cartas",
    "events": "Eventos"
  }
}
```

### React Context Implementation

```typescript
// Language Context
interface LanguageContextType {
  language: 'es' | 'en';
  setLanguage: (lang: 'es' | 'en') => void;
  t: (key: string, params?: Record<string, string>) => string;
  isLoading: boolean;
}

// Translation Hook
const useTranslation = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useTranslation must be used within LanguageProvider');
  }
  return context;
};
```

### Critical Implementation Requirements

1. **Language Persistence**: Language settings must persist across sessions and page refreshes
2. **Translation Completeness**: All user-facing text must have proper translations
3. **Performance**: Language switching should be immediate without full page reloads
4. **Fallback Safety**: Proper fallback to Spanish for any missing translations
5. **Multi-Tenant Support**: Language settings must be congregation-scoped
6. **JW.org Integration**: Language-aware data fetching from external sources

## Testing

### Test Data Requirements

- Coral Oeste congregation with Spanish as default language
- Test congregation with English as default language
- Complete translation files for all major UI components
- Sample JW.org data in both Spanish and English

### Validation Scenarios

- Test language switching affects both admin and member areas immediately
- Test language persistence across browser sessions and page refreshes
- Verify proper fallback behavior for missing translations
- Test JW.org integration with different language settings
- Validate translation completeness across all application sections

## Definition of Done

- [x] Language Settings Management in Congregation Settings implemented
- [x] Comprehensive Multilingual Support for Admin Areas complete (core components)
- [ ] Comprehensive Multilingual Support for Member Areas complete
- [x] Language Persistence and User Experience working
- [ ] JW.org Integration Language Support functional
- [x] Translation Infrastructure and Management complete
- [ ] All unit tests pass with language switching scenarios
- [ ] Integration tests validate complete multilingual workflow
- [ ] E2E tests confirm language switching across all areas
- [ ] Code review completed and approved
- [ ] Documentation updated with multilingual features

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: Product Manager (John)
- Date: 2025-01-25

### Debug Log References
- Implemented LanguageContext with Spanish/English translations
- Created ClientProviders wrapper for React Context integration
- Enhanced congregation settings API with language support
- Updated admin dashboard and footer with translation integration
- Added language dropdown to congregation settings interface

### Completion Notes
- Core language infrastructure implemented and functional
- Spanish translations added for admin interface components
- Language persistence working across browser sessions
- Congregation settings enhanced with language selection
- Admin dashboard and footer now support dynamic language switching
- All dashboard section cards translated to Spanish/English
- Card titles optimized to fit in one row as requested
- Ready for member area translation expansion

### File List
- docs/stories/6.5.story.md (created and updated)
- src/contexts/LanguageContext.tsx (created)
- src/components/providers/ClientProviders.tsx (created)
- src/app/layout.tsx (enhanced with language provider)
- src/app/api/admin/settings/congregation/route.ts (enhanced with language support)
- src/app/admin/page.tsx (enhanced with translations for dashboard cards)
- src/components/admin/AdminFooter.tsx (enhanced with translations)
- src/app/admin/settings/page.tsx (enhanced with language dropdown)
- scripts/test-language-implementation.js (created)
- scripts/test-dashboard-translations.js (created)

### Change Log
- 2025-01-25: Created Story 6.5 for congregation language settings and multilingual support
- 2025-01-25: Defined comprehensive acceptance criteria for language functionality
- 2025-01-25: Planned translation infrastructure and React context implementation
- 2025-01-25: Implemented core language infrastructure and admin translations
- 2025-01-25: Enhanced congregation settings with language selection dropdown
- 2025-01-25: Updated story status to In Progress with partial completion
- 2025-01-25: Added comprehensive dashboard card translations (Spanish/English)
- 2025-01-25: Optimized card titles to fit in one row as requested
