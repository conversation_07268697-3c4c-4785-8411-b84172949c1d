/**
 * PIN Reset API Endpoint
 *
 * Handles PIN reset operations for members.
 * Only accessible to elders, coordinators, and developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PinService } from '@/lib/services/pinService';

// Validation schema for PIN reset
const PinResetSchema = z.object({
  memberId: z.string().min(1, 'Member ID is required'),
  reason: z.string().optional(),
});

/**
 * POST - Reset a member's PIN
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to reset PINs' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = PinResetSchema.parse(body);

    // Extract client information for audit trail
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Reset the PIN
    const result = await PinService.resetMemberPin(
      user.congregationId,
      validatedData.memberId,
      user.userId,
      validatedData.reason,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      success: true,
      newPin: result.newPin,
      message: 'PIN reset successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('PIN reset POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid PIN reset data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to reset PIN';

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
