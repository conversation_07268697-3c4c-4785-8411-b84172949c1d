# MySQL to PostgreSQL Migration Analysis

## Overview
Analysis of the existing MySQL database structure and mapping to PostgreSQL schema for the Hermanos App migration.

## MySQL Database Structure (42 Tables)

### Core Tables
1. **congregations** - Main congregation data
2. **congregation_settings** - Congregation-specific settings
3. **members** - User/member accounts
4. **member_congregations** - Member-congregation relationships
5. **roles** - User roles definition
6. **permissions** - Permission definitions
7. **role_permissions** - Role-permission mappings
8. **user_permissions** - Direct user permissions
9. **elder_permissions** - Elder-specific permissions

### Meeting Management Tables
10. **midweek_meetings** - Midweek meeting data
11. **midweek_meetings_backup** - Backup of midweek meetings
12. **midweek_meeting_parts** - Individual meeting parts
13. **midweek_meeting_songs** - Songs for meetings
14. **midweek_meeting_tasks** - Tasks for meetings
15. **midweek_meeting_settings** - Meeting configuration
16. **midweek_members_settings** - Member-specific meeting settings
17. **midweek_parts** - Part definitions
18. **midweek_part_definitions** - Part type definitions
19. **midweek_part_roles** - Roles for parts
20. **midweek_sections** - Meeting sections
21. **midweek_settings** - General midweek settings
22. **midweek_workbooks** - Workbook data
23. **midweek_attendance** - Attendance tracking
24. **midweek_assignments_view** - View for assignments
25. **midweek_meeting_view** - View for meetings

### Task Management Tables
26. **tasks** - Task definitions
27. **task_assignments** - Task assignments to dates
28. **task_assignment_members** - Members assigned to tasks
29. **task_categories** - Task categorization
30. **task_category_members** - Members in task categories
31. **task_settings** - Task configuration

### Field Service Tables
32. **field_service_records** - Service time tracking
33. **territories** - Territory management

### Communication Tables
34. **letters** - Document/letter management
35. **events** - Congregation events
36. **event_categories** - Event categorization

### Service Groups
37. **groups** - Service group definitions
38. **service_groups** - Service group data

### Songs
39. **songs** - Song catalog
40. **special_songs** - Special songs

### System Tables
41. **feature_flags** - Feature toggles
42. **jw_scraper_logs** - JW.org scraping logs

## PostgreSQL Schema Mapping

### Already Mapped in Prisma Schema
- ✅ Congregation (congregations)
- ✅ Role (roles)
- ✅ Member (members)
- ✅ ElderPermission (elder_permissions)
- ✅ MidweekMeeting (midweek_meetings)
- ✅ MidweekMeetingPart (midweek_meeting_parts)
- ✅ WeekendMeeting (weekend_meetings)
- ✅ WeekendMeetingPart (weekend_meeting_parts)
- ✅ Task (tasks)
- ✅ TaskAssignment (task_assignments)
- ✅ FieldServiceRecord (field_service_records)
- ✅ Letter (letters)
- ✅ Event (events)
- ✅ Song (songs)

### Missing from Prisma Schema
- ❌ congregation_settings
- ❌ member_congregations
- ❌ permissions
- ❌ role_permissions
- ❌ user_permissions
- ❌ midweek_meeting_songs
- ❌ midweek_meeting_tasks
- ❌ midweek_meeting_settings
- ❌ midweek_members_settings
- ❌ midweek_parts
- ❌ midweek_part_definitions
- ❌ midweek_part_roles
- ❌ midweek_sections
- ❌ midweek_settings
- ❌ midweek_workbooks
- ❌ midweek_attendance
- ❌ task_assignment_members
- ❌ task_categories
- ❌ task_category_members
- ❌ task_settings
- ❌ territories
- ❌ event_categories
- ❌ groups
- ❌ service_groups
- ❌ special_songs
- ❌ feature_flags
- ❌ jw_scraper_logs

## Migration Strategy

### Phase 1: Core Data Migration
1. Congregations and settings
2. Roles and permissions
3. Members and relationships

### Phase 2: Meeting Data Migration
1. Midweek meeting structure
2. Meeting parts and assignments
3. Songs and workbooks

### Phase 3: Task and Service Migration
1. Tasks and categories
2. Task assignments
3. Field service records
4. Territories

### Phase 4: Communication Migration
1. Letters and documents
2. Events and categories
3. Service groups

### Phase 5: System Data Migration
1. Feature flags
2. Scraper logs
3. Views and computed data

## Data Validation Requirements

### Integrity Checks
- Foreign key relationships
- Data type compatibility
- Constraint validation
- Unique constraint verification

### Business Logic Validation
- Congregation isolation
- Role-permission consistency
- Meeting assignment logic
- Task assignment rules

## Next Steps
1. Complete Prisma schema with missing tables
2. Enhance migration script with all tables
3. Add comprehensive validation
4. Implement rollback functionality
5. Create testing utilities
