const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkRoles() {
  try {
    console.log('🔍 Checking current roles in use...\n');

    // Get distinct roles from members
    const roles = await prisma.member.groupBy({
      by: ['role'],
      _count: {
        role: true,
      },
      where: {
        congregationId: '1441',
      },
    });

    console.log('📊 Current roles in use:');
    roles.forEach(role => {
      console.log(`  - ${role.role}: ${role._count.role} member(s)`);
    });

    // Check if there's a roles table
    try {
      const rolesTable = await prisma.role.findMany();
      console.log('\n🗂️ Roles table exists with:');
      rolesTable.forEach(role => {
        console.log(`  - ${role.name} (ID: ${role.id})`);
      });
    } catch (error) {
      console.log('\n❌ No roles table found or accessible');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRoles();
