-- Migration for Story 2.3: Enhanced PIN Management and Security
-- Adds tables for temporary PINs, account lockouts, and security audit events

-- Temporary PIN records table
CREATE TABLE "temporary_pins" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "temporary_pin_hash" VARCHAR(255) NOT NULL,
    "reset_type" VARCHAR(50) NOT NULL DEFAULT 'temporary',
    "created_by" TEXT NOT NULL,
    "reason" TEXT,
    "expiration_date" TIMESTAMPTZ NOT NULL,
    "require_change" BOOLEAN NOT NULL DEFAULT true,
    "used" BOOLEAN NOT NULL DEFAULT false,
    "used_at" TIMESTAMPTZ,
    "ip_address" VARCHAR(45),
    "user_agent" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "temporary_pins_pkey" PRIMARY KEY ("id")
);

-- Account lockout records table
CREATE TABLE "account_lockouts" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT NOT NULL,
    "lockout_reason" VARCHAR(100) NOT NULL,
    "attempt_count" INTEGER NOT NULL DEFAULT 0,
    "locked_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "unlock_at" TIMESTAMPTZ NOT NULL,
    "unlocked_by" TEXT,
    "unlocked_at" TIMESTAMPTZ,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "ip_address" VARCHAR(45),
    "user_agent" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "account_lockouts_pkey" PRIMARY KEY ("id")
);

-- Security audit events table
CREATE TABLE "security_audit_events" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT,
    "event_type" VARCHAR(100) NOT NULL,
    "success" BOOLEAN NOT NULL,
    "ip_address" VARCHAR(45),
    "user_agent" TEXT,
    "details" JSONB,
    "performed_by" TEXT,
    "timestamp" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "security_audit_events_pkey" PRIMARY KEY ("id")
);

-- Enhanced PIN security policies (extend existing pin_settings)
ALTER TABLE "pin_settings" ADD COLUMN IF NOT EXISTS "max_attempts" INTEGER NOT NULL DEFAULT 5;
ALTER TABLE "pin_settings" ADD COLUMN IF NOT EXISTS "lockout_duration_minutes" INTEGER NOT NULL DEFAULT 30;
ALTER TABLE "pin_settings" ADD COLUMN IF NOT EXISTS "prevent_reuse_count" INTEGER NOT NULL DEFAULT 3;
ALTER TABLE "pin_settings" ADD COLUMN IF NOT EXISTS "temporary_pin_expiration_hours" INTEGER NOT NULL DEFAULT 24;

-- Add foreign key constraints
ALTER TABLE "temporary_pins" ADD CONSTRAINT "temporary_pins_congregation_id_fkey" 
    FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE;

ALTER TABLE "temporary_pins" ADD CONSTRAINT "temporary_pins_member_id_fkey" 
    FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE;

ALTER TABLE "temporary_pins" ADD CONSTRAINT "temporary_pins_created_by_fkey" 
    FOREIGN KEY ("created_by") REFERENCES "members"("id") ON DELETE CASCADE;

ALTER TABLE "account_lockouts" ADD CONSTRAINT "account_lockouts_congregation_id_fkey" 
    FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE;

ALTER TABLE "account_lockouts" ADD CONSTRAINT "account_lockouts_member_id_fkey" 
    FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE;

ALTER TABLE "account_lockouts" ADD CONSTRAINT "account_lockouts_unlocked_by_fkey" 
    FOREIGN KEY ("unlocked_by") REFERENCES "members"("id") ON DELETE SET NULL;

ALTER TABLE "security_audit_events" ADD CONSTRAINT "security_audit_events_congregation_id_fkey" 
    FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE;

ALTER TABLE "security_audit_events" ADD CONSTRAINT "security_audit_events_member_id_fkey" 
    FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE SET NULL;

ALTER TABLE "security_audit_events" ADD CONSTRAINT "security_audit_events_performed_by_fkey" 
    FOREIGN KEY ("performed_by") REFERENCES "members"("id") ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX "idx_temporary_pins_congregation_member" ON "temporary_pins"("congregation_id", "member_id");
CREATE INDEX "idx_temporary_pins_expiration" ON "temporary_pins"("expiration_date");
CREATE INDEX "idx_temporary_pins_used" ON "temporary_pins"("used", "expiration_date");

CREATE INDEX "idx_account_lockouts_congregation_member" ON "account_lockouts"("congregation_id", "member_id");
CREATE INDEX "idx_account_lockouts_active" ON "account_lockouts"("is_active", "unlock_at");

CREATE INDEX "idx_security_audit_events_congregation" ON "security_audit_events"("congregation_id");
CREATE INDEX "idx_security_audit_events_member" ON "security_audit_events"("member_id");
CREATE INDEX "idx_security_audit_events_type" ON "security_audit_events"("event_type");
CREATE INDEX "idx_security_audit_events_timestamp" ON "security_audit_events"("timestamp");

-- Create unique constraints
CREATE UNIQUE INDEX "idx_pin_settings_congregation" ON "pin_settings"("congregation_id");
CREATE UNIQUE INDEX "idx_temporary_pins_active_member" ON "temporary_pins"("congregation_id", "member_id") 
    WHERE "used" = false AND "expiration_date" > CURRENT_TIMESTAMP;
