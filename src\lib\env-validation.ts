import { z } from 'zod'

// Environment variable schema
const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().url('DATABASE_URL must be a valid URL'),
  DATABASE_POOL_SIZE: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional(),

  // Application URLs
  NEXT_PUBLIC_APP_URL: z.string().url('NEXT_PUBLIC_APP_URL must be a valid URL'),
  NEXT_PUBLIC_API_URL: z.string().url('NEXT_PUBLIC_API_URL must be a valid URL'),

  // Authentication
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('60d'),
  BCRYPT_ROUNDS: z.string().transform(Number).pipe(z.number().min(10).max(15)).optional(),

  // File Storage
  UPLOAD_DIR: z.string().default('./public/uploads'),
  MAX_FILE_SIZE: z.string().transform(Number).pipe(z.number().min(1)).optional(),

  // Development
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),

  // External Services
  JW_ORG_BASE_URL: z.string().url().default('https://wol.jw.org'),
})

export type Env = z.infer<typeof envSchema>

let validatedEnv: Env | null = null

export function validateEnvironmentVariables(force = false): Env {
  if (validatedEnv && !force) {
    return validatedEnv
  }

  try {
    validatedEnv = envSchema.parse(process.env)
    return validatedEnv
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      )
      throw new Error(
        `Environment validation failed:\n${errorMessages.join('\n')}`
      )
    }
    throw error
  }
}

export function getEnv(): Env {
  if (!validatedEnv) {
    throw new Error('Environment variables not validated. Call validateEnvironmentVariables() first.')
  }
  return validatedEnv
}

// For testing purposes - reset the validation cache
export function resetValidation(): void {
  validatedEnv = null
}

// Validate environment on module load in non-test environments
if (process.env.NODE_ENV !== 'test') {
  try {
    validateEnvironmentVariables()
    console.log('✅ Environment variables validated successfully')
  } catch (error) {
    console.error('❌ Environment validation failed:', error)
    if (process.env.NODE_ENV === 'production') {
      process.exit(1)
    }
  }
}
