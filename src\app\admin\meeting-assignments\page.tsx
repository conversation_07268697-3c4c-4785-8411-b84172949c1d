'use client';

/**
 * Admin Meeting Assignment Coordination Page
 *
 * Administrative interface for managing meeting part assignments,
 * viewing assignment statistics, and coordinating congregation assignments.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminFooter from '@/components/admin/AdminFooter';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface MeetingPartAssignment {
  id: string;
  meetingId: string;
  partType: string;
  title: string;
  assignedMember: string | null;
  assistant: string | null;
  timeAllocation: number | null;
  notes: string | null;
  isCompleted: boolean;
  meetingDate: Date;
  meetingType: 'midweek' | 'weekend';
  member?: {
    id: string;
    name: string;
    role: string;
  };
  assistantMember?: {
    id: string;
    name: string;
    role: string;
  };
}

interface AssignmentStatistics {
  totalUpcomingAssignments: number;
  unassignedParts: number;
  membersWithAssignments: number;
  averageAssignmentsPerMember: number;
  assignmentsByType: Record<string, number>;
  assignmentsByMonth: Record<string, number>;
  memberWorkload: Array<{
    memberId: string;
    memberName: string;
    assignmentCount: number;
    lastAssignment: Date | null;
  }>;
}

interface Member {
  id: string;
  name: string;
  role: string;
}

export default function AdminMeetingAssignmentsPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [assignments, setAssignments] = useState<MeetingPartAssignment[]>([]);
  const [unassignedParts, setUnassignedParts] = useState<MeetingPartAssignment[]>([]);
  const [statistics, setStatistics] = useState<AssignmentStatistics | null>(null);
  const [members, setMembers] = useState<Member[]>([]);
  const [isAssigning, setIsAssigning] = useState<string | null>(null);

  useEffect(() => {
    checkAuthentication();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user, activeTab]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();

      // Check if user has permission to access admin assignments
      if (!['elder', 'ministerial_servant'].includes(data.user.role)) {
        router.push('/dashboard');
        return;
      }

      setUser(data.user);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchData = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('hermanos_token');

      // Fetch different data based on active tab
      if (activeTab === 'overview') {
        // Fetch statistics
        const statsResponse = await fetch('/api/assignments/statistics?type=overview', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStatistics(statsData.statistics);
        }
      } else if (activeTab === 'unassigned') {
        // Fetch unassigned parts
        const unassignedResponse = await fetch('/api/assignments/statistics?type=unassigned', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        if (unassignedResponse.ok) {
          const unassignedData = await unassignedResponse.json();
          setUnassignedParts(unassignedData.unassignedParts);
        }

        // Fetch members for assignment
        const membersResponse = await fetch('/api/members', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        if (membersResponse.ok) {
          const membersData = await membersResponse.json();
          setMembers(membersData.members || []);
        }
      } else if (activeTab === 'assignments') {
        // Fetch all assignments
        const assignmentsResponse = await fetch('/api/assignments?includeUnassigned=true&limit=200', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        if (assignmentsResponse.ok) {
          const assignmentsData = await assignmentsResponse.json();
          setAssignments(assignmentsData.assignments);
        }
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const assignMeetingPart = async (partId: string, memberId: string, assistantId?: string) => {
    if (!user) return;

    setIsAssigning(partId);
    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/assignments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          meetingId: '', // Will be determined by the service
          partId,
          assignedMemberId: memberId,
          assistantId,
        }),
      });

      if (response.ok) {
        await fetchData(); // Refresh data
        alert('Asignación realizada exitosamente');
      } else {
        const errorData = await response.json();
        alert(`Error al asignar: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error assigning meeting part:', error);
      alert('Error al realizar la asignación');
    } finally {
      setIsAssigning(null);
    }
  };

  const removeAssignment = async (partId: string) => {
    if (!user) return;
    if (!confirm('¿Estás seguro de que quieres quitar esta asignación?')) return;

    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/assignments', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ partId }),
      });

      if (response.ok) {
        await fetchData(); // Refresh data
        alert('Asignación removida exitosamente');
      } else {
        const errorData = await response.json();
        alert(`Error al remover: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error removing assignment:', error);
      alert('Error al remover la asignación');
    }
  };

  const getPartTypeDisplayText = (partType: string): string => {
    const partTypeMap: Record<string, string> = {
      'song': 'Canción',
      'prayer': 'Oración',
      'treasures': 'Tesoros de la Palabra de Dios',
      'ministry': 'Seamos mejores maestros',
      'living': 'Nuestra vida cristiana',
      'bible_reading': 'Lectura de la Biblia',
      'initial_call': 'Primera conversación',
      'return_visit': 'Revisita',
      'bible_study': 'Curso bíblico',
      'talk': 'Discurso',
      'congregation_study': 'Estudio de la congregación',
      'public_talk': 'Discurso público',
      'watchtower': 'Estudio de La Atalaya',
      'opening_prayer': 'Oración inicial',
      'closing_prayer': 'Oración final',
      'chairman': 'Presidente',
      'reader': 'Lector',
    };

    return partTypeMap[partType] || partType;
  };

  const formatDate = (dateString: string | Date): string => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-purple-600 text-white p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <button
              onClick={() => router.push('/admin')}
              className="text-purple-200 hover:text-white mb-2 flex items-center"
            >
              ← Volver a Administración
            </button>
            <h1 className="text-2xl font-bold">Coordinación de Asignaciones de Reunión</h1>
            <p className="text-purple-200">Gestiona las asignaciones de partes de reunión</p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 pb-20">
        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'overview', label: 'Resumen', icon: '📊' },
              { key: 'unassigned', label: 'Sin Asignar', icon: '❗' },
              { key: 'assignments', label: 'Todas las Asignaciones', icon: '📋' },
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2 ${
                  activeTab === tab.key
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content based on active tab */}
        {activeTab === 'overview' && statistics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Total de Asignaciones</h3>
              <p className="text-3xl font-bold text-purple-600">{statistics.totalUpcomingAssignments}</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Sin Asignar</h3>
              <p className="text-3xl font-bold text-red-600">{statistics.unassignedParts}</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Hermanos con Asignaciones</h3>
              <p className="text-3xl font-bold text-green-600">{statistics.membersWithAssignments}</p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Promedio por Hermano</h3>
              <p className="text-3xl font-bold text-blue-600">{statistics.averageAssignmentsPerMember}</p>
            </div>
          </div>
        )}

        {activeTab === 'unassigned' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Partes Sin Asignar</h2>
            {unassignedParts.length === 0 ? (
              <p className="text-gray-500 text-center py-8">¡Excelente! No hay partes sin asignar.</p>
            ) : (
              <div className="space-y-4">
                {unassignedParts.map((part) => (
                  <div key={part.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-gray-900">{part.title}</h3>
                        <p className="text-sm text-gray-600">
                          {getPartTypeDisplayText(part.partType)} - {formatDate(part.meetingDate)}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <select
                          className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                          onChange={(e) => {
                            if (e.target.value) {
                              assignMeetingPart(part.id, e.target.value);
                              e.target.value = '';
                            }
                          }}
                          disabled={isAssigning === part.id}
                        >
                          <option value="">Asignar a...</option>
                          {members.map(member => (
                            <option key={member.id} value={member.id}>
                              {member.name} ({member.role})
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'assignments' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Todas las Asignaciones</h2>
            <div className="space-y-4">
              {assignments.map((assignment) => (
                <div key={assignment.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">{assignment.title}</h3>
                      <p className="text-sm text-gray-600">
                        {getPartTypeDisplayText(assignment.partType)} - {formatDate(assignment.meetingDate)}
                      </p>
                      {assignment.member && (
                        <p className="text-sm text-blue-600">
                          Asignado a: {assignment.member.name}
                          {assignment.assistantMember && ` (Ayudante: ${assignment.assistantMember.name})`}
                        </p>
                      )}
                    </div>
                    {assignment.assignedMember && (
                      <button
                        onClick={() => removeAssignment(assignment.id)}
                        className="px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200"
                      >
                        Quitar
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Admin Footer */}
      <AdminFooter currentSection="meeting-assignments" />
    </div>
  );
}
