/**
 * Test Date Logic
 * 
 * This script tests the date filtering logic to understand why upcoming schedules aren't showing
 */

// Simulate the dates from the database
const serviceDates = [
  '2025-07-25T00:00:00.000Z',
  '2025-07-26T00:00:00.000Z'
];

console.log('🧪 TESTING DATE LOGIC');
console.log('===================');

// Get today's date
const today = new Date();
console.log('📅 Today (raw):', today);
console.log('📅 Today (ISO):', today.toISOString());
console.log('📅 Today (date string):', today.toDateString());

// Test the filtering logic for each service date
serviceDates.forEach((serviceDateStr, index) => {
  console.log(`\n🔍 Testing Service Date ${index + 1}: ${serviceDateStr}`);
  
  // Create date objects for proper comparison (avoiding timezone issues)
  const serviceDate = new Date(serviceDateStr);
  
  // Set time to midnight for date-only comparison
  const serviceDateOnly = new Date(serviceDate.getFullYear(), serviceDate.getMonth(), serviceDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  
  const isHistorical = serviceDateOnly < todayOnly;
  const isUpcoming = serviceDateOnly >= todayOnly;
  
  console.log('  📅 Service Date (raw):', serviceDate);
  console.log('  📅 Service Date (only):', serviceDateOnly.toDateString());
  console.log('  📅 Today (only):', todayOnly.toDateString());
  console.log('  📊 Comparison:');
  console.log('    - Service < Today:', serviceDateOnly < todayOnly);
  console.log('    - Service >= Today:', serviceDateOnly >= todayOnly);
  console.log('  📋 Results:');
  console.log('    - Is Historical:', isHistorical);
  console.log('    - Is Upcoming:', isUpcoming);
  console.log('  🎯 Would show in:');
  console.log('    - Historical view:', isHistorical ? '✅ YES' : '❌ NO');
  console.log('    - Upcoming view:', isUpcoming ? '✅ YES' : '❌ NO');
});

console.log('\n📊 SUMMARY');
console.log('==========');
console.log('If upcoming schedules are not showing, the issue is likely:');
console.log('1. Date parsing/timezone conversion');
console.log('2. Frontend state management');
console.log('3. UI rendering logic');
