# Territories Management System Product Requirements Document (PRD)

## Goals and Background Context

### Goals

- Import existing territory data from Excel files into the database system
- Enable territory assignment to congregation members (Elders, Ministerial Servants, Publishers)
- Provide dedicated territory management interface for administrators (separate admin card)
- Track territory status and completion
- Integrate with MapLibre for territory visualization
- Create member territory interface aligned with existing Field Service UI patterns
- Replace manual Excel-based territory management with digital system

### Background Context

The Hermanos App congregation currently manages territories using individual Excel files, requiring manual coordination and making it difficult to track assignments, completion status, and territory availability. This creates inefficiencies in field service organization and limits visibility into territory coverage. The digital territory management system will centralize territory data, automate assignment workflows, and provide real-time visibility into territory status, enabling more effective field service coordination and ensuring comprehensive coverage of the congregation's assigned territory.

**Admin Interface**: Territories will have a dedicated admin card separate from Field Service management due to the comprehensive functionality required (import, assignment, mapping, reporting).

**Member Interface**: The member-facing territory interface will follow the established Field Service UI patterns shown in the reference screenshots (1.jpg through 8.jpg) to maintain consistency with existing user experience.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-25 | 1.0 | Initial PRD creation | PM Agent |

## Requirements

### Functional Requirements

**FR1:** The system shall import territory data from Excel files (.xlsx format) including territory number and address information.

**FR2:** The system shall allow administrators to assign territories to congregation members (Elders, Ministerial Servants, and approved Publishers).

**FR3:** The system shall track territory status (available, assigned, completed, out of service).

**FR4:** The system shall maintain assignment history including assignment date, assigned member, and completion date.

**FR5:** The system shall provide a territory management dashboard showing all territories with their current status and assignments.

**FR6:** The system shall integrate with MapLibre to display territory boundaries and locations.

**FR7:** The system shall allow members to view their assigned territories with detailed address information.

**FR9:** The system shall generate territory assignment reports for service coordinators.

**FR11:** The system shall maintain congregation-specific territory isolation (multi-tenant support).

**FR12:** The system shall provide territory search and filtering capabilities by status, assigned member, or territory number.

### Non-Functional Requirements

**NFR1:** The system shall support concurrent access by up to 100 congregation members without performance degradation.

**NFR2:** Territory data import shall process all Excel files within 5 minutes for typical congregation size (80-100 territories).

**NFR3:** The system shall maintain 99.9% uptime during peak usage hours (weekends and meeting times).

**NFR4:** All territory data shall be backed up daily with point-in-time recovery capability.

**NFR5:** The system shall be responsive and functional on mobile devices with touch-friendly interfaces.

**NFR6:** Territory assignment changes shall be reflected in real-time across all user sessions.

**NFR7:** The system shall comply with data privacy regulations and congregation data protection policies.

**NFR8:** Map integration shall load territory boundaries within 3 seconds on standard internet connections.

**NFR9:** The system shall support Spanish and English languages with easy language switching.

**NFR10:** Territory data export shall be available in Excel format for backup and reporting purposes.

## User Interface Design Goals

### Overall UX Vision

The territory management interface should seamlessly integrate with the existing Hermanos App design language, maintaining the clean, modern aesthetic with Spanish-first language support. The interface should prioritize mobile-first design given field service usage patterns, with intuitive navigation that allows quick territory lookup and status updates.

**Admin Interface**: Dedicated "Territorios" admin card with comprehensive territory management functionality, separate from Field Service admin section.

**Member Interface**: Must align with existing Field Service UI patterns (reference screenshots 1.jpg through 8.jpg) to ensure consistent user experience. The member territory interface should follow the same visual hierarchy, navigation patterns, card layouts, and interaction paradigms established in the Field Service section.

### Key Interaction Paradigms

- **Card-based Territory Display**: Each territory represented as a card showing number, address, status, and assigned member
- **Touch-friendly Assignment**: Drag-and-drop or tap-to-assign functionality for territory management
- **Map Integration**: Seamless transition between list view and map view with territory boundaries
- **Status Indicators**: Clear visual indicators (colors, icons) for territory availability and completion status
- **Quick Actions**: Swipe gestures or context menus for common actions (assign, complete, view details)

### Core Screens and Views

**Admin Screens (Dedicated "Territorios" Admin Card):**
- **Territory Dashboard**: Main administrative view showing all territories with filters and search
- **Territory Assignment Screen**: Interface for assigning territories to members with member selection
- **Territory Import Screen**: Administrative interface for uploading and processing Excel files
- **Territory Analytics Screen**: Reports and analytics for territory management
- **Territory Map Management**: Admin map view for boundary editing and territory visualization

**Member Screens (Following Field Service UI Patterns):**
- **My Territories View**: Personal view following Field Service card layout patterns (reference screenshots 1-8)
- **Territory Detail Screen**: Detailed view with address list following Field Service detail patterns
- **Territory Map View**: Member map view with territory locations and navigation
- **Territory Completion**: Interface for marking territories complete, aligned with Field Service workflows

### Accessibility: WCAG AA

The interface will meet WCAG AA standards with proper color contrast, keyboard navigation support, screen reader compatibility, and touch target sizing appropriate for mobile devices.

### Branding

Maintain consistency with the existing Hermanos App design system including color palette, typography, and component styling. Use the established Spanish-language interface patterns and maintain the clean, professional appearance suitable for congregation use. Member territory interfaces must specifically follow the visual patterns established in the Field Service section (screenshots 1.jpg through 8.jpg).

### Target Device and Platforms: Web Responsive

Primary focus on responsive web design that works seamlessly across desktop, tablet, and mobile devices, with particular attention to mobile optimization for field service use cases.

## Technical Assumptions

### Repository Structure: Monorepo

The territories management system will be integrated into the existing Hermanos App monorepo structure, adding new components, API routes, and database models to the current Next.js application.

### Service Architecture

**Integrated Monolith Architecture**: The territories management functionality will be built as an integrated feature within the existing Hermanos App Next.js architecture, following the established patterns:
- Next.js App Router for API routes (`/api/territories/*`)
- Prisma ORM for database operations with the existing PostgreSQL database
- Server-side components and client-side React components
- Integration with existing authentication and authorization middleware
- Dedicated admin card for "Territorios" separate from Field Service admin
- Member interface components following Field Service UI patterns

### Testing Requirements

**Unit + Integration Testing**: Following the existing testing patterns in the codebase:
- Jest for unit testing of utility functions and services
- React Testing Library for component testing
- API route testing for territory management endpoints
- Database integration testing with test database
- Manual testing convenience methods for territory import and assignment workflows

### Additional Technical Assumptions and Requests

**Database Integration**:
- Extend existing PostgreSQL database with territory-related tables (territories table already exists)
- Use existing Prisma schema and migration patterns
- Maintain multi-tenant isolation using congregation_id

**Authentication & Authorization**:
- Integrate with existing JWT-based authentication system
- Use existing role-based access control (Elder, Ministerial Servant, Publisher permissions)
- Leverage existing middleware for route protection

**Frontend Framework**:
- Next.js 14+ with App Router (matching existing codebase)
- TypeScript for type safety
- Tailwind CSS for styling (consistent with existing UI)
- React Hook Form for form management
- Zustand for state management if needed

**External Integrations**:
- MapLibre GL JS for territory visualization with OpenStreetMap tiles
- Excel file parsing using existing Node.js libraries (xlsx or similar)
- Integration with existing Hermanos App notification system patterns
- Geocoding service for address-to-coordinate conversion

**Development Tools**:
- ESLint and Prettier (matching existing configuration)
- Existing development scripts and build processes
- Integration with current backup and deployment workflows

## Epic List

### Epic List for Territories Management System

**Epic 10: Foundation & Territory Data Import**
Establish territory management infrastructure and import existing Excel territory data into the database system.

**Epic 11: Territory Assignment & Management**
Enable administrators to assign territories to congregation members and track assignment status with basic management workflows.

**Epic 12: Territory Visualization & Member Interface**
Integrate MapLibre for territory visualization and provide member-facing interfaces for viewing assigned territories.

**Epic 13: Advanced Territory Management & Reporting**
Implement advanced territory management features including bulk operations, reporting, and territory analytics.

## Epic 10: Foundation & Territory Data Import

**Epic Goal:** Establish the foundational territory management infrastructure within the existing Coral Oeste App and successfully import all existing Excel territory data into the database system, providing immediate digital access to territory information and replacing manual Excel file management.

### Story 10.1: Territory Database Schema Enhancement

As a system administrator,
I want the territory database schema to support comprehensive territory management,
so that all territory data can be properly stored and managed digitally.

#### Acceptance Criteria

1. Territory table schema includes fields for territory number, address, status, assignment tracking, and congregation isolation
2. Database migration scripts are created and tested for schema updates
3. Prisma schema is updated with Territory model and relationships to Member model
4. Database indexes are created for optimal query performance on territory lookups
5. Multi-tenant isolation is enforced through congregation_id foreign key constraints

### Story 10.2: Excel Territory Data Import Service

As a congregation administrator,
I want to upload Excel files containing territory data,
so that existing territory information can be imported into the digital system.

#### Acceptance Criteria

1. API endpoint accepts Excel file uploads (.xlsx format)
2. Excel parser extracts territory number and address information from uploaded files
3. Data validation ensures territory numbers are unique within congregation
4. Import process handles errors gracefully and provides detailed feedback
5. Imported territories are automatically assigned "available" status
6. Import results summary shows successful imports, errors, and duplicates

### Story 10.3: Territory Management Admin Interface

As a congregation administrator,
I want a dedicated "Territorios" admin card and territory management dashboard,
so that I can view and manage all territories in one centralized location separate from Field Service admin.

#### Acceptance Criteria

1. Dedicated "Territorios" admin card is added to the admin dashboard (separate from Field Service)
2. Territory management dashboard displays all territories in a responsive grid/list view
3. Territory cards show territory number, address, current status, and assigned member
4. Search functionality allows filtering by territory number, address, or status
5. Status filter options include: available, assigned, completed, out of service
6. Territory count summary displays total territories and count by status
7. Interface follows existing Hermanos App design patterns and Spanish language support
8. Admin card uses appropriate icon and color scheme consistent with other admin sections

### Story 10.4: Bulk Territory Import Processing

As a congregation administrator,
I want to import multiple territory Excel files at once,
so that I can efficiently process all 80+ territory files without manual repetition.

#### Acceptance Criteria

1. Bulk upload interface accepts multiple Excel files simultaneously
2. Processing queue handles multiple files with progress tracking
3. Import results are aggregated across all files with detailed reporting
4. Failed imports are clearly identified with specific error messages
5. Successfully imported territories are immediately available in the territory dashboard
6. Import process completes within 5 minutes for typical congregation size (80-100 territories)

### Story 10.5: Territory Data Validation and Cleanup

As a congregation administrator,
I want imported territory data to be validated and cleaned,
so that the territory database maintains data quality and consistency.

#### Acceptance Criteria

1. Territory numbers are validated for proper format and uniqueness
2. Address data is cleaned and standardized for consistency
3. Duplicate territory detection prevents multiple entries for same territory
4. Data validation errors are reported with specific territory and issue details
5. Manual override capability allows administrators to resolve validation conflicts
6. Validation rules can be configured for different congregation requirements

## Epic 11: Territory Assignment & Management

**Epic Goal:** Enable congregation administrators to assign territories to members (Elders, Ministerial Servants, and approved Publishers) with comprehensive tracking of assignment status, history, and workflow management, delivering the core business value of digital territory coordination.

### Story 11.1: Member Territory Assignment Interface

As a congregation administrator,
I want to assign territories to congregation members,
so that territory assignments can be tracked digitally instead of manually.

#### Acceptance Criteria

1. Assignment interface displays available territories and congregation members
2. Dropdown or search functionality allows selection of member for assignment
3. Assignment date is automatically recorded when territory is assigned
4. Territory status changes from "available" to "assigned" upon assignment
5. Assignment confirmation shows territory details and assigned member information
6. Only users with Elder or Ministerial Servant roles can access assignment functionality

### Story 11.2: Territory Assignment History Tracking

As a congregation administrator,
I want to view territory assignment history,
so that I can track who has worked territories and when they were completed.

#### Acceptance Criteria

1. Territory detail view displays complete assignment history chronologically
2. History entries show assigned member, assignment date, completion date, and duration
3. Current assignment is clearly distinguished from historical assignments
4. Assignment history is preserved when territories are reassigned
5. History view includes search and filter capabilities by member or date range
6. Assignment statistics show average assignment duration and completion rates

### Story 11.3: Territory Status Management

As a congregation administrator,
I want to manage territory status changes,
so that territory availability and workflow states are accurately tracked.

#### Acceptance Criteria

1. Territory status can be updated to: available, assigned, completed, out of service
2. Status change interface provides reason/notes field for documentation
3. Status changes are logged with timestamp and user who made the change
4. Completed territories automatically become available for reassignment
5. Out of service territories are excluded from assignment workflows
6. Status change notifications are sent to relevant administrators

### Story 11.4: Member Territory Assignment View

As a congregation member,
I want to view my assigned territories following the established Field Service UI patterns,
so that I can see what territories I need to work with a familiar interface.

#### Acceptance Criteria

1. Member territory interface follows the exact UI patterns from Field Service screenshots (1.jpg through 8.jpg)
2. Territory cards use the same layout, styling, and visual hierarchy as Field Service cards
3. Territory list view matches Field Service list patterns with consistent spacing and typography
4. Territory detail view follows Field Service detail screen patterns
5. Navigation and interaction patterns match existing Field Service workflows
6. Territory cards display territory number, address, and assignment date using Field Service card format
7. Assignment duration indicator follows Field Service time display patterns
8. Member can mark territory as completed using Field Service-style completion interface
9. Interface maintains mobile optimization consistent with Field Service mobile patterns

### Story 11.5: Territory Assignment Notifications

As a congregation member,
I want to receive notifications about territory assignments,
so that I'm informed when territories are assigned to me or need attention.

#### Acceptance Criteria

1. Email notifications are sent when territories are assigned to members
2. Notification includes territory details and assignment information
3. Reminder notifications are sent for territories assigned beyond typical duration
4. Members can configure notification preferences (email, in-app, frequency)
5. Administrators receive notifications when territories are marked completed
6. Notification system integrates with existing Coral Oeste App notification patterns

### Story 11.6: Territory Assignment Reports

As a service coordinator,
I want to generate territory assignment reports,
so that I can monitor territory coverage and assignment effectiveness.

#### Acceptance Criteria

1. Assignment report shows all territories with current status and assigned members
2. Report includes assignment duration and completion statistics
3. Available territories report helps identify unassigned territories
4. Member workload report shows territory distribution across members
5. Reports can be filtered by date range, member, or territory status
6. Reports are exportable in PDF format for congregation records

## Epic 12: Territory Visualization & Member Interface

**Epic Goal:** Integrate MapLibre (open-source mapping solution) for territory visualization and provide comprehensive member-facing interfaces for viewing assigned territories with geographic context, completing the user-facing functionality that makes the system fully usable for field service activities.

### Story 12.1: MapLibre Integration Setup

As a system administrator,
I want MapLibre integrated into the territory management system,
so that territories can be visualized geographically using open-source mapping technology.

#### Acceptance Criteria

1. MapLibre GL JS is integrated into the application with appropriate tile sources
2. Map component loads territory locations based on address data using OpenStreetMap tiles
3. Map displays with appropriate zoom level for congregation territory area
4. Error handling manages tile loading failures and network issues gracefully
5. Map performance is optimized for mobile device usage
6. Alternative tile sources (Mapbox, OpenStreetMap) are configured as fallbacks

### Story 12.2: Territory Location Mapping

As a congregation administrator,
I want territories displayed on a map with their locations,
so that I can visualize territory distribution and geographic coverage.

#### Acceptance Criteria

1. All territories are plotted on the map using their address information via geocoding service
2. Territory markers are color-coded by status (available, assigned, completed, out of service)
3. Territory markers display territory number and basic information on hover/tap
4. Map view can be toggled between different tile styles (street, satellite if available)
5. Territory markers are clustered when zoomed out for better performance
6. Map boundaries automatically adjust to show all congregation territories

### Story 12.3: Interactive Territory Map Features

As a congregation member,
I want to interact with territories on the map,
so that I can get detailed information and navigate to territory locations.

#### Acceptance Criteria

1. Clicking/tapping territory markers opens detailed information popup
2. Territory popup shows number, address, assignment status, and assigned member
3. "Get Directions" button opens navigation app with territory address
4. Map supports pinch-to-zoom and pan gestures on mobile devices
5. Search functionality allows finding specific territories on the map
6. Filter controls show/hide territories based on status or assignment

### Story 12.4: Member Territory Map View

As a congregation member,
I want to see my assigned territories highlighted on a map,
so that I can plan my field service route efficiently.

#### Acceptance Criteria

1. Member map view highlights only territories assigned to the logged-in user
2. Assigned territories are visually distinct with special markers or colors
3. Basic route planning connects multiple assigned territories efficiently
4. Territory completion can be marked directly from the map interface
5. Map shows approximate distances between assigned territories
6. Offline map caching allows basic functionality without internet connection

### Story 12.5: Territory Boundary Visualization

As a congregation administrator,
I want to define and display territory boundaries on the map,
so that territory coverage areas are clearly defined and visible.

#### Acceptance Criteria

1. Territory boundaries can be drawn and edited using map drawing tools
2. Boundary polygons are stored and associated with territory records
3. Territory boundaries are displayed as colored overlays on the map
4. Boundary editing is restricted to administrators with appropriate permissions
5. Overlapping territory boundaries are detected and highlighted for resolution
6. Territory boundaries can be imported from KML or GeoJSON files if available

### Story 12.6: Mobile-Optimized Territory Interface

As a congregation member,
I want a mobile-optimized territory interface that matches Field Service mobile patterns,
so that I can effectively use the system during field service activities with familiar interactions.

#### Acceptance Criteria

1. Territory mobile interface follows Field Service mobile optimization patterns exactly
2. Touch targets, spacing, and mobile layouts match Field Service mobile design (screenshots 1-8)
3. Mobile navigation patterns are consistent with Field Service mobile workflows
4. Territory list view provides quick access following Field Service mobile list patterns
5. Swipe gestures and mobile interactions match Field Service mobile behavior
6. Mobile interface works reliably on both iOS and Android devices
7. Data usage is minimized for members with limited mobile data plans
8. Mobile territory completion flows follow Field Service mobile completion patterns
9. Mobile map interactions are optimized for touch following Field Service mobile standards

## Epic 13: Advanced Territory Management & Reporting

**Epic Goal:** Implement advanced territory management features including bulk operations, comprehensive reporting, and territory analytics that optimize the territory management process and provide insights for service coordinators and elders.

### Story 13.1: Bulk Territory Operations

As a congregation administrator,
I want to perform bulk operations on multiple territories,
so that I can efficiently manage large numbers of territories simultaneously.

#### Acceptance Criteria

1. Multi-select functionality allows selection of multiple territories from the dashboard
2. Bulk assignment interface enables assigning multiple territories to one member
3. Bulk status updates allow changing status of multiple territories at once
4. Bulk operations include confirmation dialog showing affected territories
5. Progress indicator shows completion status for bulk operations
6. Bulk operation results summary shows successful and failed operations

### Story 13.2: Territory Analytics Dashboard

As a service coordinator,
I want analytics and insights about territory management,
so that I can optimize territory assignments and track congregation coverage effectiveness.

#### Acceptance Criteria

1. Analytics dashboard shows territory coverage statistics and trends
2. Assignment duration analytics identify territories that take longer to complete
3. Member productivity metrics show assignment completion rates by member
4. Territory utilization charts display how frequently territories are worked
5. Geographic coverage analysis identifies underworked areas
6. Analytics data can be filtered by date range, member, or territory characteristics

### Story 13.3: Advanced Territory Reporting

As an elder,
I want comprehensive territory reports for congregation oversight,
so that I can monitor field service effectiveness and make informed decisions.

#### Acceptance Criteria

1. Comprehensive territory status report shows all territories with detailed information
2. Member assignment report displays workload distribution across congregation
3. Territory completion timeline report tracks progress over time
4. Unworked territory report identifies territories needing attention
5. Custom report builder allows creating reports with specific criteria
6. All reports are exportable in PDF and Excel formats for record keeping

### Story 13.4: Territory Assignment Optimization

As a service coordinator,
I want assignment recommendations based on territory and member data,
so that I can make optimal territory assignments for effective coverage.

#### Acceptance Criteria

1. Assignment recommendation engine suggests optimal member-territory pairings
2. Recommendations consider member location, availability, and assignment history
3. Territory difficulty assessment helps match appropriate members to territories
4. Geographic optimization suggests efficient territory groupings for members
5. Workload balancing recommendations ensure fair distribution of assignments
6. Recommendation explanations help coordinators understand suggested assignments

### Story 13.5: Territory Data Export and Backup

As a congregation administrator,
I want to export and backup territory data,
so that congregation records are preserved and can be shared with other systems.

#### Acceptance Criteria

1. Complete territory data export includes all territories, assignments, and history
2. Export formats include Excel, CSV, and JSON for different use cases
3. Selective export allows choosing specific territories or date ranges
4. Exported data maintains referential integrity and includes all related information
5. Import functionality allows restoring from exported data files
6. Automated backup scheduling ensures regular data preservation

### Story 13.6: Territory Management API

As a system integrator,
I want a comprehensive API for territory management,
so that territory data can be integrated with other congregation management systems.

#### Acceptance Criteria

1. RESTful API provides full CRUD operations for territories and assignments
2. API authentication uses existing congregation authentication system
3. API documentation includes examples and integration guidelines
4. Rate limiting prevents abuse while allowing legitimate integrations
5. API versioning ensures backward compatibility for future updates
6. Webhook support enables real-time notifications for external systems

## Checklist Results Report

### Executive Summary

**Overall PRD Completeness:** 92%
**MVP Scope Appropriateness:** Just Right
**Readiness for Architecture Phase:** Ready
**Most Critical Concerns:** Minor gaps in user research documentation and performance benchmarking

### Category Analysis

| Category                         | Status  | Critical Issues |
| -------------------------------- | ------- | --------------- |
| 1. Problem Definition & Context  | PASS    | None - Clear problem statement and business context |
| 2. MVP Scope Definition          | PASS    | Well-defined scope with clear boundaries |
| 3. User Experience Requirements  | PASS    | Comprehensive UI/UX vision with mobile focus |
| 4. Functional Requirements       | PASS    | Complete functional requirements with clear priorities |
| 5. Non-Functional Requirements   | PARTIAL | Missing specific performance benchmarks |
| 6. Epic & Story Structure        | PASS    | Well-structured epics with logical sequencing |
| 7. Technical Guidance            | PASS    | Clear technical assumptions and constraints |
| 8. Cross-Functional Requirements | PASS    | Integration and operational requirements defined |
| 9. Clarity & Communication       | PASS    | Clear documentation with consistent terminology |

### Top Issues by Priority

**MEDIUM Priority:**
- Performance benchmarks could be more specific (e.g., exact response times for map loading)
- User research section could benefit from more detailed persona definitions
- Territory boundary definition approach needs clarification (manual vs. imported)

**LOW Priority:**
- Consider adding more detailed error handling scenarios
- API rate limiting specifics could be more detailed
- Offline functionality requirements could be expanded

### MVP Scope Assessment

**Scope Appropriateness:** The MVP scope is well-balanced, focusing on core territory management functionality while deferring advanced features to later epics. The progression from data import to assignment to visualization to advanced features follows logical user value delivery.

**Essential Features Covered:**
- Territory data import and management ✓
- Assignment workflow and tracking ✓
- Member interfaces for field service ✓
- Map visualization for geographic context ✓

**Appropriate Deferrals:**
- Advanced analytics and reporting (Epic 13)
- Bulk operations (Epic 13)
- API integrations (Epic 13)

### Technical Readiness

**Technical Constraints:** Well-defined integration with existing Coral Oeste App architecture
**Identified Technical Risks:** MapLibre integration complexity, Excel parsing reliability, geocoding service dependencies
**Areas for Architect Investigation:** Territory boundary storage format, caching strategies for map data, offline functionality implementation

### Recommendations

1. **Performance Specifications:** Define specific performance targets (e.g., "Map loads within 3 seconds" → "Map loads within 2 seconds on 3G connection")
2. **Territory Boundaries:** Clarify whether boundaries will be manually drawn, imported from files, or derived from address data
3. **Geocoding Service:** Specify which geocoding service will be used for address-to-coordinate conversion
4. **Error Handling:** Add more detailed error scenarios for Excel import failures

### Final Decision

**READY FOR ARCHITECT**: The PRD is comprehensive, properly structured, and ready for architectural design. The minor gaps identified are not blockers and can be addressed during the architecture phase.

## Next Steps

### UX Expert Prompt

Review the Territories Management PRD and create detailed UI/UX specifications with two distinct interface requirements:

1. **Admin Interface**: Design dedicated "Territorios" admin card and comprehensive territory management dashboard separate from Field Service admin
2. **Member Interface**: Create pixel-perfect alignment with existing Field Service UI patterns (reference screenshots 1.jpg through 8.jpg in \CORAL OESTE APP\Field Service\). The member territory interface must follow the exact visual hierarchy, card layouts, navigation patterns, mobile optimization, and interaction paradigms established in the Field Service section.

Focus on territory visualization patterns, mobile-first design for field service use, and seamless integration with the existing Hermanos App design system while maintaining strict consistency with Field Service UI patterns for member-facing features.

### Architect Prompt

Design the technical architecture for the Territories Management system based on this PRD. Focus on integrating with the existing Next.js/PostgreSQL/Prisma stack, MapLibre integration approach, Excel import processing architecture, and territory data modeling. Address the technical assumptions and provide detailed implementation guidance for the development team.
