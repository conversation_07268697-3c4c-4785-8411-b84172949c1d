'use client';

import React, { useEffect, useState } from 'react';
import SimpleTerritoryMap from '@/components/territories/shared/SimpleTerritoryMap';
import type { Territory } from '@/types/territories/map';

export default function DebugTerritoryMapPage() {
  const [territory, setTerritory] = useState<Territory | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    const fetchTerritory = async () => {
      try {
        // Get auth token from localStorage
        const token = localStorage.getItem('hermanos_token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        console.log('🔍 Fetching territory data...');

        // Fetch Territory 003 data (from our earlier test)
        const response = await fetch('/api/territories/cmdjwgwzb0001jk0xm9rqeni1', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch territory: ${response.statusText}`);
        }

        const territoryData = await response.json();
        console.log('🗺️ Raw territory data received:', territoryData);

        // Set debug info
        setDebugInfo({
          hasCoordinates: !!territoryData.coordinates,
          hasBoundary: !!territoryData.boundary,
          boundaryType: territoryData.boundary?.type,
          coordinateCount: territoryData.boundary?.coordinates?.[0]?.length,
          centerCoords: territoryData.coordinates,
          firstBoundaryPoint: territoryData.boundary?.coordinates?.[0]?.[0]
        });

        // Convert to map territory format
        const mapTerritory: Territory = {
          id: territoryData.id,
          territoryNumber: territoryData.territoryNumber,
          address: territoryData.address,
          status: territoryData.status,
          coordinates: territoryData.coordinates,
          boundary: territoryData.boundary
        };

        console.log('🗺️ Map territory data:', mapTerritory);
        console.log('🔍 Boundary data:', mapTerritory.boundary);
        
        setTerritory(mapTerritory);
      } catch (err) {
        console.error('❌ Error fetching territory:', err);
        setError(err instanceof Error ? err.message : 'Failed to load territory');
      } finally {
        setLoading(false);
      }
    };

    fetchTerritory();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <div className="text-sm text-gray-600">Cargando territorio...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-2">❌</div>
          <div className="text-sm text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  if (!territory) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600 mb-2">📍</div>
          <div className="text-sm text-gray-600">No territory data found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-4 py-3">
          <h1 className="text-xl font-semibold text-gray-900">
            🐛 Debug Territory Map - Territory {territory.territoryNumber}
          </h1>
          <p className="text-sm text-gray-500 mt-1">
            Debugging boundary visualization for {territory.address.split('\n')[0]}
          </p>
        </div>
      </div>

      {/* Debug Info */}
      <div className="bg-blue-50 border-b border-blue-200 px-4 py-3">
        <div className="text-sm">
          <div className="font-medium text-blue-900 mb-2">🔍 Debug Information:</div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-800">
            <div>
              <span className="font-medium">Has Coordinates:</span> {debugInfo?.hasCoordinates ? '✅ Yes' : '❌ No'}
            </div>
            <div>
              <span className="font-medium">Has Boundary:</span> {debugInfo?.hasBoundary ? '✅ Yes' : '❌ No'}
            </div>
            <div>
              <span className="font-medium">Boundary Type:</span> {debugInfo?.boundaryType || 'N/A'}
            </div>
            <div>
              <span className="font-medium">Coordinate Points:</span> {debugInfo?.coordinateCount || 'N/A'}
            </div>
            {debugInfo?.centerCoords && (
              <div className="md:col-span-2">
                <span className="font-medium">Center:</span> [{debugInfo.centerCoords.longitude.toFixed(4)}, {debugInfo.centerCoords.latitude.toFixed(4)}]
              </div>
            )}
            {debugInfo?.firstBoundaryPoint && (
              <div className="md:col-span-2">
                <span className="font-medium">First Boundary Point:</span> [{debugInfo.firstBoundaryPoint[0].toFixed(4)}, {debugInfo.firstBoundaryPoint[1].toFixed(4)}]
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-3">
        <div className="text-sm">
          <div className="font-medium text-yellow-900 mb-2">🎯 What to Look For:</div>
          <ul className="text-yellow-800 space-y-1">
            <li>• Check browser console for MapLibre GL errors</li>
            <li>• Look for colored polygon boundaries on the map</li>
            <li>• Verify the map centers on the territory area</li>
            <li>• Check if territory marker appears</li>
            <li>• Open browser dev tools and check Network tab for failed requests</li>
          </ul>
        </div>
      </div>

      {/* Map */}
      <div className="p-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">🗺️ Territory Map</h2>
            <p className="text-sm text-gray-500">
              The map should display the territory boundary as a colored polygon with stroke
            </p>
          </div>
          <div className="h-96">
            <SimpleTerritoryMap
              territories={[territory]}
              height="100%"
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Console Instructions */}
      <div className="p-4">
        <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
          <h3 className="font-medium text-gray-900 mb-2">🔧 Debugging Steps:</h3>
          <ol className="text-sm text-gray-700 space-y-1">
            <li>1. Open browser Developer Tools (F12)</li>
            <li>2. Go to Console tab and look for map-related errors</li>
            <li>3. Check Network tab for failed resource requests</li>
            <li>4. Look for messages starting with 🗺️, ✅, or ❌</li>
            <li>5. Verify if "Map loaded successfully" appears</li>
            <li>6. Check if "Added boundary for territory" messages appear</li>
          </ol>
        </div>
      </div>

      {/* Raw Data */}
      <div className="p-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">📄 Raw Territory Data</h2>
          </div>
          <div className="p-4">
            <pre className="text-xs bg-gray-50 p-3 rounded overflow-auto max-h-64">
              {JSON.stringify(territory, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
