/**
 * Diagnose Territories 002 and 003 Excel Structure
 * 
 * Examines the Excel files to understand the structure and fix parsing issues
 */

const XLSX = require('xlsx');
const path = require('path');

async function diagnoseTerritory(territoryNumber) {
  try {
    console.log(`\n🔍 Diagnosing Territory ${territoryNumber}...`);
    
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${territoryNumber}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    
    console.log(`📊 Available sheets: ${workbook.SheetNames.join(', ')}`);
    
    // Try to find the correct sheet
    const possibleSheetNames = [
      `Terr ${territoryNumber}`,
      `Terr. ${territoryNumber}`,
      `Terr  ${territoryNumber}`,
      `Territory ${territoryNumber}`,
      `T${territoryNumber}`,
      workbook.SheetNames[0]
    ];
    
    let worksheet = null;
    let sheetName = '';
    
    for (const name of possibleSheetNames) {
      if (workbook.Sheets[name]) {
        worksheet = workbook.Sheets[name];
        sheetName = name;
        break;
      }
    }
    
    console.log(`📋 Using sheet: ${sheetName}`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Total rows: ${excelData.length}`);
    
    // Show first 30 rows to understand structure
    console.log(`\n📋 First 30 rows of Territory ${territoryNumber}:`);
    for (let i = 0; i < Math.min(30, excelData.length); i++) {
      const row = excelData[i];
      if (row && row.length > 0) {
        console.log(`Row ${i + 1}:`, row.slice(0, 8)); // Show first 8 columns
      }
    }
    
    // Analyze column patterns
    console.log(`\n🔍 Column Analysis for Territory ${territoryNumber}:`);
    const columnPatterns = {};
    
    for (let i = 0; i < Math.min(50, excelData.length); i++) {
      const row = excelData[i];
      if (row && row.length > 0) {
        for (let col = 0; col < Math.min(8, row.length); col++) {
          const cell = row[col];
          if (cell !== undefined && cell !== null && cell !== '') {
            if (!columnPatterns[col]) columnPatterns[col] = [];
            columnPatterns[col].push({
              row: i + 1,
              value: cell,
              type: typeof cell
            });
          }
        }
      }
    }
    
    // Show column patterns
    for (let col = 0; col < 8; col++) {
      if (columnPatterns[col]) {
        console.log(`\nColumn ${String.fromCharCode(65 + col)} (${col}):`, 
          columnPatterns[col].slice(0, 10).map(item => 
            `Row ${item.row}: ${item.value} (${item.type})`
          ).join(', ')
        );
      }
    }
    
  } catch (error) {
    console.error(`❌ Error diagnosing Territory ${territoryNumber}:`, error.message);
  }
}

async function diagnoseMultipleTerritories() {
  console.log('🔍 Starting diagnosis of Territories 002 and 003...');
  
  await diagnoseTerritory('002');
  await diagnoseTerritory('003');
  
  console.log('\n✅ Diagnosis completed!');
}

diagnoseMultipleTerritories();
