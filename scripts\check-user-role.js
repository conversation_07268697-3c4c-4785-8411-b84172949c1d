const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUserRole() {
  try {
    console.log('Checking user roles in the database...');

    // Get all members for congregation 1441
    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441'
      },
      select: {
        id: true,
        name: true,
        role: true,
        isActive: true
      }
    });

    console.log('\nMembers in congregation 1441:');
    members.forEach(member => {
      console.log(`- ${member.name}: ${member.role} (Active: ${member.isActive})`);
    });

    // Check if there are any elders or coordinators
    const adminUsers = members.filter(m => 
      ['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(m.role)
    );

    console.log('\nUsers with admin permissions:');
    if (adminUsers.length === 0) {
      console.log('No users with admin permissions found!');
      console.log('Creating a developer user for testing...');
      
      // Create a developer user
      const devUser = await prisma.member.create({
        data: {
          congregationId: '1441',
          name: '<PERSON><PERSON>per User',
          role: 'developer',
          pin: '1234',
          isActive: true
        }
      });
      
      console.log(`Created developer user: ${devUser.name} (ID: ${devUser.id})`);
    } else {
      adminUsers.forEach(user => {
        console.log(`- ${user.name}: ${user.role}`);
      });
    }

  } catch (error) {
    console.error('Error checking user roles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUserRole();
