/**
 * Member Territory API Endpoint
 * 
 * Provides territory information for authenticated members,
 * following Field Service API patterns and data structure.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/territories/my-territories
 * Get territories assigned to the authenticated member
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { 
        id: true,
        name: true,
        role: true,
        congregationId: true 
      }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Get member's territory assignments
    const assignments = await prisma.territoryAssignment.findMany({
      where: {
        memberId: member.id,
        congregationId: member.congregationId,
        status: 'active'
      },
      include: {
        territory: {
          select: {
            id: true,
            territoryNumber: true,
            address: true,
            status: true,
            notes: true,
            boundaries: true
          }
        },
        assignedByMember: {
          select: {
            name: true,
            role: true
          }
        },
        visits: {
          orderBy: {
            visitDate: 'desc'
          },
          take: 3 // Last 3 visits for summary
        }
      },
      orderBy: {
        assignedAt: 'desc'
      }
    });

    // Calculate assignment statistics
    const territoriesWithStats = assignments.map(assignment => {
      const assignedDate = new Date(assignment.assignedAt);
      const daysAssigned = Math.ceil((new Date().getTime() - assignedDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Calculate completion percentage based on visits
      const totalVisits = assignment.visitCount || 0;
      const completedVisits = assignment.visits.filter(v => v.isCompleted).length;
      const progressPercentage = totalVisits > 0 ? Math.round((completedVisits / totalVisits) * 100) : 0;

      return {
        id: assignment.id,
        territory: assignment.territory,
        assignedAt: assignment.assignedAt,
        assignedBy: assignment.assignedByMember,
        daysAssigned,
        visitCount: assignment.visitCount || 0,
        isPartiallyCompleted: assignment.isPartiallyCompleted,
        partialCompletionNotes: assignment.partialCompletionNotes,
        progressPercentage,
        recentVisits: assignment.visits,
        status: assignment.status
      };
    });

    // Get congregation information for context
    const congregation = await prisma.congregation.findUnique({
      where: { id: member.congregationId },
      select: { name: true }
    });

    return NextResponse.json({
      success: true,
      data: {
        member: {
          id: member.id,
          name: member.name,
          role: member.role
        },
        congregation: congregation,
        territories: territoriesWithStats,
        summary: {
          totalAssigned: territoriesWithStats.length,
          inProgress: territoriesWithStats.filter(t => t.visitCount > 0 && !t.isPartiallyCompleted).length,
          partiallyCompleted: territoriesWithStats.filter(t => t.isPartiallyCompleted).length,
          averageDaysAssigned: territoriesWithStats.length > 0 
            ? Math.round(territoriesWithStats.reduce((sum, t) => sum + t.daysAssigned, 0) / territoriesWithStats.length)
            : 0
        }
      }
    });

  } catch (error) {
    console.error('Member territories get error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
