import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

/**
 * POST /api/admin/settings/reset-congregation-pin
 * Reset congregation PIN to a new random value
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'overseer_coordinator', 'coordinator', 'developer'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to reset congregation PIN' },
        { status: 403 }
      );
    }

    // Generate new 4-digit PIN
    const newPin = Math.floor(1000 + Math.random() * 9000).toString();

    // Update congregation PIN in settings
    await prisma.congregationSetting.upsert({
      where: {
        congregationId_settingKey: {
          congregationId: user.congregationId,
          settingKey: 'congregation_pin',
        },
      },
      update: {
        settingValue: newPin,
      },
      create: {
        congregationId: user.congregationId,
        settingKey: 'congregation_pin',
        settingValue: newPin,
      },
    });

    // Also update the default congregation PIN
    await prisma.congregationSetting.upsert({
      where: {
        congregationId_settingKey: {
          congregationId: user.congregationId,
          settingKey: 'default_congregation_pin',
        },
      },
      update: {
        settingValue: newPin,
      },
      create: {
        congregationId: user.congregationId,
        settingKey: 'default_congregation_pin',
        settingValue: newPin,
      },
    });

    // Log the PIN reset action for audit trail (if audit table exists)
    try {
      const ipAddress = request.headers.get('x-forwarded-for') ||
                       request.headers.get('x-real-ip') ||
                       'unknown';
      const userAgent = request.headers.get('user-agent') || 'unknown';

      // Note: We'll skip audit logging for now since the table might not exist
      console.log('PIN Reset Action:', {
        userId: user.userId,
        action: 'RESET_CONGREGATION_PIN',
        congregationId: user.congregationId,
        newPin: newPin,
        resetBy: user.name,
        ipAddress,
        userAgent,
        timestamp: new Date().toISOString()
      });
    } catch (auditError) {
      console.warn('Failed to log audit trail:', auditError);
      // Continue execution even if audit logging fails
    }

    return NextResponse.json({
      success: true,
      message: 'Congregation PIN reset successfully',
      newPin: newPin
    });

  } catch (error) {
    console.error('Error resetting congregation PIN:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
