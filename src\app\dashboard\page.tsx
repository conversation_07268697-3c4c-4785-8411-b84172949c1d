'use client';

/**
 * Dashboard Page for Hermanos App
 *
 * Main dashboard with exact UI preservation, conditional admin access,
 * and Spanish-first interface. Integrates with authentication system
 * and provides role-based navigation.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import DashboardGrid from '@/components/dashboard/DashboardGrid';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface Congregation {
  id: string;
  name: string;
  language: string;
  timezone: string;
}

interface Permissions {
  canAccessAdmin: boolean;
  canManageSettings: boolean;
  canExtendToken?: boolean;
}

interface DashboardData {
  pendingTasks: number;
  upcomingMeetings: number;
  currentMonthHours: number;
  lastServiceMonth: Date | null;
  totalMembers?: number | null;
  totalTasks?: number | null;
}

export default function DashboardPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [congregation, setCongregation] = useState<Congregation | null>(null);
  const [permissions, setPermissions] = useState<Permissions | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Load dashboard data on page load
    loadDashboardData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('hermanos_token');
    localStorage.removeItem('hermanos_user');
    localStorage.removeItem('hermanos_congregation');
    localStorage.removeItem('hermanos_permissions');
    router.push('/login');
  };

  const handleNavigation = (section: string) => {
    // Navigate to different sections
    switch (section) {
      case 'admin':
        router.push('/admin');
        break;
      case 'field-service':
        router.push('/field-service');
        break;
      case 'meetings':
        // TODO: Implement meetings page
        alert('Navegando a: Reuniones (próximamente)');
        break;
      case 'assignments':
        router.push('/assignments');
        break;
      case 'tasks':
        router.push('/tasks');
        break;
      case 'letters':
        router.push('/letters');
        break;
      case 'events':
        router.push('/events');
        break;
      case 'analytics':
        router.push('/analytics');
        break;
      default:
        alert(`Navegando a: ${section}`);
    }
  };

  const loadDashboardData = async () => {
    try {
      setError(null);
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      // Fetch dashboard data with authentication
      const response = await fetch('/api/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        setCongregation(data.congregation);
        setPermissions(data.permissions);
        setDashboardData(data.dashboardData);

        // Update stored data
        localStorage.setItem('hermanos_user', JSON.stringify(data.user));
        localStorage.setItem('hermanos_congregation', JSON.stringify(data.congregation));
        localStorage.setItem('hermanos_permissions', JSON.stringify(data.permissions));
      } else {
        // Token is invalid or expired, redirect to login
        handleLogout();
      }
    } catch (error) {
      console.error('Dashboard data loading failed:', error);
      setError('Error al cargar los datos del panel. Por favor, intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };



  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando panel de control...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-700 mb-4">{error}</p>
            <button
              onClick={loadDashboardData}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Intentar de nuevo
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Redirect if no user data
  if (!user || !congregation || !permissions || !dashboardData) {
    return null;
  }

  return (
    <DashboardLayout
      user={user}
      congregation={congregation}
      onLogout={handleLogout}
    >
      <DashboardGrid
        dashboardData={dashboardData}
        permissions={permissions}
        onNavigate={handleNavigation}
      />
    </DashboardLayout>
  );
}
