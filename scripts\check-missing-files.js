/**
 * Check for missing PDF files
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const prisma = new PrismaClient();

async function checkMissingFiles() {
  try {
    const letters = await prisma.letter.findMany({
      where: { isActive: true },
      select: { filename: true, filePath: true, title: true }
    });
    
    console.log('📄 Checking for missing PDF files...');
    
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'letters');
    let missing = 0;
    
    for (const letter of letters) {
      const filePath = path.join(uploadsDir, letter.filename);
      if (!fs.existsSync(filePath)) {
        console.log(`❌ Missing: ${letter.filename}`);
        console.log(`   Title: ${letter.title}`);
        missing++;
      }
    }
    
    if (missing === 0) {
      console.log('✅ All PDF files exist');
    } else {
      console.log(`❌ Found ${missing} missing files`);
    }
    
    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    await prisma.$disconnect();
    process.exit(1);
  }
}

checkMissingFiles();
