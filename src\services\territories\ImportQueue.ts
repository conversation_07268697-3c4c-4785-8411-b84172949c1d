// Territory Import Queue Service
// Manages bulk territory import processing with job queues and progress tracking

import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { ExcelImportService } from './ImportService';
import {
  ImportJob,
  QueueStatus,
  BulkImportBatch,
  BulkImportFile,
  BulkImportProgress,
  BulkProcessingConfig,
  DEFAULT_BULK_CONFIG
} from '@/types/territories/import';

export class ImportQueue extends EventEmitter {
  private static instance: ImportQueue;
  private jobs: Map<string, ImportJob> = new Map();
  private batches: Map<string, BulkImportBatch> = new Map();
  private processing: Set<string> = new Set();
  private config: BulkProcessingConfig;

  private constructor(config: BulkProcessingConfig = DEFAULT_BULK_CONFIG) {
    super();
    this.config = config;
    this.startProcessing();
  }

  static getInstance(config?: BulkProcessingConfig): ImportQueue {
    if (!ImportQueue.instance) {
      ImportQueue.instance = new ImportQueue(config);
    }
    return ImportQueue.instance;
  }

  /**
   * Add a batch of files to the import queue
   */
  async addBatch(
    files: BulkImportFile[],
    congregationId: string,
    batchId?: string
  ): Promise<string> {
    const id = batchId || uuidv4();
    
    const batch: BulkImportBatch = {
      id,
      congregationId,
      files,
      status: 'pending',
      totalFiles: files.length,
      completedFiles: 0,
      failedFiles: 0,
      successfulImports: 0,
      totalTerritories: 0,
      startTime: new Date(),
      overallProgress: 0
    };

    this.batches.set(id, batch);

    // Create jobs for each file
    for (const file of files) {
      const job: ImportJob = {
        id: uuidv4(),
        batchId: id,
        fileId: file.id,
        fileName: file.name,
        filePath: '', // Will be set when file is saved
        congregationId,
        status: 'queued',
        priority: 1,
        attempts: 0,
        maxAttempts: this.config.retryAttempts,
        createdAt: new Date()
      };

      this.jobs.set(job.id, job);
    }

    this.emit('batchAdded', { batchId: id, batch });
    return id;
  }

  /**
   * Get batch status and progress
   */
  getBatchProgress(batchId: string): BulkImportProgress | null {
    const batch = this.batches.get(batchId);
    if (!batch) return null;

    const batchJobs = Array.from(this.jobs.values()).filter(job => job.batchId === batchId);
    const completedJobs = batchJobs.filter(job => job.status === 'completed');
    const failedJobs = batchJobs.filter(job => job.status === 'failed');
    const processingJobs = batchJobs.filter(job => job.status === 'processing');

    const overallProgress = batch.totalFiles > 0 
      ? Math.round((completedJobs.length / batch.totalFiles) * 100)
      : 0;

    const errors = failedJobs.map(job => `${job.fileName}: ${job.error || 'Unknown error'}`);

    // Estimate time remaining based on average processing time
    let estimatedTimeRemaining: number | undefined;
    if (processingJobs.length > 0 && completedJobs.length > 0) {
      const avgProcessingTime = completedJobs.reduce((sum, job) => {
        if (job.startedAt && job.completedAt) {
          return sum + (job.completedAt.getTime() - job.startedAt.getTime());
        }
        return sum;
      }, 0) / completedJobs.length;

      const remainingFiles = batch.totalFiles - completedJobs.length;
      estimatedTimeRemaining = avgProcessingTime * remainingFiles;
    }

    return {
      batchId,
      status: batch.status,
      overallProgress,
      currentFile: processingJobs[0]?.fileName,
      filesCompleted: completedJobs.length,
      totalFiles: batch.totalFiles,
      territoriesImported: batch.successfulImports,
      errors,
      estimatedTimeRemaining
    };
  }

  /**
   * Get queue status
   */
  getQueueStatus(): QueueStatus {
    const allJobs = Array.from(this.jobs.values());
    
    return {
      totalJobs: allJobs.length,
      queuedJobs: allJobs.filter(job => job.status === 'queued').length,
      processingJobs: allJobs.filter(job => job.status === 'processing').length,
      completedJobs: allJobs.filter(job => job.status === 'completed').length,
      failedJobs: allJobs.filter(job => job.status === 'failed').length,
      isProcessing: this.processing.size > 0
    };
  }

  /**
   * Process a single import job
   */
  private async processJob(job: ImportJob): Promise<void> {
    try {
      job.status = 'processing';
      job.startedAt = new Date();
      job.attempts++;

      this.processing.add(job.id);
      this.emit('jobStarted', { jobId: job.id, job });

      // Read file and process import
      const fs = require('fs').promises;
      const fileBuffer = await fs.readFile(job.filePath);
      
      // Parse and import territories
      const parseResult = await ExcelImportService.parseExcelFile(fileBuffer);
      
      if (parseResult.errors.length > 0 && parseResult.data.length === 0) {
        throw new Error(`Failed to parse Excel file: ${parseResult.errors[0].error}`);
      }

      const importResult = await ExcelImportService.importTerritories(
        parseResult.data,
        job.congregationId
      );

      // Update job with results
      job.status = 'completed';
      job.completedAt = new Date();
      job.result = importResult;

      // Update batch progress
      await this.updateBatchProgress(job.batchId, job);

      this.emit('jobCompleted', { jobId: job.id, job, result: importResult });

    } catch (error) {
      job.status = 'failed';
      job.completedAt = new Date();
      job.error = error instanceof Error ? error.message : 'Unknown error';

      // Update batch progress
      await this.updateBatchProgress(job.batchId, job);

      this.emit('jobFailed', { jobId: job.id, job, error: job.error });

      // Retry if attempts remaining
      if (job.attempts < job.maxAttempts) {
        setTimeout(() => {
          job.status = 'queued';
          job.error = undefined;
        }, 5000); // Retry after 5 seconds
      }

    } finally {
      this.processing.delete(job.id);
    }
  }

  /**
   * Update batch progress based on job completion
   */
  private async updateBatchProgress(batchId: string, completedJob: ImportJob): Promise<void> {
    const batch = this.batches.get(batchId);
    if (!batch) return;

    const batchJobs = Array.from(this.jobs.values()).filter(job => job.batchId === batchId);
    const completedJobs = batchJobs.filter(job => job.status === 'completed');
    const failedJobs = batchJobs.filter(job => job.status === 'failed');

    batch.completedFiles = completedJobs.length;
    batch.failedFiles = failedJobs.filter(job => job.attempts >= job.maxAttempts).length;
    
    // Calculate total successful imports
    batch.successfulImports = completedJobs.reduce((sum, job) => {
      return sum + (job.result?.successfulImports || 0);
    }, 0);

    batch.overallProgress = Math.round((batch.completedFiles / batch.totalFiles) * 100);

    // Update batch status
    if (batch.completedFiles + batch.failedFiles >= batch.totalFiles) {
      if (batch.failedFiles === 0) {
        batch.status = 'completed';
      } else if (batch.completedFiles === 0) {
        batch.status = 'failed';
      } else {
        batch.status = 'partial';
      }
      batch.endTime = new Date();
    } else {
      batch.status = 'processing';
    }

    this.emit('batchProgress', { batchId, batch });
  }

  /**
   * Start the queue processing loop
   */
  private startProcessing(): void {
    setInterval(() => {
      this.processNextJobs();
    }, 1000); // Check every second

    // Cleanup old batches
    setInterval(() => {
      this.cleanupOldBatches();
    }, 60 * 60 * 1000); // Check every hour
  }

  /**
   * Process next available jobs up to max concurrent limit
   */
  private async processNextJobs(): Promise<void> {
    if (this.processing.size >= this.config.maxConcurrentFiles) {
      return; // Already at max capacity
    }

    const queuedJobs = Array.from(this.jobs.values())
      .filter(job => job.status === 'queued')
      .sort((a, b) => b.priority - a.priority || a.createdAt.getTime() - b.createdAt.getTime());

    const jobsToProcess = queuedJobs.slice(0, this.config.maxConcurrentFiles - this.processing.size);

    for (const job of jobsToProcess) {
      this.processJob(job).catch(error => {
        console.error(`Error processing job ${job.id}:`, error);
      });
    }
  }

  /**
   * Clean up old completed batches
   */
  private cleanupOldBatches(): void {
    const cutoffTime = new Date(Date.now() - (this.config.cleanupAfterHours * 60 * 60 * 1000));

    for (const [batchId, batch] of this.batches.entries()) {
      if (batch.endTime && batch.endTime < cutoffTime) {
        // Remove batch and associated jobs
        const batchJobs = Array.from(this.jobs.values()).filter(job => job.batchId === batchId);
        for (const job of batchJobs) {
          this.jobs.delete(job.id);
        }
        this.batches.delete(batchId);
        
        this.emit('batchCleaned', { batchId });
      }
    }
  }

  /**
   * Retry failed jobs in a batch
   */
  async retryBatch(batchId: string): Promise<boolean> {
    const batch = this.batches.get(batchId);
    if (!batch) return false;

    const failedJobs = Array.from(this.jobs.values())
      .filter(job => job.batchId === batchId && job.status === 'failed');

    for (const job of failedJobs) {
      job.status = 'queued';
      job.attempts = 0;
      job.error = undefined;
    }

    batch.status = 'pending';
    this.emit('batchRetry', { batchId, batch });
    
    return true;
  }

  /**
   * Cancel a batch and all its jobs
   */
  async cancelBatch(batchId: string): Promise<boolean> {
    const batch = this.batches.get(batchId);
    if (!batch) return false;

    const batchJobs = Array.from(this.jobs.values()).filter(job => job.batchId === batchId);
    
    for (const job of batchJobs) {
      if (job.status === 'queued' || job.status === 'processing') {
        job.status = 'failed';
        job.error = 'Cancelled by user';
      }
    }

    batch.status = 'failed';
    batch.endTime = new Date();
    
    this.emit('batchCancelled', { batchId, batch });
    return true;
  }

  /**
   * Set file path for a job (called after file upload)
   */
  setJobFilePath(batchId: string, fileId: string, filePath: string): boolean {
    const job = Array.from(this.jobs.values())
      .find(j => j.batchId === batchId && j.fileId === fileId);
    
    if (job) {
      job.filePath = filePath;
      return true;
    }
    
    return false;
  }
}
