const { PrismaClient } = require('@prisma/client');

async function checkActualSchema() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking actual database schema...\n');
    
    // Check if territories table exists
    const tableExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'territories'
      )
    `;
    
    console.log('📋 Territories table exists:', tableExists[0].exists);
    
    if (tableExists[0].exists) {
      // Get all columns
      const columns = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'territories' 
        ORDER BY ordinal_position
      `;
      
      console.log('\n📊 Current territories table columns:');
      columns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
      });
      
      // Get sample data
      console.log('\n📋 Sample data (first 2 rows):');
      const sampleData = await prisma.$queryRaw`SELECT * FROM territories LIMIT 2`;
      console.log(JSON.stringify(sampleData, null, 2));
      
      // Check what our Prisma model expects vs what exists
      const expectedColumns = ['territory_number', 'address', 'notes'];
      const existingColumns = columns.map(col => col.column_name);
      
      console.log('\n🎯 Column comparison:');
      expectedColumns.forEach(col => {
        if (existingColumns.includes(col)) {
          console.log(`  ✅ ${col} - EXISTS`);
        } else {
          console.log(`  ❌ ${col} - MISSING`);
        }
      });
      
      // Check for old columns that might need migration
      const oldColumns = ['name', 'description'];
      console.log('\n🔄 Old columns that might need migration:');
      oldColumns.forEach(col => {
        if (existingColumns.includes(col)) {
          console.log(`  📦 ${col} - EXISTS (needs migration)`);
        } else {
          console.log(`  ❌ ${col} - NOT FOUND`);
        }
      });
      
    } else {
      console.log('❌ Territories table does not exist!');
    }
    
  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkActualSchema();
