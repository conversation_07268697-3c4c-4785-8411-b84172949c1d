/**
 * Bulk Import Results API Endpoint
 * 
 * Provides detailed results for completed bulk territory imports.
 * Includes file-by-file results, error details, and summary statistics.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { BulkImportService } from '@/services/territories/BulkImportService';

interface RouteParams {
  params: {
    batchId: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for bulk import results' },
        { status: 403 }
      );
    }

    const { batchId } = params;

    // Validate batch ID
    if (!batchId || batchId.trim().length === 0) {
      return NextResponse.json(
        { error: 'Batch ID is required' },
        { status: 400 }
      );
    }

    // Initialize bulk import service
    BulkImportService.initialize();

    // Get batch results
    const results = await BulkImportService.getBulkImportResults(batchId);
    
    if (!results) {
      return NextResponse.json(
        { error: 'Batch not found or results not available' },
        { status: 404 }
      );
    }

    // Get current progress for additional context
    const progress = BulkImportService.getBulkImportProgress(batchId);

    return NextResponse.json({
      success: true,
      batchId,
      results,
      progress,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Bulk import results GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get batch results',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve results.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve results.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve results.' },
    { status: 405 }
  );
}
