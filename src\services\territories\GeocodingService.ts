import type { 
  GeocodingService as IGeocodingService, 
  GeocodingResult, 
  Coordinates 
} from '@/types/territories/map';

class GeocodingService implements IGeocodingService {
  private baseUrl = 'https://nominatim.openstreetmap.org';
  private cache = new Map<string, GeocodingResult>();
  private requestQueue: Array<() => Promise<void>> = [];
  private isProcessingQueue = false;
  private lastRequestTime = 0;
  private minRequestInterval = 1000; // 1 second between requests (Nominatim rate limit)

  /**
   * Geocode a single address
   */
  async geocodeAddress(address: string): Promise<GeocodingResult | null> {
    const cacheKey = `geocode:${address.toLowerCase()}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey) || null;
    }

    try {
      const result = await this.queueRequest(() => this.performGeocode(address));
      
      if (result) {
        this.cache.set(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      console.error('Geocoding error for address:', address, error);
      return null;
    }
  }

  /**
   * Geocode multiple addresses with rate limiting
   */
  async geocodeAddresses(addresses: string[]): Promise<(GeocodingResult | null)[]> {
    const results: (GeocodingResult | null)[] = [];
    
    for (const address of addresses) {
      const result = await this.geocodeAddress(address);
      results.push(result);
    }
    
    return results;
  }

  /**
   * Reverse geocode coordinates to address
   */
  async reverseGeocode(coordinates: Coordinates): Promise<GeocodingResult | null> {
    const cacheKey = `reverse:${coordinates.latitude},${coordinates.longitude}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey) || null;
    }

    try {
      const result = await this.queueRequest(() => this.performReverseGeocode(coordinates));
      
      if (result) {
        this.cache.set(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      console.error('Reverse geocoding error for coordinates:', coordinates, error);
      return null;
    }
  }

  /**
   * Perform actual geocoding request
   */
  private async performGeocode(address: string): Promise<GeocodingResult | null> {
    const params = new URLSearchParams({
      q: address,
      format: 'json',
      addressdetails: '1',
      limit: '1',
      countrycodes: 'us', // Limit to US addresses
      bounded: '1',
      viewbox: '-80.4,25.6,-80.1,25.9' // Miami area bounding box
    });

    const url = `${this.baseUrl}/search?${params}`;
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Hermanos Territory Management App'
      }
    });

    if (!response.ok) {
      throw new Error(`Geocoding request failed: ${response.status}`);
    }

    const data = await response.json();
    
    if (!Array.isArray(data) || data.length === 0) {
      return null;
    }

    const result = data[0];
    
    return {
      latitude: parseFloat(result.lat),
      longitude: parseFloat(result.lon),
      display_name: result.display_name,
      address: {
        house_number: result.address?.house_number,
        road: result.address?.road,
        city: result.address?.city || result.address?.town || result.address?.village,
        state: result.address?.state,
        postcode: result.address?.postcode,
        country: result.address?.country
      },
      boundingbox: result.boundingbox
    };
  }

  /**
   * Perform actual reverse geocoding request
   */
  private async performReverseGeocode(coordinates: Coordinates): Promise<GeocodingResult | null> {
    const params = new URLSearchParams({
      lat: coordinates.latitude.toString(),
      lon: coordinates.longitude.toString(),
      format: 'json',
      addressdetails: '1',
      zoom: '18'
    });

    const url = `${this.baseUrl}/reverse?${params}`;
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Hermanos Territory Management App'
      }
    });

    if (!response.ok) {
      throw new Error(`Reverse geocoding request failed: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result || result.error) {
      return null;
    }

    return {
      latitude: parseFloat(result.lat),
      longitude: parseFloat(result.lon),
      display_name: result.display_name,
      address: {
        house_number: result.address?.house_number,
        road: result.address?.road,
        city: result.address?.city || result.address?.town || result.address?.village,
        state: result.address?.state,
        postcode: result.address?.postcode,
        country: result.address?.country
      },
      boundingbox: result.boundingbox
    };
  }

  /**
   * Queue request to respect rate limits
   */
  private async queueRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  /**
   * Process request queue with rate limiting
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;

      if (timeSinceLastRequest < this.minRequestInterval) {
        await new Promise(resolve => 
          setTimeout(resolve, this.minRequestInterval - timeSinceLastRequest)
        );
      }

      const request = this.requestQueue.shift();
      if (request) {
        this.lastRequestTime = Date.now();
        await request();
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Clear geocoding cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache size
   */
  getCacheSize(): number {
    return this.cache.size;
  }

  /**
   * Preload geocoding for territory addresses
   */
  async preloadTerritoryGeocoding(addresses: string[]): Promise<void> {
    console.log(`Preloading geocoding for ${addresses.length} addresses...`);
    
    const batchSize = 10;
    for (let i = 0; i < addresses.length; i += batchSize) {
      const batch = addresses.slice(i, i + batchSize);
      await this.geocodeAddresses(batch);
      
      // Progress logging
      const progress = Math.min(i + batchSize, addresses.length);
      console.log(`Geocoding progress: ${progress}/${addresses.length}`);
    }
    
    console.log('Geocoding preload completed');
  }

  /**
   * Validate if coordinates are in expected area (Miami)
   */
  validateCoordinates(coordinates: Coordinates): boolean {
    // Miami area bounds
    const miamiBounds = {
      north: 25.9,
      south: 25.6,
      east: -80.1,
      west: -80.4
    };

    return (
      coordinates.latitude >= miamiBounds.south &&
      coordinates.latitude <= miamiBounds.north &&
      coordinates.longitude >= miamiBounds.west &&
      coordinates.longitude <= miamiBounds.east
    );
  }
}

// Export singleton instance
export const geocodingService = new GeocodingService();
export default geocodingService;
