# Hermanos App Product Requirements Document (PRD)

## Goals and Background Context

### Goals

- Create comprehensive PRD and architecture documentation to build "Hermanos" app from scratch in a new directory
- Implement multi-congregation support leveraging existing database structure for scalable congregation management
- Expand multilingual support beyond Spanish/English to accommodate diverse congregation languages
- Preserve existing theme system for section color customization and visual identity management
- Replicate all current functionality based on app screenshots without adding new features
- Maintain existing security model without over-complication beyond current implementation
- Preserve and document the critical JW.org data fetching logic and URL patterns for meeting content integration
- Use Coral Oeste App as reference architecture for building the new "Hermanos" multi-congregation system
- Preserve exact UI structure with member view side for all users and administrative section for leadership roles

### Background Context

The "Hermanos" app represents an evolution from the successful Coral Oeste App, expanding from single-congregation management to a multi-congregation platform. The existing Coral Oeste system has proven its value in managing congregation activities, and the rebuild aims to scale this proven functionality across multiple congregations while modernizing the technology stack from MySQL/Node.js to PostgreSQL/Next.js.

This rebuild focuses on architectural modernization and multi-tenancy rather than feature expansion, ensuring the proven workflows remain intact while enabling broader deployment. The first congregation will be Coral Oeste Spanish with Spanish as the default language.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-24 | 1.0 | Initial PRD creation for "Hermanos" multi-congregation app | Product Manager |

## Requirements

### Functional Requirements

**FR1**: Simple JWT-based authentication system preserving existing congregation-based login with congregation ID and PIN, supporting role-based access control (Developer, Overseer/Coordinator, Elder, Ministerial Servant, Publisher) with 60-day mobile-friendly token expiration (configurable by developers/elders)

**FR2**: Single authentication system for all users with conditional administrative access - all users authenticate through the same login page, but elders, ministerial servants, and overseers see an additional "Administración" button on the dashboard that provides access to the administrative section based on their assigned role

**FR3**: Simple role-based permissions system preserving existing administrative workflows without over-complication, maintaining current delegation patterns

**FR4**: Member management system with complete CRUD operations, preserving existing member profile structure and PIN management workflows with bcrypt security

**FR5**: Meeting management system preserving all existing assignment logic and workflows, with improved data validation and congregation isolation

**FR6**: Field service management enabling members to track service time and administrators to manage territories and service groups within delegated authority

**FR7**: Midweek meeting management supporting Life and Ministry Meeting Workbook integration with preserved JW.org data fetching logic and improved assignment workflows

**FR8**: Weekend meeting management for public talks, Watchtower study assignments, and visiting speaker coordination with enhanced assignment capabilities

**FR9**: Task management system allowing delegated administrators to create and assign congregation tasks with proper authorization validation

**FR10**: Assignment management for tracking personal and congregation assignments with completion status and improved assignment logic

**FR11**: Letters management system supporting PDF upload, categorization, and controlled access with administrative delegation

**FR12**: Events management for creating and managing congregation events with delegated administrative control

**FR13**: Song management with multilingual support and preserved JW.org integration for dynamic song title retrieval

**FR14**: Multi-congregation architecture with PostgreSQL tenant isolation using congregation_id, supporting Coral Oeste Spanish as the first congregation with Spanish as default language

**FR15**: Preserved multilingual support for Spanish and English with foundation for additional languages using existing patterns

**FR16**: Exact preservation of existing theme and color system for section management and visual identity

**FR17**: PostgreSQL-based backup and restore functionality with automated daily backups and manual restore capabilities

**FR18**: Pixel-perfect responsive web design preserving existing mobile-first UI structure and touch optimization

**FR19**: Complete preservation of existing JW.org integration logic, URL patterns, caching mechanisms, and fallback strategies

**FR20**: Preserved administrative interface design with Next.js-based admin access for congregation leadership

### Non-Functional Requirements

**NFR1**: Application must support concurrent access by multiple congregations (estimated 50-100 users per congregation) without performance degradation

**NFR2**: Database operations must complete within 2 seconds for standard queries and 10 seconds for complex reports

**NFR3**: System must maintain 99.5% uptime during congregation meeting times and field service periods

**NFR4**: All user data must be encrypted in transit and at rest, with secure authentication token management

**NFR5**: Application must be fully responsive and functional on mobile devices with preserved touch-optimized interfaces

**NFR6**: System must support data export and import capabilities for congregation data portability

**NFR7**: Application must gracefully handle offline scenarios with appropriate user feedback and retry mechanisms

**NFR8**: Next.js application with TypeScript strict mode compliance for type safety and maintainability, using Prisma ORM for type-safe database operations

**NFR9**: Complete PostgreSQL migration preserving all 41 MySQL tables with zero data loss and maintained relationships using Prisma migrations

**NFR10**: System must maintain backward compatibility with existing data structures during migration period

## User Interface Design Goals

### Overall UX Vision

The Hermanos app must deliver a **pixel-perfect replication** of the existing Coral Oeste app interface while enabling multi-congregation support. The user experience should maintain zero learning curve for existing users, preserving the beloved mobile-first design with Spanish-first interface and JW-specific design elements. The vision emphasizes visual continuity and cultural context preservation while providing a robust, scalable foundation underneath.

### Key Interaction Paradigms

- **Mobile-First Touch Optimization**: Compact card-based dashboard with 2x3 grid on mobile, 3x3 on desktop
- **Role-Based Progressive Disclosure**: Single login for all users with conditional "Administración" button visibility based on role
- **Spanish-First Interface**: Preserve exact wording, terminology, and cultural context throughout
- **Ocean/Beach Theme**: Blue gradient headers with ocean background imagery maintaining congregation branding
- **Bottom Navigation**: Mobile-optimized navigation system for primary sections

### Core Screens and Views

- **Login Screen**: Three-field authentication (Region dropdown, Congregation ID, PIN) with blue gradient styling
- **Main Dashboard**: Compact cards with specific color coding, ocean background, blue header with congregation name
- **Field Service Management**: Time tracking interface, territory management, service reports
- **Meeting Management**: Midweek and weekend meeting coordination with JW.org integration
- **Administrative Dashboard**: Leadership tools accessible only to authorized roles
- **Letters Management**: PDF upload, categorization, and controlled access interface
- **Events Management**: Congregation event creation and calendar integration
- **Task Management**: Assignment system with service group support

### Accessibility: WCAG AA

The application will meet WCAG AA standards while maintaining the exact visual design, ensuring compatibility with screen readers and keyboard navigation without compromising the pixel-perfect requirement.

### Branding

**Critical Design Preservation Requirements:**
- Exact blue gradient color scheme (#1e40af to #3b82f6) for headers
- Ocean/beach background imagery with blue overlay
- Specific card color mapping for each section (blue for field service, green for meetings, etc.)
- "Salón Del Reino" branding in header
- JW-specific iconography and religious context preservation
- Spanish-first terminology and cultural elements

### Target Device and Platforms: Web Responsive

Web responsive design optimized for mobile devices first, with progressive enhancement for tablets and desktop. The application must maintain excellent mobile performance and touch optimization while being fully functional across all screen sizes.

## Technical Assumptions

### Repository Structure: Monorepo

The project will use a **monorepo structure** to manage the multi-congregation architecture efficiently. This allows for shared components, utilities, and types across different congregation instances while maintaining clear separation of concerns. The existing Next.js structure with src/app organization supports this approach well.

### Service Architecture

**Monolithic Next.js Application with Multi-Tenant Architecture**: The system will be built as a single Next.js application with PostgreSQL tenant isolation using congregation_id. This approach provides:

- **Simplified deployment and maintenance** compared to microservices
- **Shared codebase** for common functionality across congregations
- **Database-level tenant isolation** ensuring data security between congregations
- **Scalable architecture** that can handle multiple congregations efficiently

### Testing Requirements

**Unit + Integration Testing Strategy**: The application will implement comprehensive testing including:

- **Unit tests** for business logic and utility functions
- **Integration tests** for API endpoints and database operations
- **Component testing** for React components using React Testing Library
- **End-to-end testing** for critical user workflows
- **Manual testing convenience methods** for congregation-specific scenarios

**Critical testing focus areas**:
- Multi-congregation data isolation
- Role-based access control
- JW.org integration reliability
- Mobile responsiveness validation

### Additional Technical Assumptions and Requests

**Technology Stack Decisions**:
- **Next.js 14+** with App Router for modern React patterns and server-side rendering
- **TypeScript** in strict mode for type safety and maintainability
- **Prisma ORM** for type-safe database operations and migrations
- **PostgreSQL** for robust multi-tenant data management
- **Tailwind CSS** for utility-first styling that supports pixel-perfect UI replication
- **React Hook Form + Zod** for form validation and type safety
- **JWT** for authentication with bcrypt for password hashing

**Database Architecture**:
- **Tenant isolation** using congregation_id foreign keys
- **Preserved schema structure** from existing 41 MySQL tables
- **Automated daily backups** with manual restore capabilities
- **Connection pooling** for performance optimization

**Deployment and Infrastructure**:
- **Vercel deployment** (recommended for Next.js optimization)
- **Environment-based configuration** for different congregation setups
- **CDN integration** for static assets and performance
- **SSL/TLS encryption** for all data transmission

**Performance Requirements**:
- **2-second response time** for standard database queries
- **10-second limit** for complex reports and data exports
- **Mobile-first optimization** with lazy loading for non-critical resources
- **Offline-first considerations** with appropriate fallback mechanisms

**Security Assumptions**:
- **Data encryption** at rest and in transit
- **Role-based access control** with granular permissions
- **Audit logging** for administrative actions
- **GDPR compliance** considerations for member data

## Epic List

**Epic 1: Foundation & Authentication Infrastructure**
Establish project setup with Next.js/PostgreSQL architecture, implement pixel-perfect login system with three-field authentication, and create basic multi-congregation tenant isolation.

**Epic 2: Core Dashboard & Navigation**
Implement the main dashboard with exact UI replication including ocean background, blue headers, color-coded cards, and role-based "Administración" button visibility.

**Epic 3: Member Management & Administrative Foundation**
Create comprehensive member management system with CRUD operations, role assignment, and administrative interface foundation for congregation leadership.

**Epic 4: Meeting Management System**
Implement midweek and weekend meeting management with JW.org integration, assignment tracking, and meeting location management (Kingdom Hall/Zoom).

**Epic 5: Field Service & Task Management**
Build field service tracking system and task management with assignment capabilities, service group support, and administrative delegation.

**Epic 6: Communication & Document Management**
Implement letters management system with PDF upload/categorization and events management for congregation activities.

## Epic Details

### Epic 1: Foundation & Authentication Infrastructure

**Epic Goal**: Establish the foundational Next.js/PostgreSQL architecture with multi-congregation support and implement pixel-perfect login system with three-field authentication. This epic delivers a working authentication system while setting up the technical foundation for all subsequent development.

#### Story 1.1: Project Setup and Infrastructure

As a **developer**,
I want **to set up the Next.js project with PostgreSQL and Prisma ORM**,
so that **we have a solid foundation for multi-congregation architecture**.

**Acceptance Criteria:**
1. Next.js 14+ project initialized with TypeScript in strict mode
2. Prisma ORM configured with PostgreSQL connection
3. Database schema migrated from existing 41 MySQL tables to PostgreSQL
4. Environment configuration setup for multiple environments
5. Basic CI/CD pipeline established
6. Health check endpoint returns successful response
7. Multi-tenant architecture foundation with congregation_id isolation implemented

#### Story 1.2: Pixel-Perfect Login Screen Implementation

As a **congregation member**,
I want **to log in using the exact three-field interface (Region, Congregation ID, PIN)**,
so that **I can access the system with the familiar interface I'm used to**.

**Acceptance Criteria:**
1. Login screen matches original design pixel-perfect including blue gradient background
2. Three input fields implemented: Region dropdown, Congregation ID text input, PIN password input
3. "Connect" button styled exactly as original with proper hover states
4. Form validation prevents submission with empty fields
5. Error messages display in Spanish with appropriate styling
6. Mobile responsive design maintains exact proportions and touch targets
7. Loading states implemented during authentication process

#### Story 1.3: JWT Authentication and Role-Based Access

As a **system administrator**,
I want **secure JWT-based authentication with role-based access control**,
so that **different user types have appropriate access levels**.

**Acceptance Criteria:**
1. JWT tokens generated with 60-day expiration (configurable)
2. Role-based access control supports: Developer, Overseer/Coordinator, Elder, Ministerial Servant, Publisher
3. Token validation middleware protects all authenticated routes
4. Congregation isolation enforced at database level using congregation_id
5. PIN authentication uses bcrypt hashing for security
6. Session management handles token refresh appropriately
7. Logout functionality clears tokens and redirects to login

### Epic 2: Core Dashboard & Navigation

**Epic Goal**: Implement the main dashboard with exact UI replication including ocean background, blue headers, color-coded cards, and role-based administrative access. This epic delivers the central hub that users interact with daily.

#### Story 2.1: Main Dashboard Layout and Styling

As a **congregation member**,
I want **to see the main dashboard with the exact visual design I'm familiar with**,
so that **I can navigate to different sections without any learning curve**.

**Acceptance Criteria:**
1. Ocean/beach background image with blue overlay implemented
2. Blue gradient header with congregation name displayed
3. Card grid layout: 2x3 on mobile, 3x3 on desktop
4. Each card sized to 120px height maximum with 12px border radius
5. Icons sized to 32px x 32px with proper spacing
6. Typography matches original: 16px bold titles, 12px regular descriptions
7. Responsive design maintains proportions across all screen sizes

#### Story 2.2: Dashboard Cards with Section Color Coding

As a **congregation member**,
I want **each dashboard section to have its specific color coding**,
so that **I can quickly identify and access different areas**.

**Acceptance Criteria:**
1. Field Service card: Light blue background (#dbeafe), blue icon (#2563eb)
2. Meetings card: Light green background (#dcfce7), green icon (#16a34a)
3. Assignments card: Light purple background (#f3e8ff), purple icon (#9333ea)
4. Tasks card: Light orange background (#fed7aa), orange icon (#ea580c)
5. Letters card: Light indigo background (#e0e7ff), indigo icon (#4f46e5)
6. Events card: Light pink background (#fce7f3), pink icon (#db2777)
7. Administration card: Light yellow background (#fef3c7), yellow icon (#d97706)

#### Story 2.3: Role-Based Administrative Access

As an **elder or ministerial servant**,
I want **to see the "Administración" button on my dashboard**,
so that **I can access administrative tools appropriate to my role**.

**Acceptance Criteria:**
1. "Administración" button visible only to roles: elder, ministerial_servant, overseer_coordinator, developer
2. Button styling matches other dashboard cards with yellow color scheme
3. Click navigation routes to administrative dashboard
4. Publishers and other roles do not see the administrative option
5. Role verification happens on both client and server side
6. Administrative access is properly secured with middleware
7. Graceful handling if user role changes during session

### Epic 3: Member Management & Administrative Foundation

**Epic Goal**: Create comprehensive member management system with CRUD operations, role assignment, and administrative interface foundation. This epic enables congregation leadership to manage their members effectively.

#### Story 3.1: Member Profile Management

As an **elder**,
I want **to create, view, edit, and manage member profiles**,
so that **I can maintain accurate congregation records**.

**Acceptance Criteria:**
1. Member creation form with all required fields (name, email, role, PIN)
2. Member list view with search and filtering capabilities
3. Member profile editing with validation and change tracking
4. Role assignment with proper permission validation
5. PIN management with bcrypt security and reset capabilities
6. Member deactivation/reactivation functionality
7. Audit trail for all member changes with timestamps and responsible user

#### Story 3.2: Administrative Dashboard Interface

As a **congregation administrator**,
I want **a clean administrative interface matching the original design**,
so that **I can efficiently manage congregation operations**.

**Acceptance Criteria:**
1. Administrative dashboard with header matching section color (yellow/gold #9e9d24)
2. Navigation menu with all administrative sections
3. Back button functionality to return to main dashboard
4. Responsive design optimized for both mobile and desktop use
5. Consistent styling with main application theme
6. Quick access to most common administrative tasks
7. Role-based menu items showing only accessible sections

### Epic 4: Meeting Management System

**Epic Goal**: Implement midweek and weekend meeting management with JW.org integration, assignment tracking, and meeting location management. This epic delivers core congregation functionality for meeting coordination.

#### Story 4.1: Midweek Meeting Management

As a **meeting overseer**,
I want **to manage midweek meetings with Life and Ministry Meeting Workbook integration**,
so that **I can coordinate meeting parts and assignments effectively**.

**Acceptance Criteria:**
1. Meeting creation with date, chairman, and prayer assignments
2. Meeting parts management with type categorization (treasures, digging, living)
3. Member assignment to meeting parts with conflict detection
4. JW.org workbook data integration with preserved fetching logic
5. Meeting location toggle (Kingdom Hall/Zoom) with Zoom details storage
6. Assignment history tracking and reporting
7. Mobile-optimized interface for quick updates

#### Story 4.2: Weekend Meeting Management

As a **meeting overseer**,
I want **to coordinate weekend meetings including public talks and Watchtower study**,
so that **I can manage speakers and assignments efficiently**.

**Acceptance Criteria:**
1. Public talk scheduling with speaker and congregation tracking
2. Visiting speaker management with contact information
3. Watchtower study conductor and reader assignments
4. Meeting part assignments with role-specific access
5. Meeting location management with Zoom integration
6. Assignment conflict detection and resolution
7. Reporting and export capabilities for meeting schedules

### Epic 5: Field Service & Task Management

**Epic Goal**: Build field service tracking system and task management with assignment capabilities, service group support, and administrative delegation. This epic enables members to track their ministry and administrators to coordinate congregation tasks.

#### Story 5.1: Field Service Time Tracking

As a **publisher**,
I want **to track my field service time and activities**,
so that **I can maintain accurate records for reporting**.

**Acceptance Criteria:**
1. Time entry form with date picker, hours/minutes, and activity type
2. Monthly service report generation with totals calculation
3. Service year tracking and historical data access
4. Notes and comments for service activities
5. Data validation and error handling for entries
6. Export functionality for personal records
7. Mobile-optimized interface for field use

#### Story 5.2: Task Assignment and Management

As a **service overseer**,
I want **to create and assign congregation tasks to members**,
so that **I can coordinate congregation activities efficiently**.

**Acceptance Criteria:**
1. Task creation with title, description, category, and frequency
2. Task assignment to specific members or service groups
3. Task completion tracking with status updates
4. Assignment history and reporting capabilities
5. Service group support for group-based assignments
6. Notification system for task assignments and deadlines
7. Administrative delegation with proper authorization

### Epic 6: Communication & Document Management

**Epic Goal**: Implement letters management system with PDF upload/categorization and events management for congregation activities. This epic completes the communication and document management capabilities.

#### Story 6.1: Letters and Document Management

As an **elder**,
I want **to upload, categorize, and manage congregation letters and documents**,
so that **members can access important communications**.

**Acceptance Criteria:**
1. PDF upload functionality with drag-and-drop interface
2. Document categorization and tagging system
3. Visibility controls (public/elders only) with role-based access
4. Search functionality with full-text search capabilities
5. Document versioning and approval workflow
6. Download tracking and access logging
7. Mobile-responsive document viewer

#### Story 6.2: Events Management and Calendar

As a **congregation secretary**,
I want **to create and manage congregation events**,
so that **members stay informed about important activities**.

**Acceptance Criteria:**
1. Event creation with date, time, location, and description
2. Event categorization and visibility controls
3. Calendar view with monthly and weekly layouts
4. Event notifications and reminders
5. Attendance tracking capabilities
6. Recurring event support with recurrence rules
7. Integration with meeting management system

## Checklist Results Report

### Executive Summary

- **Overall PRD Completeness**: 85%
- **MVP Scope Appropriateness**: Just Right
- **Readiness for Architecture Phase**: Nearly Ready
- **Most Critical Gaps**: Missing user research validation, limited problem quantification, and some technical guidance areas need strengthening

### Category Analysis Table

| Category                         | Status  | Critical Issues |
| -------------------------------- | ------- | --------------- |
| 1. Problem Definition & Context  | PARTIAL | Limited user research evidence, problem impact not quantified |
| 2. MVP Scope Definition          | PASS    | Well-defined scope with clear boundaries |
| 3. User Experience Requirements  | PASS    | Comprehensive UI/UX requirements with pixel-perfect preservation |
| 4. Functional Requirements       | PASS    | Complete functional requirements covering all major features |
| 5. Non-Functional Requirements   | PASS    | Thorough performance, security, and technical requirements |
| 6. Epic & Story Structure        | PASS    | Well-structured epics with detailed acceptance criteria |
| 7. Technical Guidance            | PARTIAL | Good technology stack decisions, needs more architecture guidance |
| 8. Cross-Functional Requirements | PARTIAL | Data requirements covered, integration details could be expanded |
| 9. Clarity & Communication       | PASS    | Clear documentation with consistent terminology |

### Top Issues by Priority

**BLOCKERS**: None - PRD is ready for architect to proceed

**HIGH**:
- Add quantified problem impact metrics where possible
- Include more specific user research validation
- Expand technical architecture guidance for complex areas

**MEDIUM**:
- Add more detailed integration requirements for JW.org
- Include specific data migration planning details
- Expand operational monitoring requirements

**LOW**:
- Add visual diagrams for user flows
- Include competitive analysis details
- Expand stakeholder communication plan

### Final Decision

**NEARLY READY FOR ARCHITECT**: The PRD provides a solid foundation for architectural design. The requirements are comprehensive and well-structured. The identified gaps are not blockers but would strengthen the overall product definition. The architect can proceed with confidence while addressing the medium-priority improvements in parallel.

**FR19**: Complete preservation of existing JW.org integration logic, URL patterns, caching mechanisms, and fallback strategies

**FR20**: Preserved administrative interface design with Next.js-based admin access for congregation leadership

### Non-Functional Requirements

**NFR1**: Application must support concurrent access by multiple congregations (estimated 50-100 users per congregation) without performance degradation

**NFR2**: Database operations must complete within 2 seconds for standard queries and 10 seconds for complex reports

**NFR3**: System must maintain 99.5% uptime during congregation meeting times and field service periods

**NFR4**: All user data must be encrypted in transit and at rest, with secure authentication token management

**NFR5**: Application must be fully responsive and functional on mobile devices with preserved touch-optimized interfaces

**NFR6**: System must support data export and import capabilities for congregation data portability

**NFR7**: Application must gracefully handle offline scenarios with appropriate user feedback and retry mechanisms

**NFR8**: Next.js application with TypeScript strict mode compliance for type safety and maintainability, using Prisma ORM for type-safe database operations

**NFR9**: Complete PostgreSQL migration preserving all 41 MySQL tables with zero data loss and maintained relationships using Prisma migrations

**NFR10**: Simple audit logging for administrative actions without over-complicating the security model

**NFR11**: Exact preservation of existing JW.org integration caching mechanisms, fallback strategies, and error handling patterns

**NFR12**: Preserved authorization patterns maintaining existing administrative workflows without additional complexity

**NFR13**: 12-week implementation timeline with incremental migration approach preserving system availability

**NFR14**: Self-hosted deployment capability using standard VPS infrastructure without cloud vendor dependencies

## Implementation Timeline

### 12-Week Phased Implementation Approach

The Hermanos app will be implemented using a carefully planned 12-week approach that prioritizes UI preservation and zero-downtime migration:

**Phase 1: Foundation (Weeks 1-3)**

- Next.js project setup with PostgreSQL and Prisma
- Complete migration of all 41 MySQL tables
- Simple JWT authentication preserving existing login

**Phase 2: Core UI (Weeks 4-6)**

- Pixel-perfect dashboard replication
- Navigation system preservation
- Member management with exact UI

**Phase 3: Meetings (Weeks 7-8)**

- Midweek and weekend meeting management
- Exact preservation of JW.org integration

**Phase 4: Activities (Weeks 9-10)**

- Task management system
- Field service reporting

**Phase 5: Communication & Deployment (Weeks 11-12)**

- Letters and events management
- Production deployment and data migration

### Success Metrics

- **UI Accuracy**: 100% visual similarity to existing app
- **Data Integrity**: Zero data loss during migration
- **Performance**: Page loads under 2 seconds on mobile
- **User Adoption**: 90% of congregation using within 30 days

## Development Standards and Guidelines

### **Critical Development Requirements**

**Environment Configuration Standards:**

- All configuration variables must be defined in `.env` file as single source of truth
- Never hardcode localhost, ports, URLs, or any environment-specific values in codebase files
- Always use default ports specified in `.env` file for all development and production environments
- Create standardized `npm run dev` script (no additional parameters like `:clean` or suffixes)

**Package and Dependency Management:**

- Always download and use latest stable versions of all packages and dependencies
- Regular dependency updates for security patches and performance improvements
- Maintain up-to-date package.json with latest compatible versions

**Code Quality and Reuse Standards:**

- Use `codebase-retrieval` tool before implementing new components to prevent duplication
- Extend existing implementations rather than creating duplicate functionality
- Maintain project simplicity focusing on core congregation management functionality
- Avoid over-engineering solutions or unnecessary complexity

**Implementation Standards:**

- No mock implementations - all functionality must be fully implemented with real data connections
- Create actual API endpoints rather than using mock alternatives
- Complete business logic implementation for all features
- Modern, attractive UI design while preserving exact existing visual identity

## Development Standards and Guidelines

### **Critical Development Requirements**

**Environment Configuration Standards:**

- All configuration variables must be defined in `.env` file as single source of truth
- Never hardcode localhost, ports, URLs, or any environment-specific values in codebase files
- Use default ports specified in `.env` file for all development and production environments
- Create standardized `npm run dev` script that initializes servers using environment variables only

**Package and Dependency Management:**

- Always download and use latest stable versions of all packages and dependencies
- Regular dependency updates for security patches and performance improvements
- Maintain up-to-date package.json with latest compatible versions
- Use exact version pinning only when necessary for stability

**Code Quality and Reuse Standards:**

- Use `codebase-retrieval` tool before implementing new components to prevent duplication
- Extend existing implementations rather than creating duplicate functionality
- Maintain project simplicity focusing on core congregation management functionality
- No mock implementations - all functionality must be fully implemented with real data connections

**UI and Performance Standards:**

- Design functionality to fit within single viewport without scrolling (except data listings)
- Prioritize compact, efficient layouts maximizing screen real estate
- Modern, attractive UI design while preserving exact existing visual identity
- Mobile-first responsive design with touch optimization

**Implementation Priority Order:**

1. Core congregation management functionality preservation
2. Exact UI/UX preservation and modern responsive design
3. Performance optimization for mobile devices
4. Multi-congregation architecture features

## User Interface Design Goals

### Overall UX Vision

The Hermanos App will maintain pixel-perfect UI compatibility with the current Coral Oeste App while adding multi-congregation support behind the scenes. Every visual element, color, spacing, typography, and interaction pattern will be preserved exactly as users currently experience them. The interface will preserve the distinctive card-based dashboard layout with Spanish-first design, ensuring zero learning curve for current users. The design will maintain two distinct interfaces: the member view side accessible to all users and the administrative section for leadership roles, with identical visual identity and navigation patterns.

### Key Interaction Paradigms

- **Dual Interface Architecture**: Separate member view and administrative interfaces with role-based access
- **Card-based Dashboard Navigation**: Preserved card layout for main navigation with consistent visual design
- **Administrative Delegation Workflow**: Interface adapts based on delegated administrative responsibilities
- **Mobile-first Responsive Design**: Touch-optimized interactions maintaining current mobile interface design
- **Modal-based Administrative Forms**: Administrative forms in overlay modals consistent with current design
- **Contextual Administrative Actions**: Actions presented based on delegated administrative authority
- **Consistent Spanish-first Interface**: Maintained Spanish terminology and interface elements

### Core Screens and Views

- **Login Screen**: Single congregation connection interface with region, ID, and PIN authentication for all users (preserved design)
- **Member Dashboard**: Card-based navigation hub with Servicio del Campo, Reuniones, Asignaciones, Tareas, Cartas, Eventos, plus conditional "Administración" button for elders/ministerial servants/overseers (exact current layout)
- **Administrative Section**: Admin interface accessible via "Administración" button for Member Management, Meeting Administration, Task Management (preserved admin design)
- **Meeting Management**: Separate interfaces for midweek and weekend meeting coordination (current design preserved)
- **Field Service Interface**: Service time tracking and territory management (current layout maintained)
- **Assignment Management**: Personal assignment tracking with improved assignment logic (preserved UI)
- **Letters & Events**: Communication and event management with current file upload interface
- **Administrative Settings**: Congregation settings and delegation management (preserved admin interface)

### Accessibility: WCAG AA

The application will comply with WCAG AA standards while maintaining the current visual design, including proper color contrast ratios, keyboard navigation support, screen reader compatibility, and alternative text for images.

### Branding

Preserve the exact visual design from current screenshots including card layouts, color schemes, typography, and Spanish interface elements. Maintain the theme system for section color customization while ensuring visual consistency across the multi-congregation platform.

### Target Device and Platforms: Web Responsive

Fully responsive web application optimized for desktop, tablet, and mobile devices, maintaining the current mobile-first design approach with enhanced desktop administrative interfaces.

## Technical Assumptions

### Repository Structure: Next.js Fullstack

Single Next.js application with App Router handling both frontend UI and backend API routes, using Prisma ORM for database operations and shared TypeScript types throughout the stack.

### Service Architecture

Next.js fullstack architecture with API routes for backend logic, preserving existing JW.org integration services exactly as implemented. Multi-congregation tenancy through PostgreSQL database-level isolation using congregation_id foreign keys. Self-hosted deployment on standard VPS infrastructure.

### Testing Requirements

Unit testing for critical business logic, integration testing for API endpoints, and manual testing convenience methods for JW.org data fetching validation. Focus on testing the preserved meeting content synchronization logic and improved assignment algorithms.

### Additional Technical Assumptions and Requests

- **Database**: PostgreSQL 15 with Prisma ORM for type-safe operations and seamless migration from existing 41 MySQL tables
- **Frontend Framework**: Next.js 14+ with App Router for unified fullstack development and pixel-perfect UI preservation
- **Authentication**: Simple JWT-based authentication with 60-day mobile-friendly expiration (configurable/disableable by developers/elders), preserving existing congregation ID/PIN model
- **Multi-tenancy**: PostgreSQL database-level isolation using congregation_id foreign keys for complete data separation
- **JW.org Integration**: Exact preservation of existing URL patterns, caching mechanisms, and data fetching logic without modification
- **Styling**: Tailwind CSS for utility-first styling while preserving exact existing visual design and theme system
- **State Management**: Zustand for simple, lightweight state management without over-complication
- **File Storage**: Local filesystem storage preserving existing upload patterns and file management workflows
- **Development Tools**: TypeScript strict mode, ESLint/Prettier for code quality, maintaining development standards
- **Deployment**: Self-hosted VPS deployment with PM2 process management and Nginx reverse proxy
- **Security**: Simple bcrypt PIN hashing and basic rate limiting without over-complicating existing security model
- **Implementation Timeline**: 12-week phased approach with exact UI preservation and zero downtime migration
- **Testing Strategy**: Focus on functionality preservation and mobile performance validation
- **Package Management**: Always download latest stable versions of all packages and dependencies for security and performance
- **Environment Configuration**: All configuration variables in .env file - never hardcode localhost, ports, or URLs in codebase files
- **Single Source of Truth**: .env file contains all environment-specific configuration as the only source of truth
- **Development Scripts**: Standardized `npm run dev` script without additional parameters, using .env configuration

## Epic List

### Epic Overview

**Epic 1: Foundation & Database Migration (Weeks 1-3)**
Establish Next.js/PostgreSQL project setup, complete migration of all 41 MySQL tables to PostgreSQL with Prisma, and implement simple JWT authentication preserving existing login workflows.

**Epic 2: UI Preservation & Core Features (Weeks 4-6)**
Implement pixel-perfect UI replication of existing dashboard, navigation, and member management interfaces, ensuring zero visual changes and identical user experience.

**Epic 3: Meeting Management & JW.org Integration (Weeks 7-8)**
Preserve existing midweek and weekend meeting management with exact JW.org data fetching logic, caching mechanisms, and assignment workflows without modification.

**Epic 4: Activities & Task Management (Weeks 9-10)**
Implement field service tracking and task management systems preserving existing workflows, data structures, and administrative patterns.

**Epic 5: Communication & Deployment (Weeks 11-12)**
Complete letters management, events system, comprehensive testing, and production deployment with data migration validation.

## Epic Details

### Epic 1: Foundation & Database Migration (Weeks 1-3)

**Epic Goal:** Establish the foundational Next.js/PostgreSQL application with complete database migration from MySQL, simple authentication system, and multi-congregation architecture. This epic delivers a deployable application with working authentication, complete data migration, and preserved login experience.

#### Story 1.1: Next.js Project Setup and Development Environment

As a developer,
I want to set up a Next.js fullstack project with PostgreSQL and Prisma,
so that I have a modern development environment for building the Hermanos app with exact UI preservation.

**Acceptance Criteria:**

1. Next.js 14+ project created with latest versions of TypeScript, App Router, and Tailwind CSS
2. PostgreSQL database connection established with latest Prisma ORM version
3. Development environment includes latest ESLint, Prettier for code quality
4. Complete `.env` file configuration with all ports, URLs, and environment variables
5. Never hardcode localhost, ports, or URLs in any codebase files
6. Single `npm run dev` script initializes both frontend and backend using .env variables
7. Health check API endpoint using environment configuration

#### Story 1.2: Complete MySQL to PostgreSQL Migration

As a system administrator,
I want to migrate all 41 MySQL tables to PostgreSQL with zero data loss,
so that the Hermanos app preserves all existing functionality and data.

**Acceptance Criteria:**

1. Prisma schema defines all 41 tables from existing MySQL structure
2. All tables include congregation_id foreign key for multi-congregation isolation
3. Complete data migration script preserves all existing records and relationships
4. Coral Oeste Spanish congregation data migrated successfully
5. Database indexes optimized for congregation-scoped queries
6. Migration validation confirms 100% data integrity
7. All existing table relationships and constraints preserved

#### Story 1.3: Single Authentication System with Role-Based Access

As a congregation member,
I want to authenticate using the exact same login process as before,
so that I can access my congregation's features, and if I'm an elder/ministerial servant, see the admin button.

**Acceptance Criteria:**

1. Single login page for all users preserving exact existing UI design and workflow
2. Simple JWT authentication with 60-day mobile-friendly expiration (configurable by developers/elders)
3. Role-based access control preserves existing roles and permissions
4. Authentication middleware protects API routes with congregation isolation
5. Secure bcrypt PIN hashing without over-complicating security
6. Coral Oeste Spanish congregation authentication works with existing credentials
7. Admin token expiration can be disabled by developers or elders with proper rights

#### Story 1.4: Dashboard with Conditional Admin Access

As a congregation member,
I want the dashboard to look and work exactly like it does now,
and if I'm an elder/ministerial servant/overseer, I want to see the "Administración" button.

**Acceptance Criteria:**

1. Dashboard preserves exact visual design, colors, spacing, and typography for all users
2. Card-based navigation maintains identical layout and touch targets
3. "Administración" button appears only for elders, ministerial servants, and overseers
4. Admin button matches existing design language and positioning from screenshots
5. Mobile-first responsive design maintains current interface behavior
6. Spanish-first interface preserves all existing terminology and text
7. Publishers see standard dashboard without admin access button

### Epic 2: Core Member & Authentication System

**Epic Goal:** Implement comprehensive member management, role-based permissions, congregation-specific authentication workflows, and administrative delegation system with full CRUD operations for members. This epic delivers complete user management capabilities with proper security, multi-congregation support, and administrative delegation hierarchy.

#### Story 2.1: Administrative Delegation System

As a coordinator elder,
I want to assign specific administrative sections to elders and ministerial servants,
so that I can delegate administrative responsibilities while maintaining oversight and control.

**Acceptance Criteria:**

1. Administrative section assignment interface for coordinator elders to delegate responsibilities
2. Section-based permission system (Field Service, Meetings, Tasks, Letters, Events, etc.)
3. Elder and ministerial servant assignment to multiple sections with defined scope
4. Assignment history tracking and modification capabilities
5. Section responsibility transfer workflow with proper handover procedures
6. Administrative assignment notifications and confirmation system
7. Coordinator oversight dashboard showing all section assignments and activities

#### Story 2.2: Enhanced Member Profile Management

As a congregation administrator,
I want to create and manage member profiles with roles and permissions,
so that I can maintain accurate congregation membership records with proper delegation authority.

**Acceptance Criteria:**

1. Admin interface for creating new members with name, contact information, and role assignment
2. Member profile editing with validation for required fields and delegation authority validation
3. Role assignment supports all congregation roles with proper authorization checking
4. Member profiles are congregation-specific and isolated by congregation_id
5. Member search and filtering functionality by name, role, and status
6. Member deactivation/reactivation without data deletion
7. Audit trail for member profile changes with timestamp and admin identification

#### Story 2.3: Enhanced PIN Management and Security

As a congregation administrator,
I want to manage member PINs with configurable length requirements,
so that members can securely access their congregation features with proper security controls.

**Acceptance Criteria:**

1. PIN generation and assignment for new members with length validation
2. PIN reset functionality for existing members with proper authorization
3. PIN validation ensures uniqueness within congregation and length constraints
4. Secure PIN storage with proper hashing for both congregation and member PINs
5. PIN change history tracking for security audit
6. Developer role can configure PIN length requirements per congregation
7. PIN complexity requirements configurable per congregation with default settings
