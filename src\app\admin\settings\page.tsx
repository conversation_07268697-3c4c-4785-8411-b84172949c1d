'use client';

/**
 * Congregation Settings Page for Hermanos App
 *
 * Administrative interface for managing congregation settings including:
 * - Congregation Information
 * - Meeting Schedule
 * - Authentication Settings
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Modal from '@/components/ui/Modal';
import AdminFooter from '@/components/admin/AdminFooter';
import { useTranslation } from '@/contexts/LanguageContext';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

interface CongregationSettings {
  id: number;
  name: string;
  number: string;
  pin: string;
  language: string; // Add language field
  circuitNumber: string;
  circuitOverseer: string;
  address: string; // Add address field
  midweekDay: string;
  midweekTime: string;
  weekendDay: string;
  weekendTime: string;
  defaultCongregationId: string;
  defaultCongregationPin: string;
}

export default function CongregationSettingsPage() {
  const router = useRouter();
  const { t } = useTranslation();
  const [user, setUser] = useState<User | null>(null);
  const [settings, setSettings] = useState<CongregationSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showEditInfo, setShowEditInfo] = useState(false);
  const [showEditSchedule, setShowEditSchedule] = useState(false);

  // Form states
  const [infoForm, setInfoForm] = useState({
    name: '',
    number: '',
    language: 'es', // Add language to form state
    circuitNumber: '',
    circuitOverseer: '',
    address: '',
    pin: ''
  });

  const [scheduleForm, setScheduleForm] = useState({
    midweekDay: '',
    midweekTime: '',
    weekendDay: '',
    weekendTime: ''
  });



  useEffect(() => {
    checkAdminAccess();
  }, []);

  // Helper function to convert 12-hour format to 24-hour format for input[type="time"]
  const convertTo24Hour = (time12h: string): string => {
    if (!time12h) return '';

    // If already in 24-hour format, return as is
    if (time12h.match(/^\d{2}:\d{2}$/)) {
      return time12h;
    }

    // Convert from 12-hour format (e.g., "7:00 PM" to "19:00")
    const [time, modifier] = time12h.split(' ');
    let [hours, minutes] = time.split(':');

    if (hours === '12') {
      hours = '00';
    }

    if (modifier === 'PM') {
      hours = (parseInt(hours, 10) + 12).toString();
    }

    return `${hours.padStart(2, '0')}:${minutes}`;
  };

  // Helper function to convert 24-hour format to 12-hour format for display
  const convertTo12Hour = (time24h: string): string => {
    if (!time24h) return '';

    const [hours, minutes] = time24h.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;

    return `${displayHour}:${minutes} ${ampm}`;
  };

  const checkAdminAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      // Verify token and check admin permissions
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();

        // Check if user has admin access
        if (!data.permissions.canAccessAdmin) {
          router.push('/dashboard');
          return;
        }

        setUser(data.user);
        await loadSettings();
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Admin access check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSettings = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/settings/congregation', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data);

        // Initialize form states
        setInfoForm({
          name: data.name || '',
          number: data.number || '',
          language: data.language || 'es', // Initialize language
          circuitNumber: data.circuitNumber || '',
          circuitOverseer: data.circuitOverseer || '',
          address: data.address || '',
          pin: data.pin || ''
        });

        setScheduleForm({
          midweekDay: data.midweekDay || '',
          midweekTime: convertTo24Hour(data.midweekTime || ''),
          weekendDay: data.weekendDay || '',
          weekendTime: convertTo24Hour(data.weekendTime || '')
        });


      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const handleBackToAdmin = () => {
    router.push('/admin');
  };

  const handleSaveInfo = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/settings/congregation', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'info',
          ...infoForm
        }),
      });

      if (response.ok) {
        await loadSettings();
        setShowEditInfo(false);
      } else {
        console.error('Failed to save congregation info');
      }
    } catch (error) {
      console.error('Error saving congregation info:', error);
    }
  };

  const handleSaveSchedule = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/settings/congregation', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'schedule',
          midweekDay: scheduleForm.midweekDay,
          midweekTime: convertTo12Hour(scheduleForm.midweekTime),
          weekendDay: scheduleForm.weekendDay,
          weekendTime: convertTo12Hour(scheduleForm.weekendTime)
        }),
      });

      if (response.ok) {
        await loadSettings();
        setShowEditSchedule(false);
      } else {
        console.error('Failed to save meeting schedule');
      }
    } catch (error) {
      console.error('Error saving meeting schedule:', error);
    }
  };



  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (!user || !settings) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-gray-500 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackToAdmin}
                className="flex items-center text-white hover:text-gray-100 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span className="text-sm font-medium">Back</span>
              </button>
            </div>
            <div className="text-center flex-1">
              <h1 className="text-xl font-semibold text-white">
                {t('admin.settings.title')}
              </h1>
            </div>
            <div className="w-24"></div> {/* Spacer for centering */}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0 space-y-8">

          {/* Congregation Information */}
          <div className="bg-white shadow-sm rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">{t('admin.settings.congregation_info')}</h2>
              <button
                onClick={() => setShowEditInfo(true)}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                {t('common.edit')} {t('admin.settings.congregation_info')}
              </button>
            </div>
            <div className="px-6 py-4">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <dt className="text-sm font-medium text-gray-700">{t('admin.settings.congregation_name')}</dt>
                  <dd className="text-sm text-gray-900">{settings.name}</dd>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <dt className="text-sm font-medium text-gray-700">{t('admin.settings.congregation_number')}</dt>
                  <dd className="text-sm text-gray-900">{settings.number}</dd>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <dt className="text-sm font-medium text-gray-700">{t('admin.settings.congregation_pin')}</dt>
                  <dd className="text-sm text-gray-900">{settings.pin}</dd>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <dt className="text-sm font-medium text-gray-700">{t('admin.settings.language')}</dt>
                  <dd className="text-sm text-gray-900">
                    {settings.language === 'es' ? t('admin.settings.language_spanish') : t('admin.settings.language_english')}
                  </dd>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <dt className="text-sm font-medium text-gray-700">Circuit Number</dt>
                  <dd className="text-sm text-gray-900">{settings.circuitNumber}</dd>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100 sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-700">Circuit Overseer</dt>
                  <dd className="text-sm text-gray-900">{settings.circuitOverseer}</dd>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100 sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-700">Address</dt>
                  <dd className="text-sm text-gray-900">{settings.address || 'No address provided'}</dd>
                </div>
              </dl>
            </div>
          </div>

          {/* Meeting Schedule */}
          <div className="bg-white shadow-sm rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">Meeting Schedule</h2>
              <button
                onClick={() => setShowEditSchedule(true)}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Meeting Schedule
              </button>
            </div>
            <div className="px-6 py-4 space-y-6">
              <div>
                <h3 className="text-base font-medium text-gray-900 mb-3">Midweek Meeting</h3>
                <dl className="grid grid-cols-2 gap-x-4">
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <dt className="text-sm font-medium text-gray-700">Day</dt>
                    <dd className="text-sm text-gray-900">{settings.midweekDay}</dd>
                  </div>
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <dt className="text-sm font-medium text-gray-700">Time</dt>
                    <dd className="text-sm text-gray-900">{settings.midweekTime}</dd>
                  </div>
                </dl>
              </div>
              <div>
                <h3 className="text-base font-medium text-gray-900 mb-3">Weekend Meeting</h3>
                <dl className="grid grid-cols-2 gap-x-4">
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <dt className="text-sm font-medium text-gray-700">Day</dt>
                    <dd className="text-sm text-gray-900">{settings.weekendDay}</dd>
                  </div>
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <dt className="text-sm font-medium text-gray-700">Time</dt>
                    <dd className="text-sm text-gray-900">{settings.weekendTime}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>

        </div>
      </main>

      {/* Edit Congregation Information Modal */}
      <Modal
        isOpen={showEditInfo}
        onClose={() => setShowEditInfo(false)}
        title="Edit Congregation Information"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Congregation Name
            </label>
            <input
              type="text"
              value={infoForm.name}
              onChange={(e) => setInfoForm({ ...infoForm, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Congregation Number
            </label>
            <input
              type="text"
              value={infoForm.number}
              onChange={(e) => setInfoForm({ ...infoForm, number: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Congregation Language
            </label>
            <select
              value={infoForm.language}
              onChange={(e) => setInfoForm({ ...infoForm, language: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="es">Español</option>
              <option value="en">English</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Circuit Number
            </label>
            <input
              type="text"
              value={infoForm.circuitNumber}
              onChange={(e) => setInfoForm({ ...infoForm, circuitNumber: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Circuit Overseer
            </label>
            <input
              type="text"
              value={infoForm.circuitOverseer}
              onChange={(e) => setInfoForm({ ...infoForm, circuitOverseer: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <textarea
              value={infoForm.address}
              onChange={(e) => setInfoForm({ ...infoForm, address: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter congregation address"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Congregation PIN
            </label>
            <input
              type="text"
              value={infoForm.pin}
              onChange={(e) => setInfoForm({ ...infoForm, pin: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter 4-digit PIN"
              maxLength={4}
            />
            <p className="mt-1 text-xs text-gray-500">
              This PIN is used for congregation authentication
            </p>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => setShowEditInfo(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveInfo}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Save Changes
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Meeting Schedule Modal */}
      <Modal
        isOpen={showEditSchedule}
        onClose={() => setShowEditSchedule(false)}
        title="Edit Meeting Schedule"
        size="lg"
      >
        <div className="space-y-4">
          {/* Compact Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Midweek Meeting */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                <h3 className="text-sm font-semibold text-gray-900">Midweek Meeting</h3>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Day
                  </label>
                  <select
                    value={scheduleForm.midweekDay}
                    onChange={(e) => setScheduleForm({ ...scheduleForm, midweekDay: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  >
                    <option value="">Select Day</option>
                    <option value="Monday">Monday</option>
                    <option value="Tuesday">Tuesday</option>
                    <option value="Wednesday">Wednesday</option>
                    <option value="Thursday">Thursday</option>
                    <option value="Friday">Friday</option>
                    <option value="Saturday">Saturday</option>
                    <option value="Sunday">Sunday</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Time
                  </label>
                  <input
                    type="time"
                    value={scheduleForm.midweekTime}
                    onChange={(e) => setScheduleForm({ ...scheduleForm, midweekTime: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>
            </div>

            {/* Weekend Meeting */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <h3 className="text-sm font-semibold text-gray-900">Weekend Meeting</h3>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Day
                  </label>
                  <select
                    value={scheduleForm.weekendDay}
                    onChange={(e) => setScheduleForm({ ...scheduleForm, weekendDay: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  >
                    <option value="">Select Day</option>
                    <option value="Monday">Monday</option>
                    <option value="Tuesday">Tuesday</option>
                    <option value="Wednesday">Wednesday</option>
                    <option value="Thursday">Thursday</option>
                    <option value="Friday">Friday</option>
                    <option value="Saturday">Saturday</option>
                    <option value="Sunday">Sunday</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Time
                  </label>
                  <input
                    type="time"
                    value={scheduleForm.weekendTime}
                    onChange={(e) => setScheduleForm({ ...scheduleForm, weekendTime: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              onClick={() => setShowEditSchedule(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveSchedule}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-sm"
            >
              Save Changes
            </button>
          </div>
        </div>
      </Modal>

      {/* Admin Footer */}
      <AdminFooter currentSection="settings" />
    </div>
  );
}
