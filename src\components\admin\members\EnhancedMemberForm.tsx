/**
 * Enhanced Member Form Component
 *
 * Comprehensive member profile form with role assignment, service group coordination,
 * and contact preferences. Integrates with Story 2.1 permission delegation system.
 */

import React, { useState, useEffect } from 'react';
import { MemberProfile } from '@/lib/services/memberManagementService';

interface ContactPreferences {
  preferredMethod: 'email' | 'phone' | 'text';
  allowEmergencyContact: boolean;
  privacyLevel: 'public' | 'elders_only' | 'private';
}

interface EnhancedMemberFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  birthDate: string;
  role: string;
  serviceGroup: string;
  pin: string;
  contactPreferences: ContactPreferences;
  qualifications: string[];
  notes: string;
  reason: string;
}

interface EnhancedMemberFormProps {
  member?: MemberProfile;
  onSubmit: (data: EnhancedMemberFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  canManageRoles?: boolean;
  availableServiceGroups?: string[];
  availableQualifications?: string[];
  availableRoles?: {value: string, label: string}[];
}

const defaultContactPreferences: ContactPreferences = {
  preferredMethod: 'email',
  allowEmergencyContact: false,
  privacyLevel: 'public',
};



const qualificationOptions = [
  'Lector',
  'Oración',
  'Discurso',
  'Demostración',
  'Presidente',
  'Conductor',
  'Anciano de Servicio',
  'Superintendente de Grupo',
];

export default function EnhancedMemberForm({
  member,
  onSubmit,
  onCancel,
  isLoading = false,
  canManageRoles = true,
  availableServiceGroups = [],
  availableQualifications = qualificationOptions,
  availableRoles = [
    { value: 'publisher', label: 'Publicador' },
    { value: 'ministerial_servant', label: 'Siervo Ministerial' },
    { value: 'elder', label: 'Anciano' },
    { value: 'coordinator', label: 'Coordinador' },
    { value: 'developer', label: 'Desarrollador' },
  ],
}: EnhancedMemberFormProps) {
  const [formData, setFormData] = useState<EnhancedMemberFormData>({
    name: member?.name || '',
    email: member?.email || '',
    phone: member?.phone || '',
    address: member?.address || '',
    birthDate: member?.birthDate ? (
      typeof member.birthDate === 'string'
        ? member.birthDate.split('T')[0]
        : member.birthDate.toISOString().split('T')[0]
    ) : '',
    role: member?.role || 'publisher',
    serviceGroup: member?.serviceGroup || '',
    pin: '',
    contactPreferences: member?.contactPreferences || defaultContactPreferences,
    qualifications: member?.qualifications || [],
    notes: member?.notes || '',
    reason: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'El nombre es requerido';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'El email es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (!member && !formData.pin.trim()) {
      newErrors.pin = 'El PIN es requerido para nuevos miembros';
    } else if (formData.pin && formData.pin.length < 4) {
      newErrors.pin = 'El PIN debe tener al menos 4 caracteres';
    }

    if (formData.phone && !/^[\d\s\-\+\(\)]+$/.test(formData.phone)) {
      newErrors.phone = 'Formato de teléfono inválido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const handleInputChange = (field: keyof EnhancedMemberFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleContactPreferenceChange = (field: keyof ContactPreferences, value: any) => {
    setFormData(prev => ({
      ...prev,
      contactPreferences: {
        ...prev.contactPreferences,
        [field]: value,
      },
    }));
  };

  const handleQualificationToggle = (qualification: string) => {
    setFormData(prev => ({
      ...prev,
      qualifications: prev.qualifications.includes(qualification)
        ? prev.qualifications.filter(q => q !== qualification)
        : [...prev.qualifications, qualification],
    }));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 max-w-4xl mx-auto">
      <div className="mb-6 border-b border-gray-200 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {member ? 'Edit Member' : 'Add Member'}
        </h2>
        <p className="text-gray-600 text-sm">
          {member ? 'Update member information and settings' : 'Create a new member profile for the congregation'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information Section */}
        <div className="bg-gray-50 rounded-lg p-5">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter full name"
                disabled={isLoading}
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.phone ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="+****************"
                disabled={isLoading}
              />
              {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Birth Date
              </label>
              <input
                type="date"
                value={formData.birthDate}
                onChange={(e) => handleInputChange('birthDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Address
            </label>
            <textarea
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="Complete address"
              disabled={isLoading}
            />
          </div>
        </div>

        {/* Congregation Role Section */}
        <div className="bg-gray-50 rounded-lg p-5">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Congregation Role</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Rol *
              </label>
              <select
                value={formData.role}
                onChange={(e) => handleInputChange('role', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading || !canManageRoles}
              >
                {availableRoles.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Grupo de Servicio
              </label>
              <select
                value={formData.serviceGroup}
                onChange={(e) => handleInputChange('serviceGroup', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="">Seleccionar grupo...</option>
                {availableServiceGroups.map(group => (
                  <option key={group} value={group}>
                    {group}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Authentication Section */}
        {!member && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Autenticación</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                PIN de Acceso *
              </label>
              <input
                type="password"
                value={formData.pin}
                onChange={(e) => handleInputChange('pin', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.pin ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Mínimo 4 caracteres"
                disabled={isLoading}
              />
              {errors.pin && <p className="text-red-500 text-sm mt-1">{errors.pin}</p>}
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 transition-colors"
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : (member ? 'Update Member' : 'Create Member')}
          </button>
        </div>
      </form>
    </div>
  );
}
