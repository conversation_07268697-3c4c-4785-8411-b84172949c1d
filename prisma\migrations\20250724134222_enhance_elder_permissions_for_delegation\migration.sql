/*
  Warnings:

  - You are about to drop the column `can_access` on the `elder_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `can_delete` on the `elder_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `can_edit` on the `elder_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `section` on the `elder_permissions` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[member_id,section_id]` on the table `elder_permissions` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `assigned_by` to the `elder_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `section_id` to the `elder_permissions` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "elder_permissions_congregation_id_section_idx";

-- DropIndex
DROP INDEX "elder_permissions_member_id_section_key";

-- AlterTable
ALTER TABLE "elder_permissions" DROP COLUMN "can_access",
DROP COLUMN "can_delete",
DROP COLUMN "can_edit",
DROP COLUMN "section",
ADD COLUMN     "assigned_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "assigned_by" TEXT NOT NULL,
ADD COLUMN     "expiration_date" TIMESTAMPTZ(6),
ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "notes" TEXT,
ADD COLUMN     "permissions" JSONB NOT NULL DEFAULT '[]',
ADD COLUMN     "section_id" VARCHAR(100) NOT NULL;

-- CreateTable
CREATE TABLE "permission_audit_logs" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "user_id" TEXT NOT NULL,
    "action" VARCHAR(50) NOT NULL,
    "section_id" VARCHAR(100) NOT NULL,
    "permissions" JSONB NOT NULL DEFAULT '[]',
    "performed_by" TEXT NOT NULL,
    "reason" TEXT,
    "ip_address" VARCHAR(45),
    "user_agent" TEXT,
    "timestamp" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "permission_audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "permission_audit_logs_congregation_id_user_id_idx" ON "permission_audit_logs"("congregation_id", "user_id");

-- CreateIndex
CREATE INDEX "permission_audit_logs_congregation_id_section_id_idx" ON "permission_audit_logs"("congregation_id", "section_id");

-- CreateIndex
CREATE INDEX "permission_audit_logs_performed_by_idx" ON "permission_audit_logs"("performed_by");

-- CreateIndex
CREATE INDEX "permission_audit_logs_timestamp_idx" ON "permission_audit_logs"("timestamp");

-- CreateIndex
CREATE INDEX "elder_permissions_congregation_id_section_id_idx" ON "elder_permissions"("congregation_id", "section_id");

-- CreateIndex
CREATE INDEX "elder_permissions_assigned_by_idx" ON "elder_permissions"("assigned_by");

-- CreateIndex
CREATE INDEX "elder_permissions_is_active_idx" ON "elder_permissions"("is_active");

-- CreateIndex
CREATE UNIQUE INDEX "elder_permissions_member_id_section_id_key" ON "elder_permissions"("member_id", "section_id");

-- AddForeignKey
ALTER TABLE "elder_permissions" ADD CONSTRAINT "elder_permissions_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "members"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "permission_audit_logs" ADD CONSTRAINT "permission_audit_logs_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "permission_audit_logs" ADD CONSTRAINT "permission_audit_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "permission_audit_logs" ADD CONSTRAINT "permission_audit_logs_performed_by_fkey" FOREIGN KEY ("performed_by") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;
