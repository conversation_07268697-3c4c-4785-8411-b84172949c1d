// Territory Import API Tests
// Tests for Excel territory import functionality

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { ExcelImportService } from '@/services/territories/ImportService';
import * as XLSX from 'xlsx';

// Mock Prisma for testing
jest.mock('@/lib/prisma', () => ({
  prisma: {
    territory: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

describe('ExcelImportService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('parseExcelFile', () => {
    it('should parse valid Excel file with territory data', async () => {
      // Create test Excel data
      const testData = [
        ['Territory Number', 'Address', 'Notes'],
        ['T001', '123 Main St', 'Test territory 1'],
        ['T002', '456 Oak Ave', 'Test territory 2'],
        ['T003', '789 Pine Rd', '']
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(testData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Territories');
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      const result = await ExcelImportService.parseExcelFile(buffer);

      expect(result.data).toHaveLength(3);
      expect(result.errors).toHaveLength(0);
      expect(result.data[0]).toEqual({
        territoryNumber: 'T001',
        address: '123 Main St',
        notes: 'Test territory 1',
        rowNumber: 2
      });
    });

    it('should handle Excel file with column letters (A, B, C)', async () => {
      // Create test Excel data without headers
      const testData = [
        ['T001', '123 Main St', 'Notes 1'],
        ['T002', '456 Oak Ave', 'Notes 2']
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(testData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Territories');
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      const result = await ExcelImportService.parseExcelFile(buffer);

      expect(result.data).toHaveLength(2);
      expect(result.data[0].territoryNumber).toBe('T001');
      expect(result.data[0].address).toBe('123 Main St');
    });

    it('should validate territory data and report errors', async () => {
      // Create test Excel data with validation errors
      const testData = [
        ['Territory Number', 'Address'],
        ['', '123 Main St'], // Missing territory number
        ['T002', ''], // Missing address
        ['T003-with-very-long-territory-number-that-exceeds-limit', '456 Oak Ave'], // Too long
        ['T@04', '789 Pine Rd'] // Invalid characters
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(testData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Territories');
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      const result = await ExcelImportService.parseExcelFile(buffer);

      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.error.includes('Territory number is required'))).toBe(true);
      expect(result.errors.some(e => e.error.includes('Address is required'))).toBe(true);
    });

    it('should handle empty Excel file gracefully', async () => {
      // Create empty Excel file
      const worksheet = XLSX.utils.aoa_to_sheet([]);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Empty');
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      const result = await ExcelImportService.parseExcelFile(buffer);

      expect(result.data).toHaveLength(0);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0].error).toContain('Could not detect territory data columns');
    });

    it('should handle corrupted Excel file', async () => {
      // Create invalid buffer
      const invalidBuffer = Buffer.from('This is not an Excel file');

      const result = await ExcelImportService.parseExcelFile(invalidBuffer);

      expect(result.data).toHaveLength(0);
      expect(result.errors.length).toBeGreaterThan(0);
      // Corrupted file should produce validation errors
      expect(result.errors.some(e => e.error.includes('invalid characters'))).toBe(true);
    });
  });

  describe('importTerritories', () => {
    const mockPrisma = require('@/lib/prisma').prisma;

    beforeEach(() => {
      mockPrisma.territory.findMany.mockResolvedValue([]);
      mockPrisma.territory.create.mockImplementation((data) => ({
        id: `territory-${Math.random()}`,
        ...data.data
      }));
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        return await callback(mockPrisma);
      });
    });

    it('should import valid territories successfully', async () => {
      const territories = [
        { territoryNumber: 'T001', address: '123 Main St', rowNumber: 2 },
        { territoryNumber: 'T002', address: '456 Oak Ave', rowNumber: 3 }
      ];

      const result = await ExcelImportService.importTerritories(territories, '1441');

      expect(result.success).toBe(true);
      expect(result.successfulImports).toBe(2);
      expect(result.failedImports).toBe(0);
      expect(result.duplicatesFound).toBe(0);
      expect(mockPrisma.territory.create).toHaveBeenCalledTimes(2);
    });

    it('should detect and skip duplicate territory numbers in import data', async () => {
      const territories = [
        { territoryNumber: 'T001', address: '123 Main St', rowNumber: 2 },
        { territoryNumber: 'T001', address: '456 Oak Ave', rowNumber: 3 }, // Duplicate
        { territoryNumber: 'T002', address: '789 Pine Rd', rowNumber: 4 }
      ];

      const result = await ExcelImportService.importTerritories(territories, '1441');

      expect(result.duplicatesFound).toBe(1);
      expect(result.successfulImports).toBe(2); // Only unique territories imported
      expect(mockPrisma.territory.create).toHaveBeenCalledTimes(2);
    });

    it('should detect existing territories in database', async () => {
      // Mock existing territory in database
      mockPrisma.territory.findMany.mockResolvedValue([
        { territoryNumber: 'T001' }
      ]);

      const territories = [
        { territoryNumber: 'T001', address: '123 Main St', rowNumber: 2 }, // Exists in DB
        { territoryNumber: 'T002', address: '456 Oak Ave', rowNumber: 3 }
      ];

      const result = await ExcelImportService.importTerritories(territories, '1441');

      expect(result.duplicatesFound).toBe(1);
      expect(result.successfulImports).toBe(1); // Only new territory imported
      expect(mockPrisma.territory.create).toHaveBeenCalledTimes(1);
    });

    it('should handle database errors gracefully', async () => {
      mockPrisma.territory.create.mockRejectedValue(new Error('Database constraint violation'));

      const territories = [
        { territoryNumber: 'T001', address: '123 Main St', rowNumber: 2 }
      ];

      const result = await ExcelImportService.importTerritories(territories, '1441');

      expect(result.success).toBe(false);
      expect(result.successfulImports).toBe(0);
      expect(result.failedImports).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].error).toContain('Failed to create territory');
    });

    it('should generate appropriate import summary', async () => {
      const territories = [
        { territoryNumber: 'T001', address: '123 Main St', rowNumber: 2 },
        { territoryNumber: 'T002', address: '456 Oak Ave', rowNumber: 3 }
      ];

      const result = await ExcelImportService.importTerritories(territories, '1441');

      expect(result.summary).toContain('2 territories imported successfully');
    });
  });

  describe('Data Validation', () => {
    it('should validate territory number format', async () => {
      const testData = [
        ['Territory Number', 'Address'],
        ['T@01', '123 Main St'], // Invalid character @
        ['T 02', '456 Oak Ave'], // Invalid space
        ['T-03', '789 Pine Rd'], // Valid with hyphen
        ['T_04', '321 Elm St'] // Valid with underscore
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(testData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Territories');
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      const result = await ExcelImportService.parseExcelFile(buffer);

      // Should have 2 valid territories and 2 validation errors
      expect(result.data).toHaveLength(2);
      expect(result.errors).toHaveLength(2);
      expect(result.errors.some(e => e.error.includes('invalid characters'))).toBe(true);
    });

    it('should validate required fields', async () => {
      const testData = [
        ['Territory Number', 'Address'],
        ['T001', ''], // Missing address
        ['', '123 Main St'], // Missing territory number
        ['T003', '456 Oak Ave'] // Valid
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(testData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Territories');
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      const result = await ExcelImportService.parseExcelFile(buffer);

      expect(result.data).toHaveLength(1); // Only valid territory
      expect(result.errors).toHaveLength(2); // Two validation errors
    });
  });
});
