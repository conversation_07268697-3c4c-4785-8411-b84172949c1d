/**
 * Test the actual API endpoint for Territory 001
 */

async function testAPIEndpoint() {
  try {
    console.log('🧪 Testing Territory API endpoint...\n');

    // Territory 001 ID from our previous test
    const territory001Id = 'cmdjtpo7b000163cf8kvvkokh';

    // First, login to get a token
    console.log('🔐 Logging in...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1234'
      })
    });

    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      throw new Error(`Login failed: ${loginResponse.statusText} - ${errorText}`);
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ Login successful');

    // Test the individual territory API
    console.log('\n🎯 Testing Territory 001 API endpoint...');
    const territoryResponse = await fetch(`http://localhost:3001/api/territories/${territory001Id}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!territoryResponse.ok) {
      const errorText = await territoryResponse.text();
      throw new Error(`Failed to get territory: ${territoryResponse.statusText} - ${errorText}`);
    }

    const territoryData = await territoryResponse.json();
    
    console.log('\n📊 API Response:');
    console.log(`  Territory Number: ${territoryData.territoryNumber}`);
    console.log(`  Address: ${territoryData.address?.split('\n')[0] || 'No address'}`);
    console.log(`  Has Coordinates: ${territoryData.coordinates ? 'YES' : 'NO'}`);
    console.log(`  Has Boundary: ${territoryData.boundary ? 'YES' : 'NO'}`);
    
    if (territoryData.boundary) {
      console.log(`  Boundary Type: ${territoryData.boundary.type}`);
      console.log(`  Boundary Coordinates: ${territoryData.boundary.coordinates?.[0]?.length || 0} points`);
      console.log('\n🎉 SUCCESS: API is returning boundary data!');
      console.log('   Territory detail maps should now work properly.');
    } else {
      console.log('\n❌ ISSUE: API is NOT returning boundary data');
      console.log('   Check the API route logic.');
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

testAPIEndpoint();
