#!/usr/bin/env node

/**
 * SQL Backup Restoration
 * 
 * This script properly parses and executes the PostgreSQL backup file
 * using Node.js pg library with proper SQL parsing.
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function restoreSqlBackup() {
    console.log('🔄 RESTORING DATABASE FROM SQL BACKUP...');
    console.log('');

    let client;

    try {
        // 1. Check if backup file exists
        const backupFile = path.join(__dirname, '../hermanos-07-25-25E.sql');
        if (!fs.existsSync(backupFile)) {
            throw new Error('Backup file hermanos-07-25-25E.sql not found');
        }
        console.log('✅ Backup file found: hermanos-07-25-25E.sql');

        // 2. Read and clean backup file
        console.log('📖 Reading and parsing backup file...');
        const backupContent = fs.readFileSync(backupFile, 'utf8');
        console.log(`   📊 Backup file size: ${(backupContent.length / 1024 / 1024).toFixed(2)} MB`);

        // 3. Parse SQL statements properly
        const sqlStatements = parseSqlStatements(backupContent);
        console.log(`   📊 Found ${sqlStatements.length} valid SQL statements`);

        // 4. Connect to PostgreSQL
        console.log('🔌 Connecting to PostgreSQL...');
        client = new Client({
            host: 'localhost',
            port: 5432,
            user: 'mywebsites',
            password: 'password',
            database: 'postgres'
        });

        await client.connect();
        console.log('✅ Connected to PostgreSQL');

        // 5. Drop and recreate database
        console.log('🗑️  Dropping existing database...');
        try {
            await client.query(`
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = 'hermanos' AND pid <> pg_backend_pid();
            `);
            await client.query('DROP DATABASE IF EXISTS hermanos;');
            console.log('✅ Database dropped');
        } catch (error) {
            console.log('⚠️  Database drop warning:', error.message);
        }

        console.log('🆕 Creating new database...');
        await client.query('CREATE DATABASE hermanos;');
        console.log('✅ Database created');

        // 6. Connect to hermanos database
        await client.end();
        
        console.log('🔌 Connecting to hermanos database...');
        client = new Client({
            host: 'localhost',
            port: 5432,
            user: 'mywebsites',
            password: 'password',
            database: 'hermanos'
        });

        await client.connect();
        console.log('✅ Connected to hermanos database');

        // 7. Execute SQL statements
        console.log('📥 Executing SQL statements...');
        
        let successCount = 0;
        let errorCount = 0;
        let skipCount = 0;

        for (let i = 0; i < sqlStatements.length; i++) {
            const statement = sqlStatements[i];
            
            try {
                await client.query(statement);
                successCount++;
                
                if (i % 50 === 0) {
                    console.log(`   🔄 Progress: ${i}/${sqlStatements.length} statements executed`);
                }
            } catch (error) {
                // Skip common warnings and non-critical errors
                if (isSkippableError(error.message)) {
                    skipCount++;
                } else {
                    errorCount++;
                    console.log(`   ⚠️  Statement ${i} error: ${error.message.substring(0, 100)}...`);
                }
            }
        }

        console.log(`✅ SQL execution completed:`);
        console.log(`   📊 Successful statements: ${successCount}`);
        console.log(`   ⚠️  Errors: ${errorCount}`);
        console.log(`   ⏭️  Skipped: ${skipCount}`);

        // 8. Verify restoration
        console.log('🔍 Verifying restoration...');
        const verificationResults = await verifyRestoration(client);
        
        console.log('');
        console.log('🎉 DATABASE RESTORATION SUCCESSFUL!');
        console.log('');
        console.log('📊 RESTORED DATA:');
        console.log(`   📋 Congregations: ${verificationResults.congregations}`);
        console.log(`   👥 Members: ${verificationResults.members}`);
        console.log(`   ⚙️  Settings: ${verificationResults.settings}`);
        console.log(`   🎵 Songs: ${verificationResults.songs}`);
        console.log(`   📋 Tasks: ${verificationResults.tasks}`);

        // 9. Get sample login data
        if (verificationResults.congregations > 0) {
            console.log('');
            console.log('📋 SAMPLE LOGIN CREDENTIALS:');
            
            const sampleCongregation = await client.query('SELECT id, name FROM congregations LIMIT 1;');
            if (sampleCongregation.rows.length > 0) {
                console.log(`   Congregation ID: ${sampleCongregation.rows[0].id}`);
                console.log(`   Congregation Name: ${sampleCongregation.rows[0].name}`);
            }
            
            const sampleMember = await client.query('SELECT email, name, role FROM members WHERE is_active = true LIMIT 1;');
            if (sampleMember.rows.length > 0) {
                console.log(`   Sample Member: ${sampleMember.rows[0].email}`);
                console.log(`   Member Name: ${sampleMember.rows[0].name}`);
                console.log(`   Member Role: ${sampleMember.rows[0].role}`);
            }
        }
        
        console.log('');
        console.log('🎯 NEXT STEPS:');
        console.log('   1. Start the development server: npm run dev');
        console.log('   2. Test authentication with restored data');
        console.log('   3. Check congregation PIN and member PINs from backup');

    } catch (error) {
        console.error('❌ Error during database restoration:', error);
        throw error;
    } finally {
        if (client) {
            await client.end();
        }
    }
}

function parseSqlStatements(content) {
    // Remove comments and split into statements
    const lines = content.split('\n');
    const cleanLines = [];
    
    for (const line of lines) {
        const trimmed = line.trim();
        
        // Skip comments and empty lines
        if (trimmed.startsWith('--') || 
            trimmed.startsWith('/*') || 
            trimmed.startsWith('*') ||
            trimmed.startsWith('SET ') ||
            trimmed.startsWith('SELECT pg_catalog.') ||
            trimmed.includes('Type:') ||
            trimmed.includes('Schema:') ||
            trimmed.includes('Owner:') ||
            trimmed.length === 0) {
            continue;
        }
        
        cleanLines.push(line);
    }
    
    // Join lines and split by semicolon
    const cleanContent = cleanLines.join('\n');
    const statements = cleanContent.split(';');
    
    // Filter and clean statements
    const validStatements = [];
    
    for (const statement of statements) {
        const trimmed = statement.trim();
        
        if (trimmed.length > 10 && 
            !trimmed.startsWith('--') &&
            !trimmed.includes('pg_catalog') &&
            (trimmed.toUpperCase().includes('CREATE') ||
             trimmed.toUpperCase().includes('INSERT') ||
             trimmed.toUpperCase().includes('ALTER') ||
             trimmed.toUpperCase().includes('COPY'))) {
            validStatements.push(trimmed);
        }
    }
    
    return validStatements;
}

function isSkippableError(errorMessage) {
    const skippableErrors = [
        'already exists',
        'does not exist',
        'NOTICE',
        'duplicate key',
        'constraint',
        'relation already exists'
    ];
    
    return skippableErrors.some(error => errorMessage.includes(error));
}

async function verifyRestoration(client) {
    try {
        const tables = ['congregations', 'members', 'congregation_settings', 'songs', 'tasks'];
        const results = {};
        
        for (const table of tables) {
            try {
                const result = await client.query(`SELECT COUNT(*) FROM ${table};`);
                results[table] = result.rows[0].count;
            } catch (error) {
                results[table] = 'Table not found';
            }
        }
        
        return {
            congregations: results.congregations,
            members: results.members,
            settings: results.congregation_settings,
            songs: results.songs,
            tasks: results.tasks
        };
    } catch (error) {
        console.log('   ⚠️  Verification failed, but restoration may have succeeded');
        return {
            congregations: 'Unknown',
            members: 'Unknown',
            settings: 'Unknown',
            songs: 'Unknown',
            tasks: 'Unknown'
        };
    }
}

// Run the restoration
restoreSqlBackup()
    .catch((error) => {
        console.error('Failed to restore database:', error);
        process.exit(1);
    });
