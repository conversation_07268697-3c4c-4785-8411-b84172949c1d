'use client';

/**
 * Field Service Management Page
 *
 * Administrative interface for field service management with tabs:
 * - Service Schedule
 * - Service Groups
 * - Territories
 * - Service Records
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PlusIcon } from '@heroicons/react/24/outline';
import AdminFooter from '@/components/admin/AdminFooter';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface ServiceScheduleTime {
  id: string;
  serviceDate: string;
  serviceTime: string;
  location: string;
  address: string;
  conductorId?: string;
  conductor?: {
    id: string;
    name: string;
    role: string;
  };
  notes?: string;
}

interface ServiceGroup {
  id: string;
  name: string;
  groupNumber: number;
  overseerId?: string;
  assistantId?: string;
  overseer?: {
    id: string;
    name: string;
    role: string;
  };
  assistant?: {
    id: string;
    name: string;
    role: string;
  };
  address?: string;
  isActive: boolean;
}

interface Member {
  id: string;
  name: string;
  role: string;
}

interface Location {
  id: string;
  name: string;
  address: string;
  type: 'kingdom_hall' | 'zoom' | 'custom';
  zoomId?: string;
  zoomPassword?: string;
  zoomUrl?: string;
}

interface NewServiceSchedule {
  serviceDate: string;
  serviceTime: string;
  location: string;
  address: string;
  conductorId: string;
  notes?: string;
  zoomId?: string;
  zoomPassword?: string;
  zoomUrl?: string;
}

interface NewServiceGroup {
  name: string;
  groupNumber: number;
  overseerId: string;
  assistantId: string;
  address: string;
}

export default function FieldServiceAdminPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'schedule' | 'groups' | 'territories' | 'records'>('schedule');
  const [schedules, setSchedules] = useState<ServiceScheduleTime[]>([]);
  const [serviceGroups, setServiceGroups] = useState<ServiceGroup[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddGroupModal, setShowAddGroupModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<ServiceScheduleTime | null>(null);
  const [editingGroup, setEditingGroup] = useState<ServiceGroup | null>(null);
  const [selectedMonth, setSelectedMonth] = useState('2025-07');
  const [showHistoricalData, setShowHistoricalData] = useState(false);
  const [expandedDates, setExpandedDates] = useState<Set<string>>(new Set());
  const [showReassignConfirmation, setShowReassignConfirmation] = useState(false);
  const [reassignmentInfo, setReassignmentInfo] = useState<{
    memberName: string;
    currentGroup: number;
    newGroup: number;
    isOverseer: boolean;
    pendingAction: () => void;
  } | null>(null);
  const [showQuickEditModal, setShowQuickEditModal] = useState(false);
  const [quickEditData, setQuickEditData] = useState<{
    groupId: string;
    field: 'conductor' | 'assistant';
    currentValue: string;
    currentName: string;
  } | null>(null);

  // Generate dynamic month options
  const generateMonthOptions = () => {
    const options = [];
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();

    // Generate 12 months: 6 months back and 6 months forward
    for (let i = -6; i <= 6; i++) {
      const date = new Date(currentYear, currentMonth + i, 1);
      const year = date.getFullYear();
      const month = date.getMonth();
      const monthNames = [
        'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
        'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
      ];

      const value = `${year}-${(month + 1).toString().padStart(2, '0')}`;
      const label = `${monthNames[month]} ${year}`;

      options.push({ value, label });
    }

    return options;
  };

  const monthOptions = generateMonthOptions();
  const [newSchedule, setNewSchedule] = useState<NewServiceSchedule>({
    serviceDate: '',
    serviceTime: '',
    location: '',
    address: '',
    conductorId: '',
    notes: '',
    zoomId: '',
    zoomPassword: '',
    zoomUrl: ''
  });
  const [newGroup, setNewGroup] = useState<NewServiceGroup>({
    name: '',
    groupNumber: 1,
    overseerId: '',
    assistantId: '',
    address: ''
  });
  const [newLocation, setNewLocation] = useState({
    name: '',
    address: '',
    type: 'custom' as const,
    zoomId: '',
    zoomPassword: '',
    zoomUrl: ''
  });

  useEffect(() => {
    checkAdminAccess();
    loadServiceSchedules();
    loadMembers();
    loadServiceGroups();
    loadLocations();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Reload schedules when historical view changes
  useEffect(() => {
    loadServiceSchedules();
  }, [showHistoricalData]);

  const checkAdminAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();

      // Check if user has permission to view service statistics
      if (!['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(data.user.role)) {
        alert('No tienes permisos para acceder a esta sección');
        router.push('/admin');
        return;
      }

      setUser(data.user);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const loadServiceSchedules = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/service-schedules', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();

        // The API returns a weekly schedule structure, we need to extract service times
        const weeklySchedule = data.schedule;
        const allServiceTimes = weeklySchedule?.serviceTimes || [];

        console.log('📅 API Response:', { weeklySchedule, serviceTimesCount: allServiceTimes.length });
        console.log('📅 Raw service times:', allServiceTimes);

        // Filter schedules based on historical view
        const currentDate = new Date();
        const currentDateString = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD format

        console.log('🗓️ Current date for filtering:', currentDateString);
        console.log('🗓️ Current date object:', currentDate);
        console.log('🔄 Historical mode:', showHistoricalData);

        const filteredSchedules = allServiceTimes.filter((serviceTime: any) => {
          // Parse dates as strings to avoid timezone conversion issues
          const serviceDateStr = serviceTime.serviceDate.split('T')[0]; // Extract YYYY-MM-DD
          const todayStr = new Date().toISOString().split('T')[0]; // Get today as YYYY-MM-DD

          if (showHistoricalData) {
            // Show past schedules (before today)
            return serviceDateStr < todayStr;
          } else {
            // Show upcoming schedules (today and future)
            return serviceDateStr >= todayStr;
          }
        });

        setSchedules(filteredSchedules);
        console.log(`✅ Loaded ${filteredSchedules.length} ${showHistoricalData ? 'historical' : 'upcoming'} schedules`);
      } else {
        console.error('❌ Failed to load service schedules:', response.status, response.statusText);
        setSchedules([]);
      }
    } catch (error) {
      console.error('Error loading service schedules:', error);
      // Don't add hardcoded data - leave empty if API fails
      setSchedules([]);
    }
  };

  const loadMembers = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/members', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMembers(data.members || []);
      } else {
        console.error('Failed to load members:', response.status, response.statusText);
        setMembers([]);
      }
    } catch (error) {
      console.error('Error loading members:', error);
      setMembers([]);
    }
  };

  const loadServiceGroups = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/service-groups', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setServiceGroups(data.groups || []);
      } else {
        console.error('Failed to load service groups:', response.status, response.statusText);
        setServiceGroups([]);
      }
    } catch (error) {
      console.error('Error loading service groups:', error);
      setServiceGroups([]);
    }
  };

  const loadLocations = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/locations', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setLocations(data.locations || []);
      } else {
        console.error('Failed to load locations:', response.status, response.statusText);
        // Fallback to basic locations if API fails
        setLocations([
          {
            id: 'kingdom_hall',
            name: 'Salón del Reino',
            address: '7790 West 4th Av Hialeah Fl 33014',
            type: 'kingdom_hall'
          },
          {
            id: 'zoom',
            name: 'Zoom',
            address: 'Reunión Virtual',
            type: 'zoom'
          }
        ]);
      }
    } catch (error) {
      console.error('Error loading locations:', error);
      setLocations([]);
    }
  };

  const handleEdit = (scheduleId: string) => {
    const schedule = schedules.find(s => s.id === scheduleId);
    if (schedule) {
      // Check if this is an upcoming schedule (can be edited)
      const scheduleDate = new Date(schedule.serviceDate);
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);
      scheduleDate.setHours(0, 0, 0, 0);

      if (scheduleDate < currentDate) {
        alert('No se pueden editar horarios pasados');
        return;
      }

      setEditingSchedule(schedule);
      setNewSchedule({
        serviceDate: schedule.serviceDate,
        serviceTime: schedule.serviceTime,
        location: schedule.location,
        address: schedule.address || '',
        conductorId: schedule.conductorId || '',
        notes: schedule.notes || '',
        zoomId: '',
        zoomPassword: '',
        zoomUrl: ''
      });
      setShowEditModal(true);
    }
  };

  const checkForExistingAssignment = (memberId: string, newGroupNumber: number, isOverseer: boolean): boolean => {
    const member = members.find(m => m.id === memberId);
    if (!member) return false;

    // Check if member is already assigned to any group as overseer or assistant
    const existingAssignment = serviceGroups.find(group =>
      group.overseerId === memberId || group.assistantId === memberId
    );

    if (existingAssignment && existingAssignment.groupNumber !== newGroupNumber) {
      const isCurrentOverseer = existingAssignment.overseerId === memberId;
      setReassignmentInfo({
        memberName: member.name,
        currentGroup: existingAssignment.groupNumber,
        newGroup: newGroupNumber,
        isOverseer: isCurrentOverseer,
        pendingAction: () => {
          // This will be set by the calling function
        }
      });
      setShowReassignConfirmation(true);
      return true;
    }
    return false;
  };

  const handleEditGroup = (groupId: string) => {
    const group = serviceGroups.find(g => g.id === groupId);
    if (group) {
      setEditingGroup(group);
      setNewGroup({
        name: group.name,
        groupNumber: group.groupNumber,
        overseerId: group.overseerId || '',
        assistantId: group.assistantId || '',
        address: group.address || ''
      });
      setShowAddGroupModal(true);
    }
  };

  const handleDeleteGroup = async (groupId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este grupo?')) {
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch(`/api/admin/service-groups?id=${groupId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        console.log('✅ Group deleted successfully');
        await loadServiceGroups(); // Reload from database
      } else {
        const errorText = await response.text();
        console.error('❌ Failed to delete group:', errorText);
        alert('Error al eliminar el grupo');
      }
    } catch (error) {
      console.error('Error deleting group:', error);
      alert('Error al eliminar el grupo');
    }
  };

  const handleQuickEdit = (groupId: string, field: 'conductor' | 'assistant') => {
    const group = serviceGroups.find(g => g.id === groupId);
    if (!group) return;

    const currentValue = field === 'conductor' ? group.overseerId || '' : group.assistantId || '';
    const currentName = field === 'conductor'
      ? group.overseer?.name || 'Sin asignar'
      : group.assistant?.name || 'Sin asignar';

    setQuickEditData({
      groupId,
      field,
      currentValue,
      currentName
    });
    setShowQuickEditModal(true);
  };

  const handleQuickEditSave = async (newMemberId: string) => {
    if (!quickEditData) return;

    try {
      const token = localStorage.getItem('hermanos_token');
      const updateData = {
        id: quickEditData.groupId,
        [quickEditData.field === 'conductor' ? 'overseerId' : 'assistantId']: newMemberId || null
      };

      const response = await fetch('/api/admin/service-groups', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        console.log('✅ Group updated successfully');
        await loadServiceGroups(); // Reload from database
        setShowQuickEditModal(false);
        setQuickEditData(null);
      } else {
        const errorText = await response.text();
        console.error('❌ Failed to update group:', errorText);
        alert('Error al actualizar el grupo');
      }
    } catch (error) {
      console.error('Error updating group:', error);
      alert('Error al actualizar el grupo');
    }
  };

  const handleDelete = async (scheduleId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este horario?')) {
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/service-schedules', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timeId: scheduleId
        }),
      });

      if (response.ok) {
        console.log('✅ Schedule deleted successfully');
        // Reload schedules from database to ensure consistency
        await loadServiceSchedules();
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to delete schedule:', errorData);
        alert(`Error al eliminar el horario: ${errorData.message || errorData.error}`);
      }
    } catch (error) {
      console.error('Error deleting schedule:', error);
      alert('Error al eliminar el horario');
    }
  };

  const formatDate = (dateString: string | undefined | null) => {
    // Handle undefined, null, or empty dates
    if (!dateString || dateString === 'undefined' || dateString === 'null' || dateString.trim() === '') {
      return 'Seleccionar';
    }

    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Seleccionar';
    }

    const days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
    const dayName = days[date.getDay()];
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear().toString().slice(-2);
    return `${dayName}, ${month}/${day}/${year}`;
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '';

    // Parse the time string (HH:MM format)
    const [hours, minutes] = timeString.split(':');
    const hour24 = parseInt(hours, 10);
    const minute = parseInt(minutes, 10);

    // Convert to 12-hour format
    const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
    const ampm = hour24 >= 12 ? 'PM' : 'AM';

    // Format with leading zeros if needed
    const formattedMinute = minute.toString().padStart(2, '0');

    return `${hour12}:${formattedMinute} ${ampm}`;
  };

  const formatDateSpanish = (dateString: string) => {
    const date = new Date(dateString + 'T00:00:00'); // Avoid timezone issues
    const dayNames = ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado'];
    const monthNames = ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'];

    const dayName = dayNames[date.getDay()];
    const day = date.getDate();
    const month = monthNames[date.getMonth()];

    return `${dayName}, ${day} de ${month}`;
  };

  const groupSchedulesByDate = (schedules: any[]) => {
    const grouped: { [key: string]: any[] } = {};

    schedules.forEach(schedule => {
      const dateKey = schedule.serviceDate.split('T')[0]; // YYYY-MM-DD
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(schedule);
    });

    // Sort dates and sort schedules within each date by time
    Object.keys(grouped).forEach(dateKey => {
      grouped[dateKey].sort((a, b) => a.serviceTime.localeCompare(b.serviceTime));
    });

    return grouped;
  };

  const getWeekRange = (dateString: string) => {
    const date = new Date(dateString + 'T00:00:00');
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay()); // Sunday
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Saturday

    const monthNames = ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'];

    const startDay = startOfWeek.getDate();
    const endDay = endOfWeek.getDate();
    const startMonth = monthNames[startOfWeek.getMonth()];
    const endMonth = monthNames[endOfWeek.getMonth()];
    const year = endOfWeek.getFullYear();

    if (startMonth === endMonth) {
      return `${startDay}-${endDay} ${endMonth} de ${year}`;
    } else {
      return `${startDay} ${startMonth} - ${endDay} ${endMonth} de ${year}`;
    }
  };

  const groupSchedulesByWeek = (schedules: any[]) => {
    const weekGroups: { [key: string]: { [key: string]: any[] } } = {};

    schedules.forEach(schedule => {
      const dateKey = schedule.serviceDate.split('T')[0];
      const weekRange = getWeekRange(dateKey);

      if (!weekGroups[weekRange]) {
        weekGroups[weekRange] = {};
      }
      if (!weekGroups[weekRange][dateKey]) {
        weekGroups[weekRange][dateKey] = [];
      }
      weekGroups[weekRange][dateKey].push(schedule);
    });

    // Sort schedules within each date by time
    Object.keys(weekGroups).forEach(weekRange => {
      Object.keys(weekGroups[weekRange]).forEach(dateKey => {
        weekGroups[weekRange][dateKey].sort((a, b) => a.serviceTime.localeCompare(b.serviceTime));
      });
    });

    return weekGroups;
  };

  const toggleDateExpansion = (dateKey: string) => {
    const newExpanded = new Set(expandedDates);
    if (newExpanded.has(dateKey)) {
      newExpanded.delete(dateKey);
    } else {
      newExpanded.add(dateKey);
    }
    setExpandedDates(newExpanded);
  };

  const handleAddSchedule = async () => {
    if (!newSchedule.serviceDate || !newSchedule.serviceTime || !newSchedule.location || !newSchedule.conductorId) {
      alert('Por favor completa todos los campos');
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');

      // First, create or get the weekly schedule
      // Parse date without timezone conversion
      const serviceDateStr = newSchedule.serviceDate; // Already in YYYY-MM-DD format
      const [year, month, day] = serviceDateStr.split('-').map(Number);
      const serviceDate = new Date(year, month - 1, day); // Local date without timezone issues

      const weekStart = new Date(serviceDate);
      weekStart.setDate(serviceDate.getDate() - serviceDate.getDay()); // Start of week (Sunday)
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6); // End of week (Saturday)

      const weekStartStr = weekStart.toISOString().split('T')[0];
      const weekEndStr = weekEnd.toISOString().split('T')[0];

      // Create weekly schedule first
      const scheduleResponse = await fetch('/api/admin/service-schedules', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create_schedule',
          weekStartDate: weekStartStr,
          weekEndDate: weekEndStr
        }),
      });

      if (!scheduleResponse.ok) {
        const errorText = await scheduleResponse.text();
        console.error('❌ Failed to create weekly schedule:', errorText);
        alert('Error al crear el horario semanal');
        return;
      }

      const scheduleResult = await scheduleResponse.json();
      const scheduleId = scheduleResult.schedule?.id;

      if (!scheduleId) {
        console.error('❌ No schedule ID returned');
        alert('Error al obtener el ID del horario');
        return;
      }

      // Now add the service time
      const response = await fetch('/api/admin/service-schedules', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'add_service_time',
          scheduleId: scheduleId,
          serviceDate: newSchedule.serviceDate,
          serviceTime: newSchedule.serviceTime,
          location: newSchedule.location,
          address: newSchedule.address,
          conductorId: newSchedule.conductorId,
          notes: newSchedule.notes || '',
          zoomId: newSchedule.zoomId || '',
          zoomPassword: newSchedule.zoomPassword || '',
          zoomUrl: newSchedule.zoomUrl || ''
        }),
      });

      if (response.ok) {
        console.log('✅ Schedule created successfully');
        await loadServiceSchedules(); // Reload from database
        resetScheduleForm();
        setShowAddModal(false);
      } else {
        const errorText = await response.text();
        console.error('❌ Failed to create schedule:', errorText);
        alert('Error al crear el horario');
      }
    } catch (error) {
      console.error('Error adding schedule:', error);
      alert('Error al guardar el horario');
    }
  };

  const handleUpdateSchedule = async () => {
    if (!editingSchedule || !newSchedule.serviceDate || !newSchedule.serviceTime || !newSchedule.location || !newSchedule.conductorId) {
      alert('Por favor completa todos los campos');
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');
      const scheduleData = {
        serviceDate: newSchedule.serviceDate,
        serviceTime: newSchedule.serviceTime,
        location: newSchedule.location,
        address: newSchedule.address,
        conductorId: newSchedule.conductorId,
        notes: newSchedule.notes || '',
        zoomId: newSchedule.zoomId || '',
        zoomPassword: newSchedule.zoomPassword || '',
        zoomUrl: newSchedule.zoomUrl || ''
      };

      const response = await fetch('/api/admin/service-schedules', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingSchedule.id,
          ...scheduleData
        }),
      });

      if (response.ok) {
        console.log('✅ Schedule updated successfully');
        await loadServiceSchedules(); // Reload from database
        resetScheduleForm();
        setShowEditModal(false);
        setEditingSchedule(null);
      } else {
        const errorText = await response.text();
        console.error('❌ Failed to update schedule:', errorText);
        alert('Error al actualizar el horario');
      }
    } catch (error) {
      console.error('Error updating schedule:', error);
      alert('Error al actualizar el horario');
    }
  };

  const resetScheduleForm = () => {
    setNewSchedule({
      serviceDate: '',
      serviceTime: '',
      location: '',
      address: '',
      conductorId: '',
      notes: '',
      zoomId: '',
      zoomPassword: '',
      zoomUrl: ''
    });
  };

  const handleAddGroup = async () => {
    if (!newGroup.name || !newGroup.overseerId) {
      alert('Por favor completa los campos requeridos');
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');
      const groupData = {
        name: newGroup.name,
        groupNumber: newGroup.groupNumber,
        overseerId: newGroup.overseerId,
        assistantId: newGroup.assistantId || null,
        address: newGroup.address,
        isActive: true
      };

      if (editingGroup) {
        // Update existing group
        const response = await fetch('/api/admin/service-groups', {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: editingGroup.id,
            ...groupData
          }),
        });

        if (response.ok) {
          console.log('✅ Group updated successfully');
          await loadServiceGroups(); // Reload from database
          setEditingGroup(null);
        } else {
          const errorText = await response.text();
          console.error('❌ Failed to update group:', errorText);
          alert('Error al actualizar el grupo');
          return;
        }
      } else {
        // Add new group
        const response = await fetch('/api/admin/service-groups', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(groupData),
        });

        if (response.ok) {
          console.log('✅ Group created successfully');
          await loadServiceGroups(); // Reload from database
        } else {
          const errorText = await response.text();
          console.error('❌ Failed to create group:', errorText);
          alert('Error al crear el grupo');
          return;
        }
      }

      // Reset form and close modal
      resetGroupForm();
      setShowAddGroupModal(false);
    } catch (error) {
      console.error('Error saving group:', error);
      alert('Error al guardar el grupo');
    }
  };

  const resetGroupForm = () => {
    setNewGroup({
      name: '',
      groupNumber: serviceGroups.length + 1,
      overseerId: '',
      assistantId: '',
      address: ''
    });
  };

  const handleAddLocation = () => {
    if (!newLocation.name || !newLocation.address) {
      alert('Por favor completa todos los campos');
      return;
    }

    const locationEntry: Location = {
      id: Date.now().toString(),
      name: newLocation.name,
      address: newLocation.address,
      type: newLocation.type,
      zoomId: newLocation.zoomId || undefined,
      zoomPassword: newLocation.zoomPassword || undefined,
      zoomUrl: newLocation.zoomUrl || undefined
    };

    setLocations(prev => [...prev, locationEntry]);
    setNewLocation({
      name: '',
      address: '',
      type: 'custom',
      zoomId: '',
      zoomPassword: '',
      zoomUrl: ''
    });
    setShowLocationModal(false);
  };





  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-green-600 text-white p-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.push('/admin')}
              className="text-green-200 hover:text-white flex items-center"
            >
              ← Atrás
            </button>
            <h1 className="text-2xl font-bold">Servicio del Campo</h1>
            <div></div> {/* Spacer for centering */}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-0 sm:px-6 py-4 sm:py-6 pb-20">

        {/* Field Service Management Interface */}
        <div className="bg-white rounded-lg shadow-md">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('schedule')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'schedule'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Horarios
              </button>
              <button
                onClick={() => setActiveTab('groups')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'groups'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Grupos
              </button>
              <button
                onClick={() => setActiveTab('territories')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'territories'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Territorios
              </button>
              <button
                onClick={() => setActiveTab('records')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'records'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Informes
              </button>
            </nav>
          </div>

          {/* Content */}
          <div className="p-2 sm:p-6">
            {/* Service Schedule Tab */}
            {activeTab === 'schedule' && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Horarios de Servicio</h3>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => {
                        console.log('🔄 Toggle clicked! Current state:', showHistoricalData, '→ New state:', !showHistoricalData);
                        setShowHistoricalData(!showHistoricalData);
                      }}
                      className={`px-3 sm:px-4 py-2 rounded-md transition-colors flex items-center ${
                        showHistoricalData
                          ? 'bg-blue-600 text-white hover:bg-blue-700'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      title={showHistoricalData ? "Mostrar próximos" : "Mostrar históricos"}
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="hidden sm:inline ml-2">
                        {showHistoricalData ? "Próximos" : "Históricos"}
                      </span>
                    </button>
                    <button
                      onClick={() => setShowAddModal(true)}
                      className="bg-green-600 text-white px-3 sm:px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center"
                    >
                      <PlusIcon className="h-5 w-5" />
                      <span className="hidden sm:inline ml-2">Agregar Horario</span>
                    </button>
                  </div>
                </div>

                {/* Schedule Display */}
                {showHistoricalData ? (
                  /* Historical Collapsible View */
                  <div className="space-y-4">
                    {Object.keys(groupSchedulesByWeek(schedules)).length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p className="mt-2">No hay horarios históricos</p>
                      </div>
                    ) : (
                      Object.entries(groupSchedulesByWeek(schedules))
                        .sort(([a], [b]) => b.localeCompare(a)) // Sort weeks descending (newest first)
                        .map(([weekRange, weekSchedules]) => (
                          <div key={weekRange} className="space-y-2">
                            {/* Week Range Header */}
                            <div className="text-center py-2 bg-gray-100 rounded-lg">
                              <span className="text-sm font-medium text-gray-700">{weekRange}</span>
                            </div>

                            {/* Days in the week */}
                            {Object.entries(weekSchedules)
                              .sort(([a], [b]) => b.localeCompare(a)) // Sort dates descending
                              .map(([dateKey, daySchedules]) => (
                                <div key={dateKey} className="border border-gray-200 rounded-lg overflow-hidden">
                                  <button
                                    onClick={() => toggleDateExpansion(dateKey)}
                                    className="w-full bg-green-600 text-white px-4 py-3 text-left hover:bg-green-700 transition-colors flex items-center justify-between"
                                  >
                                    <span className="font-medium">{formatDateSpanish(dateKey)}</span>
                                    <svg
                                      className={`h-5 w-5 transform transition-transform ${
                                        expandedDates.has(dateKey) ? 'rotate-180' : ''
                                      }`}
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                  </button>
                            {expandedDates.has(dateKey) && (
                              <div className="bg-white">
                                {daySchedules.map((schedule, index) => (
                                  <div key={schedule.id} className={`p-4 ${index !== daySchedules.length - 1 ? 'border-b border-gray-100' : ''}`}>
                                    <div className="space-y-2">
                                      {/* Time */}
                                      <div className="text-lg font-semibold text-gray-900">
                                        {formatTime(schedule.serviceTime)}
                                      </div>

                                      {/* Location */}
                                      <div className="text-sm text-gray-600">
                                        {schedule.location}
                                      </div>

                                      {/* Address */}
                                      {schedule.address && (
                                        <div className="text-sm text-gray-500">
                                          {schedule.address}
                                        </div>
                                      )}

                                      {/* Conductor */}
                                      <div className="text-sm text-gray-700">
                                        <span className="font-medium">Conductor</span>
                                        <br />
                                        {schedule.conductor?.name || 'Sin asignar'}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                                </div>
                              ))}
                          </div>
                        ))
                    )}
                  </div>
                ) : (
                  /* Table View for Upcoming Schedules */
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Fecha
                        </th>
                        <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Hora
                        </th>
                        <th className="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ubicación
                        </th>
                        <th className="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Dirección
                        </th>
                        <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Conductor
                        </th>
                        <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Acciones
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {schedules.length === 0 ? (
                        <tr>
                          <td colSpan={6} className="px-2 sm:px-6 py-12 text-center text-gray-500 text-sm">
                            No se encontraron horarios de servicio. Haz clic en "Agregar Horario" para crear uno.
                          </td>
                        </tr>
                      ) : (
                        schedules.map((schedule) => {
                          const scheduleDate = new Date(schedule.serviceDate);
                          const currentDate = new Date();
                          currentDate.setHours(0, 0, 0, 0);
                          scheduleDate.setHours(0, 0, 0, 0);
                          const isPastSchedule = scheduleDate < currentDate;

                          return (
                          <tr
                            key={schedule.id}
                            className={`hover:bg-gray-50 cursor-pointer ${isPastSchedule ? 'opacity-60' : ''}`}
                            onDoubleClick={() => !isPastSchedule && handleEdit(schedule.id)}
                          >
                            <td className="px-2 sm:px-6 py-3 sm:py-4 text-sm sm:text-base text-gray-900">
                              <div className="truncate max-w-[80px] sm:max-w-none">
                                {formatDate(schedule.serviceDate)}
                              </div>
                            </td>
                            <td className="px-2 sm:px-6 py-3 sm:py-4 text-sm sm:text-base text-gray-900">
                              {formatTime(schedule.serviceTime)}
                            </td>
                            <td className="hidden sm:table-cell px-6 py-4 whitespace-nowrap text-base text-gray-900">
                              {schedule.location}
                            </td>
                            <td className="hidden sm:table-cell px-6 py-4 whitespace-nowrap text-base text-gray-900">
                              {schedule.address}
                            </td>
                            <td className="px-2 sm:px-6 py-3 sm:py-4 text-sm sm:text-base text-gray-900">
                              <div className="truncate max-w-[80px] sm:max-w-none">
                                {schedule.conductor?.name || 'Sin asignar'}
                              </div>
                            </td>
                            <td className="px-2 sm:px-6 py-3 sm:py-4 text-sm sm:text-base text-gray-500">
                              <div className="flex space-x-1 sm:space-x-3">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (!isPastSchedule) {
                                      handleEdit(schedule.id);
                                    }
                                  }}
                                  className={`p-1 ${isPastSchedule ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:text-blue-900'}`}
                                  title={isPastSchedule ? 'No se pueden editar horarios pasados' : 'Editar'}
                                  disabled={isPastSchedule}
                                >
                                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDelete(schedule.id);
                                  }}
                                  className="text-red-600 hover:text-red-900 p-1"
                                  title="Eliminar"
                                >
                                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                          );
                        })
                      )}
                    </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}

            {/* Service Groups Tab */}
            {activeTab === 'groups' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold">Grupos de Servicio</h3>
                  <button
                    onClick={() => {
                      setEditingGroup(null);
                      resetGroupForm();
                      setShowAddGroupModal(true);
                    }}
                    className="bg-green-600 text-white px-3 sm:px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center"
                  >
                    <PlusIcon className="h-5 w-5" />
                    <span className="hidden sm:inline ml-2">Agregar Grupo</span>
                  </button>
                </div>

                {/* Service Groups Table */}
                <div className="overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Group #
                        </th>
                        <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Conductor
                        </th>
                        <th className="hidden sm:table-cell px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Auxiliar
                        </th>
                        <th className="hidden sm:table-cell px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Address
                        </th>
                        <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {serviceGroups.length === 0 ? (
                        <tr>
                          <td colSpan={5} className="px-2 sm:px-6 py-12 text-center text-gray-500">
                            No se encontraron grupos de servicio. Haz clic en "Agregar Grupo" para crear uno.
                          </td>
                        </tr>
                      ) : (
                        serviceGroups.map((group) => (
                          <tr key={group.id} className="hover:bg-gray-50">
                            <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {group.groupNumber}
                            </td>
                            <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div
                                onDoubleClick={() => handleQuickEdit(group.id, 'conductor')}
                                className="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded border border-transparent hover:border-gray-300 transition-colors"
                                title="Doble clic para editar"
                              >
                                {group.overseer?.name || 'Sin asignar'}
                              </div>
                            </td>
                            <td className="hidden sm:table-cell px-2 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div
                                onDoubleClick={() => handleQuickEdit(group.id, 'assistant')}
                                className="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded border border-transparent hover:border-gray-300 transition-colors"
                                title="Doble clic para editar"
                              >
                                {group.assistant?.name || 'Sin auxiliar'}
                              </div>
                            </td>
                            <td className="hidden sm:table-cell px-2 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900">
                              <div className="truncate max-w-[100px] sm:max-w-none">
                                {group.address || 'Sin dirección'}
                              </div>
                            </td>
                            <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex space-x-3">
                                <button
                                  onClick={() => handleEditGroup(group.id)}
                                  className="text-blue-600 hover:text-blue-900 p-1"
                                  title="Editar"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                </button>
                                <button
                                  onClick={() => handleDeleteGroup(group.id)}
                                  className="text-red-600 hover:text-red-900 p-1"
                                  title="Eliminar"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'territories' && (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Territorios</h3>
                <p className="text-gray-500">La gestión de territorios se implementará aquí.</p>
              </div>
            )}

            {activeTab === 'records' && (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Informes de Servicio</h3>
                <p className="text-gray-500">La gestión de informes de servicio se implementará aquí.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Add Service Schedule Modal */}
      {showAddModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowAddModal(false);
              resetScheduleForm();
            }
          }}
        >
          <div className="bg-white rounded-lg p-3 sm:p-6 w-full max-w-sm sm:max-w-md mx-2 sm:mx-4 max-h-[95vh] overflow-y-auto">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Agregar Horario de Servicio</h3>

            <div className="space-y-3 sm:space-y-4">
              {/* Date and Time in one row when Zoom is selected */}
              {newSchedule.location === 'Zoom' ? (
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Fecha
                    </label>
                    <input
                      type="date"
                      value={newSchedule.serviceDate}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, serviceDate: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Hora
                    </label>
                    <input
                      type="time"
                      value={newSchedule.serviceTime}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, serviceTime: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                </div>
              ) : (
                <>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Fecha
                    </label>
                    <input
                      type="date"
                      value={newSchedule.serviceDate}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, serviceDate: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>

                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Hora
                    </label>
                    <input
                      type="time"
                      value={newSchedule.serviceTime}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, serviceTime: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                </>
              )}

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                  Ubicación
                </label>
                <select
                  value={newSchedule.location}
                  onChange={(e) => {
                    if (e.target.value === 'add_new') {
                      setShowLocationModal(true);
                      return;
                    }
                    const selectedLocation = locations.find(l => l.name === e.target.value);
                    setNewSchedule(prev => ({
                      ...prev,
                      location: e.target.value,
                      address: selectedLocation?.address || '',
                      zoomId: selectedLocation?.zoomId || '',
                      zoomPassword: selectedLocation?.zoomPassword || '',
                      zoomUrl: selectedLocation?.zoomUrl || ''
                    }));
                  }}
                  className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Seleccionar ubicación</option>
                  {locations.map((location) => (
                    <option key={location.id} value={location.name}>
                      {location.name}
                    </option>
                  ))}
                  <option value="add_new">+ Añadir Nueva</option>
                </select>
              </div>

              {newSchedule.location === 'Zoom' && (
                <>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      URL de Zoom
                    </label>
                    <input
                      type="url"
                      value={newSchedule.zoomUrl}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, zoomUrl: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="https://zoom.us/j/1234567890"
                    />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Meeting ID
                    </label>
                    <input
                      type="text"
                      value={newSchedule.zoomId}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, zoomId: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="123 456 7890"
                    />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Passcode
                    </label>
                    <input
                      type="text"
                      value={newSchedule.zoomPassword}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, zoomPassword: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Código de acceso"
                    />
                  </div>
                </>
              )}

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                  Dirección
                </label>
                <input
                  type="text"
                  value={newSchedule.address}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Dirección o ID de reunión"
                />
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                  Conductor
                </label>
                <select
                  value={newSchedule.conductorId}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, conductorId: e.target.value }))}
                  className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Seleccionar conductor</option>
                  {members.map((member) => (
                    <option key={member.id} value={member.id}>
                      {member.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                  Notas (Opcional)
                </label>
                <textarea
                  value={newSchedule.notes}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  rows={2}
                  placeholder="Notas adicionales..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 sm:space-x-3 mt-4 sm:mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false);
                  resetScheduleForm();
                }}
                className="px-3 sm:px-4 py-1.5 sm:py-2 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancelar
              </button>
              <button
                onClick={handleAddSchedule}
                className="px-3 sm:px-4 py-1.5 sm:py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Agregar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Service Schedule Modal */}
      {showEditModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowEditModal(false);
              setEditingSchedule(null);
              resetScheduleForm();
            }
          }}
        >
          <div className="bg-white rounded-lg p-3 sm:p-6 w-full max-w-sm sm:max-w-md mx-2 sm:mx-4 max-h-[95vh] overflow-y-auto">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Editar Horario de Servicio</h3>

            <div className="space-y-3 sm:space-y-4">
              {/* Date and Time in one row when Zoom is selected */}
              {newSchedule.location === 'Zoom' ? (
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Fecha
                    </label>
                    <input
                      type="date"
                      value={newSchedule.serviceDate}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, serviceDate: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Hora
                    </label>
                    <input
                      type="time"
                      value={newSchedule.serviceTime}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, serviceTime: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                </div>
              ) : (
                <>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Fecha
                    </label>
                    <input
                      type="date"
                      value={newSchedule.serviceDate}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, serviceDate: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>

                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Hora
                    </label>
                    <input
                      type="time"
                      value={newSchedule.serviceTime}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, serviceTime: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                </>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ubicación
                </label>
                <select
                  value={newSchedule.location}
                  onChange={(e) => {
                    if (e.target.value === 'add_new') {
                      setShowLocationModal(true);
                      return;
                    }
                    const selectedLocation = locations.find(l => l.name === e.target.value);
                    setNewSchedule(prev => ({
                      ...prev,
                      location: e.target.value,
                      address: selectedLocation?.address || '',
                      zoomId: selectedLocation?.zoomId || '',
                      zoomPassword: selectedLocation?.zoomPassword || '',
                      zoomUrl: selectedLocation?.zoomUrl || ''
                    }));
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Seleccionar ubicación</option>
                  {locations.map((location) => (
                    <option key={location.id} value={location.name}>
                      {location.name}
                    </option>
                  ))}
                  <option value="add_new">+ Añadir Nueva</option>
                </select>
              </div>

              {newSchedule.location === 'Zoom' && (
                <>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      URL de Zoom
                    </label>
                    <input
                      type="url"
                      value={newSchedule.zoomUrl}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, zoomUrl: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="https://zoom.us/j/1234567890"
                    />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Meeting ID
                    </label>
                    <input
                      type="text"
                      value={newSchedule.zoomId}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, zoomId: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="123 456 7890"
                    />
                  </div>
                  <div>
                    <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                      Passcode
                    </label>
                    <input
                      type="text"
                      value={newSchedule.zoomPassword}
                      onChange={(e) => setNewSchedule(prev => ({ ...prev, zoomPassword: e.target.value }))}
                      className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Código de acceso"
                    />
                  </div>
                </>
              )}

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                  Dirección
                </label>
                <input
                  type="text"
                  value={newSchedule.address}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Dirección o ID de reunión"
                />
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                  Conductor
                </label>
                <select
                  value={newSchedule.conductorId}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, conductorId: e.target.value }))}
                  className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Seleccionar conductor</option>
                  {members.map((member) => (
                    <option key={member.id} value={member.id}>
                      {member.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                  Notas (Opcional)
                </label>
                <textarea
                  value={newSchedule.notes}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  rows={2}
                  placeholder="Notas adicionales..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 sm:space-x-3 mt-4 sm:mt-6">
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingSchedule(null);
                  resetScheduleForm();
                }}
                className="px-3 sm:px-4 py-1.5 sm:py-2 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancelar
              </button>
              <button
                onClick={handleUpdateSchedule}
                className="px-3 sm:px-4 py-1.5 sm:py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Actualizar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add/Edit Service Group Modal */}
      {showAddGroupModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {editingGroup ? 'Editar Grupo de Servicio' : 'Agregar Grupo de Servicio'}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre del Grupo
                </label>
                <input
                  type="text"
                  value={newGroup.name}
                  onChange={(e) => setNewGroup(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Grupo 1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Número de Grupo
                </label>
                <input
                  type="number"
                  value={newGroup.groupNumber}
                  onChange={(e) => setNewGroup(prev => ({ ...prev, groupNumber: parseInt(e.target.value) || 1 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  min="1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Conductor Asignado
                </label>
                <select
                  value={newGroup.overseerId}
                  onChange={(e) => setNewGroup(prev => ({ ...prev, overseerId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Seleccionar conductor</option>
                  {members.filter(m => ['elder', 'ministerial_servant', 'coordinator'].includes(m.role.toLowerCase())).map((member) => (
                    <option key={member.id} value={member.id}>
                      {member.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Auxiliar (Opcional)
                </label>
                <select
                  value={newGroup.assistantId}
                  onChange={(e) => setNewGroup(prev => ({ ...prev, assistantId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Sin auxiliar</option>
                  {members.filter(m => ['elder', 'ministerial_servant', 'coordinator'].includes(m.role.toLowerCase()) && m.id !== newGroup.overseerId).map((member) => (
                    <option key={member.id} value={member.id}>
                      {member.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Dirección
                </label>
                <input
                  type="text"
                  value={newGroup.address}
                  onChange={(e) => setNewGroup(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Dirección del grupo"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddGroupModal(false);
                  setEditingGroup(null);
                  resetGroupForm();
                }}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancelar
              </button>
              <button
                onClick={handleAddGroup}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                {editingGroup ? 'Actualizar' : 'Agregar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Location Modal */}
      {showLocationModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Añadir Nueva Ubicación</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre de la Ubicación
                </label>
                <input
                  type="text"
                  value={newLocation.name}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Casa de Juan"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tipo
                </label>
                <select
                  value={newLocation.type}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, type: e.target.value as 'kingdom_hall' | 'zoom' | 'custom' }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="custom">Ubicación Personalizada</option>
                  <option value="kingdom_hall">Salón del Reino</option>
                  <option value="zoom">Zoom</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Dirección
                </label>
                <input
                  type="text"
                  value={newLocation.address}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="123 Main St, Miami, FL"
                />
              </div>

              {newLocation.type === 'zoom' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      ID de Zoom
                    </label>
                    <input
                      type="text"
                      value={newLocation.zoomId}
                      onChange={(e) => setNewLocation(prev => ({ ...prev, zoomId: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="123 456 7890"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contraseña de Zoom
                    </label>
                    <input
                      type="text"
                      value={newLocation.zoomPassword}
                      onChange={(e) => setNewLocation(prev => ({ ...prev, zoomPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="password123"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      URL de Zoom
                    </label>
                    <input
                      type="url"
                      value={newLocation.zoomUrl}
                      onChange={(e) => setNewLocation(prev => ({ ...prev, zoomUrl: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="https://zoom.us/j/1234567890"
                    />
                  </div>
                </>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowLocationModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancelar
              </button>
              <button
                onClick={handleAddLocation}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Agregar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Group Reassignment Confirmation Modal */}
      {showReassignConfirmation && reassignmentInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Confirmar Reasignación
            </h3>
            <p className="text-gray-700 mb-6">
              <strong>{reassignmentInfo.memberName}</strong> ya está asignado al Grupo {reassignmentInfo.currentGroup} como{' '}
              {reassignmentInfo.isOverseer ? 'Conductor' : 'Auxiliar'}.
              ¿Desea cambiarlo al Grupo {reassignmentInfo.newGroup}?
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowReassignConfirmation(false);
                  setReassignmentInfo(null);
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={() => {
                  if (reassignmentInfo.pendingAction) {
                    reassignmentInfo.pendingAction();
                  }
                  setShowReassignConfirmation(false);
                  setReassignmentInfo(null);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Confirmar Cambio
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Quick Edit Modal */}
      {showQuickEditModal && quickEditData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Editar {quickEditData.field === 'conductor' ? 'Conductor' : 'Auxiliar'}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Grupo {serviceGroups.find(g => g.id === quickEditData.groupId)?.groupNumber}
            </p>
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Seleccionar {quickEditData.field === 'conductor' ? 'Conductor' : 'Auxiliar'}
              </label>
              <select
                defaultValue={quickEditData.currentValue}
                onChange={(e) => handleQuickEditSave(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">
                  {quickEditData.field === 'conductor' ? 'Sin asignar' : 'Sin auxiliar'}
                </option>
                {members
                  .filter(m => {
                    const validRoles = ['elder', 'ministerial_servant', 'coordinator'].includes(m.role.toLowerCase());
                    if (quickEditData.field === 'assistant') {
                      const currentGroup = serviceGroups.find(g => g.id === quickEditData.groupId);
                      return validRoles && m.id !== currentGroup?.overseerId;
                    }
                    return validRoles;
                  })
                  .map((member) => (
                    <option key={member.id} value={member.id}>
                      {member.name}
                    </option>
                  ))}
              </select>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowQuickEditModal(false);
                  setQuickEditData(null);
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Admin Footer */}
      <AdminFooter currentSection="territorios" />
    </div>
  );
}
