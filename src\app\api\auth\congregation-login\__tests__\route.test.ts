/**
 * Tests for Congregation Login API Endpoint
 *
 * Tests the enhanced authentication system with region validation,
 * role-based access control, and JWT token generation.
 */

import { NextRequest } from 'next/server';
import { POST } from '../route';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    congregation: {
      findUnique: jest.fn(),
    },
    member: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
    },
  },
}));

// Mock bcrypt
jest.mock('bcryptjs', () => ({
  compare: jest.fn(),
}));

// Mock JWT
jest.mock('@/lib/auth/simpleJWT', () => ({
  SimpleJWTManager: {
    generateToken: jest.fn(() => 'mock-jwt-token'),
    generateAdminToken: jest.fn(() => 'mock-admin-jwt-token'),
  },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('/api/auth/congregation-login', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createMockRequest = (body: any) => {
    const request = {
      json: jest.fn().mockResolvedValue(body),
      method: 'POST',
      headers: new Headers({
        'Content-Type': 'application/json',
      }),
      url: 'http://localhost:3000/api/auth/congregation-login',
    } as unknown as NextRequest;

    return request;
  };

  const mockCongregation = {
    id: 'CORALOES',
    name: 'Coral Oeste',
    region: 'america-central',
    pin: '$2b$12$hashedpin',
    language: 'es',
    timezone: 'America/Mexico_City',
    isActive: true,
  };

  const mockMember = {
    id: 'member-1',
    name: 'Test Elder',
    email: '<EMAIL>',
    role: 'elder',
    congregationId: 'CORALOES',
    pin: '$2b$12$hashedmemberpin',
    isActive: true,
  };

  describe('Successful Authentication', () => {
    it('should authenticate congregation with valid credentials including region', async () => {
      const requestBody = {
        region: 'america-central',
        congregationId: 'CORALOES',
        pin: 'validpin',
        rememberMe: false,
      };

      mockPrisma.congregation.findUnique.mockResolvedValue(mockCongregation);
      mockPrisma.member.findFirst.mockResolvedValue(mockMember);
      mockPrisma.member.findMany.mockResolvedValue([mockMember]);
      mockBcrypt.compare.mockResolvedValue(true);

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.token).toBe('mock-jwt-token');
      expect(data.user).toEqual({
        id: mockMember.id,
        name: mockMember.name,
        role: mockMember.role,
        congregationId: mockMember.congregationId,
        congregationName: mockCongregation.name,
      });
      expect(data.congregation).toEqual({
        id: mockCongregation.id,
        name: mockCongregation.name,
        language: mockCongregation.language,
        timezone: mockCongregation.timezone,
      });
    });

    it('should work with congregation without region specified', async () => {
      const requestBody = {
        region: 'america-central',
        congregationId: 'CORALOES',
        pin: 'validpin',
        rememberMe: false,
      };

      const congregationWithoutRegion = { ...mockCongregation, region: null };
      mockPrisma.congregation.findUnique.mockResolvedValue(congregationWithoutRegion);
      mockPrisma.member.findFirst.mockResolvedValue(mockMember);
      mockPrisma.member.findMany.mockResolvedValue([mockMember]);
      mockBcrypt.compare.mockResolvedValue(true);

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });

  describe('Validation Errors', () => {
    it('should reject request without region', async () => {
      const requestBody = {
        congregationId: 'CORALOES',
        pin: 'validpin',
        rememberMe: false,
      };

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid input data');
      expect(data.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            path: ['region'],
            message: 'Region is required',
          }),
        ])
      );
    });

    it('should reject request without congregation ID', async () => {
      const requestBody = {
        region: 'america-central',
        pin: 'validpin',
        rememberMe: false,
      };

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid input data');
    });

    it('should reject request without PIN', async () => {
      const requestBody = {
        region: 'america-central',
        congregationId: 'CORALOES',
        rememberMe: false,
      };

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid input data');
    });
  });

  describe('Authentication Failures', () => {
    it('should reject invalid congregation ID', async () => {
      const requestBody = {
        region: 'america-central',
        congregationId: 'INVALID',
        pin: 'validpin',
        rememberMe: false,
      };

      mockPrisma.congregation.findUnique.mockResolvedValue(null);

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Congregation not found or inactive');
    });

    it('should reject invalid region for congregation', async () => {
      const requestBody = {
        region: 'europa',
        congregationId: 'CORALOES',
        pin: 'validpin',
        rememberMe: false,
      };

      mockPrisma.congregation.findUnique.mockResolvedValue(mockCongregation);

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid region for this congregation');
    });

    it('should reject invalid PIN', async () => {
      const requestBody = {
        region: 'america-central',
        congregationId: 'CORALOES',
        pin: 'invalidpin',
        rememberMe: false,
      };

      mockPrisma.congregation.findUnique.mockResolvedValue(mockCongregation);
      mockBcrypt.compare.mockResolvedValue(false);

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Invalid congregation PIN');
    });
  });

  describe('Role-Based Access Control', () => {
    it('should provide admin permissions for elder role', async () => {
      const requestBody = {
        region: 'america-central',
        congregationId: 'CORALOES',
        pin: 'validpin',
        rememberMe: false,
      };

      mockPrisma.congregation.findUnique.mockResolvedValue(mockCongregation);
      mockPrisma.member.findFirst.mockResolvedValue(mockMember);
      mockPrisma.member.findMany.mockResolvedValue([mockMember]);
      mockBcrypt.compare.mockResolvedValue(true);

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.permissions.canAccessAdmin).toBe(true);
      expect(data.permissions.canManageSettings).toBe(false); // Only overseer/coordinator can manage settings
      expect(data.permissions.canExtendToken).toBe(true);
    });

    it('should provide limited permissions for publisher role', async () => {
      const publisherMember = { ...mockMember, role: 'publisher' };
      const requestBody = {
        region: 'america-central',
        congregationId: 'CORALOES',
        pin: 'validpin',
        rememberMe: false,
      };

      mockPrisma.congregation.findUnique.mockResolvedValue(mockCongregation);
      mockPrisma.member.findFirst.mockResolvedValue(publisherMember);
      mockPrisma.member.findMany.mockResolvedValue([publisherMember]);
      mockBcrypt.compare.mockResolvedValue(true);

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.permissions.canAccessAdmin).toBe(false);
      expect(data.permissions.canManageSettings).toBe(false);
      expect(data.permissions.canExtendToken).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const requestBody = {
        region: 'america-central',
        congregationId: 'CORALOES',
        pin: 'validpin',
        rememberMe: false,
      };

      mockPrisma.congregation.findUnique.mockRejectedValue(new Error('Database error'));

      const request = createMockRequest(requestBody);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Authentication failed');
      expect(data.timestamp).toBeDefined();
    });
  });
});
