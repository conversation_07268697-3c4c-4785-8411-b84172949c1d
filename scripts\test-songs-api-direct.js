/**
 * Test Songs API Direct
 *
 * Tests the songs API endpoint directly with authentication
 */

// Using built-in fetch (Node.js 18+)

async function testSongsAPI() {
  try {
    console.log('🎵 Testing Songs API Direct...\n');

    // First, let's try to authenticate with member login
    console.log('🔐 Attempting member authentication...');

    const loginResponse = await fetch('http://localhost:3001/api/auth/member-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        congregationId: '1441',
        email: '<EMAIL>',
        pin: '1234'
      })
    });

    if (!loginResponse.ok) {
      console.error('❌ Authentication failed:', loginResponse.status);
      const errorText = await loginResponse.text();
      console.error('Error details:', errorText);
      return;
    }

    const authData = await loginResponse.json();
    console.log('✅ Authentication successful');
    console.log('Member:', authData.member.name);
    console.log('Role:', authData.member.role);

    // Now test the songs API
    console.log('\n📋 Testing songs API...');

    const songsResponse = await fetch('http://localhost:3001/api/songs?limit=10', {
      headers: {
        'Authorization': `Bearer ${authData.token}`,
      },
    });

    if (!songsResponse.ok) {
      console.error('❌ Songs API failed:', songsResponse.status);
      const errorText = await songsResponse.text();
      console.error('Error details:', errorText);
      return;
    }

    const songsData = await songsResponse.json();
    console.log('✅ Songs API successful');
    console.log('Total songs returned:', songsData.songs.length);
    console.log('Pagination:', songsData.pagination);

    if (songsData.songs.length > 0) {
      console.log('\n📋 First 5 songs:');
      songsData.songs.slice(0, 5).forEach(song => {
        console.log(`${song.songNumber}: ${song.titleEs} / ${song.titleEn || 'N/A'}`);
      });
    }

    // Test with language filter
    console.log('\n🌐 Testing with Spanish language filter...');

    const spanishSongsResponse = await fetch('http://localhost:3001/api/songs?language=es&limit=5', {
      headers: {
        'Authorization': `Bearer ${authData.token}`,
      },
    });

    if (spanishSongsResponse.ok) {
      const spanishData = await spanishSongsResponse.json();
      console.log('✅ Spanish language filter works');
      console.log('Songs returned:', spanishData.songs.length);
    } else {
      console.error('❌ Spanish language filter failed');
    }

    // Test with English language filter
    console.log('\n🌐 Testing with English language filter...');

    const englishSongsResponse = await fetch('http://localhost:3001/api/songs?language=en&limit=5', {
      headers: {
        'Authorization': `Bearer ${authData.token}`,
      },
    });

    if (englishSongsResponse.ok) {
      const englishData = await englishSongsResponse.json();
      console.log('✅ English language filter works');
      console.log('Songs returned:', englishData.songs.length);
    } else {
      console.error('❌ English language filter failed');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testSongsAPI().catch(console.error);
