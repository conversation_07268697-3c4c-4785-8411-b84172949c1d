/**
 * Service Schedule Conductors API Endpoint
 * 
 * Handles retrieval of available conductors for service time assignments.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { ServiceScheduleService } from '@/lib/services/serviceScheduleService';

/**
 * GET /api/admin/service-schedules/conductors
 * Retrieve available conductors for service time assignments
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has permission to view conductors
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view conductors' },
        { status: 403 }
      );
    }

    // Get available conductors
    const conductors = await ServiceScheduleService.getAvailableConductors(
      member.congregationId
    );

    return NextResponse.json({
      success: true,
      conductors,
      count: conductors.length,
    });

  } catch (error) {
    console.error('Service conductors GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve available conductors',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
