# Hermanos Fullstack Architecture Document

## Table of Contents

1. [Project Overview](#1-project-overview)
2. [Current System Analysis](#2-current-system-analysis)
3. [Technology Stack Selection](#3-technology-stack-selection)
4. [Database Architecture](#4-database-architecture)
5. [Authentication and Authorization](#5-authentication-and-authorization)
6. [API Design](#6-api-design)
7. [Frontend Architecture](#7-frontend-architecture)
8. [State Management](#8-state-management)
9. [File Management](#9-file-management)
10. [Multi-Congregation Support](#10-multi-congregation-support)
11. [Meeting Management](#11-meeting-management)
12. [Task and Assignment System](#12-task-and-assignment-system)
13. [Integration Strategy](#13-integration-strategy)
14. [Deployment Architecture](#14-deployment-architecture)
15. [Security and Performance](#15-security-and-performance)
16. [Testing Strategy](#16-testing-strategy)
17. [Coding Standards](#17-coding-standards)
18. [Error Handling Strategy](#18-error-handling-strategy)
19. [Monitoring and Observability](#19-monitoring-and-observability)
20. [Implementation Roadmap and Next Steps](#20-implementation-roadmap-and-next-steps)

---

## 1. Project Overview

### Vision Statement

The Hermanos application is a comprehensive multi-congregation church management system designed to modernize and scale the existing Coral Oeste congregation management application. The project aims to preserve the exact user experience that congregation members already know and love while providing a robust foundation for supporting multiple congregations across different regions.

### Core Objectives

**Primary Goals:**

- **Exact UI Preservation**: Maintain pixel-perfect compatibility with the existing user interface to ensure zero learning curve for current users
- **Multi-Congregation Architecture**: Build a scalable foundation that can support multiple congregations with isolated data and customizable features
- **Technology Modernization**: Migrate from the current MySQL/Node.js stack to a modern PostgreSQL/Next.js architecture for better performance and maintainability
- **Mobile-First Experience**: Optimize for mobile devices as the primary platform, ensuring excellent performance on smartphones
- **Data Integrity**: Ensure 100% data preservation during migration with enhanced data validation and security

**Secondary Goals:**

- Enhanced performance and reliability
- Improved administrative capabilities
- Simplified deployment and maintenance
- Foundation for future feature expansion
- Better integration capabilities with external services

### Target Users

**Primary Users:**

- **Congregation Members**: Publishers, ministerial servants, elders who use the app for meeting information, assignments, and communication
- **Congregation Administrators**: Elders and ministerial servants who manage congregation data, assignments, and administrative tasks
- **System Administrators**: Technical personnel responsible for system maintenance and multi-congregation setup

**User Characteristics:**

- Primarily Spanish-speaking users with varying technical proficiency
- Heavy mobile device usage (80%+ mobile traffic)
- Preference for simple, intuitive interfaces
- Need for reliable offline functionality
- Requirement for quick access to meeting and assignment information

### Success Criteria

**Technical Success Metrics:**

- 100% feature parity with existing system
- Page load times under 2 seconds on mobile devices
- 99.9% uptime after stabilization period
- Support for 10+ congregations without performance degradation
- Zero data loss during migration

**User Experience Metrics:**

- 95% user satisfaction rating from congregation feedback
- 90% user adoption within 30 days of launch
- Less than 3 support tickets per week after first month
- Identical user workflows and navigation patterns

**Business Metrics:**

- 50% reduction in system maintenance overhead
- Foundation ready for rapid multi-congregation expansion
- Enhanced administrative efficiency
- Improved data accuracy and reporting capabilities

### Project Scope

**In Scope:**

- Complete UI replication of existing Coral Oeste application
- Migration of all 41 database tables from MySQL to PostgreSQL
- Implementation of multi-congregation architecture
- Mobile-optimized responsive design
- Role-based access control system
- Meeting management (midweek and weekend)
- Task and assignment management
- Letter and document management
- Field service reporting
- Member management and administration
- File upload and storage system
- Basic reporting and analytics

**Out of Scope (Future Phases):**

- Advanced reporting and analytics dashboard
- WhatsApp API integration
- Push notification system
- Mobile app development (native iOS/Android)
- Advanced workflow automation
- Integration with external congregation management systems
- Multi-language support beyond Spanish/English

### Key Constraints

**Technical Constraints:**

- Must maintain exact UI compatibility with existing system
- Database migration must preserve all existing data
- System must work reliably on mobile devices with limited connectivity
- Self-hosted deployment requirement (no cloud vendor lock-in)
- Budget constraints favor simple, proven technologies

**Business Constraints:**

- Zero downtime tolerance during migration
- Congregation members cannot experience any disruption
- Administrative workflows must remain identical
- All existing features must be preserved
- Implementation timeline of 8-12 weeks

**Regulatory Constraints:**

- Data privacy compliance for congregation member information
- Secure handling of personal and spiritual data
- Audit trail requirements for administrative actions
- Backup and recovery compliance for religious organization data

---

## 2. Current System Analysis

### Existing Architecture Overview

The current Coral Oeste application represents a well-functioning single-congregation management system that has successfully served the congregation's needs. Understanding its strengths and limitations is crucial for designing an effective migration strategy.

**Current Technology Stack:**

- **Frontend**: HTML, CSS, JavaScript (vanilla)
- **Backend**: Node.js with Express framework
- **Database**: MySQL with 41 tables
- **Authentication**: JWT-based with congregation ID and PIN
- **File Storage**: Local filesystem for PDF documents
- **Deployment**: Self-hosted on local server infrastructure

### Database Schema Analysis

The existing MySQL database contains 41 tables that comprehensively cover all aspects of congregation management:

**Core Tables (8 tables):**

1. `congregations` - Congregation information and settings
2. `roles` - User role definitions
3. `members` - Member accounts and profiles
4. `elder_permissions` - Granular permission control
5. `letters` - Document metadata and file references
6. `tasks` - Task definitions and templates
7. `task_assignments` - Task assignments to dates and members
8. `field_service_records` - Service time tracking

**Meeting Management Tables (6 tables):** 9. `midweek_meetings` - Midweek meeting information 10. `weekend_meetings` - Weekend meeting information 11. `midweek_meeting_parts` - Individual meeting parts and assignments 12. `weekend_meeting_parts` - Weekend meeting parts and speakers 13. `songs` - Song catalog in multiple languages 14. `meeting_locations` - Meeting venue management

**Additional Feature Tables (27 tables):**

- Event management and calendar functionality
- Advanced task categorization and tracking
- Service group organization
- Historical data preservation
- Administrative audit trails
- File upload metadata
- User preference storage
- System configuration settings

### Current User Interface Analysis

**Dashboard Design:**

- Clean, mobile-first layout with large touch targets
- Grid-based card system for easy navigation
- Consistent color coding for different sections
- Spanish-language interface with intuitive iconography

**Navigation Patterns:**

- Simple back-button navigation
- Breadcrumb-style page headers with section-specific colors
- Bottom navigation for quick access to main sections
- Minimal cognitive load with clear visual hierarchy

**Key UI Strengths:**

- Excellent mobile responsiveness and touch optimization
- Consistent visual design language across all pages
- Fast loading times and smooth transitions
- Intuitive information architecture
- Effective use of color coding for different functional areas

### Feature Analysis

**Core Features Successfully Implemented:**

1. **Member Dashboard**: Central hub with quick access to all features
2. **Meeting Management**: Comprehensive midweek and weekend meeting information
3. **Task Assignment**: Flexible task creation and assignment system
4. **Field Service**: Service time reporting and tracking
5. **Letter Management**: Document upload and categorization system
6. **Administrative Tools**: Member management and permission control
7. **Event Management**: Congregation event planning and communication

**Advanced Features:**

- Multi-language song catalog integration
- Service group organization and coordination
- Historical data tracking and reporting
- File upload with security controls
- Role-based access with granular permissions
- Mobile-optimized user experience

### Performance Characteristics

**Current Performance Metrics:**

- Page load times: 1-3 seconds on mobile devices
- Database query response: 50-200ms for typical operations
- File upload handling: Supports up to 10MB PDF files
- Concurrent user capacity: 50+ simultaneous users
- Mobile data usage: Optimized for limited bandwidth

**Performance Strengths:**

- Lightweight frontend with minimal JavaScript
- Efficient database queries with proper indexing
- Optimized image and asset delivery
- Effective caching strategies
- Mobile-first performance optimization

### User Experience Assessment

**User Satisfaction Indicators:**

- High adoption rate among congregation members
- Minimal support requests and user complaints
- Positive feedback on ease of use and reliability
- Successful integration into congregation workflows
- Effective mobile usage patterns

**Key User Experience Strengths:**

- Intuitive navigation that requires minimal training
- Fast access to frequently needed information
- Reliable performance on various mobile devices
- Clear visual feedback for user actions
- Consistent behavior across different features

### Technical Debt and Limitations

**Current Limitations:**

1. **Single-Congregation Architecture**: Hard-coded for one congregation, limiting scalability
2. **Technology Stack Age**: Some dependencies and patterns could benefit from modernization
3. **Database Scalability**: MySQL setup optimized for single congregation, not multi-tenant
4. **Deployment Complexity**: Manual deployment process with potential for human error
5. **Testing Coverage**: Limited automated testing for regression prevention

**Areas for Improvement:**

- Enhanced error handling and user feedback
- Improved development and deployment workflows
- Better separation of concerns in codebase architecture
- Enhanced security measures for multi-congregation data isolation
- Improved monitoring and observability capabilities

### Migration Considerations

**Data Migration Challenges:**

- Preserving all historical data during MySQL to PostgreSQL migration
- Maintaining data relationships and integrity constraints
- Handling character encoding differences between database systems
- Ensuring zero data loss during the migration process

**UI Migration Requirements:**

- Pixel-perfect replication of existing user interface
- Preservation of all user interaction patterns
- Maintenance of mobile responsiveness and performance
- Conservation of accessibility features and touch optimization

**Functional Migration Priorities:**

- All existing features must work identically after migration
- Administrative workflows must remain unchanged
- User authentication and authorization must be seamless
- File upload and document management must be preserved
- Meeting and task management functionality must be identical

### Success Factors from Current System

**Key Elements to Preserve:**

1. **User Interface Excellence**: The current UI is highly effective and should be replicated exactly
2. **Mobile Optimization**: Outstanding mobile performance must be maintained
3. **Simplicity**: The straightforward, intuitive design should be preserved
4. **Reliability**: The system's dependable performance must continue
5. **Feature Completeness**: All current functionality must be maintained

**Lessons Learned:**

- Simple, focused design leads to high user adoption
- Mobile-first approach is essential for congregation use
- Reliable performance is more important than advanced features
- Spanish-language interface is crucial for user acceptance
- Administrative efficiency directly impacts congregation operations

This analysis provides the foundation for designing a migration strategy that preserves all the strengths of the current system while addressing its limitations and preparing for multi-congregation scalability.

---

## 3. Technology Stack Selection

### Technology Selection Criteria

The technology stack selection for the Hermanos application prioritizes proven reliability, development efficiency, and long-term maintainability while ensuring the ability to preserve the exact user experience of the current system.

**Primary Selection Criteria:**

- **UI Preservation Capability**: Technology must enable pixel-perfect replication of existing interface
- **Development Velocity**: Stack should accelerate development while maintaining quality
- **Mobile Performance**: Excellent mobile device performance and responsiveness
- **Multi-Tenancy Support**: Native support for multi-congregation data isolation
- **Deployment Simplicity**: Self-hosted deployment without complex infrastructure requirements
- **Community Support**: Active community and long-term viability
- **Learning Curve**: Reasonable learning curve for development team
- **Cost Effectiveness**: Minimal licensing and infrastructure costs

### Selected Technology Stack

#### Frontend Framework: Next.js 14

**Rationale for Next.js Selection:**

- **Server-Side Rendering**: Excellent mobile performance with fast initial page loads
- **Static Generation**: Optimal performance for frequently accessed pages
- **API Routes**: Unified frontend and backend development experience
- **Built-in Optimization**: Automatic image optimization, code splitting, and performance enhancements
- **TypeScript Support**: Native TypeScript integration for better code quality
- **Deployment Flexibility**: Can be deployed as static site or full-stack application

**Next.js Configuration:**

```javascript
// next.config.js - Optimized for congregation management
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true, // Use new App Router for better organization
  },
  images: {
    domains: ['localhost'], // Local image optimization
    formats: ['image/webp', 'image/avif'], // Modern image formats
  },
  compress: true, // Enable gzip compression
  poweredByHeader: false, // Remove X-Powered-By header for security
  reactStrictMode: true, // Enable React strict mode
  swcMinify: true, // Use SWC for faster builds

  // Mobile optimization
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

#### Database: PostgreSQL 15

**Rationale for PostgreSQL Selection:**

- **Multi-Tenancy Excellence**: Superior support for data isolation between congregations
- **JSON Support**: Native JSON handling for flexible configuration storage
- **Performance**: Excellent query performance with proper indexing
- **Reliability**: ACID compliance and robust transaction handling
- **Scalability**: Proven scalability for multi-congregation growth
- **Open Source**: No licensing costs with enterprise-grade features

**PostgreSQL Configuration:**

```sql
-- postgresql.conf optimizations for congregation management
shared_buffers = 256MB                    # 25% of RAM for small deployments
effective_cache_size = 1GB                # Estimate of OS cache
work_mem = 4MB                           # Memory for sorting operations
maintenance_work_mem = 64MB              # Memory for maintenance operations
checkpoint_completion_target = 0.9       # Spread checkpoints
wal_buffers = 16MB                       # WAL buffer size
default_statistics_target = 100          # Statistics for query planning

# Connection settings
max_connections = 100                    # Sufficient for congregation usage
shared_preload_libraries = 'pg_stat_statements'  # Query performance monitoring

# Logging for monitoring
log_statement = 'mod'                    # Log data modifications
log_min_duration_statement = 1000       # Log slow queries (>1s)
```

#### ORM: Prisma

**Rationale for Prisma Selection:**

- **Type Safety**: Automatic TypeScript type generation from database schema
- **Migration Management**: Robust database migration system
- **Query Builder**: Intuitive query API with excellent performance
- **Multi-Database Support**: Easy migration from MySQL to PostgreSQL
- **Development Experience**: Excellent tooling and debugging capabilities

**Prisma Schema Example:**

```prisma
// prisma/schema.prisma - Core congregation management schema
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Congregation {
  id       String @id @db.VarChar(8)
  name     String @db.VarChar(255)
  region   String @db.VarChar(50)
  pin      String @db.VarChar(255) // bcrypt hashed
  language String @default("es") @db.VarChar(5)

  // Relationships
  members  Member[]
  meetings MidweekMeeting[]
  tasks    Task[]
  letters  Letter[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("congregations")
}

model Member {
  id             String  @id @default(cuid())
  congregationId String  @db.VarChar(8)
  name           String  @db.VarChar(255)
  email          String? @db.VarChar(255)
  role           String  @db.VarChar(50)
  pin            String  @db.VarChar(255) // bcrypt hashed
  isActive       Boolean @default(true)

  // Relationships
  congregation Congregation @relation(fields: [congregationId], references: [id])
  assignments  TaskAssignment[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("members")
  @@index([congregationId])
}
```

#### Styling: Tailwind CSS

**Rationale for Tailwind CSS Selection:**

- **Rapid UI Development**: Utility-first approach enables fast UI replication
- **Mobile-First**: Built-in responsive design utilities
- **Consistency**: Design system ensures consistent styling across components
- **Performance**: Purged CSS results in minimal bundle sizes
- **Customization**: Easy customization for congregation-specific branding

**Tailwind Configuration:**

```javascript
// tailwind.config.js - Optimized for congregation management UI
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      // Congregation-specific color palette
      colors: {
        'congregation-blue': '#3b82f6',
        'congregation-purple': '#8b5cf6',
        'congregation-green': '#10b981',
        'congregation-orange': '#f59e0b',
      },
      // Mobile-optimized spacing
      spacing: {
        18: '4.5rem',
        88: '22rem',
      },
      // Touch-friendly sizing
      minHeight: {
        44: '44px', // Minimum touch target size
      },
      minWidth: {
        44: '44px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'), // Better form styling
    require('@tailwindcss/typography'), // Rich text styling
  ],
};
```

#### State Management: Zustand

**Rationale for Zustand Selection:**

- **Simplicity**: Minimal boilerplate compared to Redux
- **TypeScript Support**: Excellent TypeScript integration
- **Performance**: Efficient re-rendering with selective subscriptions
- **Bundle Size**: Lightweight library with minimal overhead
- **Learning Curve**: Easy to understand and implement

**Zustand Store Example:**

```typescript
// stores/authStore.ts - Authentication state management
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;

  // Actions
  login: (user: User, token: string) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;

  // Permission helpers
  canViewAdmin: () => boolean;
  canManageMembers: () => boolean;
  canUploadLetters: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,

      login: (user, token) =>
        set({
          user,
          token,
          isAuthenticated: true,
        }),

      logout: () =>
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        }),

      updateUser: updates =>
        set(state => ({
          user: state.user ? { ...state.user, ...updates } : null,
        })),

      canViewAdmin: () => {
        const { user } = get();
        return user?.role !== 'publisher';
      },

      canManageMembers: () => {
        const { user } = get();
        return ['elder', 'coordinator'].includes(user?.role || '') || user?.hasCongregationPinAccess;
      },

      canUploadLetters: () => {
        const { user } = get();
        return user?.role !== 'publisher';
      },
    }),
    {
      name: 'hermanos-auth', // localStorage key
      partialize: state => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
```

### Development Tools and Utilities

#### TypeScript Configuration

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["components/*"],
      "@/lib/*": ["lib/*"],
      "@/stores/*": ["stores/*"],
      "@/types/*": ["types/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

#### Package.json Dependencies

```json
{
  "name": "hermanos-app",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:studio": "prisma studio",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:e2e": "playwright test"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@prisma/client": "^5.0.0",
    "prisma": "^5.0.0",
    "zustand": "^4.4.0",
    "jsonwebtoken": "^9.0.0",
    "bcryptjs": "^2.4.3",
    "zod": "^3.22.0",
    "react-hot-toast": "^2.4.0",
    "lucide-react": "^0.290.0"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/bcryptjs": "^2.4.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0",
    "eslint": "^8.0.0",
    "eslint-config-next": "14.0.0",
    "@tailwindcss/forms": "^0.5.0",
    "@tailwindcss/typography": "^0.5.0",
    "jest": "^29.0.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^6.0.0",
    "playwright": "^1.40.0"
  }
}
```

### Technology Stack Benefits

**Development Efficiency:**

- Unified frontend/backend development with Next.js
- Type safety across the entire stack with TypeScript
- Rapid UI development with Tailwind CSS
- Simplified state management with Zustand
- Excellent developer experience with modern tooling

**Performance Advantages:**

- Server-side rendering for fast initial page loads
- Automatic code splitting and optimization
- Efficient database queries with Prisma
- Minimal JavaScript bundle sizes
- Optimized mobile performance

**Scalability Features:**

- Multi-tenant architecture support with PostgreSQL
- Horizontal scaling capabilities
- Efficient caching strategies
- API-first design for future integrations
- Modular component architecture

**Maintenance Benefits:**

- Strong typing reduces runtime errors
- Automated database migrations
- Consistent code formatting and linting
- Comprehensive testing capabilities
- Clear separation of concerns

This technology stack provides the optimal foundation for building a modern, scalable, and maintainable congregation management system while preserving the excellent user experience of the existing application.

---

## 4. Database Architecture

### Database Migration Strategy

The database architecture transformation from MySQL to PostgreSQL requires careful planning to ensure zero data loss while enabling multi-congregation scalability. The migration strategy preserves all existing functionality while establishing the foundation for multi-tenant operations.

**Migration Approach:**

- **Schema-First Migration**: Convert all 41 MySQL tables to PostgreSQL with enhanced constraints and indexing
- **Data Preservation**: Ensure 100% data integrity during the migration process
- **Multi-Tenant Preparation**: Add congregation isolation to all tables
- **Performance Optimization**: Implement proper indexing and query optimization for congregation-scale operations
- **Backward Compatibility**: Maintain all existing relationships and data structures

### Core Database Schema

#### Congregation Management Tables

```sql
-- Congregations table - Central tenant management
CREATE TABLE congregations (
    id VARCHAR(8) PRIMARY KEY,                    -- Short, memorable congregation ID
    name VARCHAR(255) NOT NULL,                   -- Congregation name
    region VARCHAR(50),                           -- Geographic region
    pin VARCHAR(255) NOT NULL,                    -- bcrypt hashed PIN
    language VARCHAR(5) DEFAULT 'es',             -- Default language (es/en)
    timezone VARCHAR(50) DEFAULT 'America/Mexico_City',
    settings JSONB DEFAULT '{}',                  -- Flexible congregation settings
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for congregation management
CREATE INDEX idx_congregations_region ON congregations(region);
CREATE INDEX idx_congregations_active ON congregations(is_active);

-- Members table - User accounts with congregation isolation
CREATE TABLE members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    role VARCHAR(50) NOT NULL DEFAULT 'publisher',
    pin VARCHAR(255) NOT NULL,                    -- bcrypt hashed PIN
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for member management
CREATE INDEX idx_members_congregation ON members(congregation_id);
CREATE INDEX idx_members_role ON members(congregation_id, role);
CREATE INDEX idx_members_active ON members(congregation_id, is_active);
CREATE UNIQUE INDEX idx_members_email_congregation ON members(congregation_id, email) WHERE email IS NOT NULL;

-- Roles table - Role definitions
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Elder permissions table - Granular permission control
CREATE TABLE elder_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    permission_type VARCHAR(100) NOT NULL,
    granted_by UUID REFERENCES members(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Indexes for permission management
CREATE INDEX idx_elder_permissions_member ON elder_permissions(member_id);
CREATE INDEX idx_elder_permissions_congregation ON elder_permissions(congregation_id);
CREATE UNIQUE INDEX idx_elder_permissions_unique ON elder_permissions(congregation_id, member_id, permission_type) WHERE is_active = true;
```

#### Meeting Management Schema

```sql
-- Midweek meetings table
CREATE TABLE midweek_meetings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    meeting_date DATE NOT NULL,
    theme VARCHAR(255),
    chairman_id UUID REFERENCES members(id),
    location VARCHAR(100) DEFAULT 'Kingdom Hall',
    zoom_link TEXT,
    notes TEXT,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Weekend meetings table
CREATE TABLE weekend_meetings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    meeting_date DATE NOT NULL,
    public_talk_theme VARCHAR(255),
    public_talk_speaker_id UUID REFERENCES members(id),
    visiting_speaker_name VARCHAR(255),
    visiting_speaker_congregation VARCHAR(255),
    watchtower_conductor_id UUID REFERENCES members(id),
    watchtower_reader_id UUID REFERENCES members(id),
    location VARCHAR(100) DEFAULT 'Kingdom Hall',
    zoom_link TEXT,
    notes TEXT,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meeting parts for midweek meetings
CREATE TABLE midweek_meeting_parts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    meeting_id UUID NOT NULL REFERENCES midweek_meetings(id) ON DELETE CASCADE,
    part_type VARCHAR(50) NOT NULL,              -- 'song', 'prayer', 'treasures', 'ministry', 'living'
    title VARCHAR(255) NOT NULL,
    duration_minutes INTEGER,
    assigned_member_id UUID REFERENCES members(id),
    assistant_id UUID REFERENCES members(id),
    display_order INTEGER NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meeting parts for weekend meetings
CREATE TABLE weekend_meeting_parts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    meeting_id UUID NOT NULL REFERENCES weekend_meetings(id) ON DELETE CASCADE,
    part_type VARCHAR(50) NOT NULL,              -- 'song', 'prayer', 'public_talk', 'watchtower'
    title VARCHAR(255) NOT NULL,
    assigned_member_id UUID REFERENCES members(id),
    display_order INTEGER NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for meeting management
CREATE INDEX idx_midweek_meetings_congregation_date ON midweek_meetings(congregation_id, meeting_date);
CREATE INDEX idx_weekend_meetings_congregation_date ON weekend_meetings(congregation_id, meeting_date);
CREATE INDEX idx_midweek_parts_meeting ON midweek_meeting_parts(meeting_id, display_order);
CREATE INDEX idx_weekend_parts_meeting ON weekend_meeting_parts(meeting_id, display_order);
```

#### Task and Assignment Management

```sql
-- Tasks table - Task definitions
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    estimated_duration_minutes INTEGER,
    requires_elder BOOLEAN DEFAULT false,
    requires_ministerial_servant BOOLEAN DEFAULT false,
    service_group_specific BOOLEAN DEFAULT false,
    is_recurring BOOLEAN DEFAULT false,
    recurrence_pattern JSONB,                    -- Flexible recurrence definition
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES members(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Task assignments table
CREATE TABLE task_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    assigned_member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    assigned_date DATE NOT NULL,
    due_date DATE,
    status VARCHAR(50) DEFAULT 'assigned',        -- 'assigned', 'in_progress', 'completed', 'cancelled'
    completion_date DATE,
    notes TEXT,
    assigned_by UUID REFERENCES members(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Field service records table
CREATE TABLE field_service_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    service_month DATE NOT NULL,                  -- First day of the service month
    hours DECIMAL(4,1) DEFAULT 0,
    placements INTEGER DEFAULT 0,
    video_showings INTEGER DEFAULT 0,
    return_visits INTEGER DEFAULT 0,
    bible_studies INTEGER DEFAULT 0,
    notes TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for task and service management
CREATE INDEX idx_tasks_congregation ON tasks(congregation_id);
CREATE INDEX idx_task_assignments_congregation_date ON task_assignments(congregation_id, assigned_date);
CREATE INDEX idx_task_assignments_member ON task_assignments(assigned_member_id);
CREATE INDEX idx_field_service_congregation_month ON field_service_records(congregation_id, service_month);
CREATE UNIQUE INDEX idx_field_service_member_month ON field_service_records(member_id, service_month);
```

#### Communication and Document Management

```sql
-- Letters table - Document management
CREATE TABLE letters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    category VARCHAR(100),
    visibility VARCHAR(50) DEFAULT 'ALL_MEMBERS',  -- 'ALL_MEMBERS', 'ELDERS_ONLY', 'MINISTERIAL_SERVANTS_PLUS'
    upload_date DATE DEFAULT CURRENT_DATE,
    uploaded_by UUID REFERENCES members(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Events table - Congregation events
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    location VARCHAR(255),
    event_type VARCHAR(100),                      -- 'assembly', 'convention', 'special_meeting', 'service'
    is_mandatory BOOLEAN DEFAULT false,
    created_by UUID REFERENCES members(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Songs table - Multi-language song catalog
CREATE TABLE songs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    song_number INTEGER NOT NULL,
    title_spanish VARCHAR(255),
    title_english VARCHAR(255),
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for communication and documents
CREATE INDEX idx_letters_congregation ON letters(congregation_id);
CREATE INDEX idx_letters_visibility ON letters(congregation_id, visibility);
CREATE INDEX idx_events_congregation_date ON events(congregation_id, event_date);
CREATE UNIQUE INDEX idx_songs_number ON songs(song_number);
```

### Data Migration Scripts

#### MySQL to PostgreSQL Migration

```javascript
// scripts/migrate-mysql-to-postgresql.js
const mysql = require('mysql2/promise');
const { Pool } = require('pg');

class DatabaseMigrator {
  constructor() {
    this.mysqlConnection = mysql.createConnection({
      host: process.env.MYSQL_HOST,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE,
    });

    this.pgPool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });
  }

  async migrateCongregations() {
    console.log('Migrating congregations...');

    const [mysqlRows] = await this.mysqlConnection.execute('SELECT * FROM congregations');

    for (const row of mysqlRows) {
      await this.pgPool.query(
        `
        INSERT INTO congregations (id, name, region, pin, language, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (id) DO UPDATE SET
          name = EXCLUDED.name,
          region = EXCLUDED.region,
          updated_at = NOW()
      `,
        [
          row.id,
          row.name,
          row.region,
          row.pin, // Already hashed in MySQL
          row.language || 'es',
          row.created_at,
          row.updated_at,
        ]
      );
    }

    console.log(`Migrated ${mysqlRows.length} congregations`);
  }

  async migrateMembers() {
    console.log('Migrating members...');

    const [mysqlRows] = await this.mysqlConnection.execute(`
      SELECT m.*, c.id as congregation_id
      FROM members m
      JOIN congregations c ON m.congregation_id = c.id
    `);

    for (const row of mysqlRows) {
      await this.pgPool.query(
        `
        INSERT INTO members (id, congregation_id, name, email, role, pin, is_active, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (id) DO UPDATE SET
          name = EXCLUDED.name,
          email = EXCLUDED.email,
          role = EXCLUDED.role,
          updated_at = NOW()
      `,
        [
          row.id,
          row.congregation_id,
          row.name,
          row.email,
          row.role,
          row.pin, // Already hashed in MySQL
          row.is_active,
          row.created_at,
          row.updated_at,
        ]
      );
    }

    console.log(`Migrated ${mysqlRows.length} members`);
  }

  async migrateMeetings() {
    console.log('Migrating meetings...');

    // Migrate midweek meetings
    const [midweekRows] = await this.mysqlConnection.execute(`
      SELECT m.*, c.id as congregation_id
      FROM midweek_meetings m
      JOIN congregations c ON m.congregation_id = c.id
    `);

    for (const row of midweekRows) {
      await this.pgPool.query(
        `
        INSERT INTO midweek_meetings (id, congregation_id, meeting_date, theme, chairman_id, location, notes, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (id) DO UPDATE SET
          theme = EXCLUDED.theme,
          chairman_id = EXCLUDED.chairman_id,
          updated_at = NOW()
      `,
        [
          row.id,
          row.congregation_id,
          row.meeting_date,
          row.theme,
          row.chairman_id,
          row.location || 'Kingdom Hall',
          row.notes,
          row.created_at,
          row.updated_at,
        ]
      );
    }

    console.log(`Migrated ${midweekRows.length} midweek meetings`);
  }

  async validateMigration() {
    console.log('Validating migration...');

    // Count records in both databases
    const [mysqlCounts] = await this.mysqlConnection.execute(`
      SELECT
        (SELECT COUNT(*) FROM congregations) as congregations,
        (SELECT COUNT(*) FROM members) as members,
        (SELECT COUNT(*) FROM midweek_meetings) as midweek_meetings,
        (SELECT COUNT(*) FROM tasks) as tasks
    `);

    const pgCounts = await this.pgPool.query(`
      SELECT
        (SELECT COUNT(*) FROM congregations) as congregations,
        (SELECT COUNT(*) FROM members) as members,
        (SELECT COUNT(*) FROM midweek_meetings) as midweek_meetings,
        (SELECT COUNT(*) FROM tasks) as tasks
    `);

    const mysql = mysqlCounts[0];
    const postgres = pgCounts.rows[0];

    console.log('Migration validation:');
    console.log(
      `Congregations: MySQL ${mysql.congregations} -> PostgreSQL ${postgres.congregations}`
    );
    console.log(`Members: MySQL ${mysql.members} -> PostgreSQL ${postgres.members}`);
    console.log(
      `Midweek Meetings: MySQL ${mysql.midweek_meetings} -> PostgreSQL ${postgres.midweek_meetings}`
    );
    console.log(`Tasks: MySQL ${mysql.tasks} -> PostgreSQL ${postgres.tasks}`);

    const isValid =
      mysql.congregations === parseInt(postgres.congregations) &&
      mysql.members === parseInt(postgres.members) &&
      mysql.midweek_meetings === parseInt(postgres.midweek_meetings) &&
      mysql.tasks === parseInt(postgres.tasks);

    if (isValid) {
      console.log('✅ Migration validation successful');
    } else {
      console.log('❌ Migration validation failed');
      throw new Error('Migration validation failed');
    }
  }

  async run() {
    try {
      await this.migrateCongregations();
      await this.migrateMembers();
      await this.migrateMeetings();
      // ... migrate other tables

      await this.validateMigration();

      console.log('✅ Database migration completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    } finally {
      await this.mysqlConnection.end();
      await this.pgPool.end();
    }
  }
}

// Run migration
if (require.main === module) {
  const migrator = new DatabaseMigrator();
  migrator.run().catch(console.error);
}

module.exports = DatabaseMigrator;
```

### Database Performance Optimization

#### Indexing Strategy

```sql
-- Performance indexes for congregation-scoped queries
CREATE INDEX CONCURRENTLY idx_members_congregation_role ON members(congregation_id, role) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_meetings_congregation_date_range ON midweek_meetings(congregation_id, meeting_date) WHERE meeting_date >= CURRENT_DATE - INTERVAL '1 month';
CREATE INDEX CONCURRENTLY idx_tasks_congregation_active ON tasks(congregation_id) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_assignments_member_status ON task_assignments(assigned_member_id, status) WHERE status != 'completed';

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY idx_field_service_reporting ON field_service_records(congregation_id, service_month, member_id);
CREATE INDEX CONCURRENTLY idx_letters_congregation_visibility_date ON letters(congregation_id, visibility, upload_date) WHERE is_active = true;

-- Partial indexes for performance
CREATE INDEX CONCURRENTLY idx_active_members ON members(congregation_id, name) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_published_meetings ON midweek_meetings(congregation_id, meeting_date) WHERE is_published = true;
```

#### Query Optimization Examples

```sql
-- Optimized query for dashboard data
EXPLAIN (ANALYZE, BUFFERS)
SELECT
  m.id,
  m.name,
  m.role,
  COUNT(ta.id) as pending_tasks,
  MAX(fsr.service_month) as last_service_report
FROM members m
LEFT JOIN task_assignments ta ON m.id = ta.assigned_member_id
  AND ta.status = 'assigned'
  AND ta.congregation_id = m.congregation_id
LEFT JOIN field_service_records fsr ON m.id = fsr.member_id
  AND fsr.congregation_id = m.congregation_id
WHERE m.congregation_id = $1
  AND m.is_active = true
GROUP BY m.id, m.name, m.role
ORDER BY m.name;

-- Optimized query for meeting data with parts
EXPLAIN (ANALYZE, BUFFERS)
SELECT
  mm.id,
  mm.meeting_date,
  mm.theme,
  json_agg(
    json_build_object(
      'id', mmp.id,
      'title', mmp.title,
      'part_type', mmp.part_type,
      'assigned_member', mem.name,
      'display_order', mmp.display_order
    ) ORDER BY mmp.display_order
  ) as parts
FROM midweek_meetings mm
LEFT JOIN midweek_meeting_parts mmp ON mm.id = mmp.meeting_id
LEFT JOIN members mem ON mmp.assigned_member_id = mem.id
WHERE mm.congregation_id = $1
  AND mm.meeting_date >= CURRENT_DATE
  AND mm.meeting_date <= CURRENT_DATE + INTERVAL '4 weeks'
GROUP BY mm.id, mm.meeting_date, mm.theme
ORDER BY mm.meeting_date;
```

This database architecture provides a solid foundation for the Hermanos application, ensuring data integrity, performance, and scalability while maintaining all the functionality of the existing system.

---

## 5. Authentication and Authorization

### Authentication Strategy

The authentication system for the Hermanos application maintains the simplicity and user-friendliness of the existing system while adding robust security measures and multi-congregation support. The approach balances security requirements with the practical needs of congregation members who primarily access the system via mobile devices.

**Authentication Approach:**

- **Congregation-Based Login**: Users connect to their specific congregation using congregation ID and PIN
- **Member Authentication**: Individual members authenticate using their personal PIN within their congregation
- **JWT Token Management**: Secure token-based authentication with mobile-friendly expiration times
- **Role-Based Access**: Automatic role assignment based on member status within the congregation
- **Persistent Sessions**: Long-lived sessions optimized for mobile usage patterns

### Simple JWT Implementation

```typescript
// lib/auth/simpleJWT.ts - Lightweight JWT management
import jwt from 'jsonwebtoken';

interface JWTPayload {
  userId: string;
  congregationId: string;
  role: string;
  name: string;
  iat?: number;
  exp?: number;
}

export class SimpleJWTManager {
  private static readonly JWT_SECRET = process.env.JWT_SECRET!;
  private static readonly TOKEN_EXPIRY = '30d'; // Mobile-friendly long expiration

  // Generate JWT token for authenticated user
  static generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    return jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.TOKEN_EXPIRY,
      issuer: 'hermanos-app',
      audience: payload.congregationId,
    });
  }

  // Verify and decode JWT token
  static verifyToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as JWTPayload;
      return decoded;
    } catch (error) {
      console.error('JWT verification failed:', error);
      return null;
    }
  }

  // Extract token from Authorization header
  static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }

  // Check if token is expired
  static isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as JWTPayload;
      if (!decoded || !decoded.exp) return true;

      return Date.now() >= decoded.exp * 1000;
    } catch {
      return true;
    }
  }

  // Refresh token if needed (within 7 days of expiry)
  static shouldRefreshToken(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as JWTPayload;
      if (!decoded || !decoded.exp) return false;

      const sevenDaysInSeconds = 7 * 24 * 60 * 60;
      const timeUntilExpiry = decoded.exp - Date.now() / 1000;

      return timeUntilExpiry < sevenDaysInSeconds;
    } catch {
      return false;
    }
  }
}
```

### Role-Based Access Control

```typescript
// lib/auth/simpleRBAC.ts - Simple role-based access control
export enum ROLES {
  PUBLISHER = 'publisher',
  MINISTERIAL_SERVANT = 'ministerial_servant',
  ELDER = 'elder',
  COORDINATOR = 'coordinator', // Full permissions by default, can delegate to others
}

export enum PERMISSIONS {
  VIEW_DASHBOARD = 'view_dashboard',
  VIEW_MEETINGS = 'view_meetings',
  VIEW_TASKS = 'view_tasks',
  VIEW_LETTERS = 'view_letters',
  VIEW_FIELD_SERVICE = 'view_field_service',

  // Administrative permissions
  VIEW_ADMIN = 'view_admin',
  MANAGE_MEMBERS = 'manage_members',
  MANAGE_MEETINGS = 'manage_meetings',
  MANAGE_TASKS = 'manage_tasks',
  MANAGE_LETTERS = 'manage_letters',
  UPLOAD_LETTERS = 'upload_letters',
  ASSIGN_MEETING_PARTS = 'assign_meeting_parts',
  VIEW_ALL_SERVICE_REPORTS = 'view_all_service_reports',

  // Elder-specific permissions
  MANAGE_CONGREGATION_SETTINGS = 'manage_congregation_settings',
  VIEW_SENSITIVE_LETTERS = 'view_sensitive_letters',
  MANAGE_PERMISSIONS = 'manage_permissions',
}

// Role permission mapping
const ROLE_PERMISSIONS: Record<ROLES, PERMISSIONS[]> = {
  [ROLES.PUBLISHER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MEETINGS,
    PERMISSIONS.VIEW_TASKS,
    PERMISSIONS.VIEW_LETTERS,
    PERMISSIONS.VIEW_FIELD_SERVICE,
  ],

  [ROLES.MINISTERIAL_SERVANT]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MEETINGS,
    PERMISSIONS.VIEW_TASKS,
    PERMISSIONS.VIEW_LETTERS,
    PERMISSIONS.VIEW_FIELD_SERVICE,
    PERMISSIONS.VIEW_ADMIN,
    PERMISSIONS.MANAGE_TASKS,
    PERMISSIONS.UPLOAD_LETTERS,
    PERMISSIONS.ASSIGN_MEETING_PARTS,
  ],

  [ROLES.ELDER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MEETINGS,
    PERMISSIONS.VIEW_TASKS,
    PERMISSIONS.VIEW_LETTERS,
    PERMISSIONS.VIEW_FIELD_SERVICE,
    PERMISSIONS.VIEW_ADMIN,
    PERMISSIONS.MANAGE_MEMBERS,
    PERMISSIONS.MANAGE_MEETINGS,
    PERMISSIONS.MANAGE_TASKS,
    PERMISSIONS.MANAGE_LETTERS,
    PERMISSIONS.UPLOAD_LETTERS,
    PERMISSIONS.ASSIGN_MEETING_PARTS,
    PERMISSIONS.VIEW_ALL_SERVICE_REPORTS,
    PERMISSIONS.VIEW_SENSITIVE_LETTERS,
  ],

  [ROLES.COORDINATOR]: Object.values(PERMISSIONS), // Full permissions by default
};

// Permission checking functions
export function hasPermission(role: ROLES, permission: PERMISSIONS): boolean {
  const rolePermissions = ROLE_PERMISSIONS[role];
  return rolePermissions.includes(permission);
}

export function canViewAdmin(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.VIEW_ADMIN);
}

export function canManageMembers(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.MANAGE_MEMBERS);
}

export function canUploadLetters(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.UPLOAD_LETTERS);
}

export function canAssignMeetingParts(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.ASSIGN_MEETING_PARTS);
}

// Get all permissions for a role
export function getRolePermissions(role: ROLES): PERMISSIONS[] {
  return ROLE_PERMISSIONS[role] || [];
}

// Check if user has any of the specified roles
export function hasAnyRole(userRole: ROLES, allowedRoles: ROLES[]): boolean {
  return allowedRoles.includes(userRole);
}
```

### New Permission Architecture (2025)

The permission system has been simplified and enhanced with a hybrid approach that combines role-based permissions with congregation PIN access and delegation capabilities.

**Key Architectural Changes:**

1. **Removed Developer Role**: The `developer` role has been eliminated. Developers now use congregation PIN for full access during development and testing.

2. **Added Coordinator Role**: New `coordinator` role with full permissions by default. Coordinators can delegate specific permissions to elders and ministerial servants.

3. **Congregation PIN Access**: Acts as "super admin" access that bypasses all role restrictions. PIN holders have full overseer/coordinator access.

4. **Dual Authority Model**: Permission delegation authority comes from either:
   - **Coordinator Role**: Full administrative access with delegation capabilities
   - **Congregation PIN Access**: Emergency/development override with full permissions

**Permission Flow:**
```
Base Role Permissions → Coordinator Role → Congregation PIN Access
     (RBAC)                (Full Access)      (Super Admin)
```

**Delegation System:**
- **Authority**: Coordinators OR congregation PIN holders
- **Target**: Elders and ministerial servants
- **Scope**: Section-specific permissions (meetings, tasks, letters, events, field service)
- **Audit**: Complete logging of all permission changes with IP tracking

```typescript
// New permission checking with PIN access support
interface UserPermissionContext {
  userId: string;
  role: ROLES;
  congregationId: string;
  hasCongregationPinAccess?: boolean; // Super admin access
}

// Permission delegation context
interface DelegationContext {
  performedBy: string;
  hasCongregationPinAccess?: boolean;
  ipAddress?: string;
  userAgent?: string;
}
```

### Authentication Middleware

```typescript
// lib/middleware/auth.ts - Authentication middleware for API routes
import { NextApiRequest, NextApiResponse } from 'next';
import { SimpleJWTManager } from '@/lib/auth/simpleJWT';
import { ROLES, hasPermission, PERMISSIONS } from '@/lib/auth/simpleRBAC';

interface AuthenticatedRequest extends NextApiRequest {
  user?: {
    id: string;
    name: string;
    role: ROLES;
    congregationId: string;
  };
}

interface AuthContext {
  user: {
    id: string;
    name: string;
    role: ROLES;
    congregationId: string;
  };
  congregation: {
    id: string;
  };
}

type AuthenticatedHandler = (
  req: AuthenticatedRequest,
  res: NextApiResponse,
  context: AuthContext
) => Promise<void>;

interface AuthOptions {
  requireRole?: ROLES[];
  requirePermission?: PERMISSIONS;
  allowSameUser?: boolean; // Allow access if user is accessing their own data
}

// Main authentication middleware
export function withAuth(handler: AuthenticatedHandler, options: AuthOptions = {}) {
  return async (req: AuthenticatedRequest, res: NextApiResponse) => {
    try {
      // Extract token from Authorization header
      const authHeader = req.headers.authorization;
      const token = SimpleJWTManager.extractTokenFromHeader(authHeader);

      if (!token) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication token required',
          },
        });
      }

      // Verify token
      const payload = SimpleJWTManager.verifyToken(token);
      if (!payload) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid or expired authentication token',
          },
        });
      }

      // Set user context
      req.user = {
        id: payload.userId,
        name: payload.name,
        role: payload.role as ROLES,
        congregationId: payload.congregationId,
      };

      // Check role requirements
      if (options.requireRole && !options.requireRole.includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_ROLE',
            message: 'Insufficient role for this operation',
          },
        });
      }

      // Check permission requirements
      if (options.requirePermission && !hasPermission(req.user.role, options.requirePermission)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Insufficient permissions for this operation',
          },
        });
      }

      // Create context object
      const context: AuthContext = {
        user: req.user,
        congregation: {
          id: req.user.congregationId,
        },
      };

      // Call the handler with context
      await handler(req, res, context);
    } catch (error) {
      console.error('Authentication middleware error:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
        },
      });
    }
  };
}

// Convenience middleware for common permission checks
export const requireElder = (handler: AuthenticatedHandler) =>
  withAuth(handler, { requireRole: [ROLES.ELDER, ROLES.COORDINATOR] });

export const requireMinisterialServant = (handler: AuthenticatedHandler) =>
  withAuth(handler, {
    requireRole: [
      ROLES.MINISTERIAL_SERVANT,
      ROLES.ELDER,
      ROLES.COORDINATOR,
    ],
  });

export const requireAdmin = (handler: AuthenticatedHandler) =>
  withAuth(handler, { requirePermission: PERMISSIONS.VIEW_ADMIN });
```

### Login API Implementation

```typescript
// pages/api/auth/congregation-login.ts - Congregation authentication endpoint
import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import { prisma } from '@/lib/prisma';
import { SimpleJWTManager } from '@/lib/auth/simpleJWT';
import { validateCongregationLogin } from '@/lib/validation/simple';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  try {
    // Validate input
    const validation = validateCongregationLogin(req.body);
    if (validation.length > 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: validation,
        },
      });
    }

    const { congregationId, pin, region } = req.body;

    // Find congregation
    const congregation = await prisma.congregation.findUnique({
      where: { id: congregationId },
    });

    if (!congregation) {
      return res.status(401).json({
        success: false,
        message: 'Invalid congregation credentials',
      });
    }

    // Verify congregation PIN
    const isPinValid = await bcrypt.compare(pin, congregation.pin);
    if (!isPinValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid congregation credentials',
      });
    }

    // Find a default user for this congregation (first elder or overseer)
    const defaultUser = await prisma.member.findFirst({
      where: {
        congregationId: congregation.id,
        isActive: true,
        role: {
          in: ['elder', 'coordinator'],
        },
      },
      orderBy: {
        role: 'desc', // Prefer coordinator over elder
      },
    });

    if (!defaultUser) {
      return res.status(404).json({
        success: false,
        message: 'No active administrators found for this congregation',
      });
    }

    // Generate JWT token
    const token = SimpleJWTManager.generateToken({
      userId: defaultUser.id,
      congregationId: congregation.id,
      role: defaultUser.role,
      name: defaultUser.name,
    });

    // Return success response
    return res.status(200).json({
      success: true,
      token,
      user: {
        id: defaultUser.id,
        name: defaultUser.name,
        role: defaultUser.role,
        congregationId: congregation.id,
        congregationName: congregation.name,
      },
      congregation: {
        id: congregation.id,
        name: congregation.name,
        region: congregation.region,
        language: congregation.language,
      },
    });
  } catch (error) {
    console.error('Congregation login error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
}
```

### Frontend Authentication Hook

```typescript
// hooks/useAuth.ts - Frontend authentication management
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { SimpleJWTManager } from '@/lib/auth/simpleJWT';

export function useAuth() {
  const router = useRouter();
  const {
    user,
    token,
    isAuthenticated,
    login,
    logout,
    canViewAdmin,
    canManageMembers,
    canUploadLetters,
  } = useAuthStore();

  // Check token validity on mount and periodically
  useEffect(() => {
    if (token && SimpleJWTManager.isTokenExpired(token)) {
      logout();
      router.push('/login');
    }
  }, [token, logout, router]);

  // Login function
  const handleLogin = async (congregationId: string, pin: string, region: string) => {
    try {
      const response = await fetch('/api/auth/congregation-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ congregationId, pin, region }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      // Store authentication data
      login(data.user, data.token);

      return { success: true, user: data.user };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    }
  };

  // Logout function
  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  // Check if user needs to refresh token
  const shouldRefreshToken = () => {
    return token ? SimpleJWTManager.shouldRefreshToken(token) : false;
  };

  return {
    user,
    token,
    isAuthenticated,
    login: handleLogin,
    logout: handleLogout,
    canViewAdmin,
    canManageMembers,
    canUploadLetters,
    shouldRefreshToken,
  };
}

// Protected route wrapper
export function useRequireAuth() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  return isAuthenticated;
}

// Role-based access hook
export function useRequireRole(allowedRoles: string[]) {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (user && !allowedRoles.includes(user.role)) {
      router.push('/dashboard'); // Redirect to dashboard if insufficient role
    }
  }, [user, isAuthenticated, allowedRoles, router]);

  return user && allowedRoles.includes(user.role);
}
```

### Login Page Implementation

```typescript
// app/login/page.tsx - Login page with exact UI replication
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';

export default function LoginPage() {
  const [formData, setFormData] = useState({
    region: '',
    congregationId: '',
    congregationPin: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await login(
        formData.congregationId,
        formData.congregationPin,
        formData.region
      );

      if (result.success) {
        router.push('/dashboard');
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (error) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        {/* Header - Exact replication */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">HERMANOS</h1>
          <h2 className="text-lg text-gray-600">Connect to your Congregation</h2>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* Login form - Exact replication */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="region" className="block text-sm font-medium text-gray-700 mb-1">
              Region
            </label>
            <Select
              id="region"
              value={formData.region}
              onChange={(value) => handleInputChange('region', value)}
              required
              className="w-full"
            >
              <option value="">Select Region</option>
              <option value="North America">North America</option>
              <option value="Central America">Central America</option>
              <option value="South America">South America</option>
              <option value="Europe">Europe</option>
              <option value="Asia">Asia</option>
              <option value="Africa">Africa</option>
              <option value="Oceania">Oceania</option>
            </Select>
          </div>

          <div>
            <label htmlFor="congregationId" className="block text-sm font-medium text-gray-700 mb-1">
              Congregation ID
            </label>
            <Input
              id="congregationId"
              type="text"
              value={formData.congregationId}
              onChange={(e) => handleInputChange('congregationId', e.target.value)}
              placeholder="Enter congregation ID"
              required
              className="w-full"
            />
          </div>

          <div>
            <label htmlFor="congregationPin" className="block text-sm font-medium text-gray-700 mb-1">
              Congregation PIN
            </label>
            <Input
              id="congregationPin"
              type="password"
              value={formData.congregationPin}
              onChange={(e) => handleInputChange('congregationPin', e.target.value)}
              placeholder="Enter congregation PIN"
              required
              className="w-full"
            />
          </div>

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-md font-medium"
          >
            {isLoading ? 'Connecting...' : 'Connect'}
          </Button>
        </form>

        {/* Footer */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>Congregation Management System</p>
          <p className="mt-1">Version 2.0</p>
        </div>
      </div>
    </div>
  );
}
```

This authentication system provides secure, user-friendly access control while maintaining the simplicity that congregation members expect. The implementation preserves the exact login experience while adding robust security measures and multi-congregation support.

---

## 6. API Design

### API Architecture Principles

The API design for the Hermanos application follows RESTful principles while prioritizing simplicity, consistency, and mobile optimization. The API serves as the bridge between the frontend application and the database, ensuring secure data access with proper congregation isolation.

**Core API Principles:**

- **RESTful Design**: Standard HTTP methods and status codes for predictable behavior
- **Congregation Isolation**: All endpoints automatically filter data by congregation
- **Mobile Optimization**: Efficient data transfer and caching for mobile devices
- **Consistent Response Format**: Standardized response structure across all endpoints
- **Error Handling**: Comprehensive error responses with user-friendly messages
- **Authentication Required**: All endpoints require valid JWT authentication
- **Type Safety**: Full TypeScript integration for request/response validation

### API Response Format

```typescript
// lib/types/api.types.ts - Standardized API response types
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    filters?: Record<string, any>;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
  };
}

// Success response helper
export function createSuccessResponse<T>(data: T, meta?: ApiResponse<T>['meta']): ApiResponse<T> {
  return {
    success: true,
    data,
    meta,
  };
}

// Error response helper
export function createErrorResponse(code: string, message: string, details?: any): ApiResponse {
  return {
    success: false,
    error: {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      requestId: generateRequestId(),
    },
  };
}

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
```

### Member Management API

```typescript
// pages/api/members/index.ts - Member management endpoints
import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { PERMISSIONS } from '@/lib/auth/simpleRBAC';
import { createSuccessResponse, createErrorResponse } from '@/lib/types/api.types';
import { validateMemberData } from '@/lib/validation/simple';
import bcrypt from 'bcryptjs';

export default withAuth(
  async (req, res, context) => {
    const { user, congregation } = context;

    switch (req.method) {
      case 'GET':
        return await getMembers(req, res, congregation.id);
      case 'POST':
        return await createMember(req, res, congregation.id, user.id);
      default:
        return res
          .status(405)
          .json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
    }
  },
  { requirePermission: PERMISSIONS.VIEW_ADMIN }
);

// Get all members for congregation
async function getMembers(req: NextApiRequest, res: NextApiResponse, congregationId: string) {
  try {
    const { page = 1, limit = 50, role, active = 'true' } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const take = Math.min(Number(limit), 100); // Max 100 per page

    const where: any = {
      congregationId,
      ...(role && { role: role as string }),
      ...(active !== 'all' && { isActive: active === 'true' }),
    };

    const [members, total] = await Promise.all([
      prisma.member.findMany({
        where,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          lastLogin: true,
          createdAt: true,
        },
        orderBy: { name: 'asc' },
        skip,
        take,
      }),
      prisma.member.count({ where }),
    ]);

    return res.status(200).json(
      createSuccessResponse(members, {
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit)),
        },
      })
    );
  } catch (error) {
    console.error('Get members error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to retrieve members'));
  }
}

// Create new member
async function createMember(
  req: NextApiRequest,
  res: NextApiResponse,
  congregationId: string,
  createdBy: string
) {
  try {
    // Validate input data
    const validation = validateMemberData(req.body);
    if (validation.length > 0) {
      return res
        .status(400)
        .json(createErrorResponse('VALIDATION_ERROR', 'Invalid member data', validation));
    }

    const { name, email, role, pin } = req.body;

    // Check if member with same email already exists in congregation
    if (email) {
      const existingMember = await prisma.member.findFirst({
        where: {
          congregationId,
          email,
          isActive: true,
        },
      });

      if (existingMember) {
        return res
          .status(409)
          .json(createErrorResponse('DUPLICATE_EMAIL', 'A member with this email already exists'));
      }
    }

    // Hash the PIN
    const hashedPin = await bcrypt.hash(pin, 12);

    // Create member
    const member = await prisma.member.create({
      data: {
        congregationId,
        name,
        email: email || null,
        role,
        pin: hashedPin,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true,
      },
    });

    return res.status(201).json(createSuccessResponse(member));
  } catch (error) {
    console.error('Create member error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to create member'));
  }
}
```

### Meeting Management API

```typescript
// pages/api/meetings/midweek/index.ts - Midweek meeting endpoints
import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { PERMISSIONS } from '@/lib/auth/simpleRBAC';
import { createSuccessResponse, createErrorResponse } from '@/lib/types/api.types';

export default withAuth(async (req, res, context) => {
  const { congregation } = context;

  switch (req.method) {
    case 'GET':
      return await getMidweekMeetings(req, res, congregation.id);
    case 'POST':
      return await createMidweekMeeting(req, res, congregation.id);
    default:
      return res.status(405).json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
  }
});

// Get midweek meetings with parts and assignments
async function getMidweekMeetings(
  req: NextApiRequest,
  res: NextApiResponse,
  congregationId: string
) {
  try {
    const { startDate = new Date().toISOString().split('T')[0], endDate, limit = 10 } = req.query;

    const endDateValue =
      endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 30 days from now

    const meetings = await prisma.midweekMeeting.findMany({
      where: {
        congregationId,
        meetingDate: {
          gte: new Date(startDate as string),
          lte: new Date(endDateValue as string),
        },
      },
      include: {
        chairman: {
          select: { id: true, name: true },
        },
        parts: {
          include: {
            assignedMember: {
              select: { id: true, name: true },
            },
            assistant: {
              select: { id: true, name: true },
            },
          },
          orderBy: { displayOrder: 'asc' },
        },
      },
      orderBy: { meetingDate: 'asc' },
      take: Number(limit),
    });

    return res.status(200).json(createSuccessResponse(meetings));
  } catch (error) {
    console.error('Get midweek meetings error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to retrieve meetings'));
  }
}

// Create new midweek meeting
async function createMidweekMeeting(
  req: NextApiRequest,
  res: NextApiResponse,
  congregationId: string
) {
  try {
    const { meetingDate, theme, chairmanId, location, parts } = req.body;

    // Check if meeting already exists for this date
    const existingMeeting = await prisma.midweekMeeting.findFirst({
      where: {
        congregationId,
        meetingDate: new Date(meetingDate),
      },
    });

    if (existingMeeting) {
      return res
        .status(409)
        .json(createErrorResponse('MEETING_EXISTS', 'A meeting already exists for this date'));
    }

    // Create meeting with parts in a transaction
    const meeting = await prisma.$transaction(async tx => {
      const newMeeting = await tx.midweekMeeting.create({
        data: {
          congregationId,
          meetingDate: new Date(meetingDate),
          theme,
          chairmanId,
          location: location || 'Kingdom Hall',
        },
      });

      // Create meeting parts if provided
      if (parts && Array.isArray(parts)) {
        await tx.midweekMeetingPart.createMany({
          data: parts.map((part: any, index: number) => ({
            meetingId: newMeeting.id,
            partType: part.partType,
            title: part.title,
            durationMinutes: part.durationMinutes,
            assignedMemberId: part.assignedMemberId,
            assistantId: part.assistantId,
            displayOrder: index + 1,
            notes: part.notes,
          })),
        });
      }

      return newMeeting;
    });

    // Fetch the complete meeting with parts
    const completeMeeting = await prisma.midweekMeeting.findUnique({
      where: { id: meeting.id },
      include: {
        chairman: {
          select: { id: true, name: true },
        },
        parts: {
          include: {
            assignedMember: {
              select: { id: true, name: true },
            },
            assistant: {
              select: { id: true, name: true },
            },
          },
          orderBy: { displayOrder: 'asc' },
        },
      },
    });

    return res.status(201).json(createSuccessResponse(completeMeeting));
  } catch (error) {
    console.error('Create midweek meeting error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to create meeting'));
  }
}
```

### Task Management API

```typescript
// pages/api/tasks/index.ts - Task management endpoints
import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { createSuccessResponse, createErrorResponse } from '@/lib/types/api.types';

export default withAuth(async (req, res, context) => {
  const { congregation } = context;

  switch (req.method) {
    case 'GET':
      return await getTasks(req, res, congregation.id);
    case 'POST':
      return await createTask(req, res, congregation.id);
    default:
      return res.status(405).json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
  }
});

// Get tasks with optional filtering
async function getTasks(req: NextApiRequest, res: NextApiResponse, congregationId: string) {
  try {
    const { category, active = 'true', page = 1, limit = 20 } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const take = Math.min(Number(limit), 100);

    const where: any = {
      congregationId,
      ...(category && { category: category as string }),
      ...(active !== 'all' && { isActive: active === 'true' }),
    };

    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        include: {
          createdBy: {
            select: { id: true, name: true },
          },
          assignments: {
            where: {
              status: { in: ['assigned', 'in_progress'] },
            },
            include: {
              assignedMember: {
                select: { id: true, name: true },
              },
            },
            orderBy: { assignedDate: 'desc' },
            take: 5, // Latest 5 assignments
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take,
      }),
      prisma.task.count({ where }),
    ]);

    return res.status(200).json(
      createSuccessResponse(tasks, {
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit)),
        },
      })
    );
  } catch (error) {
    console.error('Get tasks error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to retrieve tasks'));
  }
}

// Create new task
async function createTask(req: NextApiRequest, res: NextApiResponse, congregationId: string) {
  try {
    const {
      title,
      description,
      category,
      estimatedDurationMinutes,
      requiresElder,
      requiresMinisterialServant,
      serviceGroupSpecific,
      isRecurring,
      recurrencePattern,
    } = req.body;

    const task = await prisma.task.create({
      data: {
        congregationId,
        title,
        description,
        category,
        estimatedDurationMinutes,
        requiresElder: requiresElder || false,
        requiresMinisterialServant: requiresMinisterialServant || false,
        serviceGroupSpecific: serviceGroupSpecific || false,
        isRecurring: isRecurring || false,
        recurrencePattern: recurrencePattern || null,
        isActive: true,
      },
      include: {
        createdBy: {
          select: { id: true, name: true },
        },
      },
    });

    return res.status(201).json(createSuccessResponse(task));
  } catch (error) {
    console.error('Create task error:', error);
    return res
      .status(500)
      .json(createErrorResponse('INTERNAL_SERVER_ERROR', 'Failed to create task'));
  }
}
```

### File Upload API

```typescript
// pages/api/letters/upload.ts - File upload endpoint
import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { PERMISSIONS } from '@/lib/auth/simpleRBAC';
import { createSuccessResponse, createErrorResponse } from '@/lib/types/api.types';
import formidable from 'formidable';
import fs from 'fs/promises';
import path from 'path';

export const config = {
  api: {
    bodyParser: false, // Disable body parser for file uploads
  },
};

export default withAuth(
  async (req, res, context) => {
    if (req.method !== 'POST') {
      return res.status(405).json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
    }

    return await uploadLetter(req, res, context);
  },
  { requirePermission: PERMISSIONS.UPLOAD_LETTERS }
);

async function uploadLetter(req: NextApiRequest, res: NextApiResponse, context: any) {
  try {
    const { congregation, user } = context;

    // Parse form data
    const form = formidable({
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
      allowEmptyFiles: false,
      filter: ({ mimetype }) => {
        return (
          mimetype === 'application/pdf' || mimetype === 'image/jpeg' || mimetype === 'image/png'
        );
      },
    });

    const [fields, files] = await form.parse(req);

    const title = Array.isArray(fields.title) ? fields.title[0] : fields.title;
    const category = Array.isArray(fields.category) ? fields.category[0] : fields.category;
    const visibility = Array.isArray(fields.visibility) ? fields.visibility[0] : fields.visibility;
    const file = Array.isArray(files.file) ? files.file[0] : files.file;

    if (!file || !title) {
      return res
        .status(400)
        .json(createErrorResponse('MISSING_REQUIRED_FIELDS', 'File and title are required'));
    }

    // Generate unique filename
    const fileExtension = path.extname(file.originalFilename || '');
    const sanitizedTitle = title.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
    const timestamp = Date.now();
    const filename = `${sanitizedTitle}_${timestamp}${fileExtension}`;

    // Create upload directory if it doesn't exist
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'letters', congregation.id);
    await fs.mkdir(uploadDir, { recursive: true });

    // Move file to upload directory
    const filePath = path.join(uploadDir, filename);
    await fs.copyFile(file.filepath, filePath);

    // Clean up temporary file
    await fs.unlink(file.filepath);

    // Save file metadata to database
    const letter = await prisma.letter.create({
      data: {
        congregationId: congregation.id,
        title,
        filename,
        filePath: `/uploads/letters/${congregation.id}/${filename}`,
        fileSize: file.size,
        mimeType: file.mimetype,
        category: category || 'General',
        visibility: visibility || 'ALL_MEMBERS',
        uploadedBy: user.id,
        isActive: true,
      },
      include: {
        uploadedBy: {
          select: { id: true, name: true },
        },
      },
    });

    return res.status(201).json(createSuccessResponse(letter));
  } catch (error) {
    console.error('File upload error:', error);
    return res.status(500).json(createErrorResponse('UPLOAD_FAILED', 'Failed to upload file'));
  }
}
```

### API Client Service

```typescript
// lib/services/apiClient.ts - Frontend API client
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL;
  }

  setToken(token: string) {
    this.token = token;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || 'API request failed');
    }

    return data;
  }

  // Member API methods
  async getMembers(params?: { page?: number; limit?: number; role?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.role) searchParams.set('role', params.role);

    const query = searchParams.toString();
    return this.request(`/members${query ? `?${query}` : ''}`);
  }

  async createMember(memberData: any) {
    return this.request('/members', {
      method: 'POST',
      body: JSON.stringify(memberData),
    });
  }

  // Meeting API methods
  async getMidweekMeetings(params?: { startDate?: string; endDate?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.set('startDate', params.startDate);
    if (params?.endDate) searchParams.set('endDate', params.endDate);

    const query = searchParams.toString();
    return this.request(`/meetings/midweek${query ? `?${query}` : ''}`);
  }

  async createMidweekMeeting(meetingData: any) {
    return this.request('/meetings/midweek', {
      method: 'POST',
      body: JSON.stringify(meetingData),
    });
  }

  // Task API methods
  async getTasks(params?: { category?: string; page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (params?.category) searchParams.set('category', params.category);
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.request(`/tasks${query ? `?${query}` : ''}`);
  }

  async createTask(taskData: any) {
    return this.request('/tasks', {
      method: 'POST',
      body: JSON.stringify(taskData),
    });
  }

  // File upload method
  async uploadLetter(formData: FormData) {
    const url = `${this.baseURL}/letters/upload`;

    const headers: HeadersInit = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData, // Don't set Content-Type for FormData
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || 'Upload failed');
    }

    return data;
  }
}

export const apiClient = new ApiClient();
```

This API design provides a robust, secure, and efficient interface for all congregation management operations while maintaining the simplicity and performance that mobile users expect.

---
