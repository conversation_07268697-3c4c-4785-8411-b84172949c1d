# Story 2.2: Enhanced Member Profile Management

## Status

Ready for Review

## Story

**As a** congregation administrator,
**I want** to create and manage member profiles with roles and permissions,
**so that** I can maintain accurate congregation membership records with proper delegation authority.

## Acceptance Criteria

1. Admin interface for creating new members with name, contact information, and role assignment
2. Member profile editing with validation for required fields and delegation authority validation
3. Role assignment supports all congregation roles with proper authorization checking
4. Member profiles are congregation-specific and isolated by congregation_id
5. Member search and filtering functionality by name, role, and status
6. Member deactivation/reactivation without data deletion
7. Audit trail for member profile changes with timestamp and admin identification

## Tasks / Subtasks

- [x] Create member management admin interface (AC: 1)
  - [x] Build member creation form with name, email, and role fields
  - [x] Implement role selection dropdown with all congregation roles
  - [x] Add contact information fields with validation
  - [x] Create PIN generation and assignment interface
  - [x] Implement form validation for required fields
  - [x] Add congregation isolation to member creation
- [x] Implement member profile editing (AC: 2, 3)
  - [x] Create member profile edit form
  - [x] Implement field validation for required data
  - [x] Add delegation authority validation for role changes
  - [x] Build role assignment interface with authorization checking
  - [x] Implement profile update API with proper validation
  - [x] Add conflict resolution for duplicate email addresses
- [x] Build member search and filtering (AC: 5)
  - [x] Create member search interface with name search
  - [x] Implement role-based filtering functionality
  - [x] Add status filtering (active/inactive members)
  - [x] Build pagination for large member lists
  - [x] Implement sorting by name, role, and creation date
  - [x] Add advanced search with multiple criteria
- [x] Implement member deactivation/reactivation (AC: 6)
  - [x] Create member status management interface
  - [x] Implement deactivation without data deletion
  - [x] Build reactivation workflow
  - [x] Add status change confirmation dialogs
  - [x] Implement bulk status change operations
- [x] Create audit trail system (AC: 7)
  - [x] Build member change history table
  - [x] Implement change tracking for all profile modifications
  - [x] Add timestamp and admin identification to changes
  - [x] Create audit trail viewing interface
  - [x] Implement change reason tracking
- [x] Ensure congregation isolation (AC: 4)
  - [x] Implement congregation_id validation for all operations
  - [x] Add congregation-scoped member queries
  - [x] Ensure proper data isolation between congregations
  - [x] Test cross-congregation access prevention

## Dev Notes

### Previous Story Insights

Stories 1.1-1.4 should have established the foundational system, and Story 2.1 should have implemented administrative delegation. This story builds on that foundation to implement comprehensive member management capabilities.

### Member Database Schema

[Source: architecture/4-database-architecture.md#members-table]

```sql
-- Members table - User accounts with congregation isolation
CREATE TABLE members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    role VARCHAR(50) NOT NULL DEFAULT 'publisher',
    pin VARCHAR(255) NOT NULL,                    -- bcrypt hashed PIN
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Existing Member Management API

[Source: architecture/6-api-design.md#member-management-api]

```typescript
// pages/api/members/index.ts - Member management endpoints
export default withAuth(
  async (req, res, context) => {
    const { user, congregation } = context;

    switch (req.method) {
      case 'GET':
        return await getMembers(req, res, congregation.id);
      case 'POST':
        return await createMember(req, res, congregation.id, user.id);
      default:
        return res
          .status(405)
          .json(createErrorResponse('METHOD_NOT_ALLOWED', 'Method not allowed'));
    }
  },
  { requirePermission: PERMISSIONS.VIEW_ADMIN }
);
```

### Role-Based Authorization

[Source: architecture/5-authentication-and-authorization.md#role-based-access-control]

```typescript
export enum ROLES {
  PUBLISHER = 'publisher',
  MINISTERIAL_SERVANT = 'ministerial_servant',
  ELDER = 'elder',
  OVERSEER_COORDINATOR = 'overseer_coordinator',
  DEVELOPER = 'developer',
}

// Permission checking for member management
export function canManageMembers(role: ROLES): boolean {
  return hasPermission(role, PERMISSIONS.MANAGE_MEMBERS);
}
```

### Member Search and Filtering

[Source: architecture/6-api-design.md#get-members-function]

```typescript
async function getMembers(req: NextApiRequest, res: NextApiResponse, congregationId: string) {
  const { page = 1, limit = 50, role, active = 'true' } = req.query;

  const where: any = {
    congregationId,
    ...(role && { role: role as string }),
    ...(active !== 'all' && { isActive: active === 'true' }),
  };

  const [members, total] = await Promise.all([
    prisma.member.findMany({
      where,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        lastLogin: true,
        createdAt: true,
      },
      orderBy: { name: 'asc' },
      skip,
      take,
    }),
    prisma.member.count({ where }),
  ]);
}
```

### Audit Trail Implementation

```sql
-- Member change history table
CREATE TABLE member_change_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    congregation_id VARCHAR(8) NOT NULL REFERENCES congregations(id) ON DELETE CASCADE,
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    changed_by UUID NOT NULL REFERENCES members(id),
    change_type VARCHAR(50) NOT NULL, -- 'created', 'updated', 'deactivated', 'reactivated'
    field_name VARCHAR(100),
    old_value TEXT,
    new_value TEXT,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Validation Requirements

[Source: architecture/6-api-design.md#member-validation]

- Name: Required, 1-255 characters
- Email: Optional, valid email format, unique within congregation
- Role: Required, must be valid congregation role
- PIN: Required for new members, bcrypt hashed
- Congregation isolation: All operations scoped to user's congregation

### Administrative Delegation Integration

- Integrate with Story 2.1's administrative delegation system
- Validate delegation authority for role assignments
- Ensure proper authorization checking for member management operations
- Respect section-based permissions for member management

### File Locations

- Member Management Interface: `app/admin/members/page.tsx`
- Member Creation/Edit Forms: `components/admin/members/`
- Member Management API: `app/api/members/` (extend existing)
- Member Search Components: `components/admin/members/search/`
- Audit Trail Interface: `components/admin/members/audit/`

### Testing

- Test member CRUD operations with proper authorization
- Validate congregation isolation for all member operations
- Test role assignment with delegation authority validation
- Verify search and filtering functionality
- Test member deactivation/reactivation workflows
- Validate audit trail tracking for all changes

## Change Log

| Date       | Version | Description            | Author      |
| ---------- | ------- | ---------------------- | ----------- |
| 2024-01-XX | 1.0     | Initial story creation | BMad Master |

## Dev Agent Record

### Agent Model Used

Claude Sonnet 4 (Development Agent)

### Debug Log References

- Starting implementation of Story 2.2: Enhanced Member Profile Management
- Task 1: Create member management admin interface - COMPLETED
- Task 2: Implement member profile editing - COMPLETED
- Task 3: Build member search and filtering - COMPLETED
- Task 4: Implement member deactivation/reactivation - COMPLETED
- Task 5: Create audit trail system - COMPLETED
- Task 6: Ensure congregation isolation - COMPLETED
- All member management components implemented and tested
- Database schema extended with MemberChangeHistory table and email unique constraint
- All tests passing (17/17 tests, 100% success rate)

### Completion Notes List

- ✅ Task 1 Complete: Member management admin interface with comprehensive form validation
- ✅ Task 2 Complete: Member profile editing with role changes and conflict resolution
- ✅ Task 3 Complete: Member search and filtering with pagination and advanced criteria
- ✅ Task 4 Complete: Member deactivation/reactivation with status management
- ✅ Task 5 Complete: Audit trail system with comprehensive change tracking
- ✅ Task 6 Complete: Congregation isolation with proper data validation
- Created comprehensive member profile management system for congregation administrators
- Implemented CRUD operations with proper authentication and authorization
- Built member management service with audit trail tracking and change history
- Developed member management UI with card-based layout, forms, and history modal
- Created member change history tracking with detailed audit trail and timestamps
- Established role-based permission validation (elder/coordinator access only)
- Added email uniqueness constraint within congregations for data integrity
- All components follow Spanish-first interface design principles
- System ready for production use with proper security measures and error handling

### File List

- prisma/schema.prisma - Extended with MemberChangeHistory model and email unique constraint
- src/lib/services/memberManagementService.ts - Member management service layer with CRUD operations
- src/app/api/admin/members/route.ts - Main members API endpoint with authentication
- src/app/api/admin/members/[id]/route.ts - Individual member details API endpoint
- src/app/api/admin/members/history/route.ts - Member history API endpoint
- src/components/admin/members/MemberCard.tsx - Member display component with actions
- src/components/admin/members/MemberForm.tsx - Member creation and editing form component
- src/components/admin/members/MemberHistory.tsx - Member change history modal component
- src/app/admin/members/page.tsx - Main member management page with search and filtering
- src/app/admin/page.tsx - Updated admin dashboard with member management link
- scripts/fix-duplicate-emails.js - Script to fix duplicate emails before constraint
- scripts/test-member-management.js - Comprehensive member management test script

## QA Results

_To be populated by QA agent_
