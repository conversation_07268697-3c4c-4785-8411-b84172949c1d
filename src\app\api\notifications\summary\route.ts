/**
 * Notifications Summary API Endpoint
 * 
 * Provides notification summary data for dashboard and overview displays.
 * Includes unread count, recent notifications, and category statistics.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { CommunicationService } from '@/lib/services/communicationService';

/**
 * GET /api/notifications/summary
 * Get notification summary for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get notification summary
    const summary = await CommunicationService.getNotificationSummary(
      member.congregationId,
      member.id
    );

    return NextResponse.json({
      success: true,
      summary,
    });

  } catch (error) {
    console.error('Notifications summary error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve notifications summary',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
