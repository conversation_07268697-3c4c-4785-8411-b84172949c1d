// Territory Duplicate Detection Service
// Service for detecting duplicate territories using fuzzy matching algorithms

import {
  DuplicateDetectionConfig,
  DuplicateMatch,
  DEFAULT_DUPLICATE_CONFIG
} from '@/types/territories/validation';

export class DuplicateDetectionService {
  private static config: DuplicateDetectionConfig = DEFAULT_DUPLICATE_CONFIG;

  /**
   * Set duplicate detection configuration
   */
  static setConfig(config: Partial<DuplicateDetectionConfig>): void {
    this.config = { ...DEFAULT_DUPLICATE_CONFIG, ...config };
  }

  /**
   * Calculate similarity between two strings using Levenshtein distance
   */
  private static calculateSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0;

    // Normalize strings based on configuration
    const normalized1 = this.normalizeString(str1);
    const normalized2 = this.normalizeString(str2);

    if (normalized1 === normalized2) return 1;

    const len1 = normalized1.length;
    const len2 = normalized2.length;

    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;

    // Create matrix for dynamic programming
    const matrix: number[][] = [];
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // Fill the matrix
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = normalized1[i - 1] === normalized2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,     // deletion
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j - 1] + cost // substitution
        );
      }
    }

    const distance = matrix[len1][len2];
    const maxLength = Math.max(len1, len2);
    return (maxLength - distance) / maxLength;
  }

  /**
   * Normalize string based on configuration
   */
  private static normalizeString(str: string): string {
    let normalized = str;

    if (this.config.ignoreCase) {
      normalized = normalized.toLowerCase();
    }

    if (this.config.ignoreSpacing) {
      normalized = normalized.replace(/\s+/g, '');
    }

    if (this.config.ignorePunctuation) {
      normalized = normalized.replace(/[^\w\s]/g, '');
    }

    return normalized.trim();
  }

  /**
   * Calculate Jaro-Winkler similarity (alternative algorithm)
   */
  private static calculateJaroWinklerSimilarity(str1: string, str2: string): number {
    const normalized1 = this.normalizeString(str1);
    const normalized2 = this.normalizeString(str2);

    if (normalized1 === normalized2) return 1;
    if (!normalized1 || !normalized2) return 0;

    const len1 = normalized1.length;
    const len2 = normalized2.length;
    const matchWindow = Math.floor(Math.max(len1, len2) / 2) - 1;

    if (matchWindow < 0) return 0;

    const matches1 = new Array(len1).fill(false);
    const matches2 = new Array(len2).fill(false);
    let matches = 0;
    let transpositions = 0;

    // Find matches
    for (let i = 0; i < len1; i++) {
      const start = Math.max(0, i - matchWindow);
      const end = Math.min(i + matchWindow + 1, len2);

      for (let j = start; j < end; j++) {
        if (matches2[j] || normalized1[i] !== normalized2[j]) continue;
        matches1[i] = matches2[j] = true;
        matches++;
        break;
      }
    }

    if (matches === 0) return 0;

    // Count transpositions
    let k = 0;
    for (let i = 0; i < len1; i++) {
      if (!matches1[i]) continue;
      while (!matches2[k]) k++;
      if (normalized1[i] !== normalized2[k]) transpositions++;
      k++;
    }

    const jaro = (matches / len1 + matches / len2 + (matches - transpositions / 2) / matches) / 3;

    // Apply Winkler prefix bonus
    let prefix = 0;
    for (let i = 0; i < Math.min(len1, len2, 4); i++) {
      if (normalized1[i] === normalized2[i]) prefix++;
      else break;
    }

    return jaro + (0.1 * prefix * (1 - jaro));
  }

  /**
   * Calculate composite similarity score
   */
  private static calculateCompositeSimilarity(
    territory1: { territoryNumber: string; address: string },
    territory2: { territoryNumber: string; address: string }
  ): { score: number; matchedFields: string[] } {
    const scores: { field: string; score: number; weight: number }[] = [];
    const matchedFields: string[] = [];

    // Territory number similarity
    if (this.config.compareFields.includes('territoryNumber')) {
      const numberScore = this.config.enableFuzzyMatching
        ? this.calculateJaroWinklerSimilarity(territory1.territoryNumber, territory2.territoryNumber)
        : territory1.territoryNumber === territory2.territoryNumber ? 1 : 0;
      
      scores.push({ field: 'territoryNumber', score: numberScore, weight: 0.4 });
      
      if (numberScore > this.config.similarityThreshold) {
        matchedFields.push('territoryNumber');
      }
    }

    // Address similarity
    if (this.config.compareFields.includes('address')) {
      const addressScore = this.config.enableFuzzyMatching
        ? this.calculateSimilarity(territory1.address, territory2.address)
        : territory1.address === territory2.address ? 1 : 0;
      
      scores.push({ field: 'address', score: addressScore, weight: 0.6 });
      
      if (addressScore > this.config.similarityThreshold) {
        matchedFields.push('address');
      }
    }

    // Calculate weighted average
    const totalWeight = scores.reduce((sum, s) => sum + s.weight, 0);
    const weightedScore = scores.reduce((sum, s) => sum + (s.score * s.weight), 0) / totalWeight;

    return { score: weightedScore, matchedFields };
  }

  /**
   * Detect duplicates in a list of territories
   */
  static detectDuplicates(territories: Array<{
    id?: string;
    territoryNumber: string;
    address: string;
    notes?: string;
  }>): DuplicateMatch[] {
    const duplicates: DuplicateMatch[] = [];
    const processed = new Set<string>();

    for (let i = 0; i < territories.length; i++) {
      const territory1 = territories[i];
      const key1 = `${i}-${territory1.id || territory1.territoryNumber}`;
      
      if (processed.has(key1)) continue;

      for (let j = i + 1; j < territories.length; j++) {
        const territory2 = territories[j];
        const key2 = `${j}-${territory2.id || territory2.territoryNumber}`;
        
        if (processed.has(key2)) continue;

        const { score, matchedFields } = this.calculateCompositeSimilarity(territory1, territory2);

        if (score >= this.config.similarityThreshold) {
          const confidence = this.getConfidenceLevel(score);
          const suggestedAction = this.getSuggestedAction(score, matchedFields);

          duplicates.push({
            id: `dup-${Date.now()}-${i}-${j}`,
            territory1: {
              id: territory1.id,
              territoryNumber: territory1.territoryNumber,
              address: territory1.address
            },
            territory2: {
              id: territory2.id,
              territoryNumber: territory2.territoryNumber,
              address: territory2.address
            },
            similarityScore: score,
            matchedFields,
            confidence,
            suggestedAction,
            isResolved: false
          });

          // Mark both territories as processed for this comparison
          processed.add(key1);
          processed.add(key2);
        }
      }
    }

    return duplicates.sort((a, b) => b.similarityScore - a.similarityScore);
  }

  /**
   * Check if a single territory is a duplicate of existing territories
   */
  static async checkForDuplicates(
    newTerritory: { territoryNumber: string; address: string },
    existingTerritories: Array<{ id: string; territoryNumber: string; address: string }>
  ): Promise<DuplicateMatch[]> {
    const duplicates: DuplicateMatch[] = [];

    for (const existing of existingTerritories) {
      const { score, matchedFields } = this.calculateCompositeSimilarity(newTerritory, existing);

      if (score >= this.config.similarityThreshold) {
        const confidence = this.getConfidenceLevel(score);
        const suggestedAction = this.getSuggestedAction(score, matchedFields);

        duplicates.push({
          id: `dup-${Date.now()}-${existing.id}`,
          territory1: {
            territoryNumber: newTerritory.territoryNumber,
            address: newTerritory.address
          },
          territory2: {
            id: existing.id,
            territoryNumber: existing.territoryNumber,
            address: existing.address
          },
          similarityScore: score,
          matchedFields,
          confidence,
          suggestedAction,
          isResolved: false
        });
      }
    }

    return duplicates.sort((a, b) => b.similarityScore - a.similarityScore);
  }

  /**
   * Get confidence level based on similarity score
   */
  private static getConfidenceLevel(score: number): 'low' | 'medium' | 'high' {
    if (score >= 0.95) return 'high';
    if (score >= 0.85) return 'medium';
    return 'low';
  }

  /**
   * Get suggested action based on score and matched fields
   */
  private static getSuggestedAction(
    score: number,
    matchedFields: string[]
  ): 'merge' | 'keep_both' | 'manual_review' {
    // High confidence exact matches
    if (score >= 0.95 && matchedFields.includes('territoryNumber')) {
      return 'merge';
    }

    // Medium confidence with address match
    if (score >= 0.9 && matchedFields.includes('address')) {
      return 'merge';
    }

    // Low confidence or partial matches
    if (score >= 0.8) {
      return 'manual_review';
    }

    return 'keep_both';
  }

  /**
   * Get duplicate detection statistics
   */
  static getDetectionStatistics(duplicates: DuplicateMatch[]): {
    totalDuplicates: number;
    byConfidence: Record<'low' | 'medium' | 'high', number>;
    bySuggestedAction: Record<'merge' | 'keep_both' | 'manual_review', number>;
    averageSimilarity: number;
    highestSimilarity: number;
  } {
    const byConfidence = { low: 0, medium: 0, high: 0 };
    const bySuggestedAction = { merge: 0, keep_both: 0, manual_review: 0 };
    let totalSimilarity = 0;
    let highestSimilarity = 0;

    for (const duplicate of duplicates) {
      byConfidence[duplicate.confidence]++;
      bySuggestedAction[duplicate.suggestedAction]++;
      totalSimilarity += duplicate.similarityScore;
      highestSimilarity = Math.max(highestSimilarity, duplicate.similarityScore);
    }

    return {
      totalDuplicates: duplicates.length,
      byConfidence,
      bySuggestedAction,
      averageSimilarity: duplicates.length > 0 ? totalSimilarity / duplicates.length : 0,
      highestSimilarity
    };
  }

  /**
   * Filter duplicates by confidence level
   */
  static filterByConfidence(
    duplicates: DuplicateMatch[],
    minConfidence: 'low' | 'medium' | 'high'
  ): DuplicateMatch[] {
    const confidenceOrder = { low: 1, medium: 2, high: 3 };
    const minLevel = confidenceOrder[minConfidence];

    return duplicates.filter(duplicate => 
      confidenceOrder[duplicate.confidence] >= minLevel
    );
  }

  /**
   * Group duplicates by territory
   */
  static groupDuplicatesByTerritory(duplicates: DuplicateMatch[]): Map<string, DuplicateMatch[]> {
    const groups = new Map<string, DuplicateMatch[]>();

    for (const duplicate of duplicates) {
      const key1 = duplicate.territory1.id || duplicate.territory1.territoryNumber;
      const key2 = duplicate.territory2.id || duplicate.territory2.territoryNumber;

      if (!groups.has(key1)) groups.set(key1, []);
      if (!groups.has(key2)) groups.set(key2, []);

      groups.get(key1)!.push(duplicate);
      groups.get(key2)!.push(duplicate);
    }

    return groups;
  }
}
