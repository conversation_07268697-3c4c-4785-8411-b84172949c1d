'use client';

/**
 * PIN Management Page
 *
 * Comprehensive PIN management interface for congregation administrators
 * with settings configuration, PIN reset, and audit trail.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import PinSettingsForm, { PinSettingsFormData } from '@/components/admin/pin-management/PinSettingsForm';
import PinResetForm, { PinResetFormData } from '@/components/admin/pin-management/PinResetForm';
import PinHistory from '@/components/admin/pin-management/PinHistory';
import { PinSettings, PinChangeRecord } from '@/lib/services/pinService';
import AdminFooter from '@/components/admin/AdminFooter';
import { MemberProfile } from '@/lib/services/memberManagementService';

export default function PinManagementPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string>('');

  // Data state
  const [pinSettings, setPinSettings] = useState<PinSettings | null>(null);
  const [members, setMembers] = useState<MemberProfile[]>([]);
  const [pinHistory, setPinHistory] = useState<PinChangeRecord[]>([]);

  // UI state
  const [activeTab, setActiveTab] = useState<'settings' | 'reset' | 'history'>('settings');
  const [showHistory, setShowHistory] = useState(false);
  const [resetResult, setResetResult] = useState<{ newPin: string; memberName: string } | null>(null);

  useEffect(() => {
    checkAccess();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (userRole) {
      loadData();
    }
  }, [userRole]);

  const checkAccess = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      // Verify token and check permissions
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserRole(data.user.role);

        // Check if user has admin access
        if (!data.permissions.canAccessAdmin) {
          router.push('/dashboard');
          return;
        }
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Access check failed:', error);
      router.push('/login');
    }
  };

  const loadData = async () => {
    try {
      setError(null);
      const token = localStorage.getItem('hermanos_token');

      // Load PIN settings
      const settingsResponse = await fetch('/api/admin/pin-management/settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        setPinSettings(settingsData.settings);
      }

      // Load members for PIN reset
      const membersResponse = await fetch('/api/admin/members?limit=100', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (membersResponse.ok) {
        const membersData = await membersResponse.json();
        setMembers(membersData.members);
      }

    } catch (error) {
      console.error('Error loading data:', error);
      setError('Error al cargar los datos');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateSettings = async (formData: PinSettingsFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/pin-management/settings', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        setPinSettings(data.settings);
        setError(null);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update PIN settings');
      }
    } catch (error) {
      console.error('Error updating PIN settings:', error);
      setError(error instanceof Error ? error.message : 'Error al actualizar la configuración');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResetPin = async (formData: PinResetFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/pin-management/reset', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        const member = members.find(m => m.id === formData.memberId);
        setResetResult({
          newPin: data.newPin,
          memberName: member?.name || 'Miembro',
        });
        setError(null);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reset PIN');
      }
    } catch (error) {
      console.error('Error resetting PIN:', error);
      setError(error instanceof Error ? error.message : 'Error al restablecer el PIN');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewHistory = async () => {
    try {
      setShowHistory(true);
      setPinHistory([]);

      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/pin-management/history?limit=100', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPinHistory(data.history);
      } else {
        throw new Error('Failed to load PIN history');
      }
    } catch (error) {
      console.error('Error loading PIN history:', error);
      setError('Error al cargar el historial de PINs');
    }
  };

  const handleBackToDashboard = () => {
    router.push('/admin');
  };

  const canEditSettings = userRole === 'developer';
  const canResetPins = ['elder', 'overseer_coordinator', 'developer'].includes(userRole);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando gestión de PINs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-purple-600 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-white">
                Gestión de PINs
              </h1>
              <p className="text-purple-100">
                Configuración de seguridad y administración de PINs
              </p>
            </div>
            <button
              onClick={handleBackToDashboard}
              className="bg-purple-700 hover:bg-purple-800 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Volver al Admin
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 pb-20">
        <div className="px-4 py-6 sm:px-0">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800">{error}</p>
              <button
                onClick={() => setError(null)}
                className="text-red-600 hover:text-red-800 text-sm mt-2"
              >
                Cerrar
              </button>
            </div>
          )}

          {/* Tabs */}
          <div className="mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab('settings')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'settings'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Configuración
              </button>
              {canResetPins && (
                <button
                  onClick={() => {
                    setActiveTab('reset');
                    setResetResult(null);
                  }}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'reset'
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Restablecer PINs
                </button>
              )}
              <button
                onClick={() => {
                  setActiveTab('history');
                  handleViewHistory();
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Historial
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {activeTab === 'settings' && pinSettings && (
              <PinSettingsForm
                settings={pinSettings}
                onSubmit={handleUpdateSettings}
                onCancel={() => {}}
                isLoading={isSubmitting}
                canEdit={canEditSettings}
              />
            )}

            {activeTab === 'reset' && canResetPins && (
              <PinResetForm
                members={members}
                onSubmit={handleResetPin}
                onCancel={() => setActiveTab('settings')}
                isLoading={isSubmitting}
                resetResult={resetResult}
              />
            )}

            {activeTab === 'history' && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold text-gray-900">
                    Historial de Cambios de PIN
                  </h2>
                  <button
                    onClick={handleViewHistory}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    Actualizar
                  </button>
                </div>

                {pinHistory.length === 0 ? (
                  <div className="text-center py-12">
                    <p className="text-gray-500 text-lg">
                      No hay cambios de PIN registrados
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pinHistory.slice(0, 10).map((record) => (
                      <div key={record.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">
                              {record.memberName || 'PIN de Congregación'} - {record.changeType}
                            </p>
                            <p className="text-sm text-gray-600">
                              Por: {record.changedByName} | {new Date(record.createdAt).toLocaleString('es-ES')}
                            </p>
                            {record.reason && (
                              <p className="text-sm text-gray-500 mt-1">{record.reason}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}

                    {pinHistory.length > 10 && (
                      <div className="text-center">
                        <button
                          onClick={() => setShowHistory(true)}
                          className="text-purple-600 hover:text-purple-800 text-sm font-medium"
                        >
                          Ver historial completo ({pinHistory.length} registros)
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </main>

      {/* PIN History Modal */}
      {showHistory && (
        <PinHistory
          history={pinHistory}
          isLoading={false}
          onClose={() => setShowHistory(false)}
        />
      )}

      {/* Admin Footer */}
      <AdminFooter currentSection="pin-management" />
    </div>
  );
}
