/**
 * Administrative Sections Constants
 *
 * Defines the administrative sections that can be delegated to elders
 * and ministerial servants by coordinator elders.
 */

export enum ADMINISTRATIVE_SECTIONS {
  FIELD_SERVICE = 'field_service',
  MEETINGS = 'meetings',
  TASKS = 'tasks',
  LETTERS = 'letters',
  EVENTS = 'events',
}

export interface AdministrativeSection {
  id: ADMINISTRATIVE_SECTIONS;
  name: string;
  description: string;
  icon: string;
  color: string;
  permissions: string[];
  eligibleRoles: string[];
}

export const ADMINISTRATIVE_SECTION_DEFINITIONS: Record<ADMINISTRATIVE_SECTIONS, AdministrativeSection> = {
  [ADMINISTRATIVE_SECTIONS.FIELD_SERVICE]: {
    id: ADMINISTRATIVE_SECTIONS.FIELD_SERVICE,
    name: '<PERSON><PERSON><PERSON> del Campo',
    description: 'Gestión de territorios, grupos de servicio y coordinación del ministerio',
    icon: 'shield',
    color: 'blue',
    permissions: [
      'view_all_service_reports',
      'manage_service_groups',
      'assign_territories',
      'coordinate_field_service',
    ],
    eligibleRoles: ['elder', 'ministerial_servant'],
  },
  [ADMINISTRATIVE_SECTIONS.MEETINGS]: {
    id: ADMINISTRATIVE_SECTIONS.MEETINGS,
    name: 'Reuniones',
    description: 'Coordinación de reuniones entre semana y fin de semana',
    icon: 'users',
    color: 'green',
    permissions: [
      'manage_meetings',
      'assign_meeting_parts',
      'schedule_meetings',
      'manage_meeting_assignments',
    ],
    eligibleRoles: ['elder', 'ministerial_servant'],
  },
  [ADMINISTRATIVE_SECTIONS.TASKS]: {
    id: ADMINISTRATIVE_SECTIONS.TASKS,
    name: 'Tareas',
    description: 'Creación y asignación de tareas de la congregación',
    icon: 'clipboard-list',
    color: 'orange',
    permissions: [
      'manage_tasks',
      'assign_tasks',
      'view_all_tasks',
      'create_tasks',
    ],
    eligibleRoles: ['elder', 'ministerial_servant'],
  },
  [ADMINISTRATIVE_SECTIONS.LETTERS]: {
    id: ADMINISTRATIVE_SECTIONS.LETTERS,
    name: 'Cartas',
    description: 'Gestión de documentos y comunicaciones de la congregación',
    icon: 'mail',
    color: 'indigo',
    permissions: [
      'manage_letters',
      'upload_letters',
      'categorize_letters',
      'control_letter_access',
    ],
    eligibleRoles: ['elder', 'ministerial_servant'],
  },
  [ADMINISTRATIVE_SECTIONS.EVENTS]: {
    id: ADMINISTRATIVE_SECTIONS.EVENTS,
    name: 'Eventos',
    description: 'Planificación y coordinación de actividades de la congregación',
    icon: 'calendar',
    color: 'pink',
    permissions: [
      'manage_events',
      'create_events',
      'coordinate_activities',
      'manage_event_attendance',
    ],
    eligibleRoles: ['elder', 'ministerial_servant'],
  },
};

export const getAllAdministrativeSections = (): AdministrativeSection[] => {
  return Object.values(ADMINISTRATIVE_SECTION_DEFINITIONS);
};

export const getAdministrativeSection = (sectionId: ADMINISTRATIVE_SECTIONS): AdministrativeSection | undefined => {
  return ADMINISTRATIVE_SECTION_DEFINITIONS[sectionId];
};

export const getSectionsByRole = (role: string): AdministrativeSection[] => {
  return getAllAdministrativeSections().filter(section =>
    section.eligibleRoles.includes(role)
  );
};

export const canAssignSection = (assignerRole: string, targetRole: string, sectionId: ADMINISTRATIVE_SECTIONS): boolean => {
  // Only coordinators can assign sections
  if (assignerRole !== 'coordinator') {
    return false;
  }

  const section = getAdministrativeSection(sectionId);
  if (!section) {
    return false;
  }

  // Check if target role is eligible for this section
  return section.eligibleRoles.includes(targetRole);
};

export interface ScopeDefinition {
  description?: string;
  limitations?: string[];
  specificAreas?: string[];
  expirationDate?: string;
  notes?: string;
}

export const getDefaultScopeDefinition = (sectionId: ADMINISTRATIVE_SECTIONS): ScopeDefinition => {
  const section = getAdministrativeSection(sectionId);
  return {
    description: `Responsabilidad completa para ${section?.name || 'esta sección'}`,
    limitations: [],
    specificAreas: [],
    notes: 'Asignación estándar con responsabilidades completas de la sección',
  };
};
