// Using built-in fetch (Node.js 18+)

async function testLogin() {
  try {
    console.log('🧪 Testing congregation login...');

    const loginData = {
      region: 'North America',
      congregationId: '1441',
      pin: '1930',
      rememberMe: false,
    };

    console.log('📤 Sending login request with:');
    console.log('   Region:', loginData.region);
    console.log('   Congregation ID:', loginData.congregationId);
    console.log('   PIN:', loginData.pin);

    const response = await fetch('http://localhost:3000/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData),
    });

    console.log('\n📥 Response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Login successful!');
      console.log('🎫 Token received:', data.token ? 'Yes' : 'No');
      console.log('👤 User info:', data.user ? `${data.user.name} (${data.user.role})` : 'Not provided');
      console.log('🏛️ Congregation:', data.congregation ? data.congregation.name : 'Not provided');
    } else {
      const errorData = await response.json();
      console.log('❌ Login failed!');
      console.log('🚫 Error:', errorData.error || 'Unknown error');
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testLogin();
