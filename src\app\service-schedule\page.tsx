'use client';

/**
 * Service Schedule Page (Member View)
 *
 * Displays weekly service schedules with expandable date sections
 * matching the Servicio-del-Campo.png design.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface ServiceScheduleTime {
  id: string;
  serviceDate: string;
  serviceTime: string;
  location: string;
  address?: string;
  conductor?: {
    id: string;
    name: string;
    role: string;
  };
  notes?: string;
}

interface ServiceSchedule {
  id: string;
  weekStartDate: string;
  weekEndDate: string;
  scheduleTimes: ServiceScheduleTime[];
}

interface ScheduleNavigation {
  currentWeek: string;
  previousWeek: string;
  nextWeek: string;
  isCurrentWeek: boolean;
}

export default function ServiceSchedulePage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [schedule, setSchedule] = useState<ServiceSchedule | null>(null);
  const [navigation, setNavigation] = useState<ScheduleNavigation | null>(null);
  const [currentWeekStart, setCurrentWeekStart] = useState('');
  const [expandedDates, setExpandedDates] = useState<Set<string>>(new Set());

  useEffect(() => {
    checkAuthentication();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      fetchServiceSchedule();
    }
  }, [user, currentWeekStart]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();
      setUser(data.member);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchServiceSchedule = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('hermanos_token');
      const url = currentWeekStart 
        ? `/api/service-schedule?weekStartDate=${currentWeekStart}`
        : '/api/service-schedule';

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSchedule(data.schedule);
        setNavigation(data.navigation);
        
        if (!currentWeekStart) {
          setCurrentWeekStart(data.weekStartDate);
        }
      }
    } catch (error) {
      console.error('Error fetching service schedule:', error);
    }
  };

  const formatDateRange = (startDate: string, endDate: string): string => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const startDay = start.getDate();
    const endDay = end.getDate();
    const month = start.toLocaleDateString('es-ES', { month: 'long' });
    const year = start.getFullYear();
    
    return `${startDay}-${endDay} ${month} de ${year}`;
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const dayName = date.toLocaleDateString('es-ES', { weekday: 'long' });
    const dayNumber = date.getDate();
    const month = date.toLocaleDateString('es-ES', { month: 'long' });
    
    return `${dayName}, ${dayNumber} de ${month}`;
  };

  const groupSchedulesByDate = (scheduleTimes: ServiceScheduleTime[]) => {
    const grouped: { [key: string]: ServiceScheduleTime[] } = {};
    
    scheduleTimes.forEach(time => {
      const date = time.serviceDate;
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(time);
    });
    
    // Sort times within each date
    Object.keys(grouped).forEach(date => {
      grouped[date].sort((a, b) => a.serviceTime.localeCompare(b.serviceTime));
    });
    
    return grouped;
  };

  const toggleDateExpansion = (date: string) => {
    const newExpanded = new Set(expandedDates);
    if (newExpanded.has(date)) {
      newExpanded.delete(date);
    } else {
      newExpanded.add(date);
    }
    setExpandedDates(newExpanded);
  };

  const navigateWeek = (direction: 'previous' | 'next' | 'current') => {
    if (!navigation) return;
    
    let targetDate = '';
    switch (direction) {
      case 'previous':
        targetDate = navigation.previousWeek;
        break;
      case 'next':
        targetDate = navigation.nextWeek;
        break;
      case 'current':
        targetDate = navigation.currentWeek;
        break;
    }
    
    setCurrentWeekStart(targetDate);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando horarios...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const groupedSchedule = schedule?.scheduleTimes ? groupSchedulesByDate(schedule.scheduleTimes) : {};
  const sortedDates = Object.keys(groupedSchedule).sort();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-purple-600 text-white p-4">
        <div className="max-w-md mx-auto flex items-center">
          <button
            onClick={() => router.push('/dashboard')}
            className="text-purple-200 hover:text-white mr-4"
          >
            ← 
          </button>
          <h1 className="text-xl font-semibold">Servicio del Campo</h1>
        </div>
      </div>

      <div className="max-w-md mx-auto bg-white min-h-screen">
        {/* Week Navigation */}
        {navigation && (
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <button
                onClick={() => navigateWeek('previous')}
                className="text-purple-600 hover:text-purple-800"
              >
                ← Anterior
              </button>
              <button
                onClick={() => navigateWeek('current')}
                className={`px-3 py-1 rounded-full text-sm ${
                  navigation.isCurrentWeek
                    ? 'bg-purple-100 text-purple-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Esta semana
              </button>
              <button
                onClick={() => navigateWeek('next')}
                className="text-purple-600 hover:text-purple-800"
              >
                Siguiente →
              </button>
            </div>
            
            {schedule && (
              <div className="text-center">
                <p className="text-lg font-medium text-gray-900">
                  {formatDateRange(schedule.weekStartDate, schedule.weekEndDate)}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Service Schedule */}
        <div className="p-4">
          {sortedDates.length === 0 ? (
            <div className="text-center py-12">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-gray-500">No hay horarios programados para esta semana</p>
            </div>
          ) : (
            <div className="space-y-2">
              {sortedDates.map((date) => {
                const times = groupedSchedule[date];
                const isExpanded = expandedDates.has(date);
                
                return (
                  <div key={date} className="border border-gray-200 rounded-lg overflow-hidden">
                    {/* Date Header */}
                    <button
                      onClick={() => toggleDateExpansion(date)}
                      className="w-full bg-purple-600 text-white p-4 flex items-center justify-between hover:bg-purple-700 transition-colors"
                    >
                      <span className="font-medium">
                        {formatDate(date)}
                      </span>
                      {isExpanded ? (
                        <ChevronUpIcon className="w-5 h-5" />
                      ) : (
                        <ChevronDownIcon className="w-5 h-5" />
                      )}
                    </button>
                    
                    {/* Service Times */}
                    {isExpanded && (
                      <div className="bg-white">
                        {times.map((time) => (
                          <div key={time.id} className="p-4 border-b border-gray-100 last:border-b-0">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <p className="text-lg font-semibold text-gray-900">
                                  {time.serviceTime}
                                </p>
                                <p className="text-gray-600">
                                  {time.location}
                                </p>
                                {time.address && (
                                  <p className="text-sm text-gray-500 mt-1">
                                    {time.address}
                                  </p>
                                )}
                                {time.conductor && (
                                  <div className="mt-2">
                                    <span className="text-sm text-gray-500">Conductor</span>
                                    <p className="text-sm font-medium text-gray-900">
                                      {time.conductor.name}
                                    </p>
                                  </div>
                                )}
                                {time.notes && (
                                  <div className="mt-2">
                                    <span className="text-sm text-gray-500">Notas</span>
                                    <p className="text-sm text-gray-700">
                                      {time.notes}
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
