const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addServiceGroups() {
  try {
    console.log('Adding address field and sample service groups...');

    // Check if service groups already exist
    const existingGroups = await prisma.serviceGroup.findMany({
      where: {
        congregationId: '1441'
      }
    });

    if (existingGroups.length > 0) {
      console.log(`Found ${existingGroups.length} existing service groups. Updating them with addresses...`);
      
      // Update existing groups with addresses
      const addresses = [
        '123 Main St, Miami, FL',
        '456 Oak Ave, Miami, FL', 
        '789 Pine Rd, Miami, FL',
        '321 Elm St, Miami, FL',
        '654 Maple Dr, Miami, FL',
        '987 Cedar Ln, Miami, FL',
        '147 Birch Way, Miami, FL'
      ];

      for (let i = 0; i < existingGroups.length && i < addresses.length; i++) {
        await prisma.serviceGroup.update({
          where: { id: existingGroups[i].id },
          data: { address: addresses[i] }
        });
        console.log(`Updated Group ${existingGroups[i].groupNumber} with address: ${addresses[i]}`);
      }
    } else {
      console.log('No existing service groups found. Creating new ones...');
      
      // Create new service groups
      const serviceGroups = [
        { name: 'Grupo 1', groupNumber: 1, address: '123 Main St, Miami, FL' },
        { name: 'Grupo 2', groupNumber: 2, address: '456 Oak Ave, Miami, FL' },
        { name: 'Grupo 3', groupNumber: 3, address: '789 Pine Rd, Miami, FL' },
        { name: 'Grupo 4', groupNumber: 4, address: '321 Elm St, Miami, FL' },
        { name: 'Grupo 5', groupNumber: 5, address: '654 Maple Dr, Miami, FL' },
        { name: 'Grupo 6', groupNumber: 6, address: '987 Cedar Ln, Miami, FL' },
        { name: 'Grupo 7', groupNumber: 7, address: '147 Birch Way, Miami, FL' }
      ];

      for (const group of serviceGroups) {
        await prisma.serviceGroup.create({
          data: {
            congregationId: '1441',
            name: group.name,
            groupNumber: group.groupNumber,
            address: group.address,
            isActive: true
          }
        });
        console.log(`Created ${group.name} with address: ${group.address}`);
      }
    }

    console.log('Service groups setup completed successfully!');
  } catch (error) {
    console.error('Error setting up service groups:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addServiceGroups();
