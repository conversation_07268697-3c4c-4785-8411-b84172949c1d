/**
 * Import Territory 001 Script
 * 
 * Imports Territory 001 from Excel file with proper address parsing logic.
 * Understands the format where street names are followed by house numbers.
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

function parseAddresses(excelData) {
  const addresses = [];
  let currentStreet = null;
  let zipCode = 'Miami, FL 33126'; // Default from the Excel file
  
  // Extract ZIP code from row 3 if available
  const zipRow = excelData.find(row => row && row[1] && typeof row[1] === 'string' && row[1].includes('Zip:'));
  if (zipRow && zipRow[1]) {
    const zipMatch = zipRow[1].match(/Zip:\s*(.+)/);
    if (zipMatch) {
      zipCode = zipMatch[1].trim();
    }
  }
  
  console.log(`📍 Using ZIP code: ${zipCode}`);
  
  for (let i = 0; i < excelData.length; i++) {
    const row = excelData[i];
    if (!row || !row[1]) continue;
    
    const cellValue = row[1];
    
    // Skip header rows and empty cells
    if (typeof cellValue === 'string' && (
      cellValue.includes('REGISTRO') || 
      cellValue.includes('Zip:') || 
      cellValue.includes('Símbolos') ||
      cellValue.includes('Fechas') ||
      cellValue.includes('No. de Casa')
    )) {
      continue;
    }
    
    // Check if this is a street name (string that doesn't look like a house number)
    if (typeof cellValue === 'string' && cellValue.trim() && 
        !cellValue.match(/^\d+[a-z]?$/i) && // Not just a number with optional letter
        cellValue.length > 3) { // Reasonable street name length
      
      currentStreet = cellValue.trim();
      console.log(`🛣️  Found street: ${currentStreet}`);
      continue;
    }
    
    // Check if this is a house number
    if (currentStreet && (typeof cellValue === 'number' || 
        (typeof cellValue === 'string' && cellValue.match(/^\d+[a-z]?$/i)))) {
      
      const houseNumber = cellValue.toString().trim();
      const fullAddress = `${houseNumber} ${currentStreet}, ${zipCode}`;
      
      // Get notes from column G (index 6) if available
      const notes = row[6] && typeof row[6] === 'string' ? row[6].trim() : null;
      
      addresses.push({
        address: fullAddress,
        notes: notes && notes !== 'null' ? notes : null,
        street: currentStreet,
        houseNumber: houseNumber
      });
      
      console.log(`🏠 Added address: ${fullAddress}${notes ? ` (${notes})` : ''}`);
    }
  }
  
  return addresses;
}

async function importTerritory001() {
  try {
    console.log('📂 Importing Territory 001...');

    // Read Excel file
    const filePath = path.join(__dirname, '..', 'Territorios', 'Terr. 001.xlsx');
    const workbook = XLSX.readFile(filePath);
    const worksheet = workbook.Sheets['Terr 1']; // Use the correct sheet name
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse addresses
    const addresses = parseAddresses(excelData);
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    if (addresses.length === 0) {
      console.log('⚠️  No addresses found to import');
      return;
    }
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (!congregation) {
      console.error('❌ Congregation 1441 (Coral Oeste) not found');
      process.exit(1);
    }

    console.log(`✅ Found congregation: ${congregation.name}`);
    
    // Clear existing Territory 001 if it exists
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: congregation.id,
        territory: {
          territoryNumber: '001'
        }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: congregation.id,
        territoryNumber: '001'
      }
    });
    
    console.log('🗑️  Cleared existing Territory 001');
    
    // Create the territory with all addresses combined
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: congregation.id,
        territoryNumber: '001',
        address: allAddresses,
        status: 'available',
        notes: allNotes || null
      }
    });

    console.log(`✅ Created Territory 001 with ${addresses.length} addresses`);
    console.log(`📍 Territory ID: ${territory.id}`);
    
    // Display summary
    console.log('\n📋 Address Summary:');
    addresses.forEach((addr, index) => {
      console.log(`   ${index + 1}. ${addr.address}`);
    });
    
    console.log('\n🎉 Territory 001 imported successfully!');

  } catch (error) {
    console.error('❌ Error importing Territory 001:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  importTerritory001();
}

module.exports = { importTerritory001, parseAddresses };
