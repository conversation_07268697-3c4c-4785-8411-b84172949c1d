/**
 * Dashboard Implementation Comparison Script
 * 
 * Compares the reference implementation vs current implementation
 * to identify specific visual discrepancies and provide evidence.
 */

const puppeteer = require('puppeteer');

async function compareImplementations() {
  console.log('🔍 Comparing Reference vs Current Dashboard Implementation...\n');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 375, height: 812 }
  });
  
  try {
    // Capture Reference Implementation
    console.log('📸 Attempting to capture Reference Implementation...');
    const referencePage = await browser.newPage();
    let referenceAnalysis = null;
    
    try {
      await referencePage.goto('file:///C:/laragon/www/SalonDelReino/dashboard.html', { 
        waitUntil: 'networkidle0',
        timeout: 15000 
      });
      
      await referencePage.screenshot({ 
        path: 'reference-dashboard.png',
        fullPage: true
      });
      console.log('✅ Reference screenshot saved as reference-dashboard.png');
      
      referenceAnalysis = await analyzeImplementation(referencePage, 'Reference');
      
    } catch (error) {
      console.log('⚠️  Could not access reference implementation:', error.message);
      console.log('   Using reference screenshot from IMAGES OF OUR APP folder instead\n');
    }
    
    // Capture Current Implementation
    console.log('📸 Capturing Current Implementation...');
    const currentPage = await browser.newPage();
    
    // Login to current implementation
    await currentPage.goto('http://localhost:3001/login');
    await currentPage.waitForSelector('input[type="text"]', { timeout: 10000 });
    
    await currentPage.type('input[type="text"]', '1441');
    await currentPage.type('input[type="password"]', '1441');
    await currentPage.click('button[type="submit"]');
    
    // Wait for dashboard
    await currentPage.waitForSelector('[data-testid="dashboard-grid"]', { timeout: 10000 });
    
    await currentPage.screenshot({ 
      path: 'current-dashboard.png',
      fullPage: true
    });
    console.log('✅ Current screenshot saved as current-dashboard.png');
    
    const currentAnalysis = await analyzeImplementation(currentPage, 'Current');
    
    // Perform detailed comparison
    console.log('\n🎯 DETAILED VISUAL DISCREPANCY ANALYSIS:');
    await performDetailedComparison(currentPage);
    
    // Generate comparison report
    console.log('\n📋 COMPARISON SUMMARY:');
    console.log('Reference: file:///C:/laragon/www/SalonDelReino/dashboard.html');
    console.log('Current:   http://localhost:3001/dashboard');
    console.log('\nScreenshots captured for manual comparison:');
    console.log('- reference-dashboard.png (if accessible)');
    console.log('- current-dashboard.png');
    console.log('- Reference image: C:/laragon/www/HERMANOS/IMAGES OF OUR APP/dashboard-members.png');
    
  } catch (error) {
    console.error('❌ Comparison failed:', error.message);
  } finally {
    await browser.close();
  }
}

async function analyzeImplementation(page, label) {
  console.log(`\n🔍 Analyzing ${label} Implementation:`);
  
  const analysis = await page.evaluate(() => {
    const results = {
      // Structure analysis
      hasAdminButton: false,
      adminButtonStyle: null,
      
      // Section headers
      sectionHeaders: [],
      
      // Button analysis
      buttons: [],
      
      // Layout analysis
      hasBottomNav: false,
      bottomNavButtons: 0,
      
      // Background analysis
      hasOceanBackground: false,
      headerBackground: null,
      
      // Card analysis
      cardCount: 0,
      cardStyles: null
    };
    
    // Check admin button
    const adminBtn = document.querySelector('button');
    const adminButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
      btn.textContent?.includes('Administración') || btn.textContent?.includes('Admin')
    );
    
    if (adminButtons.length > 0) {
      results.hasAdminButton = true;
      const btn = adminButtons[0];
      const styles = window.getComputedStyle(btn);
      results.adminButtonStyle = {
        backgroundColor: styles.backgroundColor,
        borderRadius: styles.borderRadius,
        padding: styles.padding,
        display: styles.display
      };
    }
    
    // Get section headers
    const headers = document.querySelectorAll('h1, h2, h3, .section-header, [class*="header"]');
    results.sectionHeaders = Array.from(headers).map(h => ({
      text: h.textContent?.trim(),
      tagName: h.tagName,
      className: h.className
    })).filter(h => h.text && h.text.length > 0);
    
    // Get all buttons with their text
    const allButtons = document.querySelectorAll('button');
    results.buttons = Array.from(allButtons).map(btn => ({
      text: btn.textContent?.trim(),
      className: btn.className,
      hasIcon: btn.querySelector('svg') !== null
    })).filter(b => b.text && b.text.length > 0);
    
    // Check bottom navigation
    const bottomNav = document.querySelector('nav, .bottom-nav, .navigation, [class*="bottom"]');
    if (bottomNav) {
      results.hasBottomNav = true;
      results.bottomNavButtons = bottomNav.querySelectorAll('button').length;
    }
    
    // Check ocean background
    const oceanBg = document.querySelector('[style*="background-image"], .ocean, [class*="ocean"]');
    results.hasOceanBackground = oceanBg !== null;
    
    // Check header background
    const header = document.querySelector('header, .header, [class*="header"]');
    if (header) {
      const styles = window.getComputedStyle(header);
      results.headerBackground = {
        background: styles.background,
        backgroundImage: styles.backgroundImage,
        backgroundColor: styles.backgroundColor
      };
    }
    
    // Count cards/dashboard items
    const cards = document.querySelectorAll('.card, .dashboard-item, button[class*="rounded"], [class*="grid"] button');
    results.cardCount = cards.length;
    
    if (cards.length > 0) {
      const firstCard = cards[0];
      const styles = window.getComputedStyle(firstCard);
      results.cardStyles = {
        borderRadius: styles.borderRadius,
        backgroundColor: styles.backgroundColor,
        boxShadow: styles.boxShadow,
        padding: styles.padding
      };
    }
    
    return results;
  });
  
  // Display analysis results
  console.log(`   Admin Button: ${analysis.hasAdminButton ? '✅ Present' : '❌ Missing'}`);
  if (analysis.adminButtonStyle) {
    console.log(`     Style: ${JSON.stringify(analysis.adminButtonStyle, null, 2)}`);
  }
  
  console.log(`   Section Headers (${analysis.sectionHeaders.length}):`);
  analysis.sectionHeaders.forEach(h => {
    console.log(`     - "${h.text}" (${h.tagName})`);
  });
  
  console.log(`   Buttons (${analysis.buttons.length}):`);
  analysis.buttons.slice(0, 10).forEach(b => { // Show first 10
    console.log(`     - "${b.text}" ${b.hasIcon ? '🎨' : ''}`);
  });
  
  console.log(`   Bottom Navigation: ${analysis.hasBottomNav ? '✅' : '❌'} (${analysis.bottomNavButtons} buttons)`);
  console.log(`   Ocean Background: ${analysis.hasOceanBackground ? '✅' : '❌'}`);
  console.log(`   Cards: ${analysis.cardCount} found`);
  
  return analysis;
}

async function performDetailedComparison(currentPage) {
  const issues = await currentPage.evaluate(() => {
    const problems = [];
    
    // Check for specific layout issues
    const expectedSections = ['Reuniones', 'Actividades', 'Comunicación'];
    const foundSections = Array.from(document.querySelectorAll('h1, h2, h3')).map(h => h.textContent?.trim());
    
    expectedSections.forEach(section => {
      if (!foundSections.some(found => found?.includes(section))) {
        problems.push(`Missing section header: "${section}"`);
      }
    });
    
    // Check button ordering
    const buttons = Array.from(document.querySelectorAll('button')).map(btn => btn.textContent?.trim());
    const expectedButtons = ['Servicio al campo', 'Programas', 'Entre semana', 'Fin de semana', 'Asignaciones', 'Tareas', 'Cartas', 'Eventos'];
    
    expectedButtons.forEach(expected => {
      if (!buttons.some(btn => btn?.includes(expected))) {
        problems.push(`Missing button: "${expected}"`);
      }
    });
    
    // Check for Actividades grouping
    const actividadesHeader = Array.from(document.querySelectorAll('h1, h2, h3')).find(h => 
      h.textContent?.includes('Actividades')
    );
    
    if (!actividadesHeader) {
      problems.push('Missing "Actividades" section header');
    }
    
    return problems;
  });
  
  if (issues.length > 0) {
    console.log('\n❌ IDENTIFIED ISSUES:');
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  } else {
    console.log('\n✅ No major structural issues found');
  }
  
  // Check specific styling differences
  console.log('\n🎨 STYLING ANALYSIS:');
  const stylingCheck = await currentPage.evaluate(() => {
    const results = {};
    
    // Check header gradient
    const header = document.querySelector('header');
    if (header) {
      const styles = window.getComputedStyle(header);
      results.headerGradient = styles.backgroundImage.includes('gradient');
    }
    
    // Check card styling
    const cards = document.querySelectorAll('button[class*="rounded"]');
    if (cards.length > 0) {
      const firstCard = cards[0];
      const styles = window.getComputedStyle(firstCard);
      results.cardBorderRadius = styles.borderRadius;
      results.cardShadow = styles.boxShadow !== 'none';
    }
    
    return results;
  });
  
  console.log(`   Header Gradient: ${stylingCheck.headerGradient ? '✅' : '❌'}`);
  console.log(`   Card Border Radius: ${stylingCheck.cardBorderRadius || 'Not found'}`);
  console.log(`   Card Shadow: ${stylingCheck.cardShadow ? '✅' : '❌'}`);
}

// Run the comparison
compareImplementations().catch(console.error);
