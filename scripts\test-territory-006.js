/**
 * Test Territory 006 Import
 * 
 * Simple test to check if Territory 006 Excel file can be read
 */

const XLSX = require('xlsx');
const path = require('path');

async function testTerritory006() {
  try {
    console.log('🧪 Testing Territory 006...');
    
    const filePath = path.join(__dirname, '..', 'Territorios', 'Terr. 006.xlsx');
    console.log(`📁 File path: ${filePath}`);
    
    // Check if file exists
    const fs = require('fs');
    if (!fs.existsSync(filePath)) {
      console.error('❌ File does not exist');
      return;
    }
    
    console.log('✅ File exists');
    
    // Try to read the Excel file
    const workbook = XLSX.readFile(filePath);
    console.log(`📊 Available sheets: ${workbook.SheetNames.join(', ')}`);
    
    // Try to find the correct sheet
    const possibleSheetNames = [
      'Terr 6',
      'Terr. 6',
      'Territory 6',
      'T6',
      workbook.SheetNames[0]
    ];
    
    let worksheet = null;
    let sheetName = '';
    
    for (const name of possibleSheetNames) {
      if (workbook.Sheets[name]) {
        worksheet = workbook.Sheets[name];
        sheetName = name;
        break;
      }
    }
    
    if (!worksheet) {
      console.error('❌ No valid sheet found');
      return;
    }
    
    console.log(`✅ Using sheet: ${sheetName}`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Show first few rows
    console.log('\n📋 First 10 rows:');
    for (let i = 0; i < Math.min(10, excelData.length); i++) {
      console.log(`Row ${i + 1}:`, excelData[i]);
    }
    
    console.log('\n✅ Territory 006 test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing Territory 006:', error.message);
    console.error(error.stack);
  }
}

testTerritory006();
