'use client';

/**
 * Notification Preferences Page
 *
 * Member interface for managing notification and communication preferences.
 * Allows users to configure delivery methods, quiet hours, and category preferences.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  CommunicationPreferences,
  NotificationCategory,
  NOTIFICATION_CATEGORY_LABELS
} from '@/lib/services/communicationService';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

export default function NotificationPreferencesPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [preferences, setPreferences] = useState<CommunicationPreferences | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    checkAuthentication();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      loadPreferences();
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const data = await response.json();
      setUser(data.member);
    } catch (error) {
      console.error('Authentication check failed:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const loadPreferences = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/communication/preferences', {
        headers: { 'Authorization': `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        setPreferences(data.preferences);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Error al cargar preferencias');
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      setError('Error al cargar las preferencias');
    }
  };

  const updatePreferences = async () => {
    if (!preferences) return;

    setIsUpdating(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/communication/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          emailNotifications: preferences.emailNotifications,
          smsNotifications: preferences.smsNotifications,
          inAppNotifications: preferences.inAppNotifications,
          quietHoursStart: preferences.quietHoursStart,
          quietHoursEnd: preferences.quietHoursEnd,
          categoryPreferences: preferences.categoryPreferences,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setPreferences(data.preferences);
        setSuccessMessage('Preferencias actualizadas exitosamente');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Error al actualizar preferencias');
      }
    } catch (error) {
      console.error('Error updating preferences:', error);
      setError('Error al actualizar las preferencias');
    } finally {
      setIsUpdating(false);
    }
  };

  const updateDeliveryPreference = (type: 'email' | 'sms' | 'inApp', value: boolean) => {
    if (!preferences) return;

    setPreferences({
      ...preferences,
      [`${type}Notifications`]: value,
    });
  };

  const updateQuietHours = (type: 'start' | 'end', value: string) => {
    if (!preferences) return;

    setPreferences({
      ...preferences,
      [`quietHours${type.charAt(0).toUpperCase() + type.slice(1)}`]: value,
    });
  };

  const updateCategoryPreference = (category: NotificationCategory, value: boolean) => {
    if (!preferences) return;

    setPreferences({
      ...preferences,
      categoryPreferences: {
        ...preferences.categoryPreferences,
        [category]: value,
      },
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user || !preferences) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <button
              onClick={() => router.push('/notifications')}
              className="text-blue-200 hover:text-white mb-2 flex items-center"
            >
              ← Volver a Notificaciones
            </button>
            <h1 className="text-2xl font-bold">Preferencias de Notificaciones</h1>
            <p className="text-blue-200">Configura cómo deseas recibir las comunicaciones</p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        {/* Success/Error Messages */}
        {successMessage && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <p className="text-green-800">{successMessage}</p>
            <button
              onClick={() => setSuccessMessage(null)}
              className="text-green-600 hover:text-green-800 text-sm mt-2"
            >
              Cerrar
            </button>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <p className="text-red-800">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800 text-sm mt-2"
            >
              Cerrar
            </button>
          </div>
        )}

        {/* Delivery Methods */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Métodos de Entrega</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Notificaciones en la Aplicación</h3>
                <p className="text-sm text-gray-600">Recibe notificaciones dentro de la aplicación</p>
              </div>
              <input
                type="checkbox"
                checked={preferences.inAppNotifications}
                onChange={(e) => updateDeliveryPreference('inApp', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Notificaciones por Correo Electrónico</h3>
                <p className="text-sm text-gray-600">Recibe notificaciones en tu correo electrónico</p>
              </div>
              <input
                type="checkbox"
                checked={preferences.emailNotifications}
                onChange={(e) => updateDeliveryPreference('email', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Notificaciones por SMS</h3>
                <p className="text-sm text-gray-600">Recibe notificaciones por mensaje de texto</p>
              </div>
              <input
                type="checkbox"
                checked={preferences.smsNotifications}
                onChange={(e) => updateDeliveryPreference('sms', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>
          </div>
        </div>

        {/* Quiet Hours */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Horario de Silencio</h2>
          <p className="text-gray-600 mb-4">
            Durante estas horas no recibirás notificaciones (excepto las urgentes)
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Hora de Inicio
              </label>
              <input
                type="time"
                value={preferences.quietHoursStart || ''}
                onChange={(e) => updateQuietHours('start', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Hora de Fin
              </label>
              <input
                type="time"
                value={preferences.quietHoursEnd || ''}
                onChange={(e) => updateQuietHours('end', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Category Preferences */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Preferencias por Categoría</h2>
          <p className="text-gray-600 mb-4">
            Selecciona qué tipos de notificaciones deseas recibir
          </p>
          <div className="space-y-4">
            {Object.values(NotificationCategory).map(category => (
              <div key={category} className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">{NOTIFICATION_CATEGORY_LABELS[category]}</h3>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.categoryPreferences[category] ?? true}
                  onChange={(e) => updateCategoryPreference(category, e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            onClick={updatePreferences}
            disabled={isUpdating}
            className={`px-6 py-3 rounded-md font-medium transition-colors ${
              isUpdating
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white`}
          >
            {isUpdating ? 'Guardando...' : 'Guardar Preferencias'}
          </button>
        </div>
      </div>
    </div>
  );
}
