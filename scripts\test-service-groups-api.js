// Using built-in fetch (Node.js 18+)

async function testServiceGroupsAPI() {
  try {
    console.log('🧪 Testing Service Groups API...');

    // First, let's get a valid token by logging in
    console.log('1. Getting authentication token...');

    const loginResponse = await fetch('http://localhost:3001/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1234' // Using the developer user PIN
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginResponse.status, loginResponse.statusText);
      const errorText = await loginResponse.text();
      console.log('Error details:', errorText);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');

    // Now test the service groups API
    console.log('2. Testing service groups API...');

    const apiResponse = await fetch('http://localhost:3001/api/admin/service-groups', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('API Response Status:', apiResponse.status, apiResponse.statusText);

    if (apiResponse.ok) {
      const data = await apiResponse.json();
      console.log('✅ API call successful!');
      console.log('📊 Service Groups Data:');
      console.log(`Found ${data.groups?.length || 0} groups:`);

      if (data.groups && data.groups.length > 0) {
        data.groups.forEach(group => {
          console.log(`- Group ${group.groupNumber}: ${group.name}`);
          console.log(`  Overseer: ${group.overseer?.name || 'Sin asignar'}`);
          console.log(`  Assistant: ${group.assistant?.name || 'Sin asignar'}`);
          console.log(`  Address: ${group.address || 'Sin dirección'}`);
          console.log('');
        });
      }
    } else {
      console.log('❌ API call failed');
      const errorText = await apiResponse.text();
      console.log('Error details:', errorText);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testServiceGroupsAPI();
