/**
 * Check Actual Database Schema
 * 
 * Discovers what tables actually exist in the database
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkActualDatabaseSchema() {
  try {
    console.log('🔍 Checking Actual Database Schema...\n');

    // Get all tables in the database
    console.log('1. Discovering all tables in the database...');
    
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `;

    console.log(`✅ Found ${tables.length} tables in the database:`);
    
    const tableNames = [];
    for (const table of tables) {
      console.log(`  - ${table.table_name}`);
      tableNames.push(table.table_name);
    }

    // Check what we're currently trying to backup vs what exists
    console.log('\n2. Comparing with backup configuration...');
    
    const backupTables = [
      'congregations',
      'members', 
      'roles',
      'elder_permissions',
      'songs',
      'letters',
      'tasks',
      'task_assignments',
      'field_service_records',
      'congregation_settings'
    ];

    console.log('\nBackup configuration vs actual tables:');
    for (const backupTable of backupTables) {
      const exists = tableNames.includes(backupTable);
      console.log(`  ${exists ? '✅' : '❌'} ${backupTable} ${exists ? '(exists)' : '(MISSING)'}`);
    }

    console.log('\nTables in database NOT in backup configuration:');
    for (const tableName of tableNames) {
      if (!backupTables.includes(tableName)) {
        console.log(`  ⚠️ ${tableName} (not being backed up)`);
      }
    }

    // Get record counts for all actual tables
    console.log('\n3. Getting record counts for all actual tables...');
    
    let totalActualRecords = 0;
    for (const table of tables) {
      try {
        const result = await prisma.$queryRawUnsafe(`SELECT COUNT(*) as count FROM "${table.table_name}"`);
        const count = parseInt(result[0].count);
        totalActualRecords += count;
        console.log(`  ${table.table_name}: ${count} records`);
      } catch (error) {
        console.log(`  ${table.table_name}: Error - ${error.message}`);
      }
    }

    console.log(`\n📊 Total records in ALL tables: ${totalActualRecords}`);

    // Get table schemas
    console.log('\n4. Getting table schemas...');
    
    for (const table of tables.slice(0, 5)) { // Show first 5 tables
      try {
        console.log(`\n📋 Schema for ${table.table_name}:`);
        
        const columns = await prisma.$queryRaw`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = ${table.table_name}
          ORDER BY ordinal_position;
        `;

        for (const column of columns) {
          console.log(`    ${column.column_name}: ${column.data_type} ${column.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
        }
      } catch (error) {
        console.log(`    Error getting schema: ${error.message}`);
      }
    }

    console.log('\n🎯 RECOMMENDATIONS:');
    console.log('1. Update backup configuration to include ALL actual tables');
    console.log('2. Remove non-existent tables from backup configuration');
    console.log('3. Use dynamic table discovery instead of hardcoded list');
    console.log('4. Verify Prisma schema matches actual database');

  } catch (error) {
    console.error('❌ Schema check failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the schema check
checkActualDatabaseSchema().catch(console.error);
