# Coding Standards

## Critical Fullstack Rules

- **Database-First Testing**: Always use real database with sample data instead of mocks - seed comprehensive test data for existing midweek meetings, letters, and other entities
- **Preserve Existing Data**: Never delete or modify existing midweek meetings, letters, or other production data during development or testing
- **Multi-Tenant Data Isolation**: Every database query must include congregation_id filtering to ensure proper tenant isolation
- **Type Safety Enforcement**: All API calls must use tRPC procedures with Zod validation - never bypass type checking
- **Authentication Required**: All protected routes must use authentication middleware - never skip auth for convenience
- **JW.org Integration Preservation**: Always use the exact wol-scraper.js logic for JW.org data fetching - never create alternative implementations
- **Local Infrastructure Only**: Use local PostgreSQL database and local file storage - never assume cloud services
- **PIN Security**: Always use bcrypt for PIN hashing with 12 rounds - never store plain text PINs
- **Error Handling Consistency**: All API routes must use standardized error responses with proper HTTP status codes
- **Congregation Context**: Always pass congregation context through middleware - never hardcode congregation IDs

## Naming Conventions

| Element | Frontend | Backend | Example |
|---------|----------|---------|---------|
| Components | PascalCase | - | `DashboardCard.tsx` |
| Hooks | camelCase with 'use' | - | `useAuth.ts` |
| API Routes | - | kebab-case | `/api/midweek-meetings` |
| Database Tables | - | snake_case | `midweek_meetings` |
| tRPC Procedures | - | camelCase | `getMidweekMeetings` |
| Environment Variables | - | UPPER_SNAKE_CASE | `DATABASE_URL` |
| File Names | kebab-case | kebab-case | `meeting-service.ts` |
| Constants | UPPER_SNAKE_CASE | UPPER_SNAKE_CASE | `MAX_FILE_SIZE` |
