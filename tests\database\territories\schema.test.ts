// Territory Database Schema Tests
// Tests for territory database schema validation and constraints

// Using Jest testing framework
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL,
    },
  },
});

describe('Territory Database Schema', () => {
  beforeAll(async () => {
    // Ensure test database is clean
    await prisma.territoryAssignment.deleteMany();
    await prisma.territory.deleteMany();
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.territoryAssignment.deleteMany();
    await prisma.territory.deleteMany();
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up before each test
    await prisma.territoryAssignment.deleteMany();
    await prisma.territory.deleteMany();
  });

  describe('Territory Model', () => {
    it('should create territory with all required fields', async () => {
      const territory = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: 'T001',
          address: 'Test Address 123',
          status: 'available',
        },
      });

      expect(territory).toBeDefined();
      expect(territory.territoryNumber).toBe('T001');
      expect(territory.address).toBe('Test Address 123');
      expect(territory.status).toBe('available');
      expect(territory.congregationId).toBe('1441');
      expect(territory.createdAt).toBeInstanceOf(Date);
      expect(territory.updatedAt).toBeInstanceOf(Date);
    });

    it('should enforce unique territory number per congregation', async () => {
      // Create first territory
      await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: 'T001',
          address: 'Test Address 123',
          status: 'available',
        },
      });

      // Try to create duplicate territory number in same congregation
      await expect(
        prisma.territory.create({
          data: {
            congregationId: '1441',
            territoryNumber: 'T001',
            address: 'Different Address',
            status: 'available',
          },
        })
      ).rejects.toThrow();
    });

    it('should allow same territory number in different congregations', async () => {
      // Create territory in first congregation
      const territory1 = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: 'T001',
          address: 'Test Address 123',
          status: 'available',
        },
      });

      // Create same territory number in different congregation
      const territory2 = await prisma.territory.create({
        data: {
          congregationId: '1442',
          territoryNumber: 'T001',
          address: 'Different Address',
          status: 'available',
        },
      });

      expect(territory1.territoryNumber).toBe(territory2.territoryNumber);
      expect(territory1.congregationId).not.toBe(territory2.congregationId);
    });

    it('should validate territory status enum values', async () => {
      const validStatuses = ['available', 'assigned', 'completed', 'out_of_service'];

      for (const status of validStatuses) {
        const territory = await prisma.territory.create({
          data: {
            congregationId: '1441',
            territoryNumber: `T${status}`,
            address: 'Test Address',
            status: status as any,
          },
        });
        expect(territory.status).toBe(status);
      }
    });

    it('should store optional boundaries as JSON', async () => {
      const boundaries = {
        type: 'Polygon',
        coordinates: [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
      };

      const territory = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: 'T001',
          address: 'Test Address',
          status: 'available',
          boundaries: boundaries,
        },
      });

      expect(territory.boundaries).toEqual(boundaries);
    });

    it('should store optional notes', async () => {
      const territory = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: 'T001',
          address: 'Test Address',
          status: 'available',
          notes: 'This is a test territory with special instructions',
        },
      });

      expect(territory.notes).toBe('This is a test territory with special instructions');
    });
  });

  describe('Territory Indexes', () => {
    it('should have proper indexes for performance', async () => {
      // This test verifies that the indexes exist by checking query performance
      // In a real test, you might use EXPLAIN ANALYZE to verify index usage

      // Create multiple territories for testing
      const territories = [];
      for (let i = 1; i <= 10; i++) {
        territories.push({
          congregationId: '1441',
          territoryNumber: `T${i.toString().padStart(3, '0')}`,
          address: `Test Address ${i}`,
          status: i % 2 === 0 ? 'available' : 'assigned',
        });
      }

      await prisma.territory.createMany({
        data: territories,
      });

      // Test congregation_id index
      const congregationTerritories = await prisma.territory.findMany({
        where: { congregationId: '1441' },
      });
      expect(congregationTerritories).toHaveLength(10);

      // Test status index
      const availableTerritories = await prisma.territory.findMany({
        where: { status: 'available' },
      });
      expect(availableTerritories.length).toBeGreaterThan(0);

      // Test compound index (congregation + status)
      const congregationAvailable = await prisma.territory.findMany({
        where: {
          congregationId: '1441',
          status: 'available',
        },
      });
      expect(congregationAvailable.length).toBeGreaterThan(0);
    });
  });

  describe('TerritoryAssignment Model', () => {
    let testTerritoryId: string;
    let testMemberId: string;
    let testAssignedById: string;

    beforeEach(async () => {
      // Create test territory
      const territory = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: 'T001',
          address: 'Test Address',
          status: 'available',
        },
      });
      testTerritoryId = territory.id;

      // Note: In a real test, you would create test members
      // For now, we'll use placeholder IDs that should exist in test data
      testMemberId = 'test-member-id';
      testAssignedById = 'test-assigned-by-id';
    });

    it('should create territory assignment with required fields', async () => {
      const assignment = await prisma.territoryAssignment.create({
        data: {
          territoryId: testTerritoryId,
          memberId: testMemberId,
          assignedBy: testAssignedById,
          congregationId: '1441',
          status: 'active',
        },
      });

      expect(assignment).toBeDefined();
      expect(assignment.territoryId).toBe(testTerritoryId);
      expect(assignment.memberId).toBe(testMemberId);
      expect(assignment.assignedBy).toBe(testAssignedById);
      expect(assignment.status).toBe('active');
      expect(assignment.congregationId).toBe('1441');
      expect(assignment.assignedAt).toBeInstanceOf(Date);
    });

    it('should validate assignment status enum values', async () => {
      const validStatuses = ['active', 'completed', 'overdue', 'cancelled'];

      for (const status of validStatuses) {
        const assignment = await prisma.territoryAssignment.create({
          data: {
            territoryId: testTerritoryId,
            memberId: `${testMemberId}-${status}`,
            assignedBy: testAssignedById,
            congregationId: '1441',
            status: status as any,
          },
        });
        expect(assignment.status).toBe(status);
      }
    });

    it('should store optional completion date and due date', async () => {
      const dueDate = new Date('2024-12-31');
      const completedAt = new Date('2024-12-15');

      const assignment = await prisma.territoryAssignment.create({
        data: {
          territoryId: testTerritoryId,
          memberId: testMemberId,
          assignedBy: testAssignedById,
          congregationId: '1441',
          status: 'completed',
          dueDate: dueDate,
          completedAt: completedAt,
          notes: 'Territory completed successfully',
        },
      });

      expect(assignment.dueDate).toEqual(dueDate);
      expect(assignment.completedAt).toEqual(completedAt);
      expect(assignment.notes).toBe('Territory completed successfully');
    });

    it('should enforce foreign key constraints', async () => {
      // Test invalid territory ID
      await expect(
        prisma.territoryAssignment.create({
          data: {
            territoryId: 'invalid-territory-id',
            memberId: testMemberId,
            assignedBy: testAssignedById,
            congregationId: '1441',
            status: 'active',
          },
        })
      ).rejects.toThrow();
    });

    it('should cascade delete when territory is deleted', async () => {
      // Create assignment
      const assignment = await prisma.territoryAssignment.create({
        data: {
          territoryId: testTerritoryId,
          memberId: testMemberId,
          assignedBy: testAssignedById,
          congregationId: '1441',
          status: 'active',
        },
      });

      // Delete territory
      await prisma.territory.delete({
        where: { id: testTerritoryId },
      });

      // Assignment should be deleted too
      const deletedAssignment = await prisma.territoryAssignment.findUnique({
        where: { id: assignment.id },
      });
      expect(deletedAssignment).toBeNull();
    });
  });

  describe('Multi-Tenant Isolation', () => {
    it('should enforce congregation isolation for territories', async () => {
      // Create territories in different congregations
      const territory1 = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: 'T001',
          address: 'Congregation 1 Territory',
          status: 'available',
        },
      });

      const territory2 = await prisma.territory.create({
        data: {
          congregationId: '1442',
          territoryNumber: 'T001',
          address: 'Congregation 2 Territory',
          status: 'available',
        },
      });

      // Query should only return territories for specific congregation
      const cong1Territories = await prisma.territory.findMany({
        where: { congregationId: '1441' },
      });

      const cong2Territories = await prisma.territory.findMany({
        where: { congregationId: '1442' },
      });

      expect(cong1Territories).toHaveLength(1);
      expect(cong2Territories).toHaveLength(1);
      expect(cong1Territories[0].id).toBe(territory1.id);
      expect(cong2Territories[0].id).toBe(territory2.id);
    });
  });
});
