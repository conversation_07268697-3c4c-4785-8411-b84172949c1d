#!/usr/bin/env node

/**
 * PIN Management Test Script for Hermanos App
 * 
 * Tests the PIN management system including settings configuration,
 * PIN generation, validation, reset functionality, and audit trail.
 * 
 * Usage: node scripts/test-pin-management.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

class PinManagementTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = [];
  }

  addTestResult(testName, success, message) {
    this.testResults.push({
      test: testName,
      success,
      message,
    });
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  async testDatabaseSchema() {
    console.log('\n🗄️ Testing Database Schema...');
    
    try {
      // Test pin_settings table
      const settingsCount = await this.prisma.pinSettings.count();
      this.addTestResult('PIN Settings Table', true, `Table exists with ${settingsCount} records`);

      // Test pin_change_history table
      const historyCount = await this.prisma.pinChangeHistory.count();
      this.addTestResult('PIN Change History Table', true, `Table exists with ${historyCount} records`);

      return true;
    } catch (error) {
      this.addTestResult('Database Schema', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testPinSettings() {
    console.log('\n⚙️ Testing PIN Settings...');
    
    try {
      // Get test congregation
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      if (!congregation) {
        this.addTestResult('Congregation Setup', false, 'No active congregation found');
        return false;
      }

      // Test default settings creation
      const settings = await this.prisma.pinSettings.upsert({
        where: { congregationId: congregation.id },
        update: {},
        create: {
          congregationId: congregation.id,
          minLength: 4,
          maxLength: 8,
          requireNumeric: true,
          requireAlphanumeric: false,
          requireSpecialChars: false,
          allowSequential: true,
          allowRepeated: true,
          expirationDays: null,
          bcryptRounds: 12,
        },
      });

      this.addTestResult('Default Settings Creation', !!settings.id, 
        `Created/found settings with ID: ${settings.id}`);

      // Test settings update
      const updatedSettings = await this.prisma.pinSettings.update({
        where: { id: settings.id },
        data: {
          minLength: 6,
          maxLength: 10,
          requireAlphanumeric: true,
        },
      });

      this.addTestResult('Settings Update', 
        updatedSettings.minLength === 6 && updatedSettings.requireAlphanumeric === true,
        `Updated settings: minLength=${updatedSettings.minLength}, requireAlphanumeric=${updatedSettings.requireAlphanumeric}`);

      return true;
    } catch (error) {
      this.addTestResult('PIN Settings', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testPinGeneration() {
    console.log('\n🔑 Testing PIN Generation...');
    
    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Test PIN generation with different settings
      const testCases = [
        { minLength: 4, maxLength: 6, requireNumeric: true, requireAlphanumeric: false },
        { minLength: 6, maxLength: 8, requireNumeric: true, requireAlphanumeric: true },
        { minLength: 8, maxLength: 10, requireNumeric: true, requireAlphanumeric: true, requireSpecialChars: true },
      ];

      for (const testCase of testCases) {
        // Update settings
        await this.prisma.pinSettings.upsert({
          where: { congregationId: congregation.id },
          update: testCase,
          create: {
            congregationId: congregation.id,
            ...testCase,
            allowSequential: true,
            allowRepeated: true,
            expirationDays: null,
            bcryptRounds: 12,
          },
        });

        // Generate multiple PINs to test uniqueness
        const pins = [];
        for (let i = 0; i < 5; i++) {
          const pin = this.generateTestPin(testCase);
          pins.push(pin);
        }

        // Validate PIN format
        const validPins = pins.filter(pin => this.validatePinFormat(pin, testCase));
        
        this.addTestResult(`PIN Generation (${testCase.minLength}-${testCase.maxLength})`, 
          validPins.length === pins.length,
          `Generated ${validPins.length}/${pins.length} valid PINs`);
      }

      return true;
    } catch (error) {
      this.addTestResult('PIN Generation', false, `Error: ${error.message}`);
      return false;
    }
  }

  generateTestPin(settings) {
    const length = Math.floor(Math.random() * (settings.maxLength - settings.minLength + 1)) + settings.minLength;
    
    let charset = '';
    if (settings.requireNumeric && !settings.requireAlphanumeric) {
      charset = '0123456789';
    } else if (settings.requireAlphanumeric) {
      charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    } else {
      charset = '0123456789';
    }

    if (settings.requireSpecialChars) {
      charset += '!@#$%^&*';
    }

    let pin = '';
    for (let i = 0; i < length; i++) {
      pin += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    return pin;
  }

  validatePinFormat(pin, settings) {
    // Length validation
    if (pin.length < settings.minLength || pin.length > settings.maxLength) {
      return false;
    }

    // Character type validation
    if (settings.requireNumeric && !/\d/.test(pin)) {
      return false;
    }

    if (settings.requireAlphanumeric && !/[a-zA-Z]/.test(pin)) {
      return false;
    }

    if (settings.requireSpecialChars && !/[!@#$%^&*]/.test(pin)) {
      return false;
    }

    return true;
  }

  async testPinReset() {
    console.log('\n🔄 Testing PIN Reset...');
    
    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Get test member
      const member = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
      });

      if (!member) {
        this.addTestResult('Member Setup', false, 'No active member found for testing');
        return false;
      }

      // Get admin user for reset operation
      const adminUser = await this.prisma.member.findFirst({
        where: {
          congregationId: congregation.id,
          role: { in: ['elder', 'overseer_coordinator', 'developer'] },
          isActive: true,
        },
      });

      if (!adminUser) {
        this.addTestResult('Admin User Setup', false, 'No admin user found');
        return false;
      }

      // Store original PIN hash
      const originalPinHash = member.pin;

      // Generate new PIN and hash it
      const newPin = this.generateTestPin({ minLength: 4, maxLength: 8, requireNumeric: true });
      const newPinHash = await bcrypt.hash(newPin, 12);

      // Update member PIN and create history record in transaction
      await this.prisma.$transaction(async (tx) => {
        // Update member PIN
        await tx.member.update({
          where: { id: member.id },
          data: { pin: newPinHash },
        });

        // Create history record
        await tx.pinChangeHistory.create({
          data: {
            congregationId: congregation.id,
            memberId: member.id,
            changedBy: adminUser.id,
            changeType: 'reset',
            oldPinHash: originalPinHash,
            newPinHash: newPinHash,
            reason: 'Test PIN reset',
            ipAddress: '127.0.0.1',
            userAgent: 'Test Script',
          },
        });
      });

      this.addTestResult('PIN Reset', true, `Reset PIN for member: ${member.name}`);

      // Verify PIN change history was created
      const historyRecord = await this.prisma.pinChangeHistory.findFirst({
        where: {
          congregationId: congregation.id,
          memberId: member.id,
          changeType: 'reset',
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      this.addTestResult('PIN Reset History', !!historyRecord, 
        historyRecord ? 'History record created successfully' : 'History record not found');

      return true;
    } catch (error) {
      this.addTestResult('PIN Reset', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testPinHistory() {
    console.log('\n📋 Testing PIN History...');
    
    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Test getting PIN history
      const history = await this.prisma.pinChangeHistory.findMany({
        where: {
          congregationId: congregation.id,
        },
        include: {
          member: {
            select: {
              name: true,
            },
          },
          changedByMember: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10,
      });

      this.addTestResult('PIN History Query', true, 
        `Retrieved ${history.length} history records`);

      // Test history filtering by change type
      const resetRecords = await this.prisma.pinChangeHistory.findMany({
        where: {
          congregationId: congregation.id,
          changeType: 'reset',
        },
      });

      this.addTestResult('History Filtering by Type', true, 
        `Found ${resetRecords.length} reset records`);

      return true;
    } catch (error) {
      this.addTestResult('PIN History', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testDataIntegrity() {
    console.log('\n🔒 Testing Data Integrity...');
    
    try {
      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Test congregation isolation
      const settingsCount = await this.prisma.pinSettings.count({
        where: {
          congregationId: congregation.id,
        },
      });

      const totalSettings = await this.prisma.pinSettings.count();
      
      const isolationWorking = settingsCount <= totalSettings;
      this.addTestResult('Congregation Isolation', isolationWorking, 
        isolationWorking ? 'Congregation isolation working' : 'Potential isolation issue');

      // Test foreign key constraints
      const historyWithValidReferences = await this.prisma.pinChangeHistory.findMany({
        where: {
          congregationId: congregation.id,
        },
        include: {
          member: true,
          changedByMember: true,
        },
        take: 5,
      });

      const allReferencesValid = historyWithValidReferences.every(record => 
        record.changedByMember && (record.member || record.memberId === null)
      );

      this.addTestResult('Foreign Key Integrity', allReferencesValid, 
        allReferencesValid ? 'All foreign key references valid' : 'Some invalid references found');

      return true;
    } catch (error) {
      this.addTestResult('Data Integrity', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testPerformance() {
    console.log('\n⚡ Testing PIN Management Performance...');
    
    try {
      const startTime = Date.now();

      const congregation = await this.prisma.congregation.findFirst({
        where: { isActive: true },
      });

      // Run complex query with joins and filtering
      await this.prisma.pinChangeHistory.findMany({
        where: {
          congregationId: congregation.id,
        },
        include: {
          member: {
            select: {
              name: true,
            },
          },
          changedByMember: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 20,
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      const isPerformant = duration < 500; // Should complete in under 500ms
      this.addTestResult('PIN History Query Performance', isPerformant, 
        `Complex query completed in ${duration}ms ${isPerformant ? '(good)' : '(slow)'}`);

      return isPerformant;
    } catch (error) {
      this.addTestResult('PIN Management Performance', false, `Error: ${error.message}`);
      return false;
    }
  }

  generateReport() {
    console.log('\n📋 PIN Management Test Report');
    console.log('===============================');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.test}: ${r.message}`));
    }
    
    const allPassed = failedTests === 0;
    if (allPassed) {
      console.log('\n🎉 All PIN management tests passed!');
      console.log('\n📝 PIN management system is ready:');
      console.log('1. Start the development server: npm run dev');
      console.log('2. Login as an elder, coordinator, or developer');
      console.log('3. Go to Admin → Gestión de PINs');
      console.log('4. Configure PIN settings and manage member PINs');
    } else {
      console.log('\n⚠️ Some PIN management tests failed!');
    }
    
    return allPassed;
  }

  async run() {
    try {
      console.log('🚀 Starting PIN management tests...');
      
      await this.prisma.$connect();
      console.log('✅ Database connection established');
      
      // Run all tests
      await this.testDatabaseSchema();
      await this.testPinSettings();
      await this.testPinGeneration();
      await this.testPinReset();
      await this.testPinHistory();
      await this.testDataIntegrity();
      await this.testPerformance();
      
      // Generate report
      const success = this.generateReport();
      
      if (!success) {
        process.exit(1);
      }
      
    } catch (error) {
      console.error('\n💥 Test execution failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new PinManagementTester();
  tester.run().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = PinManagementTester;
