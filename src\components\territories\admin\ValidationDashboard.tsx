'use client';

/**
 * Validation Dashboard Component
 * 
 * Admin interface for territory data validation and cleanup.
 * Provides validation overview, error management, and conflict resolution.
 */

import React, { useState, useEffect } from 'react';
import { ValidationResult, ValidationError, DuplicateMatch } from '@/types/territories/validation';

interface ValidationDashboardProps {
  className?: string;
}

interface ValidationSummary {
  totalTerritories: number;
  validTerritories: number;
  territoriesWithErrors: number;
  territoriesWithWarnings: number;
  totalErrors: number;
  totalWarnings: number;
  totalDuplicates: number;
  totalCleanedFields: number;
}

export default function ValidationDashboard({ className = '' }: ValidationDashboardProps) {
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [summary, setSummary] = useState<ValidationSummary | null>(null);
  const [selectedErrors, setSelectedErrors] = useState<ValidationError[]>([]);
  const [selectedDuplicates, setSelectedDuplicates] = useState<DuplicateMatch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'errors' | 'duplicates' | 'cleanup'>('overview');

  // Mock data for demonstration
  useEffect(() => {
    loadValidationData();
  }, []);

  const loadValidationData = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from API
      // For now, we'll use mock data
      const mockSummary: ValidationSummary = {
        totalTerritories: 150,
        validTerritories: 142,
        territoriesWithErrors: 5,
        territoriesWithWarnings: 8,
        totalErrors: 7,
        totalWarnings: 12,
        totalDuplicates: 3,
        totalCleanedFields: 25
      };

      const mockErrors: ValidationError[] = [
        {
          id: 'error-1',
          territoryNumber: 'T-001',
          address: '123 Main St',
          field: 'territoryNumber',
          errorType: 'format',
          severity: 'error',
          message: 'Territory number contains invalid characters',
          details: 'Only alphanumeric characters, hyphens, and underscores are allowed',
          ruleId: 'territory-number-format',
          canOverride: true,
          isResolved: false
        },
        {
          id: 'error-2',
          territoryNumber: 'A-15',
          address: '456 Oak Ave',
          field: 'territoryNumber',
          errorType: 'uniqueness',
          severity: 'error',
          message: 'Territory number already exists',
          details: 'This territory number is already assigned to another territory',
          ruleId: 'territory-number-uniqueness',
          canOverride: false,
          isResolved: false
        }
      ];

      const mockDuplicates: DuplicateMatch[] = [
        {
          id: 'dup-1',
          territory1: { territoryNumber: 'A-10', address: '789 Pine St' },
          territory2: { territoryNumber: 'A-10B', address: '789 Pine Street' },
          similarityScore: 0.92,
          matchedFields: ['address'],
          confidence: 'high',
          suggestedAction: 'merge',
          isResolved: false
        }
      ];

      setSummary(mockSummary);
      setSelectedErrors(mockErrors);
      setSelectedDuplicates(mockDuplicates);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load validation data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRunValidation = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would trigger validation
      console.log('Running territory validation...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      await loadValidationData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to run validation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResolveError = async (errorId: string, resolution: string) => {
    try {
      console.log(`Resolving error ${errorId} with resolution: ${resolution}`);
      // Update local state
      setSelectedErrors(prev => 
        prev.map(error => 
          error.id === errorId 
            ? { ...error, isResolved: true, resolutionNotes: resolution }
            : error
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resolve error');
    }
  };

  const handleResolveDuplicate = async (duplicateId: string, action: 'merge' | 'keep_both' | 'delete') => {
    try {
      console.log(`Resolving duplicate ${duplicateId} with action: ${action}`);
      // Update local state
      setSelectedDuplicates(prev => 
        prev.map(duplicate => 
          duplicate.id === duplicateId 
            ? { ...duplicate, isResolved: true, resolution: action === 'delete' ? 'deleted_duplicate' : action === 'merge' ? 'merged' : 'kept_both' }
            : duplicate
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resolve duplicate');
    }
  };

  const getSeverityColor = (severity: 'warning' | 'error' | 'critical') => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'error': return 'text-red-500 bg-red-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getConfidenceColor = (confidence: 'low' | 'medium' | 'high') => {
    switch (confidence) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (isLoading && !summary) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Cargando datos de validación...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Validación de Territorios</h2>
          <p className="text-sm text-gray-600 mt-1">
            Gestiona la calidad y consistencia de los datos de territorios
          </p>
        </div>
        <button
          onClick={handleRunValidation}
          disabled={isLoading}
          className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Validando...' : 'Ejecutar Validación'}
        </button>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Territorios Válidos</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.validTerritories}</p>
                <p className="text-xs text-gray-500">de {summary.totalTerritories} total</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Errores</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.totalErrors}</p>
                <p className="text-xs text-gray-500">{summary.territoriesWithErrors} territorios</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Advertencias</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.totalWarnings}</p>
                <p className="text-xs text-gray-500">{summary.territoriesWithWarnings} territorios</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Duplicados</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.totalDuplicates}</p>
                <p className="text-xs text-gray-500">posibles coincidencias</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Resumen', icon: '📊' },
            { id: 'errors', name: 'Errores', icon: '❌', count: summary?.totalErrors },
            { id: 'duplicates', name: 'Duplicados', icon: '👥', count: summary?.totalDuplicates },
            { id: 'cleanup', name: 'Limpieza', icon: '🧹', count: summary?.totalCleanedFields }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
              {tab.count !== undefined && tab.count > 0 && (
                <span className="ml-2 bg-red-100 text-red-600 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Estado de Validación</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Territorios procesados</span>
                  <span className="text-sm font-medium">{summary?.totalTerritories || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Territorios válidos</span>
                  <span className="text-sm font-medium text-green-600">{summary?.validTerritories || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Campos limpiados</span>
                  <span className="text-sm font-medium text-blue-600">{summary?.totalCleanedFields || 0}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'errors' && (
          <div className="space-y-4">
            {selectedErrors.length === 0 ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No hay errores</h3>
                <p className="mt-1 text-sm text-gray-500">Todos los territorios han pasado la validación.</p>
              </div>
            ) : (
              selectedErrors.map((error) => (
                <div key={error.id} className="bg-white rounded-lg border border-gray-200 p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(error.severity)}`}>
                          {error.severity.toUpperCase()}
                        </span>
                        <span className="text-sm font-medium text-gray-900">{error.territoryNumber}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{error.address}</p>
                      <p className="text-sm text-gray-900 mt-2">{error.message}</p>
                      {error.details && (
                        <p className="text-xs text-gray-500 mt-1">{error.details}</p>
                      )}
                    </div>
                    {error.canOverride && !error.isResolved && (
                      <button
                        onClick={() => handleResolveError(error.id, 'Manual override approved')}
                        className="ml-4 px-3 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200"
                      >
                        Resolver
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {activeTab === 'duplicates' && (
          <div className="space-y-4">
            {selectedDuplicates.length === 0 ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No hay duplicados</h3>
                <p className="mt-1 text-sm text-gray-500">No se encontraron territorios duplicados.</p>
              </div>
            ) : (
              selectedDuplicates.map((duplicate) => (
                <div key={duplicate.id} className="bg-white rounded-lg border border-gray-200 p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(duplicate.confidence)}`}>
                          {duplicate.confidence.toUpperCase()}
                        </span>
                        <span className="text-sm text-gray-600">
                          Similitud: {Math.round(duplicate.similarityScore * 100)}%
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{duplicate.territory1.territoryNumber}</p>
                          <p className="text-sm text-gray-600">{duplicate.territory1.address}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{duplicate.territory2.territoryNumber}</p>
                          <p className="text-sm text-gray-600">{duplicate.territory2.address}</p>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Campos coincidentes: {duplicate.matchedFields.join(', ')}
                      </p>
                    </div>
                    {!duplicate.isResolved && (
                      <div className="ml-4 flex space-x-2">
                        <button
                          onClick={() => handleResolveDuplicate(duplicate.id, 'merge')}
                          className="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                        >
                          Combinar
                        </button>
                        <button
                          onClick={() => handleResolveDuplicate(duplicate.id, 'keep_both')}
                          className="px-3 py-1 text-xs bg-green-100 text-green-800 rounded hover:bg-green-200"
                        >
                          Mantener Ambos
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {activeTab === 'cleanup' && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Limpieza de Datos</h3>
            <p className="text-sm text-gray-600 mb-4">
              Se han limpiado {summary?.totalCleanedFields || 0} campos automáticamente durante la validación.
            </p>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Espacios en blanco eliminados</span>
                <span className="font-medium">15</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Capitalización estandarizada</span>
                <span className="font-medium">8</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Abreviaciones aplicadas</span>
                <span className="font-medium">2</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
