import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { auth } from '@/lib/auth';
import DocumentManager from '@/components/admin/DocumentManager';
import AdminFooter from '@/components/admin/AdminFooter';

export const metadata: Metadata = {
  title: 'Document Management - Admin',
  description: 'Manage congregation documents and letters',
};

export default async function DocumentManagementPage() {
  // Get user from auth
  const user = await auth.getUser();

  if (!user) {
    redirect('/login');
  }

  // Check if user has admin access
  if (!['elder', 'ministerial_servant'].includes(user.role)) {
    redirect('/dashboard');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">Document Management</h1>
                <p className="text-blue-100 mt-1">
                  Manage congregation documents, letters, and files
                </p>
              </div>
              <div className="text-right">
                <p className="text-blue-100 text-sm">Logged in as</p>
                <p className="font-medium">{user.name}</p>
                <p className="text-blue-200 text-sm capitalize">{user.role.replace('_', ' ')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <DocumentManager
          congregationId={user.congregationId}
          userRole={user.role}
        />
      </div>

      {/* Navigation */}
      <div className="fixed bottom-4 left-4">
        <a
          href="/admin"
          className="bg-gray-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-gray-700 transition-colors"
        >
          ← Back to Admin
        </a>
      </div>

      {/* Admin Footer */}
      <AdminFooter currentSection="documents" />
    </div>
  );
}
