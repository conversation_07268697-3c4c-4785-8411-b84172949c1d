'use client';

/**
 * My Territories Component
 *
 * Member interface for viewing assigned territories.
 * Follows Field Service UI patterns with mobile-optimized design.
 */

import React, { useState, useEffect } from 'react';
import { Territory } from '@/types/territories/territory';
import TerritoryDetail from './TerritoryDetail';
import SimpleTerritoryMap from '../shared/SimpleTerritoryMap';

interface MyTerritoriesProps {
  className?: string;
}

interface TerritoryWithAssignment extends Territory {
  assignedMember?: {
    id: string;
    name: string;
    assignedAt: Date;
    assignedBy: string;
  } | null;
}

export default function MyTerritories({
  className = ''
}: MyTerritoriesProps) {
  const [territories, setTerritories] = useState<TerritoryWithAssignment[]>([]);
  const [selectedTerritoryId, setSelectedTerritoryId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userInfo, setUserInfo] = useState<{ name: string; role: string } | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'map'>('list');

  // Fetch member territories
  useEffect(() => {
    const fetchMyTerritories = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get user info from token
        const token = localStorage.getItem('hermanos_token');
        if (token) {
          try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            setUserInfo({
              name: payload.name || 'Usuario',
              role: payload.role || 'publisher'
            });
          } catch (e) {
            console.warn('Could not parse token for user info');
          }
        }

        // Fetch territories assigned to current member
        const response = await fetch('/api/territories?memberView=true&includeAssignments=true', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('hermanos_token')}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch territories: ${response.statusText}`);
        }

        const data = await response.json();

        // Enhance territories that don't have boundary data
        const enhancedTerritories = data.territories || [];

        setTerritories(enhancedTerritories);

      } catch (err) {
        console.error('Error fetching my territories:', err);
        setError(err instanceof Error ? err.message : 'Failed to load territories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMyTerritories();
  }, []);

  const handleTerritoryClick = (territoryId: string) => {
    setSelectedTerritoryId(territoryId);
  };

  const handleBackToList = () => {
    setSelectedTerritoryId(null);
  };

  // Note: Map view is for visualization only - territory selection happens in list view

  // Show territory detail if one is selected
  if (selectedTerritoryId) {
    return (
      <TerritoryDetail
        territoryId={selectedTerritoryId}
        onBack={handleBackToList}
        className={className}
      />
    );
  }

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <div className="text-sm text-gray-600">Cargando territorios...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`min-h-screen bg-gray-50 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-600 mb-2">❌</div>
            <div className="text-sm text-gray-600">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900">Mis Territorios</h1>
              {userInfo && (
                <p className="text-sm text-gray-500">{userInfo.name}</p>
              )}
            </div>
            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'list'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Lista
                </button>
                <button
                  onClick={() => setViewMode('map')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'map'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Mapa
                </button>
              </div>

              {/* Territory Count */}
              <div className="text-right">
                <div className="text-lg font-semibold text-blue-600">{territories.length}</div>
                <div className="text-xs text-gray-500">asignados</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-4">
        {territories.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tienes territorios asignados</h3>
            <p className="text-gray-600">Contacta a un anciano para que te asigne territorios.</p>
          </div>
        ) : viewMode === 'map' ? (
          /* Map View */
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <SimpleTerritoryMap
              territories={territories.map(t => ({
                id: t.id,
                territoryNumber: t.territoryNumber,
                address: t.address,
                status: t.status,
                coordinates: undefined, // Will be enhanced by the component
                boundary: t.boundaries ? {
                  type: 'Polygon',
                  coordinates: t.boundaries.coordinates
                } : undefined
              }))}
              height="500px"
              className="w-full"
            />
          </div>
        ) : (
          /* List View */
          <div className="space-y-3">
            {territories.map((territory) => (
              <div
                key={territory.id}
                onClick={() => handleTerritoryClick(territory.id)}
                className="bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {/* Territory Number Circle */}
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600">
                        {territory.territoryNumber}
                      </span>
                    </div>

                    {/* Territory Info */}
                    <div className="flex-1">
                      <h3 className="text-base font-medium text-gray-900">
                        Territorio {territory.territoryNumber}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {territory.address.split('\n').length} direcciones
                      </p>
                      {territory.assignedMember && (
                        <p className="text-xs text-blue-600 mt-1">
                          Asignado el {new Date(territory.assignedMember.assignedAt).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Status and Arrow */}
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      territory.status === 'assigned' ? 'bg-blue-500' :
                      territory.status === 'completed' ? 'bg-green-500' :
                      territory.status === 'available' ? 'bg-gray-400' :
                      'bg-red-500'
                    }`}></div>
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
