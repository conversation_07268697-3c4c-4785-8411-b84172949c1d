/**
 * Simple test to verify the documents API endpoint is working
 */

const BASE_URL = 'http://localhost:3000';

async function testDocumentsAPI() {
  console.log('🧪 Testing Documents API...');
  
  try {
    // Test without authentication first to see if the endpoint exists
    const response = await fetch(`${BASE_URL}/api/documents?category=letters`);
    
    console.log(`📡 Response status: ${response.status}`);
    console.log(`📡 Response headers:`, Object.fromEntries(response.headers.entries()));
    
    if (response.status === 401) {
      console.log('✅ API endpoint exists and requires authentication (expected)');
      return true;
    } else if (response.status === 404) {
      console.log('❌ API endpoint not found');
      return false;
    } else {
      const data = await response.text();
      console.log(`📄 Response body: ${data.substring(0, 200)}...`);
      return true;
    }
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    return false;
  }
}

async function testLettersPageExists() {
  console.log('🧪 Testing Letters Page...');
  
  try {
    const response = await fetch(`${BASE_URL}/admin/letters`);
    console.log(`📡 Admin Letters page status: ${response.status}`);
    
    const memberResponse = await fetch(`${BASE_URL}/letters`);
    console.log(`📡 Member Letters page status: ${memberResponse.status}`);
    
    return response.status !== 404 && memberResponse.status !== 404;
  } catch (error) {
    console.error('❌ Page test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Simple API Tests...\n');
  
  const apiWorks = await testDocumentsAPI();
  console.log('');
  
  const pagesExist = await testLettersPageExists();
  console.log('');
  
  console.log('📋 Test Results:');
  console.log(`   API Endpoint: ${apiWorks ? '✅ Working' : '❌ Failed'}`);
  console.log(`   Pages: ${pagesExist ? '✅ Accessible' : '❌ Failed'}`);
  
  if (apiWorks && pagesExist) {
    console.log('\n🎉 Basic infrastructure is working!');
    console.log('💡 Next step: Check browser console for JavaScript errors');
  } else {
    console.log('\n⚠️  Some issues detected. Check the logs above.');
  }
}

runTests().catch(console.error);
