/**
 * Add Sample Notes Script
 * 
 * Adds sample notes with status information to territory 001 addresses
 * to test the status icon functionality.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addSampleNotes() {
  try {
    console.log('🏷️  Adding sample notes to Territory 001...');

    // Find Territory 001
    const territory = await prisma.territory.findFirst({
      where: { 
        congregationId: '1441',
        territoryNumber: '001'
      }
    });

    if (!territory) {
      console.error('❌ Territory 001 not found');
      process.exit(1);
    }

    console.log(`✅ Found Territory 001: ${territory.id}`);
    
    // Get current addresses
    const addresses = territory.address.split('\n').filter(addr => addr.trim());
    console.log(`📍 Found ${addresses.length} addresses`);
    
    // Create sample notes for some addresses
    const sampleNotes = [
      '7901 NW 8th St, Miami, FL 33126: Perros/Rejas',
      '7905 NW 8th St, Miami, FL 33126: No Llamar',
      '7909 NW 8th St, Miami, FL 33126: Testigo',
      '7913 NW 8th St, Miami, FL 33126: No en Casa',
      '7917 NW 8th St, Miami, FL 33126: No Trespassing'
    ];
    
    // Update territory notes
    const updatedNotes = territory.notes ? 
      `${territory.notes}\n${sampleNotes.join('\n')}` : 
      sampleNotes.join('\n');
    
    await prisma.territory.update({
      where: { id: territory.id },
      data: { notes: updatedNotes }
    });

    console.log('✅ Sample notes added successfully!');
    console.log('\n📋 Added notes:');
    sampleNotes.forEach((note, index) => {
      console.log(`   ${index + 1}. ${note}`);
    });

  } catch (error) {
    console.error('❌ Error adding sample notes:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  addSampleNotes();
}

module.exports = { addSampleNotes };
