/**
 * Test Territory API - Check if boundary data is being returned correctly
 */

// Use built-in fetch (Node.js 18+)

async function testTerritoryAPI() {
  try {
    console.log('🧪 Testing Territory API for boundary data...\n');

    // First, login to get a token
    console.log('🔐 Logging in...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1234'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.statusText}`);
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ Login successful');

    // Get list of territories to find Territory 001
    console.log('\n📋 Getting territories list...');
    const territoriesResponse = await fetch('http://localhost:3000/api/territories', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!territoriesResponse.ok) {
      throw new Error(`Failed to get territories: ${territoriesResponse.statusText}`);
    }

    const territoriesData = await territoriesResponse.json();
    console.log('Territories response type:', typeof territoriesData);
    console.log('Is array:', Array.isArray(territoriesData));

    // Handle different response formats
    const territories = Array.isArray(territoriesData) ? territoriesData : territoriesData.territories || [];
    const territory001 = territories.find(t => t.territoryNumber === '001');

    if (!territory001) {
      throw new Error('Territory 001 not found');
    }

    console.log(`✅ Found Territory 001 with ID: ${territory001.id}`);

    // Test the individual territory API
    console.log('\n🎯 Testing individual territory API...');
    const territoryResponse = await fetch(`http://localhost:3000/api/territories/${territory001.id}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!territoryResponse.ok) {
      throw new Error(`Failed to get territory details: ${territoryResponse.statusText}`);
    }

    const territoryData = await territoryResponse.json();

    console.log('\n📊 Territory API Response:');
    console.log(`  Territory Number: ${territoryData.territoryNumber}`);
    console.log(`  Address: ${territoryData.address?.split('\n')[0] || 'No address'}`);
    console.log(`  Has Coordinates: ${territoryData.coordinates ? 'YES' : 'NO'}`);
    console.log(`  Has Boundary: ${territoryData.boundary ? 'YES' : 'NO'}`);

    if (territoryData.boundary) {
      console.log(`  Boundary Type: ${territoryData.boundary.type}`);
      console.log(`  Boundary Coordinates Length: ${territoryData.boundary.coordinates?.[0]?.length || 0}`);
    }

    if (territoryData.boundary) {
      console.log('\n🎉 SUCCESS: Territory API is returning boundary data!');
      console.log('   The map should now display territory boundaries properly.');
    } else {
      console.log('\n❌ ISSUE: Territory API is NOT returning boundary data');
      console.log('   The map will not show territory boundaries.');
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

testTerritoryAPI();
