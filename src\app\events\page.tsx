'use client';

/**
 * Events Page - Mobile-First Design
 *
 * Member interface for viewing congregation events and activities.
 * Mobile-optimized design matching the screenshots with orange header and card layout.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  EventData,
  EventCategory,
  EVENT_CATEGORY_LABELS
} from '@/lib/services/eventManagementService';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface EventSummary {
  totalEvents: number;
  upcomingEvents: number;
  eventsByCategory: Record<EventCategory, number>;
  nextEvent?: EventData;
}

export default function EventsPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [events, setEvents] = useState<EventData[]>([]);
  const [summary, setSummary] = useState<EventSummary | null>(null);
  const [isLoadingEvents, setIsLoadingEvents] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedEvent, setExpandedEvent] = useState<string | null>(null);

  useEffect(() => {
    loadUserData();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (user) {
      loadEventsData();
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadUserData = async () => {
    try {
      setError(null);
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        router.push('/login');
        return;
      }

      // Use dashboard API to get user data (same pattern as dashboard page)
      const response = await fetch('/api/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token is invalid, redirect to login
          localStorage.removeItem('hermanos_token');
          localStorage.removeItem('hermanos_user');
          localStorage.removeItem('hermanos_congregation');
          localStorage.removeItem('hermanos_permissions');
          router.push('/login');
          return;
        }
        throw new Error('Failed to load user data');
      }

      const data = await response.json();

      // Set user data from dashboard response
      setUser({
        id: data.user.id,
        name: data.user.name,
        role: data.user.role,
        congregationId: data.user.congregationId,
        congregationName: data.congregation.name,
        hasCongregationPinAccess: data.user.hasCongregationPinAccess
      });

    } catch (error) {
      console.error('Failed to load user data:', error);
      setError('Error al cargar datos del usuario');
    } finally {
      setIsLoading(false);
    }
  };

  const loadEventsData = async () => {
    if (!user) return;

    setIsLoadingEvents(true);
    setError(null);

    try {
      const token = localStorage.getItem('hermanos_token');

      if (!token) {
        setError('No hay token de autenticación');
        return;
      }

      // Load summary and events
      const [summaryResponse, eventsResponse] = await Promise.all([
        fetch('/api/events/summary', {
          headers: { 'Authorization': `Bearer ${token}` },
        }),
        fetch('/api/events?limit=20', {
          headers: { 'Authorization': `Bearer ${token}` },
        }),
      ]);

      // Handle summary response
      if (summaryResponse.ok) {
        const summaryData = await summaryResponse.json();
        setSummary(summaryData.summary);
      } else if (summaryResponse.status === 401) {
        // Only redirect to login on 401, not on other errors
        localStorage.removeItem('hermanos_token');
        localStorage.removeItem('hermanos_user');
        localStorage.removeItem('hermanos_congregation');
        localStorage.removeItem('hermanos_permissions');
        router.push('/login');
        return;
      }

      // Handle events response
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json();
        setEvents(eventsData.events || []);
      } else if (eventsResponse.status === 401) {
        // Only redirect to login on 401, not on other errors
        localStorage.removeItem('hermanos_token');
        localStorage.removeItem('hermanos_user');
        localStorage.removeItem('hermanos_congregation');
        localStorage.removeItem('hermanos_permissions');
        router.push('/login');
        return;
      } else {
        // For non-401 errors, just show error message
        try {
          const errorData = await eventsResponse.json();
          setError(errorData.error || 'Error al cargar eventos');
        } catch {
          setError('Error al cargar eventos');
        }
      }
    } catch (error) {
      console.error('Error loading events data:', error);
      setError('Error al cargar los datos de eventos');
    } finally {
      setIsLoadingEvents(false);
    }
  };

  const getEventCategoryColor = (category: EventCategory): string => {
    const colors: Record<EventCategory, string> = {
      [EventCategory.ASSEMBLY]: 'bg-blue-100 text-blue-800 border-blue-200',
      [EventCategory.CONVENTION]: 'bg-purple-100 text-purple-800 border-purple-200',
      [EventCategory.SPECIAL_MEETING]: 'bg-green-100 text-green-800 border-green-200',
      [EventCategory.SERVICE_ACTIVITY]: 'bg-orange-100 text-orange-800 border-orange-200',
      [EventCategory.SOCIAL_ACTIVITY]: 'bg-pink-100 text-pink-800 border-pink-200',
      [EventCategory.TRAINING]: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      [EventCategory.OTHER]: 'bg-gray-100 text-gray-800 border-gray-200',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const formatEventDate = (date: Date): string => {
    return new Date(date).toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatEventTime = (startTime?: string, endTime?: string, isAllDay?: boolean): string => {
    if (isAllDay) return 'Todo el día';
    if (!startTime) return 'Hora por confirmar';
    if (!endTime) return startTime;
    return `${startTime} - ${endTime}`;
  };

  const isEventUpcoming = (eventDate: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return new Date(eventDate) >= today;
  };

  const toggleEventExpansion = (eventId: string) => {
    setExpandedEvent(expandedEvent === eventId ? null : eventId);
  };

  const addToCalendar = (event: EventData) => {
    // Create calendar event URL (Google Calendar format)
    const startDate = new Date(event.eventDate);
    const endDate = new Date(event.eventDate);

    if (event.startTime) {
      const [hours, minutes] = event.startTime.split(':');
      startDate.setHours(parseInt(hours), parseInt(minutes));
    }

    if (event.endTime) {
      const [hours, minutes] = event.endTime.split(':');
      endDate.setHours(parseInt(hours), parseInt(minutes));
    } else {
      endDate.setHours(startDate.getHours() + 2); // Default 2 hours
    }

    const formatDateForCalendar = (date: Date) => {
      return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    };

    const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(event.title)}&dates=${formatDateForCalendar(startDate)}/${formatDateForCalendar(endDate)}&details=${encodeURIComponent(event.description || '')}&location=${encodeURIComponent(event.location || '')}`;

    window.open(calendarUrl, '_blank');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile-first container - exact match to reference design */}
      <div className="max-w-lg mx-auto min-h-screen bg-gray-100 relative">
        {/* Header - Orange theme matching screenshots */}
        <header className="bg-orange-500 text-white">
          <div className="px-4 py-4">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/dashboard')}
                className="mr-4 text-white hover:text-orange-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-xl font-bold">Eventos</h1>
              {/* Clock icon on the right */}
              <div className="ml-auto">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto pb-20">
          {error && (
            <div className="mx-4 mt-4 bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {isLoadingEvents ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
                <p className="mt-2 text-gray-600 text-sm">Cargando eventos...</p>
              </div>
            </div>
          ) : events.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 px-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-300 rounded-lg flex items-center justify-center mb-4">
                  <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <p className="text-gray-600 text-base">No hay eventos próximos.</p>
              </div>
            </div>
          ) : (
            <div className="px-4 py-4 space-y-4">
              {events.map((event) => (
                <div key={event.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                  {/* Event Header - Gray background like in screenshots */}
                  <div
                    className="bg-gray-400 text-white p-4 cursor-pointer"
                    onClick={() => toggleEventExpansion(event.id)}
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-white font-semibold text-base uppercase tracking-wide">
                        {event.title}
                      </h3>
                      <div className="flex items-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Event Details */}
                  <div className="p-4">
                    <div className="flex items-start mb-3">
                      <svg className="w-5 h-5 text-gray-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <span className="text-gray-700 text-sm">{formatEventDate(event.eventDate)}</span>
                    </div>

                    <div className="flex items-start mb-3">
                      <svg className="w-5 h-5 text-gray-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span className="text-gray-700 text-sm">{event.location || 'Ubicación por confirmar'}</span>
                    </div>

                    {/* Expanded Details */}
                    {expandedEvent === event.id && (
                      <div className="mt-4 p-4 bg-orange-50 rounded-lg">
                        <h4 className="font-semibold text-gray-900 mb-2">Fecha</h4>
                        <p className="text-gray-700 mb-4">{formatEventDate(event.eventDate)}</p>

                        <h4 className="font-semibold text-gray-900 mb-2">Ubicación</h4>
                        <p className="text-gray-700 mb-4">{event.location || 'Ubicación por confirmar'}</p>

                        <h4 className="font-semibold text-gray-900 mb-2">Detalles</h4>
                        <p className="text-gray-700 mb-4">
                          {event.description || 'Información adicional será proporcionada próximamente.'}
                        </p>

                        {/* Add to Calendar Button */}
                        <button
                          onClick={() => addToCalendar(event)}
                          className="w-full bg-orange-500 text-white py-2 px-4 rounded-md font-medium hover:bg-orange-600 transition-colors flex items-center justify-center"
                        >
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          Añadir al calendario
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </main>

        {/* Bottom Navigation - Matching mobile design */}
        <nav className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-lg bg-white border-t border-gray-200">
          <div className="flex justify-around py-2">
            <button
              onClick={() => router.push('/dashboard')}
              className="flex flex-col items-center py-2 px-3"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              <span className="text-xs text-gray-600 mt-1">Inicio</span>
            </button>

            <button
              onClick={() => router.push('/field-service')}
              className="flex flex-col items-center py-2 px-3"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span className="text-xs text-gray-600 mt-1">Servicio</span>
            </button>

            <button
              onClick={() => router.push('/midweek-meetings')}
              className="flex flex-col items-center py-2 px-3"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <span className="text-xs text-gray-600 mt-1">Entre Semana</span>
            </button>

            <button
              onClick={() => router.push('/weekend-meetings')}
              className="flex flex-col items-center py-2 px-3"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className="text-xs text-gray-600 mt-1">Fin de Semana</span>
            </button>

            <button
              onClick={() => router.push('/assignments')}
              className="flex flex-col items-center py-2 px-3"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
              <span className="text-xs text-gray-600 mt-1">Asignaciones</span>
            </button>
          </div>
        </nav>
      </div>
    </div>
  );
}
