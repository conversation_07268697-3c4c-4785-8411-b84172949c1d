# Enhanced Document Management System

## Overview

The Enhanced Document Management System provides comprehensive document organization, access control, and management capabilities for congregation documents and letters. This system extends the basic letter functionality with advanced features like categorization, role-based access, search, and folder organization.

## Features

### 🔐 Role-Based Access Control
- **Elders**: Full access to all documents
- **Ministerial Servants**: Access to all-members and ministerial-servant+ documents
- **Publishers**: Access to all-members documents only

### 📁 Document Organization
- **Categories**: Letters, Forms, Announcements, Guidelines, Reports
- **Subcategories**: Custom subcategories within each category
- **Folders**: Organized folder structure for better document management
- **Tags**: Flexible tagging system for cross-category organization

### 🔍 Advanced Search & Filtering
- **Full-text search**: Search document titles, descriptions, and content
- **Category filtering**: Filter by document categories and subcategories
- **Tag filtering**: Filter by document tags
- **Date range filtering**: Filter by upload date or expiration date
- **Priority filtering**: Filter by document priority levels
- **Visibility filtering**: Filter by access level

### 📊 Document Metadata
- **Priority levels**: Low, Normal, High, Urgent
- **Status tracking**: Active, Draft, Archived, Expired
- **Version control**: Track document versions
- **Usage statistics**: Download and view counts
- **Expiration dates**: Automatic expiration handling
- **File information**: Size, type, upload date

## API Endpoints

### GET /api/documents
Retrieve documents with filtering and pagination.

**Query Parameters:**
- `category` - Filter by category
- `subcategory` - Filter by subcategory
- `visibility` - Filter by visibility level
- `priority` - Filter by priority
- `status` - Filter by status
- `folderId` - Filter by folder
- `search` - Full-text search
- `tags` - Comma-separated list of tags
- `startDate` - Filter by upload date (from)
- `endDate` - Filter by upload date (to)
- `isExpired` - Filter expired documents
- `limit` - Number of results (default: 50)
- `offset` - Pagination offset (default: 0)

**Response:**
```json
{
  "documents": [
    {
      "id": 1,
      "title": "Document Title",
      "description": "Document description",
      "filename": "document.pdf",
      "filePath": "/uploads/documents/document.pdf",
      "category": "letters",
      "subcategory": "branch-letters",
      "tags": ["important", "branch"],
      "visibility": "ALL_MEMBERS",
      "priority": "HIGH",
      "status": "ACTIVE",
      "uploadDate": "2024-01-15T10:00:00Z",
      "expirationDate": null,
      "fileSize": 1024000,
      "mimeType": "application/pdf"
    }
  ]
}
```

### POST /api/documents
Create a new document record.

**Request Body:**
```json
{
  "title": "Document Title",
  "description": "Document description",
  "filename": "document.pdf",
  "filePath": "/uploads/documents/document.pdf",
  "category": "letters",
  "subcategory": "branch-letters",
  "tags": ["important", "branch"],
  "visibility": "ALL_MEMBERS",
  "priority": "HIGH",
  "expirationDate": "2024-12-31T23:59:59Z"
}
```

### POST /api/documents/upload
Upload a document file and create document record.

**Form Data:**
- `file` - The document file
- `title` - Document title
- `description` - Document description
- `category` - Document category
- `subcategory` - Document subcategory
- `visibility` - Access level
- `priority` - Priority level
- `tags` - Comma-separated tags
- `expirationDate` - Expiration date (optional)

## Components

### DocumentManager (Admin)
Full-featured document management interface for administrators.

**Features:**
- Document upload with metadata
- Advanced filtering and search
- Document editing and management
- Bulk operations
- Statistics and reporting

**Usage:**
```tsx
import DocumentManager from '@/components/admin/DocumentManager';

<DocumentManager 
  congregationId={user.congregationId}
  userRole={user.role}
/>
```

### DocumentViewer (Frontend)
User-friendly document viewing interface for congregation members.

**Features:**
- Category-based organization
- Search and filtering
- Document preview and download
- Mobile-responsive design
- Role-based visibility

**Usage:**
```tsx
import DocumentViewer from '@/components/documents/DocumentViewer';

<DocumentViewer 
  congregationId={user.congregationId}
  userRole={user.role}
/>
```

## Database Schema

### Letters Table (Enhanced)
The existing `letters` table has been enhanced with additional fields:

```sql
-- New fields added to existing letters table
ALTER TABLE letters ADD COLUMN category VARCHAR(50);
ALTER TABLE letters ADD COLUMN subcategory VARCHAR(50);
ALTER TABLE letters ADD COLUMN tags TEXT[];
ALTER TABLE letters ADD COLUMN priority VARCHAR(20) DEFAULT 'NORMAL';
ALTER TABLE letters ADD COLUMN status VARCHAR(20) DEFAULT 'ACTIVE';
ALTER TABLE letters ADD COLUMN version INTEGER DEFAULT 1;
ALTER TABLE letters ADD COLUMN parent_id INTEGER;
ALTER TABLE letters ADD COLUMN folder_id INTEGER;
ALTER TABLE letters ADD COLUMN expiration_date TIMESTAMP;
ALTER TABLE letters ADD COLUMN publish_date TIMESTAMP;
ALTER TABLE letters ADD COLUMN approved_by_id INTEGER;
ALTER TABLE letters ADD COLUMN approved_at TIMESTAMP;
ALTER TABLE letters ADD COLUMN download_count INTEGER DEFAULT 0;
ALTER TABLE letters ADD COLUMN view_count INTEGER DEFAULT 0;
ALTER TABLE letters ADD COLUMN searchable_text TEXT;
ALTER TABLE letters ADD COLUMN thumbnail_path VARCHAR(500);
```

### DocumentFolders Table
```sql
CREATE TABLE document_folders (
  id SERIAL PRIMARY KEY,
  congregation_id INTEGER NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  color VARCHAR(7),
  icon VARCHAR(10),
  parent_id INTEGER,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Installation & Setup

### 1. Run Database Migration
```bash
npm run docs:migrate
```

This will:
- Update existing letters with default values
- Create default document folders
- Set up proper categorization
- Ensure search indexes are in place

### 2. Test the System
```bash
npm run docs:test
```

This will run comprehensive tests to verify:
- Document creation and retrieval
- Role-based access control
- Search and filtering functionality
- Performance benchmarks

### 3. Update Navigation
Add links to the document management system in your navigation:

```tsx
// For admin users
<Link href="/admin/documents">Manage Documents</Link>

// For all users
<Link href="/documents">Documents</Link>
```

## Configuration

### File Upload Settings
Configure file upload limits and allowed types in the upload API:

```typescript
// Maximum file size (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Allowed file types
const ALLOWED_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/jpeg',
  'image/png',
  'image/gif',
  'text/plain'
];
```

### Default Categories
The system creates these default categories:
- **Letters**: Official letters and communications
- **Forms**: Service reports, applications, forms
- **Announcements**: Congregation announcements
- **Guidelines**: Instructions and guidelines
- **Reports**: Monthly reports and statistics

## Security Considerations

### Access Control
- All API endpoints require authentication
- Role-based access is enforced at the service level
- File uploads are restricted to authorized users
- File types and sizes are validated

### File Security
- Files are stored outside the web root when possible
- File access is controlled through the application
- Malicious file uploads are prevented through type validation
- File paths are sanitized to prevent directory traversal

## Performance Optimization

### Database Indexes
- Full-text search index on title, description, and searchable_text
- Indexes on frequently filtered fields (category, visibility, priority)
- Composite indexes for common filter combinations

### Caching
- Document metadata can be cached for improved performance
- Search results can be cached for common queries
- File metadata is stored in the database to avoid filesystem calls

## Troubleshooting

### Common Issues

1. **Upload fails with "File type not allowed"**
   - Check the ALLOWED_TYPES configuration
   - Ensure the file has the correct MIME type

2. **Documents not visible to users**
   - Check the visibility setting on the document
   - Verify the user's role and permissions

3. **Search not working**
   - Ensure the full-text search index is created
   - Run `npm run docs:migrate` to set up indexes

4. **Performance issues**
   - Check database indexes are in place
   - Consider implementing caching for frequently accessed documents
   - Monitor file storage usage

### Debug Mode
Enable debug logging by setting the environment variable:
```bash
DEBUG=enhanced-documents npm run dev
```

## Future Enhancements

### Planned Features
- Document approval workflow
- Automatic document expiration notifications
- Document templates
- Bulk document operations
- Advanced analytics and reporting
- Integration with external storage services
- Document collaboration features

### API Extensions
- Bulk upload API
- Document conversion API
- Advanced search with faceted filtering
- Document sharing and permissions API
