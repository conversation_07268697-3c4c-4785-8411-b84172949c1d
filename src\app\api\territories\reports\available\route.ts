import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyToken } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyToken(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { congregationId } = authResult;

    // Get available territories
    const availableTerritories = await prisma.territory.findMany({
      where: {
        congregationId: congregationId,
        status: 'available'
      },
      include: {
        assignments: {
          where: {
            status: 'completed'
          },
          orderBy: {
            completedAt: 'desc'
          },
          take: 1,
          select: {
            completedAt: true
          }
        }
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });

    // Format the data for the report
    const reportData = availableTerritories.map(territory => {
      let lastAssigned = null;
      let daysAvailable = 0;

      if (territory.assignments.length > 0 && territory.assignments[0].completedAt) {
        lastAssigned = territory.assignments[0].completedAt.toISOString();
        const today = new Date();
        const completedDate = new Date(territory.assignments[0].completedAt);
        daysAvailable = Math.floor((today.getTime() - completedDate.getTime()) / (1000 * 60 * 60 * 24));
      }

      return {
        territoryNumber: territory.territoryNumber,
        address: territory.address,
        lastAssigned: lastAssigned,
        daysAvailable: daysAvailable
      };
    });

    return NextResponse.json(reportData);

  } catch (error) {
    console.error('Error fetching available territories report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
