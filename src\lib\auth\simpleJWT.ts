/**
 * Simple JWT Manager for Hermanos App
 *
 * Provides JWT token generation and verification with mobile-friendly
 * long expiration times and congregation-based authentication.
 */

import jwt, { SignOptions } from 'jsonwebtoken';

export interface JWTPayload {
  userId: string;
  congregationId: string;
  role: string;
  name: string;
  hasCongregationPinAccess?: boolean;
  iat?: number;
  exp?: number;
}

export interface TokenOptions {
  expiresIn?: string;
  noExpiration?: boolean;
}

export class SimpleJWTManager {
  private static readonly JWT_SECRET = process.env.JWT_SECRET!;
  private static readonly DEFAULT_TOKEN_EXPIRY = '60d'; // Mobile-friendly long expiration
  private static readonly ISSUER = 'hermanos-app';

  /**
   * Generate a JWT token for authenticated user
   */
  static generateToken(
    payload: Omit<JWTPayload, 'iat' | 'exp'>,
    options: TokenOptions = {}
  ): string {
    if (!this.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is required');
    }

    const tokenOptions: SignOptions = {
      issuer: this.ISSUER,
      audience: payload.congregationId,
    };

    // Handle token expiration configuration
    if (!options.noExpiration) {
      const expiresIn = options.expiresIn || this.DEFAULT_TOKEN_EXPIRY;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (tokenOptions as any).expiresIn = expiresIn;
    }

    return jwt.sign(payload, this.JWT_SECRET, tokenOptions);
  }

  /**
   * Verify and decode a JWT token
   */
  static verifyToken(token: string): JWTPayload | null {
    try {
      if (!this.JWT_SECRET) {
        throw new Error('JWT_SECRET environment variable is required');
      }

      const decoded = jwt.verify(token, this.JWT_SECRET, {
        issuer: this.ISSUER,
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      // Token is invalid, expired, or malformed
      console.error('JWT verification failed:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Decode token without verification (for debugging)
   */
  static decodeToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.decode(token) as JWTPayload;
      return decoded;
    } catch (error) {
      console.error('JWT decode failed:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    const decoded = this.decodeToken(token);
    if (!decoded || !decoded.exp) {
      return true;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  }

  /**
   * Refresh token if it's close to expiration
   */
  static refreshTokenIfNeeded(
    token: string,
    refreshThresholdDays: number = 7
  ): string | null {
    const decoded = this.decodeToken(token);
    if (!decoded || !decoded.exp) {
      return null;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const thresholdTime = refreshThresholdDays * 24 * 60 * 60; // Convert days to seconds

    // If token expires within threshold, generate new token
    if (decoded.exp - currentTime < thresholdTime) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { iat: _iat, exp: _exp, ...payload } = decoded;
      return this.generateToken(payload);
    }

    return null; // No refresh needed
  }

  /**
   * Extract token from Authorization header
   */
  static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * Generate token with custom expiration for admin users
   */
  static generateAdminToken(
    payload: Omit<JWTPayload, 'iat' | 'exp'>,
    canDisableExpiration: boolean = false
  ): string {
    const options: TokenOptions = {};

    // Allow developers and elders to have longer sessions
    if (canDisableExpiration && (payload.role === 'developer' || payload.role === 'elder')) {
      // Very long expiration for admin users (1 year)
      options.expiresIn = '365d';
    }

    return this.generateToken(payload, options);
  }
}
