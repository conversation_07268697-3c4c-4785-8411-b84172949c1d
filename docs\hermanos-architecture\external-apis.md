# External APIs

## JW.org Data Scraping

- **Purpose:** Fetch Life and Ministry Meeting Workbook content using proven web scraping logic from existing wol-scraper.js implementation
- **Documentation:** No official API - uses web scraping with Puppeteer to extract meeting data from public JW.org pages
- **Base URL(s):** `https://wol.jw.org/es/wol/meetings/r4/lp-s` (Spanish), similar pattern for other languages
- **Authentication:** No authentication required - public data scraping
- **Rate Limits:** No official limits, but respectful scraping with caching implemented

**Key Endpoints Scraped:**
- `GET https://wol.jw.org/es/wol/meetings/r4/lp-s/{year}/{week}` - Weekly meeting content
- `GET https://wol.jw.org/es/wol/meetings/r4/lp-s/{year}` - Year overview for workbook discovery
- Dynamic song URLs extracted from meeting content for song title retrieval

**Integration Notes:** 
- **Must preserve exact wol-scraper.js logic** - Copy the proven Puppeteer-based implementation that handles:
  - Browser automation with proper wait conditions
  - HTML parsing for meeting themes, scriptures, and parts
  - Song number extraction and dynamic title fetching
  - Robust error handling and fallback mechanisms
- **Alternative implementation**: Create MCP server using the same scraping logic for better modularity
- **Caching strategy**: Implement local caching to reduce scraping frequency and improve performance

**Technical Implementation Details from wol-scraper.js:**
- Uses Puppeteer for browser automation
- Extracts meeting themes using `.publicationMeetingTheme` selector
- Parses scripture citations with `.scriptureCitation` selector
- Handles dynamic song title fetching with fallback to generic titles
- Implements year/week URL pattern discovery
- Includes comprehensive error handling and browser cleanup
