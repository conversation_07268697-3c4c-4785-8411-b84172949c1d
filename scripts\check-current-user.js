const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCurrentUser() {
  try {
    console.log('🔍 Checking current user and permissions...');

    // Get all members for congregation 1441
    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        isActive: true
      },
      select: {
        id: true,
        name: true,
        role: true,
        pin: true,
        email: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`\n👥 Found ${members.length} active members in congregation 1441:`);
    
    members.forEach(member => {
      console.log(`- ${member.name} (${member.role}) - PIN: ${member.pin || 'No PIN'} - Email: ${member.email || 'No email'}`);
    });

    // Check which user might be the developer
    const developers = members.filter(m => m.role === 'developer');
    const elders = members.filter(m => m.role === 'elder');
    const coordinators = members.filter(m => m.role === 'coordinator');
    const ministerialServants = members.filter(m => m.role === 'ministerial_servant');

    console.log('\n📊 Role Summary:');
    console.log(`- Developers: ${developers.length}`);
    console.log(`- Elders: ${elders.length}`);
    console.log(`- Coordinators: ${coordinators.length}`);
    console.log(`- Ministerial Servants: ${ministerialServants.length}`);

    if (developers.length > 0) {
      console.log('\n🔧 Developer accounts:');
      developers.forEach(dev => {
        console.log(`- ${dev.name} - PIN: ${dev.pin}`);
      });
    }

    console.log('\n💡 To access field service management, you need to login with:');
    console.log('- A user with role: developer, elder, coordinator, overseer_coordinator, or ministerial_servant');
    console.log('- Use their PIN to login');

  } catch (error) {
    console.error('❌ Error checking users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentUser();
