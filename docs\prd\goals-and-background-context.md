# Goals and Background Context

## Goals

- Create comprehensive PRD and architecture documentation to build "Hermanos" app from scratch in a new directory
- Implement multi-congregation support leveraging existing database structure for scalable congregation management
- Expand multilingual support beyond Spanish/English to accommodate diverse congregation languages
- Preserve existing theme system for section color customization and visual identity management
- Replicate all current functionality based on app screenshots without adding new features
- Maintain existing security model without over-complication beyond current implementation
- Preserve and document the critical JW.org data fetching logic and URL patterns for meeting content integration
- Use Coral Oeste App as reference architecture for building the new "Hermanos" multi-congregation system
- Preserve exact UI structure with member view side for all users and administrative section for leadership roles

## Background Context

The "Hermanos" app represents an evolution from the successful Coral Oeste App, expanding from single-congregation management to a multi-congregation platform. The existing Coral Oeste system has proven its value in managing congregation activities, and the rebuild aims to scale this proven functionality across multiple congregations while modernizing the technology stack from MySQL/Node.js to PostgreSQL/Next.js.

This rebuild focuses on architectural modernization and multi-tenancy rather than feature expansion, ensuring the proven workflows remain intact while enabling broader deployment. The first congregation will be Coral Oeste Spanish with Spanish as the default language.

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-24 | 1.0 | Initial PRD creation for "Hermanos" multi-congregation app | Product Manager |
