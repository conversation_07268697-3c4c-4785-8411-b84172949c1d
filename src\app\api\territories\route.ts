/**
 * Territory Management API Endpoint
 *
 * Handles territory data retrieval for admin interface.
 * Supports filtering by status, search, and congregation isolation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

// Validation schema for query parameters
const TerritoryQuerySchema = z.object({
  status: z.enum(['available', 'assigned', 'completed', 'out_of_service']).nullable().optional(),
  search: z.string().nullable().optional(),
  congregationId: z.string().nullable().optional()
});

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user still exists and get their current role
    const member = await prisma.member.findUnique({
      where: {
        id: user.userId,
        isActive: true,
      },
    });

    if (!member) {
      return NextResponse.json(
        { error: 'User not found or inactive' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions (same logic as auth/verify endpoint)
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for territory management' },
        { status: 403 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      status: searchParams.get('status'),
      search: searchParams.get('search'),
      congregationId: searchParams.get('congregationId') || user.congregationId
    };

    const validatedParams = TerritoryQuerySchema.parse(queryParams);

    // Ensure congregation isolation
    if (validatedParams.congregationId !== user.congregationId) {
      return NextResponse.json(
        { error: 'Cannot access territories for different congregation' },
        { status: 403 }
      );
    }

    // Build where clause for filtering
    const where: any = {
      congregationId: user.congregationId
    };

    // Add status filter if provided
    if (validatedParams.status && validatedParams.status !== null) {
      where.status = validatedParams.status;
    }

    // Add search filter if provided
    if (validatedParams.search && validatedParams.search !== null) {
      const searchTerm = validatedParams.search.toLowerCase();
      where.OR = [
        {
          territoryNumber: {
            contains: searchTerm,
            mode: 'insensitive'
          }
        },
        {
          address: {
            contains: searchTerm,
            mode: 'insensitive'
          }
        }
      ];
    }

    // Fetch territories with assignment information
    const territories = await prisma.territory.findMany({
      where,
      include: {
        assignments: {
          where: {
            status: 'active'
          },
          include: {
            member: {
              select: {
                id: true,
                name: true
              }
            },
            assignedByMember: {
              select: {
                id: true,
                name: true
              }
            }
          },
          orderBy: {
            assignedAt: 'desc'
          },
          take: 1
        }
      },
      orderBy: [
        { displayOrder: 'asc' },
        { territoryNumber: 'asc' }
      ]
    });

    // Transform data for frontend consumption
    const transformedTerritories = territories.map(territory => {
      const currentAssignment = territory.assignments.length > 0 ? territory.assignments[0] : null;

      // Parse boundary data and extract coordinates from boundary
      let coordinates = null;
      let boundary = null;

      try {
        // Parse boundary if it exists
        if (territory.boundaries) {
          boundary = typeof territory.boundaries === 'string'
            ? JSON.parse(territory.boundaries)
            : territory.boundaries;

          // Extract center coordinates from boundary for map centering
          if (boundary && boundary.coordinates && boundary.coordinates[0]) {
            const polygonCoords = boundary.coordinates[0];
            if (polygonCoords.length > 0) {
              // Calculate center point of the polygon
              const lats = polygonCoords.map((coord: number[]) => coord[1]);
              const lngs = polygonCoords.map((coord: number[]) => coord[0]);

              const centerLat = lats.reduce((sum: number, lat: number) => sum + lat, 0) / lats.length;
              const centerLng = lngs.reduce((sum: number, lng: number) => sum + lng, 0) / lngs.length;

              coordinates = {
                latitude: centerLat,
                longitude: centerLng
              };
            }
          }
        }
      } catch (parseError) {
        console.warn(`Error parsing territory ${territory.territoryNumber} boundary:`, parseError);
      }

      return {
        id: territory.id,
        territoryNumber: territory.territoryNumber,
        address: territory.address,
        status: territory.status,
        notes: territory.notes,
        displayOrder: territory.displayOrder,
        coordinates,
        boundary,
        assignedMember: currentAssignment ? {
          id: currentAssignment.member.id,
          name: currentAssignment.member.name,
          assignedAt: currentAssignment.assignedAt,
          assignedBy: currentAssignment.assignedByMember.name
        } : null,
        createdAt: territory.createdAt,
        updatedAt: territory.updatedAt
      };
    });

    // Calculate summary statistics
    const summary = {
      total: territories.length,
      available: territories.filter(t => t.status === 'available').length,
      assigned: territories.filter(t => t.status === 'assigned').length,
      completed: territories.filter(t => t.status === 'completed').length,
      out_of_service: territories.filter(t => t.status === 'out_of_service').length
    };

    return NextResponse.json({
      success: true,
      territories: transformedTerritories,
      summary,
      filters: {
        status: validatedParams.status,
        search: validatedParams.search
      }
    });

  } catch (error) {
    console.error('Territory GET error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch territories',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve territories.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve territories.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve territories.' },
    { status: 405 }
  );
}
