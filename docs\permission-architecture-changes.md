# Permission Architecture Changes - 2025

## Overview

This document summarizes the architectural changes made to the Hermanos application permission system in January 2025. The changes simplify the permission model while enhancing security and delegation capabilities.

## Key Architectural Changes

### 1. Role System Simplification

**REMOVED:**
- `DEVELOPER` role - Eliminated from the system entirely
- `OVERSEER_COORDINATOR` role - Replaced with simplified `COOR<PERSON><PERSON>TOR` role

**ADDED:**
- `COOR<PERSON>NATOR` role - Has full permissions by default, can delegate to others

**PRESERVED:**
- `PUBLISHER` role - Unchanged base permissions
- `MINISTERIAL_SERVANT` role - Unchanged base permissions  
- `ELDER` role - Unchanged base permissions

### 2. New Permission Model

The system now uses a **hybrid permission model** with three layers:

```
Layer 1: Base Role Permissions (RBAC)
    ↓
Layer 2: Coordinator Role (Full Access)
    ↓
Layer 3: Congregation PIN Access (Super Admin)
```

**Base Role Permissions (Layer 1):**
- Publishers: Basic dashboard and profile access
- Ministerial Servants: Limited admin access for tasks and field service
- Elders: Extended admin access for meetings, letters, events, and field service
- Coordinators: Full administrative access to all sections

**Coordinator Role (Layer 2):**
- Full permissions by default (equivalent to old overseer/coordinator)
- Can delegate specific section permissions to elders and ministerial servants
- Authority to assign and revoke permissions with full audit logging

**Congregation PIN Access (Layer 3):**
- "Super admin" access that bypasses all role restrictions
- Used for development, testing, and emergency administration
- Provides full overseer/coordinator capabilities regardless of user role
- Replaces the need for a dedicated developer role

### 3. Permission Delegation System

**Delegation Authority:**
- **Coordinators**: Can delegate permissions based on their role
- **Congregation PIN Holders**: Can delegate permissions with super admin access
- **Dual Authority Model**: Either coordinator role OR PIN access provides delegation authority

**Delegation Scope:**
- Section-specific permissions (meetings, tasks, letters, events, field service)
- Granular permission assignment (view, edit, create, assign, etc.)
- Time-limited permissions with expiration dates
- Permission revocation with audit trail

**Audit System:**
- Complete logging of all permission changes
- IP address and user agent tracking
- Reason codes for permission assignments/revocations
- Audit log access restricted to coordinators and PIN holders

### 4. Database Schema Changes

**Enhanced ElderPermission Model:**
```sql
-- New fields added to elder_permissions table
ALTER TABLE elder_permissions ADD COLUMN permissions JSONB NOT NULL DEFAULT '[]';
ALTER TABLE elder_permissions ADD COLUMN assigned_by VARCHAR(255) NOT NULL;
ALTER TABLE elder_permissions ADD COLUMN assigned_at TIMESTAMP NOT NULL DEFAULT NOW();
ALTER TABLE elder_permissions ADD COLUMN expiration_date TIMESTAMP NULL;
ALTER TABLE elder_permissions ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE elder_permissions ADD COLUMN notes TEXT NULL;
```

**New PermissionAuditLog Model:**
```sql
-- New table for permission change tracking
CREATE TABLE permission_audit_logs (
  id VARCHAR(255) PRIMARY KEY,
  congregation_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  action VARCHAR(50) NOT NULL, -- 'assign', 'revoke', 'modify', 'expire'
  section_id VARCHAR(255) NOT NULL,
  permissions JSONB NOT NULL,
  performed_by VARCHAR(255) NOT NULL,
  reason TEXT NULL,
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  timestamp TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### 5. API Changes

**Updated Endpoints:**
- `/api/admin/permissions` - Permission assignment and revocation
- `/api/admin/permissions/audit` - Audit log access

**New Authentication Context:**
```typescript
interface UserPermissionContext {
  userId: string;
  role: ROLES;
  congregationId: string;
  hasCongregationPinAccess?: boolean; // New field for PIN access
}

interface DelegationContext {
  performedBy: string;
  hasCongregationPinAccess?: boolean; // New field for PIN access
  ipAddress?: string;
  userAgent?: string;
}
```

**Authority Validation:**
- Old: `user.role === 'overseer_coordinator' || user.role === 'developer'`
- New: `user.role === 'coordinator' || user.hasCongregationPinAccess`

### 6. Service Layer Changes

**PermissionDelegationService:**
- Updated to use `DelegationContext` instead of individual parameters
- Enhanced validation with PIN access support
- Improved audit logging with comprehensive tracking

**PermissionChecker:**
- Added PIN access support to all permission checking methods
- Enhanced `canDelegatePermissions()` method
- Updated `getAccessibleSections()` with PIN bypass logic

## Migration Impact

### For Developers
- **No Role Required**: Developers use congregation PIN for full access
- **Development Access**: PIN provides complete administrative capabilities
- **Testing**: PIN access allows testing all permission scenarios

### For Coordinators
- **Full Access**: Coordinators have all permissions by default
- **Delegation Authority**: Can assign specific permissions to elders/ministerial servants
- **Audit Oversight**: Can view all permission changes and audit logs

### For Elders
- **Base Permissions**: Retain existing elder-level permissions
- **Delegated Permissions**: Can receive additional section-specific permissions
- **Limited Delegation**: Cannot delegate permissions to others (unless they have PIN access)

### For Ministerial Servants
- **Base Permissions**: Retain existing ministerial servant permissions
- **Delegated Permissions**: Can receive limited administrative permissions
- **No Delegation**: Cannot delegate permissions to others

### For Publishers
- **Unchanged**: Base permissions remain the same
- **PIN Access**: Can gain full access if they have congregation PIN

## Security Enhancements

1. **Reduced Attack Surface**: Fewer roles to manage and secure
2. **Audit Trail**: Complete logging of all permission changes
3. **Time-Limited Permissions**: Support for expiring delegated permissions
4. **IP Tracking**: Enhanced audit logging with network information
5. **PIN-Based Emergency Access**: Secure override mechanism for critical situations

## Implementation Status

- ✅ Database schema migration completed
- ✅ RBAC system updated with new roles
- ✅ Permission delegation service implemented
- ✅ API endpoints updated with new authentication model
- ✅ Permission checker enhanced with PIN access support
- ✅ Comprehensive testing completed
- ✅ Documentation updated across all files

## Next Steps

1. **Frontend Integration**: Update admin UI to support new permission model
2. **User Training**: Document new delegation workflows for coordinators
3. **Migration Scripts**: Create scripts to migrate existing role assignments
4. **Performance Monitoring**: Monitor permission checking performance with new model
5. **Security Review**: Conduct security audit of new permission system

---

*This document reflects the implemented changes as of January 2025. For technical implementation details, see the updated architecture documentation and API specifications.*
