/**
 * Territory Import API Endpoint
 * 
 * Handles Excel file uploads for territory data import.
 * Supports .xlsx files with territory number and address columns.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { ExcelImportService } from '@/services/territories/ImportService';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

// Validation schema for import request
const ImportRequestSchema = z.object({
  congregationId: z.string().min(1, 'Congregation ID is required'),
  overwriteDuplicates: z.boolean().optional().default(false)
});

// File validation
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_TYPES = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.ms-excel' // .xls (legacy)
];

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for territory import' },
        { status: 403 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const congregationId = formData.get('congregationId') as string || user.congregationId;
    const overwriteDuplicates = formData.get('overwriteDuplicates') === 'true';

    // Validate request data
    const validatedData = ImportRequestSchema.parse({
      congregationId,
      overwriteDuplicates
    });

    // Validate file
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File size exceeds maximum limit of ${MAX_FILE_SIZE / 1024 / 1024}MB` },
        { status: 400 }
      );
    }

    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only Excel files (.xlsx, .xls) are allowed' },
        { status: 400 }
      );
    }

    // Ensure congregation isolation
    if (congregationId !== user.congregationId) {
      return NextResponse.json(
        { error: 'Cannot import territories for different congregation' },
        { status: 403 }
      );
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'territories');
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${congregationId}-${timestamp}-${file.name}`;
    const filepath = join(uploadDir, filename);

    // Save file to disk
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filepath, buffer);

    try {
      // Parse Excel file
      console.log('Parsing Excel file:', filename);
      const parseResult = await ExcelImportService.parseExcelFile(buffer);

      if (parseResult.errors.length > 0 && parseResult.data.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Failed to parse Excel file',
          details: parseResult.errors,
          filename
        }, { status: 400 });
      }

      // Import territories to database
      console.log(`Importing ${parseResult.data.length} territories for congregation ${congregationId}`);
      const importResult = await ExcelImportService.importTerritories(
        parseResult.data,
        congregationId
      );

      // Log import results
      console.log('Import completed:', {
        successful: importResult.successfulImports,
        failed: importResult.failedImports,
        duplicates: importResult.duplicatesFound
      });

      // Return results
      return NextResponse.json({
        success: importResult.success,
        result: importResult,
        filename,
        message: importResult.summary
      }, { status: importResult.success ? 200 : 400 });

    } catch (error) {
      console.error('Import processing error:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to process import',
        message: error instanceof Error ? error.message : 'Unknown error',
        filename
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Territory import POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to import territories',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to import territories.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to import territories.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to import territories.' },
    { status: 405 }
  );
}
