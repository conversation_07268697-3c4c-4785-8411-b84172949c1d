/**
 * Members Management API Endpoint
 *
 * Handles CRUD operations for member profiles with proper authentication
 * and audit trail tracking.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { MemberManagementService } from '@/lib/services/memberManagementService';

// Validation schemas
const CreateMemberSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  email: z.string().email('Invalid email format').max(255, 'Email too long'),
  role: z.enum(['publisher', 'ministerial_servant', 'elder', 'coordinator'], {
    errorMap: () => ({ message: 'Invalid role' }),
  }),
  pin: z.string().min(4, 'PIN must be at least 4 characters').max(50, 'PIN too long'),
  reason: z.string().optional(),
});

const UpdateMemberSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  email: z.string().email('Invalid email format').max(255, 'Email too long').optional(),
  role: z.enum(['publisher', 'ministerial_servant', 'elder', 'overseer_coordinator'], {
    errorMap: () => ({ message: 'Invalid role' }),
  }).optional(),
  pin: z.string().min(4, 'PIN must be at least 4 characters').max(50, 'PIN too long').optional(),
  isActive: z.boolean().optional(),
  reason: z.string().optional(),
});

/**
 * GET - Retrieve members with pagination and filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'coordinator'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view members' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const search = searchParams.get('search') || undefined;
    const rolesParam = searchParams.get('roles') || searchParams.get('role') || undefined;
    const activeFilter = searchParams.get('active') ? searchParams.get('active') === 'true' : undefined;

    // Parse roles parameter (supports comma-separated values)
    let roleFilter: string | undefined;
    if (rolesParam) {
      const roles = rolesParam.split(',').map(r => r.trim()).filter(r => r);
      if (roles.length === 1) {
        roleFilter = roles[0];
      } else if (roles.length > 1) {
        // For multiple roles, we'll need to modify the service call
        roleFilter = rolesParam; // Pass the comma-separated string
      }
    }

    // Validate parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Get members
    const result = await MemberManagementService.getMembers(
      user.congregationId,
      page,
      limit,
      search,
      roleFilter,
      activeFilter
    );

    return NextResponse.json({
      success: true,
      ...result,
      page,
      limit,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Members GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch members',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Create a new member
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'coordinator'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create members' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = CreateMemberSchema.parse(body);

    // Create the member
    const member = await MemberManagementService.createMember(
      user.congregationId,
      validatedData,
      user.userId
    );

    return NextResponse.json({
      success: true,
      member,
      message: 'Member created successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Member POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to create member';

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT - Update a member
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'coordinator'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update members' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const { memberId, ...updateData } = body;

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    const validatedData = UpdateMemberSchema.parse(updateData);

    // Update the member
    const member = await MemberManagementService.updateMember(
      user.congregationId,
      memberId,
      validatedData,
      user.userId
    );

    return NextResponse.json({
      success: true,
      member,
      message: 'Member updated successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Member PUT error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to update member';

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT to deactivate members.' },
    { status: 405 }
  );
}
