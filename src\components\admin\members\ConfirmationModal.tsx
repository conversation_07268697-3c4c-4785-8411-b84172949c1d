'use client';

import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import { AlertTriangle, Info } from 'lucide-react';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  title: string;
  message: string;
  confirmText: string;
  confirmVariant?: 'danger' | 'warning' | 'success' | 'primary';
  icon?: 'warning' | 'info';
}

export default function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText,
  confirmVariant = 'primary',
  icon = 'warning',
}: ConfirmationModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setError(null);
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const handleConfirm = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      await onConfirm();
      onClose();
    } catch (error) {
      console.error('Confirmation action failed:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const getIconComponent = () => {
    const iconClasses = "w-5 h-5";

    switch (icon) {
      case 'warning':
        return <AlertTriangle className={`${iconClasses} text-amber-500`} />;
      case 'info':
        return <Info className={`${iconClasses} text-blue-500`} />;
      default:
        return <AlertTriangle className={`${iconClasses} text-amber-500`} />;
    }
  };

  const getConfirmButtonClasses = () => {
    const baseClasses = "px-4 py-2 text-sm font-medium border rounded-md focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2";

    switch (confirmVariant) {
      case 'danger':
        return `${baseClasses} text-white bg-red-600 border-transparent hover:bg-red-700 focus:ring-red-500`;
      case 'warning':
        return `${baseClasses} text-white bg-amber-600 border-transparent hover:bg-amber-700 focus:ring-amber-500`;
      case 'success':
        return `${baseClasses} text-white bg-green-600 border-transparent hover:bg-green-700 focus:ring-green-500`;
      case 'primary':
      default:
        return `${baseClasses} text-white bg-blue-600 border-transparent hover:bg-blue-700 focus:ring-blue-500`;
    }
  };

  const getIconBackgroundClasses = () => {
    switch (confirmVariant) {
      case 'danger':
        return 'bg-red-100';
      case 'warning':
        return 'bg-amber-100';
      case 'success':
        return 'bg-green-100';
      case 'primary':
      default:
        return 'bg-blue-100';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={title}
      size="sm"
    >
      <div className="space-y-4">
        {/* Icon and Message */}
        <div className="flex items-center space-x-3">
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${getIconBackgroundClasses()}`}>
            {getIconComponent()}
          </div>
          <div className="flex-1">
            <p className="text-sm text-gray-700">
              {message}
            </p>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Footer */}
        <div className="flex justify-end space-x-3 pt-3 border-t border-gray-200">
          <button
            type="button"
            onClick={handleClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            disabled={isSubmitting}
            className={getConfirmButtonClasses()}
          >
            {isSubmitting && (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
            <span>{isSubmitting ? 'Processing...' : confirmText}</span>
          </button>
        </div>
      </div>
    </Modal>
  );
}
