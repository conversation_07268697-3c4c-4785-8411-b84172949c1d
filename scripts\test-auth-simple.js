#!/usr/bin/env node

/**
 * Simple Authentication Test Script for Hermanos App
 * 
 * Tests the basic authentication data and database setup.
 * 
 * Usage: node scripts/test-auth-simple.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

class SimpleAuthTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = [];
  }

  addTestResult(testName, success, message) {
    this.testResults.push({
      test: testName,
      success,
      message,
    });
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  async testDatabaseConnection() {
    console.log('\n🔗 Testing Database Connection...');
    
    try {
      await this.prisma.$connect();
      this.addTestResult('Database Connection', true, 'Connected to PostgreSQL database');
      return true;
    } catch (error) {
      this.addTestResult('Database Connection', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testCongregationData() {
    console.log('\n🏛️ Testing Congregation Data...');
    
    try {
      // Test congregation exists
      const congregation = await this.prisma.congregation.findUnique({
        where: { id: 'CORALOES' },
      });

      if (!congregation) {
        this.addTestResult('Congregation Exists', false, 'Congregation CORALOES not found');
        return false;
      }

      this.addTestResult('Congregation Exists', true, `Found: ${congregation.name}`);

      // Test PIN hashing
      const testPin = 'coralpin123';
      const isPinValid = await bcrypt.compare(testPin, congregation.pin);
      this.addTestResult('PIN Verification', isPinValid, isPinValid ? 'PIN verified correctly' : 'PIN verification failed');

      // Test congregation is active
      this.addTestResult('Congregation Active', congregation.isActive, congregation.isActive ? 'Congregation is active' : 'Congregation is inactive');

      return isPinValid && congregation.isActive;
    } catch (error) {
      this.addTestResult('Congregation Data', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testMemberData() {
    console.log('\n👥 Testing Member Data...');
    
    try {
      // Test members exist
      const members = await this.prisma.member.findMany({
        where: {
          congregationId: 'CORALOES',
          isActive: true,
        },
      });

      this.addTestResult('Members Exist', members.length > 0, `Found ${members.length} active members`);

      if (members.length === 0) {
        return false;
      }

      // Test role distribution
      const roles = [...new Set(members.map(m => m.role))];
      this.addTestResult('Role Variety', roles.length > 1, `Found roles: ${roles.join(', ')}`);

      // Test elder exists
      const elders = members.filter(m => m.role === 'elder');
      this.addTestResult('Elder Exists', elders.length > 0, `Found ${elders.length} elder(s)`);

      // Test member PIN hashing (test first member)
      const firstMember = members[0];
      const memberPins = {
        'Juan Pérez': 'elder123',
        'María González': 'maria123',
        'Carlos Rodríguez': 'carlos123',
        'Ana Martínez': 'ana123',
        'Developer Admin': 'dev123',
      };

      const expectedPin = memberPins[firstMember.name];
      if (expectedPin) {
        const isPinValid = await bcrypt.compare(expectedPin, firstMember.pin);
        this.addTestResult('Member PIN', isPinValid, `${firstMember.name} PIN verified`);
      }

      return true;
    } catch (error) {
      this.addTestResult('Member Data', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testRoleData() {
    console.log('\n🎭 Testing Role Data...');
    
    try {
      // Test roles exist
      const roles = await this.prisma.role.findMany({
        where: { isActive: true },
      });

      this.addTestResult('Roles Exist', roles.length > 0, `Found ${roles.length} active roles`);

      if (roles.length === 0) {
        return false;
      }

      // Test specific roles
      const expectedRoles = ['publisher', 'ministerial_servant', 'elder', 'overseer_coordinator', 'developer'];
      const foundRoles = roles.map(r => r.name);
      
      for (const expectedRole of expectedRoles) {
        const roleExists = foundRoles.includes(expectedRole);
        this.addTestResult(`Role: ${expectedRole}`, roleExists, roleExists ? 'Found' : 'Missing');
      }

      // Test role permissions
      const elderRole = roles.find(r => r.name === 'elder');
      if (elderRole) {
        const hasPermissions = elderRole.permissions && elderRole.permissions.length > 0;
        this.addTestResult('Elder Permissions', hasPermissions, hasPermissions ? `${elderRole.permissions.length} permissions` : 'No permissions');
      }

      return true;
    } catch (error) {
      this.addTestResult('Role Data', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testAuthenticationFlow() {
    console.log('\n🔄 Testing Authentication Flow Simulation...');
    
    try {
      // Simulate login flow
      const congregationId = 'CORALOES';
      const pin = 'coralpin123';

      // Step 1: Find congregation
      const congregation = await this.prisma.congregation.findUnique({
        where: { 
          id: congregationId.toUpperCase(),
          isActive: true,
        },
      });

      if (!congregation) {
        this.addTestResult('Auth Flow: Congregation', false, 'Congregation not found');
        return false;
      }

      // Step 2: Verify PIN
      const isPinValid = await bcrypt.compare(pin, congregation.pin);
      if (!isPinValid) {
        this.addTestResult('Auth Flow: PIN', false, 'Invalid PIN');
        return false;
      }

      // Step 3: Get members
      const members = await this.prisma.member.findMany({
        where: {
          congregationId: congregation.id,
          isActive: true,
        },
        orderBy: [
          { role: 'desc' },
          { createdAt: 'asc' },
        ],
      });

      if (members.length === 0) {
        this.addTestResult('Auth Flow: Members', false, 'No active members');
        return false;
      }

      const defaultMember = members[0];

      // Step 4: Validate member data
      const hasRequiredFields = defaultMember.id && defaultMember.name && defaultMember.role;
      if (!hasRequiredFields) {
        this.addTestResult('Auth Flow: Member Data', false, 'Member missing required fields');
        return false;
      }

      this.addTestResult('Auth Flow: Complete', true, `Would authenticate as ${defaultMember.name} (${defaultMember.role})`);

      return true;
    } catch (error) {
      this.addTestResult('Authentication Flow', false, `Error: ${error.message}`);
      return false;
    }
  }

  generateReport() {
    console.log('\n📋 Authentication Test Report');
    console.log('==============================');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.test}: ${r.message}`));
    }
    
    const allPassed = failedTests === 0;
    if (allPassed) {
      console.log('\n🎉 All authentication tests passed!');
      console.log('\n📝 Next Steps:');
      console.log('1. Start the development server: npm run dev');
      console.log('2. Go to http://localhost:3001/login');
      console.log('3. Enter Congregation ID: CORALOES');
      console.log('4. Enter Congregation PIN: coralpin123');
      console.log('5. You should be redirected to the dashboard');
    } else {
      console.log('\n⚠️ Some authentication tests failed!');
    }
    
    return allPassed;
  }

  async run() {
    try {
      console.log('🚀 Starting simple authentication tests...');
      
      // Run all tests
      await this.testDatabaseConnection();
      await this.testCongregationData();
      await this.testMemberData();
      await this.testRoleData();
      await this.testAuthenticationFlow();
      
      // Generate report
      const success = this.generateReport();
      
      if (!success) {
        process.exit(1);
      }
      
    } catch (error) {
      console.error('\n💥 Test execution failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new SimpleAuthTester();
  tester.run().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = SimpleAuthTester;
