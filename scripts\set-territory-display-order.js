/**
 * Set Territory Display Order Script
 * 
 * Sets the display order for territories based on the Excel file order.
 * This preserves the exact order that territories appear in the Excel sheets.
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function setTerritoryDisplayOrder() {
  try {
    console.log('📋 Setting territory display order...');

    // Get list of all Excel files in Territorios directory
    const territoriosDir = path.join(__dirname, '..', 'Territorios');
    const files = fs.readdirSync(territoriosDir)
      .filter(file => file.endsWith('.xlsx'))
      .sort(); // Sort files to get consistent order

    console.log(`📁 Found ${files.length} Excel files`);

    // Extract territory numbers and create order mapping
    const territoryOrder = [];
    
    files.forEach((file, index) => {
      // Extract territory number from filename (e.g., "Terr. 001.xlsx" -> "001")
      const match = file.match(/Terr\.\s*(\d+)\.xlsx/i);
      if (match) {
        const territoryNumber = match[1].padStart(3, '0'); // Ensure 3-digit format
        territoryOrder.push({
          territoryNumber,
          displayOrder: index + 1,
          filename: file
        });
      }
    });

    console.log(`🔢 Extracted ${territoryOrder.length} territory numbers`);

    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (!congregation) {
      console.error('❌ Congregation 1441 (Coral Oeste) not found');
      process.exit(1);
    }

    console.log(`✅ Found congregation: ${congregation.name}`);

    // Update display order for each territory
    let updatedCount = 0;
    
    for (const territory of territoryOrder) {
      try {
        const result = await prisma.territory.updateMany({
          where: {
            congregationId: congregation.id,
            territoryNumber: territory.territoryNumber
          },
          data: {
            displayOrder: territory.displayOrder
          }
        });

        if (result.count > 0) {
          console.log(`✅ Updated Territory ${territory.territoryNumber} -> Order ${territory.displayOrder}`);
          updatedCount++;
        } else {
          console.log(`⚠️  Territory ${territory.territoryNumber} not found in database`);
        }
      } catch (error) {
        console.error(`❌ Error updating Territory ${territory.territoryNumber}:`, error.message);
      }
    }

    console.log(`\n🎉 Successfully updated display order for ${updatedCount} territories!`);
    
    // Display final order
    console.log('\n📋 Final Territory Order:');
    territoryOrder.forEach((territory, index) => {
      console.log(`   ${index + 1}. Territory ${territory.territoryNumber} (${territory.filename})`);
    });

  } catch (error) {
    console.error('❌ Error setting territory display order:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  setTerritoryDisplayOrder();
}

module.exports = { setTerritoryDisplayOrder };
