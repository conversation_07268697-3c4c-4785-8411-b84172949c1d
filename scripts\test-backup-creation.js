/**
 * Test Backup Creation Functionality
 * 
 * Tests the new Node.js-based backup creation
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs').promises;
const path = require('path');

const prisma = new PrismaClient();

async function testBackupCreation() {
  try {
    console.log('🗄️ Testing Backup Creation Functionality...\n');

    // Test database connection
    console.log('🔗 Testing database connection...');
    const testQuery = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection successful');

    // Test table access
    console.log('\n📋 Testing table access...');
    const tables = [
      'congregations',
      'members', 
      'roles',
      'elder_permissions',
      'songs',
      'letters',
      'tasks',
      'task_assignments',
      'field_service_records',
      'congregation_settings'
    ];

    for (const tableName of tables) {
      try {
        const count = await prisma.$queryRawUnsafe(`SELECT COUNT(*) FROM "${tableName}"`);
        console.log(`✅ ${tableName}: ${count[0].count} records`);
      } catch (error) {
        console.log(`⚠️ ${tableName}: table may not exist or is inaccessible`);
      }
    }

    // Test backup content generation
    console.log('\n📝 Testing backup content generation...');
    const timestamp = new Date().toISOString();
    let sqlContent = `-- Database Backup Created: ${timestamp}\n`;
    sqlContent += `-- Coral Oeste Congregation Database\n\n`;

    let totalRecords = 0;

    for (const tableName of tables) {
      try {
        console.log(`  Processing table: ${tableName}...`);
        
        const tableData = await prisma.$queryRawUnsafe(`SELECT * FROM "${tableName}"`);
        
        if (Array.isArray(tableData) && tableData.length > 0) {
          const columns = Object.keys(tableData[0]);
          const columnList = columns.map(col => `"${col}"`).join(', ');
          
          sqlContent += `-- Table: ${tableName} (${tableData.length} records)\n`;
          sqlContent += `INSERT INTO "${tableName}" (${columnList}) VALUES\n`;
          
          const values = tableData.slice(0, 2).map(row => { // Only show first 2 rows for testing
            const rowValues = columns.map(col => {
              const value = row[col];
              if (value === null) return 'NULL';
              if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
              if (value instanceof Date) return `'${value.toISOString()}'`;
              if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
              return String(value);
            });
            return `(${rowValues.join(', ')})`;
          });
          
          sqlContent += values.join(',\n') + ';\n\n';
          totalRecords += tableData.length;
          console.log(`    ✅ ${tableData.length} records processed`);
        } else {
          sqlContent += `-- No data in table ${tableName}\n\n`;
          console.log(`    ⚠️ No data found`);
        }
      } catch (tableError) {
        console.log(`    ❌ Error processing table: ${tableError.message}`);
        sqlContent += `-- Warning: Could not backup table ${tableName}\n\n`;
      }
    }

    sqlContent += `-- Backup completed: ${new Date().toISOString()}\n`;
    sqlContent += `-- Total records processed: ${totalRecords}\n`;

    // Test file writing
    console.log('\n💾 Testing backup file creation...');
    const backupDir = path.join(process.cwd(), 'backups');
    await fs.mkdir(backupDir, { recursive: true });
    
    const testFilename = `test_backup_${Date.now()}.sql`;
    const testFilePath = path.join(backupDir, testFilename);
    
    await fs.writeFile(testFilePath, sqlContent, 'utf8');
    
    const stats = await fs.stat(testFilePath);
    console.log(`✅ Backup file created: ${testFilename}`);
    console.log(`✅ File size: ${formatFileSize(stats.size)}`);
    console.log(`✅ Total records: ${totalRecords}`);

    // Show sample content
    console.log('\n📄 Sample backup content:');
    const sampleContent = sqlContent.split('\n').slice(0, 10).join('\n');
    console.log(sampleContent + '...');

    // Clean up test file
    await fs.unlink(testFilePath);
    console.log('\n🧹 Test backup file cleaned up');

    console.log('\n🎉 Backup creation test completed successfully!');
    console.log('The backup functionality should now work in the admin interface.');

  } catch (error) {
    console.error('❌ Backup creation test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Helper function to format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run the test
testBackupCreation().catch(console.error);
