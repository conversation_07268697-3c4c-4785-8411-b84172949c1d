template:
  id: brownfield-prd-template-v2
  name: Brownfield Enhancement PRD
  version: 2.0
  output:
    format: markdown
    filename: docs/prd.md
    title: "{{project_name}} Brownfield Enhancement PRD"

workflow:
  mode: interactive
  elicitation: advanced-elicitation

sections:
  - id: intro-analysis
    title: Intro Project Analysis and Context
    instruction: |
      IMPORTANT - SCOPE ASSESSMENT REQUIRED:
      
      This PRD is for SIGNIFICANT enhancements to existing projects that require comprehensive planning and multiple stories. Before proceeding:
      
      1. **Assess Enhancement Complexity**: If this is a simple feature addition or bug fix that could be completed in 1-2 focused development sessions, STOP and recommend: "For simpler changes, consider using the brownfield-create-epic or brownfield-create-story task with the Product Owner instead. This full PRD process is designed for substantial enhancements that require architectural planning and multiple coordinated stories."
      
      2. **Project Context**: Determine if we're working in an IDE with the project already loaded or if the user needs to provide project information. If project files are available, analyze existing documentation in the docs folder. If insufficient documentation exists, recommend running the document-project task first.
      
      3. **Deep Assessment Requirement**: You MUST thoroughly analyze the existing project structure, patterns, and constraints before making ANY suggestions. Every recommendation must be grounded in actual project analysis, not assumptions.
      
      Gather comprehensive information about the existing project. This section must be completed before proceeding with requirements.
      
      CRITICAL: Throughout this analysis, explicitly confirm your understanding with the user. For every assumption you make about the existing project, ask: "Based on my analysis, I understand that [assumption]. Is this correct?"
      
      Do not proceed with any recommendations until the user has validated your understanding of the existing system.
    sections:
      - id: existing-project-overview
        title: Existing Project Overview
        instruction: Check if document-project analysis was already performed. If yes, reference that output instead of re-analyzing.
        sections:
          - id: analysis-source
            title: Analysis Source
            instruction: |
              Indicate one of the following:
              - Document-project output available at: {{path}}
              - IDE-based fresh analysis
              - User-provided information
          - id: current-state
            title: Current Project State
            instruction: |
              - If document-project output exists: Extract summary from "High Level Architecture" and "Technical Summary" sections
              - Otherwise: Brief description of what the project currently does and its primary purpose
      - id: documentation-analysis
        title: Available Documentation Analysis
        instruction: |
          If document-project was run:
          - Note: "Document-project analysis available - using existing technical documentation"
          - List key documents created by document-project
          - Skip the missing documentation check below
          
          Otherwise, check for existing documentation:
        sections:
          - id: available-docs
            title: Available Documentation
            type: checklist
            items:
              - Tech Stack Documentation [[LLM: If from document-project, check ✓]]
              - Source Tree/Architecture [[LLM: If from document-project, check ✓]]
              - Coding Standards [[LLM: If from document-project, may be partial]]
              - API Documentation [[LLM: If from document-project, check ✓]]
              - External API Documentation [[LLM: If from document-project, check ✓]]
              - UX/UI Guidelines [[LLM: May not be in document-project]]
              - Technical Debt Documentation [[LLM: If from document-project, check ✓]]
              - "Other: {{other_docs}}"
            instruction: |
              - If document-project was already run: "Using existing project analysis from document-project output."
              - If critical documentation is missing and no document-project: "I recommend running the document-project task first..."
      - id: enhancement-scope
        title: Enhancement Scope Definition
        instruction: Work with user to clearly define what type of enhancement this is. This is critical for scoping and approach.
        sections:
          - id: enhancement-type
            title: Enhancement Type
            type: checklist
            instruction: Determine with user which applies
            items:
              - New Feature Addition
              - Major Feature Modification
              - Integration with New Systems
              - Performance/Scalability Improvements
              - UI/UX Overhaul
              - Technology Stack Upgrade
              - Bug Fix and Stability Improvements
              - "Other: {{other_type}}"
          - id: enhancement-description
            title: Enhancement Description
            instruction: 2-3 sentences describing what the user wants to add or change
          - id: impact-assessment
            title: Impact Assessment
            type: checklist
            instruction: Assess the scope of impact on existing codebase
            items:
              - Minimal Impact (isolated additions)
              - Moderate Impact (some existing code changes)
              - Significant Impact (substantial existing code changes)
              - Major Impact (architectural changes required)
      - id: goals-context
        title: Goals and Background Context
        sections:
          - id: goals
            title: Goals
            type: bullet-list
            instruction: Bullet list of 1-line desired outcomes this enhancement will deliver if successful
          - id: background
            title: Background Context
            type: paragraphs
            instruction: 1-2 short paragraphs explaining why this enhancement is needed, what problem it solves, and how it fits with the existing project
      - id: changelog
        title: Change Log
        type: table
        columns: [Change, Date, Version, Description, Author]

  - id: requirements
    title: Requirements
    instruction: |
      Draft functional and non-functional requirements based on your validated understanding of the existing project. Before presenting requirements, confirm: "These requirements are based on my understanding of your existing system. Please review carefully and confirm they align with your project's reality."
    elicit: true
    sections:
      - id: functional
        title: Functional
        type: numbered-list
        prefix: FR
        instruction: Each Requirement will be a bullet markdown with identifier starting with FR
        examples:
          - "FR1: The existing Todo List will integrate with the new AI duplicate detection service without breaking current functionality."
      - id: non-functional
        title: Non Functional
        type: numbered-list
        prefix: NFR
        instruction: Each Requirement will be a bullet markdown with identifier starting with NFR. Include constraints from existing system
        examples:
          - "NFR1: Enhancement must maintain existing performance characteristics and not exceed current memory usage by more than 20%."
      - id: compatibility
        title: Compatibility Requirements
        instruction: Critical for brownfield - what must remain compatible
        type: numbered-list
        prefix: CR
        template: "{{requirement}}: {{description}}"
        items:
          - id: cr1
            template: "CR1: {{existing_api_compatibility}}"
          - id: cr2
            template: "CR2: {{database_schema_compatibility}}"
          - id: cr3
            template: "CR3: {{ui_ux_consistency}}"
          - id: cr4
            template: "CR4: {{integration_compatibility}}"

  - id: ui-enhancement-goals
    title: User Interface Enhancement Goals
    condition: Enhancement includes UI changes
    instruction: For UI changes, capture how they will integrate with existing UI patterns and design systems
    sections:
      - id: existing-ui-integration
        title: Integration with Existing UI
        instruction: Describe how new UI elements will fit with existing design patterns, style guides, and component libraries
      - id: modified-screens
        title: Modified/New Screens and Views
        instruction: List only the screens/views that will be modified or added
      - id: ui-consistency
        title: UI Consistency Requirements
        instruction: Specific requirements for maintaining visual and interaction consistency with existing application

  - id: technical-constraints
    title: Technical Constraints and Integration Requirements
    instruction: This section replaces separate architecture documentation. Gather detailed technical constraints from existing project analysis.
    sections:
      - id: existing-tech-stack
        title: Existing Technology Stack
        instruction: |
          If document-project output available:
          - Extract from "Actual Tech Stack" table in High Level Architecture section
          - Include version numbers and any noted constraints
          
          Otherwise, document the current technology stack:
        template: |
          **Languages**: {{languages}}
          **Frameworks**: {{frameworks}}
          **Database**: {{database}}
          **Infrastructure**: {{infrastructure}}
          **External Dependencies**: {{external_dependencies}}
      - id: integration-approach
        title: Integration Approach
        instruction: Define how the enhancement will integrate with existing architecture
        template: |
          **Database Integration Strategy**: {{database_integration}}
          **API Integration Strategy**: {{api_integration}}
          **Frontend Integration Strategy**: {{frontend_integration}}
          **Testing Integration Strategy**: {{testing_integration}}
      - id: code-organization
        title: Code Organization and Standards
        instruction: Based on existing project analysis, define how new code will fit existing patterns
        template: |
          **File Structure Approach**: {{file_structure}}
          **Naming Conventions**: {{naming_conventions}}
          **Coding Standards**: {{coding_standards}}
          **Documentation Standards**: {{documentation_standards}}
      - id: deployment-operations
        title: Deployment and Operations
        instruction: How the enhancement fits existing deployment pipeline
        template: |
          **Build Process Integration**: {{build_integration}}
          **Deployment Strategy**: {{deployment_strategy}}
          **Monitoring and Logging**: {{monitoring_logging}}
          **Configuration Management**: {{config_management}}
      - id: risk-assessment
        title: Risk Assessment and Mitigation
        instruction: |
          If document-project output available:
          - Reference "Technical Debt and Known Issues" section
          - Include "Workarounds and Gotchas" that might impact enhancement
          - Note any identified constraints from "Critical Technical Debt"
          
          Build risk assessment incorporating existing known issues:
        template: |
          **Technical Risks**: {{technical_risks}}
          **Integration Risks**: {{integration_risks}}
          **Deployment Risks**: {{deployment_risks}}
          **Mitigation Strategies**: {{mitigation_strategies}}

  - id: epic-structure
    title: Epic and Story Structure
    instruction: |
      For brownfield projects, favor a single comprehensive epic unless the user is clearly requesting multiple unrelated enhancements. Before presenting the epic structure, confirm: "Based on my analysis of your existing project, I believe this enhancement should be structured as [single epic/multiple epics] because [rationale based on actual project analysis]. Does this align with your understanding of the work required?"
    elicit: true
    sections:
      - id: epic-approach
        title: Epic Approach
        instruction: Explain the rationale for epic structure - typically single epic for brownfield unless multiple unrelated features
        template: "**Epic Structure Decision**: {{epic_decision}} with rationale"

  - id: epic-details
    title: "Epic 1: {{enhancement_title}}"
    instruction: |
      Comprehensive epic that delivers the brownfield enhancement while maintaining existing functionality
      
      CRITICAL STORY SEQUENCING FOR BROWNFIELD:
      - Stories must ensure existing functionality remains intact
      - Each story should include verification that existing features still work
      - Stories should be sequenced to minimize risk to existing system
      - Include rollback considerations for each story
      - Focus on incremental integration rather than big-bang changes
      - Size stories for AI agent execution in existing codebase context
      - MANDATORY: Present the complete story sequence and ask: "This story sequence is designed to minimize risk to your existing system. Does this order make sense given your project's architecture and constraints?"
      - Stories must be logically sequential with clear dependencies identified
      - Each story must deliver value while maintaining system integrity
    template: |
      **Epic Goal**: {{epic_goal}}
      
      **Integration Requirements**: {{integration_requirements}}
    sections:
      - id: story
        title: "Story 1.{{story_number}} {{story_title}}"
        repeatable: true
        template: |
          As a {{user_type}},
          I want {{action}},
          so that {{benefit}}.
        sections:
          - id: acceptance-criteria
            title: Acceptance Criteria
            type: numbered-list
            instruction: Define criteria that include both new functionality and existing system integrity
            item_template: "{{criterion_number}}: {{criteria}}"
          - id: integration-verification
            title: Integration Verification
            instruction: Specific verification steps to ensure existing functionality remains intact
            type: numbered-list
            prefix: IV
            items:
              - template: "IV1: {{existing_functionality_verification}}"
              - template: "IV2: {{integration_point_verification}}"
              - template: "IV3: {{performance_impact_verification}}"