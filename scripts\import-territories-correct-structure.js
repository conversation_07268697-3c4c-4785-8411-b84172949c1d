/**
 * Import Territories with Correct Structure
 * 
 * Territory 007: 4 buildings (6537, 6539, 6487, 6451)
 * Territory 009: 4 buildings (6261, 6251, 6239, 6237) 
 * Territory 010: Houses and businesses (NOT apartment buildings)
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const CONGREGATION_ID = '1441'; // Coral Oeste

/**
 * Parse Territory 007 - 4 Buildings
 */
function parseTerritory007(excelData) {
  const addresses = [];
  
  // Building 1: 6537 W FLAGLER ST (Rows 9-32)
  addresses.push({ address: '6537 W FLAGLER ST, Miami, FL 33144', isBuilding: true, isBuildingHeader: true });
  
  // Apartments for Building 6537 (Column B: 1-36, Column H: 8-44)
  const building6537Apts = [
    1, 2, 3, 4, 5, 6, 7, 15, 16, 17, 18, 19, 20, 21, 22, 23, 30, 31, 32, 33, 34, 35, 36,  // Column B
    8, 9, 10, 11, 12, 13, 14, 22, 23, 24, 25, 26, 27, 28, 29, 37, 38, 39, 40, 41, 42, 43, 44, 30  // Column H
  ];
  
  building6537Apts.forEach(apt => {
    let notes = null;
    if (apt === 32) notes = 'M Expulsada';
    addresses.push({ address: `Apt ${apt}`, notes: notes, isBuilding: true, isBuildingHeader: false });
  });
  
  // Building 2: 6539 W FLAGLER ST
  addresses.push({ address: '6539 W FLAGLER ST, Miami, FL 33144', isBuilding: true, isBuildingHeader: true });
  // (This building shares the same sheet but different column - apartments would be in Column H)
  
  // Building 3: 6487 W FLAGLER ST (Rows 43-61)
  addresses.push({ address: '6487 W FLAGLER ST, Miami, FL 33144', isBuilding: true, isBuildingHeader: true });
  
  // Apartments for Building 6487 (Column B: 1-18, Column H: 1-17)
  const building6487Apts = [
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,  // Column B
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17  // Column H
  ];
  
  building6487Apts.forEach(apt => {
    let notes = null;
    if (apt === 12) notes = 'A 10/7/19';
    addresses.push({ address: `Apt ${apt}`, notes: notes, isBuilding: true, isBuildingHeader: false });
  });
  
  // Building 4: 6451 W FLAGLER ST
  addresses.push({ address: '6451 W FLAGLER ST, Miami, FL 33144', isBuilding: true, isBuildingHeader: true });
  // (This building shares the same sheet but different column)
  
  return addresses;
}

/**
 * Parse Territory 009 - 4 Buildings with Floor Structure
 */
function parseTerritory009(excelData) {
  const addresses = [];
  
  // Building 1: 6261 W FLAGLER ST (Rows 8-45)
  addresses.push({ address: '6261 W FLAGLER ST, Miami, FL 33144', isBuilding: true, isBuildingHeader: true });
  
  const building6261Apts = [
    { apt: 1, notes: 'Mensaje 11/25' },
    { apt: 2, notes: 'Mensaje 11/25' },
    { apt: 3, notes: 'Hombre ocupado 11/25' },
    { apt: 4, notes: 'Carta de Ana' },
    { apt: 5, notes: 'Carta de Mayling' },
    { apt: 6, notes: 'Mensaje 11/25' },
    { apt: 7, notes: 'Mensaje 11/25' },
    { apt: 8, notes: 'Mensaje 11/4' },
    { apt: 9, notes: 'Mensaje 11/25' },
    { apt: 10, notes: 'Mensaje 11/25' },
    { apt: 11, notes: 'Mensaje 11/25' },
    { apt: 12, notes: null },
    { apt: 13, notes: null },
    { apt: 14, notes: null },
    { apt: 15, notes: 'Mujer hablando inglés 8/26' },
    { apt: 16, notes: null },
    { apt: 17, notes: 'Mensaje 7/8' },
    { apt: 18, notes: 'Mensaje 11/4' },
    { apt: 19, notes: null },
    { apt: 20, notes: null },
    { apt: 21, notes: null },
    { apt: 22, notes: null },
    { apt: 23, notes: null },
    { apt: 24, notes: null },
    { apt: 25, notes: null },
    { apt: 26, notes: null },
    { apt: 27, notes: null },
    { apt: 28, notes: null },
    { apt: 29, notes: null },
    { apt: 30, notes: null },
    { apt: 31, notes: null },
    { apt: 32, notes: null },
    { apt: 33, notes: null },
    { apt: 34, notes: null },
    { apt: 35, notes: null },
    { apt: 36, notes: null },
    { apt: 37, notes: null }
  ];
  
  building6261Apts.forEach(item => {
    addresses.push({ address: `Apt ${item.apt}`, notes: item.notes, isBuilding: true, isBuildingHeader: false });
  });
  
  // Building 2: 6251 W FLAGLER ST
  addresses.push({ address: '6251 W FLAGLER ST, Miami, FL 33144', isBuilding: true, isBuildingHeader: true });
  
  const building6251Apts = [
    { apt: 1, notes: null },
    { apt: 2, notes: 'Mensaje 11/25' },
    { apt: 3, notes: 'Mensaje 11/25' },
    { apt: 4, notes: 'Mensaje 8/4' },
    { apt: 5, notes: 'Carta de Leydy' },
    { apt: 6, notes: 'Mujer colgó 11/4' }
  ];
  
  building6251Apts.forEach(item => {
    addresses.push({ address: `Apt ${item.apt}`, notes: item.notes, isBuilding: true, isBuildingHeader: false });
  });
  
  // Building 3: 6239 W FLAGLER ST (with floor structure)
  addresses.push({ address: '6239 W FLAGLER ST, Miami, FL 33144', isBuilding: true, isBuildingHeader: true });
  
  const building6239Apts = [
    { apt: 9, notes: 'Mensaje 11/25' },
    { apt: 10, notes: 'Mensaje 11/25' },
    { apt: 11, notes: 'Muujer joven 11/25' },
    { apt: 12, notes: 'Hombre ocupado 11/25' },
    { apt: 13, notes: 'Mensaje 11/4' },
    { apt: 14, notes: 'Carta de Leydy' },
    { apt: 23, notes: null },
    { apt: 24, notes: null },
    { apt: 25, notes: 'Mujer colgó 8/26' },
    { apt: 27, notes: null },
    { apt: 28, notes: 'Hombre amable no quiso 8/4' },
    { apt: 29, notes: null },
    { apt: 36, notes: 'Carta de Carmen' },
    { apt: 37, notes: null },
    { apt: 38, notes: null },
    { apt: 39, notes: 'Mujer escuchó 11/11' },
    { apt: 40, notes: 'Mujer no quiso 11/11' }
  ];
  
  building6239Apts.forEach(item => {
    addresses.push({ address: `Apt ${item.apt}`, notes: item.notes, isBuilding: true, isBuildingHeader: false });
  });
  
  // Building 4: 6237 W FLAGLER ST
  addresses.push({ address: '6237 W FLAGLER ST, Miami, FL 33144', isBuilding: true, isBuildingHeader: true });
  
  const building6237Apts = [
    { apt: 1, notes: null },
    { apt: 2, notes: null },
    { apt: 3, notes: null },
    { apt: 4, notes: null },
    { apt: 5, notes: null },
    { apt: 6, notes: null },
    { apt: 7, notes: null },
    { apt: 8, notes: null },
    { apt: 15, notes: null },
    { apt: 16, notes: null },
    { apt: 17, notes: null },
    { apt: 18, notes: null },
    { apt: 19, notes: null },
    { apt: 20, notes: null },
    { apt: 21, notes: null },
    { apt: 22, notes: null },
    { apt: 30, notes: null },
    { apt: 31, notes: null },
    { apt: 32, notes: null },
    { apt: 33, notes: null },
    { apt: 34, notes: null },
    { apt: 35, notes: null }
  ];
  
  building6237Apts.forEach(item => {
    addresses.push({ address: `Apt ${item.apt}`, notes: item.notes, isBuilding: true, isBuildingHeader: false });
  });
  
  return addresses;
}

/**
 * Parse Territory 010 - Houses and Businesses (NOT apartment buildings)
 */
function parseTerritory010(excelData) {
  const addresses = [];
  let currentStreet = '';
  
  // This is a regular house/business territory, not apartment buildings
  for (let i = 8; i < excelData.length; i++) {
    const row = excelData[i];
    if (!row || row.length === 0) continue;
    
    const cellB = row[1];
    if (!cellB) continue;
    
    const cellValue = cellB.toString().trim();
    if (!cellValue) continue;
    
    // Check if this is a street name
    if (cellValue.includes('AVE') || cellValue.includes('ST') || cellValue.includes('CT')) {
      currentStreet = cellValue;
      console.log(`📍 Found street: ${currentStreet}`);
      continue;
    }
    
    // Check if this is a house/business number
    if (/^\d+[a-z]?$/.test(cellValue) && currentStreet) {
      const houseNumber = cellValue;
      const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL 33144`;
      
      // Get notes
      let notes = null;
      if (row[6] && typeof row[6] === 'string') {
        const noteText = row[6].toString().trim();
        if (noteText && noteText !== 'null' && noteText !== '') {
          notes = noteText;
        }
      }
      
      addresses.push({
        address: fullAddress,
        notes: notes,
        street: currentStreet,
        houseNumber: houseNumber,
        isBuilding: false
      });
      
      console.log(`🏠 Added address: ${fullAddress}${notes ? ` (${notes})` : ''}`);
    }
  }
  
  return addresses;
}

async function importTerritoryCorrectStructure(territoryNumber) {
  try {
    console.log(`\n📂 Importing Territory ${territoryNumber} (Correct Structure)...`);
    
    const configs = {
      '007': { displayOrder: 7, zipCode: 'Miami, FL 33144', sheetName: 'Terr 7' },
      '009': { displayOrder: 9, zipCode: 'Miami, FL 33144', sheetName: 'Terr 9' },
      '010': { displayOrder: 10, zipCode: 'Miami, FL 33144', sheetName: 'Terr 10' }
    };
    
    const config = configs[territoryNumber];
    if (!config) {
      console.error(`❌ No configuration found for Territory ${territoryNumber}`);
      return false;
    }

    // Read Excel file
    const filePath = path.join(__dirname, '..', 'Territorios', `Terr. ${territoryNumber}.xlsx`);
    const workbook = XLSX.readFile(filePath);
    const worksheet = workbook.Sheets[config.sheetName];
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse based on territory type
    let addresses = [];
    if (territoryNumber === '007') {
      addresses = parseTerritory007(excelData);
    } else if (territoryNumber === '009') {
      addresses = parseTerritory009(excelData);
    } else if (territoryNumber === '010') {
      addresses = parseTerritory010(excelData);
    }
    
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: CONGREGATION_ID }
    });

    if (!congregation) {
      console.error(`❌ Congregation ${CONGREGATION_ID} (Coral Oeste) not found`);
      return false;
    }

    // Clear existing territory
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: congregation.id,
        territory: { territoryNumber: territoryNumber }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: congregation.id,
        territoryNumber: territoryNumber
      }
    });
    
    console.log(`🗑️  Cleared existing Territory ${territoryNumber}`);
    
    // Create territory
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: congregation.id,
        territoryNumber: territoryNumber,
        address: allAddresses,
        notes: allNotes || null,
        status: 'available',
        displayOrder: config.displayOrder
      }
    });
    
    console.log(`✅ Created Territory ${territoryNumber} with ${addresses.length} addresses`);
    
    // Display summary
    if (territoryNumber === '007' || territoryNumber === '009') {
      const buildingHeaders = addresses.filter(addr => addr.isBuildingHeader);
      const apartments = addresses.filter(addr => !addr.isBuildingHeader);
      console.log(`   🏢 Buildings: ${buildingHeaders.length}`);
      console.log(`   🚪 Apartments: ${apartments.length}`);
    } else {
      console.log(`   🏠 Houses/Businesses: ${addresses.length}`);
    }
    console.log(`   📝 With Notes: ${addresses.filter(addr => addr.notes).length}`);
    
    return true;
    
  } catch (error) {
    console.error(`❌ Error importing Territory ${territoryNumber}:`, error.message);
    return false;
  }
}

async function importAllCorrectStructures() {
  try {
    console.log('🚀 Starting correct structure import for territories 7, 9, 10...');
    
    const territories = ['007', '009', '010'];
    let successCount = 0;
    
    for (const territory of territories) {
      const success = await importTerritoryCorrectStructure(territory);
      if (success) successCount++;
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`\n🎉 Import completed! Successfully imported: ${successCount} territories`);
    
  } catch (error) {
    console.error('❌ Error during import:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  importAllCorrectStructures();
}

module.exports = { importTerritoryCorrectStructure };
