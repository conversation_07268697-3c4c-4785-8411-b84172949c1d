# Story 7.2: Service Group Coordination and Management

**Epic:** Epic 7: Advanced Field Service & Territory Management  
**Story Points:** 13  
**Priority:** High  
**Status:** Draft  

## Story

As a service overseer and group overseer,
I want advanced service group management with coordination tools and communication features,
so that I can effectively organize field service activities and support publishers in their ministry.

## Acceptance Criteria

1. **Service group management with member assignment and group overseer designation**
   - Comprehensive service group creation and management with member assignment workflows
   - Group overseer designation with role-based permissions and administrative capabilities
   - Member transfer between service groups with approval workflows and history tracking
   - Service group composition optimization with balanced assignment recommendations

2. **Group meeting scheduling and coordination with location and time management**
   - Advanced scheduling system for service group meetings with recurring appointment support
   - Location management with meeting point coordination and address validation
   - Calendar integration with conflict detection and alternative scheduling suggestions
   - Meeting reminder system with automated notifications and confirmation tracking

3. **Service arrangement planning with territory assignment and meeting point coordination**
   - Service arrangement planning interface with territory assignment and coordination
   - Meeting point management with location optimization and accessibility considerations
   - Transportation coordination with carpooling arrangements and route optimization
   - Service activity planning with territory coverage and publisher participation tracking

4. **Group communication system with announcements and ministry updates**
   - Integrated communication platform with group announcements and ministry updates
   - Message broadcasting with targeted delivery and read receipt tracking
   - Ministry resource sharing with document distribution and access management
   - Emergency communication system with urgent notification capabilities

5. **Group performance tracking with participation rates and activity monitoring**
   - Comprehensive performance dashboard with participation rates and activity metrics
   - Individual publisher tracking with service hour monitoring and goal progress
   - Group activity analysis with trend identification and improvement recommendations
   - Comparative analysis between service groups with best practice sharing

6. **Service goal setting and progress tracking for groups and individuals**
   - Goal setting interface for both group and individual service objectives
   - Progress tracking with milestone monitoring and achievement recognition
   - Goal adjustment workflows with overseer approval and guidance integration
   - Motivation and encouragement system with progress celebration and support

7. **Integration with congregation-wide service campaigns and special activities**
   - Campaign coordination with congregation-wide service initiatives and special activities
   - Special activity planning with resource allocation and participation tracking
   - Circuit assembly and convention coordination with group participation management
   - Memorial and special event coordination with service group involvement

## Dev Notes

### Technical Architecture

**Frontend Components:**
- `ServiceGroupManager.tsx` - Main service group management interface
- `GroupMemberAssignment.tsx` - Member assignment and transfer management
- `ServiceMeetingScheduler.tsx` - Group meeting scheduling and coordination
- `ServiceArrangementPlanner.tsx` - Service arrangement and territory coordination
- `GroupCommunication.tsx` - Communication platform with messaging and announcements
- `GroupPerformanceTracker.tsx` - Performance monitoring and analytics dashboard
- `ServiceGoalManager.tsx` - Goal setting and progress tracking interface

**Backend Services:**
- `service-group-service.ts` - Core service group management and coordination
- `group-scheduling-service.ts` - Meeting scheduling and calendar management
- `service-arrangement-service.ts` - Service arrangement planning and coordination
- `group-communication-service.ts` - Communication platform and messaging
- `group-performance-service.ts` - Performance tracking and analytics
- `service-goal-service.ts` - Goal setting and progress monitoring
- `campaign-coordination-service.ts` - Congregation-wide campaign management

**Database Tables:**
- `service_groups` - Service group definitions with overseer and member assignments
- `group_meetings` - Service group meeting scheduling and coordination
- `service_arrangements` - Service arrangement planning and territory assignments
- `group_communications` - Communication messages and announcements
- `group_performance` - Performance tracking and activity monitoring
- `service_goals` - Goal setting and progress tracking for groups and individuals

### API Endpoints (tRPC)

```typescript
// Service group coordination routes
serviceGroups: router({
  createServiceGroup: adminProcedure
    .input(z.object({
      name: z.string(),
      overseerId: z.string(),
      memberIds: z.array(z.string()),
      meetingDay: z.number(),
      meetingTime: z.string(),
      meetingLocation: z.string()
    }))
    .mutation(async ({ input, ctx }) => {
      return await serviceGroupService.createGroup(
        input,
        ctx.user.congregationId
      );
    }),

  scheduleGroupMeeting: protectedProcedure
    .input(z.object({
      groupId: z.string(),
      meetingDate: z.date(),
      location: z.string(),
      serviceArrangement: z.object({
        territoryId: z.string(),
        meetingPoint: z.string(),
        startTime: z.string()
      }),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await groupSchedulingService.scheduleMeeting(
        input.groupId,
        input,
        ctx.user.congregationId
      );
    }),

  sendGroupCommunication: protectedProcedure
    .input(z.object({
      groupId: z.string(),
      messageType: z.enum(['announcement', 'reminder', 'update', 'emergency']),
      subject: z.string(),
      message: z.string(),
      priority: z.enum(['low', 'normal', 'high', 'urgent']),
      requiresResponse: z.boolean().default(false)
    }))
    .mutation(async ({ input, ctx }) => {
      return await groupCommunicationService.sendMessage(
        input.groupId,
        input,
        ctx.user.memberId,
        ctx.user.congregationId
      );
    }),

  getGroupPerformance: protectedProcedure
    .input(z.object({
      groupId: z.string(),
      dateRange: z.object({
        start: z.date(),
        end: z.date()
      }),
      metricsType: z.enum(['participation', 'activity', 'goals', 'comparative'])
    }))
    .query(async ({ input, ctx }) => {
      return await groupPerformanceService.getPerformanceMetrics(
        input.groupId,
        input.dateRange,
        input.metricsType,
        ctx.user.congregationId
      );
    }),

  setServiceGoal: protectedProcedure
    .input(z.object({
      targetType: z.enum(['individual', 'group']),
      targetId: z.string(),
      goalType: z.enum(['hours', 'visits', 'studies', 'placements']),
      targetValue: z.number(),
      timeframe: z.enum(['monthly', 'quarterly', 'yearly']),
      description: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      return await serviceGoalService.setGoal(
        input,
        ctx.user.memberId,
        ctx.user.congregationId
      );
    })
})
```

### Data Models

```typescript
interface ServiceGroup {
  id: string;
  congregationId: string;
  name: string;
  overseerId: string;
  memberIds: string[];
  meetingSchedule: {
    dayOfWeek: number;
    time: string;
    location: string;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface GroupMeeting {
  id: string;
  groupId: string;
  congregationId: string;
  meetingDate: Date;
  location: string;
  serviceArrangement: {
    territoryId: string;
    meetingPoint: string;
    startTime: string;
  };
  attendees: string[];
  notes?: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

interface GroupCommunication {
  id: string;
  groupId: string;
  congregationId: string;
  senderId: string;
  messageType: 'announcement' | 'reminder' | 'update' | 'emergency';
  subject: string;
  message: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  requiresResponse: boolean;
  recipients: string[];
  readBy: { memberId: string; readAt: Date }[];
  responses: { memberId: string; response: string; respondedAt: Date }[];
  sentAt: Date;
  createdAt: Date;
}

interface ServiceGoal {
  id: string;
  congregationId: string;
  targetType: 'individual' | 'group';
  targetId: string;
  goalType: 'hours' | 'visits' | 'studies' | 'placements';
  targetValue: number;
  currentValue: number;
  timeframe: 'monthly' | 'quarterly' | 'yearly';
  startDate: Date;
  endDate: Date;
  description?: string;
  status: 'active' | 'achieved' | 'missed' | 'cancelled';
  setBy: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Critical Implementation Requirements

1. **Multi-Tenant Data Isolation**: Every database query must include congregation_id filtering
2. **Type Safety Enforcement**: All API calls must use tRPC procedures with Zod validation
3. **Authentication Required**: All protected routes must use authentication middleware
4. **Database-First Testing**: Use real database with comprehensive test data for service groups
5. **Local Infrastructure Only**: Use local PostgreSQL database and local file storage
6. **Performance Optimization**: Efficient group coordination with real-time communication

### Testing Requirements

**Unit Tests:**
- Service group management algorithms with member assignment logic
- Meeting scheduling with conflict detection and optimization
- Communication system with message delivery and tracking
- Performance tracking calculations with various metrics

**Integration Tests:**
- Complete service group coordination workflow
- Multi-group communication and coordination
- Goal setting and progress tracking integration
- Campaign coordination with congregation-wide activities

**E2E Tests:**
- Full service group management workflow from creation to coordination
- Group meeting scheduling and service arrangement planning
- Communication platform with messaging and response tracking
- Performance monitoring and goal achievement tracking

## Testing

### Test Data Requirements

- Seed database with diverse service group configurations and member assignments
- Include complex scheduling scenarios with overlapping meetings
- Test data should include various communication patterns and goal types
- Sample performance data for analytics and tracking validation

### Validation Scenarios

- Test service group coordination with large numbers of groups and members
- Validate communication system performance with high message volumes
- Test goal tracking accuracy with various timeframes and metrics
- Verify campaign coordination across multiple service groups

## Definition of Done

- [ ] Service group management with member assignment implemented
- [ ] Group meeting scheduling and coordination functional
- [ ] Service arrangement planning with territory coordination complete
- [ ] Group communication system with announcements working
- [ ] Group performance tracking with activity monitoring implemented
- [ ] Service goal setting and progress tracking functional
- [ ] Integration with congregation-wide campaigns complete
- [ ] All unit tests pass with real database data
- [ ] Integration tests validate multi-group coordination
- [ ] E2E tests confirm complete service group workflow
- [ ] Code review completed and approved
- [ ] Documentation updated with service group features

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: BMad Master Task Executor
- Date: 2025-01-24

### Debug Log References
- None yet

### Completion Notes
- Story created with comprehensive service group coordination system
- Advanced scheduling and communication features for group management
- Performance tracking and goal setting with progress monitoring
- Complete API specification with tRPC procedures for group coordination
- Testing requirements defined with multi-group scenario validation

### File List
- docs/stories/7.2.story.md (created)

### Change Log
- 2025-01-24: Initial story creation with service group coordination specification
