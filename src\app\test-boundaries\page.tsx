'use client';

import React, { useState, useEffect } from 'react';
import TerritoryMap from '@/components/territories/shared/TerritoryMap';
import SimpleTerritoryMap from '@/components/territories/shared/SimpleTerritoryMap';
import BoundaryService from '@/services/territories/BoundaryService';
import type { Territory } from '@/types/territories/map';

// Test territories with different boundary types
const testTerritories: Territory[] = [
  {
    id: '1',
    territoryNumber: '007',
    address: '6580 FLAGLER ST, Miami, FL 33144',
    coordinates: {
      latitude: 25.7620,
      longitude: -80.2715
    },
    status: 'assigned',
    boundary: BoundaryService.createSampleBoundary(25.7620, -80.2715, 0.4)
  },
  {
    id: '2',
    territoryNumber: '014',
    address: '6500 FLAGLER ST, Miami, FL 33144',
    coordinates: {
      latitude: 25.7615,
      longitude: -80.2708
    },
    status: 'available',
    boundary: BoundaryService.createSampleBoundary(25.7615, -80.2708, 0.3)
  },
  {
    id: '3',
    territoryNumber: '025',
    address: '6400 FLAGLER ST, Miami, FL 33144',
    coordinates: {
      latitude: 25.7610,
      longitude: -80.2700
    },
    status: 'completed',
    boundary: BoundaryService.createSampleBoundary(25.7610, -80.2700, 0.35)
  },
  {
    id: '4',
    territoryNumber: '032',
    address: '6300 FLAGLER ST, Miami, FL 33144',
    coordinates: {
      latitude: 25.7605,
      longitude: -80.2692
    },
    status: 'out_of_service',
    boundary: BoundaryService.createSampleBoundary(25.7605, -80.2692, 0.25)
  }
];

export default function TestBoundariesPage() {
  const [selectedTerritory, setSelectedTerritory] = useState<Territory | null>(null);
  const [mapHeight, setMapHeight] = useState('600px');
  const [useSimpleMap, setUseSimpleMap] = useState(false);

  const handleTerritorySelect = (territory: Territory) => {
    setSelectedTerritory(territory);
    console.log('Selected territory:', territory);
  };

  // Load MapLibre CSS
  useEffect(() => {
    if (typeof window !== 'undefined' && !document.querySelector('link[href*="maplibre-gl"]')) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://unpkg.com/maplibre-gl@3.6.2/dist/maplibre-gl.css';
      document.head.appendChild(link);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-green-600 text-white p-6">
            <h1 className="text-2xl font-bold">Territory Boundaries Test</h1>
            <p className="text-green-100 mt-2">
              Testing territory boundary visualization with different statuses and colors
            </p>
          </div>

          {/* Controls */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Altura del mapa:</label>
                <select
                  value={mapHeight}
                  onChange={(e) => setMapHeight(e.target.value)}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="400px">400px</option>
                  <option value="500px">500px</option>
                  <option value="600px">600px</option>
                  <option value="70vh">70vh</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Tipo de mapa:</label>
                <button
                  onClick={() => setUseSimpleMap(!useSimpleMap)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    useSimpleMap
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {useSimpleMap ? 'Mapa Simple' : 'Mapa Completo'}
                </button>
              </div>
            </div>
          </div>

          {/* Territory Status Legend */}
          <div className="p-6 border-b border-gray-200 bg-gray-50">
            <h3 className="text-lg font-semibold mb-4">Leyenda de Estados</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded border border-green-600"></div>
                <span className="text-sm">Disponible</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-blue-500 rounded border border-blue-600"></div>
                <span className="text-sm">Asignado</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-orange-500 rounded border border-orange-600"></div>
                <span className="text-sm">Completado</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-500 rounded border border-red-600"></div>
                <span className="text-sm">Fuera de Servicio</span>
              </div>
            </div>
          </div>

          {/* Territory Statistics */}
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold mb-4">Estadísticas de Territorios</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="bg-gray-100 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-gray-900">{testTerritories.length}</div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="bg-green-100 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">
                  {testTerritories.filter(t => t.status === 'available').length}
                </div>
                <div className="text-sm text-gray-600">Disponibles</div>
              </div>
              <div className="bg-blue-100 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {testTerritories.filter(t => t.status === 'assigned').length}
                </div>
                <div className="text-sm text-gray-600">Asignados</div>
              </div>
              <div className="bg-orange-100 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {testTerritories.filter(t => t.status === 'completed').length}
                </div>
                <div className="text-sm text-gray-600">Completados</div>
              </div>
              <div className="bg-red-100 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-red-600">
                  {testTerritories.filter(t => t.status === 'out_of_service').length}
                </div>
                <div className="text-sm text-gray-600">Fuera de Servicio</div>
              </div>
            </div>
          </div>

          {/* Territory Map */}
          <div className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Mapa con Límites de Territorios</h3>
              <p className="text-sm text-gray-600">
                Este mapa muestra los límites de cada territorio con colores según su estado.
                Usa el botón de límites en la esquina superior derecha para mostrar/ocultar los límites.
              </p>
            </div>

            <div className="border border-gray-300 rounded-lg overflow-hidden">
              {useSimpleMap ? (
                <SimpleTerritoryMap
                  territories={testTerritories}
                  height={mapHeight}
                  width="100%"
                />
              ) : (
                <TerritoryMap
                  territories={testTerritories}
                  selectedTerritory={selectedTerritory}
                  onTerritorySelect={handleTerritorySelect}
                  height={mapHeight}
                  width="100%"
                  showControls={true}
                  interactive={true}
                  className=""
                />
              )}
            </div>
          </div>

          {/* Selected Territory Details */}
          {selectedTerritory && (
            <div className="p-6 border-t border-gray-200 bg-gray-50">
              <h3 className="text-lg font-semibold mb-4">Territorio Seleccionado</h3>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-lg font-medium">Territorio {selectedTerritory.territoryNumber}</h4>
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                    selectedTerritory.status === 'available' ? 'bg-green-100 text-green-800' :
                    selectedTerritory.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                    selectedTerritory.status === 'completed' ? 'bg-orange-100 text-orange-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {selectedTerritory.status === 'available' ? 'Disponible' :
                     selectedTerritory.status === 'assigned' ? 'Asignado' :
                     selectedTerritory.status === 'completed' ? 'Completado' :
                     'Fuera de Servicio'}
                  </span>
                </div>
                <p className="text-gray-700 mb-3">{selectedTerritory.address}</p>
                {selectedTerritory.boundary && (
                  <div className="text-sm text-gray-600">
                    <p>✅ Límites definidos</p>
                    <p>📍 Coordenadas: {selectedTerritory.coordinates?.latitude.toFixed(4)}, {selectedTerritory.coordinates?.longitude.toFixed(4)}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Testing Instructions */}
          <div className="p-6 bg-gray-50 border-t border-gray-200">
            <h3 className="text-lg font-semibold mb-4">Instrucciones de Prueba</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Funciones de Límites</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Los límites se muestran con colores según el estado del territorio</li>
                  <li>• Haz clic en el botón de límites (esquina superior derecha) para mostrar/ocultar</li>
                  <li>• Haz clic en cualquier límite o marcador para seleccionar el territorio</li>
                  <li>• Los límites son interactivos y cambian el cursor al pasar el mouse</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Características Técnicas</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Límites generados automáticamente como polígonos circulares</li>
                  <li>• Colores dinámicos basados en el estado del territorio</li>
                  <li>• Integración con MapLibre GL JS para renderizado eficiente</li>
                  <li>• Soporte para polígonos complejos y multi-polígonos</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
