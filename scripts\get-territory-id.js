const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function getTerritoryId() {
  try {
    const territory = await prisma.territory.findFirst({
      where: { 
        congregationId: '1441', 
        territoryNumber: '001' 
      },
      select: { 
        id: true, 
        territoryNumber: true 
      }
    });
    
    console.log('Territory 001 ID:', territory?.id);
    
    // Also get Territory 007 for testing
    const territory007 = await prisma.territory.findFirst({
      where: { 
        congregationId: '1441', 
        territoryNumber: '007' 
      },
      select: { 
        id: true, 
        territoryNumber: true 
      }
    });
    
    console.log('Territory 007 ID:', territory007?.id);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getTerritoryId();
