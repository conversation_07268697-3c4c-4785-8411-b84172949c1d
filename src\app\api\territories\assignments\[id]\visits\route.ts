/**
 * Territory Visit Logging API Endpoint
 * 
 * Handles logging individual visits to territories, supporting partial completion
 * tracking and multiple visits per assignment.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { TerritoryStatusService } from '@/services/territories/TerritoryStatusService';

// Validation schemas
const VisitLogSchema = z.object({
  visitDate: z.string().transform(str => new Date(str)),
  isCompleted: z.boolean(),
  notes: z.string().optional(),
  addressesWorked: z.string().optional()
});

const PartialCompletionSchema = z.object({
  isPartiallyCompleted: z.boolean(),
  partialCompletionNotes: z.string().optional(),
  visitCount: z.number().min(0)
});

/**
 * POST /api/territories/assignments/[id]/visits
 * Log a new visit to a territory
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { congregationId: true, name: true }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Verify assignment exists and user has access
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        id: params.id,
        congregationId: member.congregationId,
        OR: [
          { memberId: user.userId }, // User is assigned to this territory
          // Or user has admin access (we'll check this below)
        ]
      },
      include: {
        territory: {
          select: {
            territoryNumber: true,
            address: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      }
    });

    if (!assignment) {
      return NextResponse.json(
        { error: 'Assignment not found or access denied' },
        { status: 404 }
      );
    }

    // Check if user has admin access if they're not the assigned member
    if (assignment.memberId !== user.userId) {
      const hasAdminAccess = user.hasCongregationPinAccess ||
        ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

      if (!hasAdminAccess) {
        return NextResponse.json(
          { error: 'Access denied - you can only log visits for your own assignments' },
          { status: 403 }
        );
      }
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = VisitLogSchema.parse(body);

    // Log the visit
    const result = await TerritoryStatusService.logTerritoryVisit({
      assignmentId: params.id,
      visitDate: validatedData.visitDate,
      isCompleted: validatedData.isCompleted,
      notes: validatedData.notes,
      addressesWorked: validatedData.addressesWorked,
      congregationId: member.congregationId
    });

    return NextResponse.json({
      success: true,
      data: {
        visit: result.visit,
        assignment: result.assignment,
        territory: assignment.territory,
        message: validatedData.isCompleted 
          ? `Territory ${assignment.territory.territoryNumber} visit logged and marked as completed`
          : `Territory ${assignment.territory.territoryNumber} visit logged`
      }
    });

  } catch (error) {
    console.error('Visit logging error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/territories/assignments/[id]/visits
 * Get visit history for an assignment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { congregationId: true, role: true }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Verify assignment exists and user has access
    const assignment = await prisma.territoryAssignment.findFirst({
      where: {
        id: params.id,
        congregationId: member.congregationId
      },
      include: {
        territory: {
          select: {
            territoryNumber: true,
            address: true
          }
        },
        member: {
          select: {
            name: true
          }
        }
      }
    });

    if (!assignment) {
      return NextResponse.json(
        { error: 'Assignment not found' },
        { status: 404 }
      );
    }

    // Check access permissions
    const hasAccess = assignment.memberId === user.userId ||
      user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(member.role);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get visit history and assignment statistics
    const [visits, stats] = await Promise.all([
      TerritoryStatusService.getVisitHistory(params.id, member.congregationId),
      TerritoryStatusService.getAssignmentStatistics(params.id, member.congregationId)
    ]);

    return NextResponse.json({
      success: true,
      data: {
        assignment: {
          id: assignment.id,
          territory: assignment.territory,
          member: assignment.member,
          assignedAt: assignment.assignedAt,
          status: assignment.status,
          visitCount: assignment.visitCount,
          isPartiallyCompleted: assignment.isPartiallyCompleted,
          partialCompletionNotes: assignment.partialCompletionNotes
        },
        visits,
        statistics: stats.statistics
      }
    });

  } catch (error) {
    console.error('Visit history get error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/territories/assignments/[id]/visits
 * Update partial completion status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get member information
    const member = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { congregationId: true, role: true }
    });

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = PartialCompletionSchema.parse(body);

    // Update partial completion status
    const updatedAssignment = await TerritoryStatusService.updatePartialCompletion({
      assignmentId: params.id,
      isPartiallyCompleted: validatedData.isPartiallyCompleted,
      partialCompletionNotes: validatedData.partialCompletionNotes,
      visitCount: validatedData.visitCount,
      congregationId: member.congregationId
    });

    return NextResponse.json({
      success: true,
      data: {
        assignment: updatedAssignment,
        message: `Partial completion status updated for territory ${updatedAssignment.territory.territoryNumber}`
      }
    });

  } catch (error) {
    console.error('Partial completion update error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
