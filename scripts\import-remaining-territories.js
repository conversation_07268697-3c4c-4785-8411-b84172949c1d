const XLSX = require('xlsx');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function importRemainingTerritories() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');

  // Get current territories to skip
  const existingTerritories = await prisma.territory.findMany({
    where: { congregationId: '1441' },
    select: { territoryNumber: true }
  });
  const existingNumbers = new Set(existingTerritories.map(t => t.territoryNumber));

  // Get all Excel files
  const excelFiles = fs.readdirSync(territoriosDir)
    .filter(file => file.endsWith('.xlsx'))
    .map(file => {
      const match = file.match(/Terr\.\s*(\d+)/);
      return match ? { file, number: match[1].padStart(3, '0') } : null;
    })
    .filter(Boolean)
    .filter(item => !existingNumbers.has(item.number))
    .sort((a, b) => a.number.localeCompare(b.number));

  console.log(`🚀 Importing ${excelFiles.length} territories...`);
  console.log(`Skipping existing: ${Array.from(existingNumbers).sort().join(', ')}\n`);

  let successCount = 0;
  let errorCount = 0;

  for (const { file, number } of excelFiles) {
    try {
      console.log(`📋 Processing Territory ${number} (${file})...`);

      const filePath = path.join(territoriosDir, file);
      const workbook = XLSX.readFile(filePath);

      // Find the main territory sheet (not "Mapa")
      const territorySheet = workbook.SheetNames.find(name =>
        name.toLowerCase().includes('terr') && !name.toLowerCase().includes('mapa')
      ) || workbook.SheetNames[0];

      const sheet = workbook.Sheets[territorySheet];
      const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });

      // Parse addresses and notes
      const addresses = [];
      const notes = [];
      let currentStreet = '';

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (!row || row.length === 0) continue;

        // Check for street names in second column (common pattern)
        const secondCell = row[1] ? row[1].toString().trim() : '';
        if (secondCell && /\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(secondCell)) {
          currentStreet = secondCell;
          continue;
        }

        // Check for house numbers (usually in second column, can be number or string type)
        let houseNumber = '';
        let observaciones = '';

        // Check second column for house numbers (most common pattern)
        if (!row[0] && row[1] !== undefined && row[1] !== null && row[1] !== '') {
          const cellValue = row[1].toString().trim();
          // House numbers can be pure numbers or numbers with letters
          if (/^\d+[A-Z]?$/i.test(cellValue) && cellValue !== 'No. de Casa') {
            houseNumber = cellValue;
            // Look for observations in column 7 (index 6) - the "Observaciones" column
            if (row[6] && row[6].toString().trim()) {
              const obs = row[6].toString().trim();
              if (obs !== 'Observaciones' && obs !== 'NO CAMBIAR ESTAS LETRAS') {
                observaciones = obs;
              }
            }
          }
        }

        if (houseNumber && currentStreet) {
          const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL`;
          addresses.push(fullAddress);

          if (observaciones && observaciones !== 'NO CAMBIAR ESTAS LETRAS') {
            notes.push(`${fullAddress}: ${observaciones}`);
          }
        }
      }

      if (addresses.length === 0) {
        console.log(`  ⚠️  No addresses found, skipping...`);
        continue;
      }

      // Create territory record
      const territory = await prisma.territory.create({
        data: {
          congregationId: '1441',
          territoryNumber: number,
          address: addresses.join('\n'),
          notes: notes.length > 0 ? notes.join('\n') : null,
          status: 'available',
          displayOrder: parseInt(number)
        }
      });

      console.log(`  ✅ Created territory ${number} with ${addresses.length} addresses`);
      if (notes.length > 0) {
        console.log(`     📝 ${notes.length} notes imported`);
      }

      successCount++;

    } catch (error) {
      console.log(`  ❌ Error processing ${file}: ${error.message}`);
      errorCount++;
    }
  }

  console.log(`\n🎉 Import completed!`);
  console.log(`✅ Successfully imported: ${successCount} territories`);
  console.log(`❌ Errors: ${errorCount} territories`);

  await prisma.$disconnect();
}

importRemainingTerritories().catch(console.error);
