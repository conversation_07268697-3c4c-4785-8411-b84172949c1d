/**
 * PIN Reset Form Component
 * 
 * Form for resetting member PINs with member selection
 * and reason tracking for audit trail.
 */

import React, { useState, useEffect } from 'react';
import { MemberProfile } from '@/lib/services/memberManagementService';

interface PinResetFormProps {
  members: MemberProfile[];
  onSubmit: (data: PinResetFormData) => void;
  onCancel: () => void;
  isLoading: boolean;
  resetResult?: { newPin: string; memberName: string } | null;
}

export interface PinResetFormData {
  memberId: string;
  reason?: string;
}

export default function PinResetForm({
  members,
  onSubmit,
  onCancel,
  isLoading,
  resetResult,
}: PinResetFormProps) {
  const [formData, setFormData] = useState<PinResetFormData>({
    memberId: '',
    reason: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showNewPin, setShowNewPin] = useState(false);

  useEffect(() => {
    if (resetResult) {
      setShowNewPin(true);
    }
  }, [resetResult]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.memberId) {
      newErrors.memberId = 'Debe seleccionar un miembro';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSubmit(formData);
  };

  const handleInputChange = (field: keyof PinResetFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleNewReset = () => {
    setFormData({
      memberId: '',
      reason: '',
    });
    setShowNewPin(false);
    setErrors({});
  };

  const selectedMember = members.find(m => m.id === formData.memberId);

  if (resetResult && showNewPin) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          PIN Restablecido Exitosamente
        </h2>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center mb-3">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                PIN restablecido para {resetResult.memberName}
              </h3>
            </div>
          </div>

          <div className="bg-white border border-green-300 rounded-md p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-900 mb-2">Nuevo PIN:</p>
                <p className="text-2xl font-mono font-bold text-gray-900 tracking-wider">
                  {resetResult.newPin}
                </p>
              </div>
              <button
                onClick={() => navigator.clipboard.writeText(resetResult.newPin)}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium"
              >
                Copiar
              </button>
            </div>
          </div>

          <div className="mt-4">
            <p className="text-sm text-green-700">
              <strong>Importante:</strong> Guarde este PIN de forma segura y entréguelo al miembro.
              Este PIN no se mostrará nuevamente por razones de seguridad.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            onClick={handleNewReset}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Restablecer Otro PIN
          </button>
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          >
            Cerrar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        Restablecer PIN de Miembro
      </h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Member Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Seleccionar Miembro *
          </label>
          <select
            value={formData.memberId}
            onChange={(e) => handleInputChange('memberId', e.target.value)}
            className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.memberId ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Seleccione un miembro...</option>
            {members
              .filter(member => member.isActive)
              .sort((a, b) => a.name.localeCompare(b.name))
              .map(member => (
                <option key={member.id} value={member.id}>
                  {member.name} ({member.role === 'publisher' ? 'Publicador' : 
                   member.role === 'ministerial_servant' ? 'Siervo Ministerial' :
                   member.role === 'elder' ? 'Anciano' : 
                   member.role === 'overseer_coordinator' ? 'Superintendente/Coordinador' : member.role})
                </option>
              ))}
          </select>
          {errors.memberId && (
            <p className="text-red-600 text-sm mt-1">{errors.memberId}</p>
          )}
        </div>

        {/* Selected Member Info */}
        {selectedMember && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-900 mb-2">Miembro Seleccionado:</h3>
            <div className="space-y-1 text-sm text-blue-800">
              <p><strong>Nombre:</strong> {selectedMember.name}</p>
              <p><strong>Email:</strong> {selectedMember.email}</p>
              <p><strong>Rol:</strong> {
                selectedMember.role === 'publisher' ? 'Publicador' : 
                selectedMember.role === 'ministerial_servant' ? 'Siervo Ministerial' :
                selectedMember.role === 'elder' ? 'Anciano' : 
                selectedMember.role === 'overseer_coordinator' ? 'Superintendente/Coordinador' : selectedMember.role
              }</p>
              <p><strong>Estado:</strong> {selectedMember.isActive ? 'Activo' : 'Inactivo'}</p>
            </div>
          </div>
        )}

        {/* Reason */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Razón del Restablecimiento (Opcional)
          </label>
          <textarea
            value={formData.reason}
            onChange={(e) => handleInputChange('reason', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
            placeholder="Describe la razón del restablecimiento del PIN..."
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            Esta información se guardará en el historial de cambios de PIN
          </p>
        </div>

        {/* Warning */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Advertencia de Seguridad
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>Se generará un nuevo PIN automáticamente</li>
                  <li>El PIN anterior quedará invalidado inmediatamente</li>
                  <li>Esta acción se registrará en el historial de auditoría</li>
                  <li>Debe entregar el nuevo PIN al miembro de forma segura</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors"
            disabled={isLoading || !formData.memberId}
          >
            {isLoading ? 'Restableciendo...' : 'Restablecer PIN'}
          </button>
        </div>
      </form>
    </div>
  );
}
