/**
 * Import Audit Service
 * 
 * Comprehensive logging and audit trail service for meeting import operations.
 * Tracks all import activities, errors, and provides detailed reporting.
 */

import { prisma } from '@/lib/prisma';

export interface ImportAuditLog {
  id: string;
  congregationId: string;
  userId: string;
  operationType: 'workbook_import' | 'manual_import' | 'data_validation';
  status: 'started' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  configuration: any;
  results: any;
  errors: string[];
  warnings: string[];
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface ImportStatistics {
  totalImports: number;
  successfulImports: number;
  failedImports: number;
  successRate: number;
  averageDuration: number;
  totalMeetingsImported: number;
  totalSongsValidated: number;
  commonErrors: { error: string; count: number }[];
  recentActivity: ImportAuditLog[];
}

export interface ConnectivityIssue {
  timestamp: Date;
  url: string;
  error: string;
  httpStatus?: number;
  responseTime?: number;
  retryAttempt: number;
}

export class ImportAuditService {
  /**
   * Start a new import operation audit log
   */
  static async startImportOperation(
    congregationId: string,
    userId: string,
    operationType: 'workbook_import' | 'manual_import' | 'data_validation',
    configuration: any
  ): Promise<string> {
    try {
      const auditLog = await prisma.importAuditLog.create({
        data: {
          congregationId,
          userId,
          operationType,
          status: 'started',
          startTime: new Date(),
          configuration: JSON.stringify(configuration),
          results: '{}',
          errors: '[]',
          warnings: '[]',
          metadata: '{}',
        },
      });

      return auditLog.id;
    } catch (error) {
      console.error('Failed to start import audit log:', error);
      throw new Error('Failed to initialize import audit log');
    }
  }

  /**
   * Update import operation with progress or completion
   */
  static async updateImportOperation(
    auditId: string,
    status: 'completed' | 'failed' | 'cancelled',
    results: any,
    errors: string[] = [],
    warnings: string[] = [],
    metadata: any = {}
  ): Promise<void> {
    try {
      const endTime = new Date();
      
      // Get start time to calculate duration
      const existingLog = await prisma.importAuditLog.findUnique({
        where: { id: auditId },
        select: { startTime: true },
      });

      const duration = existingLog 
        ? endTime.getTime() - existingLog.startTime.getTime()
        : 0;

      await prisma.importAuditLog.update({
        where: { id: auditId },
        data: {
          status,
          endTime,
          duration,
          results: JSON.stringify(results),
          errors: JSON.stringify(errors),
          warnings: JSON.stringify(warnings),
          metadata: JSON.stringify(metadata),
        },
      });
    } catch (error) {
      console.error('Failed to update import audit log:', error);
      // Don't throw here to avoid breaking the main import process
    }
  }

  /**
   * Log connectivity issues with JW.org
   */
  static async logConnectivityIssue(
    congregationId: string,
    issue: ConnectivityIssue
  ): Promise<void> {
    try {
      await prisma.connectivityIssue.create({
        data: {
          congregationId,
          timestamp: issue.timestamp,
          url: issue.url,
          error: issue.error,
          httpStatus: issue.httpStatus,
          responseTime: issue.responseTime,
          retryAttempt: issue.retryAttempt,
        },
      });
    } catch (error) {
      console.error('Failed to log connectivity issue:', error);
    }
  }

  /**
   * Get import statistics for a congregation
   */
  static async getImportStatistics(
    congregationId: string,
    days: number = 30
  ): Promise<ImportStatistics> {
    try {
      const since = new Date();
      since.setDate(since.getDate() - days);

      const logs = await prisma.importAuditLog.findMany({
        where: {
          congregationId,
          createdAt: { gte: since },
        },
        orderBy: { createdAt: 'desc' },
      });

      const totalImports = logs.length;
      const successfulImports = logs.filter(log => log.status === 'completed').length;
      const failedImports = logs.filter(log => log.status === 'failed').length;
      const successRate = totalImports > 0 ? (successfulImports / totalImports) * 100 : 0;

      // Calculate average duration
      const completedLogs = logs.filter(log => log.duration !== null);
      const averageDuration = completedLogs.length > 0
        ? completedLogs.reduce((sum, log) => sum + (log.duration || 0), 0) / completedLogs.length
        : 0;

      // Count total meetings imported
      let totalMeetingsImported = 0;
      let totalSongsValidated = 0;

      for (const log of logs) {
        try {
          const results = JSON.parse(log.results || '{}');
          totalMeetingsImported += results.imported || 0;
          
          if (results.songValidation) {
            totalSongsValidated += results.songValidation.totalSongs || 0;
          }
        } catch (error) {
          // Skip invalid JSON
        }
      }

      // Analyze common errors
      const errorCounts = new Map<string, number>();
      for (const log of logs) {
        try {
          const errors = JSON.parse(log.errors || '[]');
          for (const error of errors) {
            const count = errorCounts.get(error) || 0;
            errorCounts.set(error, count + 1);
          }
        } catch (error) {
          // Skip invalid JSON
        }
      }

      const commonErrors = Array.from(errorCounts.entries())
        .map(([error, count]) => ({ error, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        totalImports,
        successfulImports,
        failedImports,
        successRate,
        averageDuration,
        totalMeetingsImported,
        totalSongsValidated,
        commonErrors,
        recentActivity: logs.slice(0, 10).map(log => ({
          ...log,
          configuration: JSON.parse(log.configuration || '{}'),
          results: JSON.parse(log.results || '{}'),
          errors: JSON.parse(log.errors || '[]'),
          warnings: JSON.parse(log.warnings || '[]'),
          metadata: JSON.parse(log.metadata || '{}'),
        })),
      };
    } catch (error) {
      console.error('Failed to get import statistics:', error);
      return {
        totalImports: 0,
        successfulImports: 0,
        failedImports: 0,
        successRate: 0,
        averageDuration: 0,
        totalMeetingsImported: 0,
        totalSongsValidated: 0,
        commonErrors: [],
        recentActivity: [],
      };
    }
  }

  /**
   * Get detailed error report for troubleshooting
   */
  static async getErrorReport(
    congregationId: string,
    days: number = 7
  ): Promise<{
    connectivityIssues: ConnectivityIssue[];
    importErrors: { log: ImportAuditLog; errorDetails: string[] }[];
    recommendations: string[];
  }> {
    try {
      const since = new Date();
      since.setDate(since.getDate() - days);

      // Get connectivity issues
      const connectivityIssues = await prisma.connectivityIssue.findMany({
        where: {
          congregationId,
          timestamp: { gte: since },
        },
        orderBy: { timestamp: 'desc' },
        take: 50,
      });

      // Get failed imports
      const failedLogs = await prisma.importAuditLog.findMany({
        where: {
          congregationId,
          status: 'failed',
          createdAt: { gte: since },
        },
        orderBy: { createdAt: 'desc' },
        take: 20,
      });

      const importErrors = failedLogs.map(log => ({
        log: {
          ...log,
          configuration: JSON.parse(log.configuration || '{}'),
          results: JSON.parse(log.results || '{}'),
          errors: JSON.parse(log.errors || '[]'),
          warnings: JSON.parse(log.warnings || '[]'),
          metadata: JSON.parse(log.metadata || '{}'),
        },
        errorDetails: JSON.parse(log.errors || '[]'),
      }));

      // Generate recommendations
      const recommendations = this.generateTroubleshootingRecommendations(
        connectivityIssues,
        importErrors
      );

      return {
        connectivityIssues,
        importErrors,
        recommendations,
      };
    } catch (error) {
      console.error('Failed to get error report:', error);
      return {
        connectivityIssues: [],
        importErrors: [],
        recommendations: ['Error generating report. Please contact support.'],
      };
    }
  }

  /**
   * Generate troubleshooting recommendations based on error patterns
   */
  private static generateTroubleshootingRecommendations(
    connectivityIssues: ConnectivityIssue[],
    importErrors: any[]
  ): string[] {
    const recommendations: string[] = [];

    // Analyze connectivity issues
    if (connectivityIssues.length > 5) {
      recommendations.push(
        'Multiple connectivity issues detected. Check internet connection and firewall settings.'
      );
    }

    const timeoutErrors = connectivityIssues.filter(issue => 
      issue.error.toLowerCase().includes('timeout')
    );
    if (timeoutErrors.length > 2) {
      recommendations.push(
        'Timeout errors detected. Consider increasing timeout settings or checking network speed.'
      );
    }

    // Analyze import errors
    const songErrors = importErrors.filter(error =>
      error.errorDetails.some((detail: string) => detail.toLowerCase().includes('song'))
    );
    if (songErrors.length > 0) {
      recommendations.push(
        'Song validation errors detected. Update the songs database with missing songs.'
      );
    }

    const validationErrors = importErrors.filter(error =>
      error.errorDetails.some((detail: string) => detail.toLowerCase().includes('validation'))
    );
    if (validationErrors.length > 0) {
      recommendations.push(
        'Data validation errors detected. Check the format of imported data.'
      );
    }

    if (recommendations.length === 0) {
      recommendations.push(
        'No specific issues detected. If problems persist, contact technical support.'
      );
    }

    return recommendations;
  }

  /**
   * Clean up old audit logs (keep last 90 days)
   */
  static async cleanupOldLogs(congregationId: string): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 90);

      const result = await prisma.importAuditLog.deleteMany({
        where: {
          congregationId,
          createdAt: { lt: cutoffDate },
        },
      });

      return result.count;
    } catch (error) {
      console.error('Failed to cleanup old audit logs:', error);
      return 0;
    }
  }
}
