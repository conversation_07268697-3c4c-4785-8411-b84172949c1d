const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixUserPermissions() {
  try {
    console.log('Checking and fixing user permissions...');

    // Get all members for congregation 1441
    const members = await prisma.member.findMany({
      where: {
        congregationId: '1441'
      },
      select: {
        id: true,
        name: true,
        role: true,
        isActive: true
      }
    });

    console.log('\nCurrent members in congregation 1441:');
    members.forEach(member => {
      console.log(`- ${member.name}: ${member.role} (Active: ${member.isActive})`);
    });

    // Check if there are any users with admin permissions
    const adminUsers = members.filter(m => 
      ['elder', 'coordinator', 'overseer_coordinator', 'ministerial_servant', 'developer'].includes(m.role)
    );

    console.log('\nUsers with admin permissions:');
    if (adminUsers.length === 0) {
      console.log('No users with admin permissions found!');
      
      // Find the first active user and make them an elder
      const firstUser = members.find(m => m.isActive);
      if (firstUser) {
        await prisma.member.update({
          where: { id: firstUser.id },
          data: { role: 'elder' }
        });
        console.log(`✅ Updated ${firstUser.name} to Elder role for admin access`);
      } else {
        console.log('❌ No active users found to update');
      }
    } else {
      console.log('✅ Found users with admin permissions:');
      adminUsers.forEach(user => {
        console.log(`  - ${user.name}: ${user.role}`);
      });
    }

    // Also create a developer user if none exists
    const devUser = members.find(m => m.role === 'developer');
    if (!devUser) {
      console.log('\nCreating developer user for testing...');
      const newDevUser = await prisma.member.create({
        data: {
          congregationId: '1441',
          name: 'Developer Admin',
          role: 'developer',
          pin: '1234',
          isActive: true
        }
      });
      console.log(`✅ Created developer user: ${newDevUser.name} (ID: ${newDevUser.id})`);
    }

    console.log('\n✅ User permissions have been updated successfully!');
    console.log('You can now access admin features with proper permissions.');

  } catch (error) {
    console.error('❌ Error fixing user permissions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixUserPermissions();
