#!/usr/bin/env node

/**
 * Debug Territory Visibility Issue
 * 
 * This script checks the actual data being returned by the API
 * to understand why territory numbers are not visible.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Test the API endpoint that AssignedTerritories component uses
 */
async function testAssignedTerritoriesAPI() {
  try {
    console.log('🧪 Testing Assigned Territories API');
    console.log('==================================\n');

    // Get authentication token
    const loginResponse = await fetch('http://localhost:3001/api/auth/congregation-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: 'coral2024'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Failed to get authentication token');
      console.log('Response status:', loginResponse.status);
      const errorText = await loginResponse.text();
      console.log('Error:', errorText);
      return false;
    }

    const { token } = await loginResponse.json();
    console.log('✅ Authentication token obtained');

    // Test the assignments endpoint that AssignedTerritories uses
    console.log('\n📋 Testing /api/territories/assignments?type=members:');
    const assignmentsResponse = await fetch('http://localhost:3001/api/territories/assignments?type=members', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!assignmentsResponse.ok) {
      console.log('❌ Assignments API failed');
      console.log('Response status:', assignmentsResponse.status);
      const errorText = await assignmentsResponse.text();
      console.log('Error:', errorText);
      return false;
    }

    const assignmentsData = await assignmentsResponse.json();
    console.log('✅ Assignments API working');
    console.log(`   Members with assignments: ${assignmentsData.length}`);

    // Examine the data structure
    if (assignmentsData.length > 0) {
      console.log('\n🔍 Data Structure Analysis:');
      assignmentsData.forEach((member, index) => {
        console.log(`\n   Member ${index + 1}: ${member.name}`);
        console.log(`   Role: ${member.role}`);
        console.log(`   Total assignments: ${member.totalAssignments}`);
        console.log(`   Assignments array length: ${member.assignments?.length || 0}`);
        
        if (member.assignments && member.assignments.length > 0) {
          console.log('   Territory details:');
          member.assignments.forEach((territory, tIndex) => {
            console.log(`     ${tIndex + 1}. ID: ${territory.id}`);
            console.log(`        Territory Number: "${territory.territoryNumber}" (type: ${typeof territory.territoryNumber})`);
            console.log(`        Address: "${territory.address}"`);
            console.log(`        Assigned At: ${territory.assignedAt}`);
            console.log(`        Days Assigned: ${territory.daysAssigned}`);
          });
        }
      });

      // Check for potential issues
      console.log('\n⚠️  Potential Issues Check:');
      let hasEmptyNumbers = false;
      let hasUndefinedNumbers = false;
      let hasNullNumbers = false;

      assignmentsData.forEach(member => {
        if (member.assignments) {
          member.assignments.forEach(territory => {
            if (territory.territoryNumber === '') {
              hasEmptyNumbers = true;
            }
            if (territory.territoryNumber === undefined) {
              hasUndefinedNumbers = true;
            }
            if (territory.territoryNumber === null) {
              hasNullNumbers = true;
            }
          });
        }
      });

      console.log(`   Empty territory numbers: ${hasEmptyNumbers ? 'YES' : 'NO'}`);
      console.log(`   Undefined territory numbers: ${hasUndefinedNumbers ? 'YES' : 'NO'}`);
      console.log(`   Null territory numbers: ${hasNullNumbers ? 'YES' : 'NO'}`);
    }

    return true;

  } catch (error) {
    console.error('❌ Error testing API:', error);
    return false;
  }
}

/**
 * Test direct database query to compare with API
 */
async function testDirectDatabaseQuery() {
  try {
    console.log('\n🧪 Testing Direct Database Query');
    console.log('=================================\n');

    // Get members with assigned territories directly from database
    const membersWithAssignments = await prisma.member.findMany({
      where: {
        congregationId: '1441',
        territoryAssignments: {
          some: {
            status: 'active'
          }
        }
      },
      include: {
        territoryAssignments: {
          where: {
            status: 'active'
          },
          include: {
            territory: {
              select: {
                id: true,
                territoryNumber: true,
                address: true
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`👥 Members with assignments (direct DB): ${membersWithAssignments.length}`);

    if (membersWithAssignments.length > 0) {
      console.log('\n📋 Direct Database Results:');
      membersWithAssignments.forEach((member, index) => {
        console.log(`\n   Member ${index + 1}: ${member.name}`);
        console.log(`   Role: ${member.role}`);
        console.log(`   Territory assignments: ${member.territoryAssignments.length}`);
        
        if (member.territoryAssignments.length > 0) {
          console.log('   Territory details:');
          member.territoryAssignments.forEach((assignment, tIndex) => {
            console.log(`     ${tIndex + 1}. Assignment ID: ${assignment.id}`);
            console.log(`        Territory ID: ${assignment.territory.id}`);
            console.log(`        Territory Number: "${assignment.territory.territoryNumber}" (type: ${typeof assignment.territory.territoryNumber})`);
            console.log(`        Address: "${assignment.territory.address}"`);
            console.log(`        Assigned At: ${assignment.assignedAt}`);
          });
        }
      });
    }

    return true;

  } catch (error) {
    console.error('❌ Error testing database:', error);
    return false;
  }
}

/**
 * Test territory data integrity
 */
async function testTerritoryDataIntegrity() {
  try {
    console.log('\n🧪 Testing Territory Data Integrity');
    console.log('===================================\n');

    // Check all territories for data integrity
    const allTerritories = await prisma.territory.findMany({
      where: {
        congregationId: '1441'
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        status: true
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });

    console.log(`📊 Total territories: ${allTerritories.length}`);

    // Check for data issues
    const emptyNumbers = allTerritories.filter(t => t.territoryNumber === '');
    const nullNumbers = allTerritories.filter(t => t.territoryNumber === null);
    const undefinedNumbers = allTerritories.filter(t => t.territoryNumber === undefined);

    console.log(`   Empty territory numbers: ${emptyNumbers.length}`);
    console.log(`   Null territory numbers: ${nullNumbers.length}`);
    console.log(`   Undefined territory numbers: ${undefinedNumbers.length}`);

    if (emptyNumbers.length > 0) {
      console.log('\n   Territories with empty numbers:');
      emptyNumbers.forEach(t => console.log(`     ID: ${t.id}, Address: ${t.address}`));
    }

    // Show sample of valid territories
    const validTerritories = allTerritories.filter(t => t.territoryNumber && t.territoryNumber.trim() !== '');
    console.log(`\n   Valid territories: ${validTerritories.length}`);
    if (validTerritories.length > 0) {
      console.log('   Sample valid territory numbers:');
      validTerritories.slice(0, 10).forEach(t => {
        console.log(`     "${t.territoryNumber}" (${t.territoryNumber.length} chars) - ${t.address.substring(0, 50)}...`);
      });
    }

    return true;

  } catch (error) {
    console.error('❌ Error testing territory data:', error);
    return false;
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🧪 Territory Visibility Debug Test');
  console.log('==================================\n');

  try {
    const tests = [
      { name: 'Territory Data Integrity', test: testTerritoryDataIntegrity },
      { name: 'Direct Database Query', test: testDirectDatabaseQuery },
      { name: 'Assigned Territories API', test: testAssignedTerritoriesAPI }
    ];

    let passed = 0;
    let total = tests.length;

    for (const { name, test } of tests) {
      try {
        const result = await test();
        if (result) {
          passed++;
          console.log(`\n✅ ${name} test: PASSED`);
        } else {
          console.log(`\n❌ ${name} test: FAILED`);
        }
      } catch (error) {
        console.log(`\n❌ ${name} test: ERROR - ${error.message}`);
      }
    }

    console.log('\n📊 Debug Results:');
    console.log('=================');
    console.log(`Passed: ${passed}/${total}`);
    console.log(`Status: ${passed === total ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    if (passed === total) {
      console.log('\n🎯 Territory visibility debugging complete!');
      console.log('✅ Data integrity verified');
      console.log('✅ API endpoints working');
      console.log('✅ Database queries successful');
    } else {
      console.log('\n⚠️  Issues found that may affect territory number visibility');
    }

  } catch (error) {
    console.error('❌ Debug error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testAssignedTerritoriesAPI,
  testDirectDatabaseQuery,
  testTerritoryDataIntegrity
};
