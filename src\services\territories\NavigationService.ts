import type { Territory, Coordinates } from '@/types/territories/map';

/**
 * Navigation Service
 * Handles platform-specific navigation app integration for territory directions
 */
export class NavigationService {
  
  /**
   * Detect the current platform
   */
  static detectPlatform(): 'ios' | 'android' | 'web' {
    if (typeof window === 'undefined') return 'web';
    
    const userAgent = navigator.userAgent;
    
    if (/iPad|iPhone|iPod/.test(userAgent)) {
      return 'ios';
    } else if (/Android/.test(userAgent)) {
      return 'android';
    } else {
      return 'web';
    }
  }

  /**
   * Check if device supports native navigation apps
   */
  static supportsNativeNavigation(): boolean {
    const platform = this.detectPlatform();
    return platform === 'ios' || platform === 'android';
  }

  /**
   * Generate navigation URL for the current platform
   */
  static getNavigationUrl(address: string, coordinates?: Coordinates): string {
    const platform = this.detectPlatform();
    const encodedAddress = encodeURIComponent(address);
    
    switch (platform) {
      case 'ios':
        // Apple Maps URL scheme
        if (coordinates) {
          return `maps://maps.apple.com/?q=${encodedAddress}&ll=${coordinates.latitude},${coordinates.longitude}`;
        } else {
          return `maps://maps.apple.com/?q=${encodedAddress}`;
        }
        
      case 'android':
        // Google Maps intent for Android
        if (coordinates) {
          return `geo:${coordinates.latitude},${coordinates.longitude}?q=${encodedAddress}`;
        } else {
          return `geo:0,0?q=${encodedAddress}`;
        }
        
      case 'web':
      default:
        // Google Maps web URL
        if (coordinates) {
          return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}&center=${coordinates.latitude},${coordinates.longitude}`;
        } else {
          return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
        }
    }
  }

  /**
   * Get multiple navigation options for fallback
   */
  static getNavigationOptions(address: string, coordinates?: Coordinates): Array<{
    name: string;
    url: string;
    icon: string;
    primary: boolean;
  }> {
    const platform = this.detectPlatform();
    const encodedAddress = encodeURIComponent(address);
    const options = [];

    // Primary option based on platform
    if (platform === 'ios') {
      options.push({
        name: 'Apple Maps',
        url: this.getNavigationUrl(address, coordinates),
        icon: '🗺️',
        primary: true
      });
    } else if (platform === 'android') {
      options.push({
        name: 'Google Maps',
        url: this.getNavigationUrl(address, coordinates),
        icon: '🗺️',
        primary: true
      });
    } else {
      options.push({
        name: 'Google Maps',
        url: this.getNavigationUrl(address, coordinates),
        icon: '🗺️',
        primary: true
      });
    }

    // Alternative options
    if (coordinates) {
      // Waze (works on all platforms)
      options.push({
        name: 'Waze',
        url: `https://waze.com/ul?ll=${coordinates.latitude},${coordinates.longitude}&navigate=yes&q=${encodedAddress}`,
        icon: '🚗',
        primary: false
      });

      // Google Maps (as alternative for iOS)
      if (platform === 'ios') {
        options.push({
          name: 'Google Maps',
          url: `https://www.google.com/maps/search/?api=1&query=${encodedAddress}&center=${coordinates.latitude},${coordinates.longitude}`,
          icon: '🌐',
          primary: false
        });
      }

      // Apple Maps (as alternative for Android/Web)
      if (platform !== 'ios') {
        options.push({
          name: 'Apple Maps',
          url: `https://maps.apple.com/?q=${encodedAddress}&ll=${coordinates.latitude},${coordinates.longitude}`,
          icon: '🍎',
          primary: false
        });
      }
    }

    return options;
  }

  /**
   * Open navigation for a territory
   */
  static async openNavigation(territory: Territory): Promise<boolean> {
    try {
      const url = this.getNavigationUrl(territory.address, territory.coordinates);
      
      // Try to open in native app first
      if (this.supportsNativeNavigation()) {
        window.location.href = url;
        return true;
      } else {
        // Open in new tab for web
        window.open(url, '_blank', 'noopener,noreferrer');
        return true;
      }
    } catch (error) {
      console.error('Failed to open navigation:', error);
      return false;
    }
  }

  /**
   * Copy address to clipboard as fallback
   */
  static async copyAddressToClipboard(address: string): Promise<boolean> {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(address);
        return true;
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = address;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        const result = document.execCommand('copy');
        document.body.removeChild(textArea);
        return result;
      }
    } catch (error) {
      console.error('Failed to copy address to clipboard:', error);
      return false;
    }
  }

  /**
   * Show navigation options modal/menu
   */
  static showNavigationOptions(
    territory: Territory, 
    onSelect: (url: string, name: string) => void
  ): void {
    const options = this.getNavigationOptions(territory.address, territory.coordinates);
    
    // Create a simple modal for navigation options
    const modal = document.createElement('div');
    modal.className = 'navigation-options-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      font-family: system-ui, sans-serif;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      border-radius: 12px;
      padding: 24px;
      max-width: 320px;
      width: 90%;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    `;

    content.innerHTML = `
      <h3 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #1f2937;">
        Abrir Direcciones
      </h3>
      <p style="margin: 0 0 16px 0; font-size: 14px; color: #6b7280;">
        Territorio ${territory.territoryNumber}: ${territory.address}
      </p>
      <div class="navigation-options" style="display: flex; flex-direction: column; gap: 8px;">
        ${options.map(option => `
          <button 
            data-url="${option.url}" 
            data-name="${option.name}"
            style="
              display: flex;
              align-items: center;
              gap: 12px;
              padding: 12px;
              border: 1px solid ${option.primary ? '#2563eb' : '#d1d5db'};
              border-radius: 8px;
              background: ${option.primary ? '#eff6ff' : 'white'};
              color: ${option.primary ? '#1e40af' : '#374151'};
              font-size: 14px;
              font-weight: ${option.primary ? '500' : '400'};
              cursor: pointer;
              transition: all 0.2s;
            "
            onmouseover="this.style.backgroundColor='${option.primary ? '#dbeafe' : '#f9fafb'}'"
            onmouseout="this.style.backgroundColor='${option.primary ? '#eff6ff' : 'white'}'"
          >
            <span style="font-size: 18px;">${option.icon}</span>
            <span>${option.name}</span>
            ${option.primary ? '<span style="margin-left: auto; font-size: 12px; color: #2563eb;">Recomendado</span>' : ''}
          </button>
        `).join('')}
      </div>
      <div style="margin-top: 16px; display: flex; gap: 8px;">
        <button 
          id="copy-address-btn"
          style="
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 13px;
            cursor: pointer;
          "
        >
          📋 Copiar Dirección
        </button>
        <button 
          id="cancel-btn"
          style="
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 13px;
            cursor: pointer;
          "
        >
          Cancelar
        </button>
      </div>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    // Add event listeners
    content.querySelectorAll('[data-url]').forEach(button => {
      button.addEventListener('click', () => {
        const url = button.getAttribute('data-url')!;
        const name = button.getAttribute('data-name')!;
        onSelect(url, name);
        document.body.removeChild(modal);
      });
    });

    content.querySelector('#copy-address-btn')?.addEventListener('click', async () => {
      const success = await this.copyAddressToClipboard(territory.address);
      if (success) {
        alert('Dirección copiada al portapapeles');
      } else {
        alert('No se pudo copiar la dirección');
      }
      document.body.removeChild(modal);
    });

    content.querySelector('#cancel-btn')?.addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    // Close on backdrop click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }

  /**
   * Get estimated travel time (placeholder for future implementation)
   */
  static async getEstimatedTravelTime(
    from: Coordinates, 
    to: Coordinates
  ): Promise<{ duration: number; distance: number } | null> {
    // TODO: Implement with a routing service like Google Directions API
    // For now, return null to indicate feature not available
    return null;
  }

  /**
   * Validate coordinates for navigation
   */
  static validateCoordinates(coordinates?: Coordinates): boolean {
    if (!coordinates) return false;
    
    return (
      typeof coordinates.latitude === 'number' &&
      typeof coordinates.longitude === 'number' &&
      coordinates.latitude >= -90 &&
      coordinates.latitude <= 90 &&
      coordinates.longitude >= -180 &&
      coordinates.longitude <= 180 &&
      !isNaN(coordinates.latitude) &&
      !isNaN(coordinates.longitude)
    );
  }
}

export default NavigationService;
