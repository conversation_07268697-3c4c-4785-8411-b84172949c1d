/**
 * Event Management Service
 *
 * Comprehensive service for managing congregation events, activities, and special occasions.
 * Handles event creation, scheduling, coordination, and member participation.
 */

import { prisma } from '@/lib/prisma';
import { Event } from '@prisma/client';

// Event interfaces
export interface EventData {
  id: string;
  congregationId: string;
  title: string;
  description?: string;
  eventDate: Date;
  startTime?: string;
  endTime?: string;
  location?: string;
  category: EventCategory;
  isAllDay: boolean;
  isRecurring: boolean;
  recurrenceRule?: string;
  visibility: EventVisibility;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateEventData {
  title: string;
  description?: string;
  eventDate: Date;
  startTime?: string;
  endTime?: string;
  location?: string;
  category: EventCategory;
  isAllDay?: boolean;
  isRecurring?: boolean;
  recurrenceRule?: string;
  visibility?: EventVisibility;
}

export interface UpdateEventData {
  title?: string;
  description?: string;
  eventDate?: Date;
  startTime?: string;
  endTime?: string;
  location?: string;
  category?: EventCategory;
  isAllDay?: boolean;
  isRecurring?: boolean;
  recurrenceRule?: string;
  visibility?: EventVisibility;
  isActive?: boolean;
}

export interface EventFilters {
  category?: EventCategory;
  visibility?: EventVisibility;
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
  search?: string;
}

export interface EventSummary {
  totalEvents: number;
  upcomingEvents: number;
  eventsByCategory: Record<EventCategory, number>;
  nextEvent?: EventData;
}

// Event enums
export enum EventCategory {
  ASSEMBLY = 'ASSEMBLY',
  CONVENTION = 'CONVENTION',
  SPECIAL_MEETING = 'SPECIAL_MEETING',
  SERVICE_ACTIVITY = 'SERVICE_ACTIVITY',
  SOCIAL_ACTIVITY = 'SOCIAL_ACTIVITY',
  TRAINING = 'TRAINING',
  OTHER = 'OTHER'
}

export enum EventVisibility {
  ALL_MEMBERS = 'ALL_MEMBERS',
  ELDERS_ONLY = 'ELDERS_ONLY',
  MINISTERIAL_SERVANTS = 'MINISTERIAL_SERVANTS',
  PUBLISHERS_ONLY = 'PUBLISHERS_ONLY',
  SPECIFIC_GROUP = 'SPECIFIC_GROUP'
}

// Event category labels in Spanish
export const EVENT_CATEGORY_LABELS: Record<EventCategory, string> = {
  [EventCategory.ASSEMBLY]: 'Asamblea',
  [EventCategory.CONVENTION]: 'Convención',
  [EventCategory.SPECIAL_MEETING]: 'Reunión Especial',
  [EventCategory.SERVICE_ACTIVITY]: 'Actividad de Servicio',
  [EventCategory.SOCIAL_ACTIVITY]: 'Actividad Social',
  [EventCategory.TRAINING]: 'Capacitación',
  [EventCategory.OTHER]: 'Otro'
};

export const EVENT_VISIBILITY_LABELS: Record<EventVisibility, string> = {
  [EventVisibility.ALL_MEMBERS]: 'Todos los Hermanos',
  [EventVisibility.ELDERS_ONLY]: 'Solo Ancianos',
  [EventVisibility.MINISTERIAL_SERVANTS]: 'Siervos Ministeriales',
  [EventVisibility.PUBLISHERS_ONLY]: 'Solo Publicadores',
  [EventVisibility.SPECIFIC_GROUP]: 'Grupo Específico'
};

export class EventManagementService {
  /**
   * Create a new event
   */
  static async createEvent(
    congregationId: string,
    eventData: CreateEventData
  ): Promise<EventData> {
    try {
      // Validate event data
      this.validateEventData(eventData);

      // Check for scheduling conflicts
      await this.checkSchedulingConflicts(congregationId, eventData.eventDate, eventData.startTime, eventData.endTime);

      const event = await prisma.event.create({
        data: {
          congregationId,
          title: eventData.title,
          description: eventData.description,
          eventDate: eventData.eventDate,
          startTime: eventData.startTime,
          endTime: eventData.endTime,
          location: eventData.location,
          category: eventData.category,
          isAllDay: eventData.isAllDay || false,
          isRecurring: eventData.isRecurring || false,
          recurrenceRule: eventData.recurrenceRule,
          visibility: eventData.visibility || EventVisibility.ALL_MEMBERS,
        },
      });

      return this.mapEventToEventData(event);
    } catch (error) {
      console.error('Error creating event:', error);
      throw new Error('Failed to create event');
    }
  }

  /**
   * Get events for a congregation
   */
  static async getEvents(
    congregationId: string,
    filters: EventFilters = {},
    limit: number = 50,
    offset: number = 0
  ): Promise<EventData[]> {
    try {
      const where: {
        congregationId: string;
        isActive: boolean;
        category?: EventCategory;
        visibility?: EventVisibility;
        eventDate?: { gte?: Date; lte?: Date };
        OR?: Array<{
          title?: { contains: string; mode: 'insensitive' };
          description?: { contains: string; mode: 'insensitive' };
          location?: { contains: string; mode: 'insensitive' };
        }>;
      } = {
        congregationId,
        isActive: filters.isActive !== undefined ? filters.isActive : true,
      };

      // Apply filters
      if (filters.category) {
        where.category = filters.category;
      }

      if (filters.visibility) {
        where.visibility = filters.visibility;
      }

      if (filters.startDate || filters.endDate) {
        where.eventDate = {};
        if (filters.startDate) {
          where.eventDate.gte = filters.startDate;
        }
        if (filters.endDate) {
          where.eventDate.lte = filters.endDate;
        }
      }

      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          { location: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      const events = await prisma.event.findMany({
        where,
        orderBy: { eventDate: 'asc' },
        take: limit,
        skip: offset,
      });

      return events.map(this.mapEventToEventData);
    } catch (error) {
      console.error('Error fetching events:', error);
      throw new Error('Failed to fetch events');
    }
  }

  /**
   * Get upcoming events for a congregation
   */
  static async getUpcomingEvents(
    congregationId: string,
    limit: number = 10
  ): Promise<EventData[]> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const events = await prisma.event.findMany({
        where: {
          congregationId,
          isActive: true,
          eventDate: {
            gte: today,
          },
        },
        orderBy: { eventDate: 'asc' },
        take: limit,
      });

      return events.map(this.mapEventToEventData);
    } catch (error) {
      console.error('Error fetching upcoming events:', error);
      throw new Error('Failed to fetch upcoming events');
    }
  }

  /**
   * Get event by ID
   */
  static async getEventById(
    congregationId: string,
    eventId: string
  ): Promise<EventData | null> {
    try {
      const event = await prisma.event.findFirst({
        where: {
          id: eventId,
          congregationId,
        },
      });

      return event ? this.mapEventToEventData(event) : null;
    } catch (error) {
      console.error('Error fetching event:', error);
      throw new Error('Failed to fetch event');
    }
  }

  /**
   * Update an event
   */
  static async updateEvent(
    congregationId: string,
    eventId: string,
    updateData: UpdateEventData
  ): Promise<EventData> {
    try {
      // Validate update data
      if (updateData.eventDate || updateData.startTime || updateData.endTime) {
        await this.checkSchedulingConflicts(
          congregationId,
          updateData.eventDate,
          updateData.startTime,
          updateData.endTime,
          eventId
        );
      }

      const event = await prisma.event.update({
        where: {
          id: eventId,
          congregationId,
        },
        data: updateData,
      });

      return this.mapEventToEventData(event);
    } catch (error) {
      console.error('Error updating event:', error);
      throw new Error('Failed to update event');
    }
  }

  /**
   * Delete an event (soft delete)
   */
  static async deleteEvent(
    congregationId: string,
    eventId: string
  ): Promise<void> {
    try {
      await prisma.event.update({
        where: {
          id: eventId,
          congregationId,
        },
        data: {
          isActive: false,
        },
      });
    } catch (error) {
      console.error('Error deleting event:', error);
      throw new Error('Failed to delete event');
    }
  }

  /**
   * Get event summary for dashboard
   */
  static async getEventSummary(congregationId: string): Promise<EventSummary> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get total events
      const totalEvents = await prisma.event.count({
        where: {
          congregationId,
          isActive: true,
        },
      });

      // Get upcoming events count
      const upcomingEvents = await prisma.event.count({
        where: {
          congregationId,
          isActive: true,
          eventDate: {
            gte: today,
          },
        },
      });

      // Get events by category
      const eventsByCategory: Record<EventCategory, number> = {
        [EventCategory.ASSEMBLY]: 0,
        [EventCategory.CONVENTION]: 0,
        [EventCategory.SPECIAL_MEETING]: 0,
        [EventCategory.SERVICE_ACTIVITY]: 0,
        [EventCategory.SOCIAL_ACTIVITY]: 0,
        [EventCategory.TRAINING]: 0,
        [EventCategory.OTHER]: 0,
      };

      const categoryStats = await prisma.event.groupBy({
        by: ['category'],
        where: {
          congregationId,
          isActive: true,
        },
        _count: {
          category: true,
        },
      });

      categoryStats.forEach(stat => {
        if (stat.category in eventsByCategory) {
          eventsByCategory[stat.category as EventCategory] = stat._count.category;
        }
      });

      // Get next event
      const nextEventRecord = await prisma.event.findFirst({
        where: {
          congregationId,
          isActive: true,
          eventDate: {
            gte: today,
          },
        },
        orderBy: { eventDate: 'asc' },
      });

      const nextEvent = nextEventRecord ? this.mapEventToEventData(nextEventRecord) : undefined;

      return {
        totalEvents,
        upcomingEvents,
        eventsByCategory,
        nextEvent,
      };
    } catch (error) {
      console.error('Error getting event summary:', error);
      throw new Error('Failed to get event summary');
    }
  }

  /**
   * Validate event data
   */
  private static validateEventData(eventData: CreateEventData | UpdateEventData): void {
    if ('title' in eventData && eventData.title && eventData.title.trim().length === 0) {
      throw new Error('Event title is required');
    }

    if ('eventDate' in eventData && eventData.eventDate && eventData.eventDate < new Date()) {
      throw new Error('Event date cannot be in the past');
    }

    if ('startTime' in eventData && 'endTime' in eventData &&
        eventData.startTime && eventData.endTime &&
        eventData.startTime >= eventData.endTime) {
      throw new Error('End time must be after start time');
    }
  }

  /**
   * Check for scheduling conflicts
   */
  private static async checkSchedulingConflicts(
    congregationId: string,
    eventDate?: Date,
    startTime?: string,
    endTime?: string,
    excludeEventId?: string
  ): Promise<void> {
    if (!eventDate) return;

    const where: {
      congregationId: string;
      isActive: boolean;
      eventDate: Date;
      id?: { not: string };
    } = {
      congregationId,
      isActive: true,
      eventDate,
    };

    if (excludeEventId) {
      where.id = { not: excludeEventId };
    }

    const conflictingEvents = await prisma.event.findMany({
      where,
      select: {
        id: true,
        title: true,
        startTime: true,
        endTime: true,
        isAllDay: true,
      },
    });

    if (conflictingEvents.length > 0) {
      // Check for time conflicts if times are specified
      if (startTime && endTime) {
        const hasTimeConflict = conflictingEvents.some(event => {
          if (event.isAllDay) return true;
          if (!event.startTime || !event.endTime) return false;

          return (
            (startTime >= event.startTime && startTime < event.endTime) ||
            (endTime > event.startTime && endTime <= event.endTime) ||
            (startTime <= event.startTime && endTime >= event.endTime)
          );
        });

        if (hasTimeConflict) {
          throw new Error('Event conflicts with existing event on the same date');
        }
      } else {
        // If no specific times, warn about potential conflict
        console.warn('Potential scheduling conflict detected for event on', eventDate);
      }
    }
  }

  /**
   * Map Prisma Event to EventData
   */
  private static mapEventToEventData(event: Event): EventData {
    return {
      id: event.id,
      congregationId: event.congregationId,
      title: event.title,
      description: event.description || undefined,
      eventDate: event.eventDate,
      startTime: event.startTime || undefined,
      endTime: event.endTime || undefined,
      location: event.location || undefined,
      category: event.category as EventCategory,
      isAllDay: event.isAllDay,
      isRecurring: event.isRecurring,
      recurrenceRule: event.recurrenceRule || undefined,
      visibility: event.visibility as EventVisibility,
      isActive: event.isActive,
      createdAt: event.createdAt,
      updatedAt: event.updatedAt,
    };
  }

  /**
   * Format event date for display
   */
  static formatEventDate(date: Date): string {
    return date.toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  /**
   * Format event time for display
   */
  static formatEventTime(startTime?: string, endTime?: string): string {
    if (!startTime) return 'Todo el día';
    if (!endTime) return startTime;
    return `${startTime} - ${endTime}`;
  }

  /**
   * Get event category color
   */
  static getEventCategoryColor(category: EventCategory): string {
    const colors: Record<EventCategory, string> = {
      [EventCategory.ASSEMBLY]: 'blue',
      [EventCategory.CONVENTION]: 'purple',
      [EventCategory.SPECIAL_MEETING]: 'green',
      [EventCategory.SERVICE_ACTIVITY]: 'orange',
      [EventCategory.SOCIAL_ACTIVITY]: 'pink',
      [EventCategory.TRAINING]: 'indigo',
      [EventCategory.OTHER]: 'gray',
    };
    return colors[category] || 'gray';
  }
}
