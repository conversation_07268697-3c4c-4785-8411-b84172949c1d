const { PrismaClient } = require('@prisma/client');

async function findHermanoCandadoNotes() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Finding territories with "hermano/hermana" and "candado" notes...\n');
    
    const territories = await prisma.territory.findMany({
      where: {
        congregationId: '1441',
        notes: {
          not: null
        }
      },
      select: {
        territoryNumber: true,
        notes: true
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });
    
    let hermanoCount = 0;
    let candadoCount = 0;
    
    console.log('🏠 Territories with "hermano/hermana" notes:');
    territories.forEach(territory => {
      if (territory.notes) {
        const notes = territory.notes.toLowerCase();
        if (notes.includes('hermano') || notes.includes('hermana')) {
          hermanoCount++;
          console.log(`  Territory ${territory.territoryNumber}:`);
          
          // Show specific notes with hermano/hermana
          const noteLines = territory.notes.split('\n');
          noteLines.forEach(note => {
            if (note.toLowerCase().includes('hermano') || note.toLowerCase().includes('hermana')) {
              console.log(`    - ${note}`);
            }
          });
        }
      }
    });
    
    console.log(`\n🔒 Territories with "candado" notes:`);
    territories.forEach(territory => {
      if (territory.notes) {
        const notes = territory.notes.toLowerCase();
        if (notes.includes('candado')) {
          candadoCount++;
          console.log(`  Territory ${territory.territoryNumber}:`);
          
          // Show specific notes with candado
          const noteLines = territory.notes.split('\n');
          noteLines.forEach(note => {
            if (note.toLowerCase().includes('candado')) {
              console.log(`    - ${note}`);
            }
          });
        }
      }
    });
    
    console.log(`\n📊 Summary:`);
    console.log(`🏠 Territories with hermano/hermana notes: ${hermanoCount}`);
    console.log(`🔒 Territories with candado notes: ${candadoCount}`);
    console.log(`\n✅ These will now show as icons instead of text notes!`);
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

findHermanoCandadoNotes().catch(console.error);
