# Data Models

Based on the PRD requirements and the need to preserve the existing 41 MySQL tables while enabling multi-congregation support, here are the core data models that will be shared between frontend and backend.

## Congregation

**Purpose:** Central tenant entity that enables multi-congregation support and data isolation

**Key Attributes:**
- id: string - Unique congregation identifier (primary key)
- name: string - Display name of the congregation
- region: string - Geographic region for organization
- pin: string - Hashed congregation access PIN
- language: string - Default language (es, en, etc.)
- timezone: string - Congregation timezone for meeting scheduling
- settings: JSON - Congregation-specific configuration
- createdAt: Date - Registration timestamp
- updatedAt: Date - Last modification timestamp

### TypeScript Interface
```typescript
interface Congregation {
  id: string;
  name: string;
  region: string;
  pin: string;
  language: 'es' | 'en' | string;
  timezone: string;
  settings: {
    theme: Record<string, string>;
    meetingLocation: 'kingdom_hall' | 'zoom';
    zoomDetails?: {
      meetingId: string;
      password: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- One-to-many with Members
- One-to-many with Meetings
- One-to-many with Tasks
- One-to-many with Letters

## Member

**Purpose:** User accounts with role-based access control and congregation association

**Key Attributes:**
- id: string - Unique member identifier
- congregationId: string - Foreign key to congregation
- name: string - Full member name
- email: string - Contact email (optional)
- pin: string - Hashed personal PIN for authentication
- role: enum - Access level (publisher, ministerial_servant, elder, overseer_coordinator, developer)
- serviceGroup: string - Service group assignment
- isActive: boolean - Account status
- lastLogin: Date - Last authentication timestamp

### TypeScript Interface
```typescript
interface Member {
  id: string;
  congregationId: string;
  name: string;
  email?: string;
  pin: string;
  role: 'publisher' | 'ministerial_servant' | 'elder' | 'overseer_coordinator' | 'developer';
  serviceGroup?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- Many-to-one with Congregation
- One-to-many with FieldServiceRecords
- Many-to-many with MeetingParts (assignments)
- One-to-many with TaskAssignments

## Meeting

**Purpose:** Base entity for both midweek and weekend meetings with assignment tracking

**Key Attributes:**
- id: string - Unique meeting identifier
- congregationId: string - Tenant isolation
- type: enum - Meeting type (midweek, weekend)
- date: Date - Meeting date
- chairman: string - Meeting chairman member ID
- location: enum - Meeting location type
- zoomDetails: JSON - Virtual meeting information
- status: enum - Meeting status (scheduled, completed, cancelled)

### TypeScript Interface
```typescript
interface Meeting {
  id: string;
  congregationId: string;
  type: 'midweek' | 'weekend';
  date: Date;
  chairman?: string;
  location: 'kingdom_hall' | 'zoom';
  zoomDetails?: {
    meetingId: string;
    password: string;
    url: string;
  };
  status: 'scheduled' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- Many-to-one with Congregation
- One-to-many with MeetingParts
- Many-to-one with Member (chairman)

## MeetingPart

**Purpose:** Individual meeting segments with member assignments and JW.org integration

**Key Attributes:**
- id: string - Unique part identifier
- meetingId: string - Parent meeting reference
- congregationId: string - Tenant isolation
- partType: string - Type of meeting part
- title: string - Part title from JW.org or custom
- duration: number - Duration in minutes
- assignedMemberId: string - Assigned member
- assistantId: string - Assistant member (if applicable)
- songNumber: number - Associated song number
- notes: string - Additional notes

### TypeScript Interface
```typescript
interface MeetingPart {
  id: string;
  meetingId: string;
  congregationId: string;
  partType: 'treasures' | 'digging' | 'living' | 'public_talk' | 'watchtower';
  title: string;
  duration: number;
  assignedMemberId?: string;
  assistantId?: string;
  songNumber?: number;
  notes?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- Many-to-one with Meeting
- Many-to-one with Congregation
- Many-to-one with Member (assigned)
- Many-to-one with Member (assistant)

## Task

**Purpose:** Congregation task definitions with assignment and tracking capabilities

**Key Attributes:**
- id: string - Unique task identifier
- congregationId: string - Tenant isolation
- title: string - Task title
- description: string - Detailed description
- category: string - Task categorization
- frequency: enum - Recurrence pattern
- isActive: boolean - Task availability
- createdBy: string - Creator member ID

### TypeScript Interface
```typescript
interface Task {
  id: string;
  congregationId: string;
  title: string;
  description: string;
  category: 'cleaning' | 'maintenance' | 'service' | 'meeting' | 'administrative';
  frequency: 'once' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- Many-to-one with Congregation
- Many-to-one with Member (creator)
- One-to-many with TaskAssignments

## FieldServiceRecord

**Purpose:** Individual service time tracking with monthly reporting capabilities

**Key Attributes:**
- id: string - Unique record identifier
- memberId: string - Member reference
- congregationId: string - Tenant isolation
- date: Date - Service date
- hours: number - Hours spent in service
- minutes: number - Additional minutes
- activityType: enum - Type of service activity
- notes: string - Optional notes
- serviceYear: number - Service year for reporting

### TypeScript Interface
```typescript
interface FieldServiceRecord {
  id: string;
  memberId: string;
  congregationId: string;
  date: Date;
  hours: number;
  minutes: number;
  activityType: 'field_service' | 'return_visit' | 'bible_study' | 'public_witnessing';
  notes?: string;
  serviceYear: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- Many-to-one with Member
- Many-to-one with Congregation

## Letter

**Purpose:** Document management with categorization and access control

**Key Attributes:**
- id: string - Unique letter identifier
- congregationId: string - Tenant isolation
- title: string - Document title
- filename: string - Stored file name
- category: string - Document category
- visibility: enum - Access control level
- uploadedBy: string - Uploader member ID
- uploadDate: Date - Upload timestamp

### TypeScript Interface
```typescript
interface Letter {
  id: string;
  congregationId: string;
  title: string;
  filename: string;
  category: 'announcement' | 'instruction' | 'form' | 'schedule' | 'other';
  visibility: 'public' | 'elders_only' | 'ms_and_elders';
  uploadedBy: string;
  uploadDate: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- Many-to-one with Congregation
- Many-to-one with Member (uploader)
