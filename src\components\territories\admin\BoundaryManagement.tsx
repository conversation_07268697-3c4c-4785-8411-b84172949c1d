'use client';

import React, { useState, useEffect } from 'react';

interface BoundaryStatus {
  totalTerritories: number;
  territoriesWithBoundaries: number;
  territoriesWithoutBoundaries: number;
  territories: Array<{
    territoryNumber: string;
    hasBoundaries: boolean;
    address: string;
  }>;
}

export default function BoundaryManagement() {
  const [status, setStatus] = useState<BoundaryStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBoundaryStatus();
  }, []);

  const fetchBoundaryStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/territories/boundary-status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch boundary status');
      }

      const data = await response.json();
      setStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load boundary status');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error</h3>
          <p className="text-red-600 text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  if (!status) {
    return (
      <div className="p-6">
        <p className="text-gray-500">No boundary status data available</p>
      </div>
    );
  }

  const percentage = status.totalTerritories > 0 
    ? Math.round((status.territoriesWithBoundaries / status.totalTerritories) * 100)
    : 0;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Gestión de Límites de Territorios</h2>
        <p className="text-gray-600 text-sm mt-1">
          Administra los datos de límites geográficos para los territorios
        </p>
      </div>

      {/* Status Overview */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Estado Actual</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{status.totalTerritories}</div>
            <div className="text-blue-800 text-sm">Total Territorios</div>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">{status.territoriesWithBoundaries}</div>
            <div className="text-green-800 text-sm">Con Límites</div>
          </div>
          
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="text-2xl font-bold text-orange-600">{status.territoriesWithoutBoundaries}</div>
            <div className="text-orange-800 text-sm">Sin Límites</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progreso de Límites</span>
            <span>{percentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${percentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Information Panel */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-amber-800 mb-3">
          ⚠️ Información Importante sobre Límites
        </h3>
        
        <div className="space-y-3 text-sm text-amber-700">
          <p>
            <strong>Estado Actual:</strong> Los territorios actualmente no tienen datos de límites reales. 
            Los mapas muestran solo marcadores de ubicación sin contornos de territorio.
          </p>
          
          <p>
            <strong>Para Agregar Límites Reales:</strong>
          </p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>Obtener coordenadas de límites reales de datos de topografía o GIS</li>
            <li>Los límites deben seguir patrones de calles reales y líneas de propiedad</li>
            <li>Usar formato GeoJSON Polygon con coordenadas [longitud, latitud]</li>
            <li>Contactar al desarrollador para importar datos de límites</li>
          </ul>
          
          <p>
            <strong>Beneficios de Límites Reales:</strong>
          </p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>Visualización precisa de áreas de territorio en mapas</li>
            <li>Mejor planificación de asignaciones de servicio</li>
            <li>Identificación clara de límites para publicadores</li>
          </ul>
        </div>
      </div>

      {/* Territories Without Boundaries */}
      {status.territoriesWithoutBoundaries > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Territorios Sin Límites ({status.territoriesWithoutBoundaries})
          </h3>
          
          <div className="max-h-64 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {status.territories
                .filter(t => !t.hasBoundaries)
                .map(territory => (
                  <div 
                    key={territory.territoryNumber}
                    className="bg-gray-50 border border-gray-200 rounded p-3"
                  >
                    <div className="font-medium text-gray-900">
                      Territorio {territory.territoryNumber}
                    </div>
                    <div className="text-sm text-gray-600 truncate">
                      {territory.address.split('\n')[0] || 'Sin dirección'}
                    </div>
                  </div>
                ))
              }
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-3">
        <button
          onClick={fetchBoundaryStatus}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Actualizar Estado
        </button>
        
        <button
          onClick={() => window.open('/api/territories/boundary-export', '_blank')}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          Exportar Lista
        </button>
      </div>
    </div>
  );
}
