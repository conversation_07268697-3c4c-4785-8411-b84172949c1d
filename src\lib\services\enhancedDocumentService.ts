/**
 * Enhanced Document Management Service
 *
 * Comprehensive service for managing congregation documents, letters, and files.
 * Handles document organization, search, workflow, collaboration, and analytics.
 */

import { prisma } from '@/lib/prisma';

// Document interfaces
export interface DocumentData {
  id: string;
  congregationId: string;
  title: string;
  description?: string;
  filename: string;
  filePath: string;
  fileSize?: number;
  mimeType?: string;
  category?: string;
  subcategory?: string;
  tags: string[];
  visibility: DocumentVisibility;
  priority: DocumentPriority;
  status: DocumentStatus;
  version: number;
  parentId?: string;
  folderId?: string;
  expirationDate?: Date;
  publishDate?: Date;
  uploadDate: Date;
  uploadedById?: string;
  approvedById?: string;
  approvedAt?: Date;
  downloadCount: number;
  viewCount: number;
  searchableText?: string;
  thumbnailPath?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateDocumentData {
  title: string;
  description?: string;
  filename: string;
  filePath: string;
  fileSize?: number;
  mimeType?: string;
  category?: string;
  subcategory?: string;
  tags?: string[];
  visibility?: DocumentVisibility;
  priority?: DocumentPriority;
  folderId?: string;
  expirationDate?: Date;
  publishDate?: Date;
  searchableText?: string;
  thumbnailPath?: string;
}

export interface UpdateDocumentData {
  title?: string;
  description?: string;
  category?: string;
  subcategory?: string;
  tags?: string[];
  visibility?: DocumentVisibility;
  priority?: DocumentPriority;
  status?: DocumentStatus;
  folderId?: string;
  expirationDate?: Date;
  publishDate?: Date;
  searchableText?: string;
  thumbnailPath?: string;
  isActive?: boolean;
}

export interface DocumentFilters {
  category?: string;
  subcategory?: string;
  tags?: string[];
  visibility?: DocumentVisibility;
  priority?: DocumentPriority;
  status?: DocumentStatus;
  folderId?: string;
  uploadedById?: string;
  startDate?: Date;
  endDate?: Date;
  search?: string;
  isExpired?: boolean;
}

export interface DocumentFolder {
  id: string;
  congregationId: string;
  name: string;
  description?: string;
  parentId?: string;
  path: string;
  color?: string;
  icon?: string;
  visibility: DocumentVisibility;
  sortOrder: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  children?: DocumentFolder[];
  documentCount?: number;
}

export interface DocumentAccessLog {
  id: string;
  documentId: string;
  memberId: string;
  accessType: DocumentAccessType;
  ipAddress?: string;
  userAgent?: string;
  accessedAt: Date;
}

export interface DocumentComment {
  id: string;
  documentId: string;
  memberId: string;
  parentId?: string;
  content: string;
  isInternal: boolean;
  isResolved: boolean;
  createdAt: Date;
  updatedAt: Date;
  member?: {
    id: string;
    name: string;
  };
  replies?: DocumentComment[];
}

export interface DocumentWorkflow {
  id: string;
  documentId: string;
  workflowType: DocumentWorkflowType;
  status: DocumentWorkflowStatus;
  assignedToId?: string;
  assignedById: string;
  priority: DocumentPriority;
  dueDate?: Date;
  comments?: string;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface DocumentAnalytics {
  totalDocuments: number;
  documentsByCategory: Record<string, number>;
  documentsByStatus: Record<DocumentStatus, number>;
  recentUploads: DocumentData[];
  popularDocuments: DocumentData[];
  expiringDocuments: DocumentData[];
  accessStats: {
    totalViews: number;
    totalDownloads: number;
    uniqueUsers: number;
  };
}

// Document enums
export enum DocumentVisibility {
  ALL_MEMBERS = 'ALL_MEMBERS',
  ELDERS_ONLY = 'ELDERS_ONLY',
  MINISTERIAL_SERVANTS_PLUS = 'MINISTERIAL_SERVANTS_PLUS',
  COORDINATORS_ONLY = 'COORDINATORS_ONLY',
  CUSTOM = 'CUSTOM'
}

export enum DocumentPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum DocumentStatus {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  EXPIRED = 'EXPIRED'
}

export enum DocumentAccessType {
  VIEW = 'VIEW',
  DOWNLOAD = 'DOWNLOAD',
  SEARCH = 'SEARCH'
}

export enum DocumentWorkflowType {
  APPROVAL = 'APPROVAL',
  REVIEW = 'REVIEW',
  PUBLICATION = 'PUBLICATION'
}

export enum DocumentWorkflowStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED'
}

// Category labels in Spanish
export const DOCUMENT_CATEGORY_LABELS: Record<string, string> = {
  'cartas_cuerpo_gobernante': 'Cartas del Cuerpo Gobernante',
  'anuncios': 'Anuncios',
  'formularios': 'Formularios',
  'programas': 'Programas de Reunión',
  'asignaciones': 'Asignaciones',
  'servicio_campo': 'Servicio del Campo',
  'eventos': 'Eventos',
  'administrativo': 'Documentos Administrativos',
  'general': 'General'
};

export const DOCUMENT_VISIBILITY_LABELS: Record<DocumentVisibility, string> = {
  [DocumentVisibility.ALL_MEMBERS]: 'Todos los Hermanos',
  [DocumentVisibility.ELDERS_ONLY]: 'Solo Ancianos',
  [DocumentVisibility.MINISTERIAL_SERVANTS_PLUS]: 'Siervos Ministeriales y Ancianos',
  [DocumentVisibility.COORDINATORS_ONLY]: 'Solo Coordinadores',
  [DocumentVisibility.CUSTOM]: 'Personalizado'
};

export const DOCUMENT_PRIORITY_LABELS: Record<DocumentPriority, string> = {
  [DocumentPriority.LOW]: 'Baja',
  [DocumentPriority.NORMAL]: 'Normal',
  [DocumentPriority.HIGH]: 'Alta',
  [DocumentPriority.URGENT]: 'Urgente'
};

export const DOCUMENT_STATUS_LABELS: Record<DocumentStatus, string> = {
  [DocumentStatus.DRAFT]: 'Borrador',
  [DocumentStatus.PENDING_APPROVAL]: 'Pendiente de Aprobación',
  [DocumentStatus.ACTIVE]: 'Activo',
  [DocumentStatus.ARCHIVED]: 'Archivado',
  [DocumentStatus.EXPIRED]: 'Expirado'
};

export class EnhancedDocumentService {
  /**
   * Create a new document
   */
  static async createDocument(
    congregationId: string,
    documentData: CreateDocumentData,
    uploadedById?: string
  ): Promise<DocumentData> {
    try {
      // Validate document data
      this.validateDocumentData(documentData);

      const document = await prisma.letter.create({
        data: {
          congregationId,
          title: documentData.title,
          description: documentData.description,
          filename: documentData.filename,
          filePath: documentData.filePath,
          fileSize: documentData.fileSize,
          mimeType: documentData.mimeType,
          category: documentData.category,
          subcategory: documentData.subcategory,
          tags: documentData.tags || [],
          visibility: documentData.visibility || DocumentVisibility.ALL_MEMBERS,
          priority: documentData.priority || DocumentPriority.NORMAL,
          status: DocumentStatus.ACTIVE,
          version: 1,
          folderId: documentData.folderId,
          expirationDate: documentData.expirationDate,
          publishDate: documentData.publishDate || new Date(),
          uploadedById,
          searchableText: documentData.searchableText,
          thumbnailPath: documentData.thumbnailPath,
        },
      });

      return this.mapDocumentToDocumentData(document);
    } catch (error) {
      console.error('Error creating document:', error);
      throw new Error('Failed to create document');
    }
  }

  /**
   * Get documents with filtering and pagination
   */
  static async getDocuments(
    congregationId: string,
    filters: DocumentFilters = {},
    limit: number = 50,
    offset: number = 0,
    userRole?: string
  ): Promise<DocumentData[]> {
    try {
      const where: any = {
        congregationId,
        isActive: true,
      };

      // Apply visibility filter based on user role
      if (userRole && userRole !== 'elder') {
        const allowedVisibilities = this.getAllowedVisibilities(userRole);
        where.visibility = { in: allowedVisibilities };
      }

      // Apply filters
      if (filters.category) {
        where.category = filters.category;
      }

      if (filters.subcategory) {
        where.subcategory = filters.subcategory;
      }

      if (filters.visibility) {
        where.visibility = filters.visibility;
      }

      if (filters.priority) {
        where.priority = filters.priority;
      }

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.folderId !== undefined) {
        where.folderId = filters.folderId || null;
      }

      if (filters.uploadedById) {
        where.uploadedById = filters.uploadedById;
      }

      if (filters.startDate || filters.endDate) {
        where.uploadDate = {};
        if (filters.startDate) {
          where.uploadDate.gte = filters.startDate;
        }
        if (filters.endDate) {
          where.uploadDate.lte = filters.endDate;
        }
      }

      if (filters.isExpired !== undefined) {
        if (filters.isExpired) {
          where.expirationDate = { lt: new Date() };
        } else {
          where.expirationDate = { gte: new Date() };
        }
      }

      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          { searchableText: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      if (filters.tags && filters.tags.length > 0) {
        where.tags = { hasSome: filters.tags };
      }

      const documents = await prisma.letter.findMany({
        where,
        orderBy: [
          { priority: 'desc' },
          { uploadDate: 'desc' },
        ],
        take: limit,
        skip: offset,
      });

      return documents.map(this.mapDocumentToDocumentData);
    } catch (error) {
      console.error('Error fetching documents:', error);
      throw new Error('Failed to fetch documents');
    }
  }

  /**
   * Helper methods
   */
  private static validateDocumentData(documentData: CreateDocumentData): void {
    if (!documentData.title || documentData.title.trim().length === 0) {
      throw new Error('Document title is required');
    }

    if (!documentData.filename || documentData.filename.trim().length === 0) {
      throw new Error('Document filename is required');
    }

    if (!documentData.filePath || documentData.filePath.trim().length === 0) {
      throw new Error('Document file path is required');
    }
  }

  private static getAllowedVisibilities(userRole: string): DocumentVisibility[] {
    switch (userRole) {
      case 'elder':
        return Object.values(DocumentVisibility);
      case 'ministerial_servant':
        return [
          DocumentVisibility.ALL_MEMBERS,
          DocumentVisibility.MINISTERIAL_SERVANTS_PLUS,
        ];
      default:
        return [DocumentVisibility.ALL_MEMBERS];
    }
  }

  private static canAccessDocument(visibility: string, userRole: string): boolean {
    const allowedVisibilities = this.getAllowedVisibilities(userRole);
    return allowedVisibilities.includes(visibility as DocumentVisibility);
  }

  private static hasDocumentAccess(visibility: DocumentVisibility, userRole: string): boolean {
    const allowedVisibilities = this.getAllowedVisibilities(userRole);
    return allowedVisibilities.includes(visibility);
  }

  /**
   * Get a document by ID
   */
  static async getDocumentById(
    documentId: string,
    congregationId: string,
    userRole: string
  ): Promise<DocumentData | null> {
    try {
      const document = await prisma.letter.findFirst({
        where: {
          id: documentId,
          congregationId,
          isActive: true,
        },
      });

      if (!document) {
        return null;
      }

      // Check visibility permissions
      if (!this.hasDocumentAccess(document.visibility as DocumentVisibility, userRole)) {
        return null;
      }

      return this.mapDocumentToDocumentData(document);
    } catch (error) {
      console.error('Error getting document by ID:', error);
      throw error;
    }
  }

  /**
   * Update a document
   */
  static async updateDocument(
    documentId: string,
    updateData: UpdateDocumentData,
    congregationId: string,
    updatedById: string
  ): Promise<DocumentData> {
    try {
      const document = await prisma.letter.update({
        where: {
          id: documentId,
          congregationId,
        },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
      });

      return this.mapDocumentToDocumentData(document);
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  }

  /**
   * Delete a document
   */
  static async deleteDocument(
    documentId: string,
    congregationId: string
  ): Promise<void> {
    try {
      await prisma.letter.delete({
        where: {
          id: documentId,
          congregationId,
        },
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  private static mapDocumentToDocumentData(document: any): DocumentData {
    return {
      id: document.id,
      congregationId: document.congregationId,
      title: document.title,
      description: document.description || undefined,
      filename: document.filename,
      filePath: document.filePath,
      fileSize: document.fileSize || undefined,
      mimeType: document.mimeType || undefined,
      category: document.category || undefined,
      subcategory: undefined, // Not in Letter model
      tags: [], // Not in Letter model
      visibility: document.visibility as DocumentVisibility,
      priority: 'NORMAL' as DocumentPriority, // Default value
      status: 'ACTIVE' as DocumentStatus, // Default value
      version: 1, // Default value
      parentId: undefined, // Not in Letter model
      folderId: document.folderId || undefined,
      expirationDate: document.expirationDate || undefined,
      publishDate: undefined, // Not in Letter model
      uploadDate: document.uploadDate,
      uploadedById: document.uploadedById || undefined,
      approvedById: document.approvedById || undefined,
      approvedAt: document.approvedAt || undefined,
      downloadCount: document.downloadCount || 0,
      viewCount: 0, // Not in Letter model
      searchableText: undefined, // Not in Letter model
      thumbnailPath: undefined, // Not in Letter model
      isActive: document.isActive,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
    };
  }
}
