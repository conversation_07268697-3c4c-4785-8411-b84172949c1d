const XLSX = require('xlsx');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function testImportFewTerritories() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');

  // Test with just a few territories first
  const testTerritories = ['012', '025', '051'];

  console.log(`🧪 Testing import with territories: ${testTerritories.join(', ')}\n`);

  for (const number of testTerritories) {
    try {
      console.log(`📋 Testing Territory ${number}...`);

      const fileName = `Terr. ${number}.xlsx`;
      const filePath = path.join(territoriosDir, fileName);

      if (!fs.existsSync(filePath)) {
        console.log(`  ❌ File not found: ${fileName}`);
        continue;
      }

      const workbook = XLSX.readFile(filePath);

      // Find the main territory sheet (not "Mapa")
      const territorySheet = workbook.SheetNames.find(name =>
        name.toLowerCase().includes('terr') && !name.toLowerCase().includes('mapa')
      ) || workbook.SheetNames[0];

      console.log(`  📄 Using sheet: ${territorySheet}`);

      const sheet = workbook.Sheets[territorySheet];
      const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });

      // Parse addresses and notes
      const addresses = [];
      const notes = [];
      let currentStreet = '';

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (!row || row.length === 0) continue;

        // Check for street names in second column (common pattern)
        const secondCell = row[1] ? row[1].toString().trim() : '';
        if (secondCell && /\b(ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD|CT|COURT|PL|PLACE|DR|DRIVE|LN|LANE)\b/i.test(secondCell)) {
          currentStreet = secondCell;
          console.log(`    🛣️  Found street: ${currentStreet}`);
          continue;
        }

        // Check for house numbers (usually in second column, can be number or string type)
        let houseNumber = '';
        let observaciones = '';

        // Check second column for house numbers (most common pattern)
        if (!row[0] && row[1] !== undefined && row[1] !== null && row[1] !== '') {
          const cellValue = row[1].toString().trim();
          // House numbers can be pure numbers or numbers with letters
          if (/^\d+[A-Z]?$/i.test(cellValue) && cellValue !== 'No. de Casa') {
            houseNumber = cellValue;
            // Look for observations in column 7 (index 6) - the "Observaciones" column
            if (row[6] && row[6].toString().trim()) {
              const obs = row[6].toString().trim();
              if (obs !== 'Observaciones' && obs !== 'NO CAMBIAR ESTAS LETRAS') {
                observaciones = obs;
              }
            }
          }
        }

        if (houseNumber && currentStreet) {
          const fullAddress = `${houseNumber} ${currentStreet}, Miami, FL`;
          addresses.push(fullAddress);
          console.log(`    🏠 Address: ${fullAddress}`);

          if (observaciones && observaciones !== 'NO CAMBIAR ESTAS LETRAS') {
            notes.push(`${fullAddress}: ${observaciones}`);
            console.log(`      📝 Note: ${observaciones}`);
          }
        }
      }

      console.log(`  📊 Summary: ${addresses.length} addresses, ${notes.length} notes`);

      if (addresses.length > 0) {
        console.log(`  ✅ Would create territory ${number}`);
      } else {
        console.log(`  ⚠️  No addresses found`);
      }

      console.log('');

    } catch (error) {
      console.log(`  ❌ Error: ${error.message}\n`);
    }
  }

  await prisma.$disconnect();
}

testImportFewTerritories().catch(console.error);
