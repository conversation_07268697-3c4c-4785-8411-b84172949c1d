const mysql = require('mysql2/promise');
require('dotenv').config();

async function runMigration() {
  let connection;

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.MYSQL_HOST || 'localhost',
      user: process.env.MYSQL_USER || 'root',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'coral_oeste_db'
    });

    console.log('Connected to database');

    // Add missing columns to congregations table
    const alterTableQueries = [
      `ALTER TABLE congregations
       ADD COLUMN IF NOT EXISTS circuit_number VARCHAR(10) DEFAULT NULL`,

      `ALTER TABLE congregations
       ADD COLUMN IF NOT EXISTS circuit_overseer VARCHAR(255) DEFAULT NULL`,

      `ALTER TABLE congregations
       ADD COLUMN IF NOT EXISTS midweek_day VARCHAR(20) DEFAULT 'Thursday'`,

      `ALTER TABLE congregations
       ADD COLUMN IF NOT EXISTS midweek_time VARCHAR(10) DEFAULT '7:00 PM'`,

      `ALTER TABLE congregations
       ADD COLUMN IF NOT EXISTS weekend_day VARCHAR(20) DEFAULT 'Sunday'`,

      `ALTER TABLE congregations
       ADD COLUMN IF NOT EXISTS weekend_time VARCHAR(10) DEFAULT '10:00 AM'`,

      `ALTER TABLE congregations
       ADD COLUMN IF NOT EXISTS default_congregation_id VARCHAR(20) DEFAULT NULL`,

      `ALTER TABLE congregations
       ADD COLUMN IF NOT EXISTS default_congregation_pin VARCHAR(20) DEFAULT NULL`
    ];

    // Execute ALTER TABLE queries
    for (const query of alterTableQueries) {
      try {
        await connection.execute(query);
        console.log('✓ Executed:', query.split('\n')[0].trim());
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log('- Column already exists:', query.split('\n')[0].trim());
        } else {
          console.error('✗ Error executing:', query.split('\n')[0].trim(), error.message);
        }
      }
    }

    // Update the existing Coral Oeste congregation with default values
    const updateQuery = `
      UPDATE congregations
      SET
        circuit_number = COALESCE(circuit_number, '23'),
        circuit_overseer = COALESCE(circuit_overseer, 'Brother Smith'),
        midweek_day = COALESCE(midweek_day, 'Thursday'),
        midweek_time = COALESCE(midweek_time, '7:00 PM'),
        weekend_day = COALESCE(weekend_day, 'Sunday'),
        weekend_time = COALESCE(weekend_time, '10:00 AM'),
        default_congregation_id = COALESCE(default_congregation_id, '1441'),
        default_congregation_pin = COALESCE(default_congregation_pin, '1930')
      WHERE id = 1
    `;

    await connection.execute(updateQuery);
    console.log('✓ Updated Coral Oeste congregation with default values');

    // Create audit_logs table if it doesn't exist
    const createAuditLogsQuery = `
      CREATE TABLE IF NOT EXISTS audit_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        action VARCHAR(100) NOT NULL,
        table_name VARCHAR(100) NOT NULL,
        record_id VARCHAR(255) NOT NULL,
        details JSON DEFAULT NULL,
        ip_address VARCHAR(45) DEFAULT NULL,
        user_agent TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    await connection.execute(createAuditLogsQuery);
    console.log('✓ Created audit_logs table');

    // Add indexes
    const indexQueries = [
      `CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action ON audit_logs(user_id, action)`,
      `CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at)`
    ];

    for (const query of indexQueries) {
      try {
        await connection.execute(query);
        console.log('✓ Created index:', query.split(' ')[5]);
      } catch (error) {
        if (error.code === 'ER_DUP_KEYNAME') {
          console.log('- Index already exists:', query.split(' ')[5]);
        } else {
          console.error('✗ Error creating index:', error.message);
        }
      }
    }

    console.log('\n✅ Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
runMigration();
