/**
 * Boundary Generator Service
 * 
 * Generates mock territory boundaries for territories that don't have them yet.
 * Creates realistic rectangular boundaries around territory coordinates.
 */

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Boundary {
  type: 'Polygon';
  coordinates: number[][][];
}

export class BoundaryGeneratorService {
  /**
   * Generate a mock boundary around a center coordinate
   */
  static generateBoundaryAroundCoordinate(
    center: Coordinates,
    sizeKm: number = 0.5
  ): Boundary {
    // Convert km to approximate degrees (rough approximation)
    const latDelta = sizeKm / 111; // 1 degree lat ≈ 111 km
    const lngDelta = sizeKm / (111 * Math.cos(center.latitude * Math.PI / 180));

    // Create a rectangular boundary around the center
    const boundary: Boundary = {
      type: 'Polygon',
      coordinates: [[
        [center.longitude - lngDelta, center.latitude + latDelta], // NW
        [center.longitude + lngDelta, center.latitude + latDelta], // NE
        [center.longitude + lngDelta, center.latitude - latDelta], // SE
        [center.longitude - lngDelta, center.latitude - latDelta], // SW
        [center.longitude - lngDelta, center.latitude + latDelta]  // Close polygon
      ]]
    };

    return boundary;
  }

  /**
   * Generate a mock boundary from an address (creates coordinate first)
   */
  static generateBoundaryFromAddress(address: string): Boundary {
    // For demo purposes, generate coordinates based on address hash
    const hash = this.simpleHash(address);
    
    // Generate coordinates in Miami area (where our test data is)
    const baseLat = 25.7617; // Miami base latitude
    const baseLng = -80.1918; // Miami base longitude
    
    // Create some variation based on address hash
    const latOffset = ((hash % 1000) / 1000 - 0.5) * 0.1; // ±0.05 degrees
    const lngOffset = ((hash % 2000) / 2000 - 0.5) * 0.1; // ±0.05 degrees
    
    const center: Coordinates = {
      latitude: baseLat + latOffset,
      longitude: baseLng + lngOffset
    };

    return this.generateBoundaryAroundCoordinate(center, 0.3);
  }

  /**
   * Generate coordinates from address (simple hash-based approach for demo)
   */
  static generateCoordinatesFromAddress(address: string): Coordinates {
    const hash = this.simpleHash(address);
    
    // Generate coordinates in Miami area
    const baseLat = 25.7617;
    const baseLng = -80.1918;
    
    const latOffset = ((hash % 1000) / 1000 - 0.5) * 0.1;
    const lngOffset = ((hash % 2000) / 2000 - 0.5) * 0.1;
    
    return {
      latitude: baseLat + latOffset,
      longitude: baseLng + lngOffset
    };
  }

  /**
   * Generate multiple territory boundaries for a list of territories
   */
  static generateBoundariesForTerritories(territories: Array<{
    id: string;
    territoryNumber: string;
    address: string;
    coordinates?: Coordinates | null;
    boundary?: Boundary | null;
  }>): Array<{
    id: string;
    coordinates: Coordinates;
    boundary: Boundary;
  }> {
    return territories.map((territory, index) => {
      let coordinates = territory.coordinates;
      let boundary = territory.boundary;

      // Generate coordinates if missing
      if (!coordinates) {
        coordinates = this.generateCoordinatesFromAddress(territory.address);
      }

      // Generate boundary if missing
      if (!boundary) {
        // Vary the size slightly for each territory
        const sizeKm = 0.3 + (index % 3) * 0.1; // 0.3, 0.4, or 0.5 km
        boundary = this.generateBoundaryAroundCoordinate(coordinates, sizeKm);
      }

      return {
        id: territory.id,
        coordinates,
        boundary
      };
    });
  }

  /**
   * Simple hash function for generating consistent coordinates from address
   */
  private static simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Get predefined boundaries for known territories (for testing)
   */
  static getPredefinedBoundaries(): Record<string, { coordinates: Coordinates; boundary: Boundary }> {
    return {
      '007': {
        coordinates: { latitude: 25.7620, longitude: -80.2715 },
        boundary: {
          type: 'Polygon',
          coordinates: [[
            [-80.2725, 25.7630],
            [-80.2705, 25.7630],
            [-80.2705, 25.7610],
            [-80.2725, 25.7610],
            [-80.2725, 25.7630]
          ]]
        }
      },
      '014': {
        coordinates: { latitude: 25.7615, longitude: -80.2708 },
        boundary: {
          type: 'Polygon',
          coordinates: [[
            [-80.2718, 25.7625],
            [-80.2698, 25.7625],
            [-80.2698, 25.7605],
            [-80.2718, 25.7605],
            [-80.2718, 25.7625]
          ]]
        }
      },
      '021': {
        coordinates: { latitude: 25.7610, longitude: -80.2700 },
        boundary: {
          type: 'Polygon',
          coordinates: [[
            [-80.2710, 25.7620],
            [-80.2690, 25.7620],
            [-80.2690, 25.7600],
            [-80.2710, 25.7600],
            [-80.2710, 25.7620]
          ]]
        }
      },
      '028': {
        coordinates: { latitude: 25.7605, longitude: -80.2692 },
        boundary: {
          type: 'Polygon',
          coordinates: [[
            [-80.2702, 25.7615],
            [-80.2682, 25.7615],
            [-80.2682, 25.7595],
            [-80.2702, 25.7595],
            [-80.2702, 25.7615]
          ]]
        }
      }
    };
  }

  /**
   * Enhance territory data with coordinates and boundaries
   */
  static enhanceTerritoryData(territory: {
    id: string;
    territoryNumber: string;
    address: string;
    coordinates?: Coordinates | null;
    boundary?: Boundary | null;
  }): {
    id: string;
    territoryNumber: string;
    address: string;
    coordinates: Coordinates;
    boundary: Boundary;
  } {
    const predefined = this.getPredefinedBoundaries();
    
    // Use predefined data if available
    if (predefined[territory.territoryNumber]) {
      return {
        ...territory,
        coordinates: predefined[territory.territoryNumber].coordinates,
        boundary: predefined[territory.territoryNumber].boundary
      };
    }

    // Generate coordinates if missing
    let coordinates = territory.coordinates;
    if (!coordinates) {
      coordinates = this.generateCoordinatesFromAddress(territory.address);
    }

    // Generate boundary if missing
    let boundary = territory.boundary;
    if (!boundary) {
      boundary = this.generateBoundaryAroundCoordinate(coordinates, 0.4);
    }

    return {
      ...territory,
      coordinates,
      boundary
    };
  }
}
