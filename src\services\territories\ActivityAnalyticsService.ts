/**
 * Activity Analytics Service
 *
 * Provides analytics for territory activities including action-based reports,
 * member activity patterns, and effectiveness metrics.
 */

import { prisma } from '@/lib/prisma';

export type ActivityType = 'en_casa' | 'no_en_casa' | 'perros_rejas' | 'no_llamar' | 'no_trespassing';

export interface ActivityAnalytics {
  territoryId: string;
  territoryNumber: string;
  activityType: ActivityType;
  count: number;
  percentage: number;
  lastActivity: Date | null;
  memberActivity: {
    memberId: string;
    memberName: string;
    activityCount: number;
  }[];
}

export interface ActivitySummary {
  totalActivities: number;
  activityBreakdown: {
    [key in ActivityType]: number;
  };
  mostActiveTerritory: string;
  mostActiveMember: string;
  averageActivitiesPerTerritory: number;
}

export interface MemberActivityPattern {
  memberId: string;
  memberName: string;
  totalActivities: number;
  activityBreakdown: {
    [key in ActivityType]: number;
  };
  territoriesWorked: number;
  averageActivitiesPerTerritory: number;
  lastActivity: Date | null;
}

export class ActivityAnalyticsService {
  /**
   * Map action strings to activity types
   */
  private static mapActionToActivityType(action: string): ActivityType | null {
    const actionLower = action.toLowerCase();

    if (actionLower.includes('en casa') || actionLower.includes('home')) {
      return 'en_casa';
    }
    if (actionLower.includes('no en casa') || actionLower.includes('not home')) {
      return 'no_en_casa';
    }
    if (actionLower.includes('perros') || actionLower.includes('rejas') ||
        actionLower.includes('dogs') || actionLower.includes('gates')) {
      return 'perros_rejas';
    }
    if (actionLower.includes('no llamar') || actionLower.includes('do not call')) {
      return 'no_llamar';
    }
    if (actionLower.includes('no trespassing') || actionLower.includes('trespassing')) {
      return 'no_trespassing';
    }

    return null;
  }

  /**
   * Get activity analytics for a specific territory
   */
  static async getTerritoryActivityAnalytics(territoryId: string, congregationId: string): Promise<ActivityAnalytics[]> {
    try {
      // Get territory information
      const territory = await prisma.territory.findUnique({
        where: {
          id: territoryId,
          congregationId
        },
        select: {
          territoryNumber: true
        }
      });

      if (!territory) {
        return [];
      }

      // Get territory actions from assignment and visit notes
      const assignmentNotes = await prisma.territoryAssignment.findMany({
        where: {
          territoryId,
          congregationId,
          notes: {
            not: null
          }
        },
        include: {
          member: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      const visitNotes = await prisma.territoryVisit.findMany({
        where: {
          congregationId,
          assignment: {
            territoryId
          },
          notes: {
            not: null
          }
        },
        include: {
          assignment: {
            include: {
              member: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Combine all actions
      const territoryActions = [
        ...assignmentNotes.map(assignment => ({
          id: assignment.id,
          note: assignment.notes,
          member: assignment.member,
          createdAt: assignment.createdAt
        })),
        ...visitNotes.map(visit => ({
          id: visit.id,
          note: visit.notes,
          member: visit.assignment.member,
          createdAt: visit.createdAt
        }))
      ];

      // Group activities by type
      const activityGroups: { [key in ActivityType]: any[] } = {
        en_casa: [],
        no_en_casa: [],
        perros_rejas: [],
        no_llamar: [],
        no_trespassing: []
      };

      territoryActions.forEach(action => {
        const activityType = this.mapActionToActivityType(action.note || '');
        if (activityType) {
          activityGroups[activityType].push(action);
        }
      });

      // Create analytics for each activity type
      const analytics: ActivityAnalytics[] = [];
      const totalActivities = territoryActions.length;

      Object.entries(activityGroups).forEach(([type, activities]) => {
        const activityType = type as ActivityType;

        // Group by member
        const memberActivity: { [memberId: string]: { name: string; count: number } } = {};
        activities.forEach(activity => {
          if (activity.member) {
            if (!memberActivity[activity.member.id]) {
              memberActivity[activity.member.id] = {
                name: activity.member.name,
                count: 0
              };
            }
            memberActivity[activity.member.id].count++;
          }
        });

        analytics.push({
          territoryId,
          territoryNumber: territory.territoryNumber,
          activityType,
          count: activities.length,
          percentage: totalActivities > 0 ? Math.round((activities.length / totalActivities) * 1000) / 10 : 0,
          lastActivity: activities.length > 0 ? activities[0].createdAt : null,
          memberActivity: Object.entries(memberActivity).map(([memberId, data]) => ({
            memberId,
            memberName: data.name,
            activityCount: data.count
          }))
        });
      });

      return analytics;

    } catch (error) {
      console.error('Error getting territory activity analytics:', error);
      return [];
    }
  }

  /**
   * Get activity summary for the entire congregation
   */
  static async getActivitySummary(congregationId: string): Promise<ActivitySummary> {
    try {
      // Get all territory assignment notes
      const assignmentNotes = await prisma.territoryAssignment.findMany({
        where: {
          congregationId,
          notes: {
            not: null
          }
        },
        include: {
          territory: {
            select: {
              territoryNumber: true
            }
          },
          member: {
            select: {
              name: true
            }
          }
        }
      });

      // Get all territory visit notes
      const visitNotes = await prisma.territoryVisit.findMany({
        where: {
          congregationId,
          notes: {
            not: null
          }
        },
        include: {
          assignment: {
            include: {
              territory: {
                select: {
                  territoryNumber: true
                }
              },
              member: {
                select: {
                  name: true
                }
              }
            }
          }
        }
      });

      // Combine all actions
      const allActions = [
        ...assignmentNotes.map(assignment => ({
          note: assignment.notes,
          territory: assignment.territory,
          member: assignment.member,
          createdAt: assignment.createdAt
        })),
        ...visitNotes.map(visit => ({
          note: visit.notes,
          territory: visit.assignment.territory,
          member: visit.assignment.member,
          createdAt: visit.createdAt
        }))
      ];

      // Initialize breakdown
      const activityBreakdown: { [key in ActivityType]: number } = {
        en_casa: 0,
        no_en_casa: 0,
        perros_rejas: 0,
        no_llamar: 0,
        no_trespassing: 0
      };

      // Count activities by type
      const territoryActivityCounts: { [territoryNumber: string]: number } = {};
      const memberActivityCounts: { [memberName: string]: number } = {};

      allActions.forEach(action => {
        const activityType = this.mapActionToActivityType(action.note || '');
        if (activityType) {
          activityBreakdown[activityType]++;
        }

        // Count by territory
        if (action.territory) {
          territoryActivityCounts[action.territory.territoryNumber] =
            (territoryActivityCounts[action.territory.territoryNumber] || 0) + 1;
        }

        // Count by member
        if (action.member) {
          memberActivityCounts[action.member.name] =
            (memberActivityCounts[action.member.name] || 0) + 1;
        }
      });

      // Find most active territory and member
      const mostActiveTerritory = Object.entries(territoryActivityCounts)
        .sort(([,a], [,b]) => b - a)[0]?.[0] || '';

      const mostActiveMember = Object.entries(memberActivityCounts)
        .sort(([,a], [,b]) => b - a)[0]?.[0] || '';

      // Calculate average activities per territory
      const uniqueTerritories = new Set(allActions.map(a => a.territory?.territoryNumber).filter(Boolean));
      const averageActivitiesPerTerritory = uniqueTerritories.size > 0
        ? Math.round((allActions.length / uniqueTerritories.size) * 10) / 10
        : 0;

      return {
        totalActivities: allActions.length,
        activityBreakdown,
        mostActiveTerritory,
        mostActiveMember,
        averageActivitiesPerTerritory
      };

    } catch (error) {
      console.error('Error getting activity summary:', error);
      return {
        totalActivities: 0,
        activityBreakdown: {
          en_casa: 0,
          no_en_casa: 0,
          perros_rejas: 0,
          no_llamar: 0,
          no_trespassing: 0
        },
        mostActiveTerritory: '',
        mostActiveMember: '',
        averageActivitiesPerTerritory: 0
      };
    }
  }

  /**
   * Get member activity patterns
   */
  static async getMemberActivityPatterns(congregationId: string): Promise<MemberActivityPattern[]> {
    try {
      const members = await prisma.member.findMany({
        where: { congregationId },
        include: {
          territoryAssignments: {
            where: {
              notes: {
                not: null
              }
            },
            include: {
              territory: {
                select: {
                  territoryNumber: true
                }
              }
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      });

      // Also get visit notes for each member
      const memberVisits = await prisma.territoryVisit.findMany({
        where: {
          congregationId,
          notes: {
            not: null
          }
        },
        include: {
          assignment: {
            include: {
              territory: {
                select: {
                  territoryNumber: true
                }
              },
              member: {
                select: {
                  id: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return members.map(member => {
        const activityBreakdown: { [key in ActivityType]: number } = {
          en_casa: 0,
          no_en_casa: 0,
          perros_rejas: 0,
          no_llamar: 0,
          no_trespassing: 0
        };

        const territoriesWorked = new Set<string>();
        let lastActivity: Date | null = null;

        // Process assignment notes
        member.territoryAssignments.forEach(assignment => {
          const activityType = this.mapActionToActivityType(assignment.notes || '');
          if (activityType) {
            activityBreakdown[activityType]++;
          }

          if (assignment.territory) {
            territoriesWorked.add(assignment.territory.territoryNumber);
          }

          if (!lastActivity || assignment.createdAt > lastActivity) {
            lastActivity = assignment.createdAt;
          }
        });

        // Process visit notes for this member
        const memberVisitNotes = memberVisits.filter(visit =>
          visit.assignment.member.id === member.id
        );

        memberVisitNotes.forEach(visit => {
          const activityType = this.mapActionToActivityType(visit.notes || '');
          if (activityType) {
            activityBreakdown[activityType]++;
          }

          if (visit.assignment.territory) {
            territoriesWorked.add(visit.assignment.territory.territoryNumber);
          }

          if (!lastActivity || visit.createdAt > lastActivity) {
            lastActivity = visit.createdAt;
          }
        });

        const totalActivities = Object.values(activityBreakdown).reduce((sum, count) => sum + count, 0);
        const averageActivitiesPerTerritory = territoriesWorked.size > 0
          ? Math.round((totalActivities / territoriesWorked.size) * 10) / 10
          : 0;

        return {
          memberId: member.id,
          memberName: member.name,
          totalActivities,
          activityBreakdown,
          territoriesWorked: territoriesWorked.size,
          averageActivitiesPerTerritory,
          lastActivity
        };
      }).filter(pattern => pattern.totalActivities > 0)
        .sort((a, b) => b.totalActivities - a.totalActivities);

    } catch (error) {
      console.error('Error getting member activity patterns:', error);
      return [];
    }
  }

  /**
   * Get activity trends over time
   */
  static async getActivityTrends(congregationId: string, months: number = 12): Promise<{
    month: string;
    activities: { [key in ActivityType]: number };
    total: number;
  }[]> {
    try {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      // Get assignment notes
      const assignmentActions = await prisma.territoryAssignment.findMany({
        where: {
          congregationId,
          notes: {
            not: null
          },
          createdAt: {
            gte: startDate
          }
        },
        orderBy: {
          createdAt: 'asc'
        }
      });

      // Get visit notes
      const visitActions = await prisma.territoryVisit.findMany({
        where: {
          congregationId,
          notes: {
            not: null
          },
          createdAt: {
            gte: startDate
          }
        },
        orderBy: {
          createdAt: 'asc'
        }
      });

      // Combine all actions
      const actions = [
        ...assignmentActions.map(assignment => ({
          note: assignment.notes,
          createdAt: assignment.createdAt
        })),
        ...visitActions.map(visit => ({
          note: visit.notes,
          createdAt: visit.createdAt
        }))
      ].sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());

      // Group by month
      const monthlyData: { [month: string]: { [key in ActivityType]: number } } = {};

      actions.forEach(action => {
        const month = action.createdAt.toISOString().substring(0, 7); // YYYY-MM format

        if (!monthlyData[month]) {
          monthlyData[month] = {
            en_casa: 0,
            no_en_casa: 0,
            perros_rejas: 0,
            no_llamar: 0,
            no_trespassing: 0
          };
        }

        const activityType = this.mapActionToActivityType(action.note || '');
        if (activityType) {
          monthlyData[month][activityType]++;
        }
      });

      // Convert to array format
      return Object.entries(monthlyData).map(([month, activities]) => ({
        month,
        activities,
        total: Object.values(activities).reduce((sum, count) => sum + count, 0)
      })).sort((a, b) => a.month.localeCompare(b.month));

    } catch (error) {
      console.error('Error getting activity trends:', error);
      return [];
    }
  }
}
