// Territory Validation Service Tests
// Tests for territory data validation, cleaning, and conflict resolution

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ValidationService } from '@/services/territories/ValidationService';
import { DataCleaningService } from '@/services/territories/DataCleaningService';
import { DuplicateDetectionService } from '@/services/territories/DuplicateDetectionService';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    territory: {
      findFirst: jest.fn(),
      findMany: jest.fn()
    }
  }
}));

// Mock services
jest.mock('@/services/territories/DataCleaningService', () => ({
  DataCleaningService: {
    cleanTerritoryData: jest.fn()
  }
}));

jest.mock('@/services/territories/DuplicateDetectionService', () => ({
  DuplicateDetectionService: {
    checkForDuplicates: jest.fn()
  }
}));

describe('ValidationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateTerritory', () => {
    const mockTerritoryData = {
      territoryNumber: 'A-001',
      address: '123 Main Street',
      notes: 'Test notes'
    };

    beforeEach(() => {
      // Mock DataCleaningService
      (DataCleaningService.cleanTerritoryData as jest.Mock).mockReturnValue({
        territoryNumber: { cleaned: 'A-001', changes: [] },
        address: { cleaned: '123 Main St', changes: [{ type: 'abbreviation', description: 'Applied abbreviation', before: 'Street', after: 'St' }] },
        notes: { cleaned: 'Test notes', changes: [] }
      });

      // Mock DuplicateDetectionService
      (DuplicateDetectionService.checkForDuplicates as jest.Mock).mockResolvedValue([]);
    });

    it('should validate territory successfully with no errors', async () => {
      const result = await ValidationService.validateTerritory(mockTerritoryData, '1441');

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.cleaned.territoryNumber).toBe('A-001');
      expect(result.cleaned.address).toBe('123 Main St');
      expect(result.summary.cleanedFields).toBe(1);
    });

    it('should detect format validation errors', async () => {
      const invalidData = {
        territoryNumber: '', // Empty territory number
        address: '123 Main Street'
      };

      const result = await ValidationService.validateTerritory(invalidData, '1441');

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.field === 'territoryNumber')).toBe(true);
    });

    it('should detect uniqueness violations', async () => {
      const { prisma } = require('@/lib/prisma');
      prisma.territory.findFirst.mockResolvedValue({
        id: 'existing-id',
        territoryNumber: 'A-001',
        address: 'Different address'
      });

      const result = await ValidationService.validateTerritory(mockTerritoryData, '1441');

      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.errorType === 'uniqueness')).toBe(true);
    });

    it('should detect duplicates', async () => {
      const mockDuplicates = [{
        id: 'dup-1',
        territory1: { territoryNumber: 'A-001', address: '123 Main St' },
        territory2: { territoryNumber: 'A-002', address: '123 Main Street' },
        similarityScore: 0.95,
        matchedFields: ['address'],
        confidence: 'high' as const,
        suggestedAction: 'merge' as const,
        isResolved: false
      }];

      (DuplicateDetectionService.checkForDuplicates as jest.Mock).mockResolvedValue(mockDuplicates);

      const result = await ValidationService.validateTerritory(mockTerritoryData, '1441');

      expect(result.duplicates).toHaveLength(1);
      expect(result.duplicates[0].confidence).toBe('high');
    });

    it('should handle validation rule loading errors gracefully', async () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await ValidationService.validateTerritory(mockTerritoryData, 'invalid-congregation');

      expect(result).toBeDefined();
      expect(result.isValid).toBeDefined();

      consoleSpy.mockRestore();
    });
  });

  describe('validateTerritories', () => {
    it('should validate multiple territories', async () => {
      const territories = [
        { territoryNumber: 'A-001', address: '123 Main St' },
        { territoryNumber: 'A-002', address: '456 Oak Ave' },
        { territoryNumber: 'A-003', address: '789 Pine Rd' }
      ];

      // Mock DataCleaningService for each territory
      (DataCleaningService.cleanTerritoryData as jest.Mock).mockReturnValue({
        territoryNumber: { cleaned: 'A-001', changes: [] },
        address: { cleaned: '123 Main St', changes: [] },
        notes: { cleaned: '', changes: [] }
      });

      (DuplicateDetectionService.checkForDuplicates as jest.Mock).mockResolvedValue([]);

      const results = await ValidationService.validateTerritories(territories, '1441');

      expect(results).toHaveLength(3);
      expect(results.every(r => r.isValid)).toBe(true);
    });
  });

  describe('getValidationSummary', () => {
    it('should calculate validation summary correctly', () => {
      const mockResults = [
        {
          isValid: true,
          errors: [],
          warnings: [],
          cleaned: { territoryNumber: 'A-001', address: '123 Main St' },
          duplicates: [],
          summary: { totalChecked: 1, errorsFound: 0, warningsFound: 0, duplicatesFound: 0, cleanedFields: 1 }
        },
        {
          isValid: false,
          errors: [{ id: 'e1' } as any],
          warnings: [{ id: 'w1' } as any],
          cleaned: { territoryNumber: 'A-002', address: '456 Oak Ave' },
          duplicates: [{ id: 'd1' } as any],
          summary: { totalChecked: 1, errorsFound: 1, warningsFound: 1, duplicatesFound: 1, cleanedFields: 0 }
        }
      ];

      const summary = ValidationService.getValidationSummary(mockResults);

      expect(summary.totalTerritories).toBe(2);
      expect(summary.validTerritories).toBe(1);
      expect(summary.territoriesWithErrors).toBe(1);
      expect(summary.territoriesWithWarnings).toBe(1);
      expect(summary.totalErrors).toBe(1);
      expect(summary.totalWarnings).toBe(1);
      expect(summary.totalDuplicates).toBe(1);
      expect(summary.totalCleanedFields).toBe(1);
    });
  });

  describe('loadValidationRules', () => {
    it('should load default validation rules for congregation', async () => {
      const rules = await ValidationService.loadValidationRules('1441');

      expect(rules).toBeDefined();
      expect(rules.length).toBeGreaterThan(0);
      expect(rules.some(r => r.type === 'format')).toBe(true);
      expect(rules.some(r => r.type === 'uniqueness')).toBe(true);
      expect(rules.some(r => r.type === 'address')).toBe(true);
      expect(rules.some(r => r.type === 'duplicate')).toBe(true);
    });

    it('should cache validation rules', async () => {
      // Load rules twice
      const rules1 = await ValidationService.loadValidationRules('1441');
      const rules2 = await ValidationService.loadValidationRules('1441');

      expect(rules1).toEqual(rules2);
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      const { prisma } = require('@/lib/prisma');
      prisma.territory.findFirst.mockRejectedValue(new Error('Database error'));

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await ValidationService.validateTerritory(
        { territoryNumber: 'A-001', address: '123 Main St' },
        '1441'
      );

      expect(result).toBeDefined();
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle duplicate detection errors gracefully', async () => {
      const { prisma } = require('@/lib/prisma');
      prisma.territory.findMany.mockRejectedValue(new Error('Database error'));

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await ValidationService.validateTerritory(
        { territoryNumber: 'A-001', address: '123 Main St' },
        '1441'
      );

      expect(result.duplicates).toHaveLength(0);
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('validation rule application', () => {
    it('should apply format rules correctly', async () => {
      const invalidData = {
        territoryNumber: 'A-001@#$', // Invalid characters
        address: '123 Main Street'
      };

      (DataCleaningService.cleanTerritoryData as jest.Mock).mockReturnValue({
        territoryNumber: { cleaned: 'A-001', changes: [] },
        address: { cleaned: '123 Main St', changes: [] }
      });

      const result = await ValidationService.validateTerritory(invalidData, '1441');

      expect(result.errors.some(e => e.errorType === 'format')).toBe(true);
    });

    it('should apply address validation rules', async () => {
      const dataWithForbiddenChars = {
        territoryNumber: 'A-001',
        address: '123 Main St <script>'
      };

      const result = await ValidationService.validateTerritory(dataWithForbiddenChars, '1441');

      expect(result.errors.some(e => e.field === 'address')).toBe(true);
    });
  });
});
