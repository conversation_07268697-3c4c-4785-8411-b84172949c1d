'use client';

import React, { useState, useEffect } from 'react';

interface AssignmentStatsProps {
  territoryId: string;
  territoryNumber: string;
}

interface AssignmentStatsData {
  territory: {
    id: string;
    territoryNumber: string;
    address: string;
    status: string;
  };
  summary: {
    totalAssignments: number;
    activeAssignments: number;
    completedAssignments: number;
    overdueAssignments: number;
    cancelledAssignments: number;
    averageDuration: number;
    completionRate: number;
    utilizationRate: number;
  };
  memberStats: Array<{
    memberId: string;
    memberName: string;
    totalAssignments: number;
    completedAssignments: number;
    activeAssignments: number;
    averageDuration: number;
  }>;
  monthlyActivity: Record<string, number>;
  lastAssignment?: {
    assignedAt: string;
    memberName: string;
    status: string;
  };
}

export default function AssignmentStats({ territoryId, territoryNumber }: AssignmentStatsProps) {
  const [data, setData] = useState<AssignmentStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/territories/${territoryId}/assignments/stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al cargar las estadísticas');
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [territoryId]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Estadísticas de Asignaciones
        </h3>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Cargando estadísticas...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Estadísticas de Asignaciones
        </h3>
        <div className="text-center py-8">
          <div className="text-red-600 mb-2">⚠️ Error</div>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={fetchStats}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  const { summary, memberStats, lastAssignment } = data;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        Estadísticas de Asignaciones - Territorio {territoryNumber}
      </h3>

      {/* Summary Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {summary.totalAssignments}
          </div>
          <div className="text-sm text-gray-600">Total Asignaciones</div>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {summary.completedAssignments}
          </div>
          <div className="text-sm text-gray-600">Completadas</div>
        </div>
        
        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">
            {summary.activeAssignments}
          </div>
          <div className="text-sm text-gray-600">Activas</div>
        </div>
        
        <div className="bg-red-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-red-600">
            {summary.overdueAssignments}
          </div>
          <div className="text-sm text-gray-600">Vencidas</div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Duración Promedio</h4>
          <div className="text-xl font-semibold text-gray-700">
            {summary.averageDuration > 0 ? `${summary.averageDuration} días` : 'N/A'}
          </div>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Tasa de Completación</h4>
          <div className="text-xl font-semibold text-gray-700">
            {summary.completionRate}%
          </div>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Utilización</h4>
          <div className="text-xl font-semibold text-gray-700">
            {summary.utilizationRate}%
          </div>
        </div>
      </div>

      {/* Last Assignment */}
      {lastAssignment && (
        <div className="mb-8">
          <h4 className="font-medium text-gray-900 mb-3">Última Asignación</h4>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">
                  {lastAssignment.memberName}
                </div>
                <div className="text-sm text-gray-600">
                  {new Date(lastAssignment.assignedAt).toLocaleDateString('es-ES')}
                </div>
              </div>
              <div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  lastAssignment.status === 'active' ? 'bg-blue-100 text-blue-800' :
                  lastAssignment.status === 'completed' ? 'bg-green-100 text-green-800' :
                  lastAssignment.status === 'overdue' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {lastAssignment.status === 'active' ? 'Activo' :
                   lastAssignment.status === 'completed' ? 'Completado' :
                   lastAssignment.status === 'overdue' ? 'Vencido' : 'Cancelado'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Member Statistics */}
      {memberStats.length > 0 && (
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Estadísticas por Hermano</h4>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Hermano
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Completadas
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Activas
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duración Promedio
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {memberStats.map((member) => (
                  <tr key={member.memberId}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {member.memberName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {member.totalAssignments}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {member.completedAssignments}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {member.activeAssignments}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {member.averageDuration > 0 ? `${member.averageDuration} días` : 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* No Data Message */}
      {summary.totalAssignments === 0 && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">📊</div>
          <p className="text-gray-600">
            No hay datos de asignaciones para mostrar estadísticas
          </p>
        </div>
      )}
    </div>
  );
}
