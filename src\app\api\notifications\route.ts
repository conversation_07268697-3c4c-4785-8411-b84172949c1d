/**
 * Notifications API Endpoint
 * 
 * Handles CRUD operations for congregation notifications and communications.
 * Provides notification management, delivery, and tracking functionality.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { 
  CommunicationService, 
  NotificationCategory, 
  NotificationPriority,
  DeliveryMethod,
  CreateNotificationData,
  NotificationFilters
} from '@/lib/services/communicationService';

// Validation schemas
const CreateNotificationSchema = z.object({
  recipientId: z.string().optional(),
  recipientIds: z.array(z.string()).optional(),
  senderId: z.string().optional(),
  title: z.string().min(1, 'Notification title is required').max(255, 'Title too long'),
  message: z.string().min(1, 'Notification message is required'),
  category: z.nativeEnum(NotificationCategory),
  priority: z.nativeEnum(NotificationPriority).optional(),
  deliveryMethod: z.array(z.nativeEnum(DeliveryMethod)).optional(),
  scheduledFor: z.string().regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/).optional(),
  metadata: z.record(z.unknown()).optional(),
});

const NotificationFiltersSchema = z.object({
  category: z.nativeEnum(NotificationCategory).optional(),
  priority: z.nativeEnum(NotificationPriority).optional(),
  senderId: z.string().optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  isRead: z.string().transform(val => val === 'true').optional(),
  search: z.string().optional(),
  limit: z.string().transform(val => parseInt(val, 10)).optional(),
  offset: z.string().transform(val => parseInt(val, 10)).optional(),
});

/**
 * GET /api/notifications
 * Retrieve notifications for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = NotificationFiltersSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { 
      category, 
      priority, 
      senderId, 
      startDate, 
      endDate, 
      isRead, 
      search, 
      limit = 50, 
      offset = 0 
    } = validationResult.data;

    // Build filters
    const filters: NotificationFilters = {};
    if (category) filters.category = category;
    if (priority) filters.priority = priority;
    if (senderId) filters.senderId = senderId;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);
    if (isRead !== undefined) filters.isRead = isRead;
    if (search) filters.search = search;

    // Get notifications for the authenticated user
    const notifications = await CommunicationService.getNotifications(
      member.congregationId,
      member.id,
      filters,
      limit,
      offset
    );

    return NextResponse.json({
      success: true,
      notifications,
      count: notifications.length,
      filters: {
        category,
        priority,
        senderId,
        startDate,
        endDate,
        isRead,
        search,
      },
    });

  } catch (error) {
    console.error('Notifications GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve notifications',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/notifications
 * Create a new notification
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can create notifications
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create notifications' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CreateNotificationSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid notification data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const notificationData = validationResult.data;

    // Convert scheduled date string to Date object if provided
    const createData: CreateNotificationData = {
      ...notificationData,
      senderId: member.id, // Set sender to authenticated user
      ...(notificationData.scheduledFor && { 
        scheduledFor: new Date(notificationData.scheduledFor) 
      }),
    };

    // Create notification(s)
    const notifications = await CommunicationService.createNotification(
      member.congregationId,
      createData
    );

    return NextResponse.json({
      success: true,
      notifications,
      count: notifications.length,
      message: 'Notification(s) created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Notification creation error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to create notification',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/notifications
 * Mark notification as read
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { notificationId } = body;

    if (!notificationId) {
      return NextResponse.json(
        { error: 'Notification ID is required' },
        { status: 400 }
      );
    }

    // Mark notification as read
    const notification = await CommunicationService.markNotificationAsRead(
      member.congregationId,
      notificationId,
      member.id
    );

    return NextResponse.json({
      success: true,
      notification,
      message: 'Notification marked as read',
    });

  } catch (error) {
    console.error('Notification update error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to update notification',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
