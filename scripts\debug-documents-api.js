/**
 * Debug Documents API
 * Test the documents API and debug why it's not returning letters
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const BASE_URL = 'http://localhost:3001';

async function debugDocumentsAPI() {
  try {
    console.log('🔍 Debugging Documents API...');

    // Step 1: Check database directly
    console.log('\n📊 Step 1: Direct database query...');
    const allLetters = await prisma.letter.findMany({
      where: {
        isActive: true
      }
    });
    console.log(`📄 Total active letters in DB: ${allLetters.length}`);

    if (allLetters.length > 0) {
      const sample = allLetters[0];
      console.log(`📄 Sample letter congregation ID: "${sample.congregationId}"`);
      console.log(`📄 Sample letter category: "${sample.category}"`);
      console.log(`📄 Sample letter visibility: "${sample.visibility}"`);
    }

    // Step 2: Test authentication
    console.log('\n🔐 Step 2: Testing authentication...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/congregation-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        congregationId: '1441',
        pin: '1234',
        memberId: '1' // Richard Rubi - coordinator
      }),
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ Authentication successful');
    console.log(`📄 User congregation ID: "${loginData.user.congregationId}"`);
    console.log(`📄 User role: "${loginData.user.role}"`);

    // Step 3: Test documents API with debug
    console.log('\n📄 Step 3: Testing documents API...');
    const response = await fetch(`${BASE_URL}/api/documents`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    console.log(`📡 API response status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ API error: ${errorText}`);
      return;
    }

    const data = await response.json();
    console.log(`📊 Documents returned: ${data.documents?.length || 0}`);

    if (data.documents && data.documents.length > 0) {
      console.log('📄 Sample document:');
      console.log(JSON.stringify(data.documents[0], null, 2));
    }

    // Step 4: Test with specific congregation filter
    console.log('\n🔍 Step 4: Testing congregation filter...');
    const congregationLetters = await prisma.letter.findMany({
      where: {
        congregationId: '1441',
        isActive: true
      }
    });
    console.log(`📄 Letters for congregation 1441: ${congregationLetters.length}`);

    // Step 5: Test the exact query from the service
    console.log('\n🔍 Step 5: Testing service query...');
    const serviceQuery = {
      congregationId: '1441',
      isActive: true,
    };

    // Add visibility filter for non-elder users
    const userRole = loginData.user.role;
    if (userRole && userRole !== 'elder') {
      const allowedVisibilities = getAllowedVisibilities(userRole);
      serviceQuery.visibility = { in: allowedVisibilities };
      console.log(`📄 Visibility filter for ${userRole}: ${allowedVisibilities.join(', ')}`);
    }

    const serviceResults = await prisma.letter.findMany({
      where: serviceQuery,
      orderBy: [
        { priority: 'desc' },
        { uploadDate: 'desc' },
      ],
      take: 50,
      skip: 0,
    });

    console.log(`📄 Service query results: ${serviceResults.length}`);

    await prisma.$disconnect();

  } catch (error) {
    console.error('❌ Error:', error.message);
    await prisma.$disconnect();
    process.exit(1);
  }
}

function getAllowedVisibilities(userRole) {
  switch (userRole) {
    case 'elder':
      return ['ALL_MEMBERS', 'ELDERS_ONLY', 'MINISTERIAL_SERVANTS_PLUS', 'COORDINATORS_ONLY', 'CUSTOM'];
    case 'ministerial_servant':
      return ['ALL_MEMBERS', 'MINISTERIAL_SERVANTS_PLUS'];
    default:
      return ['ALL_MEMBERS'];
  }
}

debugDocumentsAPI();
