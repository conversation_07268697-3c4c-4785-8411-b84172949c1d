# Unified Project Structure

Based on the chosen monorepo approach with npm workspaces and the local infrastructure requirements, here's the complete project structure for the Hermanos multi-congregation app:

```plaintext
hermanos/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml            # Continuous integration
│       ├── test.yaml          # Automated testing
│       └── deploy.yaml        # Deployment pipeline
├── apps/                       # Application packages
│   └── web/                    # Next.js frontend application
│       ├── src/
│       │   ├── app/            # Next.js App Router
│       │   │   ├── (auth)/     # Auth route group
│       │   │   │   ├── login/
│       │   │   │   │   └── page.tsx
│       │   │   │   └── layout.tsx
│       │   │   ├── (dashboard)/ # Protected dashboard routes
│       │   │   │   ├── dashboard/
│       │   │   │   │   └── page.tsx
│       │   │   │   ├── meetings/
│       │   │   │   │   ├── page.tsx
│       │   │   │   │   ├── [id]/
│       │   │   │   │   │   └── page.tsx
│       │   │   │   │   └── import/
│       │   │   │   │       └── page.tsx
│       │   │   │   ├── field-service/
│       │   │   │   │   ├── page.tsx
│       │   │   │   │   └── reports/
│       │   │   │   │       └── page.tsx
│       │   │   │   ├── tasks/
│       │   │   │   │   └── page.tsx
│       │   │   │   ├── letters/
│       │   │   │   │   └── page.tsx
│       │   │   │   ├── events/
│       │   │   │   │   └── page.tsx
│       │   │   │   ├── admin/
│       │   │   │   │   ├── page.tsx
│       │   │   │   │   ├── members/
│       │   │   │   │   │   └── page.tsx
│       │   │   │   │   └── settings/
│       │   │   │   │       └── page.tsx
│       │   │   │   └── layout.tsx
│       │   │   ├── api/         # API routes
│       │   │   │   ├── trpc/
│       │   │   │   │   └── [trpc]/
│       │   │   │   │       └── route.ts
│       │   │   │   ├── auth/
│       │   │   │   │   ├── login/
│       │   │   │   │   │   └── route.ts
│       │   │   │   │   └── refresh/
│       │   │   │   │       └── route.ts
│       │   │   │   ├── upload/
│       │   │   │   │   └── letters/
│       │   │   │   │       └── route.ts
│       │   │   │   └── health/
│       │   │   │       └── route.ts
│       │   │   ├── globals.css
│       │   │   ├── layout.tsx
│       │   │   └── page.tsx
│       │   ├── components/      # UI components
│       │   │   ├── ui/          # Base UI components
│       │   │   │   ├── button.tsx
│       │   │   │   ├── card.tsx
│       │   │   │   ├── form.tsx
│       │   │   │   └── input.tsx
│       │   │   ├── dashboard/   # Dashboard components
│       │   │   │   ├── dashboard-card.tsx
│       │   │   │   ├── dashboard-grid.tsx
│       │   │   │   └── welcome-header.tsx
│       │   │   ├── meetings/    # Meeting components
│       │   │   │   ├── meeting-list.tsx
│       │   │   │   ├── meeting-part-assignment.tsx
│       │   │   │   └── jw-org-import.tsx
│       │   │   ├── field-service/ # Field service components
│       │   │   │   ├── time-entry-form.tsx
│       │   │   │   ├── monthly-report.tsx
│       │   │   │   └── service-history.tsx
│       │   │   ├── auth/         # Auth components
│       │   │   │   ├── login-form.tsx
│       │   │   │   └── congregation-selector.tsx
│       │   │   └── layout/       # Layout components
│       │   │       ├── header.tsx
│       │   │       ├── navigation.tsx
│       │   │       └── mobile-nav.tsx
│       │   ├── hooks/           # Custom React hooks
│       │   │   ├── use-auth.ts
│       │   │   ├── use-congregation.ts
│       │   │   ├── use-meetings.ts
│       │   │   └── use-field-service.ts
│       │   ├── lib/             # Frontend utilities
│       │   │   ├── trpc.ts      # tRPC client setup
│       │   │   ├── auth.ts      # Auth utilities
│       │   │   ├── utils.ts     # General utilities
│       │   │   └── validations.ts # Form validations
│       │   ├── stores/          # Zustand stores
│       │   │   ├── auth-store.ts
│       │   │   ├── congregation-store.ts
│       │   │   ├── ui-store.ts
│       │   │   └── meeting-store.ts
│       │   └── styles/          # Global styles
│       │       ├── globals.css
│       │       └── components.css
│       ├── server/              # Backend services
│       │   ├── api/
│       │   │   ├── routers/     # tRPC routers
│       │   │   │   ├── auth.ts
│       │   │   │   ├── meetings.ts
│       │   │   │   ├── members.ts
│       │   │   │   ├── field-service.ts
│       │   │   │   ├── tasks.ts
│       │   │   │   ├── letters.ts
│       │   │   │   └── events.ts
│       │   │   ├── root.ts      # Main tRPC router
│       │   │   └── trpc.ts      # tRPC setup
│       │   ├── services/        # Business logic services
│       │   │   ├── auth-service.ts
│       │   │   ├── meeting-service.ts
│       │   │   ├── jw-org-service.ts
│       │   │   ├── file-service.ts
│       │   │   ├── member-service.ts
│       │   │   └── field-service-service.ts
│       │   ├── middleware/      # API middleware
│       │   │   ├── auth.ts
│       │   │   ├── tenant.ts
│       │   │   ├── validation.ts
│       │   │   └── error-handler.ts
│       │   ├── lib/             # Backend utilities
│       │   │   ├── db.ts        # Prisma client
│       │   │   ├── auth.ts      # JWT utilities
│       │   │   ├── cache.ts     # Redis cache client
│       │   │   ├── file-storage.ts # Local file operations
│       │   │   └── jw-org-scraper.ts # Preserved wol-scraper logic
│       │   └── types/           # Backend TypeScript types
│       │       ├── auth.ts
│       │       ├── meetings.ts
│       │       ├── api.ts
│       │       └── database.ts
│       ├── public/              # Static assets
│       │   ├── uploads/         # Local file storage
│       │   │   ├── letters/     # PDF documents
│       │   │   └── images/      # Image uploads
│       │   ├── icons/           # App icons
│       │   ├── images/          # Static images
│       │   │   └── ocean-bg.jpg # Dashboard background
│       │   └── favicon.ico
│       ├── prisma/              # Database schema and migrations
│       │   ├── schema.prisma    # Prisma schema definition
│       │   ├── migrations/      # Database migrations
│       │   │   └── 001_initial_schema/
│       │   │       └── migration.sql
│       │   └── seed.ts          # Database seeding
│       ├── tests/               # Application tests
│       │   ├── __mocks__/       # Test mocks
│       │   ├── components/      # Component tests
│       │   ├── api/             # API endpoint tests
│       │   ├── services/        # Service layer tests
│       │   ├── e2e/             # End-to-end tests
│       │   └── setup.ts         # Test setup
│       ├── .env.example         # Environment template
│       ├── .env.local           # Local environment (gitignored)
│       ├── next.config.js       # Next.js configuration
│       ├── tailwind.config.js   # Tailwind CSS configuration
│       ├── tsconfig.json        # TypeScript configuration
│       ├── package.json         # App dependencies
│       └── README.md            # App documentation
├── packages/                    # Shared packages
│   ├── shared/                  # Shared types and utilities
│   │   ├── src/
│   │   │   ├── types/           # Shared TypeScript interfaces
│   │   │   │   ├── congregation.ts
│   │   │   │   ├── member.ts
│   │   │   │   ├── meeting.ts
│   │   │   │   ├── field-service.ts
│   │   │   │   └── index.ts
│   │   │   ├── constants/       # Shared constants
│   │   │   │   ├── roles.ts
│   │   │   │   ├── meeting-types.ts
│   │   │   │   └── index.ts
│   │   │   ├── utils/           # Shared utilities
│   │   │   │   ├── date.ts
│   │   │   │   ├── validation.ts
│   │   │   │   └── index.ts
│   │   │   └── index.ts
│   │   ├── package.json
│   │   └── tsconfig.json
│   └── config/                  # Shared configuration
│       ├── eslint/
│       │   ├── base.js
│       │   ├── next.js
│       │   └── react.js
│       ├── typescript/
│       │   ├── base.json
│       │   ├── next.json
│       │   └── react.json
│       ├── tailwind/
│       │   └── base.js
│       └── jest/
│           └── base.js
├── scripts/                     # Build and utility scripts
│   ├── build.sh                # Build script
│   ├── dev.sh                  # Development startup
│   ├── test.sh                 # Test runner
│   ├── db-migrate.sh           # Database migration
│   ├── db-seed.sh              # Database seeding
│   ├── backup-db.sh            # Database backup
│   └── setup.sh                # Initial project setup
├── docs/                        # Project documentation
│   ├── prd.md                  # Product Requirements Document
│   ├── hermanos-architecture.md # This architecture document
│   ├── api/                    # API documentation
│   │   ├── authentication.md
│   │   ├── meetings.md
│   │   └── field-service.md
│   ├── deployment/             # Deployment guides
│   │   ├── local-setup.md
│   │   └── production.md
│   └── migration/              # Migration documentation
│       ├── mysql-to-postgres.md
│       └── data-migration.md
├── database/                    # Database setup and migrations
│   ├── init/                   # Initial database setup
│   │   ├── 001-create-database.sql
│   │   └── 002-create-extensions.sql
│   ├── migrations/             # Manual migrations (if needed)
│   └── backups/                # Database backup storage
├── .env.example                # Environment template
├── .env                        # Local environment (gitignored)
├── .gitignore                  # Git ignore rules
├── package.json                # Root package.json with workspaces
├── tsconfig.json               # Root TypeScript configuration
├── docker-compose.yml          # Local development services
├── Dockerfile                  # Production container (if needed)
└── README.md                   # Project documentation
```
