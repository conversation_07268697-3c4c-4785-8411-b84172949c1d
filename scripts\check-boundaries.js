const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkBoundaries() {
  try {
    console.log('🔍 Checking territory boundaries...\n');
    
    const territories = await prisma.territory.findMany({
      where: { congregationId: '1441' },
      select: { 
        territoryNumber: true, 
        boundaries: true,
        address: true
      },
      take: 5,
      orderBy: { territoryNumber: 'asc' }
    });
    
    console.log('📋 Sample territories:');
    territories.forEach(t => {
      console.log(`Territory ${t.territoryNumber}:`);
      console.log(`  Address: ${t.address?.split('\n')[0] || 'No address'}`);
      console.log(`  Boundaries: ${t.boundaries ? 'YES' : 'NO'}`);
      if (t.boundaries) {
        console.log(`  Boundary type: ${t.boundaries.type || 'Unknown'}`);
      }
      console.log('');
    });
    
    const withBoundaries = await prisma.territory.count({
      where: { 
        congregationId: '1441',
        boundaries: { not: null }
      }
    });
    
    const total = await prisma.territory.count({
      where: { congregationId: '1441' }
    });
    
    console.log(`📊 Summary: ${withBoundaries}/${total} territories have boundaries`);
    
    if (withBoundaries === 0) {
      console.log('\n⚠️  No territories have boundary data yet.');
      console.log('💡 We need to add boundary data for the zoom-to-territory feature to work properly.');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkBoundaries();
