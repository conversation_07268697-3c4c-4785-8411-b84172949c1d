# Technical Assumptions

## Repository Structure: Monorepo

The territories management system will be integrated into the existing Hermanos App monorepo structure, adding new components, API routes, and database models to the current Next.js application.

## Service Architecture

**Integrated Monolith Architecture**: The territories management functionality will be built as an integrated feature within the existing Hermanos App Next.js architecture, following the established patterns:
- Next.js App Router for API routes (`/api/territories/*`)
- Prisma ORM for database operations with the existing PostgreSQL database
- Server-side components and client-side React components
- Integration with existing authentication and authorization middleware
- Dedicated admin card for "Territorios" separate from Field Service admin
- Member interface components following Field Service UI patterns

## Testing Requirements

**Unit + Integration Testing**: Following the existing testing patterns in the codebase:
- Jest for unit testing of utility functions and services
- React Testing Library for component testing
- API route testing for territory management endpoints
- Database integration testing with test database
- Manual testing convenience methods for territory import and assignment workflows

## Additional Technical Assumptions and Requests

**Database Integration**:
- Extend existing PostgreSQL database with territory-related tables (territories table already exists)
- Use existing Prisma schema and migration patterns
- Maintain multi-tenant isolation using congregation_id

**Authentication & Authorization**:
- Integrate with existing JWT-based authentication system
- Use existing role-based access control (<PERSON>, Ministerial Servant, Publisher permissions)
- Leverage existing middleware for route protection

**Frontend Framework**:
- Next.js 14+ with App Router (matching existing codebase)
- TypeScript for type safety
- Tailwind CSS for styling (consistent with existing UI)
- React Hook Form for form management
- Zustand for state management if needed

**External Integrations**:
- MapLibre GL JS for territory visualization with OpenStreetMap tiles
- Excel file parsing using existing Node.js libraries (xlsx or similar)
- Integration with existing Hermanos App notification system patterns
- Geocoding service for address-to-coordinate conversion

**Development Tools**:
- ESLint and Prettier (matching existing configuration)
- Existing development scripts and build processes
- Integration with current backup and deployment workflows
