'use client';

import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  MapPinIcon,
  CheckIcon,
  XMarkIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

interface Member {
  id: string;
  name: string;
  role: string;
  assignedTerritories?: number;
}

interface Territory {
  id: string;
  territoryNumber: string;
  address: string;
  status: string;
}

interface TerritoryAssignmentProps {
  onAssignmentComplete?: () => void;
}

export default function TerritoryAssignment({ onAssignmentComplete }: TerritoryAssignmentProps) {
  const [members, setMembers] = useState<Member[]>([]);
  const [availableTerritories, setAvailableTerritories] = useState<Territory[]>([]);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [selectedTerritories, setSelectedTerritories] = useState<string[]>([]);
  const [memberSearch, setMemberSearch] = useState('');
  const [territorySearch, setTerritorySearch] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadMembers();
    loadAvailableTerritories();
  }, []);

  const loadMembers = async () => {
    try {
      const rawToken = localStorage.getItem('hermanos_token');
      if (!rawToken) {
        throw new Error('No authentication token found');
      }

      // Clean token - remove any existing Bearer prefix if present
      let token = rawToken.trim();
      if (token.toLowerCase().startsWith('bearer ')) {
        token = token.substring(7);
      }

      const response = await fetch('/api/admin/members', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Error al cargar miembros');
      }

      const result = await response.json();
      // Get members with territory count from assignments API
      const assignmentsResponse = await fetch('/api/territories/assignments', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      let membersWithCounts = result.data?.members || result.members || [];

      if (assignmentsResponse.ok) {
        const assignmentsResult = await assignmentsResponse.json();
        const membersWithAssignments = assignmentsResult.data?.members || [];

        // Map assignment counts to members
        membersWithCounts = membersWithCounts.map((member: Member) => {
          const memberWithAssignments = membersWithAssignments.find((m: any) => m.id === member.id);
          return {
            ...member,
            assignedTerritories: memberWithAssignments?.activeAssignments || 0
          };
        });
      }

      setMembers(membersWithCounts);
    } catch (err) {
      console.error('Error loading members:', err);
      setError(err instanceof Error ? err.message : 'Error al cargar miembros');
    }
  };

  const loadAvailableTerritories = async () => {
    try {
      const rawToken = localStorage.getItem('hermanos_token');
      if (!rawToken) {
        throw new Error('No authentication token found');
      }

      // Clean token - remove any existing Bearer prefix if present
      let token = rawToken.trim();
      if (token.toLowerCase().startsWith('bearer ')) {
        token = token.substring(7);
      }

      const response = await fetch('/api/territories?status=available', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Error al cargar territorios');
      }

      const result = await response.json();
      setAvailableTerritories(result.territories || []);
    } catch (err) {
      console.error('Error loading territories:', err);
      setError(err instanceof Error ? err.message : 'Error al cargar territorios disponibles');
    }
  };

  const handleAssignTerritories = async () => {
    if (!selectedMember || selectedTerritories.length === 0) {
      alert('Por favor selecciona un miembro y al menos un territorio');
      return;
    }

    try {
      setIsLoading(true);
      const rawToken = localStorage.getItem('hermanos_token');
      if (!rawToken) {
        throw new Error('No authentication token found');
      }

      // Clean token - remove any existing Bearer prefix if present
      let token = rawToken.trim();
      if (token.toLowerCase().startsWith('bearer ')) {
        token = token.substring(7);
      }

      const response = await fetch('/api/territories/assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          memberId: selectedMember.id,
          territoryIds: selectedTerritories
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Error al asignar territorios');
      }

      const result = await response.json();

      // Show success message
      alert(`${selectedTerritories.length} territorio(s) asignado(s) exitosamente a ${selectedMember.name}`);

      // Reset form
      setSelectedMember(null);
      setSelectedTerritories([]);
      setMemberSearch('');
      setTerritorySearch('');

      // Reload available territories
      await loadAvailableTerritories();

      // Notify parent component
      onAssignmentComplete?.();

    } catch (err) {
      alert(err instanceof Error ? err.message : 'Error al asignar territorios');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTerritorySelection = (territoryId: string) => {
    setSelectedTerritories(prev =>
      prev.includes(territoryId)
        ? prev.filter(id => id !== territoryId)
        : [...prev, territoryId]
    );
  };

  // Helper functions for role grouping
  const getRoleGroup = (role: string) => {
    if (['elder', 'overseer_coordinator', 'coordinator'].includes(role)) {
      return 1; // Elders
    }
    if (role === 'ministerial_servant') {
      return 2; // Ministerial Servants
    }
    return 3; // Publishers and others
  };

  const getRoleGroupLabel = (role: string) => {
    const group = getRoleGroup(role);
    switch (group) {
      case 1: return 'Ancianos';
      case 2: return 'Siervos Ministeriales';
      case 3: return 'Publicadores';
      default: return 'Otros';
    }
  };

  const filteredMembers = members
    .filter(member =>
      member.name.toLowerCase().includes(memberSearch.toLowerCase())
    )
    .sort((a, b) => {
      // Define role priority: elders first, then ministerial servants, then publishers
      const roleOrder = {
        'elder': 1,
        'overseer_coordinator': 1,
        'coordinator': 1,
        'ministerial_servant': 2,
        'publisher': 3,
        'auxiliary_pioneer': 3,
        'regular_pioneer': 3,
        'special_pioneer': 3
      };

      const aOrder = roleOrder[a.role] || 4; // Unknown roles go last
      const bOrder = roleOrder[b.role] || 4;

      // First sort by role priority
      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }

      // Then sort alphabetically by name within the same role
      return a.name.localeCompare(b.name);
    });

  const filteredTerritories = availableTerritories.filter(territory =>
    territory.territoryNumber.toLowerCase().includes(territorySearch.toLowerCase()) ||
    territory.address.toLowerCase().includes(territorySearch.toLowerCase())
  );

  return (
    <div className="p-4 lg:p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Asignación de Territorios</h2>
        <p className="text-gray-600">Selecciona un miembro (izquierda) y los territorios (derecha) que deseas asignar</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6">
        {/* Member Selection - Left Column */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <UserIcon className="h-5 w-5 mr-2" />
            Seleccionar Miembro
          </h3>

          {/* Member Search */}
          <div className="relative mb-4">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar miembro..."
              value={memberSearch}
              onChange={(e) => setMemberSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Selected Member Display */}
          {selectedMember && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-blue-900">{selectedMember.name}</div>
                  <div className="text-sm text-blue-700">{selectedMember.assignedTerritories || 0} asignados</div>
                </div>
                <button
                  onClick={() => setSelectedMember(null)}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}

          {/* Member List */}
          <div className="h-80 overflow-y-auto space-y-1">
            {filteredMembers.map((member, index) => {
              // Check if we need to show a role header
              const prevMember = index > 0 ? filteredMembers[index - 1] : null;
              const showRoleHeader = !prevMember || getRoleGroup(member.role) !== getRoleGroup(prevMember.role);

              return (
                <div key={member.id}>
                  {/* Role Header */}
                  {showRoleHeader && (
                    <div className="px-2 py-1 text-xs font-medium text-gray-500 uppercase tracking-wide bg-gray-100 rounded-md mt-2 mb-1">
                      {getRoleGroupLabel(member.role)}
                    </div>
                  )}

                  {/* Member Button */}
                  <button
                    onClick={() => setSelectedMember(member)}
                    className={`w-full text-left p-3 rounded-md border transition-colors ${
                      selectedMember?.id === member.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{member.name}</div>
                      <div className="text-sm text-gray-500">
                        {member.assignedTerritories || 0}
                      </div>
                    </div>
                  </button>
                </div>
              );
            })}
          </div>

          {filteredMembers.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              No se encontraron miembros
            </div>
          )}
        </div>

        {/* Territory Selection - Right Column */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <MapPinIcon className="h-5 w-5 mr-2" />
            Seleccionar Territorios ({selectedTerritories.length} seleccionados)
          </h3>

          {/* Territory Search */}
          <div className="relative mb-4">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar territorio..."
              value={territorySearch}
              onChange={(e) => setTerritorySearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Territory List - Simplified */}
          <div className="h-80 overflow-y-auto">
            <div className="grid grid-cols-4 sm:grid-cols-5 lg:grid-cols-6 gap-2">
              {filteredTerritories.map((territory) => (
                <button
                  key={territory.id}
                  onClick={() => toggleTerritorySelection(territory.id)}
                  className={`p-3 rounded-md border transition-colors text-center ${
                    selectedTerritories.includes(territory.id)
                      ? 'border-green-500 bg-green-50 text-green-800'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  title={territory.address}
                >
                  <div className="font-bold text-base">
                    {territory.territoryNumber}
                  </div>
                  {selectedTerritories.includes(territory.id) && (
                    <CheckIcon className="h-4 w-4 text-green-600 mx-auto mt-1" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {filteredTerritories.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              No hay territorios disponibles
            </div>
          )}
        </div>
      </div>

      {/* Assignment Summary and Action */}
      <div className="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3">Resumen de Asignación</h4>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
          <div>
            <span className="text-sm text-gray-600">Miembro seleccionado:</span>
            <div className="font-medium">
              {selectedMember ? `${selectedMember.name} (${selectedMember.assignedTerritories || 0})` : 'Ninguno'}
            </div>
          </div>
          <div>
            <span className="text-sm text-gray-600">Territorios seleccionados:</span>
            <div className="font-medium">
              {selectedTerritories.length > 0
                ? `${selectedTerritories.length} territorio(s)`
                : 'Ninguno'
              }
            </div>
          </div>
        </div>

        {selectedTerritories.length > 0 && (
          <div className="mb-4">
            <span className="text-sm text-gray-600">Territorios:</span>
            <div className="flex flex-wrap gap-2 mt-1">
              {selectedTerritories.map(territoryId => {
                const territory = availableTerritories.find(t => t.id === territoryId);
                return territory ? (
                  <span
                    key={territoryId}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    {territory.territoryNumber}
                  </span>
                ) : null;
              })}
            </div>
          </div>
        )}

        <button
          onClick={handleAssignTerritories}
          disabled={!selectedMember || selectedTerritories.length === 0 || isLoading}
          className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
            !selectedMember || selectedTerritories.length === 0 || isLoading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Asignando...
            </div>
          ) : (
            'Asignar Territorios'
          )}
        </button>
      </div>


    </div>
  );
}
