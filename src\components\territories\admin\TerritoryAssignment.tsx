'use client';

import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  MapPinIcon,
  CheckIcon,
  XMarkIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

interface Member {
  id: string;
  name: string;
  role: string;
}

interface Territory {
  id: string;
  territoryNumber: string;
  address: string;
  status: string;
}

interface TerritoryAssignmentProps {
  onAssignmentComplete?: () => void;
}

export default function TerritoryAssignment({ onAssignmentComplete }: TerritoryAssignmentProps) {
  const [members, setMembers] = useState<Member[]>([]);
  const [availableTerritories, setAvailableTerritories] = useState<Territory[]>([]);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [selectedTerritories, setSelectedTerritories] = useState<string[]>([]);
  const [memberSearch, setMemberSearch] = useState('');
  const [territorySearch, setTerritorySearch] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadMembers();
    loadAvailableTerritories();
  }, []);

  const loadMembers = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/admin/members', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al cargar miembros');
      }

      const result = await response.json();
      setMembers(result.data.members || []);
    } catch (err) {
      console.error('Error loading members:', err);
      setError('Error al cargar miembros');
    }
  };

  const loadAvailableTerritories = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories?status=available', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al cargar territorios');
      }

      const result = await response.json();
      setAvailableTerritories(result.territories || []);
    } catch (err) {
      console.error('Error loading territories:', err);
      setError('Error al cargar territorios disponibles');
    }
  };

  const handleAssignTerritories = async () => {
    if (!selectedMember || selectedTerritories.length === 0) {
      alert('Por favor selecciona un miembro y al menos un territorio');
      return;
    }

    try {
      setIsLoading(true);
      const token = localStorage.getItem('hermanos_token');

      const response = await fetch('/api/territories/assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          memberId: selectedMember.id,
          territoryIds: selectedTerritories
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al asignar territorios');
      }

      const result = await response.json();
      
      // Show success message
      alert(`${selectedTerritories.length} territorio(s) asignado(s) exitosamente a ${selectedMember.name}`);
      
      // Reset form
      setSelectedMember(null);
      setSelectedTerritories([]);
      setMemberSearch('');
      setTerritorySearch('');
      
      // Reload available territories
      await loadAvailableTerritories();
      
      // Notify parent component
      onAssignmentComplete?.();

    } catch (err) {
      alert(err instanceof Error ? err.message : 'Error al asignar territorios');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTerritorySelection = (territoryId: string) => {
    setSelectedTerritories(prev => 
      prev.includes(territoryId)
        ? prev.filter(id => id !== territoryId)
        : [...prev, territoryId]
    );
  };

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(memberSearch.toLowerCase())
  );

  const filteredTerritories = availableTerritories.filter(territory =>
    territory.territoryNumber.toLowerCase().includes(territorySearch.toLowerCase()) ||
    territory.address.toLowerCase().includes(territorySearch.toLowerCase())
  );

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Asignación de Territorios</h2>
        <p className="text-gray-600">Selecciona un miembro y los territorios que deseas asignar</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Member Selection */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <UserIcon className="h-5 w-5 mr-2" />
            Seleccionar Miembro
          </h3>

          {/* Member Search */}
          <div className="relative mb-4">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar miembro..."
              value={memberSearch}
              onChange={(e) => setMemberSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Selected Member Display */}
          {selectedMember && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-blue-900">{selectedMember.name}</div>
                  <div className="text-sm text-blue-700">{selectedMember.role}</div>
                </div>
                <button
                  onClick={() => setSelectedMember(null)}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}

          {/* Member List */}
          <div className="max-h-64 overflow-y-auto space-y-2">
            {filteredMembers.map((member) => (
              <button
                key={member.id}
                onClick={() => setSelectedMember(member)}
                className={`w-full text-left p-3 rounded-md border transition-colors ${
                  selectedMember?.id === member.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="font-medium">{member.name}</div>
                <div className="text-sm text-gray-600">{member.role}</div>
              </button>
            ))}
          </div>

          {filteredMembers.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              No se encontraron miembros
            </div>
          )}
        </div>

        {/* Territory Selection */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <MapPinIcon className="h-5 w-5 mr-2" />
            Seleccionar Territorios ({selectedTerritories.length} seleccionados)
          </h3>

          {/* Territory Search */}
          <div className="relative mb-4">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar territorio..."
              value={territorySearch}
              onChange={(e) => setTerritorySearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Territory List */}
          <div className="max-h-64 overflow-y-auto space-y-2">
            {filteredTerritories.map((territory) => (
              <button
                key={territory.id}
                onClick={() => toggleTerritorySelection(territory.id)}
                className={`w-full text-left p-3 rounded-md border transition-colors ${
                  selectedTerritories.includes(territory.id)
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Territorio {territory.territoryNumber}</div>
                    <div className="text-sm text-gray-600">{territory.address}</div>
                  </div>
                  {selectedTerritories.includes(territory.id) && (
                    <CheckIcon className="h-5 w-5 text-green-600" />
                  )}
                </div>
              </button>
            ))}
          </div>

          {filteredTerritories.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              No hay territorios disponibles
            </div>
          )}
        </div>
      </div>

      {/* Assignment Summary and Action */}
      <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-3">Resumen de Asignación</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <span className="text-sm text-gray-600">Miembro seleccionado:</span>
            <div className="font-medium">
              {selectedMember ? `${selectedMember.name} (${selectedMember.role})` : 'Ninguno'}
            </div>
          </div>
          <div>
            <span className="text-sm text-gray-600">Territorios seleccionados:</span>
            <div className="font-medium">
              {selectedTerritories.length > 0 
                ? `${selectedTerritories.length} territorio(s)`
                : 'Ninguno'
              }
            </div>
          </div>
        </div>

        {selectedTerritories.length > 0 && (
          <div className="mb-4">
            <span className="text-sm text-gray-600">Territorios:</span>
            <div className="flex flex-wrap gap-2 mt-1">
              {selectedTerritories.map(territoryId => {
                const territory = availableTerritories.find(t => t.id === territoryId);
                return territory ? (
                  <span
                    key={territoryId}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  >
                    {territory.territoryNumber}
                  </span>
                ) : null;
              })}
            </div>
          </div>
        )}

        <button
          onClick={handleAssignTerritories}
          disabled={!selectedMember || selectedTerritories.length === 0 || isLoading}
          className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
            !selectedMember || selectedTerritories.length === 0 || isLoading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Asignando...
            </div>
          ) : (
            'Asignar Territorios'
          )}
        </button>
      </div>
    </div>
  );
}
