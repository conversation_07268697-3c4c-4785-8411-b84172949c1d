#!/usr/bin/env node

/**
 * Systematic Dashboard Visual Analysis
 *
 * This script performs precise comparison between the reference image and current implementation
 * to identify exact visual differences before making any changes.
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class SystematicDashboardAnalyzer {
  constructor() {
    this.referenceImagePath = 'CORAL OESTE APP/dashboard-members.png';
    this.currentScreenshotPath = 'current-dashboard-analysis.png';
    this.analysisResults = {
      dimensions: {},
      layout: {},
      sections: {},
      colors: {},
      spacing: {},
      icons: {},
      differences: []
    };
  }

  async performSystematicAnalysis() {
    console.log('🔍 SYSTEMATIC DASHBOARD VISUAL ANALYSIS');
    console.log('='.repeat(50));

    // Step 1: Verify reference image exists
    console.log('\n📋 STEP 1: Verify Reference Image');
    await this.verifyReferenceImage();

    // Step 2: Capture current implementation
    console.log('\n📸 STEP 2: Capture Current Implementation');
    await this.captureCurrentImplementation();

    // Step 3: Analyze current implementation structure
    console.log('\n🔬 STEP 3: Analyze Current Implementation Structure');
    await this.analyzeCurrentStructure();

    // Step 4: Document specific differences
    console.log('\n📝 STEP 4: Document Specific Visual Differences');
    this.documentSpecificDifferences();

    // Step 5: Generate evidence-based recommendations
    console.log('\n🎯 STEP 5: Generate Evidence-Based Recommendations');
    this.generateRecommendations();

    // Step 6: Create detailed analysis report
    console.log('\n📊 STEP 6: Create Detailed Analysis Report');
    this.createAnalysisReport();
  }

  async verifyReferenceImage() {
    try {
      const referenceExists = fs.existsSync(this.referenceImagePath);
      if (referenceExists) {
        const stats = fs.statSync(this.referenceImagePath);
        console.log(`✅ Reference image found: ${this.referenceImagePath}`);
        console.log(`   Size: ${(stats.size / 1024).toFixed(2)} KB`);
        console.log(`   Modified: ${stats.mtime.toLocaleDateString()}`);
      } else {
        console.log(`❌ Reference image not found at: ${this.referenceImagePath}`);
        console.log('   Please verify the path is correct');
        throw new Error('Reference image not found');
      }
    } catch (error) {
      console.error('Error verifying reference image:', error.message);
      throw error;
    }
  }

  async captureCurrentImplementation() {
    let browser;
    try {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 375, height: 812 } // iPhone X dimensions for mobile-first
      });

      const page = await browser.newPage();

      // Navigate to current implementation
      console.log('   Navigating to current dashboard...');
      await page.goto('http://localhost:3001/login', { waitUntil: 'networkidle0' });

      // Login to access dashboard
      console.log('   Performing login...');
      await page.waitForSelector('input[type="text"]', { timeout: 10000 });
      await page.type('input[type="text"]', '1441');
      await page.type('input[type="password"]', '1441');
      await page.click('button[type="submit"]');

      // Wait for dashboard to load
      console.log('   Waiting for dashboard to load...');
      await page.waitForSelector('[data-testid="dashboard-grid"]', { timeout: 10000 });

      // Capture screenshot
      console.log('   Capturing current implementation screenshot...');
      await page.screenshot({
        path: this.currentScreenshotPath,
        fullPage: true
      });

      console.log(`✅ Current implementation captured: ${this.currentScreenshotPath}`);

      // Store page reference for further analysis
      this.currentPage = page;
      this.browser = browser;

    } catch (error) {
      if (browser) await browser.close();
      console.error('❌ Error capturing current implementation:', error.message);
      throw error;
    }
  }

  async analyzeCurrentStructure() {
    try {
      const page = this.currentPage;

      // Analyze dimensions
      console.log('   📏 Analyzing dimensions...');
      this.analysisResults.dimensions = await page.evaluate(() => {
        const container = document.querySelector('.max-w-md, .max-w-sm, [class*="max-w"]');
        const dashboardGrid = document.querySelector('[data-testid="dashboard-grid"]');

        return {
          containerWidth: container ? container.offsetWidth : null,
          containerMaxWidth: container ? getComputedStyle(container).maxWidth : null,
          dashboardGridWidth: dashboardGrid ? dashboardGrid.offsetWidth : null,
          viewportWidth: window.innerWidth,
          viewportHeight: window.innerHeight
        };
      });

      // Analyze sections
      console.log('   📋 Analyzing sections...');
      this.analysisResults.sections = await page.evaluate(() => {
        const sections = {};

        // Check for section headers
        const headers = document.querySelectorAll('h1, h2, h3');
        headers.forEach((header, index) => {
          const text = header.textContent.trim();
          if (text.includes('Reuniones') || text.includes('Actividades') || text.includes('Comunicación')) {
            sections[text] = {
              tagName: header.tagName,
              hasBackground: false, // Check if parent has background
              parentClasses: header.parentElement ? header.parentElement.className : null,
              position: header.getBoundingClientRect()
            };

            // Check if section has background container
            let parent = header.parentElement;
            while (parent && parent !== document.body) {
              const styles = getComputedStyle(parent);
              if (styles.backgroundColor !== 'rgba(0, 0, 0, 0)' && styles.backgroundColor !== 'transparent') {
                sections[text].hasBackground = true;
                sections[text].backgroundElement = parent.className;
                break;
              }
              parent = parent.parentElement;
            }
          }
        });

        return sections;
      });

      // Analyze spacing
      console.log('   📐 Analyzing spacing...');
      this.analysisResults.spacing = await page.evaluate(() => {
        const dashboardGrid = document.querySelector('[data-testid="dashboard-grid"]');
        const gridItems = document.querySelectorAll('[data-testid="dashboard-grid"] > div');

        return {
          dashboardGridPadding: dashboardGrid ? getComputedStyle(dashboardGrid).padding : null,
          dashboardGridGap: dashboardGrid ? getComputedStyle(dashboardGrid).gap : null,
          gridItemsCount: gridItems.length,
          firstItemMarginTop: gridItems[0] ? getComputedStyle(gridItems[0]).marginTop : null
        };
      });

      // Analyze admin button
      console.log('   🔧 Analyzing admin button...');
      this.analysisResults.adminButton = await page.evaluate(() => {
        // Find admin button by text content
        const buttons = document.querySelectorAll('button');
        let adminButton = null;

        for (let button of buttons) {
          if (button.textContent.includes('Administración')) {
            adminButton = button;
            break;
          }
        }

        return adminButton ? {
          found: true,
          backgroundColor: getComputedStyle(adminButton).backgroundColor,
          padding: getComputedStyle(adminButton).padding,
          borderRadius: getComputedStyle(adminButton).borderRadius,
          position: adminButton.getBoundingClientRect(),
          classes: adminButton.className
        } : { found: false };
      });

      console.log('✅ Current structure analysis complete');

    } catch (error) {
      console.error('❌ Error analyzing current structure:', error.message);
      throw error;
    }
  }

  documentSpecificDifferences() {
    console.log('   📝 Based on your observations, documenting known differences...');

    // Document the three critical issues you identified
    this.analysisResults.differences = [
      {
        category: 'Section Backgrounds',
        issue: 'Reuniones, Actividades, and Comunicación sections lack background containers',
        evidence: 'User reported these should be subsections with their own backgrounds',
        currentState: this.analysisResults.sections,
        severity: 'CRITICAL'
      },
      {
        category: 'App Dimensions',
        issue: 'Current app appears smaller than reference design',
        evidence: 'User reported app looks smaller than dashboard.html reference',
        currentState: this.analysisResults.dimensions,
        severity: 'HIGH'
      },
      {
        category: 'Icon Comparison',
        issue: 'Icons may not match original dashboard.html implementation',
        evidence: 'User requested comparison with original dashboard icons',
        currentState: 'Icons need comparison with reference',
        severity: 'MEDIUM'
      }
    ];

    console.log('✅ Specific differences documented');
  }

  generateRecommendations() {
    console.log('   🎯 Generating evidence-based recommendations...');

    this.analysisResults.recommendations = [
      {
        priority: 1,
        category: 'Section Backgrounds',
        action: 'Add background containers for Reuniones, Actividades, and Comunicación sections',
        implementation: 'Wrap each section in a white background container with proper padding and border radius',
        evidence: 'User specifically identified this as missing from reference design'
      },
      {
        priority: 2,
        category: 'App Dimensions',
        action: 'Adjust container width to match reference design exactly',
        implementation: `Current: ${this.analysisResults.dimensions.containerMaxWidth}, needs verification against reference`,
        evidence: 'User reported current app appears smaller than reference'
      },
      {
        priority: 3,
        category: 'Icon Analysis',
        action: 'Compare current icons with original dashboard.html icons',
        implementation: 'Examine reference codebase icons and ensure exact match',
        evidence: 'User requested icon comparison with original implementation'
      }
    ];

    console.log('✅ Evidence-based recommendations generated');
  }

  createAnalysisReport() {
    const report = {
      timestamp: new Date().toISOString(),
      referenceImage: this.referenceImagePath,
      currentScreenshot: this.currentScreenshotPath,
      analysis: this.analysisResults
    };

    const reportPath = 'dashboard-analysis-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`✅ Detailed analysis report saved: ${reportPath}`);
    console.log('\n📋 ANALYSIS SUMMARY:');
    console.log(`   Reference Image: ${this.referenceImagePath}`);
    console.log(`   Current Screenshot: ${this.currentScreenshotPath}`);
    console.log(`   Container Width: ${this.analysisResults.dimensions.containerMaxWidth}`);
    console.log(`   Critical Issues: ${this.analysisResults.differences.filter(d => d.severity === 'CRITICAL').length}`);
    console.log(`   High Priority Issues: ${this.analysisResults.differences.filter(d => d.severity === 'HIGH').length}`);

    // Close browser
    if (this.browser) {
      this.browser.close();
    }
  }
}

// Run the systematic analysis
const analyzer = new SystematicDashboardAnalyzer();
analyzer.performSystematicAnalysis().catch(console.error);
