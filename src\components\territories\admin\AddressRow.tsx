'use client';

/**
 * Address Row Component
 *
 * Displays individual territory addresses in a table row format.
 * Allows elders and ministerial servants to edit addresses on double-click.
 */

import React, { useState, useEffect } from 'react';

interface AddressRowProps {
  address: string;
  notes?: string;
  territoryId: string;
  territoryNumber: string;
  index: number;
  userRole: string;
  showStreet?: boolean;
  street?: string;
  isExpanded?: boolean;
  onAddressUpdate?: (territoryId: string, updatedAddress: string, updatedNotes?: string) => void;
  onFieldServiceAction?: (territoryId: string, address: string, action: string) => void;
  onToggleExpand?: (index: number) => void;
  onAddNote?: (territoryId: string, address: string, note: string) => void;
}

export default function AddressRow({
  address,
  notes,
  territoryId,
  territoryNumber,
  index,
  userRole,
  showStreet = false,
  street,
  isExpanded = false,
  onAddressUpdate,
  onFieldServiceAction,
  onToggleExpand,
  onAddNote
}: AddressRowProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editAddress, setEditAddress] = useState(address);
  const [editNotes, setEditNotes] = useState(notes || '');
  const [isSaving, setIsSaving] = useState(false);
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [newNote, setNewNote] = useState('');

  // Update local state when props change (after refresh)
  useEffect(() => {
    setEditAddress(address);
    setEditNotes(notes || '');
  }, [address, notes]);

  // Check if user can edit (elders, ministerial servants, coordinators)
  const canEdit = ['elder', 'ministerial_servant', 'coordinator', 'overseer_coordinator'].includes(userRole);

  // Get status icon based on notes - matches action labels exactly
  const getStatusIcon = (notes?: string) => {
    if (!notes) return null;

    const lowerNotes = notes.toLowerCase();

    // Match action labels and territory notes
    if (lowerNotes.includes('perros/rejas') || lowerNotes.includes('perro') || lowerNotes.includes('rejas') || lowerNotes.includes('candado')) {
      return { icon: '🐕', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    }
    if (lowerNotes.includes('no visitar') || lowerNotes.includes('no llamar')) {
      return { icon: '🚫', color: 'text-red-600', bg: 'bg-red-100' };
    }
    if (lowerNotes.includes('no traspasar') || lowerNotes.includes('no trespassing')) {
      return { icon: '🚫', color: 'text-red-600', bg: 'bg-red-100' };
    }
    if (lowerNotes.includes('no estaba') || lowerNotes.includes('no en casa')) {
      return { icon: '🚪', color: 'text-orange-600', bg: 'bg-orange-100' };
    }
    if (lowerNotes.includes('en casa')) {
      return { icon: '🏠', color: 'text-green-600', bg: 'bg-green-100' };
    }
    if (lowerNotes.includes('testigo') || lowerNotes.includes('hermano') || lowerNotes.includes('hermana') || lowerNotes.includes('hermanos')) {
      return { icon: '🏠', color: 'text-purple-600', bg: 'bg-purple-100' };
    }

    return null;
  };

  const statusIcon = getStatusIcon(notes);

  const handleDoubleClick = () => {
    if (canEdit) {
      setIsEditing(true);
      setEditAddress(address);
      setEditNotes(notes || '');
    }
  };

  const handleSave = async () => {
    if (!canEdit || !onAddressUpdate) return;

    try {
      setIsSaving(true);
      await onAddressUpdate(territoryId, editAddress.trim(), editNotes.trim() || undefined);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating address:', error);
      // Reset to original values on error
      setEditAddress(address);
      setEditNotes(notes || '');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditAddress(address);
    setEditNotes(notes || '');
  };

  const handleAddNote = async () => {
    if (onAddNote) {
      try {
        // If note is empty, we're deleting the note
        const noteToSave = newNote.trim();
        await onAddNote(territoryId, address, noteToSave);

        setNewNote('');
        setShowNoteModal(false);
      } catch (error) {
        console.error('Error updating note:', error);
        // Keep modal open on error
      }
    }
  };

  const handleToggleExpand = () => {
    if (onToggleExpand) {
      onToggleExpand(index);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className="border-b border-gray-200 bg-blue-50 p-4">
        <div className="space-y-3">
          <input
            type="text"
            value={editAddress}
            onChange={(e) => setEditAddress(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Dirección completa"
            autoFocus
          />
          <input
            type="text"
            value={editNotes}
            onChange={(e) => setEditNotes(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Notas (opcional)"
          />
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              disabled={isSaving || !editAddress.trim()}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Guardando...
                </>
              ) : (
                'Guardar'
              )}
            </button>
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 disabled:opacity-50"
            >
              Cancelar
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="border-b border-gray-200 bg-white">
      {/* Address Row - Click to expand */}
      <div
        className="px-4 py-3 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
        onClick={handleToggleExpand}
        onDoubleClick={canEdit ? handleDoubleClick : undefined}
      >
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-gray-900">{address}</div>
            {statusIcon && (
              <span
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusIcon.bg} ${statusIcon.color}`}
                title={notes}
              >
                {statusIcon.icon}
              </span>
            )}
          </div>
          {showStreet && street && (
            <div className="text-xs text-blue-600 mt-1 font-medium">📍 {street}</div>
          )}
          {notes && !statusIcon && (
            <div
              className="text-xs text-gray-500 mt-1 cursor-pointer hover:text-blue-600 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                setNewNote(notes);
                setShowNoteModal(true);
              }}
              title="Click para editar nota"
            >
              📝 {notes}
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {/* Note Icon */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowNoteModal(true);
            }}
            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
            title="Agregar nota"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
          <div className="text-gray-400 ml-2">
            {isExpanded ? '▼' : '▶'}
          </div>
        </div>
      </div>

      {/* Action Buttons - Only show when expanded */}
      {isExpanded && (
        <div className="px-4 pb-3 bg-gray-50">
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => onFieldServiceAction?.(territoryId, address, 'at_home')}
              className="flex items-center justify-center px-3 py-2 bg-green-100 text-green-800 rounded-lg text-sm hover:bg-green-200 transition-colors"
            >
              🏠 En Casa
            </button>
            <button
              onClick={() => onFieldServiceAction?.(territoryId, address, 'not_home')}
              className="flex items-center justify-center px-3 py-2 bg-orange-100 text-orange-800 rounded-lg text-sm hover:bg-orange-200 transition-colors"
            >
              🚪 No en Casa
            </button>
            <button
              onClick={() => onFieldServiceAction?.(territoryId, address, 'do_not_call')}
              className="flex items-center justify-center px-3 py-2 bg-red-100 text-red-800 rounded-lg text-sm hover:bg-red-200 transition-colors"
            >
              🚫 No Llamar
            </button>
            <button
              onClick={() => onFieldServiceAction?.(territoryId, address, 'testigo')}
              className="flex items-center justify-center px-3 py-2 bg-purple-100 text-purple-800 rounded-lg text-sm hover:bg-purple-200 transition-colors"
            >
              🏠 Testigo
            </button>
            <button
              onClick={() => onFieldServiceAction?.(territoryId, address, 'perros_rejas')}
              className="flex items-center justify-center px-3 py-2 bg-yellow-100 text-yellow-800 rounded-lg text-sm hover:bg-yellow-200 transition-colors"
            >
              🐕 Perros/Rejas
            </button>
            <button
              onClick={() => onFieldServiceAction?.(territoryId, address, 'no_trespassing')}
              className="flex items-center justify-center px-3 py-2 bg-red-100 text-red-800 rounded-lg text-sm hover:bg-red-200 transition-colors"
            >
              🚫 No Trespassing
            </button>
          </div>
        </div>
      )}

      {/* Note Modal */}
      {showNoteModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={(e) => {
            // Close modal when clicking outside
            if (e.target === e.currentTarget) {
              setShowNoteModal(false);
              setNewNote('');
            }
          }}
        >
          <div className="bg-white rounded-lg p-6 w-96 max-w-full mx-4 relative">
            {/* Close button */}
            <button
              onClick={() => {
                setShowNoteModal(false);
                setNewNote('');
              }}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <h3 className="text-lg font-semibold mb-4">
              {newNote ? 'Editar Nota' : 'Agregar Nota'}
            </h3>
            <p className="text-sm text-gray-600 mb-3">Dirección: {address}</p>
            <textarea
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              placeholder="Escriba su nota aquí..."
              className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24 focus:outline-none focus:ring-2 focus:ring-blue-500"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  setShowNoteModal(false);
                  setNewNote('');
                }
              }}
            />
            <div className="flex justify-center mt-4">
              <button
                onClick={handleAddNote}
                className="bg-blue-600 text-white py-2 px-6 rounded-lg hover:bg-blue-700 transition-colors"
              >
                {notes ? 'Actualizar' : 'Guardar'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
