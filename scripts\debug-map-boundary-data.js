#!/usr/bin/env node

/**
 * Debug Map Boundary Data
 *
 * This script tests what boundary data is actually being sent to the map
 * components and compares it with what's stored in the database.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Test database boundary data
 */
async function testDatabaseBoundary() {
  console.log('🗄️  Database Boundary Data');
  console.log('==========================\n');

  try {
    const territory = await prisma.territory.findFirst({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        id: true,
        territoryNumber: true,
        boundaries: true
      }
    });

    if (!territory) {
      console.log('❌ Territory 001 not found');
      return null;
    }

    console.log(`✅ Territory ID: ${territory.id}`);
    console.log(`✅ Territory Number: ${territory.territoryNumber}`);

    if (territory.boundaries) {
      console.log('✅ Boundary data exists in database');
      console.log('📍 Database Boundary Coordinates:');

      const coords = territory.boundaries.coordinates[0];
      coords.forEach((coord, index) => {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
        console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}]`);
      });

      return territory;
    } else {
      console.log('❌ No boundary data in database');
      return null;
    }
  } catch (error) {
    console.error('❌ Database error:', error);
    return null;
  }
}

/**
 * Test individual territory API
 */
async function testIndividualTerritoryAPI(territoryId) {
  console.log('\n🌐 Individual Territory API Test');
  console.log('=================================\n');

  try {
    // Note: This would normally require authentication
    // For debugging, we'll simulate the API response structure

    const territory = await prisma.territory.findFirst({
      where: { id: territoryId },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        status: true,
        notes: true,
        boundaries: true
      }
    });

    if (!territory) {
      console.log('❌ Territory not found');
      return null;
    }

    // Simulate API response
    const apiResponse = {
      id: territory.id,
      territoryNumber: territory.territoryNumber,
      address: territory.address,
      status: territory.status,
      notes: territory.notes,
      boundaries: territory.boundaries // This is what gets sent to TerritoryDetail
    };

    console.log('📤 Individual Territory API Response:');
    console.log(`   ID: ${apiResponse.id}`);
    console.log(`   Territory Number: ${apiResponse.territoryNumber}`);
    console.log(`   Has Boundaries: ${!!apiResponse.boundaries}`);

    if (apiResponse.boundaries) {
      console.log('📍 API Boundary Coordinates:');
      const coords = apiResponse.boundaries.coordinates[0];
      coords.forEach((coord, index) => {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
        console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}]`);
      });
    }

    return apiResponse;
  } catch (error) {
    console.error('❌ API test error:', error);
    return null;
  }
}

/**
 * Test map data API
 */
async function testMapDataAPI() {
  console.log('\n🗺️  Map Data API Test');
  console.log('======================\n');

  try {
    // Simulate map data API response
    const territories = await prisma.territory.findMany({
      where: {
        congregationId: '1441',
        territoryNumber: '001'
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        status: true,
        boundaries: true
      }
    });

    if (territories.length === 0) {
      console.log('❌ No territories found');
      return null;
    }

    const territory = territories[0];

    // Simulate map data API processing
    const mapDataResponse = {
      id: territory.id,
      territoryNumber: territory.territoryNumber,
      address: territory.address,
      status: territory.status,
      boundary: territory.boundaries, // Note: field name is 'boundary' not 'boundaries'
      coordinates: null
    };

    // Calculate center coordinates from boundary
    if (territory.boundaries && territory.boundaries.coordinates) {
      const coords = territory.boundaries.coordinates[0];
      const lats = coords.slice(0, -1).map(c => c[1]); // Exclude duplicate last point
      const lngs = coords.slice(0, -1).map(c => c[0]);

      mapDataResponse.coordinates = {
        latitude: lats.reduce((a, b) => a + b, 0) / lats.length,
        longitude: lngs.reduce((a, b) => a + b, 0) / lngs.length
      };
    }

    console.log('📤 Map Data API Response:');
    console.log(`   ID: ${mapDataResponse.id}`);
    console.log(`   Territory Number: ${mapDataResponse.territoryNumber}`);
    console.log(`   Has Boundary: ${!!mapDataResponse.boundary}`);
    console.log(`   Has Coordinates: ${!!mapDataResponse.coordinates}`);

    if (mapDataResponse.boundary) {
      console.log('📍 Map API Boundary Coordinates:');
      const coords = mapDataResponse.boundary.coordinates[0];
      coords.forEach((coord, index) => {
        const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];
        console.log(`   ${labels[index]}: [${coord[0]}, ${coord[1]}]`);
      });
    }

    if (mapDataResponse.coordinates) {
      console.log('📍 Calculated Center Coordinates:');
      console.log(`   Latitude: ${mapDataResponse.coordinates.latitude}`);
      console.log(`   Longitude: ${mapDataResponse.coordinates.longitude}`);
    }

    return mapDataResponse;
  } catch (error) {
    console.error('❌ Map data API test error:', error);
    return null;
  }
}

/**
 * Check for coordinate transformation issues
 */
function checkCoordinateTransformation(boundary) {
  console.log('\n🔍 Coordinate Transformation Check');
  console.log('===================================\n');

  if (!boundary || !boundary.coordinates) {
    console.log('❌ No boundary data to check');
    return;
  }

  const coords = boundary.coordinates[0];

  console.log('🧮 Coordinate Validation:');

  // Check coordinate order (should be [longitude, latitude])
  coords.forEach((coord, index) => {
    const [lng, lat] = coord;
    const labels = ['Northwest', 'Northeast', 'Southeast', 'Southwest', 'Close'];

    // Validate ranges for Miami
    const lngValid = lng >= -80.3 && lng <= -80.2;
    const latValid = lat >= 25.7 && lat <= 25.8;

    console.log(`   ${labels[index]}: [${lng}, ${lat}]`);
    console.log(`     Longitude valid: ${lngValid ? '✅' : '❌'} (${lng})`);
    console.log(`     Latitude valid: ${latValid ? '✅' : '❌'} (${lat})`);
  });

  // Check if polygon is properly closed
  const firstPoint = coords[0];
  const lastPoint = coords[coords.length - 1];
  const isClosed = firstPoint[0] === lastPoint[0] && firstPoint[1] === lastPoint[1];

  console.log(`\n🔄 Polygon Closure: ${isClosed ? '✅' : '❌'}`);

  // Check coordinate order consistency
  const lngs = coords.slice(0, -1).map(c => c[0]);
  const lats = coords.slice(0, -1).map(c => c[1]);

  console.log('\n📐 Boundary Shape Analysis:');
  console.log(`   Longitude range: ${Math.min(...lngs)} to ${Math.max(...lngs)}`);
  console.log(`   Latitude range: ${Math.min(...lats)} to ${Math.max(...lats)}`);
  console.log(`   Width: ${(Math.max(...lngs) - Math.min(...lngs)).toFixed(6)} degrees`);
  console.log(`   Height: ${(Math.max(...lats) - Math.min(...lats)).toFixed(6)} degrees`);
}

/**
 * Main debug function
 */
async function main() {
  console.log('🔧 Territory 001 Map Boundary Debug');
  console.log('====================================\n');

  try {
    // Test database
    const dbTerritory = await testDatabaseBoundary();
    if (!dbTerritory) return;

    // Test individual territory API
    const apiResponse = await testIndividualTerritoryAPI(dbTerritory.id);
    if (!apiResponse) return;

    // Test map data API
    const mapResponse = await testMapDataAPI();
    if (!mapResponse) return;

    // Check coordinate transformations
    checkCoordinateTransformation(dbTerritory.boundaries);

    console.log('\n🎯 Debug Summary:');
    console.log('=================');
    console.log('✅ Database contains boundary data');
    console.log('✅ Individual territory API includes boundary data');
    console.log('✅ Map data API includes boundary data');
    console.log('✅ Coordinates are in valid Miami ranges');

    console.log('\n💡 If map display is incorrect, check:');
    console.log('1. MapLibre coordinate order (longitude, latitude)');
    console.log('2. Map projection settings');
    console.log('3. Boundary rendering in SimpleTerritoryMap component');
    console.log('4. Console logs in browser developer tools');

  } catch (error) {
    console.error('❌ Debug error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testDatabaseBoundary,
  testIndividualTerritoryAPI,
  testMapDataAPI,
  checkCoordinateTransformation
};
