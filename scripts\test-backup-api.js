/**
 * Test Backup API Endpoint
 * 
 * Tests the backup API endpoint directly to identify authentication or request issues
 */

const fetch = require('node-fetch');

async function testBackupAPI() {
  try {
    console.log('🌐 Testing Backup API Endpoint...\n');

    // Test 1: Check if server is running
    console.log('1. Testing server availability...');
    try {
      const healthResponse = await fetch('http://localhost:3000/api/health', {
        method: 'GET'
      });
      
      if (healthResponse.ok) {
        console.log('✅ Server is running');
      } else {
        console.log('⚠️ Server responded but health check failed');
      }
    } catch (error) {
      console.log('❌ Server not accessible:', error.message);
      console.log('Make sure the development server is running with: npm run dev');
      return;
    }

    // Test 2: Test authentication endpoint
    console.log('\n2. Testing authentication...');
    try {
      const loginResponse = await fetch('http://localhost:3000/api/auth/congregation-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          congregationId: '1441',
          pin: 'admin123' // Default admin PIN
        })
      });

      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        console.log('✅ Authentication successful');
        
        const token = loginData.token;
        if (!token) {
          console.log('❌ No token received from login');
          return;
        }

        // Test 3: Test backup creation with authentication
        console.log('\n3. Testing backup creation API...');
        const backupResponse = await fetch('http://localhost:3000/api/admin/database/backup', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            description: 'Test backup from API test script'
          })
        });

        console.log(`Backup API response status: ${backupResponse.status}`);

        if (backupResponse.ok) {
          const backupData = await backupResponse.json();
          console.log('✅ Backup creation successful!');
          console.log('Backup details:', {
            filename: backupData.backup?.filename,
            fileSize: backupData.backup?.fileSize,
            status: backupData.backup?.status
          });
        } else {
          const errorText = await backupResponse.text();
          console.log('❌ Backup creation failed');
          console.log('Error response:', errorText);
          
          try {
            const errorData = JSON.parse(errorText);
            console.log('Error details:', errorData);
          } catch {
            console.log('Raw error response:', errorText);
          }
        }

        // Test 4: Test backup listing
        console.log('\n4. Testing backup listing...');
        const listResponse = await fetch('http://localhost:3000/api/admin/database/backup', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (listResponse.ok) {
          const listData = await listResponse.json();
          console.log('✅ Backup listing successful');
          console.log(`Found ${listData.backups?.length || 0} backup files`);
        } else {
          console.log('❌ Backup listing failed');
          const listError = await listResponse.text();
          console.log('List error:', listError);
        }

      } else {
        console.log('❌ Authentication failed');
        const authError = await loginResponse.text();
        console.log('Auth error:', authError);
      }

    } catch (authError) {
      console.log('❌ Authentication request failed:', authError.message);
    }

    console.log('\n📋 API Test Summary:');
    console.log('- Server availability: Check above');
    console.log('- Authentication: Check above');
    console.log('- Backup creation: Check above');
    console.log('- Backup listing: Check above');

  } catch (error) {
    console.error('❌ API test failed:', error);
  }
}

// Run the API test
testBackupAPI().catch(console.error);
