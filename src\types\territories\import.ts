// Territory Import Types
// Types for Excel territory data import functionality

export interface TerritoryImportData {
  territoryNumber: string;
  address: string;
  notes?: string;
  rowNumber: number; // For error reporting
}

export interface ImportValidationError {
  rowNumber: number;
  field: string;
  value: string;
  error: string;
}

export interface ImportResult {
  success: boolean;
  totalRows: number;
  successfulImports: number;
  failedImports: number;
  duplicatesFound: number;
  errors: ImportValidationError[];
  duplicates: TerritoryImportData[];
  importedTerritories: string[]; // Territory IDs
  summary: string;
}

export interface ExcelImportRequest {
  file: File;
  congregationId: string;
  overwriteDuplicates?: boolean;
}

export interface ExcelParseResult {
  data: TerritoryImportData[];
  errors: ImportValidationError[];
  totalRows: number;
}

// Excel column mapping configuration
export interface ExcelColumnMapping {
  territoryNumberColumn: string | number; // Column name or index
  addressColumn: string | number;
  notesColumn?: string | number;
  startRow?: number; // Row to start reading from (default: 2 for headers)
}

// Default column mappings for common Excel formats
export const DEFAULT_COLUMN_MAPPINGS: ExcelColumnMapping[] = [
  {
    territoryNumberColumn: 'Territory Number',
    addressColumn: 'Address',
    notesColumn: 'Notes',
    startRow: 2
  },
  {
    territoryNumberColumn: 'Territorio',
    addressColumn: 'Dirección',
    notesColumn: 'Notas',
    startRow: 2
  },
  {
    territoryNumberColumn: 'A', // First column
    addressColumn: 'B', // Second column
    notesColumn: 'C', // Third column
    startRow: 1
  }
];

// Import processing status
export interface ImportProgress {
  id: string;
  status: 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  currentRow: number;
  totalRows: number;
  errors: ImportValidationError[];
  startTime: Date;
  endTime?: Date;
}

// Territory validation rules
export interface TerritoryValidationRules {
  territoryNumberRequired: boolean;
  territoryNumberMaxLength: number;
  addressRequired: boolean;
  addressMaxLength: number;
  allowDuplicateNumbers: boolean;
  territoryNumberPattern?: RegExp;
}

export const DEFAULT_VALIDATION_RULES: TerritoryValidationRules = {
  territoryNumberRequired: true,
  territoryNumberMaxLength: 50,
  addressRequired: true,
  addressMaxLength: 1000,
  allowDuplicateNumbers: false,
  territoryNumberPattern: /^[A-Za-z0-9\-_]+$/ // Alphanumeric with hyphens and underscores
};

// Bulk Import Types
export interface BulkImportFile {
  id: string;
  file: File;
  name: string;
  size: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  result?: ImportResult;
  error?: string;
}

export interface BulkImportBatch {
  id: string;
  congregationId: string;
  files: BulkImportFile[];
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial';
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  successfulImports: number;
  totalTerritories: number;
  startTime: Date;
  endTime?: Date;
  overallProgress: number; // 0-100
}

export interface BulkImportRequest {
  files: File[];
  congregationId: string;
  overwriteDuplicates?: boolean;
}

export interface BulkImportResult {
  batchId: string;
  success: boolean;
  totalFiles: number;
  successfulFiles: number;
  failedFiles: number;
  totalTerritories: number;
  successfulImports: number;
  failedImports: number;
  duplicatesFound: number;
  fileResults: Array<{
    fileName: string;
    success: boolean;
    result?: ImportResult;
    error?: string;
  }>;
  summary: string;
  processingTime: number; // milliseconds
}

export interface BulkImportProgress {
  batchId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial';
  overallProgress: number; // 0-100
  currentFile?: string;
  filesCompleted: number;
  totalFiles: number;
  territoriesImported: number;
  errors: string[];
  estimatedTimeRemaining?: number; // milliseconds
}

// Queue Management Types
export interface ImportJob {
  id: string;
  batchId: string;
  fileId: string;
  fileName: string;
  filePath: string;
  congregationId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  priority: number;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  result?: ImportResult;
}

export interface QueueStatus {
  totalJobs: number;
  queuedJobs: number;
  processingJobs: number;
  completedJobs: number;
  failedJobs: number;
  isProcessing: boolean;
}

// Bulk Processing Configuration
export interface BulkProcessingConfig {
  maxConcurrentFiles: number;
  maxFileSize: number; // bytes
  maxTotalFiles: number;
  chunkSize: number; // territories per batch
  timeoutPerFile: number; // milliseconds
  retryAttempts: number;
  cleanupAfterHours: number;
}

export const DEFAULT_BULK_CONFIG: BulkProcessingConfig = {
  maxConcurrentFiles: 3,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxTotalFiles: 20,
  chunkSize: 100,
  timeoutPerFile: 5 * 60 * 1000, // 5 minutes
  retryAttempts: 3,
  cleanupAfterHours: 24
};
