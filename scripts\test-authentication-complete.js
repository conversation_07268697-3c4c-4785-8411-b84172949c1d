const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testAuthentication() {
  try {
    console.log('🧪 Testing Complete Authentication Flow...\n');

    // 1. Test congregation PIN
    console.log('1️⃣ Testing Congregation Authentication...');
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (congregation) {
      const congregationPinMatches = await bcrypt.compare('1930', congregation.pin);
      console.log(`✅ Congregation PIN (1930) works: ${congregationPinMatches}`);
    }

    // 2. Test coordinator user
    console.log('\n2️⃣ Testing Coordinator User...');
    const coordinator = await prisma.member.findFirst({
      where: {
        congregationId: '1441',
        role: 'coordinator',
        isActive: true
      }
    });

    if (coordinator) {
      const coordinatorPinMatches = await bcrypt.compare('5488', coordinator.pin);
      console.log(`✅ Coordinator "${coordinator.name}" PIN (5488) works: ${coordinatorPinMatches}`);
      console.log(`📧 Email: ${coordinator.email}`);
      console.log(`🏢 Role: ${coordinator.role}`);
    } else {
      console.log('❌ No coordinator user found');
    }

    // 3. Test other admin users
    console.log('\n3️⃣ Testing Other Admin Users...');
    const adminRoles = ['elder', 'overseer_coordinator', 'developer', 'ministerial_servant'];
    
    for (const role of adminRoles) {
      const users = await prisma.member.findMany({
        where: {
          congregationId: '1441',
          role: role,
          isActive: true
        },
        select: { name: true, email: true, role: true },
        take: 2
      });

      if (users.length > 0) {
        console.log(`👥 ${role}: ${users.length} user(s) found`);
        users.forEach(user => console.log(`   - ${user.name} (${user.email})`));
      }
    }

    console.log('\n🎯 TESTING INSTRUCTIONS:');
    console.log('═══════════════════════════════════════════════════════════');
    console.log('🌐 URL: http://localhost:3001/login');
    console.log('');
    console.log('📋 STEP 1: Congregation Login');
    console.log('   🏛️ Congregation ID: 1441');
    console.log('   🔐 Congregation PIN: 1930');
    console.log('');
    console.log('📋 STEP 2: Member Login (Coordinator)');
    console.log('   👤 User: Carlos Coordinador');
    console.log('   🔑 PIN: 5488');
    console.log('');
    console.log('✅ EXPECTED RESULTS:');
    console.log('   1. Login should succeed');
    console.log('   2. Dashboard should show "Administración" button');
    console.log('   3. Profile dropdown should have logout option');
    console.log('   4. Admin section should be accessible');
    console.log('   5. Enhanced Member Management should be available');
    console.log('');
    console.log('🧪 STORY 2.2 TESTING:');
    console.log('   - Click "Administración" → Should see admin dashboard');
    console.log('   - Look for Member Management section');
    console.log('   - Test creating/editing member profiles');
    console.log('   - Test role assignment and service groups');
    console.log('   - Test advanced search and filtering');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuthentication();
