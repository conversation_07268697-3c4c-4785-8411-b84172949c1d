/**
 * Bulk Territory Import API Endpoint
 * 
 * Handles multiple Excel file uploads for bulk territory data import.
 * Supports batch processing with progress tracking and error handling.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { BulkImportService } from '@/services/territories/BulkImportService';

// Validation schema for bulk import request
const BulkImportRequestSchema = z.object({
  congregationId: z.string().min(1, 'Congregation ID is required'),
  overwriteDuplicates: z.boolean().optional().default(false)
});

// File validation constants
const MAX_FILES = 20;
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_TYPES = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.ms-excel' // .xls (legacy)
];

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for bulk territory import' },
        { status: 403 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const congregationId = formData.get('congregationId') as string || user.congregationId;
    const overwriteDuplicates = formData.get('overwriteDuplicates') === 'true';

    // Validate request data
    const validatedData = BulkImportRequestSchema.parse({
      congregationId,
      overwriteDuplicates
    });

    // Ensure congregation isolation
    if (congregationId !== user.congregationId) {
      return NextResponse.json(
        { error: 'Cannot import territories for different congregation' },
        { status: 403 }
      );
    }

    // Extract files from form data
    const files: File[] = [];
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('file') && value instanceof File) {
        files.push(value);
      }
    }

    // Validate files
    if (files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided for import' },
        { status: 400 }
      );
    }

    if (files.length > MAX_FILES) {
      return NextResponse.json(
        { error: `Too many files. Maximum allowed: ${MAX_FILES}` },
        { status: 400 }
      );
    }

    // Validate individual files
    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { error: `File "${file.name}" exceeds maximum size of ${MAX_FILE_SIZE / 1024 / 1024}MB` },
          { status: 400 }
        );
      }

      if (!ALLOWED_TYPES.includes(file.type)) {
        return NextResponse.json(
          { error: `File "${file.name}" is not a valid Excel file` },
          { status: 400 }
        );
      }
    }

    // Initialize bulk import service
    BulkImportService.initialize();

    // Start bulk import process
    console.log(`Starting bulk import for congregation ${congregationId} with ${files.length} files`);
    const result = await BulkImportService.startBulkImport(
      files,
      congregationId,
      overwriteDuplicates
    );

    if (!result.success) {
      return NextResponse.json({
        success: false,
        error: 'Failed to start bulk import',
        details: result.errors
      }, { status: 400 });
    }

    // Log bulk import start
    console.log('Bulk import started:', {
      batchId: result.batchId,
      fileCount: files.length,
      congregation: congregationId
    });

    // Return batch ID for progress tracking
    return NextResponse.json({
      success: true,
      batchId: result.batchId,
      message: `Bulk import started with ${files.length} files`,
      fileCount: files.length
    }, { status: 200 });

  } catch (error) {
    console.error('Bulk territory import POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to start bulk import',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to start bulk import.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to start bulk import.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to start bulk import.' },
    { status: 405 }
  );
}
