-- CreateTable
CREATE TABLE "pin_settings" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "min_length" INTEGER NOT NULL DEFAULT 4,
    "max_length" INTEGER NOT NULL DEFAULT 8,
    "require_numeric" BOOLEAN NOT NULL DEFAULT true,
    "require_alphanumeric" BOOLEAN NOT NULL DEFAULT false,
    "require_special_chars" BOOLEAN NOT NULL DEFAULT false,
    "allow_sequential" BOOLEAN NOT NULL DEFAULT true,
    "allow_repeated" BOOLEAN NOT NULL DEFAULT true,
    "expiration_days" INTEGER,
    "bcrypt_rounds" INTEGER NOT NULL DEFAULT 12,
    "created_by" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "pin_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pin_change_history" (
    "id" TEXT NOT NULL,
    "congregation_id" VARCHAR(8) NOT NULL,
    "member_id" TEXT,
    "changed_by" TEXT NOT NULL,
    "change_type" VARCHAR(50) NOT NULL,
    "old_pin_hash" VARCHAR(255),
    "new_pin_hash" VARCHAR(255),
    "reason" TEXT,
    "ip_address" VARCHAR(45),
    "user_agent" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "pin_change_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "pin_settings_congregation_id_key" ON "pin_settings"("congregation_id");

-- CreateIndex
CREATE INDEX "pin_change_history_congregation_id_member_id_idx" ON "pin_change_history"("congregation_id", "member_id");

-- CreateIndex
CREATE INDEX "pin_change_history_congregation_id_change_type_idx" ON "pin_change_history"("congregation_id", "change_type");

-- CreateIndex
CREATE INDEX "pin_change_history_created_at_idx" ON "pin_change_history"("created_at");

-- AddForeignKey
ALTER TABLE "pin_settings" ADD CONSTRAINT "pin_settings_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pin_settings" ADD CONSTRAINT "pin_settings_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "members"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pin_change_history" ADD CONSTRAINT "pin_change_history_congregation_id_fkey" FOREIGN KEY ("congregation_id") REFERENCES "congregations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pin_change_history" ADD CONSTRAINT "pin_change_history_member_id_fkey" FOREIGN KEY ("member_id") REFERENCES "members"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pin_change_history" ADD CONSTRAINT "pin_change_history_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "members"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
