#!/usr/bin/env node

/**
 * Proper Database Restore from PostgreSQL Backup
 * 
 * This script properly restores the hermanos-07-25-25E.sql backup file
 * using the correct PostgreSQL import method.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

async function restoreProperBackup() {
    console.log('🔄 RESTORING DATABASE FROM POSTGRESQL BACKUP...');
    console.log('');

    try {
        // 1. Check if backup file exists
        const backupFile = path.join(__dirname, '../hermanos-07-25-25E.sql');
        if (!fs.existsSync(backupFile)) {
            throw new Error('Backup file hermanos-07-25-25E.sql not found');
        }
        console.log('✅ Backup file found: hermanos-07-25-25E.sql');

        // 2. Database connection settings
        const dbConfig = {
            host: 'localhost',
            port: '5432',
            user: 'mywebsites',
            password: 'password',
            database: 'hermanos'
        };

        console.log(`📋 Database: ${dbConfig.user}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);

        // 3. Set environment variables for PostgreSQL
        const env = {
            ...process.env,
            PGHOST: dbConfig.host,
            PGPORT: dbConfig.port,
            PGUSER: dbConfig.user,
            PGPASSWORD: dbConfig.password,
            PGDATABASE: 'postgres' // Connect to postgres first
        };

        // 4. Drop and recreate database
        console.log('🗑️  Dropping existing database...');
        await runPsqlCommand(`DROP DATABASE IF EXISTS ${dbConfig.database};`, env);
        console.log('✅ Database dropped');

        console.log('🆕 Creating new database...');
        await runPsqlCommand(`CREATE DATABASE ${dbConfig.database};`, env);
        console.log('✅ Database created');

        // 5. Restore from backup file
        console.log('📥 Restoring from backup file...');
        
        // Update environment to connect to the hermanos database
        env.PGDATABASE = dbConfig.database;
        
        // Use psql to import the backup file
        await runPsqlFile(backupFile, env);
        console.log('✅ Backup restored successfully');

        // 6. Verify restoration
        console.log('🔍 Verifying restoration...');
        const verificationResults = await verifyRestoration(env);
        
        console.log('');
        console.log('🎉 DATABASE RESTORATION SUCCESSFUL!');
        console.log('');
        console.log('📊 RESTORED DATA:');
        console.log(`   📋 Congregations: ${verificationResults.congregations}`);
        console.log(`   👥 Members: ${verificationResults.members}`);
        console.log(`   ⚙️  Settings: ${verificationResults.settings}`);
        console.log(`   🎵 Songs: ${verificationResults.songs}`);
        console.log(`   📋 Tasks: ${verificationResults.tasks}`);
        console.log('');
        console.log('🎯 NEXT STEPS:');
        console.log('   1. Start the development server: npm run dev');
        console.log('   2. Test authentication with restored data');
        console.log('   3. Check login credentials from the backup');

    } catch (error) {
        console.error('❌ Error during database restoration:', error);
        throw error;
    }
}

function runPsqlCommand(command, env) {
    return new Promise((resolve, reject) => {
        console.log(`   🔄 Executing: ${command.substring(0, 50)}...`);
        
        const psql = spawn('psql', ['-c', command], {
            env,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        psql.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        psql.stderr.on('data', (data) => {
            stderr += data.toString();
        });

        psql.on('close', (code) => {
            if (code === 0) {
                resolve(stdout);
            } else {
                reject(new Error(`psql command failed with code ${code}: ${stderr}`));
            }
        });

        psql.on('error', (error) => {
            reject(new Error(`Failed to start psql: ${error.message}`));
        });
    });
}

function runPsqlFile(filePath, env) {
    return new Promise((resolve, reject) => {
        console.log(`   🔄 Importing file: ${path.basename(filePath)}`);
        
        const psql = spawn('psql', ['-f', filePath], {
            env,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        psql.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        psql.stderr.on('data', (data) => {
            stderr += data.toString();
        });

        psql.on('close', (code) => {
            if (code === 0) {
                console.log('   ✅ File imported successfully');
                resolve(stdout);
            } else {
                // Check if it's just warnings
                if (stderr.includes('NOTICE') || stderr.includes('already exists')) {
                    console.log('   ✅ File imported with warnings (normal)');
                    resolve(stdout);
                } else {
                    reject(new Error(`psql file import failed with code ${code}: ${stderr}`));
                }
            }
        });

        psql.on('error', (error) => {
            reject(new Error(`Failed to start psql: ${error.message}`));
        });
    });
}

async function verifyRestoration(env) {
    try {
        const congregations = await runPsqlCommand('SELECT COUNT(*) FROM congregations;', env);
        const members = await runPsqlCommand('SELECT COUNT(*) FROM members;', env);
        const settings = await runPsqlCommand('SELECT COUNT(*) FROM congregation_settings;', env);
        const songs = await runPsqlCommand('SELECT COUNT(*) FROM songs;', env);
        const tasks = await runPsqlCommand('SELECT COUNT(*) FROM tasks;', env);

        return {
            congregations: extractCount(congregations),
            members: extractCount(members),
            settings: extractCount(settings),
            songs: extractCount(songs),
            tasks: extractCount(tasks)
        };
    } catch (error) {
        console.log('   ⚠️  Verification failed, but restoration may have succeeded');
        return {
            congregations: 'Unknown',
            members: 'Unknown',
            settings: 'Unknown',
            songs: 'Unknown',
            tasks: 'Unknown'
        };
    }
}

function extractCount(output) {
    const match = output.match(/\s+(\d+)/);
    return match ? match[1] : 'Unknown';
}

// Alternative method using direct file streaming
async function alternativeRestore() {
    console.log('🔄 TRYING ALTERNATIVE RESTORE METHOD...');
    
    try {
        const backupFile = path.join(__dirname, '../hermanos-07-25-25E.sql');
        const env = {
            ...process.env,
            PGHOST: 'localhost',
            PGPORT: '5432',
            PGUSER: 'mywebsites',
            PGPASSWORD: 'password',
            PGDATABASE: 'hermanos'
        };

        // Try streaming the file directly to psql
        const psql = spawn('psql', [], {
            env,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        const fileStream = fs.createReadStream(backupFile);
        fileStream.pipe(psql.stdin);

        return new Promise((resolve, reject) => {
            let stderr = '';

            psql.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            psql.on('close', (code) => {
                if (code === 0 || stderr.includes('NOTICE')) {
                    console.log('✅ Alternative restore method successful');
                    resolve();
                } else {
                    reject(new Error(`Alternative restore failed: ${stderr}`));
                }
            });

            psql.on('error', (error) => {
                reject(new Error(`Alternative restore error: ${error.message}`));
            });
        });

    } catch (error) {
        console.error('❌ Alternative restore failed:', error);
        throw error;
    }
}

// Run the restoration
restoreProperBackup()
    .catch(async (error) => {
        console.error('Primary restore method failed, trying alternative...');
        try {
            await alternativeRestore();
        } catch (altError) {
            console.error('All restore methods failed. Manual restoration required.');
            console.error('Try running this command manually:');
            console.error('psql -h localhost -p 5432 -U mywebsites -d hermanos -f hermanos-07-25-25E.sql');
            process.exit(1);
        }
    });
