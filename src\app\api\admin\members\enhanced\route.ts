/**
 * Enhanced Member Management API
 *
 * Integrates with Story 2.1 permission delegation system for comprehensive
 * member profile management with role assignment and service group coordination.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { MemberManagementService, MemberSearchRequest } from '@/lib/services/memberManagementService';
import { Permission<PERSON><PERSON><PERSON>, UserPermissionContext } from '@/lib/auth/permissionChecker';
import { PERMISSIONS, ROLES } from '@/lib/auth/simpleRBAC';

// Enhanced validation schemas
const ContactPreferencesSchema = z.object({
  preferredMethod: z.enum(['email', 'phone', 'text']),
  allowEmergencyContact: z.boolean(),
  privacyLevel: z.enum(['public', 'elders_only', 'private']),
});

const CreateEnhancedMemberSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  email: z.string().email('Invalid email format').max(255, 'Email too long'),
  phone: z.string().max(20, 'Phone too long').optional(),
  address: z.string().max(1000, 'Address too long').optional(),
  birthDate: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  role: z.enum(['publisher', 'ministerial_servant', 'elder', 'coordinator'], {
    errorMap: () => ({ message: 'Invalid role' }),
  }),
  serviceGroup: z.string().max(100, 'Service group name too long').optional(),
  pin: z.string().min(4, 'PIN must be at least 4 characters').max(50, 'PIN too long'),
  contactPreferences: ContactPreferencesSchema.optional(),
  qualifications: z.array(z.string()).optional(),
  notes: z.string().max(2000, 'Notes too long').optional(),
  reason: z.string().optional(),
});

const UpdateEnhancedMemberSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long').optional(),
  email: z.string().email('Invalid email format').max(255, 'Email too long').optional(),
  phone: z.string().max(20, 'Phone too long').optional(),
  address: z.string().max(1000, 'Address too long').optional(),
  birthDate: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  role: z.enum(['publisher', 'ministerial_servant', 'elder', 'coordinator']).optional(),
  serviceGroup: z.string().max(100, 'Service group name too long').optional(),
  isActive: z.boolean().optional(),
  contactPreferences: ContactPreferencesSchema.optional(),
  qualifications: z.array(z.string()).optional(),
  notes: z.string().max(2000, 'Notes too long').optional(),
  reason: z.string().optional(),
});

const MemberSearchSchema = z.object({
  query: z.string().optional(),
  filters: z.object({
    role: z.array(z.string()).optional(),
    serviceGroup: z.array(z.string()).optional(),
    status: z.array(z.string()).optional(),
    qualifications: z.array(z.string()).optional(),
  }).optional(),
  pagination: z.object({
    page: z.number().min(1).default(1),
    limit: z.number().min(1).max(100).default(20),
  }).optional(),
  sortBy: z.string().default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * GET - Enhanced member search with advanced filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Create permission context
    const context: UserPermissionContext = {
      userId: user.userId,
      role: user.role as ROLES,
      congregationId: user.congregationId,
      hasCongregationPinAccess: user.hasCongregationPinAccess,
    };

    // Check if user can view members
    const canView = await MemberManagementService.canViewMembers(context);
    if (!canView) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view members' },
        { status: 403 }
      );
    }

    // Parse search parameters
    const { searchParams } = new URL(request.url);
    const searchRequest: MemberSearchRequest = {
      query: searchParams.get('query') || undefined,
      filters: {
        role: searchParams.get('roles')?.split(',') || undefined,
        serviceGroup: searchParams.get('serviceGroups')?.split(',') || undefined,
        status: searchParams.get('status')?.split(',') || undefined,
        qualifications: searchParams.get('qualifications')?.split(',') || undefined,
      },
      pagination: {
        page: parseInt(searchParams.get('page') || '1', 10),
        limit: parseInt(searchParams.get('limit') || '20', 10),
      },
      sortBy: searchParams.get('sortBy') || 'name',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc',
    };

    // Validate search request
    const validatedSearch = MemberSearchSchema.parse(searchRequest);

    // Perform search
    const result = await MemberManagementService.searchMembers(
      user.congregationId,
      validatedSearch
    );

    return NextResponse.json({
      success: true,
      ...result,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Enhanced member search error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid search parameters',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to search members',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Create enhanced member profile
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Create permission context
    const context: UserPermissionContext = {
      userId: user.userId,
      role: user.role as ROLES,
      congregationId: user.congregationId,
      hasCongregationPinAccess: user.hasCongregationPinAccess,
    };

    // Check if user can manage members
    const canManage = await MemberManagementService.canManageMembers(context);
    if (!canManage) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create members' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = CreateEnhancedMemberSchema.parse(body);

    // Create the member
    const member = await MemberManagementService.createMember(
      user.congregationId,
      validatedData,
      user.userId
    );

    return NextResponse.json({
      success: true,
      member,
      message: 'Enhanced member profile created successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Enhanced member creation error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid member data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        {
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to create member',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT - Update enhanced member profile
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Create permission context
    const context: UserPermissionContext = {
      userId: user.userId,
      role: user.role as ROLES,
      congregationId: user.congregationId,
      hasCongregationPinAccess: user.hasCongregationPinAccess,
    };

    // Check if user can manage members
    const canManage = await MemberManagementService.canManageMembers(context);
    if (!canManage) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update members' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const { memberId, ...updateData } = body;

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    const validatedData = UpdateEnhancedMemberSchema.parse(updateData);

    // Update the member
    const member = await MemberManagementService.updateMember(
      user.congregationId,
      memberId,
      validatedData,
      user.userId
    );

    return NextResponse.json({
      success: true,
      member,
      message: 'Enhanced member profile updated successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Enhanced member update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid update data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        {
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to update member',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE - Remove member profile
 */
export async function DELETE(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Create permission context
    const context: UserPermissionContext = {
      userId: user.userId,
      role: user.role as ROLES,
      congregationId: user.congregationId,
      hasCongregationPinAccess: user.hasCongregationPinAccess,
    };

    // Check if user can manage members
    const canManage = await MemberManagementService.canManageMembers(context);
    if (!canManage) {
      return NextResponse.json(
        { error: 'Insufficient permissions to delete members' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const { memberId } = body;

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    // Delete the member
    await MemberManagementService.deleteMember(
      user.congregationId,
      memberId,
      user.userId
    );

    return NextResponse.json({
      success: true,
      message: 'Member deleted successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Enhanced member deletion error:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        {
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to delete member',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
