# Story 6.2: Weekend Meeting Enhancement and Speaker Management

**Epic:** Epic 6: Enhanced Meeting Management & JW.org Integration  
**Story Points:** 13  
**Priority:** High  
**Status:** Draft  

## Story

As a meeting coordinator,
I want enhanced weekend meeting management with comprehensive speaker coordination,
so that I can manage visiting speakers, track talk assignments, and coordinate complex weekend programs.

## Acceptance Criteria

1. **Visiting speaker database with contact information and talk inventory**
   - Comprehensive speaker database with contact details, congregation affiliation, and qualifications
   - Talk inventory management with outline numbers, titles, and speaker expertise levels
   - Speaker availability tracking with preferred dates and travel limitations
   - Integration with circuit and district speaker databases for broader coordination

2. **Speaker scheduling system with availability tracking and confirmation workflows**
   - Advanced scheduling interface with calendar integration and conflict detection
   - Automated confirmation workflows with email/SMS notifications and response tracking
   - Speaker availability management with blackout dates and preferred scheduling windows
   - Backup speaker system for last-minute cancellations and emergency replacements

3. **Talk assignment system with outline management and preparation tracking**
   - Comprehensive talk outline database with search and filtering capabilities
   - Assignment tracking with preparation deadlines and reminder notifications
   - Talk history management to avoid repetition and ensure variety
   - Integration with Watchtower study assignment coordination

4. **Meeting location coordination with hybrid (in-person/virtual) support**
   - Flexible meeting location management supporting Kingdom Hall, Zoom, and hybrid formats
   - Virtual meeting coordination with Zoom integration and technical support
   - Attendance tracking across multiple meeting formats and locations
   - Technical requirements management for visiting speakers using virtual platforms

5. **Speaker travel coordination and accommodation management**
   - Travel planning assistance with distance calculation and route optimization
   - Accommodation coordination with local family assignments and guest arrangements
   - Meal planning and hospitality coordination for visiting speakers
   - Expense tracking and reimbursement management for speaker travel costs

6. **Meeting feedback system for continuous improvement**
   - Post-meeting feedback collection from attendees and meeting coordinators
   - Speaker performance tracking with constructive feedback and development suggestions
   - Meeting quality metrics with trend analysis and improvement recommendations
   - Integration with elder body feedback for speaker development and encouragement

7. **Integration with circuit overseer visit scheduling**
   - Circuit overseer visit coordination with special meeting arrangements
   - Integration with circuit assembly and convention scheduling
   - Special program coordination for circuit overseer visits and district events
   - Coordination with other congregations for circuit-wide speaker exchanges

## Dev Notes

### Technical Architecture

**Frontend Components:**
- `WeekendMeetingManager.tsx` - Main weekend meeting coordination interface
- `SpeakerDatabase.tsx` - Comprehensive speaker management with search and filtering
- `TalkAssignmentScheduler.tsx` - Advanced talk scheduling with conflict detection
- `SpeakerTravelCoordinator.tsx` - Travel and accommodation management interface
- `MeetingFeedbackSystem.tsx` - Feedback collection and analysis dashboard
- `CircuitOverseerIntegration.tsx` - Circuit overseer visit coordination interface
- `HybridMeetingControls.tsx` - Multi-format meeting location management

**Backend Services:**
- `weekend-meeting-service.ts` - Enhanced weekend meeting management
- `speaker-management-service.ts` - Comprehensive speaker database and coordination
- `talk-assignment-service.ts` - Talk outline management and assignment tracking
- `travel-coordination-service.ts` - Speaker travel and accommodation management
- `meeting-feedback-service.ts` - Feedback collection and analysis
- `circuit-integration-service.ts` - Circuit overseer and assembly coordination
- `notification-service.ts` - Automated notifications and confirmations

**Database Tables:**
- `visiting_speakers` - Speaker database with contact and qualification information
- `talk_outlines` - Comprehensive talk outline database with metadata
- `speaker_assignments` - Talk assignments with preparation and confirmation tracking
- `speaker_travel` - Travel coordination and accommodation management
- `meeting_feedback` - Feedback collection and quality metrics
- `circuit_events` - Circuit overseer visits and special event coordination

### API Endpoints (tRPC)

```typescript
// Weekend meeting and speaker management routes
weekendMeetings: router({
  createSpeakerAssignment: adminProcedure
    .input(z.object({
      meetingId: z.string(),
      speakerId: z.string(),
      talkOutlineId: z.string(),
      confirmationRequired: z.boolean(),
      travelRequired: z.boolean(),
      accommodationNeeded: z.boolean()
    }))
    .mutation(async ({ input, ctx }) => {
      return await speakerAssignmentService.createAssignment(
        input,
        ctx.user.congregationId
      );
    }),

  getSpeakerAvailability: protectedProcedure
    .input(z.object({
      speakerId: z.string(),
      dateRange: z.object({
        start: z.date(),
        end: z.date()
      })
    }))
    .query(async ({ input, ctx }) => {
      return await speakerManagementService.getAvailability(
        input.speakerId,
        input.dateRange,
        ctx.user.congregationId
      );
    }),

  searchTalkOutlines: protectedProcedure
    .input(z.object({
      searchTerm: z.string().optional(),
      category: z.string().optional(),
      difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
      duration: z.number().optional()
    }))
    .query(async ({ input, ctx }) => {
      return await talkOutlineService.searchOutlines(
        input,
        ctx.user.congregationId
      );
    }),

  coordinateTravel: adminProcedure
    .input(z.object({
      assignmentId: z.string(),
      travelDetails: z.object({
        departureTime: z.date(),
        arrivalTime: z.date(),
        transportationMethod: z.string(),
        accommodationFamily: z.string().optional(),
        mealArrangements: z.string().optional()
      })
    }))
    .mutation(async ({ input, ctx }) => {
      return await travelCoordinationService.coordinateTravel(
        input.assignmentId,
        input.travelDetails,
        ctx.user.congregationId
      );
    }),

  submitMeetingFeedback: protectedProcedure
    .input(z.object({
      meetingId: z.string(),
      feedback: z.object({
        overallRating: z.number().min(1).max(5),
        speakerRating: z.number().min(1).max(5),
        contentQuality: z.number().min(1).max(5),
        technicalQuality: z.number().min(1).max(5),
        comments: z.string().optional(),
        suggestions: z.string().optional()
      })
    }))
    .mutation(async ({ input, ctx }) => {
      return await meetingFeedbackService.submitFeedback(
        input.meetingId,
        input.feedback,
        ctx.user.memberId,
        ctx.user.congregationId
      );
    })
})
```

### Data Models

```typescript
interface VisitingSpeaker {
  id: string;
  congregationId: string;
  name: string;
  congregation: string;
  contactInfo: {
    phone: string;
    email: string;
    address: string;
  };
  qualifications: string[];
  specialties: string[];
  travelRadius: number;
  availability: {
    preferredDays: number[];
    blackoutDates: Date[];
    advanceNoticeRequired: number;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface TalkOutline {
  id: string;
  outlineNumber: string;
  title: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number;
  keyPoints: string[];
  scriptures: string[];
  targetAudience: string;
  lastUpdated: Date;
  isActive: boolean;
}

interface SpeakerAssignment {
  id: string;
  congregationId: string;
  meetingId: string;
  speakerId: string;
  talkOutlineId: string;
  assignmentDate: Date;
  confirmationStatus: 'pending' | 'confirmed' | 'declined' | 'cancelled';
  preparationDeadline: Date;
  travelRequired: boolean;
  accommodationNeeded: boolean;
  specialRequirements: string[];
  confirmationDate?: Date;
  cancellationReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface SpeakerTravel {
  id: string;
  assignmentId: string;
  congregationId: string;
  travelDetails: {
    departureTime: Date;
    arrivalTime: Date;
    transportationMethod: string;
    estimatedCost: number;
  };
  accommodation: {
    hostFamily: string;
    contactInfo: string;
    specialRequirements: string[];
  };
  mealArrangements: {
    lunch: boolean;
    dinner: boolean;
    specialDietary: string[];
  };
  status: 'planned' | 'confirmed' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}
```

### Critical Implementation Requirements

1. **Multi-Congregation Speaker Coordination**: Enable speaker sharing across congregations with proper coordination
2. **Multi-Tenant Data Isolation**: Every database query must include congregation_id filtering
3. **Type Safety Enforcement**: All API calls must use tRPC procedures with Zod validation
4. **Authentication Required**: All protected routes must use authentication middleware
5. **Database-First Testing**: Use real database with comprehensive test data for speakers and assignments
6. **Notification System**: Implement automated notifications for confirmations and reminders

### Testing Requirements

**Unit Tests:**
- Speaker database management with search and filtering
- Talk assignment logic with conflict detection
- Travel coordination workflows with accommodation management
- Feedback collection and analysis algorithms

**Integration Tests:**
- Complete weekend meeting coordination workflow
- Multi-congregation speaker sharing and coordination
- Circuit overseer visit integration and special event coordination
- Notification system with confirmation workflows

**E2E Tests:**
- Full speaker assignment workflow from invitation to completion
- Travel coordination and accommodation management user experience
- Meeting feedback collection and analysis dashboard
- Circuit overseer visit coordination interface

## Testing

### Test Data Requirements

- Seed database with diverse speaker profiles and talk inventories
- Include complex travel scenarios and accommodation arrangements
- Test data should include circuit overseer visits and special events
- Sample feedback data for analytics and trend analysis

### Validation Scenarios

- Test speaker coordination across multiple congregations
- Validate travel coordination with various transportation methods
- Test feedback system with different user roles and permissions
- Verify circuit overseer integration with special meeting arrangements

## Definition of Done

- [ ] Visiting speaker database with contact information and talk inventory complete
- [ ] Speaker scheduling system with availability tracking implemented
- [ ] Talk assignment system with outline management functional
- [ ] Meeting location coordination with hybrid support working
- [ ] Speaker travel coordination and accommodation management complete
- [ ] Meeting feedback system for continuous improvement implemented
- [ ] Integration with circuit overseer visit scheduling functional
- [ ] All unit tests pass with real database data
- [ ] Integration tests validate multi-congregation coordination
- [ ] E2E tests confirm complete speaker management workflow
- [ ] Code review completed and approved
- [ ] Documentation updated with speaker management features

## Dev Agent Record

### Agent Model Used
- Model: Claude Sonnet 4
- Agent: Product Manager
- Date: 2025-01-24

### Debug Log References
- None yet

### Completion Notes
- Story created with comprehensive weekend meeting and speaker management features
- Advanced speaker coordination with multi-congregation support
- Travel and accommodation management with detailed coordination workflows
- Complete API specification with tRPC procedures for speaker management
- Testing requirements defined with complex coordination scenario validation

### File List
- docs/stories/6.2.story.md (created)

### Change Log
- 2025-01-24: Initial story creation with weekend meeting enhancement specification
