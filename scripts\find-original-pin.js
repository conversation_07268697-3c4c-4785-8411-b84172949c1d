#!/usr/bin/env node

/**
 * Find Original PIN
 * 
 * This script tests common PINs against the restored hash to find the original PIN.
 */

const bcrypt = require('bcryptjs');

async function findOriginalPin() {
    console.log('🔍 FINDING ORIGINAL PIN...');
    console.log('');

    const storedHash = '$2a$12$pA6bAbsifSWflfFbZilMYOAeFyKl/OGOeCuiELE1YmBnMMHEx4osVm';
    
    // Common PINs to test
    const testPins = [
        '123456',
        '000000',
        '1441',
        '654321',
        '111111',
        '222222',
        '333333',
        '444444',
        '555555',
        '666666',
        '777777',
        '888888',
        '999999',
        '1234',
        '4321',
        '0000',
        '1111',
        '2222',
        '3333',
        '4444',
        '5555',
        '6666',
        '7777',
        '8888',
        '9999',
        'admin',
        'password',
        'coraleste',
        'coral',
        'oeste',
        'hermanos',
        'richard',
        'rubi',
        'eduardo',
        'luscinian'
    ];

    console.log('🔐 Testing common PINs against stored hash...');
    console.log(`   Hash: ${storedHash.substring(0, 30)}...`);
    console.log('');

    for (const testPin of testPins) {
        try {
            const isMatch = await bcrypt.compare(testPin, storedHash);
            console.log(`   ${testPin.padEnd(15)} : ${isMatch ? '✅ MATCH!' : '❌ No match'}`);
            
            if (isMatch) {
                console.log('');
                console.log('🎉 FOUND THE ORIGINAL PIN!');
                console.log(`   Congregation PIN: ${testPin}`);
                console.log('');
                console.log('🎯 LOGIN CREDENTIALS:');
                console.log('   Congregation ID: 1441');
                console.log(`   Congregation PIN: ${testPin}`);
                console.log('');
                console.log('📋 TEST AUTHENTICATION:');
                console.log('   1. Go to http://localhost:3000/login');
                console.log('   2. Enter Congregation ID: 1441');
                console.log(`   3. Enter Congregation PIN: ${testPin}`);
                console.log('   4. Login should work now!');
                return;
            }
        } catch (error) {
            console.log(`   ${testPin.padEnd(15)} : ❌ Error testing`);
        }
    }

    console.log('');
    console.log('❌ PIN NOT FOUND in common list');
    console.log('💡 The original PIN might be a custom value');
    console.log('');
    console.log('🔧 SOLUTION: Set a known PIN for testing');
    console.log('   You can update the PIN in Prisma Studio or run:');
    console.log('   node scripts/set-test-pin.js');
}

// Run the search
findOriginalPin();
