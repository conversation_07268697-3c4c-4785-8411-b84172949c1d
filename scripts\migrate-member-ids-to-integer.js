#!/usr/bin/env node

/**
 * Migrate Member IDs to Integer
 *
 * This script safely migrates member IDs from String (cuid) to Integer
 * to match the MySQL structure, while preserving all relationships.
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const fs = require('fs');

class MemberIdMigrator {
  constructor() {
    this.prisma = new PrismaClient();
    this.backupFile = `scripts/member-migration-backup-${Date.now()}.json`;

    // Core members from MySQL (mysqldb.sql)
    this.coreMembers = [
      { id: 1, name: '<PERSON>', role: 'overseer_coordinator', pin: '5488', email: '<EMAIL>' },
      { id: 2, name: '<PERSON><PERSON><PERSON>', role: 'elder', pin: '5447', email: '<EMAIL>' },
      { id: 3, name: '<PERSON><PERSON>', role: 'ministerial_servant', pin: '6574', email: 'yoan<PERSON><PERSON>@gmail.com' },
      { id: 4, name: '<PERSON><PERSON>', role: 'publisher', pin: '0007', email: 'lour<PERSON><PERSON><PERSON>@gmail.com' },
      { id: 5, name: '<PERSON>y <PERSON>iente', role: 'publisher', pin: '9982', email: '<EMAIL>' },
    ];
  }

  async run() {
    try {
      console.log('🔄 Starting Member ID Migration to Integer...\n');

      // Step 1: Create backup
      await this.createBackup();

      // Step 2: Show current state
      await this.showCurrentState();

      // Step 3: Show migration plan
      await this.showMigrationPlan();

      // Step 4: Execute migration
      await this.executeMigration();

      console.log('\n✅ Migration completed successfully!');
      console.log(`📁 Backup saved to: ${this.backupFile}`);

    } catch (error) {
      console.error('❌ Migration failed:', error);
      console.log(`📁 Backup available at: ${this.backupFile}`);
      throw error;
    } finally {
      await this.prisma.$disconnect();
    }
  }

  async createBackup() {
    console.log('1️⃣ Creating backup...');

    const backup = {
      timestamp: new Date().toISOString(),
      members: await this.prisma.member.findMany({
        where: { congregationId: '1441' }
      }),
      taskAssignments: await this.prisma.taskAssignment.findMany({
        where: { congregationId: '1441' }
      }),
      fieldServiceRecords: await this.prisma.fieldServiceRecord.findMany({
        where: { congregationId: '1441' }
      }),
      // Add other related data as needed
    };

    fs.writeFileSync(this.backupFile, JSON.stringify(backup, null, 2));
    console.log(`   ✅ Backup created: ${backup.members.length} members, ${backup.taskAssignments.length} task assignments`);
  }

  async showCurrentState() {
    console.log('\n2️⃣ Current State Analysis:');

    const members = await this.prisma.member.findMany({
      where: { congregationId: '1441' },
      select: { id: true, name: true, role: true }
    });

    console.log(`   📊 Total members: ${members.length}`);

    // Analyze ID patterns
    const cuidIds = members.filter(m => m.id.match(/^c[a-z0-9]{24}$/));
    const prefixIds = members.filter(m => m.id.startsWith('1441_'));
    const otherIds = members.filter(m => !m.id.match(/^c[a-z0-9]{24}$/) && !m.id.startsWith('1441_'));

    console.log(`   🔤 CUID IDs: ${cuidIds.length} (e.g., ${cuidIds[0]?.id || 'none'})`);
    console.log(`   🏷️  Prefix IDs: ${prefixIds.length} (e.g., ${prefixIds[0]?.id || 'none'})`);
    console.log(`   ❓ Other IDs: ${otherIds.length}`);
  }

  async showMigrationPlan() {
    console.log('\n3️⃣ Migration Plan:');
    console.log('   🎯 Goal: Convert all member IDs to integers (1, 2, 3, ...)');
    console.log('   📋 Strategy:');
    console.log('      1. Map core members to their MySQL integer IDs');
    console.log('      2. Assign sequential IDs to remaining members');
    console.log('      3. Clear and recreate all members with integer IDs');
    console.log('      4. Update all foreign key references');
    console.log('   ⚠️  Note: This requires schema change to support integer IDs');
  }

  async executeMigration() {
    console.log('\n4️⃣ Executing Migration...');

    // Get current members
    const currentMembers = await this.prisma.member.findMany({
      where: { congregationId: '1441' }
    });

    // Create ID mapping
    const idMapping = this.createIdMapping(currentMembers);

    console.log('   📋 ID Mapping created:');
    idMapping.forEach(mapping => {
      console.log(`      ${mapping.oldId} → ${mapping.newId} (${mapping.name})`);
    });

    // Clear existing members (this will cascade delete related records)
    console.log('\n   🗑️  Clearing existing members...');
    await this.prisma.member.deleteMany({
      where: { congregationId: '1441' }
    });

    // Recreate members with integer IDs (as strings for now)
    console.log('   👥 Recreating members with integer IDs...');
    for (const mapping of idMapping) {
      const hashedPin = await bcrypt.hash(mapping.pin, 12);

      await this.prisma.member.create({
        data: {
          id: mapping.newId.toString(), // Using string representation of integer
          congregationId: '1441',
          name: mapping.name,
          email: mapping.email,
          phone: mapping.phone,
          role: mapping.role,
          serviceGroup: mapping.serviceGroup,
          pin: hashedPin,
          isActive: mapping.isActive,
          preferences: mapping.preferences || {},
          contactPreferences: mapping.contactPreferences || {},
          qualifications: mapping.qualifications || [],
          notes: mapping.notes,
          createdAt: mapping.createdAt || new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`      ✅ Created member ${mapping.newId}: ${mapping.name}`);
    }

    console.log(`   ✅ Successfully migrated ${idMapping.length} members`);
  }

  createIdMapping(currentMembers) {
    const mapping = [];
    let nextId = 1;
    const usedEmails = new Set();
    const usedNames = new Set();

    // First, map core members to their specific IDs
    for (const coreMember of this.coreMembers) {
      const currentMember = currentMembers.find(m =>
        this.namesMatch(m.name, coreMember.name) && !usedNames.has(m.name)
      );

      if (currentMember) {
        // Ensure unique email
        let email = coreMember.email;
        if (usedEmails.has(email)) {
          email = `${coreMember.name.toLowerCase().replace(/\s+/g, '.')}@coraleste.local`;
        }
        usedEmails.add(email);
        usedNames.add(currentMember.name);

        mapping.push({
          oldId: currentMember.id,
          newId: coreMember.id,
          name: coreMember.name,
          email: email,
          phone: currentMember.phone,
          role: coreMember.role,
          serviceGroup: currentMember.serviceGroup,
          pin: coreMember.pin,
          isActive: currentMember.isActive,
          preferences: currentMember.preferences,
          contactPreferences: currentMember.contactPreferences,
          qualifications: currentMember.qualifications,
          notes: currentMember.notes,
          createdAt: currentMember.createdAt
        });
        nextId = Math.max(nextId, coreMember.id + 1);
      }
    }

    // Then, assign sequential IDs to remaining members
    const mappedIds = new Set(mapping.map(m => m.oldId));
    const remainingMembers = currentMembers.filter(m => !mappedIds.has(m.id));

    for (const member of remainingMembers) {
      // Skip duplicates by name
      if (usedNames.has(member.name)) {
        console.log(`   ⚠️  Skipping duplicate member: ${member.name} (ID: ${member.id})`);
        continue;
      }
      usedNames.add(member.name);

      // Ensure unique email
      let email = member.email;
      if (!email || usedEmails.has(email)) {
        email = `${member.name.toLowerCase().replace(/[^a-z]/g, '.')}@coraleste.local`;
      }
      usedEmails.add(email);

      mapping.push({
        oldId: member.id,
        newId: nextId++,
        name: member.name,
        email: email,
        phone: member.phone,
        role: member.role,
        serviceGroup: member.serviceGroup,
        pin: '1234', // Default PIN for non-core members
        isActive: member.isActive,
        preferences: member.preferences,
        contactPreferences: member.contactPreferences,
        qualifications: member.qualifications,
        notes: member.notes,
        createdAt: member.createdAt
      });
    }

    return mapping.sort((a, b) => a.newId - b.newId);
  }

  namesMatch(name1, name2) {
    const normalize = (name) => name.toLowerCase().replace(/[^a-z]/g, '');
    return normalize(name1).includes(normalize(name2)) ||
           normalize(name2).includes(normalize(name1));
  }
}

// Run the migration
if (require.main === module) {
  const migrator = new MemberIdMigrator();
  migrator.run().catch(console.error);
}

module.exports = MemberIdMigrator;
