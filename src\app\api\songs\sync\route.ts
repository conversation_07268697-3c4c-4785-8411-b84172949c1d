/**
 * Song Synchronization API Endpoint
 *
 * Handles synchronization with JW.org song catalog including:
 * - Fetching latest song titles from JW.org
 * - Updating existing songs with new titles
 * - Adding new songs to the catalog
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';
import { SongSyncService } from '@/lib/services/songSyncService';

// Validation schema
const syncRequestSchema = z.object({
  language: z.enum(['es', 'en']).optional().default('es'),
  forceUpdate: z.boolean().optional().default(false),
});

/**
 * POST /api/songs/sync
 * Synchronize song catalog with JW.org
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication and admin permissions
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!['overseer', 'elder'].includes(authResult.user.role)) {
      return NextResponse.json(
        { error: 'Elder permissions required for sync operations' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json().catch(() => ({}));
    const validationResult = syncRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { language, forceUpdate } = validationResult.data;

    // Log the sync operation
    console.log(`Song sync initiated by ${authResult.user.name} for language: ${language}`);

    // Perform the synchronization
    const syncResult = await SongSyncService.syncSongCatalog(language, forceUpdate);

    // Add additional metadata
    const response = {
      ...syncResult,
      message: syncResult.success
        ? 'Song synchronization completed successfully'
        : 'Song synchronization completed with errors',
      timestamp: syncResult.timestamp.toISOString(),
      initiatedBy: authResult.user.name,
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error during song synchronization:', error);
    return NextResponse.json(
      { error: 'Internal server error during synchronization' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/songs/sync
 * Get last synchronization status
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get last update timestamp from songs table
    const lastUpdate = await prisma.song.findFirst({
      orderBy: { updatedAt: 'desc' },
      select: { updatedAt: true },
    });

    // Get total songs count
    const totalSongs = await prisma.song.count();

    return NextResponse.json({
      lastSyncDate: lastUpdate?.updatedAt || null,
      totalSongs,
      syncAvailable: true,
      supportedLanguages: ['es', 'en'],
    });

  } catch (error) {
    console.error('Error fetching sync status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
