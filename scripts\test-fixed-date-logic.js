/**
 * Test Fixed Date Logic
 * 
 * This script tests the FIXED date filtering logic using string comparison
 */

// Simulate the dates from the database
const serviceDates = [
  '2025-07-25T00:00:00.000Z',
  '2025-07-26T00:00:00.000Z'
];

console.log('🧪 TESTING FIXED DATE LOGIC');
console.log('===========================');

// Get today's date as string
const todayStr = new Date().toISOString().split('T')[0];
console.log('📅 Today (string):', todayStr);

// Test the FIXED filtering logic for each service date
serviceDates.forEach((serviceDateStr, index) => {
  console.log(`\n🔍 Testing Service Date ${index + 1}: ${serviceDateStr}`);
  
  // Parse dates as strings to avoid timezone conversion issues
  const serviceDateOnly = serviceDateStr.split('T')[0]; // Extract YYYY-MM-DD
  
  const isHistorical = serviceDateOnly < todayStr;
  const isUpcoming = serviceDateOnly >= todayStr;
  
  console.log('  📅 Service Date (string):', serviceDateOnly);
  console.log('  📅 Today (string):', todayStr);
  console.log('  📊 String Comparison:');
  console.log('    - Service < Today:', serviceDateOnly < todayStr);
  console.log('    - Service >= Today:', serviceDateOnly >= todayStr);
  console.log('  📋 Results:');
  console.log('    - Is Historical:', isHistorical);
  console.log('    - Is Upcoming:', isUpcoming);
  console.log('  🎯 Would show in:');
  console.log('    - Historical view:', isHistorical ? '✅ YES' : '❌ NO');
  console.log('    - Upcoming view:', isUpcoming ? '✅ YES' : '❌ NO');
});

console.log('\n📊 SUMMARY');
console.log('==========');
console.log('With string comparison, upcoming schedules should now show correctly!');

// Test with showHistoricalData = false (upcoming mode)
console.log('\n🎯 UPCOMING MODE TEST (showHistoricalData = false)');
const upcomingSchedules = serviceDates.filter(serviceDateStr => {
  const serviceDateOnly = serviceDateStr.split('T')[0];
  return serviceDateOnly >= todayStr;
});
console.log(`Found ${upcomingSchedules.length} upcoming schedules:`, upcomingSchedules);

// Test with showHistoricalData = true (historical mode)
console.log('\n📜 HISTORICAL MODE TEST (showHistoricalData = true)');
const historicalSchedules = serviceDates.filter(serviceDateStr => {
  const serviceDateOnly = serviceDateStr.split('T')[0];
  return serviceDateOnly < todayStr;
});
console.log(`Found ${historicalSchedules.length} historical schedules:`, historicalSchedules);
