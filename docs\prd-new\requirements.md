# Requirements

## Functional Requirements

**FR1**: Simple JWT-based authentication system preserving existing congregation-based login with congregation ID and PIN, supporting hybrid permission model (Coordinator, Elder, Ministerial Servant, Publisher) with congregation PIN access for super admin capabilities, 60-day mobile-friendly token expiration (configurable by coordinators)

**FR2**: Single authentication system for all users with conditional administrative access - all users authenticate through the same login page, but coordinators, elders with delegated permissions, and ministerial servants see an additional "Administración" button on the dashboard that provides access to the administrative section based on their role or delegated permissions

**FR3**: Simple role-based permissions system preserving existing administrative workflows without over-complication, maintaining current delegation patterns

**FR4**: Member management system with complete CRUD operations, preserving existing member profile structure and PIN management workflows with bcrypt security

**FR5**: Meeting management system preserving all existing assignment logic and workflows, with improved data validation and congregation isolation

**FR6**: Field service management enabling members to track service time and administrators to manage territories and service groups within delegated authority

**FR7**: Midweek meeting management supporting Life and Ministry Meeting Workbook integration with preserved JW.org data fetching logic and improved assignment workflows

**FR8**: Weekend meeting management for public talks, Watchtower study assignments, and visiting speaker coordination with enhanced assignment capabilities

**FR9**: Task management system allowing delegated administrators to create and assign congregation tasks with proper authorization validation

**FR10**: Assignment management for tracking personal and congregation assignments with completion status and improved assignment logic

**FR11**: Letters management system supporting PDF upload, categorization, and controlled access with administrative delegation

**FR12**: Events management for creating and managing congregation events with delegated administrative control

**FR13**: Song management with multilingual support and preserved JW.org integration for dynamic song title retrieval

**FR14**: Multi-congregation architecture with PostgreSQL tenant isolation using congregation_id, supporting Coral Oeste Spanish as the first congregation with Spanish as default language

**FR15**: Preserved multilingual support for Spanish and English with foundation for additional languages using existing patterns

**FR16**: Exact preservation of existing theme and color system for section management and visual identity

**FR17**: PostgreSQL-based backup and restore functionality with automated daily backups and manual restore capabilities

**FR18**: Pixel-perfect responsive web design preserving existing mobile-first UI structure and touch optimization

**FR19**: Complete preservation of existing JW.org integration logic, URL patterns, caching mechanisms, and fallback strategies

**FR20**: Preserved administrative interface design with Next.js-based admin access for congregation leadership

## Non-Functional Requirements

**NFR1**: Application must support concurrent access by multiple congregations (estimated 50-100 users per congregation) without performance degradation

**NFR2**: Database operations must complete within 2 seconds for standard queries and 10 seconds for complex reports

**NFR3**: System must maintain 99.5% uptime during congregation meeting times and field service periods

**NFR4**: All user data must be encrypted in transit and at rest, with secure authentication token management

**NFR5**: Application must be fully responsive and functional on mobile devices with preserved touch-optimized interfaces

**NFR6**: System must support data export and import capabilities for congregation data portability

**NFR7**: Application must gracefully handle offline scenarios with appropriate user feedback and retry mechanisms

**NFR8**: Next.js application with TypeScript strict mode compliance for type safety and maintainability, using Prisma ORM for type-safe database operations

**NFR9**: Complete PostgreSQL migration preserving all 41 MySQL tables with zero data loss and maintained relationships using Prisma migrations

**NFR10**: System must maintain backward compatibility with existing data structures during migration period
