#!/usr/bin/env node

/**
 * Clear Database and Restore from Backup
 * 
 * This script completely clears the database and restores from the
 * hermanos-07-25-25E.sql backup file.
 */

const { PrismaClient } = require('@prisma/client');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function clearAndRestoreDatabase() {
    console.log('🗑️  CLEARING DATABASE AND RESTORING FROM BACKUP...');
    console.log('');

    try {
        // 1. Check if backup file exists
        const backupFile = path.join(__dirname, '../hermanos-07-25-25E.sql');
        if (!fs.existsSync(backupFile)) {
            throw new Error('Backup file hermanos-07-25-25E.sql not found');
        }
        console.log('✅ Backup file found: hermanos-07-25-25E.sql');

        // 2. Get database connection info from environment
        const dbHost = process.env.DATABASE_HOST || 'localhost';
        const dbPort = process.env.DATABASE_PORT || '5432';
        const dbName = process.env.DATABASE_NAME || 'hermanos';
        const dbUser = process.env.DATABASE_USER || 'mywebsites';
        const dbPassword = process.env.DATABASE_PASSWORD || '';

        console.log(`📋 Database connection: ${dbUser}@${dbHost}:${dbPort}/${dbName}`);

        // 3. Disconnect Prisma
        console.log('🔌 Disconnecting Prisma...');
        await prisma.$disconnect();

        // 4. Drop and recreate database
        console.log('🗑️  Dropping and recreating database...');
        
        const dropDbCommand = `psql -h ${dbHost} -p ${dbPort} -U ${dbUser} -c "DROP DATABASE IF EXISTS ${dbName};"`;
        const createDbCommand = `psql -h ${dbHost} -p ${dbPort} -U ${dbUser} -c "CREATE DATABASE ${dbName};"`;

        await executeCommand(dropDbCommand, 'Drop database');
        await executeCommand(createDbCommand, 'Create database');

        // 5. Restore from backup
        console.log('📥 Restoring from backup...');
        
        const restoreCommand = `psql -h ${dbHost} -p ${dbPort} -U ${dbUser} -d ${dbName} -f "${backupFile}"`;
        await executeCommand(restoreCommand, 'Restore backup');

        console.log('');
        console.log('🎉 DATABASE RESTORED SUCCESSFULLY!');
        console.log('');
        console.log('📋 NEXT STEPS:');
        console.log('   1. Restart the development server');
        console.log('   2. Test authentication with restored data');
        console.log('   3. Verify all features work correctly');
        console.log('');
        console.log('🔍 TO CHECK RESTORED DATA:');
        console.log('   - Use Prisma Studio: npx prisma studio');
        console.log('   - Check congregation and member data');
        console.log('   - Verify login credentials work');

    } catch (error) {
        console.error('❌ Error during database restoration:', error);
        throw error;
    }
}

function executeCommand(command, description) {
    return new Promise((resolve, reject) => {
        console.log(`   🔄 ${description}...`);
        
        exec(command, { env: { ...process.env, PGPASSWORD: process.env.DATABASE_PASSWORD } }, (error, stdout, stderr) => {
            if (error) {
                console.error(`   ❌ ${description} failed:`, error.message);
                reject(error);
                return;
            }
            
            if (stderr && !stderr.includes('NOTICE')) {
                console.error(`   ⚠️  ${description} warnings:`, stderr);
            }
            
            console.log(`   ✅ ${description} completed`);
            resolve(stdout);
        });
    });
}

// Alternative method using pg_restore if needed
async function alternativeRestore() {
    console.log('🔄 TRYING ALTERNATIVE RESTORE METHOD...');
    
    try {
        const dbHost = process.env.DATABASE_HOST || 'localhost';
        const dbPort = process.env.DATABASE_PORT || '5432';
        const dbName = process.env.DATABASE_NAME || 'hermanos';
        const dbUser = process.env.DATABASE_USER || 'mywebsites';
        
        const backupFile = path.join(__dirname, '../hermanos-07-25-25E.sql');
        
        // Try direct psql import
        const importCommand = `psql "postgresql://${dbUser}@${dbHost}:${dbPort}/${dbName}" < "${backupFile}"`;
        await executeCommand(importCommand, 'Direct SQL import');
        
        console.log('✅ Alternative restore method successful');
        
    } catch (error) {
        console.error('❌ Alternative restore failed:', error);
        throw error;
    }
}

// Run the restoration
clearAndRestoreDatabase()
    .catch(async (error) => {
        console.error('Primary restore method failed, trying alternative...');
        try {
            await alternativeRestore();
        } catch (altError) {
            console.error('All restore methods failed:', altError);
            process.exit(1);
        }
    });
