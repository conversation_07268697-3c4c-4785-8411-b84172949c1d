/**
 * Field Service Management Service
 *
 * Comprehensive service for managing field service records, statistics,
 * and reporting with proper validation and congregation isolation.
 */

import { prisma } from '@/lib/prisma';
import { Decimal } from '@prisma/client/runtime/library';

export interface ServiceRecord {
  id: string;
  congregationId: string;
  memberId: string;
  serviceMonth: Date;
  hours: Decimal | null;
  placements: number | null;
  videoShowings: number | null;
  returnVisits: number | null;
  bibleStudies: number | null;
  notes: string | null;
  isSubmitted: boolean;
  submittedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  member?: {
    id: string;
    name: string;
    role: string;
  };
}

export interface ServiceRecordInput {
  serviceMonth: string; // YYYY-MM format
  hours?: number;
  placements?: number;
  videoShowings?: number;
  returnVisits?: number;
  bibleStudies?: number;
  notes?: string;
}

export interface ServiceStatistics {
  totalHours: number;
  totalPlacements: number;
  totalVideoShowings: number;
  totalReturnVisits: number;
  totalBibleStudies: number;
  averageHours: number;
  activePublishers: number;
  submittedReports: number;
  pendingReports: number;
}

export interface MonthlyServiceSummary {
  serviceMonth: string;
  statistics: ServiceStatistics;
  memberReports: ServiceRecord[];
  submissionRate: number;
}

export class FieldServiceManagementService {
  /**
   * Get service record for a specific member and month
   */
  static async getServiceRecord(
    congregationId: string,
    memberId: string,
    serviceMonth: string
  ): Promise<ServiceRecord | null> {
    try {
      // Parse service month (YYYY-MM format)
      const [year, month] = serviceMonth.split('-').map(Number);
      const monthDate = new Date(year, month - 1, 1);

      const record = await prisma.fieldServiceRecord.findUnique({
        where: {
          memberId_serviceMonth: {
            memberId,
            serviceMonth: monthDate,
          },
        },
        include: {
          member: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return record;
    } catch (error) {
      console.error('Error fetching service record:', error);
      throw new Error('Failed to fetch service record');
    }
  }

  /**
   * Create or update service record
   */
  static async upsertServiceRecord(
    congregationId: string,
    memberId: string,
    recordData: ServiceRecordInput
  ): Promise<ServiceRecord> {
    try {
      // Parse service month
      const [year, month] = recordData.serviceMonth.split('-').map(Number);
      const monthDate = new Date(year, month - 1, 1);

      // Validate member belongs to congregation
      const member = await prisma.member.findFirst({
        where: {
          id: memberId,
          congregationId,
        },
      });

      if (!member) {
        throw new Error('Member not found or does not belong to congregation');
      }

      // Validate numeric values
      const validatedData = {
        hours: recordData.hours ? new Decimal(recordData.hours) : null,
        placements: recordData.placements || 0,
        videoShowings: recordData.videoShowings || 0,
        returnVisits: recordData.returnVisits || 0,
        bibleStudies: recordData.bibleStudies || 0,
        notes: recordData.notes || null,
      };

      // Check if record is already submitted
      const existingRecord = await prisma.fieldServiceRecord.findUnique({
        where: {
          memberId_serviceMonth: {
            memberId,
            serviceMonth: monthDate,
          },
        },
      });

      if (existingRecord?.isSubmitted) {
        throw new Error('Cannot modify submitted service record');
      }

      const record = await prisma.fieldServiceRecord.upsert({
        where: {
          memberId_serviceMonth: {
            memberId,
            serviceMonth: monthDate,
          },
        },
        update: {
          ...validatedData,
          updatedAt: new Date(),
        },
        create: {
          congregationId,
          memberId,
          serviceMonth: monthDate,
          ...validatedData,
        },
        include: {
          member: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return record;
    } catch (error) {
      console.error('Error upserting service record:', error);
      throw error;
    }
  }

  /**
   * Submit service record (mark as submitted)
   */
  static async submitServiceRecord(
    congregationId: string,
    memberId: string,
    serviceMonth: string
  ): Promise<ServiceRecord> {
    try {
      const [year, month] = serviceMonth.split('-').map(Number);
      const monthDate = new Date(year, month - 1, 1);

      const record = await prisma.fieldServiceRecord.findUnique({
        where: {
          memberId_serviceMonth: {
            memberId,
            serviceMonth: monthDate,
          },
        },
      });

      if (!record) {
        throw new Error('Service record not found');
      }

      if (record.congregationId !== congregationId) {
        throw new Error('Unauthorized access to service record');
      }

      if (record.isSubmitted) {
        throw new Error('Service record already submitted');
      }

      const updatedRecord = await prisma.fieldServiceRecord.update({
        where: {
          id: record.id,
        },
        data: {
          isSubmitted: true,
          submittedAt: new Date(),
          updatedAt: new Date(),
        },
        include: {
          member: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return updatedRecord;
    } catch (error) {
      console.error('Error submitting service record:', error);
      throw error;
    }
  }

  /**
   * Get service history for a member
   */
  static async getMemberServiceHistory(
    congregationId: string,
    memberId: string,
    limit: number = 12
  ): Promise<ServiceRecord[]> {
    try {
      const records = await prisma.fieldServiceRecord.findMany({
        where: {
          congregationId,
          memberId,
        },
        include: {
          member: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
        orderBy: {
          serviceMonth: 'desc',
        },
        take: limit,
      });

      return records;
    } catch (error) {
      console.error('Error fetching member service history:', error);
      throw new Error('Failed to fetch service history');
    }
  }

  /**
   * Calculate service statistics for a month
   */
  static async calculateMonthlyStatistics(
    congregationId: string,
    serviceMonth: string
  ): Promise<ServiceStatistics> {
    try {
      const [year, month] = serviceMonth.split('-').map(Number);
      const monthDate = new Date(year, month - 1, 1);

      const records = await prisma.fieldServiceRecord.findMany({
        where: {
          congregationId,
          serviceMonth: monthDate,
        },
      });

      const totalMembers = await prisma.member.count({
        where: {
          congregationId,
          isActive: true,
        },
      });

      const totalHours = records.reduce((sum, record) => {
        return sum + (record.hours ? parseFloat(record.hours.toString()) : 0);
      }, 0);

      const totalPlacements = records.reduce((sum, record) => sum + (record.placements || 0), 0);
      const totalVideoShowings = records.reduce((sum, record) => sum + (record.videoShowings || 0), 0);
      const totalReturnVisits = records.reduce((sum, record) => sum + (record.returnVisits || 0), 0);
      const totalBibleStudies = records.reduce((sum, record) => sum + (record.bibleStudies || 0), 0);

      const activePublishers = records.filter(record =>
        record.hours && parseFloat(record.hours.toString()) > 0
      ).length;

      const submittedReports = records.filter(record => record.isSubmitted).length;
      const pendingReports = totalMembers - submittedReports;

      const averageHours = activePublishers > 0 ? totalHours / activePublishers : 0;

      return {
        totalHours: Math.round(totalHours * 10) / 10,
        totalPlacements,
        totalVideoShowings,
        totalReturnVisits,
        totalBibleStudies,
        averageHours: Math.round(averageHours * 10) / 10,
        activePublishers,
        submittedReports,
        pendingReports,
      };
    } catch (error) {
      console.error('Error calculating monthly statistics:', error);
      throw new Error('Failed to calculate service statistics');
    }
  }

  /**
   * Get monthly service summary with all member reports
   */
  static async getMonthlyServiceSummary(
    congregationId: string,
    serviceMonth: string
  ): Promise<MonthlyServiceSummary> {
    try {
      const [year, month] = serviceMonth.split('-').map(Number);
      const monthDate = new Date(year, month - 1, 1);

      const [statistics, memberReports, totalMembers] = await Promise.all([
        this.calculateMonthlyStatistics(congregationId, serviceMonth),
        prisma.fieldServiceRecord.findMany({
          where: {
            congregationId,
            serviceMonth: monthDate,
          },
          include: {
            member: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
          },
          orderBy: {
            member: {
              name: 'asc',
            },
          },
        }),
        prisma.member.count({
          where: {
            congregationId,
            isActive: true,
          },
        }),
      ]);

      const submissionRate = totalMembers > 0
        ? Math.round((statistics.submittedReports / totalMembers) * 100)
        : 0;

      return {
        serviceMonth,
        statistics,
        memberReports,
        submissionRate,
      };
    } catch (error) {
      console.error('Error fetching monthly service summary:', error);
      throw new Error('Failed to fetch service summary');
    }
  }

  /**
   * Get members who haven't submitted reports for a month
   */
  static async getPendingReports(
    congregationId: string,
    serviceMonth: string
  ): Promise<Array<{ id: string; name: string; role: string }>> {
    try {
      const [year, month] = serviceMonth.split('-').map(Number);
      const monthDate = new Date(year, month - 1, 1);

      // Get all active members
      const allMembers = await prisma.member.findMany({
        where: {
          congregationId,
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          role: true,
        },
      });

      // Get members who have submitted reports
      const submittedReports = await prisma.fieldServiceRecord.findMany({
        where: {
          congregationId,
          serviceMonth: monthDate,
          isSubmitted: true,
        },
        select: {
          memberId: true,
        },
      });

      const submittedMemberIds = new Set(submittedReports.map(r => r.memberId));

      // Return members who haven't submitted
      return allMembers.filter(member => !submittedMemberIds.has(member.id));
    } catch (error) {
      console.error('Error fetching pending reports:', error);
      throw new Error('Failed to fetch pending reports');
    }
  }

  /**
   * Validate service record data
   */
  static validateServiceRecord(data: ServiceRecordInput): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate service month format
    if (!data.serviceMonth || !/^\d{4}-\d{2}$/.test(data.serviceMonth)) {
      errors.push('Service month must be in YYYY-MM format');
    }

    // Validate numeric values
    if (data.hours !== undefined && (data.hours < 0 || data.hours > 999)) {
      errors.push('Hours must be between 0 and 999');
    }

    if (data.placements !== undefined && (data.placements < 0 || data.placements > 9999)) {
      errors.push('Placements must be between 0 and 9999');
    }

    if (data.videoShowings !== undefined && (data.videoShowings < 0 || data.videoShowings > 9999)) {
      errors.push('Video showings must be between 0 and 9999');
    }

    if (data.returnVisits !== undefined && (data.returnVisits < 0 || data.returnVisits > 9999)) {
      errors.push('Return visits must be between 0 and 9999');
    }

    if (data.bibleStudies !== undefined && (data.bibleStudies < 0 || data.bibleStudies > 999)) {
      errors.push('Bible studies must be between 0 and 999');
    }

    if (data.notes && data.notes.length > 1000) {
      errors.push('Notes cannot exceed 1000 characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get current service month in YYYY-MM format
   */
  static getCurrentServiceMonth(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * Format service month for display
   */
  static formatServiceMonth(serviceMonth: string): string {
    const [year, month] = serviceMonth.split('-');
    const monthNames = [
      'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
      'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  }
}
