const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

async function analyzeTerritory014Buildings() {
  const territoriosDir = path.join(process.cwd(), 'Territorios');
  const fileName = 'Terr. 014.xlsx';
  const filePath = path.join(territoriosDir, fileName);
  
  console.log(`🏢 Analyzing Territory 014 building structure...\n`);
  
  try {
    const workbook = XLSX.readFile(filePath);
    const sheet = workbook.Sheets['Terr 14'];
    const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    
    console.log(`Total rows: ${data.length}\n`);
    
    // Look for building patterns
    let currentBuilding = '';
    let currentStreet = '';
    const buildings = [];
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      if (!row || row.length === 0) continue;
      
      console.log(`Row ${i + 1}:`);
      for (let j = 0; j < Math.min(row.length, 8); j++) {
        const cell = row[j];
        if (cell !== undefined && cell !== null && cell !== '') {
          const cellStr = cell.toString().trim();
          console.log(`  Col ${j + 1}: "${cellStr}"`);
          
          // Look for building patterns
          if (cellStr.includes('Edif.') || cellStr.includes('Edificio')) {
            console.log(`    🏢 BUILDING DETECTED: ${cellStr}`);
            currentBuilding = cellStr;
          }
          
          // Look for street patterns
          if (/FLAGLER|ST|AVE|AVENUE|STREET|WAY|BLVD|BOULEVARD|RD|ROAD/i.test(cellStr) && !cellStr.includes('Edif')) {
            console.log(`    🛣️  STREET DETECTED: ${cellStr}`);
            currentStreet = cellStr;
          }
          
          // Look for building numbers (4-digit numbers that could be addresses)
          if (/^\d{4}$/.test(cellStr)) {
            console.log(`    🏠 BUILDING NUMBER: ${cellStr}`);
            if (currentStreet) {
              buildings.push({
                building: currentBuilding,
                address: `${cellStr} ${currentStreet}`,
                row: i + 1
              });
            }
          }
          
          // Look for apartment numbers
          if (/^\d{1,3}$/.test(cellStr) && parseInt(cellStr) > 0 && parseInt(cellStr) < 1000) {
            console.log(`    🚪 APARTMENT NUMBER: ${cellStr}`);
          }
        }
      }
      console.log('');
      
      // Stop after reasonable number of rows for analysis
      if (i > 50) break;
    }
    
    console.log(`\n🏢 Buildings found:`);
    buildings.forEach((building, index) => {
      console.log(`  ${index + 1}. ${building.address} (${building.building}) - Row ${building.row}`);
    });
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

analyzeTerritory014Buildings().catch(console.error);
