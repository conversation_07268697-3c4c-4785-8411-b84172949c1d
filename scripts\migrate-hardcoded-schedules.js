/**
 * Migration Script: Migrate Hardcoded Service Schedules to Database
 *
 * This script migrates the hardcoded service schedule data from the frontend
 * to the database using the existing API endpoints.
 */

// Use dynamic import for node-fetch
let fetch;

// Configuration
const BASE_URL = 'http://localhost:3000';
const CONGREGATION_ID = '1441'; // Coral Oeste
const CONGREGATION_PIN = '1930'; // Updated PIN based on scripts

// Hardcoded schedules to migrate (from the frontend)
const HARDCODED_SCHEDULES = [
  {
    date: '2024-11-19', // Updated to current dates
    time: '9:30 AM',
    location: 'Salon del Reino',
    address: '7790 West 4th Av Hialeah Fl 33014',
    conductor: 'Catalino Roque'
  },
  {
    date: '2024-11-19',
    time: '6:30 PM',
    location: 'Salon del Reino',
    address: '7790 West 4th Av Hialeah Fl 33014',
    conductor: '<PERSON>'
  },
  {
    date: '2024-11-20',
    time: '9:30 AM',
    location: 'Salon del Reino',
    address: '7790 West 4th Av Hialeah Fl 33014',
    conductor: '<PERSON><PERSON>'
  },
  {
    date: '2024-11-20',
    time: '6:30 PM',
    location: 'Salon del Reino',
    address: '7790 West 4th Av <PERSON>aleah Fl 33014',
    conductor: '<PERSON> <PERSON>'
  },
  {
    date: '2024-11-21',
    time: '9:25 AM',
    location: 'Zoom y luego al Territorio',
    address: '',
    conductor: '<PERSON> <PERSON> Valdes'
  },
  {
    date: '2024-11-22',
    time: '9:25 AM',
    location: 'Zoom y luego al Territorio',
    address: '',
    conductor: 'Juan Jesus Valdes'
  },
  {
    date: '2024-11-23',
    time: '9:25 AM',
    location: 'Zoom y luego al Territorio',
    address: '',
    conductor: 'Juan Jesus Valdes'
  },
  {
    date: '2024-11-24',
    time: '9:25 AM',
    location: 'Zoom y luego al Territorio',
    address: '',
    conductor: 'Juan Jesus Valdes'
  }
];

async function authenticateAsAdmin() {
  console.log('🔐 Authenticating as admin...');

  const response = await fetch(`${BASE_URL}/api/auth/congregation-login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      congregationId: CONGREGATION_ID,
      pin: CONGREGATION_PIN
    }),
  });

  if (!response.ok) {
    throw new Error(`Authentication failed: ${response.status}`);
  }

  const data = await response.json();
  console.log('✅ Authentication successful');
  return data.token;
}

async function getMembers(token) {
  console.log('👥 Fetching members...');

  const response = await fetch(`${BASE_URL}/api/admin/members`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch members: ${response.status}`);
  }

  const data = await response.json();
  console.log(`✅ Found ${data.members.length} members`);
  return data.members;
}

function findConductorId(conductorName, members) {
  const member = members.find(m =>
    m.name.toLowerCase().includes(conductorName.toLowerCase()) ||
    conductorName.toLowerCase().includes(m.name.toLowerCase())
  );
  return member ? member.id : null;
}

function convertTimeFormat(timeStr) {
  // Convert "9:30 AM" to "09:30" format
  const [time, period] = timeStr.split(' ');
  let [hours, minutes] = time.split(':');

  hours = parseInt(hours);
  if (period === 'PM' && hours !== 12) {
    hours += 12;
  } else if (period === 'AM' && hours === 12) {
    hours = 0;
  }

  return `${hours.toString().padStart(2, '0')}:${minutes}`;
}

function getWeekDates(date) {
  const serviceDate = new Date(date);
  const dayOfWeek = serviceDate.getDay();
  const startDate = new Date(serviceDate);
  startDate.setDate(serviceDate.getDate() - dayOfWeek);

  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);

  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  };
}

async function createWeeklySchedule(token, weekDates) {
  console.log(`📅 Creating weekly schedule for ${weekDates.startDate} to ${weekDates.endDate}...`);

  const response = await fetch(`${BASE_URL}/api/admin/service-schedules`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      action: 'create_schedule',
      weekStartDate: weekDates.startDate,
      weekEndDate: weekDates.endDate
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to create weekly schedule: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  console.log('✅ Weekly schedule created');
  return data.schedule;
}

async function addServiceTime(token, scheduleId, serviceData) {
  console.log(`⏰ Adding service time: ${serviceData.serviceDate} ${serviceData.serviceTime}...`);

  const response = await fetch(`${BASE_URL}/api/admin/service-schedules`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      action: 'add_service_time',
      scheduleId: scheduleId,
      ...serviceData
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to add service time: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  console.log('✅ Service time added');
  return data.serviceTime;
}

async function migrateSchedules() {
  try {
    console.log('🚀 Starting service schedule migration...\n');

    // Initialize fetch
    if (!fetch) {
      const fetchModule = await import('node-fetch');
      fetch = fetchModule.default;
    }

    // Step 1: Authenticate
    const token = await authenticateAsAdmin();

    // Step 2: Get members for conductor mapping
    const members = await getMembers(token);

    // Step 3: Group schedules by week and create weekly schedules
    const weeklySchedules = new Map();

    for (const schedule of HARDCODED_SCHEDULES) {
      const weekDates = getWeekDates(schedule.date);
      const weekKey = `${weekDates.startDate}_${weekDates.endDate}`;

      if (!weeklySchedules.has(weekKey)) {
        const weeklySchedule = await createWeeklySchedule(token, weekDates);
        weeklySchedules.set(weekKey, weeklySchedule);
      }
    }

    // Step 4: Add service times to each weekly schedule
    for (const schedule of HARDCODED_SCHEDULES) {
      const weekDates = getWeekDates(schedule.date);
      const weekKey = `${weekDates.startDate}_${weekDates.endDate}`;
      const weeklySchedule = weeklySchedules.get(weekKey);

      const conductorId = findConductorId(schedule.conductor, members);
      if (!conductorId) {
        console.warn(`⚠️  Could not find conductor: ${schedule.conductor}`);
      }

      const serviceData = {
        serviceDate: schedule.date,
        serviceTime: convertTimeFormat(schedule.time),
        location: schedule.location,
        address: schedule.address || '',
        notes: ''
      };

      // Only add conductorId if it exists
      if (conductorId) {
        serviceData.conductorId = conductorId;
      }

      await addServiceTime(token, weeklySchedule.id, serviceData);
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('📋 Summary:');
    console.log(`   - Created ${weeklySchedules.size} weekly schedule(s)`);
    console.log(`   - Migrated ${HARDCODED_SCHEDULES.length} service time(s)`);
    console.log('\n💡 You can now remove the hardcoded data from the frontend.');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration
if (require.main === module) {
  migrateSchedules();
}

module.exports = { migrateSchedules };
