/**
 * Territory Assignments API Endpoint
 *
 * Handles territory assignment operations including creating assignments,
 * retrieving assignment data, and managing assignment workflows.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { AssignmentService } from '@/services/territories/AssignmentService';

// Validation schema for assignment creation
const AssignmentCreateSchema = z.object({
  territoryId: z.string().min(1, 'Territory ID is required'),
  memberId: z.string().min(1, 'Member ID is required'),
  notes: z.string().optional()
});

// Validation schema for assignment return
const AssignmentReturnSchema = z.object({
  assignmentId: z.string().min(1, 'Assignment ID is required'),
  notes: z.string().optional()
});

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for assignment management' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview';

    switch (type) {
      case 'members':
        // Get members with their assignment information
        const membersWithAssignments = await AssignmentService.getMembersWithAssignments(user.congregationId);

        return NextResponse.json({
          success: true,
          members: membersWithAssignments,
          timestamp: new Date().toISOString()
        });

      case 'territories':
        // Get territories with their assignment information
        const territoriesWithAssignments = await AssignmentService.getTerritoriesWithAssignments(user.congregationId);

        return NextResponse.json({
          success: true,
          territories: territoriesWithAssignments,
          timestamp: new Date().toISOString()
        });

      case 'overview':
      default:
        // Get both members and territories for overview
        const [members, territories] = await Promise.all([
          AssignmentService.getMembersWithAssignments(user.congregationId),
          AssignmentService.getTerritoriesWithAssignments(user.congregationId)
        ]);

        // Calculate summary statistics
        const totalMembers = members.length;
        const membersWithAssignments = members.filter(m => m.activeAssignments > 0).length;
        const totalTerritories = territories.length;
        const assignedTerritories = territories.filter(t => t.status === 'assigned').length;
        const availableTerritories = territories.filter(t => t.status === 'available').length;
        const totalActiveAssignments = members.reduce((sum, m) => sum + m.activeAssignments, 0);

        const summary = {
          totalMembers,
          membersWithAssignments,
          totalTerritories,
          assignedTerritories,
          availableTerritories,
          totalActiveAssignments
        };

        return NextResponse.json({
          success: true,
          members,
          territories,
          summary,
          timestamp: new Date().toISOString()
        });
    }

  } catch (error) {
    console.error('Territory assignments GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get assignment data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!user.hasCongregationPinAccess) {
      return NextResponse.json(
        { error: 'Admin access required for territory assignment' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const action = body.action;

    switch (action) {
      case 'assign':
        const assignmentData = AssignmentCreateSchema.parse(body);

        console.log(`Creating territory assignment: Territory ${assignmentData.territoryId} to Member ${assignmentData.memberId}`);

        const assignmentResult = await AssignmentService.assignTerritory(
          assignmentData,
          user.id,
          user.congregationId
        );

        if (assignmentResult.success) {
          console.log('Territory assignment created successfully:', assignmentResult.assignment?.id);
        }

        return NextResponse.json(assignmentResult, {
          status: assignmentResult.success ? 200 : 400
        });

      case 'return':
        const returnData = AssignmentReturnSchema.parse(body);

        console.log(`Returning territory assignment: ${returnData.assignmentId}`);

        const returnResult = await AssignmentService.returnTerritory(
          returnData.assignmentId,
          user.id,
          user.congregationId,
          returnData.notes
        );

        if (returnResult.success) {
          console.log('Territory assignment returned successfully:', returnData.assignmentId);
        }

        return NextResponse.json(returnResult, {
          status: returnResult.success ? 200 : 400
        });

      case 'validate':
        const validationData = AssignmentCreateSchema.parse(body);

        const validation = await AssignmentService.validateAssignment(
          validationData,
          user.congregationId
        );

        return NextResponse.json({
          success: true,
          validation
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: assign, return, validate' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Territory assignments POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to process assignment request',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST for assignment operations.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST with action=return to return assignments.' },
    { status: 405 }
  );
}
