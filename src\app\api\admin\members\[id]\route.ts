/**
 * Individual Member API Endpoint
 *
 * Handles operations for individual member profiles including
 * detailed information and change history.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { MemberManagementService } from '@/lib/services/memberManagementService';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET - Retrieve individual member details
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'coordinator'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view member details' },
        { status: 403 }
      );
    }

    const memberId = params.id;

    // Get member details
    const member = await MemberManagementService.getMemberById(
      user.congregationId,
      memberId
    );

    if (!member) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      member,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Member details GET error:', error);

    return NextResponse.json(
      {
        error: 'Failed to fetch member details',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use /api/admin/members for updates.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
