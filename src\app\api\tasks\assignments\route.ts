/**
 * Task Assignments API Endpoint
 * 
 * Handles task assignment operations including creation, status updates,
 * and reassignment with proper validation and role-based access control.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { TaskManagementService, TaskAssignmentInput } from '@/lib/services/taskManagementService';

// Validation schema for task assignment creation
const TaskAssignmentSchema = z.object({
  taskId: z.string().min(1, 'Task ID is required'),
  assignedMemberId: z.string().optional(),
  assignedDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Assigned date must be in YYYY-MM-DD format'),
  dueDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Due date must be in YYYY-MM-DD format').optional(),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional(),
});

// Validation schema for assignment status update
const StatusUpdateSchema = z.object({
  assignmentId: z.string().min(1, 'Assignment ID is required'),
  status: z.enum(['pending', 'in_progress', 'completed', 'cancelled'], {
    errorMap: () => ({ message: 'Invalid status. Must be: pending, in_progress, completed, or cancelled' })
  }),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional(),
});

// Validation schema for task reassignment
const ReassignmentSchema = z.object({
  assignmentId: z.string().min(1, 'Assignment ID is required'),
  newMemberId: z.string().optional(),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional(),
});

// Validation schema for GET requests
const GetAssignmentsSchema = z.object({
  memberId: z.string().optional(),
  taskId: z.string().optional(),
  status: z.string().optional(),
  dateFrom: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  dateTo: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  page: z.string().transform(val => parseInt(val, 10)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).optional(),
});

/**
 * GET /api/tasks/assignments
 * Retrieve task assignments with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = GetAssignmentsSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { memberId, taskId, status, dateFrom, dateTo, page, limit } = validationResult.data;

    // If memberId is specified and it's not the current user, check permissions
    if (memberId && memberId !== member.id) {
      if (!['elder', 'ministerial_servant'].includes(member.role)) {
        return NextResponse.json(
          { error: 'Insufficient permissions to view other members\' assignments' },
          { status: 403 }
        );
      }
    }

    // Get assignments
    const result = await TaskManagementService.getTaskAssignments(
      member.congregationId,
      {
        memberId: memberId || (member.role === 'publisher' ? member.id : undefined),
        taskId,
        status,
        dateFrom,
        dateTo,
        page: page || 1,
        limit: limit || 20,
      }
    );

    return NextResponse.json({
      success: true,
      assignments: result.assignments,
      pagination: {
        page: page || 1,
        limit: limit || 20,
        total: result.total,
        totalPages: Math.ceil(result.total / (limit || 20)),
      },
    });

  } catch (error) {
    console.error('Task assignments GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve task assignments',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tasks/assignments
 * Create a new task assignment
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can create assignments
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create task assignments' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = TaskAssignmentSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const assignmentData: TaskAssignmentInput = validationResult.data;

    // Create the assignment
    const assignment = await TaskManagementService.createTaskAssignment(
      member.congregationId,
      assignmentData
    );

    return NextResponse.json({
      success: true,
      assignment,
      message: 'Task assignment created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Task assignments POST error:', error);
    
    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('Invalid assignment data')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      if (error.message.includes('already assigned')) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
    }
    
    return NextResponse.json(
      {
        error: 'Failed to create task assignment',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/tasks/assignments
 * Update assignment status or reassign task
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action } = body;

    if (action === 'update_status') {
      // Update assignment status
      const validationResult = StatusUpdateSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: 'Invalid request data',
            details: validationResult.error.errors,
          },
          { status: 400 }
        );
      }

      const { assignmentId, status, notes } = validationResult.data;

      // Update the assignment status
      const assignment = await TaskManagementService.updateAssignmentStatus(
        member.congregationId,
        assignmentId,
        status,
        notes,
        member.role === 'publisher' ? member.id : undefined
      );

      return NextResponse.json({
        success: true,
        assignment,
        message: 'Assignment status updated successfully',
      });

    } else if (action === 'reassign') {
      // Reassign task to different member
      
      // Check permissions - only elders and ministerial servants can reassign
      if (!['elder', 'ministerial_servant'].includes(member.role)) {
        return NextResponse.json(
          { error: 'Insufficient permissions to reassign tasks' },
          { status: 403 }
        );
      }

      const validationResult = ReassignmentSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: 'Invalid request data',
            details: validationResult.error.errors,
          },
          { status: 400 }
        );
      }

      const { assignmentId, newMemberId, notes } = validationResult.data;

      // Reassign the task
      const assignment = await TaskManagementService.reassignTask(
        member.congregationId,
        assignmentId,
        newMemberId || null,
        notes
      );

      return NextResponse.json({
        success: true,
        assignment,
        message: 'Task reassigned successfully',
      });

    } else {
      return NextResponse.json(
        { error: 'Invalid action. Must be "update_status" or "reassign"' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Task assignments PUT error:', error);
    
    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      if (error.message.includes('can only update your own')) {
        return NextResponse.json(
          { error: error.message },
          { status: 403 }
        );
      }
      if (error.message.includes('Invalid status')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }
    
    return NextResponse.json(
      {
        error: 'Failed to update task assignment',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
