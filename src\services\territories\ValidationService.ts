// Territory Validation Service
// Main service for territory data validation, cleaning, and conflict resolution

import { prisma } from '@/lib/prisma';
import { DataCleaningService } from './DataCleaningService';
import { DuplicateDetectionService } from './DuplicateDetectionService';
import {
  ValidationRule,
  ValidationResult,
  ValidationError,
  ValidationConflict,
  TerritoryNumberValidationConfig,
  AddressValidationConfig,
  DuplicateDetectionConfig,
  VALIDATION_ERROR_MESSAGES,
  DEFAULT_TERRITORY_NUMBER_CONFIG,
  DEFAULT_ADDRESS_CONFIG,
  DEFAULT_DUPLICATE_CONFIG
} from '@/types/territories/validation';

export class ValidationService {
  private static validationRules: Map<string, ValidationRule[]> = new Map();

  /**
   * Load validation rules for a congregation
   */
  static async loadValidationRules(congregationId: string): Promise<ValidationRule[]> {
    try {
      // Check cache first
      if (this.validationRules.has(congregationId)) {
        return this.validationRules.get(congregationId)!;
      }

      // Load from database (placeholder - would need validation_rules table)
      // For now, return default rules
      const defaultRules: ValidationRule[] = [
        {
          id: 'territory-number-format',
          name: 'Territory Number Format',
          type: 'format',
          enabled: true,
          severity: 'error',
          description: 'Validates territory number format',
          configuration: DEFAULT_TERRITORY_NUMBER_CONFIG,
          congregationId,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'territory-number-uniqueness',
          name: 'Territory Number Uniqueness',
          type: 'uniqueness',
          enabled: true,
          severity: 'error',
          description: 'Ensures territory numbers are unique',
          configuration: {},
          congregationId,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'address-standardization',
          name: 'Address Standardization',
          type: 'address',
          enabled: true,
          severity: 'warning',
          description: 'Standardizes address formatting',
          configuration: DEFAULT_ADDRESS_CONFIG,
          congregationId,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'duplicate-detection',
          name: 'Duplicate Territory Detection',
          type: 'duplicate',
          enabled: true,
          severity: 'warning',
          description: 'Detects potential duplicate territories',
          configuration: DEFAULT_DUPLICATE_CONFIG,
          congregationId,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      this.validationRules.set(congregationId, defaultRules);
      return defaultRules;
    } catch (error) {
      console.error('Error loading validation rules:', error);
      return [];
    }
  }

  /**
   * Validate a single territory
   */
  static async validateTerritory(
    territoryData: {
      territoryNumber: string;
      address: string;
      notes?: string;
    },
    congregationId: string,
    existingTerritoryId?: string
  ): Promise<ValidationResult> {
    const rules = await this.loadValidationRules(congregationId);
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Clean the data first
    const cleaningResults = DataCleaningService.cleanTerritoryData(territoryData);
    const cleaned = {
      territoryNumber: cleaningResults.territoryNumber.cleaned,
      address: cleaningResults.address.cleaned,
      notes: cleaningResults.notes?.cleaned
    };

    // Apply validation rules
    for (const rule of rules.filter(r => r.enabled)) {
      try {
        const ruleErrors = await this.applyValidationRule(
          rule,
          territoryData,
          cleaned,
          congregationId,
          existingTerritoryId
        );
        
        for (const error of ruleErrors) {
          if (error.severity === 'warning') {
            warnings.push(error);
          } else {
            errors.push(error);
          }
        }
      } catch (error) {
        console.error(`Error applying validation rule ${rule.id}:`, error);
      }
    }

    // Check for duplicates
    const duplicates = await this.checkForDuplicates(cleaned, congregationId, existingTerritoryId);

    const isValid = errors.length === 0;

    return {
      isValid,
      errors,
      warnings,
      cleaned,
      duplicates,
      summary: {
        totalChecked: 1,
        errorsFound: errors.length,
        warningsFound: warnings.length,
        duplicatesFound: duplicates.length,
        cleanedFields: this.countCleanedFields(cleaningResults)
      }
    };
  }

  /**
   * Apply a specific validation rule
   */
  private static async applyValidationRule(
    rule: ValidationRule,
    originalData: { territoryNumber: string; address: string; notes?: string },
    cleanedData: { territoryNumber: string; address: string; notes?: string },
    congregationId: string,
    existingTerritoryId?: string
  ): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    switch (rule.type) {
      case 'format':
        errors.push(...this.validateFormat(rule, originalData, cleanedData));
        break;
      
      case 'uniqueness':
        const uniquenessErrors = await this.validateUniqueness(
          rule,
          cleanedData,
          congregationId,
          existingTerritoryId
        );
        errors.push(...uniquenessErrors);
        break;
      
      case 'address':
        errors.push(...this.validateAddress(rule, originalData, cleanedData));
        break;
      
      case 'duplicate':
        // Duplicate validation is handled separately
        break;
      
      case 'custom':
        errors.push(...this.validateCustomRule(rule, originalData, cleanedData));
        break;
    }

    return errors;
  }

  /**
   * Validate format rules
   */
  private static validateFormat(
    rule: ValidationRule,
    originalData: { territoryNumber: string; address: string },
    cleanedData: { territoryNumber: string; address: string }
  ): ValidationError[] {
    const errors: ValidationError[] = [];
    const config = rule.configuration as TerritoryNumberValidationConfig;

    // Territory number validation
    if (!cleanedData.territoryNumber) {
      errors.push(this.createValidationError(
        rule,
        originalData.territoryNumber,
        originalData.address,
        'territoryNumber',
        VALIDATION_ERROR_MESSAGES.TERRITORY_NUMBER_REQUIRED
      ));
    } else {
      // Length validation
      if (cleanedData.territoryNumber.length < config.minLength) {
        errors.push(this.createValidationError(
          rule,
          originalData.territoryNumber,
          originalData.address,
          'territoryNumber',
          VALIDATION_ERROR_MESSAGES.TERRITORY_NUMBER_TOO_SHORT.replace('{min}', config.minLength.toString())
        ));
      }

      if (cleanedData.territoryNumber.length > config.maxLength) {
        errors.push(this.createValidationError(
          rule,
          originalData.territoryNumber,
          originalData.address,
          'territoryNumber',
          VALIDATION_ERROR_MESSAGES.TERRITORY_NUMBER_TOO_LONG.replace('{max}', config.maxLength.toString())
        ));
      }

      // Pattern validation
      if (!config.pattern.test(cleanedData.territoryNumber)) {
        errors.push(this.createValidationError(
          rule,
          originalData.territoryNumber,
          originalData.address,
          'territoryNumber',
          VALIDATION_ERROR_MESSAGES.TERRITORY_NUMBER_INVALID_FORMAT
        ));
      }
    }

    // Address validation
    if (!cleanedData.address) {
      errors.push(this.createValidationError(
        rule,
        originalData.territoryNumber,
        originalData.address,
        'address',
        VALIDATION_ERROR_MESSAGES.ADDRESS_REQUIRED
      ));
    }

    return errors;
  }

  /**
   * Validate uniqueness rules
   */
  private static async validateUniqueness(
    rule: ValidationRule,
    cleanedData: { territoryNumber: string; address: string },
    congregationId: string,
    existingTerritoryId?: string
  ): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    try {
      const existingTerritory = await prisma.territory.findFirst({
        where: {
          congregationId,
          territoryNumber: cleanedData.territoryNumber,
          ...(existingTerritoryId ? { id: { not: existingTerritoryId } } : {})
        }
      });

      if (existingTerritory) {
        errors.push(this.createValidationError(
          rule,
          cleanedData.territoryNumber,
          cleanedData.address,
          'territoryNumber',
          VALIDATION_ERROR_MESSAGES.TERRITORY_NUMBER_DUPLICATE
        ));
      }
    } catch (error) {
      console.error('Error checking territory uniqueness:', error);
    }

    return errors;
  }

  /**
   * Validate address rules
   */
  private static validateAddress(
    rule: ValidationRule,
    originalData: { territoryNumber: string; address: string },
    cleanedData: { territoryNumber: string; address: string }
  ): ValidationError[] {
    const errors: ValidationError[] = [];
    const config = rule.configuration as AddressValidationConfig;

    // Check for forbidden characters
    for (const char of config.forbiddenCharacters) {
      if (originalData.address.includes(char)) {
        errors.push(this.createValidationError(
          rule,
          originalData.territoryNumber,
          originalData.address,
          'address',
          VALIDATION_ERROR_MESSAGES.ADDRESS_INVALID_CHARACTERS
        ));
        break;
      }
    }

    return errors;
  }

  /**
   * Validate custom rules
   */
  private static validateCustomRule(
    rule: ValidationRule,
    originalData: { territoryNumber: string; address: string },
    cleanedData: { territoryNumber: string; address: string }
  ): ValidationError[] {
    // Placeholder for custom rule validation
    // This would be implemented based on specific congregation requirements
    return [];
  }

  /**
   * Check for duplicates
   */
  private static async checkForDuplicates(
    territoryData: { territoryNumber: string; address: string },
    congregationId: string,
    existingTerritoryId?: string
  ) {
    try {
      const existingTerritories = await prisma.territory.findMany({
        where: {
          congregationId,
          ...(existingTerritoryId ? { id: { not: existingTerritoryId } } : {})
        },
        select: {
          id: true,
          territoryNumber: true,
          address: true
        }
      });

      return await DuplicateDetectionService.checkForDuplicates(
        territoryData,
        existingTerritories
      );
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      return [];
    }
  }

  /**
   * Create a validation error
   */
  private static createValidationError(
    rule: ValidationRule,
    territoryNumber: string,
    address: string,
    field: string,
    message: string,
    details?: string
  ): ValidationError {
    return {
      id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      territoryNumber,
      address,
      field,
      errorType: rule.type,
      severity: rule.severity,
      message,
      details: details || '',
      ruleId: rule.id,
      canOverride: rule.severity !== 'critical',
      isResolved: false
    };
  }

  /**
   * Count cleaned fields
   */
  private static countCleanedFields(cleaningResults: {
    territoryNumber: { changes: any[] };
    address: { changes: any[] };
    notes?: { changes: any[] };
  }): number {
    let count = 0;
    if (cleaningResults.territoryNumber.changes.length > 0) count++;
    if (cleaningResults.address.changes.length > 0) count++;
    if (cleaningResults.notes && cleaningResults.notes.changes.length > 0) count++;
    return count;
  }

  /**
   * Validate multiple territories (batch validation)
   */
  static async validateTerritories(
    territories: Array<{
      territoryNumber: string;
      address: string;
      notes?: string;
    }>,
    congregationId: string
  ): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    for (const territory of territories) {
      const result = await this.validateTerritory(territory, congregationId);
      results.push(result);
    }

    return results;
  }

  /**
   * Get validation summary for multiple results
   */
  static getValidationSummary(results: ValidationResult[]) {
    return {
      totalTerritories: results.length,
      validTerritories: results.filter(r => r.isValid).length,
      territoriesWithErrors: results.filter(r => r.errors.length > 0).length,
      territoriesWithWarnings: results.filter(r => r.warnings.length > 0).length,
      totalErrors: results.reduce((sum, r) => sum + r.errors.length, 0),
      totalWarnings: results.reduce((sum, r) => sum + r.warnings.length, 0),
      totalDuplicates: results.reduce((sum, r) => sum + r.duplicates.length, 0),
      totalCleanedFields: results.reduce((sum, r) => sum + r.summary.cleanedFields, 0)
    };
  }
}
