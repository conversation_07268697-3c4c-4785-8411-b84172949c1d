/**
 * Service Schedule API Endpoint (Member Access)
 * 
 * Handles retrieval of service schedules for congregation members.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { ServiceScheduleService } from '@/lib/services/serviceScheduleService';

// Validation schemas
const GetScheduleSchema = z.object({
  weekStartDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)').optional(),
});

/**
 * GET /api/service-schedule
 * Retrieve service schedule for a specific week or current week
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = GetScheduleSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { weekStartDate } = validationResult.data;

    // Use provided date or current week
    const targetDate = weekStartDate || ServiceScheduleService.getCurrentWeekDates().startDate;

    // Get weekly schedule
    const schedule = await ServiceScheduleService.getWeeklySchedule(
      member.congregationId,
      targetDate
    );

    // Calculate week navigation dates
    const currentWeek = ServiceScheduleService.getCurrentWeekDates();
    const requestedDate = new Date(targetDate);
    
    // Previous week
    const previousWeek = new Date(requestedDate);
    previousWeek.setDate(requestedDate.getDate() - 7);
    
    // Next week
    const nextWeek = new Date(requestedDate);
    nextWeek.setDate(requestedDate.getDate() + 7);

    return NextResponse.json({
      success: true,
      schedule,
      weekStartDate: targetDate,
      navigation: {
        currentWeek: currentWeek.startDate,
        previousWeek: previousWeek.toISOString().split('T')[0],
        nextWeek: nextWeek.toISOString().split('T')[0],
        isCurrentWeek: targetDate === currentWeek.startDate,
      },
    });

  } catch (error) {
    console.error('Service schedule GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve service schedule',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
