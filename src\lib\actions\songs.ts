/**
 * Server Actions for Song Management
 * 
 * Provides server-side functions for fetching song data without authentication
 * Used for displaying existing songs in the admin interface
 */

'use server';

import { prisma } from '@/lib/prisma';

export interface Song {
  id: string;
  songNumber: number;
  titleEs: string | null;
  titleEn: string | null;
  category: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SongsPaginationResult {
  songs: Song[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * Fetch songs from database with pagination and filtering
 */
export async function fetchSongsFromDatabase(
  page: number = 1,
  limit: number = 100,
  language?: 'es' | 'en',
  search?: string
): Promise<SongsPaginationResult> {
  try {
    const skip = (page - 1) * limit;

    // Build where clause for filtering
    let whereClause: any = {};

    // Language filtering
    if (language === 'es') {
      whereClause.titleEs = { not: null, not: '' };
    } else if (language === 'en') {
      whereClause.titleEn = { not: null, not: '' };
    }

    // Search filtering
    if (search) {
      const searchNumber = parseInt(search);
      if (!isNaN(searchNumber)) {
        // Search by song number
        whereClause.songNumber = searchNumber;
      } else {
        // Search by title in both languages
        whereClause.OR = [
          { titleEs: { contains: search, mode: 'insensitive' } },
          { titleEn: { contains: search, mode: 'insensitive' } }
        ];
      }
    }

    // Fetch songs and total count
    const [songs, total] = await Promise.all([
      prisma.song.findMany({
        where: whereClause,
        orderBy: { songNumber: 'asc' },
        skip,
        take: limit,
        select: {
          id: true,
          songNumber: true,
          titleEs: true,
          titleEn: true,
          category: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      }),
      prisma.song.count({ where: whereClause })
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      songs,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    };

  } catch (error) {
    console.error('Error fetching songs from database:', error);
    throw new Error('Failed to fetch songs from database');
  }
}

/**
 * Get song statistics without authentication
 */
export async function getSongStatistics() {
  try {
    const [
      totalSongs,
      activeSongs,
      inactiveSongs,
      songsWithSpanishTitles,
      songsWithEnglishTitles,
    ] = await Promise.all([
      prisma.song.count(),
      prisma.song.count({ where: { isActive: true } }),
      prisma.song.count({ where: { isActive: false } }),
      prisma.song.count({ 
        where: { 
          titleEs: { not: null, not: '' }
        } 
      }),
      prisma.song.count({ 
        where: { 
          titleEn: { not: null, not: '' }
        } 
      }),
    ]);

    return {
      overview: {
        totalSongs,
        activeSongs,
        inactiveSongs,
      },
      languages: {
        songsWithSpanishTitles,
        songsWithEnglishTitles,
        completionRate: totalSongs > 0 ? Math.round((songsWithSpanishTitles / totalSongs) * 100) : 0,
      },
    };

  } catch (error) {
    console.error('Error fetching song statistics:', error);
    throw new Error('Failed to fetch song statistics');
  }
}
