const { PrismaClient } = require('@prisma/client');

async function checkTerritories007008() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking format of territories 007 and 008...\n');
    
    for (const territoryNum of ['007', '008']) {
      const territory = await prisma.territory.findFirst({
        where: {
          congregationId: '1441',
          territoryNumber: territoryNum
        }
      });
      
      if (!territory) {
        console.log(`❌ Territory ${territoryNum} not found`);
        continue;
      }
      
      console.log(`📋 Territory ${territoryNum}:`);
      console.log(`Total addresses: ${territory.address.split('\n').length}`);
      
      console.log(`\nFirst 15 addresses:`);
      const addresses = territory.address.split('\n');
      addresses.slice(0, 15).forEach((addr, idx) => {
        console.log(`  ${idx + 1}. ${addr}`);
      });
      
      console.log('\n' + '='.repeat(50) + '\n');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkTerritories007008().catch(console.error);
