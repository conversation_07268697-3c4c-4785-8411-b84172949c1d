const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkData() {
  try {
    const congregations = await prisma.congregation.findMany();
    console.log('All congregations:');
    congregations.forEach((cong, index) => {
      console.log(`${index + 1}. ID: '${cong.id}', Name: '${cong.name}', Region: '${cong.region}', Active: ${cong.isActive}`);
    });
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkData();
