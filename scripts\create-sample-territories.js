/**
 * Create Sample Territories Script
 * 
 * Creates sample territory data for testing the territories admin interface.
 * This script adds a few sample territories to the Coral Oeste congregation.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleTerritories() {
  try {
    console.log('🏗️  Creating sample territories for testing...');

    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: '1441' }
    });

    if (!congregation) {
      console.error('❌ Congregation 1441 (Coral Oeste) not found');
      process.exit(1);
    }

    console.log(`✅ Found congregation: ${congregation.name}`);

    // Sample territories data
    const sampleTerritories = [
      {
        territoryNumber: 'T-001',
        address: 'Sector Los Corales, Calle Principal 1-50',
        status: 'available',
        notes: 'Territorio residencial con casas y apartamentos'
      },
      {
        territoryNumber: 'T-002', 
        address: 'Urbanización Coral Gardens, Bloques A-D',
        status: 'assigned',
        notes: 'Edificios de apartamentos, portería en cada bloque'
      },
      {
        territoryNumber: 'T-003',
        address: 'Zona Comercial Centro, Locales 1-25',
        status: 'available',
        notes: 'Área comercial con tiendas y oficinas'
      },
      {
        territoryNumber: 'T-004',
        address: 'Residencial Oeste, Manzanas 1-5',
        status: 'completed',
        notes: 'Casas unifamiliares, muy receptivo'
      },
      {
        territoryNumber: 'T-005',
        address: 'Sector Industrial, Empresas y Fábricas',
        status: 'out_of_service',
        notes: 'Temporalmente fuera de servicio por construcción'
      }
    ];

    // Create territories
    let created = 0;
    for (const territoryData of sampleTerritories) {
      try {
        // Check if territory already exists
        const existing = await prisma.territory.findFirst({
          where: {
            congregationId: congregation.id,
            territoryNumber: territoryData.territoryNumber
          }
        });

        if (existing) {
          console.log(`⚠️  Territory ${territoryData.territoryNumber} already exists, skipping...`);
          continue;
        }

        // Create territory
        const territory = await prisma.territory.create({
          data: {
            congregationId: congregation.id,
            territoryNumber: territoryData.territoryNumber,
            address: territoryData.address,
            status: territoryData.status,
            notes: territoryData.notes
          }
        });

        console.log(`✅ Created territory: ${territory.territoryNumber} - ${territory.address}`);
        created++;

      } catch (error) {
        console.error(`❌ Error creating territory ${territoryData.territoryNumber}:`, error.message);
      }
    }

    console.log(`\n🎉 Successfully created ${created} sample territories!`);
    
    // Display summary
    const totalTerritories = await prisma.territory.count({
      where: { congregationId: congregation.id }
    });

    const statusCounts = await prisma.territory.groupBy({
      by: ['status'],
      where: { congregationId: congregation.id },
      _count: { status: true }
    });

    console.log(`\n📊 Territory Summary for ${congregation.name}:`);
    console.log(`   Total territories: ${totalTerritories}`);
    statusCounts.forEach(({ status, _count }) => {
      console.log(`   ${status}: ${_count.status}`);
    });

  } catch (error) {
    console.error('❌ Error creating sample territories:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  createSampleTerritories();
}

module.exports = { createSampleTerritories };
