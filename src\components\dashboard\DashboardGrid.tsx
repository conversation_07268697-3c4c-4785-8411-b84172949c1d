/**
 * Dashboard Grid Component
 *
 * Pixel-perfect recreation of the dashboard layout matching the screenshot.
 * Exact layout with proper colors, spacing, and section organization.
 */

import React from 'react';

interface DashboardData {
  pendingTasks: number;
  upcomingMeetings: number;
  currentMonthHours: number;
  lastServiceMonth: Date | null;
  totalMembers?: number | null;
  totalTasks?: number | null;
}

interface Permissions {
  canAccessAdmin: boolean;
  canManageSettings: boolean;
}

interface DashboardGridProps {
  dashboardData: DashboardData;
  permissions: Permissions | null;
  onNavigate: (section: string) => void;
}

// Icon components matching the exact reference design
const AdminIcon = () => (
  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 1L3 5v6c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V5l-9-4z"/>
  </svg>
);

const FieldServiceIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4L9 7V9C9 10.1 9.9 11 11 11V13H7V11C5.9 11 5 10.1 5 9V7L11 4L17 7V9C17 10.1 16.1 11 15 11V13H13V11C14.1 11 15 10.1 15 9M13 15H11V17H13V15M15 15V17H17V19H15V21H13V19H11V21H9V19H7V17H9V15H7V13H17V15H15Z"/>
  </svg>
);

const ProgramsIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z"/>
    <path d="M7,7H17V9H7V7M7,11H17V13H7V11M7,15H14V17H7V15Z"/>
  </svg>
);

const MeetingIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2 1l-3 4v2h3v7h6z"/>
  </svg>
);

const CalendarIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"/>
  </svg>
);

const AssignmentIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
  </svg>
);

const TaskIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
  </svg>
);

const LetterIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
  </svg>
);

const EventIcon = () => (
  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M19,19H5V8H19M16,1V3H8V1H6V3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3H18V1M17,12H12V17H17V12Z"/>
  </svg>
);

export default function DashboardGrid({
  dashboardData,
  permissions,
  onNavigate,
}: DashboardGridProps) {
  return (
    <div className="px-4 space-y-4" data-testid="dashboard-grid">
      {/* Administración Button - Conditional - Exact match to reference screenshot */}
      {permissions?.canAccessAdmin && (
        <button
          onClick={() => onNavigate('admin')}
          className="w-full flex items-center justify-between px-4 py-2.5 bg-white hover:bg-gray-50 rounded-xl text-blue-600 transition-colors shadow-sm border border-gray-200"
        >
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 bg-blue-600 rounded-md flex items-center justify-center text-white">
              <AdminIcon />
            </div>
            <span className="font-medium text-gray-900">Administración</span>
          </div>
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      )}

      {/* First Row: Servicio al campo & Programas */}
      <div className="grid grid-cols-2 gap-4">
        <button
          onClick={() => onNavigate('field-service')}
          className="bg-white rounded-xl shadow-sm p-3 hover:shadow-md transition-shadow border border-gray-100"
        >
          <div className="flex flex-col items-center text-center space-y-1.5">
            <div className="w-11 h-11 bg-purple-500 rounded-full flex items-center justify-center text-white">
              <FieldServiceIcon />
            </div>
            <span className="text-sm font-medium text-gray-900">Servicio al campo</span>
          </div>
        </button>

        <button
          onClick={() => onNavigate('meetings')}
          className="bg-white rounded-xl shadow-sm p-3 hover:shadow-md transition-shadow border border-gray-100"
        >
          <div className="flex flex-col items-center text-center space-y-1.5">
            <div className="w-11 h-11 bg-green-500 rounded-full flex items-center justify-center text-white">
              <ProgramsIcon />
            </div>
            <span className="text-sm font-medium text-gray-900">Programas</span>
          </div>
        </button>
      </div>

      {/* Reuniones Section - With Background Container (Evidence-Based Fix) */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
        <h2 className="text-base font-semibold text-gray-900 text-center mb-3">Reuniones</h2>

        {/* Entre semana & Fin de semana */}
        <div className="grid grid-cols-2 gap-4">
        <button
          onClick={() => onNavigate('midweek-meetings')}
          className="bg-white rounded-xl shadow-sm p-3 hover:shadow-md transition-shadow border border-gray-100"
        >
          <div className="flex flex-col items-center text-center space-y-1.5">
            <div className="w-11 h-11 bg-purple-500 rounded-full flex items-center justify-center text-white">
              <MeetingIcon />
            </div>
            <span className="text-sm font-medium text-gray-900">Entre semana</span>
          </div>
        </button>

        <button
          onClick={() => onNavigate('weekend-meetings')}
          className="bg-white rounded-xl shadow-sm p-3 hover:shadow-md transition-shadow border border-gray-100"
        >
          <div className="flex flex-col items-center text-center space-y-1.5">
            <div className="w-11 h-11 bg-blue-500 rounded-full flex items-center justify-center text-white">
              <CalendarIcon />
            </div>
            <span className="text-sm font-medium text-gray-900">Fin de semana</span>
          </div>
        </button>
        </div>
      </div>

      {/* Actividades Section - With Background Container (Evidence-Based Fix) */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
        <h2 className="text-base font-semibold text-gray-900 text-center mb-3">Actividades</h2>

        {/* Asignaciones & Tareas */}
        <div className="grid grid-cols-2 gap-4">
        <button
          onClick={() => onNavigate('assignments')}
          className="bg-white rounded-xl shadow-sm p-3 hover:shadow-md transition-shadow border border-gray-100"
        >
          <div className="flex flex-col items-center text-center space-y-1.5">
            <div className="w-11 h-11 bg-blue-500 rounded-full flex items-center justify-center text-white">
              <AssignmentIcon />
            </div>
            <span className="text-sm font-medium text-gray-900">Asignaciones</span>
          </div>
        </button>

        <button
          onClick={() => onNavigate('tasks')}
          className="bg-white rounded-xl shadow-sm p-3 hover:shadow-md transition-shadow border border-gray-100"
        >
          <div className="flex flex-col items-center text-center space-y-1.5">
            <div className="w-11 h-11 bg-orange-500 rounded-full flex items-center justify-center text-white">
              <TaskIcon />
            </div>
            <span className="text-sm font-medium text-gray-900">Tareas</span>
          </div>
        </button>
        </div>
      </div>

      {/* Comunicación Section - With Background Container (Evidence-Based Fix) */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
        <h2 className="text-base font-semibold text-gray-900 text-center mb-3">Comunicación</h2>

        {/* Cartas & Eventos */}
        <div className="grid grid-cols-2 gap-4">
        <button
          onClick={() => onNavigate('letters')}
          className="bg-white rounded-xl shadow-sm p-3 hover:shadow-md transition-shadow border border-gray-100"
        >
          <div className="flex flex-col items-center text-center space-y-1.5">
            <div className="w-11 h-11 bg-red-500 rounded-full flex items-center justify-center text-white">
              <LetterIcon />
            </div>
            <span className="text-sm font-medium text-gray-900">Cartas</span>
          </div>
        </button>

        <button
          onClick={() => onNavigate('events')}
          className="bg-white rounded-xl shadow-sm p-3 hover:shadow-md transition-shadow border border-gray-100"
        >
          <div className="flex flex-col items-center text-center space-y-1.5">
            <div className="w-11 h-11 bg-orange-500 rounded-full flex items-center justify-center text-white">
              <EventIcon />
            </div>
            <span className="text-sm font-medium text-gray-900">Eventos</span>
          </div>
        </button>
        </div>
      </div>
    </div>
  );
}
