'use client';

/**
 * Bulk Import Wizard Component
 * 
 * Multi-step wizard for bulk territory import with file upload,
 * progress tracking, and results display.
 */

import React, { useState, useCallback, useRef } from 'react';
import { BulkImportProgress, BulkImportResult } from '@/types/territories/import';

interface BulkImportWizardProps {
  onClose: () => void;
  onComplete: (results: BulkImportResult) => void;
  className?: string;
}

interface UploadFile {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'uploaded' | 'error';
  progress: number;
  error?: string;
}

type WizardStep = 'upload' | 'processing' | 'results';

export default function BulkImportWizard({ onClose, onComplete, className = '' }: BulkImportWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>('upload');
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [batchId, setBatchId] = useState<string | null>(null);
  const [progress, setProgress] = useState<BulkImportProgress | null>(null);
  const [results, setResults] = useState<BulkImportResult | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Handle file selection
  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {
    if (!selectedFiles) return;

    const newFiles: UploadFile[] = Array.from(selectedFiles).map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'pending',
      progress: 0
    }));

    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  // Handle drag and drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const droppedFiles = e.dataTransfer.files;
    handleFileSelect(droppedFiles);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  // Remove file from list
  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  }, []);

  // Start bulk import
  const startBulkImport = async () => {
    if (files.length === 0) {
      setError('Please select at least one file to import');
      return;
    }

    try {
      setIsUploading(true);
      setError(null);

      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Prepare form data
      const formData = new FormData();
      files.forEach((uploadFile, index) => {
        formData.append(`file${index}`, uploadFile.file);
      });
      formData.append('overwriteDuplicates', 'false');

      // Start bulk import
      const response = await fetch('/api/territories/import/bulk', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start bulk import');
      }

      const data = await response.json();
      setBatchId(data.batchId);
      setCurrentStep('processing');

      // Start progress polling
      startProgressPolling(data.batchId);

    } catch (err) {
      console.error('Error starting bulk import:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsUploading(false);
    }
  };

  // Start polling for progress updates
  const startProgressPolling = (batchId: string) => {
    const pollProgress = async () => {
      try {
        const token = localStorage.getItem('hermanos_token');
        const response = await fetch(`/api/territories/import/bulk/${batchId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          setProgress(data.progress);

          // Check if processing is complete
          if (data.progress.status === 'completed' || data.progress.status === 'failed' || data.progress.status === 'partial') {
            // Stop polling and get results
            if (progressIntervalRef.current) {
              clearInterval(progressIntervalRef.current);
              progressIntervalRef.current = null;
            }
            await getResults(batchId);
          }
        }
      } catch (error) {
        console.error('Error polling progress:', error);
      }
    };

    // Poll every 2 seconds
    progressIntervalRef.current = setInterval(pollProgress, 2000);
    pollProgress(); // Initial call
  };

  // Get final results
  const getResults = async (batchId: string) => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch(`/api/territories/import/bulk/${batchId}/results`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setResults(data.results);
        setCurrentStep('results');
        onComplete(data.results);
      }
    } catch (error) {
      console.error('Error getting results:', error);
      setError('Failed to get import results');
    }
  };

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-teal-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Importación Masiva de Territorios</h2>
              <p className="text-teal-100 text-sm mt-1">
                {currentStep === 'upload' && 'Selecciona archivos Excel para importar'}
                {currentStep === 'processing' && 'Procesando archivos...'}
                {currentStep === 'results' && 'Resultados de la importación'}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-teal-100 transition-colors"
              aria-label="Cerrar"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center mt-6 space-x-4">
            <div className={`flex items-center ${currentStep === 'upload' ? 'text-white' : 'text-teal-200'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === 'upload' ? 'bg-white text-teal-600' : 'bg-teal-500'
              }`}>
                1
              </div>
              <span className="ml-2 text-sm">Subir Archivos</span>
            </div>
            <div className="flex-1 h-px bg-teal-400"></div>
            <div className={`flex items-center ${currentStep === 'processing' ? 'text-white' : 'text-teal-200'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === 'processing' ? 'bg-white text-teal-600' : 'bg-teal-500'
              }`}>
                2
              </div>
              <span className="ml-2 text-sm">Procesando</span>
            </div>
            <div className="flex-1 h-px bg-teal-400"></div>
            <div className={`flex items-center ${currentStep === 'results' ? 'text-white' : 'text-teal-200'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep === 'results' ? 'bg-white text-teal-600' : 'bg-teal-500'
              }`}>
                3
              </div>
              <span className="ml-2 text-sm">Resultados</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <p className="mt-1 text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Upload Step */}
          {currentStep === 'upload' && (
            <div className="space-y-6">
              {/* File Drop Zone */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p className="mt-2 text-sm text-gray-600">
                  Arrastra archivos Excel aquí o{' '}
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="text-teal-600 hover:text-teal-500 font-medium"
                  >
                    selecciona archivos
                  </button>
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Máximo 20 archivos, 10MB cada uno
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".xlsx,.xls"
                  onChange={(e) => handleFileSelect(e.target.files)}
                  className="hidden"
                />
              </div>

              {/* File List */}
              {files.length > 0 && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-900">Archivos seleccionados ({files.length})</h3>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {files.map((file) => (
                      <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <div>
                            <p className="text-sm font-medium text-gray-900">{file.file.name}</p>
                            <p className="text-xs text-gray-500">{formatFileSize(file.file.size)}</p>
                          </div>
                        </div>
                        <button
                          onClick={() => removeFile(file.id)}
                          className="text-gray-400 hover:text-red-500 transition-colors"
                          aria-label="Remover archivo"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Processing Step */}
          {currentStep === 'processing' && progress && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4">
                  <svg className="animate-spin w-16 h-16 text-teal-600" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Procesando archivos...</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {progress.filesCompleted} de {progress.totalFiles} archivos completados
                </p>
              </div>

              {/* Overall Progress */}
              <div>
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progreso general</span>
                  <span>{progress.overallProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-teal-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress.overallProgress}%` }}
                  ></div>
                </div>
              </div>

              {/* Current File */}
              {progress.currentFile && (
                <div className="bg-blue-50 rounded-lg p-4">
                  <p className="text-sm text-blue-800">
                    <span className="font-medium">Procesando:</span> {progress.currentFile}
                  </p>
                </div>
              )}

              {/* Statistics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-gray-900">{progress.territoriesImported}</div>
                  <div className="text-sm text-gray-600">Territorios Importados</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-gray-900">{progress.errors.length}</div>
                  <div className="text-sm text-gray-600">Errores</div>
                </div>
              </div>

              {/* Estimated Time */}
              {progress.estimatedTimeRemaining && (
                <div className="text-center text-sm text-gray-600">
                  Tiempo estimado restante: {formatTime(progress.estimatedTimeRemaining)}
                </div>
              )}
            </div>
          )}

          {/* Results Step */}
          {currentStep === 'results' && results && (
            <div className="space-y-6">
              {/* Summary */}
              <div className="text-center">
                <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                  results.success ? 'bg-green-100' : 'bg-yellow-100'
                }`}>
                  {results.success ? (
                    <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  )}
                </div>
                <h3 className="text-lg font-medium text-gray-900">
                  {results.success ? 'Importación Completada' : 'Importación Completada con Advertencias'}
                </h3>
                <p className="text-sm text-gray-600 mt-1">{results.summary}</p>
              </div>

              {/* Statistics Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{results.successfulImports}</div>
                  <div className="text-sm text-green-800">Territorios Importados</div>
                </div>
                <div className="bg-blue-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{results.successfulFiles}</div>
                  <div className="text-sm text-blue-800">Archivos Exitosos</div>
                </div>
                <div className="bg-red-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-red-600">{results.failedFiles}</div>
                  <div className="text-sm text-red-800">Archivos Fallidos</div>
                </div>
                <div className="bg-yellow-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-600">{results.duplicatesFound}</div>
                  <div className="text-sm text-yellow-800">Duplicados</div>
                </div>
              </div>

              {/* Processing Time */}
              <div className="text-center text-sm text-gray-600">
                Tiempo de procesamiento: {formatTime(results.processingTime)}
              </div>

              {/* File Results */}
              {results.fileResults.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Resultados por archivo</h4>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {results.fileResults.map((fileResult, index) => (
                      <div key={index} className={`p-3 rounded-lg border ${
                        fileResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                      }`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {fileResult.success ? (
                              <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            ) : (
                              <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            )}
                            <span className="text-sm font-medium text-gray-900">{fileResult.fileName}</span>
                          </div>
                          {fileResult.result && (
                            <span className="text-xs text-gray-600">
                              {fileResult.result.successfulImports} territorios
                            </span>
                          )}
                        </div>
                        {fileResult.error && (
                          <p className="text-xs text-red-600 mt-1">{fileResult.error}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-between">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
          >
            {currentStep === 'results' ? 'Cerrar' : 'Cancelar'}
          </button>
          
          {currentStep === 'upload' && (
            <button
              onClick={startBulkImport}
              disabled={files.length === 0 || isUploading}
              className="px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUploading ? 'Iniciando...' : `Importar ${files.length} archivo${files.length !== 1 ? 's' : ''}`}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
