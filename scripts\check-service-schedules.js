/**
 * Check Service Schedules Data
 * 
 * This script checks what service schedule data exists in the database
 */

const { PrismaClient } = require('@prisma/client');

async function checkServiceSchedules() {
  const prisma = new PrismaClient();

  try {
    await prisma.$connect();
    console.log('✅ Database connected');

    // Check service schedules
    console.log('\n📅 SERVICE SCHEDULES:');
    const schedules = await prisma.serviceSchedule.findMany({
      include: {
        scheduleTimes: {
          include: {
            conductor: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
          },
        },
      },
    });

    console.log(`Found ${schedules.length} service schedules:`);
    schedules.forEach(schedule => {
      console.log(`  - ID: ${schedule.id}`);
      console.log(`    Congregation: ${schedule.congregationId}`);
      console.log(`    Week: ${schedule.weekStartDate} to ${schedule.weekEndDate}`);
      console.log(`    Active: ${schedule.isActive}`);
      console.log(`    Service Times: ${schedule.scheduleTimes.length}`);
      
      schedule.scheduleTimes.forEach(time => {
        console.log(`      • ${time.serviceDate} at ${time.serviceTime} - ${time.location}`);
        if (time.conductor) {
          console.log(`        Conductor: ${time.conductor.name} (${time.conductor.role})`);
        }
      });
      console.log();
    });

    // Check service schedule times directly
    console.log('\n⏰ SERVICE SCHEDULE TIMES (Direct):');
    const serviceTimes = await prisma.serviceScheduleTime.findMany({
      include: {
        conductor: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        schedule: {
          select: {
            congregationId: true,
            weekStartDate: true,
            weekEndDate: true,
          },
        },
      },
      orderBy: [
        { serviceDate: 'asc' },
        { serviceTime: 'asc' },
      ],
    });

    console.log(`Found ${serviceTimes.length} service times:`);
    serviceTimes.forEach(time => {
      console.log(`  - Date: ${time.serviceDate}`);
      console.log(`    Time: ${time.serviceTime}`);
      console.log(`    Location: ${time.location}`);
      console.log(`    Address: ${time.address || 'N/A'}`);
      console.log(`    Conductor: ${time.conductor?.name || 'N/A'}`);
      console.log(`    Congregation: ${time.schedule.congregationId}`);
      console.log(`    Active: ${time.isActive}`);
      console.log();
    });

    // Check for congregation 1441 specifically
    console.log('\n🏛️ CORAL OESTE (1441) SERVICE SCHEDULES:');
    const coralOesteSchedules = await prisma.serviceScheduleTime.findMany({
      where: {
        schedule: {
          congregationId: '1441',
          isActive: true,
        },
        isActive: true,
      },
      include: {
        conductor: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
      },
      orderBy: [
        { serviceDate: 'asc' },
        { serviceTime: 'asc' },
      ],
    });

    console.log(`Found ${coralOesteSchedules.length} service times for Coral Oeste:`);
    coralOesteSchedules.forEach(time => {
      const today = new Date().toISOString().split('T')[0];
      const serviceDate = time.serviceDate.toISOString().split('T')[0];
      const isUpcoming = serviceDate >= today;
      const isHistorical = serviceDate < today;
      
      console.log(`  - ${serviceDate} at ${time.serviceTime} - ${time.location}`);
      console.log(`    ${isUpcoming ? '📅 UPCOMING' : '📜 HISTORICAL'}`);
      if (time.conductor) {
        console.log(`    Conductor: ${time.conductor.name}`);
      }
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkServiceSchedules();
