'use client';

/**
 * Territory Dashboard Component
 *
 * Main dashboard for territory management with search, filtering, and territory display.
 * Provides comprehensive territory management interface for administrators.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Territory, TerritoryStatus } from '@/types/territories/territory';
import TerritoryCard from '../shared/TerritoryCard';
import TerritorySearch from '../shared/TerritorySearch';
import TerritoryAddressTable from './TerritoryAddressTable';
import TerritoryAssignment from './TerritoryAssignment';
import AssignedTerritories from './AssignedTerritories';
import TerritoryReports from './TerritoryReports';
import SimpleTerritoryMap from '@/components/territories/shared/SimpleTerritoryMap';
import type { Territory as MapTerritory } from '@/types/territories/map';


interface TerritoryDashboardProps {
  className?: string;
}

interface TerritoryWithAssignment extends Territory {
  assignedMember?: {
    id: string;
    name: string;
    assignedAt: Date;
    assignedBy: string;
  } | null;
}

interface TerritoryResponse {
  success: boolean;
  territories: TerritoryWithAssignment[];
  summary: {
    total: number;
    available: number;
    assigned: number;
    completed: number;
    out_of_service: number;
  };
  filters: {
    status?: TerritoryStatus;
    search?: string;
  };
}

export default function TerritoryDashboard({ className = '' }: TerritoryDashboardProps) {
  const [territories, setTerritories] = useState<TerritoryWithAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<TerritoryStatus | 'all'>('all');
  const [viewMode, setViewMode] = useState<'cards' | 'addresses' | 'hidden'>('hidden');
  const [userRole, setUserRole] = useState<string>('');
  const [showStatistics, setShowStatistics] = useState(false);
  const [selectedTerritory, setSelectedTerritory] = useState<TerritoryWithAssignment | null>(null);
  const [mapTerritory, setMapTerritory] = useState<MapTerritory | null>(null);
  const [showMap, setShowMap] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<'territorios' | 'assign' | 'assigned' | 'reports' | 'mapa'>('territorios');
  const [showSubmenuModal, setShowSubmenuModal] = useState(false);

  // Get user role from token
  useEffect(() => {
    const token = localStorage.getItem('hermanos_token');
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setUserRole(payload.role || '');
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    }
  }, []);

  // Fetch territories from API
  const fetchTerritories = async () => {
    try {
      setLoading(true);
      setError(null);

      const rawToken = localStorage.getItem('hermanos_token');
      if (!rawToken) {
        throw new Error('No authentication token found');
      }

      // Clean token - remove any existing Bearer prefix if present
      let token = rawToken.trim();
      if (token.toLowerCase().startsWith('bearer ')) {
        token = token.substring(7); // Remove "Bearer " prefix
      }

      // Build query parameters
      const params = new URLSearchParams();
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }
      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }
      // Add cache-busting parameter to ensure fresh data
      params.append('_t', Date.now().toString());
      const response = await fetch(`/api/territories?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch territories');
      }

      const data: TerritoryResponse = await response.json();
      setTerritories(data.territories);

    } catch (err) {
      console.error('Error fetching territories:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Refresh only the selected territory without changing view state
  const refreshSelectedTerritory = async () => {
    if (!selectedTerritory) return;

    try {


      const rawToken = localStorage.getItem('hermanos_token');
      if (!rawToken) {
        throw new Error('No authentication token found');
      }

      let token = rawToken.trim();
      if (token.toLowerCase().startsWith('bearer ')) {
        token = token.substring(7);
      }

      // Fetch the specific territory with cache-busting
      const response = await fetch(`/api/territories/${selectedTerritory.id}?_t=${Date.now()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch territory');
      }

      const updatedTerritory = await response.json();

      // Update the selected territory with fresh data
      setSelectedTerritory(updatedTerritory);

      // Also update the territory in the main list
      setTerritories(prev => prev.map(t =>
        t.id === updatedTerritory.id ? updatedTerritory : t
      ));

    } catch (error) {
      console.error('Error refreshing selected territory:', error);
    }
  };

  // Fetch territories on component mount and when filters change
  useEffect(() => {
    fetchTerritories();
  }, [statusFilter, searchTerm]);

  // Calculate summary statistics from current territories
  const summary = useMemo(() => {
    return {
      total: territories.length,
      available: territories.filter(t => t.status === 'available').length,
      assigned: territories.filter(t => t.status === 'assigned').length,
      completed: territories.filter(t => t.status === 'completed').length,
      out_of_service: territories.filter(t => t.status === 'out_of_service').length
    };
  }, [territories]);

  // Handle territory card click
  const handleTerritoryClick = async (territory: Territory) => {
    console.log('🎯 Admin - Territory clicked:', territory.territoryNumber, territory.id);
    setSelectedTerritory(territory as TerritoryWithAssignment);
    setViewMode('addresses');

    // Prepare map data for the selected territory
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        console.error('❌ Admin - No token found');
        return;
      }

      console.log('🔍 Admin - Fetching detailed territory data...');
      // Fetch detailed territory data including boundary information
      const response = await fetch(`/api/territories/${territory.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const territoryData = await response.json();
        console.log('🗺️ Admin - Territory data for map:', territoryData);
        console.log('🔍 Admin - Has boundary:', !!territoryData.boundary);
        console.log('🔍 Admin - Has coordinates:', !!territoryData.coordinates);

        // Convert to map territory format
        const mapTerritoryData: MapTerritory = {
          id: territoryData.id,
          territoryNumber: territoryData.territoryNumber,
          address: territoryData.address,
          status: territoryData.status,
          coordinates: territoryData.coordinates,
          boundary: territoryData.boundary
        };

        console.log('🗺️ Admin - Map territory data:', mapTerritoryData);
        setMapTerritory(mapTerritoryData);
        setShowMap(true);
        console.log('✅ Admin - Map data set, showMap enabled');
      } else {
        console.error('❌ Admin - Failed to fetch territory data:', response.statusText);
      }
    } catch (error) {
      console.error('❌ Admin - Error fetching territory data for map:', error);
    }
  };

  // Handle search change
  const handleSearchChange = (term: string) => {
    setSearchTerm(term);
  };

  // Handle status filter change
  const handleStatusFilterChange = (status: TerritoryStatus | 'all') => {
    setStatusFilter(status);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Cargando territorios...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error al cargar territorios</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
              <button
                onClick={fetchTerritories}
                className="mt-2 text-sm text-red-800 underline hover:text-red-900"
              >
                Intentar de nuevo
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* Statistics Toggle - Only show when not viewing a specific territory - Positioned very close to header */}
      {!selectedTerritory && (
        <div className="flex items-center justify-end -mt-1 mb-1">
          <button
            onClick={() => setShowStatistics(!showStatistics)}
            className={`p-1 rounded-md transition-colors ${
              showStatistics ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
            } hover:bg-blue-200`}
            title={showStatistics ? 'Ocultar estadísticas' : 'Mostrar estadísticas'}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </button>
        </div>
      )}

      <div className={!selectedTerritory ? 'space-y-2' : 'space-y-4'}>

      {/* Summary Statistics - Conditional */}
      {showStatistics && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{summary.total}</div>
            <div className="text-sm text-gray-600">Total</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{summary.available}</div>
            <div className="text-sm text-gray-600">Disponibles</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{summary.assigned}</div>
            <div className="text-sm text-gray-600">Asignados</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{summary.completed}</div>
            <div className="text-sm text-gray-600">Completados</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{summary.out_of_service}</div>
            <div className="text-sm text-gray-600">Fuera de Servicio</div>
          </div>
        </div>
      )}



      {/* Territory Content */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Cargando territorios...</span>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchTerritories}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Intentar de nuevo
          </button>
        </div>
      ) : territories.length === 0 ? (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No se encontraron territorios</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || statusFilter !== 'all'
              ? 'Intenta ajustar los filtros de búsqueda.'
              : 'No hay territorios registrados en el sistema.'
            }
          </p>
        </div>
      ) : selectedTerritory ? (
        <div className="space-y-4">
          {/* Back Button */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => {
                  setSelectedTerritory(null);
                  setMapTerritory(null);
                  setShowMap(false);
                }}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>Volver a territorios</span>
              </button>
              <h2 className="text-xl font-semibold text-gray-900">
                Territorio {selectedTerritory.territoryNumber}
              </h2>
            </div>

            {/* Map Toggle Button */}
            {mapTerritory && (
              <button
                onClick={() => setShowMap(!showMap)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                  showMap
                    ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                title={showMap ? 'Ocultar mapa' : 'Mostrar mapa'}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
                <span className="text-sm">{showMap ? 'Ocultar' : 'Mapa'}</span>
              </button>
            )}
          </div>

          {/* Map Section */}
          {showMap && mapTerritory && (
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <SimpleTerritoryMap
                territories={[mapTerritory]}
                height="400px"
                className="w-full"
              />
            </div>
          )}

          {/* Address Table for Selected Territory */}
          <TerritoryAddressTable
            territories={[selectedTerritory]}
            userRole={userRole}
            onRefresh={refreshSelectedTerritory}
          />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Submenu Navigation */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {/* Mobile Submenu Modal */}
            {showSubmenuModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 md:hidden">
                <div className="bg-white rounded-lg p-6 w-80 max-w-full mx-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Territorios</h3>
                    <button
                      onClick={() => setShowSubmenuModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div className="space-y-3">
                    <button
                      onClick={() => {
                        setActiveSubmenu('territorios');
                        setShowSubmenuModal(false);
                      }}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                        activeSubmenu === 'territorios' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                      }`}
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3" />
                      </svg>
                      <span>Territorios</span>
                    </button>
                    <button
                      onClick={() => {
                        setActiveSubmenu('assign');
                        setShowSubmenuModal(false);
                      }}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                        activeSubmenu === 'assign' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                      }`}
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <span>Asignar</span>
                    </button>
                    <button
                      onClick={() => {
                        setActiveSubmenu('assigned');
                        setShowSubmenuModal(false);
                      }}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                        activeSubmenu === 'assigned' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                      }`}
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>Asignados</span>
                    </button>

                    <button
                      onClick={() => {
                        setActiveSubmenu('reports');
                        setShowSubmenuModal(false);
                      }}
                      className={`w-full flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                        activeSubmenu === 'reports' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                      }`}
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span>Reportes</span>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Desktop Submenu Buttons */}
            <div className="hidden md:flex px-4 py-2 bg-gray-50 border-b border-gray-200 space-x-4">
              <button
                onClick={() => setActiveSubmenu('territorios')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeSubmenu === 'territorios'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3" />
                </svg>
                <span>Territorios</span>
              </button>
              <button
                onClick={() => setActiveSubmenu('assign')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeSubmenu === 'assign'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>Asignar</span>
              </button>
              <button
                onClick={() => setActiveSubmenu('assigned')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeSubmenu === 'assigned'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Asignados</span>
              </button>

              <button
                onClick={() => setActiveSubmenu('reports')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeSubmenu === 'reports'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>Reportes</span>
              </button>
            </div>

            {/* Mobile Submenu Icons */}
            <div className="md:hidden flex px-2 py-1.5 bg-gray-50 border-b border-gray-200 justify-start space-x-1">
              <button
                onClick={() => setActiveSubmenu('territorios')}
                className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                  activeSubmenu === 'territorios' ? 'bg-blue-100 text-blue-700' : 'text-gray-600'
                }`}
                title="Territorios"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3" />
                </svg>
              </button>
              <button
                onClick={() => setActiveSubmenu('assign')}
                className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                  activeSubmenu === 'assign' ? 'bg-blue-100 text-blue-700' : 'text-gray-600'
                }`}
                title="Asignar"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </button>
              <button
                onClick={() => setActiveSubmenu('assigned')}
                className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                  activeSubmenu === 'assigned' ? 'bg-blue-100 text-blue-700' : 'text-gray-600'
                }`}
                title="Asignados"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>

              <button
                onClick={() => setActiveSubmenu('reports')}
                className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                  activeSubmenu === 'reports' ? 'bg-blue-100 text-blue-700' : 'text-gray-600'
                }`}
                title="Reportes"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>
              <button
                onClick={() => setShowSubmenuModal(true)}
                className="flex flex-col items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100"
                title="Más opciones"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </button>
            </div>
            {/* Content based on active submenu */}
            {activeSubmenu === 'territorios' && (
              <div className="divide-y divide-gray-200">
                {territories.map((territory) => (
                  <div
                    key={territory.id}
                    onClick={() => handleTerritoryClick(territory)}
                    className="px-4 py-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-semibold text-gray-700">
                              {territory.territoryNumber}
                            </span>
                          </div>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">
                            Territorio {territory.territoryNumber}
                          </h4>
                          <p className="text-sm text-gray-500">
                            {territory.address.split('\n').length} direcciones
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          territory.status === 'available' ? 'bg-green-100 text-green-800' :
                          territory.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                          territory.status === 'completed' ? 'bg-purple-100 text-purple-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {territory.status === 'available' ? 'Disponible' :
                           territory.status === 'assigned' ? 'Asignado' :
                           territory.status === 'completed' ? 'Completado' :
                           'Fuera de Servicio'}
                        </span>
                        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}



            {/* Assignment Content */}
            {activeSubmenu === 'assign' && (
              <TerritoryAssignment
                onAssignmentComplete={() => {
                  // Refresh territories after assignment
                  fetchTerritories();
                }}
              />
            )}

            {/* Assigned Territories Content */}
            {activeSubmenu === 'assigned' && (
              <AssignedTerritories />
            )}

            {/* Reports Content */}
            {activeSubmenu === 'reports' && (
              <TerritoryReports />
            )}
          </div>
        </div>
      )}
      </div>
    </div>
  );
}
