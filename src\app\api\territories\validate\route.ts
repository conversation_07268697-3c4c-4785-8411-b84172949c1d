/**
 * Territory Validation API Endpoint
 * 
 * Handles territory data validation before import or creation.
 * Provides comprehensive validation including format, uniqueness, and duplicate detection.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { ValidationService } from '@/services/territories/ValidationService';

// Validation schema for territory validation request
const TerritoryValidationSchema = z.object({
  territories: z.array(z.object({
    territoryNumber: z.string().min(1, 'Territory number is required'),
    address: z.string().min(1, 'Address is required'),
    notes: z.string().optional()
  })).min(1, 'At least one territory is required'),
  congregationId: z.string().min(1, 'Congregation ID is required'),
  existingTerritoryId: z.string().optional()
});

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await extractAndVerifyToken(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = TerritoryValidationSchema.parse(body);

    // Ensure congregation isolation
    if (validatedData.congregationId !== user.congregationId) {
      return NextResponse.json(
        { error: 'Cannot validate territories for different congregation' },
        { status: 403 }
      );
    }

    console.log(`Validating ${validatedData.territories.length} territories for congregation ${validatedData.congregationId}`);

    // Validate territories
    const validationResults = await ValidationService.validateTerritories(
      validatedData.territories,
      validatedData.congregationId
    );

    // Get validation summary
    const summary = ValidationService.getValidationSummary(validationResults);

    // Log validation results
    console.log('Validation completed:', {
      totalTerritories: summary.totalTerritories,
      validTerritories: summary.validTerritories,
      totalErrors: summary.totalErrors,
      totalWarnings: summary.totalWarnings,
      totalDuplicates: summary.totalDuplicates
    });

    return NextResponse.json({
      success: true,
      results: validationResults,
      summary,
      timestamp: new Date().toISOString()
    }, { status: 200 });

  } catch (error) {
    console.error('Territory validation POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to validate territories',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to validate territories.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to validate territories.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to validate territories.' },
    { status: 405 }
  );
}
