'use client';

import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  DocumentArrowDownIcon,
  CalendarIcon,
  UserGroupIcon,
  MapIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface AssignmentReport {
  territoryNumber: string;
  address: string;
  assignedTo: string;
  assignedDate: string;
  daysAssigned: number;
  status: string;
}

interface MemberWorkload {
  memberName: string;
  totalTerritories: number;
  activeTerritories: number;
  completedTerritories: number;
  averageDays: number;
}

interface TerritoryStats {
  totalTerritories: number;
  assignedTerritories: number;
  availableTerritories: number;
  completedTerritories: number;
  outOfServiceTerritories: number;
}

export default function TerritoryReports() {
  const [activeReport, setActiveReport] = useState<'overview' | 'assignments' | 'workload' | 'available'>('overview');
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<TerritoryStats | null>(null);
  const [assignments, setAssignments] = useState<AssignmentReport[]>([]);
  const [workload, setWorkload] = useState<MemberWorkload[]>([]);
  const [availableTerritories, setAvailableTerritories] = useState<any[]>([]);

  useEffect(() => {
    loadReportData();
  }, []);

  const loadReportData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('hermanos_token');
      
      // Load territory statistics
      const statsResponse = await fetch('/api/territories/reports/statistics', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Load assignment data
      const assignmentsResponse = await fetch('/api/territories/reports/assignments', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (assignmentsResponse.ok) {
        const assignmentsData = await assignmentsResponse.json();
        setAssignments(assignmentsData);
      }

      // Load member workload
      const workloadResponse = await fetch('/api/territories/reports/member-workload', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (workloadResponse.ok) {
        const workloadData = await workloadResponse.json();
        setWorkload(workloadData);
      }

      // Load available territories
      const availableResponse = await fetch('/api/territories/reports/available', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (availableResponse.ok) {
        const availableData = await availableResponse.json();
        setAvailableTerritories(availableData);
      }

    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportToPDF = async (reportType: string) => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories/reports/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reportType })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `reporte-territorios-${reportType}-${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting PDF:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Cargando reportes...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Report Navigation */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Reportes de Territorios</h2>
        
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setActiveReport('overview')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              activeReport === 'overview'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
            }`}
          >
            <ChartBarIcon className="w-4 h-4" />
            <span>Resumen</span>
          </button>

          <button
            onClick={() => setActiveReport('assignments')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              activeReport === 'assignments'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
            }`}
          >
            <MapIcon className="w-4 h-4" />
            <span>Asignaciones</span>
          </button>

          <button
            onClick={() => setActiveReport('workload')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              activeReport === 'workload'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
            }`}
          >
            <UserGroupIcon className="w-4 h-4" />
            <span>Carga de Trabajo</span>
          </button>

          <button
            onClick={() => setActiveReport('available')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              activeReport === 'available'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
            }`}
          >
            <CalendarIcon className="w-4 h-4" />
            <span>Disponibles</span>
          </button>
        </div>
      </div>

      {/* Overview Report */}
      {activeReport === 'overview' && stats && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Resumen General</h3>
            <button
              onClick={() => exportToPDF('overview')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalTerritories}</div>
              <div className="text-sm text-blue-800">Total Territorios</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.assignedTerritories}</div>
              <div className="text-sm text-green-800">Asignados</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{stats.availableTerritories}</div>
              <div className="text-sm text-yellow-800">Disponibles</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{stats.completedTerritories}</div>
              <div className="text-sm text-purple-800">Completados</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{stats.outOfServiceTerritories}</div>
              <div className="text-sm text-red-800">Fuera de Servicio</div>
            </div>
          </div>

          {/* Assignment Rate Chart */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h4 className="text-md font-medium text-gray-900 mb-4">Tasa de Asignación</h4>
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div 
                className="bg-blue-600 h-4 rounded-full transition-all duration-300"
                style={{ width: `${(stats.assignedTerritories / stats.totalTerritories) * 100}%` }}
              ></div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              {Math.round((stats.assignedTerritories / stats.totalTerritories) * 100)}% de territorios asignados
            </div>
          </div>
        </div>
      )}

      {/* Assignments Report */}
      {activeReport === 'assignments' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Reporte de Asignaciones</h3>
            <button
              onClick={() => exportToPDF('assignments')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Territorio
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Asignado a
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha Asignación
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Días Asignado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {assignments.map((assignment, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900">{assignment.territoryNumber}</div>
                        <div className="text-sm text-gray-500">{assignment.address}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {assignment.assignedTo}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(assignment.assignedDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {assignment.daysAssigned} días
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          assignment.status === 'active' 
                            ? 'bg-green-100 text-green-800'
                            : assignment.status === 'completed'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {assignment.status === 'active' ? 'Activo' : 
                           assignment.status === 'completed' ? 'Completado' : assignment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Member Workload Report */}
      {activeReport === 'workload' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Carga de Trabajo por Miembro</h3>
            <button
              onClick={() => exportToPDF('workload')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {workload.map((member, index) => (
              <div key={index} className="bg-white p-6 rounded-lg border border-gray-200">
                <h4 className="font-medium text-gray-900 mb-4">{member.memberName}</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total:</span>
                    <span className="font-medium">{member.totalTerritories}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Activos:</span>
                    <span className="font-medium text-green-600">{member.activeTerritories}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Completados:</span>
                    <span className="font-medium text-blue-600">{member.completedTerritories}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Promedio días:</span>
                    <span className="font-medium">{member.averageDays}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Available Territories Report */}
      {activeReport === 'available' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Territorios Disponibles</h3>
            <button
              onClick={() => exportToPDF('available')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Territorio
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dirección
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Última Asignación
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Días Disponible
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {availableTerritories.map((territory, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">
                        {territory.territoryNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {territory.address}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {territory.lastAssigned ? formatDate(territory.lastAssigned) : 'Nunca'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {territory.daysAvailable || 0} días
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
