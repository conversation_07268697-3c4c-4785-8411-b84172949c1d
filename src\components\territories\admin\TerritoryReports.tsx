'use client';

import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  DocumentArrowDownIcon,
  CalendarIcon,
  UserGroupIcon,
  MapIcon,
  ClockIcon,
  HomeIcon,
  BuildingOfficeIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface AssignmentReport {
  territoryNumber: string;
  address: string;
  assignedTo: string;
  assignedDate: string;
  daysAssigned: number;
  status: string;
}

interface MemberWorkload {
  memberName: string;
  totalTerritories: number;
  activeTerritories: number;
  completedTerritories: number;
  averageDays: number;
}

interface TerritoryStats {
  totalTerritories: number;
  assignedTerritories: number;
  availableTerritories: number;
  completedTerritories: number;
  outOfServiceTerritories: number;
}

interface PropertyAnalytics {
  totalProperties: number;
  totalHouses: number;
  totalApartments: number;
  totalBuildings: number;
  averagePropertiesPerTerritory: number;
  territoryCount: number;
}

interface ActivitySummary {
  totalActivities: number;
  activityBreakdown: {
    en_casa: number;
    no_en_casa: number;
    perros_rejas: number;
    no_llamar: number;
    no_trespassing: number;
  };
  mostActiveTerritory: string;
  mostActiveMember: string;
  averageActivitiesPerTerritory: number;
}

interface CommentsAnalytics {
  totalComments: number;
  territoriesWithComments: number;
  averageCommentsPerTerritory: number;
  comments: any[];
  territoryBreakdown: any[];
}

interface CompletionAnalytics {
  totalCompletions: number;
  uniqueTerritories: number;
  uniqueMembers: number;
  averageCompletionTime: number;
  completions: any[];
  memberCompletions: any[];
}

export default function TerritoryReports() {
  const [activeReport, setActiveReport] = useState<'overview' | 'properties' | 'activities' | 'comments' | 'completions' | 'assignments' | 'workload' | 'available'>('overview');
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<TerritoryStats | null>(null);
  const [assignments, setAssignments] = useState<AssignmentReport[]>([]);
  const [workload, setWorkload] = useState<MemberWorkload[]>([]);
  const [availableTerritories, setAvailableTerritories] = useState<any[]>([]);

  // New analytics state
  const [propertyAnalytics, setPropertyAnalytics] = useState<PropertyAnalytics | null>(null);
  const [activitySummary, setActivitySummary] = useState<ActivitySummary | null>(null);
  const [commentsAnalytics, setCommentsAnalytics] = useState<CommentsAnalytics | null>(null);
  const [completionAnalytics, setCompletionAnalytics] = useState<CompletionAnalytics | null>(null);

  useEffect(() => {
    loadReportData();
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) return;

      // Load property analytics
      const propertiesResponse = await fetch('/api/territories/analytics?type=properties', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (propertiesResponse.ok) {
        const propertiesData = await propertiesResponse.json();
        setPropertyAnalytics(propertiesData.data?.summary || null);
      }

      // Load activity analytics
      const activitiesResponse = await fetch('/api/territories/analytics?type=activities', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json();
        setActivitySummary(activitiesData.data?.summary || null);
      }

      // Load comments analytics
      const commentsResponse = await fetch('/api/territories/analytics?type=comments', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (commentsResponse.ok) {
        const commentsData = await commentsResponse.json();
        setCommentsAnalytics(commentsData.data || null);
      }

      // Load completion analytics
      const completionsResponse = await fetch('/api/territories/analytics?type=completions', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (completionsResponse.ok) {
        const completionsData = await completionsResponse.json();
        setCompletionAnalytics(completionsData.data || null);
      }

    } catch (error) {
      console.error('Error loading analytics data:', error);
    }
  };

  const loadReportData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('hermanos_token');

      // Load territory statistics
      const statsResponse = await fetch('/api/territories/assignments?type=overview', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data?.statistics || null);
      }

      // Load assignment data
      const assignmentsResponse = await fetch('/api/territories/assignments?type=assignments', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (assignmentsResponse.ok) {
        const assignmentsData = await assignmentsResponse.json();
        setAssignments(assignmentsData.data?.assignments || []);
      }

      // Load member workload
      const workloadResponse = await fetch('/api/territories/assignments?type=workload', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (workloadResponse.ok) {
        const workloadData = await workloadResponse.json();
        setWorkload(workloadData.data?.memberWorkload || []);
      }

      // Load available territories
      const availableResponse = await fetch('/api/territories/assignments?type=available', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (availableResponse.ok) {
        const availableData = await availableResponse.json();
        setAvailableTerritories(availableData.data?.availableTerritories || []);
      }

    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportToPDF = async (reportType: string) => {
    try {
      const token = localStorage.getItem('hermanos_token');
      const response = await fetch('/api/territories/reports/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reportType })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `reporte-territorios-${reportType}-${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting PDF:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Cargando reportes...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Report Navigation */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Reportes de Territorios</h2>

        {/* Desktop Navigation - Icon-based */}
        <div className="hidden md:block">
          {/* First Row - Primary Reports */}
          <div className="flex flex-wrap gap-3 mb-3">
            <button
              onClick={() => setActiveReport('overview')}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeReport === 'overview'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Estadísticas Generales"
            >
              <ChartBarIcon className="w-6 h-6 mb-1" />
              <span className="text-xs">Estadísticas</span>
            </button>

            <button
              onClick={() => setActiveReport('assignments')}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeReport === 'assignments'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Asignaciones"
            >
              <MapIcon className="w-6 h-6 mb-1" />
              <span className="text-xs">Asignados</span>
            </button>

            <button
              onClick={() => setActiveReport('workload')}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeReport === 'workload'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Carga de Trabajo"
            >
              <UserGroupIcon className="w-6 h-6 mb-1" />
              <span className="text-xs">Carga</span>
            </button>

            <button
              onClick={() => setActiveReport('properties')}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeReport === 'properties'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Propiedades"
            >
              <HomeIcon className="w-6 h-6 mb-1" />
              <span className="text-xs">Propiedades</span>
            </button>
          </div>

          {/* Second Row - Analytics Reports */}
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setActiveReport('activities')}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeReport === 'activities'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Actividades"
            >
              <UserIcon className="w-6 h-6 mb-1" />
              <span className="text-xs">Actividades</span>
            </button>

            <button
              onClick={() => setActiveReport('comments')}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeReport === 'comments'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Comentarios"
            >
              <ChatBubbleLeftRightIcon className="w-6 h-6 mb-1" />
              <span className="text-xs">Comentarios</span>
            </button>

            <button
              onClick={() => setActiveReport('completions')}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeReport === 'completions'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Completados"
            >
              <CheckCircleIcon className="w-6 h-6 mb-1" />
              <span className="text-xs">Completados</span>
            </button>

            <button
              onClick={() => setActiveReport('available')}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeReport === 'available'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Disponibles"
            >
              <CalendarIcon className="w-6 h-6 mb-1" />
              <span className="text-xs">Disponibles</span>
            </button>
          </div>
        </div>

        {/* Mobile Navigation - Icon-based */}
        <div className="md:hidden">
          {/* First Row - Primary Reports */}
          <div className="grid grid-cols-4 gap-2 mb-2">
            <button
              onClick={() => setActiveReport('overview')}
              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                activeReport === 'overview'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Estadísticas Generales"
            >
              <ChartBarIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Estadísticas</span>
            </button>

            <button
              onClick={() => setActiveReport('assignments')}
              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                activeReport === 'assignments'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Asignaciones"
            >
              <MapIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Asignados</span>
            </button>

            <button
              onClick={() => setActiveReport('workload')}
              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                activeReport === 'workload'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Carga de Trabajo"
            >
              <UserGroupIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Carga</span>
            </button>

            <button
              onClick={() => setActiveReport('properties')}
              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                activeReport === 'properties'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Propiedades"
            >
              <HomeIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Propiedades</span>
            </button>
          </div>

          {/* Second Row - Analytics Reports */}
          <div className="grid grid-cols-4 gap-2">
            <button
              onClick={() => setActiveReport('activities')}
              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                activeReport === 'activities'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Actividades"
            >
              <UserIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Actividades</span>
            </button>

            <button
              onClick={() => setActiveReport('comments')}
              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                activeReport === 'comments'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Comentarios"
            >
              <ChatBubbleLeftRightIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Comentarios</span>
            </button>

            <button
              onClick={() => setActiveReport('completions')}
              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                activeReport === 'completions'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Completados"
            >
              <CheckCircleIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Completados</span>
            </button>

            <button
              onClick={() => setActiveReport('available')}
              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                activeReport === 'available'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
              }`}
              title="Disponibles"
            >
              <CalendarIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Disponibles</span>
            </button>
          </div>
        </div>
      </div>

      {/* Overview Report */}
      {activeReport === 'overview' && stats && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Resumen General</h3>
            <button
              onClick={() => exportToPDF('overview')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalTerritories}</div>
              <div className="text-sm text-blue-800">Total Territorios</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.assignedTerritories}</div>
              <div className="text-sm text-green-800">Asignados</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{stats.availableTerritories}</div>
              <div className="text-sm text-yellow-800">Disponibles</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{stats.completedTerritories}</div>
              <div className="text-sm text-purple-800">Completados</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{stats.outOfServiceTerritories}</div>
              <div className="text-sm text-red-800">Fuera de Servicio</div>
            </div>
          </div>

          {/* Assignment Rate Chart */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h4 className="text-md font-medium text-gray-900 mb-4">Tasa de Asignación</h4>
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div
                className="bg-blue-600 h-4 rounded-full transition-all duration-300"
                style={{ width: `${(stats.assignedTerritories / stats.totalTerritories) * 100}%` }}
              ></div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              {Math.round((stats.assignedTerritories / stats.totalTerritories) * 100)}% de territorios asignados
            </div>
          </div>
        </div>
      )}

      {/* Assignments Report */}
      {activeReport === 'assignments' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Reporte de Asignaciones</h3>
            <button
              onClick={() => exportToPDF('assignments')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Territorio
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Asignado a
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha Asignación
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Días Asignado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {assignments.map((assignment, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900">{assignment.territoryNumber}</div>
                        <div className="text-sm text-gray-500">{assignment.address}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {assignment.assignedTo}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(assignment.assignedDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {assignment.daysAssigned} días
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          assignment.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : assignment.status === 'completed'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {assignment.status === 'active' ? 'Activo' :
                           assignment.status === 'completed' ? 'Completado' : assignment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Member Workload Report */}
      {activeReport === 'workload' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Carga de Trabajo por Miembro</h3>
            <button
              onClick={() => exportToPDF('workload')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {workload.map((member, index) => (
              <div key={index} className="bg-white p-6 rounded-lg border border-gray-200">
                <h4 className="font-medium text-gray-900 mb-4">{member.memberName}</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Total:</span>
                    <span className="font-medium">{member.totalTerritories}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Activos:</span>
                    <span className="font-medium text-green-600">{member.activeTerritories}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Completados:</span>
                    <span className="font-medium text-blue-600">{member.completedTerritories}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Promedio días:</span>
                    <span className="font-medium">{member.averageDays}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Available Territories Report */}
      {activeReport === 'available' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Territorios Disponibles</h3>
            <button
              onClick={() => exportToPDF('available')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Territorio
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dirección
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Última Asignación
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Días Disponible
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {availableTerritories.map((territory, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">
                        {territory.territoryNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {territory.address}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {territory.lastAssigned ? formatDate(territory.lastAssigned) : 'Nunca'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {territory.daysAvailable || 0} días
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Property Analytics Report */}
      {activeReport === 'properties' && propertyAnalytics && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Análisis de Propiedades</h3>
            <button
              onClick={() => exportToPDF('properties')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          {/* Property Summary Cards */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <HomeIcon className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Propiedades</p>
                  <p className="text-2xl font-bold text-gray-900">{propertyAnalytics.totalProperties}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <HomeIcon className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Casas</p>
                  <p className="text-2xl font-bold text-gray-900">{propertyAnalytics.totalHouses}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BuildingOfficeIcon className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Apartamentos</p>
                  <p className="text-2xl font-bold text-gray-900">{propertyAnalytics.totalApartments}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <BuildingOfficeIcon className="w-6 h-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Edificios</p>
                  <p className="text-2xl font-bold text-gray-900">{propertyAnalytics.totalBuildings}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Property Stats */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Estadísticas Adicionales</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm font-medium text-gray-600">Promedio de Propiedades por Territorio</p>
                <p className="text-xl font-bold text-gray-900">{propertyAnalytics.averagePropertiesPerTerritory}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Territorios</p>
                <p className="text-xl font-bold text-gray-900">{propertyAnalytics.territoryCount}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Activity Analytics Report */}
      {activeReport === 'activities' && activitySummary && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Análisis de Actividades</h3>
            <button
              onClick={() => exportToPDF('activities')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          {/* Activity Summary - Compact */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-xs text-gray-500">Total</p>
                <p className="text-xl font-bold text-gray-900">{activitySummary.totalActivities}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-500">Territorio Activo</p>
                <p className="text-sm font-semibold text-blue-600">{activitySummary.mostActiveTerritory || 'N/A'}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-500">Hermano Activo</p>
                <p className="text-sm font-semibold text-green-600">{activitySummary.mostActiveMember || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Activity Breakdown - Compact */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Actividades por Tipo</h4>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              <div className="text-center p-2 bg-green-50 rounded-lg">
                <CheckCircleIcon className="w-5 h-5 text-green-600 mx-auto mb-1" />
                <p className="text-xs text-gray-600">En Casa</p>
                <p className="text-lg font-bold text-green-600">{activitySummary.activityBreakdown.en_casa}</p>
              </div>
              <div className="text-center p-2 bg-yellow-50 rounded-lg">
                <XCircleIcon className="w-5 h-5 text-yellow-600 mx-auto mb-1" />
                <p className="text-xs text-gray-600">No En Casa</p>
                <p className="text-lg font-bold text-yellow-600">{activitySummary.activityBreakdown.no_en_casa}</p>
              </div>
              <div className="text-center p-2 bg-orange-50 rounded-lg">
                <ExclamationTriangleIcon className="w-5 h-5 text-orange-600 mx-auto mb-1" />
                <p className="text-xs text-gray-600">Perros/Rejas</p>
                <p className="text-lg font-bold text-orange-600">{activitySummary.activityBreakdown.perros_rejas}</p>
              </div>
              <div className="text-center p-2 bg-red-50 rounded-lg">
                <XCircleIcon className="w-5 h-5 text-red-600 mx-auto mb-1" />
                <p className="text-xs text-gray-600">No Llamar</p>
                <p className="text-lg font-bold text-red-600">{activitySummary.activityBreakdown.no_llamar}</p>
              </div>
              <div className="text-center p-2 bg-gray-50 rounded-lg">
                <XCircleIcon className="w-5 h-5 text-gray-600 mx-auto mb-1" />
                <p className="text-xs text-gray-600">No Trespassing</p>
                <p className="text-lg font-bold text-gray-600">{activitySummary.activityBreakdown.no_trespassing}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Comments Analytics Report */}
      {activeReport === 'comments' && commentsAnalytics && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Análisis de Comentarios</h3>
            <button
              onClick={() => exportToPDF('comments')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          {/* Comments Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <ChatBubbleLeftRightIcon className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Comentarios</p>
                  <p className="text-2xl font-bold text-gray-900">{commentsAnalytics.totalComments}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <MapIcon className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Territorios con Comentarios</p>
                  <p className="text-2xl font-bold text-gray-900">{commentsAnalytics.territoriesWithComments}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <ChartBarIcon className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Promedio por Territorio</p>
                  <p className="text-2xl font-bold text-gray-900">{commentsAnalytics.averageCommentsPerTerritory}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Comments */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-900">Comentarios Recientes</h4>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Territorio
                    </th>
                    <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Comentario
                    </th>
                    <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                      Hermano
                    </th>
                    <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                      Fecha
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {commentsAnalytics.comments.slice(0, 20).map((comment, index) => (
                    <tr key={index}>
                      <td className="px-2 py-2 text-sm font-medium text-gray-900">
                        {comment.territory?.territoryNumber || 'N/A'}
                      </td>
                      <td className="px-2 py-2 text-sm text-gray-900">
                        <div className="max-w-xs truncate">
                          {comment.note || comment.action || 'Sin comentario'}
                        </div>
                        <div className="md:hidden text-xs text-gray-500 mt-1">
                          {comment.member?.name || 'N/A'} • {formatDate(comment.createdAt)}
                        </div>
                      </td>
                      <td className="px-2 py-2 text-sm text-gray-900 hidden md:table-cell">
                        {comment.member?.name || 'N/A'}
                      </td>
                      <td className="px-2 py-2 text-sm text-gray-500 hidden md:table-cell">
                        {formatDate(comment.createdAt)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Completion Analytics Report */}
      {activeReport === 'completions' && completionAnalytics && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Análisis de Completados</h3>
            <button
              onClick={() => exportToPDF('completions')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
              <span>Exportar PDF</span>
            </button>
          </div>

          {/* Completion Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircleIcon className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Completados</p>
                  <p className="text-2xl font-bold text-gray-900">{completionAnalytics.totalCompletions}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MapIcon className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Territorios Únicos</p>
                  <p className="text-2xl font-bold text-gray-900">{completionAnalytics.uniqueTerritories}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <UserGroupIcon className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Hermanos Únicos</p>
                  <p className="text-2xl font-bold text-gray-900">{completionAnalytics.uniqueMembers}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <ClockIcon className="w-6 h-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Tiempo Promedio (días)</p>
                  <p className="text-2xl font-bold text-gray-900">{completionAnalytics.averageCompletionTime}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Member Completion Performance */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-900">Rendimiento por Hermano</h4>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Hermano
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Completados
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {completionAnalytics.memberCompletions.slice(0, 15).map((member, index) => (
                    <tr key={index}>
                      <td className="px-3 py-2 text-sm font-medium text-gray-900">
                        {member.memberName}
                      </td>
                      <td className="px-3 py-2 text-sm text-gray-900">
                        {member.completions}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
