'use client';

import React, { useState, useEffect } from 'react';
import { DocumentData, DocumentFilters, DocumentVisibility, DocumentPriority } from '@/lib/types/document';

// Utility functions
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('es-ES', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });
};

const formatDateMobile = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: '2-digit'
  });
};

const truncateTitle = (title: string, maxLength: number = 36) => {
  if (title.length <= maxLength) return title;
  return title.substring(0, maxLength - 3) + '...';
};

const getVisibilityLabel = (visibility: DocumentVisibility) => {
  switch (visibility) {
    case 'ALL_MEMBERS': return 'Todos los Miembros';
    case 'ELDERS_ONLY': return 'Solo Ancianos';
    case 'MINISTERIAL_SERVANTS_PLUS': return 'Siervos Ministeriales+';
    default: return visibility;
  }
};

const getCategoryLabel = (category?: string) => {
  switch (category) {
    case 'letters': return 'General';
    case 'announcements': return 'Anuncios';
    case 'instructions': return 'Instrucciones';
    case 'forms': return 'Formularios';
    default: return category || 'General';
  }
};
// Simple Modal component for this use case
function SimpleModal({ children, onClose }: { children: React.ReactNode; onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {children}
      </div>
    </div>
  );
}

interface LettersManagerProps {
  congregationId: string;
  userRole: string;
}

export default function LettersManager({ congregationId, userRole }: LettersManagerProps) {
  const [letters, setLetters] = useState<DocumentData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPdfViewer, setShowPdfViewer] = useState(false);
  const [selectedLetter, setSelectedLetter] = useState<DocumentData | null>(null);

  // Load letters
  const loadLetters = async () => {
    try {
      console.log('LettersManager: Starting to load letters...');
      setLoading(true);
      const token = localStorage.getItem('hermanos_token');
      console.log('LettersManager: Token found:', !!token);

      if (!token) {
        throw new Error('Authentication required');
      }

      console.log('LettersManager: Making API call to /api/documents (all letters)');
      const response = await fetch('/api/documents', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      console.log('LettersManager: API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('LettersManager: API error response:', errorText);
        throw new Error('Failed to load letters');
      }

      const data = await response.json();
      console.log('LettersManager: API response data:', data);
      setLetters(data.documents || []);
      console.log('LettersManager: Letters loaded successfully, count:', data.documents?.length || 0);
    } catch (err) {
      console.error('LettersManager: Error loading letters:', err);
      setError(err instanceof Error ? err.message : 'Failed to load letters');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('LettersManager: Component mounted, loading letters...');
    loadLetters();
  }, []);

  // Handle file upload
  const handleUpload = async (formData: FormData) => {
    try {
      // Set category to letters
      formData.set('category', 'letters');

      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/documents/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const data = await response.json();
      setLetters(prev => [data.document, ...prev]);
      setShowUploadModal(false);

      // Show success message
      alert('Carta subida exitosamente!');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Error al subir la carta');
    }
  };

  // Handle view letter
  const handleView = (letter: DocumentData) => {
    setSelectedLetter(letter);
    setShowPdfViewer(true);
  };

  // Handle edit letter
  const handleEdit = (letter: DocumentData) => {
    setSelectedLetter(letter);
    setShowEditModal(true);
  };

  // Handle delete letter
  const handleDelete = async (letterId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta carta?')) {
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/documents/${letterId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete letter');
      }

      setLetters(prev => prev.filter(letter => letter.id !== letterId));
      alert('Carta eliminada exitosamente');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Error al eliminar la carta');
    }
  };



  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-800">{error}</p>
        <button
          onClick={loadLetters}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Intentar de nuevo
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-end items-center">
        {['elder', 'ministerial_servant', 'coordinator'].includes(userRole) && (
          <button
            onClick={() => setShowUploadModal(true)}
            className="bg-red-600 text-white px-6 py-2 md:px-6 md:py-2 px-2 py-2 rounded-md hover:bg-red-700 transition-colors flex items-center"
          >
            <svg className="w-4 h-4 md:w-5 md:h-5 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            <span className="hidden md:inline ml-2">Subir Carta</span>
          </button>
        )}
      </div>

      {/* Letters List */}
      <div className="bg-white rounded-lg shadow border">
        {letters.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            No se encontraron cartas.
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {letters.map((letter) => (
              <div key={letter.id} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {/* PDF Icon */}
                    <div className="flex-shrink-0">
                      <svg className="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                      </svg>
                    </div>

                    {/* Letter Info */}
                    <div className="flex-1 min-w-0">
                      {/* Desktop title - full length */}
                      <h3 className="hidden md:block text-base font-medium text-gray-900 truncate">
                        {letter.title}
                      </h3>
                      {/* Mobile title - truncated to 36 characters */}
                      <h3 className="md:hidden text-base font-medium text-gray-900">
                        {truncateTitle(letter.title, 36)}
                      </h3>
                      {/* Desktop date and badges */}
                      <div className="mt-1 hidden md:flex items-center space-x-3 text-sm text-gray-500">
                        <span>{formatDate(letter.uploadDate)}</span>
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getCategoryLabel(letter.category)}
                        </span>
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {getVisibilityLabel(letter.visibility)}
                        </span>
                      </div>

                      {/* Mobile date only */}
                      <div className="mt-1 md:hidden text-sm text-gray-500">
                        <span>{formatDateMobile(letter.uploadDate)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-1">
                    {/* View Button */}
                    <button
                      onClick={() => handleView(letter)}
                      className="p-1.5 text-gray-400 hover:text-blue-600 transition-colors"
                      title="Ver"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>

                    {/* Edit Button */}
                    {['elder', 'ministerial_servant', 'coordinator'].includes(userRole) && (
                      <button
                        onClick={() => handleEdit(letter)}
                        className="p-1.5 text-gray-400 hover:text-yellow-600 transition-colors"
                        title="Editar"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                    )}

                    {/* Delete Button */}
                    {['elder', 'coordinator'].includes(userRole) && (
                      <button
                        onClick={() => handleDelete(letter.id)}
                        className="p-1.5 text-gray-400 hover:text-red-600 transition-colors"
                        title="Eliminar"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Upload Modal */}
      {showUploadModal && (
        <UploadLetterModal
          onUpload={handleUpload}
          onCancel={() => setShowUploadModal(false)}
        />
      )}

      {/* Edit Modal */}
      {showEditModal && selectedLetter && (
        <EditLetterModal
          letter={selectedLetter}
          onSave={(updatedLetter) => {
            setLetters(prev => prev.map(l => l.id === updatedLetter.id ? updatedLetter : l));
            setShowEditModal(false);
            setSelectedLetter(null);
          }}
          onCancel={() => {
            setShowEditModal(false);
            setSelectedLetter(null);
          }}
        />
      )}

      {/* PDF Viewer Modal */}
      {showPdfViewer && selectedLetter && (
        <PdfViewerModal
          letter={selectedLetter}
          onClose={() => {
            setShowPdfViewer(false);
            setSelectedLetter(null);
          }}
        />
      )}
    </div>
  );
}

// Upload Letter Modal Component
function UploadLetterModal({
  onUpload,
  onCancel
}: {
  onUpload: (formData: FormData) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    title: '',
    category: 'letters',
    visibility: 'ALL_MEMBERS' as DocumentVisibility,
  });
  const [file, setFile] = useState<File | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!file || !formData.title) {
      alert('Por favor proporciona un archivo y título');
      return;
    }

    const uploadFormData = new FormData();
    uploadFormData.append('file', file);
    Object.entries(formData).forEach(([key, value]) => {
      if (value) {
        uploadFormData.append(key, value);
      }
    });

    onUpload(uploadFormData);
  };

  return (
    <SimpleModal onClose={onCancel}>
      <div className="p-6">
        <h3 className="text-lg font-medium mb-4">Subir Nueva Carta</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* File Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Seleccionar archivo PDF
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
              <input
                type="file"
                onChange={(e) => setFile(e.target.files?.[0] || null)}
                accept=".pdf"
                className="hidden"
                id="file-upload"
                required
              />
              <label htmlFor="file-upload" className="cursor-pointer">
                <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <div className="mt-2">
                  <span className="text-blue-600 hover:text-blue-500">Seleccionar archivo PDF</span>
                </div>
              </label>
              {file && (
                <p className="mt-2 text-sm text-gray-600">
                  Archivo seleccionado: {file.name}
                </p>
              )}
            </div>
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Título
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            />
          </div>

          {/* Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Fecha
            </label>
            <input
              type="date"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            />
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Categoría
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              <option value="letters">General</option>
              <option value="announcements">Anuncios</option>
              <option value="instructions">Instrucciones</option>
              <option value="forms">Formularios</option>
            </select>
          </div>

          {/* Visibility */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Visibilidad
            </label>
            <select
              value={formData.visibility}
              onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value as DocumentVisibility }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              <option value="ALL_MEMBERS">Todos los Miembros</option>
              <option value="MINISTERIAL_SERVANTS_PLUS">Siervos Ministeriales+</option>
              <option value="ELDERS_ONLY">Solo Ancianos</option>
            </select>
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Subir Carta
            </button>
          </div>
        </form>
      </div>
    </SimpleModal>
  );
}

// Edit Letter Modal Component
function EditLetterModal({
  letter,
  onSave,
  onCancel
}: {
  letter: DocumentData;
  onSave: (letter: DocumentData) => void;
  onCancel: () => void;
}) {
  const [title, setTitle] = useState(letter.title);
  const [uploadDate, setUploadDate] = useState(
    letter.uploadDate ? new Date(letter.uploadDate).toISOString().split('T')[0] : ''
  );
  const [category, setCategory] = useState(letter.category);
  const [visibility, setVisibility] = useState(letter.visibility);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    try {
      setSaving(true);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      let updatedDocument;

      // If a new file is selected, upload it first
      if (selectedFile) {
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('title', title);
        formData.append('category', category);
        formData.append('visibility', visibility);
        if (uploadDate) {
          formData.append('uploadDate', new Date(uploadDate).toISOString());
        }

        const uploadResponse = await fetch(`/api/documents/${letter.id}/upload`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload new file');
        }

        const uploadData = await uploadResponse.json();
        updatedDocument = uploadData.document;
      } else {
        // Just update metadata
        const response = await fetch(`/api/documents/${letter.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            title,
            uploadDate: uploadDate ? new Date(uploadDate).toISOString() : null,
            category,
            visibility,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update letter');
        }

        const data = await response.json();
        updatedDocument = data.document;
      }

      onSave(updatedDocument);
    } catch (error) {
      console.error('Error updating letter:', error);
      alert('Error al actualizar la carta');
    } finally {
      setSaving(false);
    }
  };

  return (
    <SimpleModal onClose={onCancel}>
      <div className="p-6 max-w-md mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium">Editar Carta</h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Título
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            />
          </div>

          {/* Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Fecha
            </label>
            <input
              type="date"
              value={uploadDate}
              onChange={(e) => setUploadDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            />
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Categoría
            </label>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              <option value="General">General</option>
              <option value="Importante">Importante</option>
              <option value="Anuncios">Anuncios</option>
            </select>
          </div>

          {/* Visibility */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Visibilidad
            </label>
            <select
              value={visibility}
              onChange={(e) => setVisibility(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              <option value="ALL_MEMBERS">Todos los Miembros</option>
              <option value="MINISTERIAL_SERVANTS_PLUS">Siervos Ministeriales+</option>
              <option value="ELDERS_ONLY">Solo Ancianos</option>
              <option value="COORDINATORS_ONLY">Solo Coordinadores</option>
            </select>
          </div>

          {/* Current File */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Archivo PDF
            </label>
            <div className="text-sm text-gray-600 mb-2">
              Archivo actual: {letter.filename}
            </div>

            {/* File Upload */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <div className="flex items-center justify-center">
                <svg className="w-8 h-8 text-red-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
                <div className="text-center">
                  <label className="cursor-pointer">
                    <span className="text-red-600 hover:text-red-700 font-medium">
                      Reemplazar archivo PDF (opcional)
                    </span>
                    <input
                      type="file"
                      accept=".pdf"
                      onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>
              {selectedFile ? (
                <div className="mt-2 text-sm text-green-600">
                  Archivo seleccionado: {selectedFile.name}
                </div>
              ) : (
                <div className="mt-2 text-xs text-gray-400 text-center">
                  Ningún archivo seleccionado
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            disabled={saving}
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {saving ? 'Guardando...' : 'Guardar Cambios'}
          </button>
        </div>
      </div>
    </SimpleModal>
  );
}

// PDF Viewer Modal Component
function PdfViewerModal({
  letter,
  onClose
}: {
  letter: DocumentData;
  onClose: () => void;
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-1 md:p-4">
      <div className="bg-white rounded-lg w-full h-full max-w-6xl max-h-[98vh] md:max-h-[95vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-3 md:p-4 border-b border-gray-200">
          <div className="flex-1 min-w-0">
            <h3 className="text-base md:text-lg font-medium text-gray-900 truncate">
              {letter.title}
            </h3>
            <p className="text-xs md:text-sm text-gray-500 mt-1">
              {formatDate(letter.uploadDate)} • {getCategoryLabel(letter.category)}
            </p>
          </div>

          {/* Close Button */}
          <div className="ml-4">
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Cerrar"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* PDF Viewer */}
        <div className="flex-1 overflow-hidden">
          <iframe
            src={letter.filePath}
            className="w-full h-full border-0"
            title={letter.title}
          />
        </div>


      </div>
    </div>
  );
}
