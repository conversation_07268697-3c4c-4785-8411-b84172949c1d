/**
 * Territory Assignment API Endpoint
 * 
 * Handles territory assignment to members by administrators.
 * Supports multiple territory assignment to a single member.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

// Validation schema
const AssignmentSchema = z.object({
  memberId: z.string().min(1, 'Member ID is required'),
  territoryIds: z.array(z.string()).min(1, 'At least one territory must be selected')
});

/**
 * POST /api/territories/assign
 * Assign territories to a member
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get admin member information
    const adminMember = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { 
        id: true,
        name: true,
        role: true,
        congregationId: true 
      }
    });

    if (!adminMember) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions for territory assignment
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(adminMember.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required for territory assignment' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = AssignmentSchema.parse(body);

    // Verify target member exists and belongs to same congregation
    const targetMember = await prisma.member.findFirst({
      where: {
        id: validatedData.memberId,
        congregationId: adminMember.congregationId
      },
      select: {
        id: true,
        name: true,
        role: true
      }
    });

    if (!targetMember) {
      return NextResponse.json(
        { error: 'Target member not found or not in same congregation' },
        { status: 404 }
      );
    }

    // Verify all territories exist, are available, and belong to same congregation
    const territories = await prisma.territory.findMany({
      where: {
        id: { in: validatedData.territoryIds },
        congregationId: adminMember.congregationId,
        status: 'available'
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        status: true
      }
    });

    if (territories.length !== validatedData.territoryIds.length) {
      const foundIds = territories.map(t => t.id);
      const missingIds = validatedData.territoryIds.filter(id => !foundIds.includes(id));
      
      return NextResponse.json(
        { 
          error: 'Some territories are not available for assignment',
          details: `Missing or unavailable territories: ${missingIds.join(', ')}`
        },
        { status: 400 }
      );
    }

    // Create assignments in a transaction
    const result = await prisma.$transaction(async (tx) => {
      const assignments = [];

      for (const territory of territories) {
        // Create territory assignment
        const assignment = await tx.territoryAssignment.create({
          data: {
            territoryId: territory.id,
            memberId: targetMember.id,
            assignedBy: adminMember.id,
            status: 'active',
            visitCount: 0,
            isPartiallyCompleted: false,
            congregationId: adminMember.congregationId
          }
        });

        // Update territory status to assigned
        await tx.territory.update({
          where: { id: territory.id },
          data: {
            status: 'assigned',
            updatedAt: new Date()
          }
        });

        assignments.push({
          id: assignment.id,
          territory: {
            id: territory.id,
            territoryNumber: territory.territoryNumber,
            address: territory.address
          },
          assignedAt: assignment.assignedAt
        });
      }

      return assignments;
    });

    return NextResponse.json({
      success: true,
      data: {
        assignments: result,
        member: {
          id: targetMember.id,
          name: targetMember.name,
          role: targetMember.role
        },
        assignedBy: {
          id: adminMember.id,
          name: adminMember.name,
          role: adminMember.role
        },
        message: `${result.length} territorio(s) asignado(s) exitosamente a ${targetMember.name}`
      }
    });

  } catch (error) {
    console.error('Territory assignment error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/territories/assign
 * Get assignment data (available territories and members)
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { user } = authResult;

    // Get admin member information
    const adminMember = await prisma.member.findUnique({
      where: { id: user.userId },
      select: { 
        role: true,
        congregationId: true 
      }
    });

    if (!adminMember) {
      return NextResponse.json(
        { error: 'Member not found' },
        { status: 404 }
      );
    }

    // Check admin permissions
    const hasAdminAccess = user.hasCongregationPinAccess ||
      ['elder', 'overseer_coordinator', 'coordinator', 'developer', 'ministerial_servant'].includes(adminMember.role);

    if (!hasAdminAccess) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get available territories
    const availableTerritories = await prisma.territory.findMany({
      where: {
        congregationId: adminMember.congregationId,
        status: 'available'
      },
      select: {
        id: true,
        territoryNumber: true,
        address: true,
        status: true
      },
      orderBy: {
        territoryNumber: 'asc'
      }
    });

    // Get congregation members
    const members = await prisma.member.findMany({
      where: {
        congregationId: adminMember.congregationId
      },
      select: {
        id: true,
        name: true,
        role: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        availableTerritories,
        members,
        summary: {
          availableCount: availableTerritories.length,
          memberCount: members.length
        }
      }
    });

  } catch (error) {
    console.error('Assignment data get error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
