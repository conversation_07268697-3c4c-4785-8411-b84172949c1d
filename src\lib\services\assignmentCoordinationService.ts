/**
 * Assignment Coordination Service
 *
 * Comprehensive service for managing all types of congregation assignments
 * including meeting parts, territories, and special assignments.
 */

import { prisma } from '@/lib/prisma';

export interface MeetingPartAssignment {
  id: string;
  meetingId: string;
  partType: string;
  title: string;
  assignedMember: string | null;
  assistant: string | null;
  timeAllocation: number | null;
  notes: string | null;
  isCompleted: boolean;
  displayOrder?: number;
  partNumber?: number;
  meetingDate: Date;
  meetingType: 'midweek' | 'weekend';
  member?: {
    id: string;
    name: string;
    role: string;
  };
  assistantMember?: {
    id: string;
    name: string;
    role: string;
  };
}

export interface AssignmentInput {
  meetingId: string;
  partId: string;
  assignedMemberId?: string;
  assistantId?: string;
  notes?: string;
}

export interface MemberAssignmentSummary {
  memberId: string;
  memberName: string;
  memberRole: string;
  upcomingAssignments: MeetingPartAssignment[];
  recentAssignments: MeetingPartAssignment[];
  totalAssignments: number;
  lastAssignmentDate: Date | null;
}

export interface AssignmentStatistics {
  totalUpcomingAssignments: number;
  unassignedParts: number;
  membersWithAssignments: number;
  averageAssignmentsPerMember: number;
  assignmentsByType: Record<string, number>;
  assignmentsByMonth: Record<string, number>;
  memberWorkload: Array<{
    memberId: string;
    memberName: string;
    assignmentCount: number;
    lastAssignment: Date | null;
  }>;
}

export class AssignmentCoordinationService {
  /**
   * Get all upcoming meeting part assignments for a congregation
   */
  static async getUpcomingAssignments(
    congregationId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      meetingType?: 'midweek' | 'weekend';
      memberId?: string;
      includeUnassigned?: boolean;
      limit?: number;
    } = {}
  ): Promise<MeetingPartAssignment[]> {
    try {
      const {
        startDate = new Date(),
        endDate = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        meetingType,
        memberId,
        includeUnassigned = true,
        limit = 100
      } = options;

      const assignments: MeetingPartAssignment[] = [];

      // Get midweek meeting assignments
      if (!meetingType || meetingType === 'midweek') {
        const midweekAssignments = await prisma.midweekMeetingPart.findMany({
          where: {
            meeting: {
              congregationId,
              meetingDate: {
                gte: startDate,
                lte: endDate,
              },
            },
            ...(memberId && { assignedMember: memberId }),
            ...(includeUnassigned === false && { assignedMember: { not: null } }),
          },
          include: {
            meeting: {
              select: {
                id: true,
                meetingDate: true,
                congregationId: true,
              },
            },
          },
          orderBy: [
            { meeting: { meetingDate: 'asc' } },
            { partNumber: 'asc' },
          ],
          take: limit,
        });

        // Get member details for assigned members
        const memberIds = [
          ...midweekAssignments.map(a => a.assignedMember).filter(Boolean),
          ...midweekAssignments.map(a => a.assistant).filter(Boolean),
        ].filter((id, index, arr) => arr.indexOf(id) === index) as string[];

        const members = await prisma.member.findMany({
          where: {
            id: { in: memberIds },
            congregationId,
          },
          select: {
            id: true,
            name: true,
            role: true,
          },
        });

        const memberMap = new Map(members.map(m => [m.id, m]));

        assignments.push(...midweekAssignments.map(assignment => ({
          id: assignment.id,
          meetingId: assignment.meetingId,
          partType: assignment.partType,
          title: assignment.title,
          assignedMember: assignment.assignedMember,
          assistant: assignment.assistant,
          timeAllocation: assignment.timeAllocation,
          notes: assignment.notes,
          isCompleted: assignment.isCompleted,
          partNumber: assignment.partNumber,
          meetingDate: assignment.meeting.meetingDate,
          meetingType: 'midweek' as const,
          member: assignment.assignedMember ? memberMap.get(assignment.assignedMember) : undefined,
          assistantMember: assignment.assistant ? memberMap.get(assignment.assistant) : undefined,
        })));
      }

      // Get weekend meeting assignments
      if (!meetingType || meetingType === 'weekend') {
        const weekendAssignments = await prisma.weekendMeetingPart.findMany({
          where: {
            meeting: {
              congregationId,
              meetingDate: {
                gte: startDate,
                lte: endDate,
              },
            },
            ...(memberId && { assignedMember: memberId }),
            ...(includeUnassigned === false && { assignedMember: { not: null } }),
          },
          include: {
            meeting: {
              select: {
                id: true,
                meetingDate: true,
                congregationId: true,
              },
            },
          },
          orderBy: [
            { meeting: { meetingDate: 'asc' } },
            { partType: 'asc' },
          ],
          take: limit,
        });

        // Get member details for weekend assignments
        const weekendMemberIds = weekendAssignments
          .map(a => a.assignedMember)
          .filter(Boolean) as string[];

        const weekendMembers = await prisma.member.findMany({
          where: {
            id: { in: weekendMemberIds },
            congregationId,
          },
          select: {
            id: true,
            name: true,
            role: true,
          },
        });

        const weekendMemberMap = new Map(weekendMembers.map(m => [m.id, m]));

        assignments.push(...weekendAssignments.map(assignment => ({
          id: assignment.id,
          meetingId: assignment.meetingId,
          partType: assignment.partType,
          title: assignment.partType, // Weekend parts use partType as title
          assignedMember: assignment.assignedMember,
          assistant: null,
          timeAllocation: null,
          notes: assignment.notes,
          isCompleted: assignment.isCompleted,
          meetingDate: assignment.meeting.meetingDate,
          meetingType: 'weekend' as const,
          member: assignment.assignedMember ? weekendMemberMap.get(assignment.assignedMember) : undefined,
        })));
      }

      // Sort all assignments by meeting date
      return assignments.sort((a, b) => a.meetingDate.getTime() - b.meetingDate.getTime());

    } catch (error) {
      console.error('Error fetching upcoming assignments:', error);
      throw new Error('Failed to fetch upcoming assignments');
    }
  }

  /**
   * Assign a member to a meeting part
   */
  static async assignMeetingPart(
    congregationId: string,
    assignmentData: AssignmentInput
  ): Promise<MeetingPartAssignment> {
    try {
      // Validate that the member belongs to the congregation
      if (assignmentData.assignedMemberId) {
        const member = await prisma.member.findFirst({
          where: {
            id: assignmentData.assignedMemberId,
            congregationId,
            isActive: true,
          },
        });

        if (!member) {
          throw new Error('Assigned member not found or is not active');
        }
      }

      // Validate assistant if provided
      if (assignmentData.assistantId) {
        const assistant = await prisma.member.findFirst({
          where: {
            id: assignmentData.assistantId,
            congregationId,
            isActive: true,
          },
        });

        if (!assistant) {
          throw new Error('Assistant member not found or is not active');
        }
      }

      // Check if this is a midweek or weekend meeting part
      const midweekPart = await prisma.midweekMeetingPart.findFirst({
        where: {
          id: assignmentData.partId,
          meeting: {
            congregationId,
          },
        },
        include: {
          meeting: true,
        },
      });

      if (midweekPart) {
        // Update midweek meeting part
        const updatedPart = await prisma.midweekMeetingPart.update({
          where: { id: assignmentData.partId },
          data: {
            assignedMember: assignmentData.assignedMemberId || null,
            assistant: assignmentData.assistantId || null,
            notes: assignmentData.notes || null,
            updatedAt: new Date(),
          },
          include: {
            meeting: {
              select: {
                id: true,
                meetingDate: true,
                congregationId: true,
              },
            },
          },
        });

        // Get member details
        const memberIds = [updatedPart.assignedMember, updatedPart.assistant]
          .filter(Boolean) as string[];

        const members = await prisma.member.findMany({
          where: {
            id: { in: memberIds },
            congregationId,
          },
          select: {
            id: true,
            name: true,
            role: true,
          },
        });

        const memberMap = new Map(members.map(m => [m.id, m]));

        return {
          id: updatedPart.id,
          meetingId: updatedPart.meetingId,
          partType: updatedPart.partType,
          title: updatedPart.title,
          assignedMember: updatedPart.assignedMember,
          assistant: updatedPart.assistant,
          timeAllocation: updatedPart.timeAllocation,
          notes: updatedPart.notes,
          isCompleted: updatedPart.isCompleted,
          partNumber: updatedPart.partNumber,
          meetingDate: updatedPart.meeting.meetingDate,
          meetingType: 'midweek',
          member: updatedPart.assignedMember ? memberMap.get(updatedPart.assignedMember) : undefined,
          assistantMember: updatedPart.assistant ? memberMap.get(updatedPart.assistant) : undefined,
        };
      }

      // Check weekend meeting part
      const weekendPart = await prisma.weekendMeetingPart.findFirst({
        where: {
          id: assignmentData.partId,
          meeting: {
            congregationId,
          },
        },
        include: {
          meeting: true,
        },
      });

      if (weekendPart) {
        // Update weekend meeting part
        const updatedPart = await prisma.weekendMeetingPart.update({
          where: { id: assignmentData.partId },
          data: {
            assignedMember: assignmentData.assignedMemberId || null,
            notes: assignmentData.notes || null,
            updatedAt: new Date(),
          },
          include: {
            meeting: {
              select: {
                id: true,
                meetingDate: true,
                congregationId: true,
              },
            },
          },
        });

        // Get member details
        const member = updatedPart.assignedMember ? await prisma.member.findFirst({
          where: {
            id: updatedPart.assignedMember,
            congregationId,
          },
          select: {
            id: true,
            name: true,
            role: true,
          },
        }) : undefined;

        return {
          id: updatedPart.id,
          meetingId: updatedPart.meetingId,
          partType: updatedPart.partType,
          title: updatedPart.partType,
          assignedMember: updatedPart.assignedMember,
          assistant: null,
          timeAllocation: null,
          notes: updatedPart.notes,
          isCompleted: updatedPart.isCompleted,
          meetingDate: updatedPart.meeting.meetingDate,
          meetingType: 'weekend',
          member,
        };
      }

      throw new Error('Meeting part not found');

    } catch (error) {
      console.error('Error assigning meeting part:', error);
      throw error;
    }
  }

  /**
   * Remove assignment from a meeting part
   */
  static async removeAssignment(
    congregationId: string,
    partId: string
  ): Promise<MeetingPartAssignment> {
    try {
      // Check if this is a midweek meeting part
      const midweekPart = await prisma.midweekMeetingPart.findFirst({
        where: {
          id: partId,
          meeting: {
            congregationId,
          },
        },
        include: {
          meeting: true,
        },
      });

      if (midweekPart) {
        const updatedPart = await prisma.midweekMeetingPart.update({
          where: { id: partId },
          data: {
            assignedMember: null,
            assistant: null,
            notes: null,
            updatedAt: new Date(),
          },
          include: {
            meeting: {
              select: {
                id: true,
                meetingDate: true,
                congregationId: true,
              },
            },
          },
        });

        return {
          id: updatedPart.id,
          meetingId: updatedPart.meetingId,
          partType: updatedPart.partType,
          title: updatedPart.title,
          assignedMember: null,
          assistant: null,
          timeAllocation: updatedPart.timeAllocation,
          notes: null,
          isCompleted: updatedPart.isCompleted,
          partNumber: updatedPart.partNumber,
          meetingDate: updatedPart.meeting.meetingDate,
          meetingType: 'midweek',
        };
      }

      // Check weekend meeting part
      const weekendPart = await prisma.weekendMeetingPart.findFirst({
        where: {
          id: partId,
          meeting: {
            congregationId,
          },
        },
        include: {
          meeting: true,
        },
      });

      if (weekendPart) {
        const updatedPart = await prisma.weekendMeetingPart.update({
          where: { id: partId },
          data: {
            assignedMember: null,
            notes: null,
            updatedAt: new Date(),
          },
          include: {
            meeting: {
              select: {
                id: true,
                meetingDate: true,
                congregationId: true,
              },
            },
          },
        });

        return {
          id: updatedPart.id,
          meetingId: updatedPart.meetingId,
          partType: updatedPart.partType,
          title: updatedPart.partType,
          assignedMember: null,
          assistant: null,
          timeAllocation: null,
          notes: null,
          isCompleted: updatedPart.isCompleted,
          meetingDate: updatedPart.meeting.meetingDate,
          meetingType: 'weekend',
        };
      }

      throw new Error('Meeting part not found');

    } catch (error) {
      console.error('Error removing assignment:', error);
      throw error;
    }
  }

  /**
   * Get member assignment summary
   */
  static async getMemberAssignmentSummary(
    congregationId: string,
    memberId: string
  ): Promise<MemberAssignmentSummary> {
    try {
      // Get member details
      const member = await prisma.member.findFirst({
        where: {
          id: memberId,
          congregationId,
        },
        select: {
          id: true,
          name: true,
          role: true,
        },
      });

      if (!member) {
        throw new Error('Member not found');
      }

      const today = new Date();
      const threeMonthsAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);

      // Get upcoming assignments
      const upcomingAssignments = await this.getUpcomingAssignments(congregationId, {
        memberId,
        includeUnassigned: false,
        limit: 50,
      });

      // Get recent assignments (last 3 months)
      const recentAssignments = await this.getUpcomingAssignments(congregationId, {
        memberId,
        startDate: threeMonthsAgo,
        endDate: today,
        includeUnassigned: false,
        limit: 50,
      });

      // Calculate total assignments and last assignment date
      const allAssignments = [...upcomingAssignments, ...recentAssignments];
      const lastAssignmentDate = allAssignments.length > 0
        ? allAssignments.reduce((latest, assignment) =>
            assignment.meetingDate > latest ? assignment.meetingDate : latest,
            allAssignments[0].meetingDate
          )
        : null;

      return {
        memberId: member.id,
        memberName: member.name,
        memberRole: member.role,
        upcomingAssignments,
        recentAssignments,
        totalAssignments: allAssignments.length,
        lastAssignmentDate,
      };

    } catch (error) {
      console.error('Error getting member assignment summary:', error);
      throw error;
    }
  }

  /**
   * Get assignment statistics for congregation
   */
  static async getAssignmentStatistics(congregationId: string): Promise<AssignmentStatistics> {
    try {
      const today = new Date();
      const threeMonthsFromNow = new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000);

      // Get all upcoming assignments
      const upcomingAssignments = await this.getUpcomingAssignments(congregationId, {
        endDate: threeMonthsFromNow,
        includeUnassigned: true,
      });

      // Calculate statistics
      const totalUpcomingAssignments = upcomingAssignments.length;
      const unassignedParts = upcomingAssignments.filter(a => !a.assignedMember).length;
      const assignedParts = upcomingAssignments.filter(a => a.assignedMember);

      // Get unique members with assignments
      const membersWithAssignments = new Set(assignedParts.map(a => a.assignedMember)).size;
      const averageAssignmentsPerMember = membersWithAssignments > 0
        ? Math.round((assignedParts.length / membersWithAssignments) * 10) / 10
        : 0;

      // Group assignments by type
      const assignmentsByType: Record<string, number> = {};
      upcomingAssignments.forEach(assignment => {
        assignmentsByType[assignment.partType] = (assignmentsByType[assignment.partType] || 0) + 1;
      });

      // Group assignments by month
      const assignmentsByMonth: Record<string, number> = {};
      upcomingAssignments.forEach(assignment => {
        const monthKey = assignment.meetingDate.toISOString().substring(0, 7); // YYYY-MM
        assignmentsByMonth[monthKey] = (assignmentsByMonth[monthKey] || 0) + 1;
      });

      // Calculate member workload
      const memberWorkloadMap = new Map<string, { count: number; lastAssignment: Date | null }>();

      assignedParts.forEach(assignment => {
        if (assignment.assignedMember) {
          const current = memberWorkloadMap.get(assignment.assignedMember) || { count: 0, lastAssignment: null };
          current.count++;
          if (!current.lastAssignment || assignment.meetingDate > current.lastAssignment) {
            current.lastAssignment = assignment.meetingDate;
          }
          memberWorkloadMap.set(assignment.assignedMember, current);
        }
      });

      // Get member names for workload
      const memberIds = Array.from(memberWorkloadMap.keys());
      const members = await prisma.member.findMany({
        where: {
          id: { in: memberIds },
          congregationId,
        },
        select: {
          id: true,
          name: true,
        },
      });

      const memberWorkload = members.map(member => {
        const workload = memberWorkloadMap.get(member.id)!;
        return {
          memberId: member.id,
          memberName: member.name,
          assignmentCount: workload.count,
          lastAssignment: workload.lastAssignment,
        };
      }).sort((a, b) => b.assignmentCount - a.assignmentCount);

      return {
        totalUpcomingAssignments,
        unassignedParts,
        membersWithAssignments,
        averageAssignmentsPerMember,
        assignmentsByType,
        assignmentsByMonth,
        memberWorkload,
      };

    } catch (error) {
      console.error('Error calculating assignment statistics:', error);
      throw error;
    }
  }

  /**
   * Get unassigned meeting parts that need assignments
   */
  static async getUnassignedParts(
    congregationId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      meetingType?: 'midweek' | 'weekend';
      limit?: number;
    } = {}
  ): Promise<MeetingPartAssignment[]> {
    try {
      return await this.getUpcomingAssignments(congregationId, {
        ...options,
        includeUnassigned: true,
      }).then(assignments => assignments.filter(a => !a.assignedMember));

    } catch (error) {
      console.error('Error getting unassigned parts:', error);
      throw error;
    }
  }

  /**
   * Check for assignment conflicts (member assigned to multiple parts on same date)
   */
  static async checkAssignmentConflicts(
    congregationId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<Array<{
    memberId: string;
    memberName: string;
    conflictDate: Date;
    assignments: MeetingPartAssignment[];
  }>> {
    try {
      const assignments = await this.getUpcomingAssignments(congregationId, {
        startDate,
        endDate,
        includeUnassigned: false,
      });

      // Group assignments by member and date
      const memberDateMap = new Map<string, Map<string, MeetingPartAssignment[]>>();

      assignments.forEach(assignment => {
        if (assignment.assignedMember) {
          const dateKey = assignment.meetingDate.toISOString().split('T')[0];

          if (!memberDateMap.has(assignment.assignedMember)) {
            memberDateMap.set(assignment.assignedMember, new Map());
          }

          const memberMap = memberDateMap.get(assignment.assignedMember)!;
          if (!memberMap.has(dateKey)) {
            memberMap.set(dateKey, []);
          }

          memberMap.get(dateKey)!.push(assignment);
        }
      });

      // Find conflicts (more than one assignment per date)
      const conflicts: Array<{
        memberId: string;
        memberName: string;
        conflictDate: Date;
        assignments: MeetingPartAssignment[];
      }> = [];

      for (const [memberId, dateMap] of memberDateMap) {
        for (const [dateKey, memberAssignments] of dateMap) {
          if (memberAssignments.length > 1) {
            conflicts.push({
              memberId,
              memberName: memberAssignments[0].member?.name || 'Unknown',
              conflictDate: new Date(dateKey),
              assignments: memberAssignments,
            });
          }
        }
      }

      return conflicts.sort((a, b) => a.conflictDate.getTime() - b.conflictDate.getTime());

    } catch (error) {
      console.error('Error checking assignment conflicts:', error);
      throw error;
    }
  }

  /**
   * Format date for display
   */
  static formatDate(date: Date): string {
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  /**
   * Get Spanish display text for part types
   */
  static getPartTypeDisplayText(partType: string): string {
    const partTypeMap: Record<string, string> = {
      // Midweek meeting parts
      'song': 'Canción',
      'prayer': 'Oración',
      'treasures': 'Tesoros de la Palabra de Dios',
      'ministry': 'Seamos mejores maestros',
      'living': 'Nuestra vida cristiana',
      'bible_reading': 'Lectura de la Biblia',
      'initial_call': 'Primera conversación',
      'return_visit': 'Revisita',
      'bible_study': 'Curso bíblico',
      'talk': 'Discurso',
      'congregation_study': 'Estudio de la congregación',

      // Weekend meeting parts
      'public_talk': 'Discurso público',
      'watchtower': 'Estudio de La Atalaya',
      'opening_prayer': 'Oración inicial',
      'closing_prayer': 'Oración final',
      'chairman': 'Presidente',
      'reader': 'Lector',
    };

    return partTypeMap[partType] || partType;
  }

  /**
   * Get meeting type display text
   */
  static getMeetingTypeDisplayText(meetingType: 'midweek' | 'weekend'): string {
    return meetingType === 'midweek' ? 'Entre Semana' : 'Fin de Semana';
  }
}
