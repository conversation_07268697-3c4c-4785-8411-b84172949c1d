'use client';

import React, { useState, useEffect } from 'react';
import { DocumentData, DocumentFilters, DocumentVisibility, DocumentPriority } from '@/lib/types/document';

interface DocumentViewerProps {
  congregationId: string;
  userRole: string;
}

export default function DocumentViewer({ congregationId, userRole }: DocumentViewerProps) {
  const [documents, setDocuments] = useState<DocumentData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<DocumentFilters>({});
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  // Load documents
  const loadDocuments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      // Add filters to params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            params.append(key, value.join(','));
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const response = await fetch(`/api/documents?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to load documents');
      }

      const data = await response.json();
      setDocuments(data.documents);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDocuments();
  }, [filters]);

  // Group documents by category
  const groupedDocuments = documents.reduce((acc, doc) => {
    const category = doc.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(doc);
    return acc;
  }, {} as Record<string, DocumentData[]>);

  const categories = Object.keys(groupedDocuments).sort();

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-800">{error}</p>
        <button 
          onClick={loadDocuments}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Congregation Documents</h2>
        <p className="text-gray-600">Access important documents and letters</p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg shadow border">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <input
              type="text"
              value={filters.search || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Search documents..."
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Category Filter */}
          <div className="md:w-48">
            <select
              value={selectedCategory}
              onChange={(e) => {
                setSelectedCategory(e.target.value);
                setFilters(prev => ({ 
                  ...prev, 
                  category: e.target.value || undefined 
                }));
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Clear Search */}
          {(filters.search || selectedCategory) && (
            <button
              onClick={() => {
                setFilters({});
                setSelectedCategory('');
              }}
              className="px-4 py-2 text-blue-600 hover:text-blue-800 underline"
            >
              Clear
            </button>
          )}
        </div>
      </div>

      {/* Documents */}
      {documents.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
          <p className="text-gray-600">
            {filters.search || selectedCategory 
              ? 'Try adjusting your search or filters'
              : 'No documents have been uploaded yet'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-8">
          {categories.map(category => {
            const categoryDocs = groupedDocuments[category];
            if (selectedCategory && selectedCategory !== category) {
              return null;
            }

            return (
              <div key={category} className="bg-white rounded-lg shadow border">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <span className="mr-2">{getCategoryIcon(category)}</span>
                    {category}
                    <span className="ml-2 text-sm text-gray-500">({categoryDocs.length})</span>
                  </h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {categoryDocs.map(document => (
                      <DocumentCard key={document.id} document={document} />
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

// Document Card Component
function DocumentCard({ document }: { document: DocumentData }) {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const getPriorityColor = (priority: DocumentPriority) => {
    switch (priority) {
      case 'URGENT': return 'border-red-500 bg-red-50';
      case 'HIGH': return 'border-orange-500 bg-orange-50';
      case 'NORMAL': return 'border-green-500 bg-green-50';
      case 'LOW': return 'border-gray-500 bg-gray-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  const getPriorityBadge = (priority: DocumentPriority) => {
    if (priority === 'URGENT' || priority === 'HIGH') {
      const color = priority === 'URGENT' ? 'bg-red-100 text-red-800' : 'bg-orange-100 text-orange-800';
      return (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${color}`}>
          {priority}
        </span>
      );
    }
    return null;
  };

  const getFileIcon = (mimeType?: string) => {
    if (!mimeType) return '📄';
    if (mimeType.includes('pdf')) return '📕';
    if (mimeType.includes('word')) return '📘';
    if (mimeType.includes('image')) return '🖼️';
    if (mimeType.includes('text')) return '📝';
    return '📄';
  };

  return (
    <div className={`border-2 rounded-lg p-4 hover:shadow-md transition-shadow ${getPriorityColor(document.priority)}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center">
          <span className="text-2xl mr-2">{getFileIcon(document.mimeType)}</span>
          <div>
            <h4 className="font-medium text-gray-900 line-clamp-2">{document.title}</h4>
            {document.subcategory && (
              <span className="text-xs text-gray-500">{document.subcategory}</span>
            )}
          </div>
        </div>
        {getPriorityBadge(document.priority)}
      </div>

      {document.description && (
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{document.description}</p>
      )}

      {document.tags && document.tags.length > 0 && (
        <div className="mb-3">
          <div className="flex flex-wrap gap-1">
            {document.tags.slice(0, 3).map((tag, index) => (
              <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                {tag}
              </span>
            ))}
            {document.tags.length > 3 && (
              <span className="text-xs text-gray-500">+{document.tags.length - 3} more</span>
            )}
          </div>
        </div>
      )}

      <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
        <span>Uploaded: {formatDate(document.uploadDate)}</span>
        {document.fileSize && (
          <span>{formatFileSize(document.fileSize)}</span>
        )}
      </div>

      {document.expirationDate && new Date(document.expirationDate) < new Date() && (
        <div className="mb-3">
          <span className="inline-flex px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
            Expired
          </span>
        </div>
      )}

      <div className="flex space-x-2">
        <a
          href={document.filePath}
          target="_blank"
          rel="noopener noreferrer"
          className="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded text-sm hover:bg-blue-700 transition-colors"
        >
          View
        </a>
        <a
          href={document.filePath}
          download={document.filename}
          className="flex-1 bg-gray-600 text-white text-center py-2 px-3 rounded text-sm hover:bg-gray-700 transition-colors"
        >
          Download
        </a>
      </div>
    </div>
  );
}

// Helper functions
function getCategoryIcon(category: string): string {
  switch (category.toLowerCase()) {
    case 'letters': return '✉️';
    case 'forms': return '📋';
    case 'announcements': return '📢';
    case 'guidelines': return '📖';
    case 'reports': return '📊';
    default: return '📄';
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
