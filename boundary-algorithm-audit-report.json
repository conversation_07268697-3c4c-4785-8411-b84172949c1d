{"timestamp": "2025-07-27T05:24:16.843Z", "analysis": {"conflictingAlgorithms": [{"name": "BoundaryGeneratorService", "status": "DISABLED", "description": "Mock boundary generator with hash-based coordinates", "coordinates": "baseLat: 25.7617, baseLng: -80.1918 + hash offsets"}, {"name": "TerritoryEnhancementService", "status": "DISABLED", "description": "Address parsing + mock boundary generation", "coordinates": "Calculated from address parsing + rectangular boundaries"}, {"name": "generate-all-territory-boundaries.js", "status": "REPLACED", "description": "Dynamic address analysis with different coordinate mapping", "coordinates": "Variable based on address analysis"}, {"name": "generate-boundaries-territory-001-pattern.js", "status": "ACTIVE", "description": "Territory 001 pattern replication (CORRECT)", "coordinates": "Miami street grid: NW_65_AVE: -80.2725, NW_67_AVE: -80.2775, etc."}], "rootCause": "Multiple boundary generation algorithms running simultaneously", "solution": "Disabled all mock services, use only Territory 001 pattern"}}