/**
 * Member Management Service
 *
 * Handles member profile management, role changes, and audit trail tracking
 * for congregation administrators. Integrates with Story 2.1 permission delegation system.
 */

import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { Permission<PERSON><PERSON><PERSON>, UserPermissionContext } from '@/lib/auth/permissionChecker';
import { PERMISSIONS } from '@/lib/auth/simpleRBAC';

export interface MemberProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  birthDate?: Date;
  role: string;
  serviceGroup?: string;
  isActive: boolean;
  lastLogin?: Date;
  preferences: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  congregationId: string;
  congregationName: string;
  // Enhanced fields for Story 2.2
  contactPreferences?: {
    preferredMethod: 'email' | 'phone' | 'text';
    allowEmergencyContact: boolean;
    privacyLevel: 'public' | 'elders_only' | 'private';
  };
  qualifications?: string[];
  notes?: string;
}

export interface MemberChangeRecord {
  id: string;
  memberId: string;
  memberName: string;
  changedBy: string;
  changedByName: string;
  changeType: 'created' | 'updated' | 'deactivated' | 'reactivated' | 'role_changed';
  fieldName: string | null;
  oldValue: string | null;
  newValue: string | null;
  reason: string | null;
  createdAt: Date;
}

export interface CreateMemberData {
  name: string;
  email: string;
  phone?: string;
  address?: string;
  birthDate?: Date;
  role: string;
  serviceGroup?: string;
  pin: string;
  contactPreferences?: {
    preferredMethod: 'email' | 'phone' | 'text';
    allowEmergencyContact: boolean;
    privacyLevel: 'public' | 'elders_only' | 'private';
  };
  qualifications?: string[];
  notes?: string;
  reason?: string;
}

export interface UpdateMemberRequest {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  birthDate?: Date;
  role?: string;
  serviceGroup?: string;
  isActive?: boolean;
  contactPreferences?: {
    preferredMethod: 'email' | 'phone' | 'text';
    allowEmergencyContact: boolean;
    privacyLevel: 'public' | 'elders_only' | 'private';
  };
  qualifications?: string[];
  notes?: string;
  reason?: string;
}

export interface MemberSearchFilters {
  role?: string[];
  serviceGroup?: string[];
  status?: string[];
  qualifications?: string[];
}

export interface MemberSearchRequest {
  query?: string;
  filters?: MemberSearchFilters;
  pagination?: {
    page: number;
    limit: number;
  };
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface UpdateMemberData {
  name?: string;
  email?: string;
  role?: string;
  pin?: string;
  isActive?: boolean;
  reason?: string;
}

export class MemberManagementService {
  /**
   * Check if user has permission to manage members
   */
  static async canManageMembers(context: UserPermissionContext): Promise<boolean> {
    return await PermissionChecker.hasPermission(
      context,
      PERMISSIONS.MANAGE_MEMBERS,
      'members'
    );
  }

  /**
   * Check if user can view member details
   */
  static async canViewMembers(context: UserPermissionContext): Promise<boolean> {
    return await PermissionChecker.hasPermission(
      context,
      PERMISSIONS.VIEW_ADMIN,
      'members'
    );
  }
  /**
   * Enhanced member search with advanced filtering capabilities
   */
  static async searchMembers(
    congregationId: string,
    searchRequest: MemberSearchRequest
  ): Promise<{ members: MemberProfile[]; total: number; pages: number }> {
    const page = searchRequest.pagination?.page || 1;
    const limit = searchRequest.pagination?.limit || 20;
    const offset = (page - 1) * limit;

    // Build where clause
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {
      congregationId,
    };

    // Text search
    if (searchRequest.query) {
      where.OR = [
        { name: { contains: searchRequest.query, mode: 'insensitive' } },
        { email: { contains: searchRequest.query, mode: 'insensitive' } },
        { phone: { contains: searchRequest.query, mode: 'insensitive' } },
      ];
    }

    // Role filter
    if (searchRequest.filters?.role && searchRequest.filters.role.length > 0) {
      where.role = { in: searchRequest.filters.role };
    }

    // Service group filter
    if (searchRequest.filters?.serviceGroup && searchRequest.filters.serviceGroup.length > 0) {
      where.serviceGroup = { in: searchRequest.filters.serviceGroup };
    }

    // Status filter
    if (searchRequest.filters?.status && searchRequest.filters.status.length > 0) {
      if (searchRequest.filters.status.includes('active')) {
        where.isActive = true;
      } else if (searchRequest.filters.status.includes('inactive')) {
        where.isActive = false;
      }
    }

    // Build order by clause
    const orderBy: any = {};
    const sortBy = searchRequest.sortBy || 'name';
    const sortOrder = searchRequest.sortOrder || 'asc';
    orderBy[sortBy] = sortOrder;

    // Get total count and members
    const [total, members] = await Promise.all([
      prisma.member.count({ where }),
      prisma.member.findMany({
        where,
        include: {
          congregation: {
            select: {
              name: true,
            },
          },
        },
        orderBy,
        skip: offset,
        take: limit,
      }),
    ]);

    const pages = Math.ceil(total / limit);

    const memberProfiles: MemberProfile[] = members.map(member => ({
      id: member.id,
      name: member.name,
      email: member.email || '',
      phone: member.phone || undefined,
      address: member.address || undefined,
      birthDate: member.birthDate || undefined,
      role: member.role,
      serviceGroup: member.serviceGroup || undefined,
      isActive: member.isActive,
      lastLogin: member.lastLogin || undefined,
      preferences: member.preferences as Record<string, any>,
      createdAt: member.createdAt,
      updatedAt: member.updatedAt,
      congregationId: member.congregationId,
      congregationName: member.congregation.name,
      contactPreferences: member.contactPreferences as any,
      qualifications: member.qualifications as string[],
      notes: member.notes || undefined,
    }));

    return {
      members: memberProfiles,
      total,
      pages,
    };
  }

  /**
   * Get all members for a congregation with pagination (legacy method)
   */
  static async getMembers(
    congregationId: string,
    page: number = 1,
    limit: number = 20,
    search?: string,
    roleFilter?: string,
    activeFilter?: boolean
  ): Promise<{ members: MemberProfile[]; total: number; pages: number }> {
    const offset = (page - 1) * limit;

    // Build where clause
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {
      congregationId,
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (roleFilter) {
      // Support comma-separated roles
      if (roleFilter.includes(',')) {
        const roles = roleFilter.split(',').map(r => r.trim()).filter(r => r);
        where.role = { in: roles };
      } else {
        where.role = roleFilter;
      }
    }

    if (activeFilter !== undefined) {
      where.isActive = activeFilter;
    }

    // Get total count and members
    const [total, members] = await Promise.all([
      prisma.member.count({ where }),
      prisma.member.findMany({
        where,
        include: {
          congregation: {
            select: {
              name: true,
            },
          },
        },
        orderBy: [
          { isActive: 'desc' },
          { role: 'desc' },
          { name: 'asc' },
        ],
        skip: offset,
        take: limit,
      }),
    ]);

    const memberProfiles: MemberProfile[] = members.map(member => ({
      id: member.id,
      name: member.name,
      email: member.email || '',
      phone: member.phone || undefined,
      address: member.address || undefined,
      birthDate: member.birthDate || undefined,
      role: member.role,
      serviceGroup: member.serviceGroup || undefined,
      isActive: member.isActive,
      lastLogin: member.lastLogin || undefined,
      preferences: member.preferences as Record<string, any>,
      createdAt: member.createdAt,
      updatedAt: member.updatedAt,
      congregationId: member.congregationId,
      congregationName: member.congregation.name,
      contactPreferences: member.contactPreferences as any,
      qualifications: member.qualifications as string[],
      notes: member.notes || undefined,
    }));

    return {
      members: memberProfiles,
      total,
      pages: Math.ceil(total / limit),
    };
  }

  /**
   * Get a single member by ID
   */
  static async getMemberById(congregationId: string, memberId: string): Promise<MemberProfile | null> {
    const member = await prisma.member.findFirst({
      where: {
        id: memberId,
        congregationId,
      },
      include: {
        congregation: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!member) {
      return null;
    }

    return {
      id: member.id,
      name: member.name,
      email: member.email || '',
      phone: member.phone || undefined,
      address: member.address || undefined,
      birthDate: member.birthDate || undefined,
      role: member.role,
      serviceGroup: member.serviceGroup || undefined,
      isActive: member.isActive,
      lastLogin: member.lastLogin || undefined,
      preferences: member.preferences as Record<string, any>,
      createdAt: member.createdAt,
      updatedAt: member.updatedAt,
      congregationId: member.congregationId,
      congregationName: member.congregation.name,
      contactPreferences: member.contactPreferences as any,
      qualifications: member.qualifications as string[],
      notes: member.notes || undefined,
    };
  }

  /**
   * Create a new member
   */
  static async createMember(
    congregationId: string,
    memberData: CreateMemberData,
    createdBy: string
  ): Promise<MemberProfile> {
    // Check if email already exists
    const existingMember = await prisma.member.findFirst({
      where: {
        congregationId,
        email: memberData.email,
      },
    });

    if (existingMember) {
      throw new Error('A member with this email already exists');
    }

    // Hash the PIN
    const hashedPin = await bcrypt.hash(memberData.pin, 10);

    // Create member and history record in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the member
      const newMember = await tx.member.create({
        data: {
          congregationId,
          name: memberData.name,
          email: memberData.email,
          phone: memberData.phone,
          address: memberData.address,
          birthDate: memberData.birthDate,
          role: memberData.role,
          serviceGroup: memberData.serviceGroup,
          pin: hashedPin,
          contactPreferences: memberData.contactPreferences || {
            preferredMethod: 'email',
            allowEmergencyContact: false,
            privacyLevel: 'public',
          },
          qualifications: memberData.qualifications || [],
          notes: memberData.notes,
        },
        include: {
          congregation: {
            select: {
              name: true,
            },
          },
        },
      });

      // Create history record
      await tx.memberChangeHistory.create({
        data: {
          congregationId,
          memberId: newMember.id,
          changedBy: createdBy,
          changeType: 'created',
          reason: memberData.reason || `Nuevo miembro creado: ${memberData.name}`,
        },
      });

      return newMember;
    });

    return {
      id: result.id,
      name: result.name,
      email: result.email || '',
      phone: result.phone || undefined,
      address: result.address || undefined,
      birthDate: result.birthDate || undefined,
      role: result.role,
      serviceGroup: result.serviceGroup || undefined,
      isActive: result.isActive,
      lastLogin: result.lastLogin || undefined,
      preferences: result.preferences as Record<string, any>,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      congregationId: result.congregationId,
      congregationName: result.congregation.name,
      contactPreferences: result.contactPreferences as any,
      qualifications: result.qualifications as string[],
      notes: result.notes || undefined,
    };
  }

  /**
   * Update a member
   */
  static async updateMember(
    congregationId: string,
    memberId: string,
    updateData: UpdateMemberData,
    updatedBy: string
  ): Promise<MemberProfile> {
    // Get current member data
    const currentMember = await prisma.member.findFirst({
      where: {
        id: memberId,
        congregationId,
      },
    });

    if (!currentMember) {
      throw new Error('Member not found');
    }

    // Check if email is being changed and if it already exists
    if (updateData.email && updateData.email !== currentMember.email) {
      const existingMember = await prisma.member.findFirst({
        where: {
          congregationId,
          email: updateData.email,
          id: { not: memberId },
        },
      });

      if (existingMember) {
        throw new Error('A member with this email already exists');
      }
    }

    // Prepare update data
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updateFields: any = {};
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const historyRecords: any[] = [];

    // Track changes for history
    if (updateData.name && updateData.name !== currentMember.name) {
      updateFields.name = updateData.name;
      historyRecords.push({
        congregationId,
        memberId,
        changedBy: updatedBy,
        changeType: 'updated',
        fieldName: 'name',
        oldValue: currentMember.name,
        newValue: updateData.name,
        reason: updateData.reason || 'Nombre actualizado',
      });
    }

    if (updateData.email && updateData.email !== currentMember.email) {
      updateFields.email = updateData.email;
      historyRecords.push({
        congregationId,
        memberId,
        changedBy: updatedBy,
        changeType: 'updated',
        fieldName: 'email',
        oldValue: currentMember.email,
        newValue: updateData.email,
        reason: updateData.reason || 'Email actualizado',
      });
    }

    if (updateData.role && updateData.role !== currentMember.role) {
      updateFields.role = updateData.role;
      historyRecords.push({
        congregationId,
        memberId,
        changedBy: updatedBy,
        changeType: 'role_changed',
        fieldName: 'role',
        oldValue: currentMember.role,
        newValue: updateData.role,
        reason: updateData.reason || 'Rol actualizado',
      });
    }

    if (updateData.pin) {
      updateFields.pin = await bcrypt.hash(updateData.pin, 10);
      historyRecords.push({
        congregationId,
        memberId,
        changedBy: updatedBy,
        changeType: 'updated',
        fieldName: 'pin',
        oldValue: '[HIDDEN]',
        newValue: '[HIDDEN]',
        reason: updateData.reason || 'PIN actualizado',
      });
    }

    if (updateData.isActive !== undefined && updateData.isActive !== currentMember.isActive) {
      updateFields.isActive = updateData.isActive;
      historyRecords.push({
        congregationId,
        memberId,
        changedBy: updatedBy,
        changeType: updateData.isActive ? 'reactivated' : 'deactivated',
        fieldName: 'isActive',
        oldValue: currentMember.isActive.toString(),
        newValue: updateData.isActive.toString(),
        reason: updateData.reason || (updateData.isActive ? 'Miembro reactivado' : 'Miembro desactivado'),
      });
    }

    if (Object.keys(updateFields).length === 0) {
      throw new Error('No changes detected');
    }

    // Update member and create history records in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update the member
      const updatedMember = await tx.member.update({
        where: { id: memberId },
        data: updateFields,
        include: {
          congregation: {
            select: {
              name: true,
            },
          },
        },
      });

      // Create history records
      for (const historyRecord of historyRecords) {
        await tx.memberChangeHistory.create({
          data: historyRecord,
        });
      }

      return updatedMember;
    });

    return {
      id: result.id,
      name: result.name,
      email: result.email || '',
      phone: result.phone || undefined,
      address: result.address || undefined,
      birthDate: result.birthDate || undefined,
      role: result.role,
      serviceGroup: result.serviceGroup || undefined,
      isActive: result.isActive,
      lastLogin: result.lastLogin || undefined,
      preferences: result.preferences as Record<string, any>,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      congregationId: result.congregationId,
      congregationName: result.congregation.name,
      contactPreferences: result.contactPreferences as any,
      qualifications: result.qualifications as string[],
      notes: result.notes || undefined,
    };
  }

  /**
   * Get member change history
   */
  static async getMemberHistory(
    congregationId: string,
    memberId?: string,
    limit: number = 50
  ): Promise<MemberChangeRecord[]> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = { congregationId };
    if (memberId) {
      where.memberId = memberId;
    }

    const history = await prisma.memberChangeHistory.findMany({
      where,
      include: {
        member: {
          select: {
            name: true,
          },
        },
        changedByMember: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });

    return history.map(record => ({
      id: record.id,
      memberId: record.memberId,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      memberName: (record as any).member.name,
      changedBy: record.changedBy,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      changedByName: (record as any).changedByMember.name,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      changeType: record.changeType as any,
      fieldName: record.fieldName,
      oldValue: record.oldValue,
      newValue: record.newValue,
      reason: record.reason,
      createdAt: record.createdAt,
    }));
  }

  /**
   * Delete a member
   */
  static async deleteMember(
    congregationId: string,
    memberId: string,
    deletedBy: string
  ): Promise<void> {
    // Get current member data for history
    const currentMember = await prisma.member.findFirst({
      where: {
        id: memberId,
        congregationId,
      },
    });

    if (!currentMember) {
      throw new Error('Member not found');
    }

    // Delete member and create history record in a transaction
    await prisma.$transaction(async (tx) => {
      // Create history record before deletion
      await tx.memberChangeHistory.create({
        data: {
          congregationId,
          memberId,
          changedBy: deletedBy,
          changeType: 'updated',
          fieldName: 'deleted',
          oldValue: 'active',
          newValue: 'deleted',
          reason: `Member ${currentMember.name} deleted from congregation`,
        },
      });

      // Delete the member
      await tx.member.delete({
        where: { id: memberId },
      });
    });
  }
}
