/**
 * Song Management Testing Script
 * 
 * Comprehensive testing for the song management system including:
 * - API endpoint testing
 * - Database operations
 * - Search and filtering functionality
 * - Sync operations
 */

const { PrismaClient } = require('@prisma/client');

class SongManagementTester {
  constructor() {
    this.prisma = new PrismaClient();
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: [],
    };
  }

  /**
   * Run all song management tests
   */
  async runAllTests() {
    console.log('🎵 Starting Song Management Tests...\n');

    try {
      await this.testDatabaseConnection();
      await this.testSongModel();
      await this.testSongCRUDOperations();
      await this.testSongSearch();
      await this.testSongStatistics();
      await this.testSongValidation();
      await this.testSpecialSongs();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      this.testResults.errors.push(`Test suite error: ${error.message}`);
    } finally {
      await this.prisma.$disconnect();
    }
  }

  /**
   * Test database connection and song table existence
   */
  async testDatabaseConnection() {
    console.log('📊 Testing database connection...');
    
    try {
      // Test basic connection
      await this.prisma.$connect();
      this.pass('Database connection established');

      // Test song table exists
      const songCount = await this.prisma.song.count();
      this.pass(`Song table accessible (${songCount} songs found)`);

      // Test special_songs table exists
      const specialSongCount = await this.prisma.specialSong.count();
      this.pass(`Special songs table accessible (${specialSongCount} special songs found)`);

    } catch (error) {
      this.fail('Database connection failed', error);
    }
  }

  /**
   * Test song model structure and constraints
   */
  async testSongModel() {
    console.log('\n🏗️ Testing song model structure...');

    try {
      // Test creating a test song
      const testSong = await this.prisma.song.create({
        data: {
          songNumber: 999999, // Use a high number to avoid conflicts
          titleEs: 'Canción de Prueba',
          titleEn: 'Test Song',
          category: 'test',
          isActive: true,
        },
      });

      this.pass('Song creation successful');

      // Test unique constraint on songNumber
      try {
        await this.prisma.song.create({
          data: {
            songNumber: 999999, // Same number should fail
            titleEs: 'Duplicate Song',
          },
        });
        this.fail('Unique constraint not working - duplicate song number allowed');
      } catch (error) {
        this.pass('Unique constraint working - duplicate song number rejected');
      }

      // Test updating song
      const updatedSong = await this.prisma.song.update({
        where: { songNumber: 999999 },
        data: { titleEs: 'Canción de Prueba Actualizada' },
      });

      if (updatedSong.titleEs === 'Canción de Prueba Actualizada') {
        this.pass('Song update successful');
      } else {
        this.fail('Song update failed - title not updated');
      }

      // Clean up test song
      await this.prisma.song.delete({
        where: { songNumber: 999999 },
      });
      this.pass('Test song cleanup successful');

    } catch (error) {
      this.fail('Song model test failed', error);
    }
  }

  /**
   * Test CRUD operations
   */
  async testSongCRUDOperations() {
    console.log('\n📝 Testing CRUD operations...');

    try {
      // Create
      const newSong = await this.prisma.song.create({
        data: {
          songNumber: 888888,
          titleEs: 'CRUD Test Song',
          titleEn: 'CRUD Test Song EN',
          category: 'test',
          isActive: true,
        },
      });
      this.pass('CREATE operation successful');

      // Read
      const foundSong = await this.prisma.song.findUnique({
        where: { songNumber: 888888 },
      });
      
      if (foundSong && foundSong.titleEs === 'CRUD Test Song') {
        this.pass('READ operation successful');
      } else {
        this.fail('READ operation failed - song not found or incorrect data');
      }

      // Update
      await this.prisma.song.update({
        where: { songNumber: 888888 },
        data: { titleEs: 'Updated CRUD Test Song' },
      });

      const updatedSong = await this.prisma.song.findUnique({
        where: { songNumber: 888888 },
      });

      if (updatedSong && updatedSong.titleEs === 'Updated CRUD Test Song') {
        this.pass('UPDATE operation successful');
      } else {
        this.fail('UPDATE operation failed');
      }

      // Delete
      await this.prisma.song.delete({
        where: { songNumber: 888888 },
      });

      const deletedSong = await this.prisma.song.findUnique({
        where: { songNumber: 888888 },
      });

      if (!deletedSong) {
        this.pass('DELETE operation successful');
      } else {
        this.fail('DELETE operation failed - song still exists');
      }

    } catch (error) {
      this.fail('CRUD operations test failed', error);
    }
  }

  /**
   * Test search functionality
   */
  async testSongSearch() {
    console.log('\n🔍 Testing search functionality...');

    try {
      // Create test songs for search
      const testSongs = [
        { songNumber: 777771, titleEs: 'Jehová es mi Pastor', titleEn: 'Jehovah Is My Shepherd' },
        { songNumber: 777772, titleEs: 'Alabemos a Jehová', titleEn: 'Praise Jehovah' },
        { songNumber: 777773, titleEs: 'Canción de Alabanza', titleEn: 'Song of Praise' },
      ];

      for (const song of testSongs) {
        await this.prisma.song.create({
          data: { ...song, isActive: true },
        });
      }

      // Test search by song number
      const numberSearch = await this.prisma.song.findMany({
        where: { songNumber: 777771 },
      });

      if (numberSearch.length === 1) {
        this.pass('Search by song number successful');
      } else {
        this.fail('Search by song number failed');
      }

      // Test search by Spanish title
      const spanishSearch = await this.prisma.song.findMany({
        where: {
          titleEs: {
            contains: 'Jehová',
            mode: 'insensitive',
          },
        },
      });

      if (spanishSearch.length >= 2) {
        this.pass('Search by Spanish title successful');
      } else {
        this.fail('Search by Spanish title failed');
      }

      // Test search by English title
      const englishSearch = await this.prisma.song.findMany({
        where: {
          titleEn: {
            contains: 'Jehovah',
            mode: 'insensitive',
          },
        },
      });

      if (englishSearch.length >= 2) {
        this.pass('Search by English title successful');
      } else {
        this.fail('Search by English title failed');
      }

      // Clean up test songs
      await this.prisma.song.deleteMany({
        where: {
          songNumber: {
            in: [777771, 777772, 777773],
          },
        },
      });
      this.pass('Search test cleanup successful');

    } catch (error) {
      this.fail('Search functionality test failed', error);
    }
  }

  /**
   * Test song statistics
   */
  async testSongStatistics() {
    console.log('\n📈 Testing song statistics...');

    try {
      // Get basic statistics
      const totalSongs = await this.prisma.song.count();
      const activeSongs = await this.prisma.song.count({ where: { isActive: true } });
      const inactiveSongs = await this.prisma.song.count({ where: { isActive: false } });

      if (totalSongs >= 0) {
        this.pass(`Total songs count: ${totalSongs}`);
      }

      if (activeSongs + inactiveSongs === totalSongs) {
        this.pass('Active/inactive song counts are consistent');
      } else {
        this.fail('Active/inactive song counts are inconsistent');
      }

      // Test language statistics
      const songsWithSpanish = await this.prisma.song.count({
        where: {
          titleEs: { not: null },
          titleEs: { not: '' },
        },
      });

      const songsWithEnglish = await this.prisma.song.count({
        where: {
          titleEn: { not: null },
          titleEn: { not: '' },
        },
      });

      this.pass(`Songs with Spanish titles: ${songsWithSpanish}`);
      this.pass(`Songs with English titles: ${songsWithEnglish}`);

    } catch (error) {
      this.fail('Song statistics test failed', error);
    }
  }

  /**
   * Test song validation functionality
   */
  async testSongValidation() {
    console.log('\n✅ Testing song validation...');

    try {
      // Test that song numbers are positive integers
      const songs = await this.prisma.song.findMany({
        where: { songNumber: { lte: 0 } },
      });

      if (songs.length === 0) {
        this.pass('All song numbers are positive');
      } else {
        this.fail(`Found ${songs.length} songs with non-positive numbers`);
      }

      // Test that active songs have at least one title
      const activeSongsWithoutTitles = await this.prisma.song.findMany({
        where: {
          isActive: true,
          AND: [
            { OR: [{ titleEs: null }, { titleEs: '' }] },
            { OR: [{ titleEn: null }, { titleEn: '' }] },
          ],
        },
      });

      if (activeSongsWithoutTitles.length === 0) {
        this.pass('All active songs have at least one title');
      } else {
        this.fail(`Found ${activeSongsWithoutTitles.length} active songs without titles`);
      }

    } catch (error) {
      this.fail('Song validation test failed', error);
    }
  }

  /**
   * Test special songs functionality
   */
  async testSpecialSongs() {
    console.log('\n🎭 Testing special songs...');

    try {
      // Test special song creation
      const specialSong = await this.prisma.specialSong.create({
        data: {
          keyName: 'test-special',
          titleEs: 'Canción Especial de Prueba',
          titleEn: 'Test Special Song',
          isCustom: true,
          isActive: true,
        },
      });

      this.pass('Special song creation successful');

      // Test special song retrieval
      const foundSpecialSong = await this.prisma.specialSong.findUnique({
        where: { keyName: 'test-special' },
      });

      if (foundSpecialSong) {
        this.pass('Special song retrieval successful');
      } else {
        this.fail('Special song retrieval failed');
      }

      // Clean up
      await this.prisma.specialSong.delete({
        where: { keyName: 'test-special' },
      });
      this.pass('Special song cleanup successful');

    } catch (error) {
      this.fail('Special songs test failed', error);
    }
  }

  /**
   * Helper method to record a passed test
   */
  pass(message) {
    console.log(`✅ ${message}`);
    this.testResults.passed++;
  }

  /**
   * Helper method to record a failed test
   */
  fail(message, error = null) {
    console.log(`❌ ${message}`);
    if (error) {
      console.log(`   Error: ${error.message}`);
      this.testResults.errors.push(`${message}: ${error.message}`);
    } else {
      this.testResults.errors.push(message);
    }
    this.testResults.failed++;
  }

  /**
   * Print final test results
   */
  printTestResults() {
    console.log('\n' + '='.repeat(50));
    console.log('🎵 SONG MANAGEMENT TEST RESULTS');
    console.log('='.repeat(50));
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📊 Total: ${this.testResults.passed + this.testResults.failed}`);

    if (this.testResults.failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (this.testResults.failed === 0) {
      console.log('\n🎉 All tests passed! Song management system is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the errors above.');
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new SongManagementTester();
  tester.runAllTests().catch(console.error);
}

module.exports = SongManagementTester;
