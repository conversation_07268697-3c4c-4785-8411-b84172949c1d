-- Fix Territory Schema Migration
-- This script updates the territories table to match the expected Prisma schema

-- Step 1: Add missing columns
ALTER TABLE territories 
ADD COLUMN IF NOT EXISTS territory_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS address TEXT,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS display_order INTEGER;

-- Step 2: Migrate data from old columns to new columns
-- Copy 'name' to 'territory_number' and 'description' to 'address'
UPDATE territories 
SET 
  territory_number = COALESCE(name, 'Unknown'),
  address = COALESCE(description, 'No address provided')
WHERE territory_number IS NULL OR address IS NULL;

-- Step 3: Make required columns NOT NULL after data migration
ALTER TABLE territories 
ALTER COLUMN territory_number SET NOT NULL,
ALTER COLUMN address SET NOT NULL;

-- Step 4: Add unique constraint for territory_number per congregation
-- First, handle any potential duplicates
WITH numbered_territories AS (
  SELECT id, territory_number, congregation_id,
         ROW_NUMBER() OVER (PARTITION BY territory_number, congregation_id ORDER BY created_at) as rn
  FROM territories
)
UPDATE territories 
SET territory_number = territory_number || '-' || (
  SELECT rn::text 
  FROM numbered_territories 
  WHERE numbered_territories.id = territories.id
)
WHERE id IN (
  SELECT id 
  FROM numbered_territories 
  WHERE rn > 1
);

-- Add the unique constraint
ALTER TABLE territories 
ADD CONSTRAINT territories_territory_number_congregation_id_key 
UNIQUE (territory_number, congregation_id);

-- Step 5: Add indexes for performance
CREATE INDEX IF NOT EXISTS territories_congregation_id_idx ON territories(congregation_id);
CREATE INDEX IF NOT EXISTS territories_congregation_id_status_idx ON territories(congregation_id, status);
CREATE INDEX IF NOT EXISTS territories_display_order_idx ON territories(display_order);

-- Step 6: Update the status column to match expected enum values
UPDATE territories 
SET status = 'available' 
WHERE status NOT IN ('available', 'assigned', 'completed', 'out_of_service');

-- Verification queries
SELECT 'Schema Fix Complete' as status;
SELECT 'Columns after migration:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'territories' 
ORDER BY ordinal_position;
