/**
 * Service Schedule Management Service
 *
 * Comprehensive service for managing field service schedules, including
 * weekly schedule creation, service time management, and conductor assignments.
 */

import { prisma } from '@/lib/prisma';

export interface ServiceScheduleInput {
  weekStartDate: string; // YYYY-MM-DD format
  weekEndDate: string;   // YYYY-MM-DD format
}

export interface ServiceScheduleTimeInput {
  serviceDate: string;   // YYYY-MM-DD format
  serviceTime: string;   // HH:MM format
  location: string;
  address?: string;
  conductorId?: string;
  notes?: string;
}

export interface ServiceSchedule {
  id: string;
  congregationId: string;
  weekStartDate: Date;
  weekEndDate: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  scheduleTimes?: ServiceScheduleTime[];
}

export interface ServiceScheduleTime {
  id: string;
  scheduleId: string;
  serviceDate: Date;
  serviceTime: string;
  location: string;
  address?: string;
  conductorId?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  conductor?: {
    id: string;
    name: string;
    role: string;
  };
}

export class ServiceScheduleService {
  /**
   * Get service schedule for a specific week
   */
  static async getWeeklySchedule(
    congregationId: string,
    weekStartDate: string
  ): Promise<ServiceSchedule | null> {
    try {
      const startDate = new Date(weekStartDate);

      const schedule = await prisma.serviceSchedule.findFirst({
        where: {
          congregationId,
          weekStartDate: startDate,
          isActive: true,
        },
        include: {
          scheduleTimes: {
            where: { isActive: true },
            include: {
              conductor: {
                select: {
                  id: true,
                  name: true,
                  role: true,
                },
              },
            },
            orderBy: [
              { serviceDate: 'asc' },
              { serviceTime: 'asc' },
            ],
          },
        },
      });

      return schedule;
    } catch (error) {
      console.error('Error fetching weekly schedule:', error);
      throw new Error('Failed to fetch weekly schedule');
    }
  }

  /**
   * Create or update weekly service schedule
   */
  static async upsertWeeklySchedule(
    congregationId: string,
    scheduleData: ServiceScheduleInput
  ): Promise<ServiceSchedule> {
    try {
      const startDate = new Date(scheduleData.weekStartDate);
      const endDate = new Date(scheduleData.weekEndDate);

      // Validate date range
      if (startDate >= endDate) {
        throw new Error('Week start date must be before end date');
      }

      const schedule = await prisma.serviceSchedule.upsert({
        where: {
          congregationId_weekStartDate: {
            congregationId,
            weekStartDate: startDate,
          },
        },
        update: {
          weekEndDate: endDate,
          updatedAt: new Date(),
        },
        create: {
          congregationId,
          weekStartDate: startDate,
          weekEndDate: endDate,
        },
        include: {
          scheduleTimes: {
            where: { isActive: true },
            include: {
              conductor: {
                select: {
                  id: true,
                  name: true,
                  role: true,
                },
              },
            },
            orderBy: [
              { serviceDate: 'asc' },
              { serviceTime: 'asc' },
            ],
          },
        },
      });

      return schedule;
    } catch (error) {
      console.error('Error upserting weekly schedule:', error);
      throw error;
    }
  }

  /**
   * Add service time to schedule
   */
  static async addServiceTime(
    congregationId: string,
    scheduleId: string,
    timeData: ServiceScheduleTimeInput
  ): Promise<ServiceScheduleTime> {
    try {
      // Verify schedule belongs to congregation
      const schedule = await prisma.serviceSchedule.findFirst({
        where: {
          id: scheduleId,
          congregationId,
          isActive: true,
        },
      });

      if (!schedule) {
        throw new Error('Schedule not found or does not belong to congregation');
      }

      // Validate conductor if provided
      if (timeData.conductorId) {
        const conductor = await prisma.member.findFirst({
          where: {
            id: timeData.conductorId,
            congregationId,
            isActive: true,
          },
        });

        if (!conductor) {
          throw new Error('Conductor not found or does not belong to congregation');
        }
      }

      const serviceTime = await prisma.serviceScheduleTime.create({
        data: {
          scheduleId,
          serviceDate: new Date(timeData.serviceDate),
          serviceTime: timeData.serviceTime,
          location: timeData.location,
          address: timeData.address,
          conductorId: timeData.conductorId,
          notes: timeData.notes,
        },
        include: {
          conductor: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return serviceTime;
    } catch (error) {
      console.error('Error adding service time:', error);
      throw error;
    }
  }

  /**
   * Update service time
   */
  static async updateServiceTime(
    congregationId: string,
    timeId: string,
    timeData: Partial<ServiceScheduleTimeInput>
  ): Promise<ServiceScheduleTime> {
    try {
      // Verify service time belongs to congregation
      const existingTime = await prisma.serviceScheduleTime.findFirst({
        where: {
          id: timeId,
          schedule: {
            congregationId,
            isActive: true,
          },
          isActive: true,
        },
      });

      if (!existingTime) {
        throw new Error('Service time not found or does not belong to congregation');
      }

      // Validate conductor if provided
      if (timeData.conductorId) {
        const conductor = await prisma.member.findFirst({
          where: {
            id: timeData.conductorId,
            congregationId,
            isActive: true,
          },
        });

        if (!conductor) {
          throw new Error('Conductor not found or does not belong to congregation');
        }
      }

      const updateData: any = {
        updatedAt: new Date(),
      };

      if (timeData.serviceDate) updateData.serviceDate = new Date(timeData.serviceDate);
      if (timeData.serviceTime) updateData.serviceTime = timeData.serviceTime;
      if (timeData.location) updateData.location = timeData.location;
      if (timeData.address !== undefined) updateData.address = timeData.address;
      if (timeData.conductorId !== undefined) updateData.conductorId = timeData.conductorId;
      if (timeData.notes !== undefined) updateData.notes = timeData.notes;

      const serviceTime = await prisma.serviceScheduleTime.update({
        where: { id: timeId },
        data: updateData,
        include: {
          conductor: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return serviceTime;
    } catch (error) {
      console.error('Error updating service time:', error);
      throw error;
    }
  }

  /**
   * Delete service time
   */
  static async deleteServiceTime(
    congregationId: string,
    timeId: string
  ): Promise<void> {
    try {
      // Verify service time belongs to congregation
      const existingTime = await prisma.serviceScheduleTime.findFirst({
        where: {
          id: timeId,
          schedule: {
            congregationId,
            isActive: true,
          },
          isActive: true,
        },
      });

      if (!existingTime) {
        throw new Error('Service time not found or does not belong to congregation');
      }

      await prisma.serviceScheduleTime.update({
        where: { id: timeId },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Error deleting service time:', error);
      throw error;
    }
  }

  /**
   * Get all service times for a congregation
   */
  static async getAllServiceTimes(congregationId: string): Promise<ServiceScheduleTime[]> {
    try {
      const serviceTimes = await prisma.serviceScheduleTime.findMany({
        where: {
          schedule: {
            congregationId: congregationId,
            isActive: true,
          },
          isActive: true,
        },
        include: {
          conductor: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
        orderBy: [
          { serviceDate: 'asc' },
          { serviceTime: 'asc' },
        ],
      });

      return serviceTimes.map(time => ({
        id: time.id,
        scheduleId: time.scheduleId,
        serviceDate: time.serviceDate,
        serviceTime: time.serviceTime,
        location: time.location,
        address: time.address || undefined,
        conductorId: time.conductorId || undefined,
        notes: time.notes || undefined,
        isActive: time.isActive,
        createdAt: time.createdAt,
        updatedAt: time.updatedAt,
        conductor: time.conductor ? {
          id: time.conductor.id,
          name: time.conductor.name,
          role: time.conductor.role,
        } : undefined,
      }));
    } catch (error) {
      console.error('Error fetching all service times:', error);
      throw new Error('Failed to fetch service times');
    }
  }

  /**
   * Get current week's schedule
   */
  static getCurrentWeekDates(): { startDate: string; endDate: string } {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Calculate Monday of current week
    const monday = new Date(now);
    monday.setDate(now.getDate() - dayOfWeek + 1);

    // Calculate Sunday of current week
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);

    return {
      startDate: monday.toISOString().split('T')[0],
      endDate: sunday.toISOString().split('T')[0],
    };
  }

  /**
   * Get available conductors for service times
   */
  static async getAvailableConductors(congregationId: string): Promise<Array<{
    id: string;
    name: string;
    role: string;
  }>> {
    try {
      const conductors = await prisma.member.findMany({
        where: {
          congregationId,
          isActive: true,
          role: {
            in: ['elder', 'ministerial_servant'],
          },
        },
        select: {
          id: true,
          name: true,
          role: true,
        },
        orderBy: {
          name: 'asc',
        },
      });

      return conductors;
    } catch (error) {
      console.error('Error fetching available conductors:', error);
      throw new Error('Failed to fetch available conductors');
    }
  }
}
