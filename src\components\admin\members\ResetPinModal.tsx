'use client';

import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import { MemberProfile } from '@/lib/services/memberManagementService';

interface ResetPinModalProps {
  isOpen: boolean;
  onClose: () => void;
  member: MemberProfile | null;
  onSuccess: (newPin?: string) => void;
}

type ResetMode = 'auto' | 'custom';

export default function ResetPinModal({
  isOpen,
  onClose,
  member,
  onSuccess,
}: ResetPinModalProps) {
  const [resetMode, setResetMode] = useState<ResetMode>('auto');
  const [customPin, setCustomPin] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setResetMode('auto');
      setCustomPin('');
      setError(null);
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const validateCustomPin = (pin: string): boolean => {
    return /^\d{4,8}$/.test(pin);
  };

  const isFormValid = (): boolean => {
    if (resetMode === 'auto') return true;
    return validateCustomPin(customPin);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!member || !isFormValid()) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/pin-management/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('hermanos_token')}`,
        },
        body: JSON.stringify({
          memberId: member.id,
          reason: 'PIN reset from member management interface',
          resetType: resetMode,
          ...(resetMode === 'custom' && { customPin }),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reset PIN');
      }

      const data = await response.json();

      if (data.success) {
        onSuccess(data.newPin);
        // Auto-close modal after 2 seconds
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        throw new Error(data.error || 'Failed to reset PIN');
      }
    } catch (error) {
      console.error('Error resetting PIN:', error);
      setError(error instanceof Error ? error.message : 'Error al resetear el PIN');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  if (!member) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`Reset PIN for ${member.name}`}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Reset Mode Selection */}
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name="resetMode"
                value="auto"
                checked={resetMode === 'auto'}
                onChange={(e) => setResetMode(e.target.value as ResetMode)}
                disabled={isSubmitting}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <div className="text-sm font-medium text-gray-900">
                Auto-generate secure PIN
              </div>
            </label>

            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name="resetMode"
                value="custom"
                checked={resetMode === 'custom'}
                onChange={(e) => setResetMode(e.target.value as ResetMode)}
                disabled={isSubmitting}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <div className="text-sm font-medium text-gray-900">
                Set custom PIN
              </div>
            </label>
          </div>

          {/* Custom PIN Input */}
          {resetMode === 'custom' && (
            <div className="ml-7 mt-2 animate-in slide-in-from-top-2 duration-200">
              <input
                type="text"
                value={customPin}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').slice(0, 8);
                  setCustomPin(value);
                  if (error) setError(null);
                }}
                placeholder="Enter 4-8 digit PIN"
                disabled={isSubmitting}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${
                  customPin && !validateCustomPin(customPin)
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 focus:border-blue-500'
                }`}
                maxLength={8}
              />
              {customPin && !validateCustomPin(customPin) && (
                <p className="text-xs text-red-600 mt-1">
                  PIN must be 4-8 digits
                </p>
              )}
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Footer */}
        <div className="flex justify-end space-x-3 pt-3 border-t border-gray-200">
          <button
            type="button"
            onClick={handleClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!isFormValid() || isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            {isSubmitting && (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
            <span>{isSubmitting ? 'Resetting...' : 'Reset PIN'}</span>
          </button>
        </div>
      </form>
    </Modal>
  );
}
