/**
 * Congregation Analytics API Endpoint
 * 
 * Provides comprehensive analytics and reporting for congregation activities
 * including field service, tasks, assignments, and member engagement.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { CongregationAnalyticsService } from '@/lib/services/congregationAnalyticsService';

// Validation schema for analytics requests
const AnalyticsRequestSchema = z.object({
  type: z.enum(['overview', 'metrics', 'members', 'export']).default('overview'),
  includeInactive: z.string().transform(val => val === 'true').optional(),
  limit: z.string().transform(val => parseInt(val, 10)).optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  reportType: z.string().optional(),
});

/**
 * GET /api/analytics
 * Retrieve congregation analytics data
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can view analytics
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view congregation analytics' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = AnalyticsRequestSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { type, includeInactive, limit } = validationResult.data;

    switch (type) {
      case 'overview':
        // Get congregation overview with health metrics
        const overview = await CongregationAnalyticsService.getCongregationOverview(
          member.congregationId
        );

        return NextResponse.json({
          success: true,
          overview,
          type: 'overview',
        });

      case 'metrics':
        // Get comprehensive activity metrics
        const metrics = await CongregationAnalyticsService.getActivityMetrics(
          member.congregationId
        );

        return NextResponse.json({
          success: true,
          metrics,
          type: 'metrics',
        });

      case 'members':
        // Get member engagement profiles
        const memberProfiles = await CongregationAnalyticsService.getMemberEngagementProfiles(
          member.congregationId,
          {
            includeInactive: includeInactive ?? false,
            limit: limit || 50,
          }
        );

        return NextResponse.json({
          success: true,
          memberProfiles,
          count: memberProfiles.length,
          type: 'members',
        });

      default:
        return NextResponse.json(
          { error: 'Invalid analytics type' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Analytics GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve analytics data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/analytics
 * Generate analytics reports and export data
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders can generate comprehensive reports
    if (member.role !== 'elder') {
      return NextResponse.json(
        { error: 'Only elders can generate comprehensive analytics reports' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { reportType, startDate, endDate, includeMembers } = body;

    // Validate date range
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
      return NextResponse.json(
        { error: 'Dates must be in YYYY-MM-DD format' },
        { status: 400 }
      );
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start > end) {
      return NextResponse.json(
        { error: 'Start date must be before or equal to end date' },
        { status: 400 }
      );
    }

    // Limit to 1 year maximum
    const daysDiff = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
    if (daysDiff > 365) {
      return NextResponse.json(
        { error: 'Date range cannot exceed 1 year' },
        { status: 400 }
      );
    }

    // Generate comprehensive export data
    const exportData = await CongregationAnalyticsService.generateExportData(
      member.congregationId,
      {
        reportType: reportType || 'comprehensive',
        startDate: start,
        endDate: end,
        includeMembers: includeMembers ?? true,
      }
    );

    return NextResponse.json({
      success: true,
      exportData,
      message: 'Analytics report generated successfully',
    });

  } catch (error) {
    console.error('Analytics report generation error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to generate analytics report',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
