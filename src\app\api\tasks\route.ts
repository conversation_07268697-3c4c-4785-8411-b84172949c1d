/**
 * Tasks API Endpoint
 * 
 * Handles CRUD operations for congregation tasks including
 * task creation, management, and retrieval with proper validation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { TaskManagementService, TaskInput } from '@/lib/services/taskManagementService';

// Validation schema for task creation/update
const TaskSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title cannot exceed 255 characters'),
  description: z.string().max(2000, 'Description cannot exceed 2000 characters').optional(),
  category: z.string().min(1, 'Category is required').max(100, 'Category cannot exceed 100 characters'),
  frequency: z.enum(['weekly', 'monthly', 'quarterly', 'yearly', 'one-time'], {
    errorMap: () => ({ message: 'Invalid frequency. Must be: weekly, monthly, quarterly, yearly, or one-time' })
  }),
  estimatedTime: z.number().min(0).max(480).optional(),
  instructions: z.string().max(2000, 'Instructions cannot exceed 2000 characters').optional(),
});

// Validation schema for GET requests
const GetTasksSchema = z.object({
  category: z.string().optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  includeAssignments: z.string().transform(val => val === 'true').optional(),
  page: z.string().transform(val => parseInt(val, 10)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).optional(),
});

/**
 * GET /api/tasks
 * Retrieve tasks for the congregation
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = GetTasksSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { category, isActive, includeAssignments, page, limit } = validationResult.data;

    // Get tasks
    const result = await TaskManagementService.getTasks(
      member.congregationId,
      {
        category,
        isActive,
        includeAssignments,
        page: page || 1,
        limit: limit || 20,
      }
    );

    return NextResponse.json({
      success: true,
      tasks: result.tasks,
      pagination: {
        page: page || 1,
        limit: limit || 20,
        total: result.total,
        totalPages: Math.ceil(result.total / (limit || 20)),
      },
    });

  } catch (error) {
    console.error('Tasks GET error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve tasks',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tasks
 * Create a new task
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can create tasks
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create tasks' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = TaskSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const taskData: TaskInput = validationResult.data;

    // Create the task
    const task = await TaskManagementService.createTask(
      member.congregationId,
      taskData
    );

    return NextResponse.json({
      success: true,
      task,
      message: 'Task created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Tasks POST error:', error);
    
    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('Invalid task data')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }
    
    return NextResponse.json(
      {
        error: 'Failed to create task',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/tasks
 * Update an existing task
 */
export async function PUT(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can update tasks
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update tasks' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { taskId, ...taskData } = body;

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    // Validate task data (partial validation for updates)
    if (Object.keys(taskData).length > 0) {
      const validationResult = TaskSchema.partial().safeParse(taskData);
      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: 'Invalid request data',
            details: validationResult.error.errors,
          },
          { status: 400 }
        );
      }
    }

    // Update the task
    const task = await TaskManagementService.updateTask(
      member.congregationId,
      taskId,
      taskData
    );

    return NextResponse.json({
      success: true,
      task,
      message: 'Task updated successfully',
    });

  } catch (error) {
    console.error('Tasks PUT error:', error);
    
    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      if (error.message.includes('Invalid task data')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }
    
    return NextResponse.json(
      {
        error: 'Failed to update task',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/tasks
 * Delete a task (only if no assignments exist)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders can delete tasks
    if (member.role !== 'elder') {
      return NextResponse.json(
        { error: 'Only elders can delete tasks' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { taskId } = body;

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    // Delete the task
    await TaskManagementService.deleteTask(
      member.congregationId,
      taskId
    );

    return NextResponse.json({
      success: true,
      message: 'Task deleted successfully',
    });

  } catch (error) {
    console.error('Tasks DELETE error:', error);
    
    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      if (error.message.includes('Cannot delete task with existing assignments')) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
    }
    
    return NextResponse.json(
      {
        error: 'Failed to delete task',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT to update tasks.' },
    { status: 405 }
  );
}
