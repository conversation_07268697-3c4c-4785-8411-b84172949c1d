/**
 * PIN Validation API Endpoint
 *
 * Validates PIN format and uniqueness based on congregation settings.
 * Only accessible to elders, coordinators, and developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { PinService } from '@/lib/services/pinService';

// Validation schema for PIN validation request
const PinValidationRequestSchema = z.object({
  pin: z.string().min(1, 'PIN is required'),
});

/**
 * POST - Validate a PIN
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = extractAndVerifyToken(request);

    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }

    const user = authResult.user!;

    // Verify user has admin access
    if (!['elder', 'coordinator'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to validate PINs' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = PinValidationRequestSchema.parse(body);

    // Validate the PIN
    const validationResult = await PinService.validatePin(
      validatedData.pin,
      user.congregationId
    );

    return NextResponse.json({
      success: true,
      validation: validationResult,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('PIN validation POST error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid validation request data',
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to validate PIN';

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
