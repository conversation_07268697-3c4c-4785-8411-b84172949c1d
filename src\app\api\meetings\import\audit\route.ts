/**
 * Import Audit API Endpoint
 * 
 * Provides access to import statistics, error reports, and audit logs
 * for troubleshooting and monitoring import operations.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { ImportAuditService } from '@/lib/services/importAuditService';

// Validation schema for audit requests
const AuditRequestSchema = z.object({
  type: z.enum(['statistics', 'errors', 'logs']),
  days: z.number().min(1).max(365).default(30),
});

export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { member } = authResult;

    // Check if user has permission to view audit data
    if (!['elder', 'overseer_coordinator', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view audit data' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const type = searchParams.get('type') || 'statistics';
    const days = parseInt(searchParams.get('days') || '30');

    const validationResult = AuditRequestSchema.safeParse({ type, days });
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { type: requestType, days: requestDays } = validationResult.data;

    switch (requestType) {
      case 'statistics':
        const statistics = await ImportAuditService.getImportStatistics(
          member.congregationId,
          requestDays
        );
        
        return NextResponse.json({
          success: true,
          type: 'statistics',
          data: statistics,
          period: `${requestDays} days`,
        });

      case 'errors':
        const errorReport = await ImportAuditService.getErrorReport(
          member.congregationId,
          requestDays
        );
        
        return NextResponse.json({
          success: true,
          type: 'errors',
          data: errorReport,
          period: `${requestDays} days`,
        });

      case 'logs':
        const statistics2 = await ImportAuditService.getImportStatistics(
          member.congregationId,
          requestDays
        );
        
        return NextResponse.json({
          success: true,
          type: 'logs',
          data: {
            logs: statistics2.recentActivity,
            summary: {
              totalImports: statistics2.totalImports,
              successfulImports: statistics2.successfulImports,
              failedImports: statistics2.failedImports,
              successRate: statistics2.successRate,
            },
          },
          period: `${requestDays} days`,
        });

      default:
        return NextResponse.json(
          { error: 'Invalid audit type requested' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Audit API error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve audit data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const authResult = await extractAndVerifyToken(request);
    if (!authResult.success || !authResult.member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { member } = authResult;

    // Check if user has permission to manage audit data
    if (!['elder', 'overseer_coordinator', 'developer'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to manage audit data' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'cleanup':
        const deletedCount = await ImportAuditService.cleanupOldLogs(member.congregationId);
        
        return NextResponse.json({
          success: true,
          message: `Cleaned up ${deletedCount} old audit logs`,
          deletedCount,
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action requested' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Audit management error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to manage audit data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET for audit data or POST for management actions.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST with cleanup action to manage audit data.' },
    { status: 405 }
  );
}
