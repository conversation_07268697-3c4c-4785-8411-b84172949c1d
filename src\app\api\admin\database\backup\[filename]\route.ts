import { NextRequest, NextResponse } from 'next/server';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import path from 'path';
import fs from 'fs/promises';

// DELETE - Delete backup file
export async function DELETE(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    const authResult = extractAndVerifyToken(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin permissions (only coordinators and elders can delete backups)
    if (!['coordinator', 'elder'].includes(authResult.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { filename } = params;

    // Validate filename (security check)
    if (!filename || !filename.endsWith('.sql') || filename.includes('..') || filename.includes('/')) {
      return NextResponse.json({ error: 'Invalid filename' }, { status: 400 });
    }

    // Get backup file path
    const backupDir = path.join(process.cwd(), 'backups');
    const filePath = path.join(backupDir, filename);

    try {
      // Check if file exists
      await fs.access(filePath);

      // Delete file
      await fs.unlink(filePath);

      console.log(`Backup deleted: ${filename} by user ${authResult.user.id}`);

      return NextResponse.json({
        success: true,
        message: 'Backup deleted successfully'
      });

    } catch (fileError) {
      console.error('File not found:', filePath);
      return NextResponse.json({ error: 'Backup file not found' }, { status: 404 });
    }

  } catch (error) {
    console.error('Error deleting backup:', error);
    return NextResponse.json(
      { error: 'Failed to delete backup' },
      { status: 500 }
    );
  }
}
