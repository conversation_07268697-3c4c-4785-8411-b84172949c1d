'use client';

/**
 * Member Territories Page
 *
 * Main page for congregation members to view and manage their assigned territories.
 * Follows Field Service UI patterns with mobile-optimized design.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MyTerritories from '@/components/territories/member/MyTerritories';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
  hasCongregationPinAccess?: boolean;
}

export default function TerritoriesPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('hermanos_token');
        if (!token) {
          router.push('/login');
          return;
        }

        // Verify token with server
        const response = await fetch('/api/auth/verify', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          localStorage.removeItem('hermanos_token');
          router.push('/login');
          return;
        }

        const userData = await response.json();
        setUser(userData.user);
      } catch (error) {
        console.error('Auth check failed:', error);
        localStorage.removeItem('hermanos_token');
        router.push('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <div className="text-sm text-gray-600">Verificando acceso...</div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.push('/dashboard')}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Territorios</h1>
                <p className="text-sm text-gray-500">{user.congregationName}</p>
              </div>
            </div>


          </div>
        </div>
      </div>

      {/* Main Content */}
      <MyTerritories />
    </div>
  );
}
