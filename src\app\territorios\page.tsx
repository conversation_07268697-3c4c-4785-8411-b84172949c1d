'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  MapPinIcon,
  CalendarDaysIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';

interface User {
  id: string;
  name: string;
  role: string;
  congregationId: string;
  congregationName: string;
}

interface Territory {
  id: string;
  territory: {
    id: string;
    territoryNumber: string;
    address: string;
    status: string;
    notes?: string;
  };
  assignedAt: string;
  assignedBy: {
    name: string;
    role: string;
  };
  daysAssigned: number;
  visitCount: number;
  isPartiallyCompleted: boolean;
  partialCompletionNotes?: string;
  progressPercentage: number;
  recentVisits: any[];
  status: string;
}

interface TerritorySummary {
  totalAssigned: number;
  inProgress: number;
  partiallyCompleted: number;
  averageDaysAssigned: number;
}

export default function TerritoriosPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [territories, setTerritories] = useState<Territory[]>([]);
  const [summary, setSummary] = useState<TerritorySummary | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAuthentication();
    loadMyTerritories();
  }, []);

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        router.push('/login');
        return;
      }

      const userData = await response.json();
      setUser(userData.user);
    } catch (error) {
      console.error('Authentication error:', error);
      router.push('/login');
    }
  };

  const loadMyTerritories = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');

      const response = await fetch('/api/territories/my-territories', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al cargar territorios');
      }

      const result = await response.json();
      setTerritories(result.data.territories);
      setSummary(result.data.summary);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (territory: Territory) => {
    if (territory.isPartiallyCompleted) {
      return 'bg-yellow-100 text-yellow-800';
    }
    if (territory.visitCount > 0) {
      return 'bg-blue-100 text-blue-800';
    }
    if (territory.daysAssigned > 30) {
      return 'bg-red-100 text-red-800';
    }
    return 'bg-green-100 text-green-800';
  };

  const getStatusText = (territory: Territory) => {
    if (territory.isPartiallyCompleted) {
      return 'Parcialmente Completado';
    }
    if (territory.visitCount > 0) {
      return 'En Progreso';
    }
    if (territory.daysAssigned > 30) {
      return 'Pendiente';
    }
    return 'Nuevo';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <div className="text-sm text-gray-600">Verificando acceso...</div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Following Field Service pattern */}
      <div className="bg-green-600 text-white p-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.push('/dashboard')}
              className="text-green-200 hover:text-white flex items-center"
            >
              ← Atrás
            </button>
            <h1 className="text-2xl font-bold">Mis Territorios</h1>
            <div></div> {/* Spacer for centering */}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-1 sm:px-4">
        {/* Summary Cards - Following Field Service summary pattern */}
        {summary && (
          <div className="bg-white rounded-lg shadow-md p-2 sm:p-4 mb-4 sm:mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Resumen</h3>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {summary.totalAssigned}
                </div>
                <div className="text-sm text-gray-600">Asignados</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">
                  {summary.inProgress}
                </div>
                <div className="text-sm text-gray-600">En Progreso</div>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {summary.partiallyCompleted}
                </div>
                <div className="text-sm text-gray-600">Parciales</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {summary.averageDaysAssigned}
                </div>
                <div className="text-sm text-gray-600">Días Promedio</div>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Territory List - Following Field Service card pattern */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="p-2 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Territorios Asignados</h3>

            {territories.length === 0 ? (
              <div className="text-center py-12">
                <MapPinIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No tienes territorios asignados</p>
              </div>
            ) : (
              <div className="space-y-3 sm:space-y-4">
                {territories.map((territory) => (
                  <div
                    key={territory.id}
                    onClick={() => router.push(`/territorios/${territory.territory.id}`)}
                    className="border border-gray-200 rounded-lg p-3 sm:p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    {/* Territory Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        <div className="bg-green-100 p-2 rounded-lg mr-3">
                          <MapPinIcon className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 text-sm sm:text-base">
                            Territorio {territory.territory.territoryNumber}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {territory.territory.address}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(territory)}`}>
                          {getStatusText(territory)}
                        </span>
                        <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>

                    {/* Territory Details */}
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4 text-xs sm:text-sm">
                      <div className="flex items-center">
                        <CalendarDaysIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">Asignado:</span>
                        <span className="ml-1 font-medium">{formatDate(territory.assignedAt)}</span>
                      </div>
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">Días:</span>
                        <span className="ml-1 font-medium">{territory.daysAssigned}</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">Visitas:</span>
                        <span className="ml-1 font-medium">{territory.visitCount}</span>
                      </div>
                    </div>

                    {/* Assigned By */}
                    <div className="mt-3 text-xs text-gray-500">
                      Asignado por: {territory.assignedBy.name}
                    </div>

                    {/* Partial Completion Notes */}
                    {territory.isPartiallyCompleted && territory.partialCompletionNotes && (
                      <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                        <p className="text-xs text-yellow-800">
                          <strong>Notas:</strong> {territory.partialCompletionNotes}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
