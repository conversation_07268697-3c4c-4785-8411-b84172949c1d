/**
 * JW.org Workbook Data Fetching Service
 * 
 * Fetches midweek meeting workbook data from JW.org and parses it for import
 * into the congregation management system.
 */

export interface WorkbookMeetingData {
  date: string;
  theme?: string;
  parts: WorkbookPartData[];
  songs: number[];
}

export interface WorkbookPartData {
  partNumber: number;
  partType: string;
  title: string;
  timeAllocation?: number;
  bibleReading?: string;
  studyPoints?: string[];
  notes?: string;
}

export interface WorkbookFetchResult {
  success: boolean;
  meetings: WorkbookMeetingData[];
  errors: string[];
  warnings: string[];
  fetchedFrom: string;
  fetchedAt: Date;
}

export interface WorkbookFetchOptions {
  startDate: Date;
  endDate: Date;
  language: 'es' | 'en';
  retryAttempts?: number;
  timeout?: number;
}

export class JWOrgWorkbookService {
  private static readonly BASE_URL = 'https://www.jw.org';
  private static readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private static readonly DEFAULT_RETRY_ATTEMPTS = 3;
  private static readonly USER_AGENT = 'Mozilla/5.0 (compatible; CongregationApp/1.0)';

  /**
   * Fetch workbook data for a date range
   */
  static async fetchWorkbookData(options: WorkbookFetchOptions): Promise<WorkbookFetchResult> {
    const result: WorkbookFetchResult = {
      success: false,
      meetings: [],
      errors: [],
      warnings: [],
      fetchedFrom: '',
      fetchedAt: new Date(),
    };

    try {
      // Validate date range
      if (options.startDate > options.endDate) {
        result.errors.push('Start date must be before end date');
        return result;
      }

      // Generate URLs for the date range
      const urls = this.generateWorkbookUrls(options);
      result.fetchedFrom = urls.join(', ');

      // Fetch data from each URL
      for (const url of urls) {
        try {
          const meetingData = await this.fetchSingleWorkbook(url, options);
          if (meetingData) {
            result.meetings.push(meetingData);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          result.errors.push(`Failed to fetch from ${url}: ${errorMessage}`);
        }
      }

      result.success = result.meetings.length > 0;
      
      if (result.meetings.length === 0 && result.errors.length === 0) {
        result.warnings.push('No meeting data found for the specified date range');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(`Workbook fetch failed: ${errorMessage}`);
    }

    return result;
  }

  /**
   * Generate JW.org URLs for workbook data based on date range
   */
  private static generateWorkbookUrls(options: WorkbookFetchOptions): string[] {
    const urls: string[] = [];
    const current = new Date(options.startDate);
    const end = new Date(options.endDate);

    while (current <= end) {
      // Find the Monday of the current week
      const monday = new Date(current);
      const dayOfWeek = monday.getDay();
      const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
      monday.setDate(monday.getDate() + daysToMonday);

      // Generate URL for this week's workbook
      const year = monday.getFullYear();
      const month = String(monday.getMonth() + 1).padStart(2, '0');
      const day = String(monday.getDate()).padStart(2, '0');
      
      const langCode = options.language === 'es' ? 'es' : 'en';
      const url = `${this.BASE_URL}/${langCode}/library/jw-meeting-workbook/${year}/${month}/${day}/`;
      
      if (!urls.includes(url)) {
        urls.push(url);
      }

      // Move to next week
      current.setDate(current.getDate() + 7);
    }

    return urls;
  }

  /**
   * Fetch and parse a single workbook page
   */
  private static async fetchSingleWorkbook(
    url: string, 
    options: WorkbookFetchOptions
  ): Promise<WorkbookMeetingData | null> {
    const retryAttempts = options.retryAttempts || this.DEFAULT_RETRY_ATTEMPTS;
    const timeout = options.timeout || this.DEFAULT_TIMEOUT;

    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        // Fetch the workbook page
        const response = await fetch(url, {
          headers: {
            'User-Agent': this.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': options.language === 'es' ? 'es,en;q=0.5' : 'en,es;q=0.5',
            'Cache-Control': 'no-cache',
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();
        return this.parseWorkbookHtml(html, url);

      } catch (error) {
        if (attempt === retryAttempts) {
          throw error;
        }
        
        // Wait before retry (exponential backoff)
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return null;
  }

  /**
   * Parse HTML content to extract meeting data
   */
  private static parseWorkbookHtml(html: string, url: string): WorkbookMeetingData | null {
    try {
      // Extract meeting date from URL or content
      const dateMatch = url.match(/\/(\d{4})\/(\d{2})\/(\d{2})\//);
      if (!dateMatch) {
        throw new Error('Could not extract date from URL');
      }

      const [, year, month, day] = dateMatch;
      const meetingDate = `${year}-${month}-${day}`;

      // Initialize meeting data
      const meetingData: WorkbookMeetingData = {
        date: meetingDate,
        parts: [],
        songs: [],
      };

      // Extract meeting theme (if available)
      const themeMatch = html.match(/<h1[^>]*>([^<]+)<\/h1>/i);
      if (themeMatch) {
        meetingData.theme = this.cleanText(themeMatch[1]);
      }

      // Extract song numbers
      const songMatches = html.matchAll(/(?:canción|song|cántico)\s*(\d+)/gi);
      for (const match of songMatches) {
        const songNumber = parseInt(match[1], 10);
        if (!isNaN(songNumber) && !meetingData.songs.includes(songNumber)) {
          meetingData.songs.push(songNumber);
        }
      }

      // Extract meeting parts
      meetingData.parts = this.extractMeetingParts(html);

      return meetingData;

    } catch (error) {
      console.error('Error parsing workbook HTML:', error);
      return null;
    }
  }

  /**
   * Extract meeting parts from HTML content
   */
  private static extractMeetingParts(html: string): WorkbookPartData[] {
    const parts: WorkbookPartData[] = [];
    let partNumber = 1;

    // Common patterns for meeting parts
    const partPatterns = [
      // Treasures from God's Word
      /(?:tesoros|treasures)[^<]*<[^>]*>([^<]+)/gi,
      // Digging for Gems
      /(?:busquemos|digging)[^<]*<[^>]*>([^<]+)/gi,
      // Living as Christians
      /(?:vivamos|living)[^<]*<[^>]*>([^<]+)/gi,
      // Ministry parts
      /(?:ministerio|ministry)[^<]*<[^>]*>([^<]+)/gi,
    ];

    // Extract parts using patterns
    for (const pattern of partPatterns) {
      const matches = html.matchAll(pattern);
      for (const match of matches) {
        const title = this.cleanText(match[1]);
        if (title && title.length > 3) {
          parts.push({
            partNumber: partNumber++,
            partType: this.determinePartType(title),
            title,
            timeAllocation: this.extractTimeAllocation(match[0]),
          });
        }
      }
    }

    return parts;
  }

  /**
   * Determine part type based on title content
   */
  private static determinePartType(title: string): string {
    const lowerTitle = title.toLowerCase();
    
    if (lowerTitle.includes('tesoro') || lowerTitle.includes('treasure')) {
      return 'treasures';
    }
    if (lowerTitle.includes('busque') || lowerTitle.includes('digging') || lowerTitle.includes('gem')) {
      return 'digging';
    }
    if (lowerTitle.includes('vivamos') || lowerTitle.includes('living') || lowerTitle.includes('christian')) {
      return 'living';
    }
    if (lowerTitle.includes('ministerio') || lowerTitle.includes('ministry')) {
      return 'ministry';
    }
    if (lowerTitle.includes('oración') || lowerTitle.includes('prayer')) {
      return 'prayer';
    }
    if (lowerTitle.includes('canción') || lowerTitle.includes('song') || lowerTitle.includes('cántico')) {
      return 'song';
    }
    
    return 'general';
  }

  /**
   * Extract time allocation from text
   */
  private static extractTimeAllocation(text: string): number | undefined {
    const timeMatch = text.match(/(\d+)\s*(?:min|minuto)/i);
    return timeMatch ? parseInt(timeMatch[1], 10) : undefined;
  }

  /**
   * Clean and normalize text content
   */
  private static cleanText(text: string): string {
    return text
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&[^;]+;/g, ' ') // Remove HTML entities
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Validate workbook data before import
   */
  static validateWorkbookData(data: WorkbookMeetingData[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const meeting of data) {
      // Validate date format
      if (!/^\d{4}-\d{2}-\d{2}$/.test(meeting.date)) {
        errors.push(`Invalid date format: ${meeting.date}`);
      }

      // Validate parts
      if (meeting.parts.length === 0) {
        errors.push(`No parts found for meeting on ${meeting.date}`);
      }

      // Validate song numbers
      for (const songNumber of meeting.songs) {
        if (!Number.isInteger(songNumber) || songNumber < 1 || songNumber > 999) {
          errors.push(`Invalid song number: ${songNumber} for meeting on ${meeting.date}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
