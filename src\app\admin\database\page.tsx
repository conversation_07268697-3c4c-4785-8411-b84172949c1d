'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminFooter from '@/components/admin/AdminFooter';

interface DatabaseBackup {
  id: string;
  filename: string;
  fileSize: string;
  createdAt: string;
  status: 'completed' | 'failed' | 'in_progress';
}

export default function DatabaseManagementPage() {
  const router = useRouter();
  const [backups, setBackups] = useState<DatabaseBackup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [creatingBackup, setCreatingBackup] = useState(false);

  // Fetch backups from API
  const fetchBackups = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('hermanos_token');
      console.log('Raw token from localStorage:', token ? `${token.substring(0, 30)}...` : 'null');

      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/admin/database/backup', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          router.push('/login');
          return;
        }
        throw new Error('Failed to fetch backups');
      }

      const data = await response.json();
      setBackups(data.backups || []);

    } catch (err) {
      console.error('Error fetching backups:', err);
      setError(err instanceof Error ? err.message : 'Error al cargar los backups');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBackups();
  }, []);

  const handleCreateBackup = async () => {
    setCreatingBackup(true);
    try {
      const token = localStorage.getItem('hermanos_token');
      console.log('Raw token from localStorage (backup):', token ? `${token.substring(0, 30)}...` : 'null');

      if (!token) {
        router.push('/login');
        return;
      }

      console.log('Creating backup with token:', token ? 'Token present' : 'No token');

      const response = await fetch('/api/admin/database/backup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description: 'Manual backup created from admin interface'
        }),
      });

      console.log('Backup API response status:', response.status);

      if (!response.ok) {
        if (response.status === 401) {
          router.push('/login');
          return;
        }

        let errorMessage = 'Failed to create backup';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
          console.error('Backup creation error details:', errorData);
        } catch (parseError) {
          const errorText = await response.text();
          console.error('Backup creation error (raw):', errorText);
          errorMessage = errorText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('Backup creation successful:', data);

      // Refresh backup list
      await fetchBackups();

    } catch (err) {
      console.error('Error creating backup:', err);
      setError(err instanceof Error ? err.message : 'Error al crear el backup');
    } finally {
      setCreatingBackup(false);
    }
  };

  const handleDownload = async (backup: DatabaseBackup) => {
    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/admin/database/backup/${backup.filename}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to download backup');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = backup.filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

    } catch (err) {
      console.error('Error downloading backup:', err);
      setError('Error al descargar el backup');
    }
  };

  const handleRestore = (backup: DatabaseBackup) => {
    // TODO: Implement restore functionality
    alert('La funcionalidad de restauración estará disponible próximamente.');
  };

  const handleDelete = async (backup: DatabaseBackup) => {
    if (!confirm(`¿Está seguro de que desea eliminar el backup ${backup.filename}?`)) {
      return;
    }

    try {
      const token = localStorage.getItem('hermanos_token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/admin/database/backup/${backup.filename}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('No tiene permisos para eliminar backups');
        }
        throw new Error('Failed to delete backup');
      }

      // Refresh backup list
      await fetchBackups();

    } catch (err) {
      console.error('Error deleting backup:', err);
      setError(err instanceof Error ? err.message : 'Error al eliminar el backup');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando gestión de base de datos...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Teal background matching screenshot */}
      <div className="bg-teal-500 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/admin')}
                className="mr-4 p-2 rounded-md hover:bg-teal-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-xl font-semibold">Database Management</h1>
            </div>

            <button
              onClick={handleCreateBackup}
              disabled={creatingBackup}
              className="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md text-white hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-400 disabled:opacity-50"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              {creatingBackup ? 'Creating...' : 'Create Backup'}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <div className="flex items-center">
              <svg className="h-4 w-4 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <p className="text-sm text-red-800">{error}</p>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-600"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {/* Backup History Section */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Backup History</h2>
          </div>

          <div className="divide-y divide-gray-200">
            {backups.map((backup) => (
              <div key={backup.id} className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900">{backup.filename}</h3>
                  <p className="text-sm text-gray-500">{backup.fileSize} • {backup.createdAt}</p>
                </div>

                <div className="flex items-center space-x-2">
                  {/* Download Button */}
                  <button
                    onClick={() => handleDownload(backup)}
                    className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    title="Descargar backup"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </button>

                  {/* Restore Button */}
                  <button
                    onClick={() => handleRestore(backup)}
                    className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                    title="Restaurar backup"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0V9a8 8 0 1115.356 2M15 15v5h-.582M4.582 15A8.001 8.001 0 0019.418 15m0 0V15a8 8 0 11-15.356-2" />
                    </svg>
                  </button>

                  {/* Delete Button */}
                  <button
                    onClick={() => handleDelete(backup)}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    title="Eliminar backup"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Admin Footer */}
      <AdminFooter currentSection="database" />
    </div>
  );
}
