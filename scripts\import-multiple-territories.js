/**
 * Import Multiple Territories Script
 * 
 * Imports multiple territories from Excel files while preserving:
 * 1. Territory order (as they appear in Excel file list)
 * 2. Address order within each territory (as they appear in Excel sheets)
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Configuration
const CONGREGATION_ID = '1441'; // Coral Oeste
const ZIP_CODE = 'Miami, FL 33126';

/**
 * Parse addresses from Excel data while preserving order
 */
function parseAddresses(excelData) {
  const addresses = [];
  let currentStreet = '';
  
  for (let i = 0; i < excelData.length; i++) {
    const row = excelData[i];
    
    if (!row || row.length === 0) continue;
    
    // Check if this row contains a street name (usually in column A, starts with text)
    const firstCell = row[0];
    if (firstCell && typeof firstCell === 'string' && firstCell.trim()) {
      const cellValue = firstCell.toString().trim();
      
      // If it doesn't start with a number, it's likely a street name
      if (!/^\d/.test(cellValue)) {
        currentStreet = cellValue;
        console.log(`📍 Found street: ${currentStreet}`);
        continue;
      }
    }
    
    // Process house numbers (usually in columns A, B, C, D, E, F)
    for (let colIndex = 0; colIndex < Math.min(row.length, 6); colIndex++) {
      const cellValue = row[colIndex];
      
      if (cellValue && /^\d/.test(cellValue.toString().trim())) {
        const houseNumber = cellValue.toString().trim();
        const fullAddress = `${houseNumber} ${currentStreet}, ${ZIP_CODE}`;
        
        // Get notes from column G (index 6) if available
        const notes = row[6] && typeof row[6] === 'string' ? row[6].trim() : null;
        
        addresses.push({
          address: fullAddress,
          notes: notes && notes !== 'null' ? notes : null,
          street: currentStreet,
          houseNumber: houseNumber
        });
        
        console.log(`🏠 Added address: ${fullAddress}${notes ? ` (${notes})` : ''}`);
      }
    }
  }
  
  return addresses;
}

/**
 * Import a single territory from Excel file
 */
async function importSingleTerritory(filePath, territoryNumber, displayOrder) {
  try {
    console.log(`\n📂 Importing Territory ${territoryNumber}...`);
    
    // Read Excel file
    const workbook = XLSX.readFile(filePath);
    
    // Try different possible sheet names
    const possibleSheetNames = [
      `Terr ${territoryNumber}`,
      `Terr. ${territoryNumber}`,
      `Territory ${territoryNumber}`,
      `T${territoryNumber}`,
      workbook.SheetNames[0] // Fallback to first sheet
    ];
    
    let worksheet = null;
    let sheetName = '';
    
    for (const name of possibleSheetNames) {
      if (workbook.Sheets[name]) {
        worksheet = workbook.Sheets[name];
        sheetName = name;
        break;
      }
    }
    
    if (!worksheet) {
      console.error(`❌ No valid sheet found in ${filePath}`);
      console.log(`Available sheets: ${workbook.SheetNames.join(', ')}`);
      return false;
    }
    
    console.log(`📊 Using sheet: ${sheetName}`);
    
    const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`📊 Read ${excelData.length} rows from Excel`);
    
    // Parse addresses
    const addresses = parseAddresses(excelData);
    console.log(`🏘️  Parsed ${addresses.length} addresses`);
    
    if (addresses.length === 0) {
      console.log(`⚠️  No addresses found in Territory ${territoryNumber}`);
      return false;
    }
    
    // Clear existing territory if it exists
    await prisma.territoryAssignment.deleteMany({
      where: { 
        congregationId: CONGREGATION_ID,
        territory: {
          territoryNumber: territoryNumber
        }
      }
    });
    
    await prisma.territory.deleteMany({
      where: { 
        congregationId: CONGREGATION_ID,
        territoryNumber: territoryNumber
      }
    });
    
    console.log(`🗑️  Cleared existing Territory ${territoryNumber}`);
    
    // Create the territory with all addresses combined
    const allAddresses = addresses.map(addr => addr.address).join('\n');
    const allNotes = addresses
      .filter(addr => addr.notes)
      .map(addr => `${addr.address}: ${addr.notes}`)
      .join('\n');
    
    const territory = await prisma.territory.create({
      data: {
        congregationId: CONGREGATION_ID,
        territoryNumber: territoryNumber,
        address: allAddresses,
        notes: allNotes || null,
        status: 'available',
        displayOrder: displayOrder
      }
    });
    
    console.log(`✅ Created Territory ${territoryNumber} with ${addresses.length} addresses`);
    console.log(`📍 Display Order: ${displayOrder}`);
    
    return true;
    
  } catch (error) {
    console.error(`❌ Error importing Territory ${territoryNumber}:`, error.message);
    return false;
  }
}

/**
 * Main import function
 */
async function importMultipleTerritories(territoriesToImport = []) {
  try {
    console.log('🚀 Starting multiple territory import...');
    
    // Verify congregation exists
    const congregation = await prisma.congregation.findUnique({
      where: { id: CONGREGATION_ID }
    });

    if (!congregation) {
      console.error(`❌ Congregation ${CONGREGATION_ID} (Coral Oeste) not found`);
      process.exit(1);
    }

    console.log(`✅ Found congregation: ${congregation.name}`);
    
    // Get list of all Excel files if no specific territories provided
    const territoriosDir = path.join(__dirname, '..', 'Territorios');
    const allFiles = fs.readdirSync(territoriosDir)
      .filter(file => file.endsWith('.xlsx') && !file.includes('copy'))
      .sort(); // Sort to maintain Excel file order
    
    console.log(`📁 Found ${allFiles.length} Excel files`);
    
    // If specific territories provided, filter files
    let filesToProcess = allFiles;
    if (territoriesToImport.length > 0) {
      filesToProcess = allFiles.filter(file => {
        const match = file.match(/Terr\.\s*(\d+)\.xlsx/i);
        if (match) {
          const territoryNumber = match[1].padStart(3, '0');
          return territoriesToImport.includes(territoryNumber);
        }
        return false;
      });
    }
    
    console.log(`📋 Processing ${filesToProcess.length} territories...`);
    
    let successCount = 0;
    let failCount = 0;
    
    // Process each territory
    for (let i = 0; i < filesToProcess.length; i++) {
      const file = filesToProcess[i];
      const filePath = path.join(territoriosDir, file);
      
      // Extract territory number
      const match = file.match(/Terr\.\s*(\d+)\.xlsx/i);
      if (!match) {
        console.log(`⚠️  Skipping file with invalid format: ${file}`);
        continue;
      }
      
      const territoryNumber = match[1].padStart(3, '0');
      const displayOrder = allFiles.indexOf(file) + 1; // Maintain Excel file order
      
      const success = await importSingleTerritory(filePath, territoryNumber, displayOrder);
      
      if (success) {
        successCount++;
      } else {
        failCount++;
      }
      
      // Small delay to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`\n🎉 Import completed!`);
    console.log(`✅ Successfully imported: ${successCount} territories`);
    console.log(`❌ Failed to import: ${failCount} territories`);
    
  } catch (error) {
    console.error('❌ Error during import:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Export for use in other scripts
module.exports = { importMultipleTerritories, importSingleTerritory };

// Run the script if called directly
if (require.main === module) {
  // You can specify which territories to import, or leave empty to import all
  const territoriesToImport = process.argv.slice(2); // Get territories from command line
  
  if (territoriesToImport.length > 0) {
    console.log(`🎯 Importing specific territories: ${territoriesToImport.join(', ')}`);
  } else {
    console.log('🌍 Importing all available territories...');
  }
  
  importMultipleTerritories(territoriesToImport);
}
