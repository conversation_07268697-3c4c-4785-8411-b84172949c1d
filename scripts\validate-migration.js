#!/usr/bin/env node

/**
 * Migration Validation Script for Hermanos App
 * 
 * This script validates the PostgreSQL database structure and data
 * to ensure the migration was successful.
 * 
 * Usage: node scripts/validate-migration.js
 */

const { PrismaClient } = require('@prisma/client');

class MigrationValidator {
  constructor() {
    this.prisma = new PrismaClient();
    this.validationResults = [];
  }

  async validateSchema() {
    console.log('🔍 Validating database schema...');
    
    try {
      // Test each model to ensure tables exist and are accessible
      const tests = [
        { name: 'congregations', test: () => this.prisma.congregation.findMany({ take: 1 }) },
        { name: 'roles', test: () => this.prisma.role.findMany({ take: 1 }) },
        { name: 'members', test: () => this.prisma.member.findMany({ take: 1 }) },
        { name: 'elder_permissions', test: () => this.prisma.elderPermission.findMany({ take: 1 }) },
        { name: 'tasks', test: () => this.prisma.task.findMany({ take: 1 }) },
        { name: 'task_assignments', test: () => this.prisma.taskAssignment.findMany({ take: 1 }) },
        { name: 'field_service_records', test: () => this.prisma.fieldServiceRecord.findMany({ take: 1 }) },
        { name: 'letters', test: () => this.prisma.letter.findMany({ take: 1 }) },
        { name: 'events', test: () => this.prisma.event.findMany({ take: 1 }) },
        { name: 'midweek_meetings', test: () => this.prisma.midweekMeeting.findMany({ take: 1 }) },
        { name: 'weekend_meetings', test: () => this.prisma.weekendMeeting.findMany({ take: 1 }) },
        { name: 'midweek_meeting_parts', test: () => this.prisma.midweekMeetingPart.findMany({ take: 1 }) },
        { name: 'weekend_meeting_parts', test: () => this.prisma.weekendMeetingPart.findMany({ take: 1 }) },
        { name: 'songs', test: () => this.prisma.song.findMany({ take: 1 }) },
      ];
      
      for (const test of tests) {
        try {
          await test.test();
          this.validationResults.push({
            category: 'schema',
            test: test.name,
            status: 'PASS',
            message: `Table ${test.name} exists and is accessible`
          });
        } catch (error) {
          this.validationResults.push({
            category: 'schema',
            test: test.name,
            status: 'FAIL',
            message: `Table ${test.name} error: ${error.message}`
          });
        }
      }
      
      console.log(`✅ Schema validation completed - ${tests.length} tables tested`);
      
    } catch (error) {
      console.error('❌ Schema validation failed:', error.message);
      throw error;
    }
  }

  async validateData() {
    console.log('\n🔍 Validating data integrity...');
    
    try {
      // Count records in each table
      const counts = {
        congregations: await this.prisma.congregation.count(),
        roles: await this.prisma.role.count(),
        members: await this.prisma.member.count(),
        tasks: await this.prisma.task.count(),
        songs: await this.prisma.song.count(),
        letters: await this.prisma.letter.count(),
        events: await this.prisma.event.count(),
        midweekMeetings: await this.prisma.midweekMeeting.count(),
        weekendMeetings: await this.prisma.weekendMeeting.count(),
        fieldServiceRecords: await this.prisma.fieldServiceRecord.count(),
      };
      
      // Validate minimum expected data
      const validations = [
        { name: 'congregations', count: counts.congregations, min: 1, message: 'At least one congregation should exist' },
        { name: 'roles', count: counts.roles, min: 1, message: 'At least one role should exist' },
        { name: 'songs', count: counts.songs, min: 1, message: 'At least one song should exist' },
      ];
      
      for (const validation of validations) {
        if (validation.count >= validation.min) {
          this.validationResults.push({
            category: 'data',
            test: validation.name,
            status: 'PASS',
            message: `${validation.name}: ${validation.count} records found`
          });
        } else {
          this.validationResults.push({
            category: 'data',
            test: validation.name,
            status: 'FAIL',
            message: `${validation.name}: Only ${validation.count} records found, expected at least ${validation.min}`
          });
        }
      }
      
      console.log('✅ Data validation completed');
      
    } catch (error) {
      console.error('❌ Data validation failed:', error.message);
      throw error;
    }
  }

  async validateRelationships() {
    console.log('\n🔍 Validating relationships...');
    
    try {
      // Test foreign key relationships
      const congregations = await this.prisma.congregation.findMany({
        include: {
          members: true,
          tasks: true,
        },
        take: 1
      });
      
      if (congregations.length > 0) {
        this.validationResults.push({
          category: 'relationships',
          test: 'congregation_members',
          status: 'PASS',
          message: 'Congregation-Members relationship working'
        });
        
        this.validationResults.push({
          category: 'relationships',
          test: 'congregation_tasks',
          status: 'PASS',
          message: 'Congregation-Tasks relationship working'
        });
      }
      
      // Test member roles relationship
      const members = await this.prisma.member.findMany({
        include: {
          congregation: true,
        },
        take: 1
      });
      
      if (members.length > 0) {
        this.validationResults.push({
          category: 'relationships',
          test: 'member_congregation',
          status: 'PASS',
          message: 'Member-Congregation relationship working'
        });
      }
      
      console.log('✅ Relationship validation completed');
      
    } catch (error) {
      console.error('❌ Relationship validation failed:', error.message);
      this.validationResults.push({
        category: 'relationships',
        test: 'general',
        status: 'FAIL',
        message: `Relationship validation error: ${error.message}`
      });
    }
  }

  async validateCongregationIsolation() {
    console.log('\n🔍 Validating congregation isolation...');
    
    try {
      // Check that all tables with congregation_id have proper isolation
      const congregations = await this.prisma.congregation.findMany();
      
      if (congregations.length > 0) {
        const congregationId = congregations[0].id;
        
        // Test that queries can be filtered by congregation
        const congregationMembers = await this.prisma.member.findMany({
          where: { congregationId }
        });
        
        const congregationTasks = await this.prisma.task.findMany({
          where: { congregationId }
        });
        
        this.validationResults.push({
          category: 'isolation',
          test: 'congregation_filtering',
          status: 'PASS',
          message: `Congregation isolation working - found ${congregationMembers.length} members and ${congregationTasks.length} tasks for congregation ${congregationId}`
        });
      }
      
      console.log('✅ Congregation isolation validation completed');
      
    } catch (error) {
      console.error('❌ Congregation isolation validation failed:', error.message);
      this.validationResults.push({
        category: 'isolation',
        test: 'congregation_filtering',
        status: 'FAIL',
        message: `Isolation validation error: ${error.message}`
      });
    }
  }

  generateReport() {
    console.log('\n📋 Migration Validation Report');
    console.log('==============================');
    
    const categories = ['schema', 'data', 'relationships', 'isolation'];
    let totalTests = 0;
    let passedTests = 0;
    
    for (const category of categories) {
      const categoryResults = this.validationResults.filter(r => r.category === category);
      if (categoryResults.length === 0) continue;
      
      console.log(`\n${category.toUpperCase()}:`);
      
      for (const result of categoryResults) {
        const status = result.status === 'PASS' ? '✅' : '❌';
        console.log(`  ${status} ${result.test}: ${result.message}`);
        totalTests++;
        if (result.status === 'PASS') passedTests++;
      }
    }
    
    console.log('\n==============================');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    const allPassed = passedTests === totalTests;
    if (allPassed) {
      console.log('\n🎉 All validation tests passed!');
    } else {
      console.log('\n⚠️ Some validation tests failed!');
    }
    
    return allPassed;
  }

  async run() {
    try {
      console.log('🚀 Starting migration validation...');
      
      await this.prisma.$connect();
      console.log('✅ Database connection established');
      
      await this.validateSchema();
      await this.validateData();
      await this.validateRelationships();
      await this.validateCongregationIsolation();
      
      const success = this.generateReport();
      
      if (success) {
        console.log('\n✅ Migration validation completed successfully!');
      } else {
        console.log('\n❌ Migration validation completed with errors!');
        process.exit(1);
      }
      
    } catch (error) {
      console.error('\n💥 Validation failed:', error.message);
      process.exit(1);
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new MigrationValidator();
  validator.run().catch(error => {
    console.error('Validation failed:', error);
    process.exit(1);
  });
}

module.exports = MigrationValidator;
