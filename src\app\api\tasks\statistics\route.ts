/**
 * Task Statistics API Endpoint
 * 
 * Provides congregation-wide task statistics and reporting
 * for task coordinators and elders.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractAndVerifyToken } from '@/lib/middleware/auth';
import { TaskManagementService } from '@/lib/services/taskManagementService';

// Validation schema for statistics requests
const StatisticsRequestSchema = z.object({
  type: z.enum(['overview', 'categories', 'workload', 'upcoming', 'overdue']).default('overview'),
  memberId: z.string().optional(),
  days: z.string().transform(val => parseInt(val, 10)).optional(),
});

/**
 * GET /api/tasks/statistics
 * Retrieve congregation task statistics and reports
 */
export async function GET(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders and ministerial servants can view statistics
    if (!['elder', 'ministerial_servant'].includes(member.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view congregation statistics' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = StatisticsRequestSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const { type, memberId, days } = validationResult.data;

    switch (type) {
      case 'overview':
        // Get complete task statistics
        const statistics = await TaskManagementService.getTaskStatistics(
          member.congregationId
        );

        return NextResponse.json({
          success: true,
          statistics,
          type: 'overview',
        });

      case 'categories':
        // Get task categories
        const categories = await TaskManagementService.getTaskCategories(
          member.congregationId
        );

        return NextResponse.json({
          success: true,
          categories,
          type: 'categories',
        });

      case 'workload':
        // Get member workload (if memberId specified) or all members
        if (memberId) {
          const memberTasks = await TaskManagementService.getMemberTasks(
            member.congregationId,
            memberId
          );

          return NextResponse.json({
            success: true,
            memberTasks,
            memberId,
            type: 'workload',
          });
        } else {
          const statistics = await TaskManagementService.getTaskStatistics(
            member.congregationId
          );

          return NextResponse.json({
            success: true,
            memberWorkload: statistics.memberWorkload,
            type: 'workload',
          });
        }

      case 'upcoming':
        // Get upcoming assignments
        const upcomingAssignments = await TaskManagementService.getUpcomingAssignments(
          member.congregationId,
          days || 30
        );

        return NextResponse.json({
          success: true,
          upcomingAssignments,
          days: days || 30,
          type: 'upcoming',
        });

      case 'overdue':
        // Get overdue assignments
        const overdueAssignments = await TaskManagementService.getOverdueAssignments(
          member.congregationId
        );

        return NextResponse.json({
          success: true,
          overdueAssignments,
          type: 'overdue',
        });

      default:
        return NextResponse.json(
          { error: 'Invalid statistics type' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Task statistics error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve task statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tasks/statistics
 * Generate task reports and export data
 */
export async function POST(request: NextRequest) {
  try {
    // Extract and verify authentication token
    const member = await extractAndVerifyToken(request);
    if (!member) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only elders can generate reports
    if (member.role !== 'elder') {
      return NextResponse.json(
        { error: 'Only elders can generate task reports' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { reportType, dateFrom, dateTo, includeCompleted } = body;

    // Validate date range
    if (dateFrom && dateTo) {
      if (!/^\d{4}-\d{2}-\d{2}$/.test(dateFrom) || !/^\d{4}-\d{2}-\d{2}$/.test(dateTo)) {
        return NextResponse.json(
          { error: 'Dates must be in YYYY-MM-DD format' },
          { status: 400 }
        );
      }

      const startDate = new Date(dateFrom);
      const endDate = new Date(dateTo);
      
      if (startDate > endDate) {
        return NextResponse.json(
          { error: 'Start date must be before or equal to end date' },
          { status: 400 }
        );
      }

      // Limit to 1 year maximum
      const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        return NextResponse.json(
          { error: 'Date range cannot exceed 1 year' },
          { status: 400 }
        );
      }
    }

    // Generate comprehensive report
    const [
      statistics,
      assignments,
      upcomingAssignments,
      overdueAssignments,
      categories
    ] = await Promise.all([
      TaskManagementService.getTaskStatistics(member.congregationId),
      TaskManagementService.getTaskAssignments(member.congregationId, {
        dateFrom,
        dateTo,
        status: includeCompleted ? undefined : 'pending',
        limit: 1000, // Large limit for reports
      }),
      TaskManagementService.getUpcomingAssignments(member.congregationId, 30),
      TaskManagementService.getOverdueAssignments(member.congregationId),
      TaskManagementService.getTaskCategories(member.congregationId),
    ]);

    // Calculate additional metrics
    const totalAssignments = assignments.total;
    const completionRate = statistics.completedAssignments > 0 
      ? Math.round((statistics.completedAssignments / (statistics.completedAssignments + statistics.pendingAssignments + statistics.inProgressAssignments)) * 100)
      : 0;

    const report = {
      reportType: reportType || 'comprehensive',
      generatedAt: new Date().toISOString(),
      dateRange: {
        from: dateFrom || null,
        to: dateTo || null,
      },
      summary: {
        totalTasks: statistics.totalTasks,
        activeTasks: statistics.activeTasks,
        totalAssignments,
        completionRate,
        overdueCount: statistics.overdueTasks,
      },
      statistics,
      assignments: assignments.assignments,
      upcomingAssignments,
      overdueAssignments,
      categories,
      memberWorkload: statistics.memberWorkload,
    };

    return NextResponse.json({
      success: true,
      report,
      message: 'Task report generated successfully',
    });

  } catch (error) {
    console.error('Task report generation error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to generate task report',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
