/**
 * Enhance Territories Script
 * 
 * Adds real coordinates and boundaries to existing territories based on their addresses.
 * This enables the zoom-to-territory functionality with actual territory data.
 */

const { TerritoryEnhancementService } = require('../src/services/territories/TerritoryEnhancementService.ts');

async function enhanceTerritories() {
  try {
    console.log('🚀 Starting territory enhancement process...\n');
    
    // Enhance all territories for Coral Oeste congregation
    const result = await TerritoryEnhancementService.enhanceAllTerritories('1441');
    
    console.log('\n📊 Enhancement Summary:');
    console.log(`✅ Successfully enhanced: ${result.success} territories`);
    console.log(`📍 Total territories processed: ${result.total}`);
    
    if (result.success > 0) {
      console.log('\n🎯 Benefits of enhancement:');
      console.log('  • Zoom-to-territory functionality now works');
      console.log('  • Territory boundaries visible on map');
      console.log('  • Improved territory visualization');
      console.log('  • Better field service planning');
    }
    
    console.log('\n🎉 Territory enhancement completed!');
    
  } catch (error) {
    console.error('❌ Error during territory enhancement:', error);
    process.exit(1);
  }
}

// Run the enhancement
if (require.main === module) {
  enhanceTerritories();
}
