import type { LngLatLike } from 'maplibre-gl';
import type { Coordinates, MapBounds, Territory, MapConfig } from '@/types/territories/map';

/**
 * Convert coordinates to MapLibre LngLatLike format
 */
export function coordinatesToLngLat(coordinates: Coordinates): LngLatLike {
  return [coordinates.longitude, coordinates.latitude];
}

/**
 * Convert LngLatLike to Coordinates format
 */
export function lngLatToCoordinates(lngLat: LngLatLike): Coordinates {
  if (Array.isArray(lngLat)) {
    return {
      longitude: lngLat[0],
      latitude: lngLat[1]
    };
  } else {
    // Handle both lng/lat and lon/lat formats
    const lng = 'lng' in lngLat ? lngLat.lng : (lngLat as any).lon;
    const lat = lngLat.lat;
    return {
      longitude: lng,
      latitude: lat
    };
  }
}

/**
 * Calculate bounds for a set of territories
 */
export function calculateTerritoryBounds(territories: Territory[]): MapBounds | null {
  const validTerritories = territories.filter(t => t.coordinates);

  if (validTerritories.length === 0) {
    return null;
  }

  let north = -90;
  let south = 90;
  let east = -180;
  let west = 180;

  validTerritories.forEach(territory => {
    if (territory.coordinates) {
      const { latitude, longitude } = territory.coordinates;
      north = Math.max(north, latitude);
      south = Math.min(south, latitude);
      east = Math.max(east, longitude);
      west = Math.min(west, longitude);
    }
  });

  // Add padding to bounds
  const latPadding = (north - south) * 0.1;
  const lngPadding = (east - west) * 0.1;

  return {
    north: north + latPadding,
    south: south - latPadding,
    east: east + lngPadding,
    west: west - lngPadding
  };
}

/**
 * Calculate distance between two coordinates in kilometers
 */
export function calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(coord2.latitude - coord1.latitude);
  const dLon = toRadians(coord2.longitude - coord1.longitude);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Convert degrees to radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Check if coordinates are within Miami area bounds
 */
export function isWithinMiamiArea(coordinates: Coordinates): boolean {
  const miamiBounds = {
    north: 25.9,
    south: 25.6,
    east: -80.1,
    west: -80.4
  };

  return (
    coordinates.latitude >= miamiBounds.south &&
    coordinates.latitude <= miamiBounds.north &&
    coordinates.longitude >= miamiBounds.west &&
    coordinates.longitude <= miamiBounds.east
  );
}

/**
 * Generate map style with fallback tile sources
 */
export function createMapStyleWithFallbacks(primaryTileUrl: string, fallbackUrls: string[] = []): MapConfig['style'] {
  const sources: Record<string, any> = {
    'primary-tiles': {
      type: 'raster',
      tiles: [primaryTileUrl],
      tileSize: 256,
      attribution: '© OpenStreetMap contributors'
    }
  };

  // Add fallback sources
  fallbackUrls.forEach((url, index) => {
    sources[`fallback-tiles-${index}`] = {
      type: 'raster',
      tiles: [url],
      tileSize: 256,
      attribution: '© OpenStreetMap contributors'
    };
  });

  return {
    version: 8,
    sources,
    layers: [{
      id: 'base-tiles',
      type: 'raster',
      source: 'primary-tiles'
    }]
  };
}

/**
 * Validate coordinates
 */
export function isValidCoordinates(coordinates: Coordinates): boolean {
  return (
    typeof coordinates.latitude === 'number' &&
    typeof coordinates.longitude === 'number' &&
    coordinates.latitude >= -90 &&
    coordinates.latitude <= 90 &&
    coordinates.longitude >= -180 &&
    coordinates.longitude <= 180 &&
    !isNaN(coordinates.latitude) &&
    !isNaN(coordinates.longitude)
  );
}

/**
 * Parse address string to extract components
 */
export function parseAddress(address: string): {
  streetNumber?: string;
  streetName?: string;
  city?: string;
  state?: string;
  zipCode?: string;
} {
  // Basic address parsing for Miami addresses
  const parts = address.split(',').map(part => part.trim());

  if (parts.length >= 2) {
    const streetPart = parts[0];
    const cityPart = parts[1];
    const statePart = parts[2];

    // Extract street number and name
    const streetMatch = streetPart.match(/^(\d+[A-Z]?)\s+(.+)$/);
    const streetNumber = streetMatch ? streetMatch[1] : undefined;
    const streetName = streetMatch ? streetMatch[2] : streetPart;

    // Extract city
    const city = cityPart;

    // Extract state and zip
    let state: string | undefined;
    let zipCode: string | undefined;

    if (statePart) {
      const stateMatch = statePart.match(/^([A-Z]{2})\s*(\d{5})?$/);
      if (stateMatch) {
        state = stateMatch[1];
        zipCode = stateMatch[2];
      }
    }

    return {
      streetNumber,
      streetName,
      city,
      state,
      zipCode
    };
  }

  return {};
}

/**
 * Format coordinates for display
 */
export function formatCoordinates(coordinates: Coordinates, precision: number = 6): string {
  return `${coordinates.latitude.toFixed(precision)}, ${coordinates.longitude.toFixed(precision)}`;
}

/**
 * Check if device is mobile for map optimization
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false;

  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
         window.innerWidth <= 768;
}

/**
 * Get optimal zoom level based on territory count and bounds
 */
export function getOptimalZoom(territoryCount: number, bounds?: MapBounds): number {
  if (!bounds) {
    return 12; // Default zoom for Miami area
  }

  // Calculate bounds size
  const latSpan = bounds.north - bounds.south;
  const lngSpan = bounds.east - bounds.west;
  const maxSpan = Math.max(latSpan, lngSpan);

  // Determine zoom based on span and territory count
  if (maxSpan > 0.1 || territoryCount > 20) return 10;
  if (maxSpan > 0.05 || territoryCount > 10) return 12;
  if (maxSpan > 0.02 || territoryCount > 5) return 14;
  return 16;
}
